import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { getBlogPost, getBlogPosts, urlFor } from '@/lib/sanity/client'
import { PortableTextRenderer } from '@/components/sanity/portable-text-renderer'
import { Calendar, Clock, ArrowLeft, Share2 } from 'lucide-react'

interface BlogPostPageProps {
  params: Promise<{ slug: string }>
}

// Generate static params for ISR
export async function generateStaticParams() {
  const posts = await getBlogPosts()
  return posts.map((post) => ({
    slug: post.slug.current,
  }))
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    return {
      title: 'Post Not Found - Celer AI',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: post.seo?.title || `${post.title} - Celer AI Blog`,
    description: post.seo?.description || post.excerpt || `Read ${post.title} on the Celer AI blog`,
    keywords: post.seo?.keywords?.join(', ') || 'healthcare, AI, medical documentation',
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      type: 'article',
      publishedTime: post.publishedAt,
      authors: post.author?.name ? [post.author.name] : undefined,
      images: post.mainImage ? [
        {
          url: urlFor(post.mainImage).width(1200).height(630).fit('crop').auto('format').url(),
          width: 1200,
          height: 630,
          alt: post.mainImage.alt || post.title,
        }
      ] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || '',
      images: post.mainImage ? [
        urlFor(post.mainImage).width(1200).height(630).fit('crop').auto('format').url()
      ] : undefined,
    },
  }
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    notFound()
  }

  const publishedDate = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // Estimate read time (average 200 words per minute)
  const estimatedReadTime = post.body 
    ? Math.ceil(JSON.stringify(post.body).length / 1000) 
    : 5

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            <Link
              href="/blog"
              className="text-slate-900 text-sm font-medium"
            >
              Blog
            </Link>
            <Link
              href="/guide"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Get Started
            </Link>
          </div>
        </div>
      </nav>

      {/* Magical Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
      </div>

      {/* Back to Blog */}
      <div className="relative max-w-4xl mx-auto px-6 pt-32 pb-8">
        <Link
          href="/blog"
          className="inline-flex items-center space-x-2 text-slate-600 hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 group animate-fade-in"
        >
          <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300" />
          <span>Back to Blog</span>
        </Link>
      </div>

      {/* Article */}
      <article className="relative max-w-4xl mx-auto px-6 pb-16">
        {/* Categories */}
        {post.categories && post.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-8 animate-fade-in">
            {post.categories.map((category) => (
              <span
                key={category.slug.current}
                className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm font-medium rounded-full border border-indigo-200"
              >
                {category.title}
              </span>
            ))}
          </div>
        )}

        {/* Title */}
        <h1 className="text-3xl md:text-4xl font-bold mb-8 leading-tight animate-slide-up">
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 relative z-10">
            {post.title}
          </span>
        </h1>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-12 pb-8 border-b border-gradient-to-r from-indigo-200/50 to-purple-200/50 animate-slide-up delay-200">
          <div className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-indigo-500" />
            <span>{publishedDate}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-purple-500" />
            <span>{estimatedReadTime} min read</span>
          </div>
          {post.author && (
            <div className="flex items-center space-x-3">
              {post.author.image && (
                <Image
                  src={urlFor(post.author.image).width(40).height(40).fit('crop').auto('format').url()}
                  alt={post.author.name}
                  width={40}
                  height={40}
                  className="rounded-full border-2 border-white/50 shadow-lg"
                />
              )}
              <span className="font-medium">{post.author.name}</span>
            </div>
          )}
        </div>

        {/* Featured Image */}
        {post.mainImage && (
          <div className="relative h-64 md:h-96 rounded-3xl overflow-hidden mb-12 shadow-2xl animate-slide-up delay-300">
            <Image
              src={urlFor(post.mainImage).width(1200).height(600).fit('crop').auto('format').url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg prose-slate max-w-none animate-slide-up delay-400">
          <PortableTextRenderer content={post.body} />
        </div>

        {/* Share Section */}
        <div className="mt-16 pt-8 border-t border-gradient-to-r from-indigo-200/50 to-purple-200/50 animate-slide-up delay-500">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-2">
                Found this insight magical?
              </h3>
              <p className="text-slate-600 text-lg">
                Share the magic with your healthcare colleagues
              </p>
            </div>
            <button
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: post.title,
                    text: post.excerpt || '',
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert('Link copied to clipboard!')
                }
              }}
              className="group flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-8 py-4 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <Share2 className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
              <span>Share the Magic</span>
            </button>
          </div>
        </div>
      </article>
    </div>
  )
}
