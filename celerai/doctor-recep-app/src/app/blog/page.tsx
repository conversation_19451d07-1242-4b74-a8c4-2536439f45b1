import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { getBlogPosts, urlFor } from '@/lib/sanity/client'
import { trackEvent } from '@/lib/analytics'
import { Calendar, Clock, ArrowRight, Sparkles } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Blog - Celer AI',
  description: 'Latest insights, tips, and updates about AI-powered healthcare documentation',
  keywords: 'healthcare, AI, medical documentation, blog, insights',
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function BlogPage() {
  const posts = await getBlogPosts()

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            <Link
              href="/blog"
              className="text-slate-900 text-sm font-medium"
            >
              Blog
            </Link>
            <Link
              href="/guide"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Get Started
            </Link>
          </div>
        </div>
      </nav>

      {/* Magical Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-20 h-20 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Hero Section */}
      <main className="relative">
        <div className="relative max-w-4xl mx-auto px-6 pt-32 pb-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-black text-slate-900 leading-relaxed mb-4">
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse pb-2">
                Insights
              </span>
            </h1>

            <p className="text-lg text-slate-600 mb-6">
              Effortless insights that flow naturally
            </p>
          </div>
        </div>
      </main>

      {/* Blog Posts Grid */}
      <section className="relative pb-16 px-6">
        <div className="max-w-6xl mx-auto">
          {posts.length === 0 ? (
            <div className="text-center py-16 animate-fade-in">
              <div className="relative">
                <div className="w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse">
                  <Calendar className="w-16 h-16 text-indigo-500" />
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-cyan-500/20 rounded-full blur-xl animate-pulse"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">Magical insights coming soon</h3>
              <p className="text-lg text-slate-600 max-w-md mx-auto">
                We're crafting effortless insights that will make AI healthcare feel like magic.
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 font-medium">Stay tuned!</span>
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-slide-up delay-400">
              {posts.map((post, index) => (
                <div
                  key={post._id}
                  className="animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <BlogPostCard post={post} />
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

function BlogPostCard({ post }: { post: any }) {
  const publishedDate = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const handleClick = () => {
    // Track blog post view in public zone
    if (typeof window !== 'undefined') {
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('blog_post_viewed', { page_title: post.title })
      })
    }
  }

  return (
    <article className="relative group">
      {/* Magical glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-all duration-500"></div>

      <div className="relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden border border-white/20">
        {/* Featured Image */}
        {post.mainImage && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={urlFor(post.mainImage).width(400).height(300).fit('crop').auto('format').url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

            {/* Magical sparkle effect */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Sparkles className="w-5 h-5 text-white animate-pulse" />
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Categories */}
          {post.categories && post.categories.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {post.categories.slice(0, 2).map((category: any) => (
                <span
                  key={category.slug.current}
                  className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-xs font-medium rounded-full border border-indigo-200"
                >
                  {category.title}
                </span>
              ))}
            </div>
          )}

          {/* Title */}
          <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-purple-600 transition-all duration-300">
            {post.title}
          </h2>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-slate-600 mb-6 line-clamp-3 leading-relaxed">
              {post.excerpt}
            </p>
          )}

          {/* Meta */}
          <div className="flex items-center justify-between text-sm text-slate-500 mb-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{publishedDate}</span>
              </div>
              {post.author && (
                <div className="flex items-center space-x-2">
                  {post.author.image && (
                    <Image
                      src={urlFor(post.author.image).width(24).height(24).fit('crop').auto('format').url()}
                      alt={post.author.name}
                      width={24}
                      height={24}
                      className="rounded-full border border-white/50"
                    />
                  )}
                  <span>{post.author.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Read More Link */}
          <Link
            href={`/blog/${post.slug.current}`}
            onClick={handleClick}
            className="group/link inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span>Read Insight</span>
            <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </div>
    </article>
  )
}
