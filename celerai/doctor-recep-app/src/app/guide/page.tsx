import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { getGuidePosts, urlFor } from '@/lib/sanity/client'
import { BookO<PERSON>, Clock, ArrowRight, Star, Sparkles } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Guides - Celer AI',
  description: 'Step-by-step guides for healthcare professionals using AI documentation tools',
  keywords: 'healthcare guides, AI documentation, medical tutorials, how-to',
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function GuidePage() {
  const guides = await getGuidePosts()

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            <Link
              href="/blog"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Blog
            </Link>
            <Link
              href="/guide"
              className="text-slate-900 text-sm font-medium"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Get Started
            </Link>
          </div>
        </div>
      </nav>

      {/* Magical Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-20 h-20 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Hero Section */}
      <main className="relative">
        <div className="relative max-w-4xl mx-auto px-6 pt-24 pb-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4">
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
                Guides
              </span>
            </h1>

            <p className="text-lg text-slate-600 mb-6">
              Effortless step-by-step tutorials that flow like magic
            </p>
          </div>
        </div>
      </main>

      {/* Guides Grid */}
      <section className="relative pb-16 px-6">
        <div className="max-w-6xl mx-auto">
          {guides.length === 0 ? (
            <div className="text-center py-16 animate-fade-in">
              <div className="relative">
                <div className="w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse">
                  <BookOpen className="w-16 h-16 text-indigo-500" />
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-cyan-500/20 rounded-full blur-xl animate-pulse"></div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">Magical guides coming soon</h3>
              <p className="text-lg text-slate-600 max-w-md mx-auto">
                We're crafting effortless tutorials that will make AI documentation feel like magic.
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 font-medium">Stay tuned!</span>
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-slide-up delay-400">
              {guides.map((guide, index) => (
                <div
                  key={guide._id}
                  className="animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <GuideCard guide={guide} />
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

function GuideCard({ guide }: { guide: any }) {
  const publishedDate = new Date(guide.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const difficultyColors = {
    beginner: 'bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 border border-emerald-200',
    intermediate: 'bg-gradient-to-r from-amber-100 to-orange-100 text-amber-700 border border-amber-200',
    advanced: 'bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200'
  }

  const handleClick = () => {
    // Track guide view in public zone
    if (typeof window !== 'undefined') {
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('guide_viewed', { page_title: guide.title })
      })
    }
  }

  return (
    <article className="relative group">
      {/* Magical glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-all duration-500"></div>

      <div className="relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden border border-white/20">
        {/* Featured Image */}
        {guide.mainImage && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={urlFor(guide.mainImage).width(400).height(300).fit('crop').auto('format').url()}
              alt={guide.mainImage.alt || guide.title}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

            {/* Difficulty Badge */}
            {guide.difficulty && (
              <div className="absolute top-4 left-4">
                <span className={`px-3 py-1 text-xs font-medium rounded-full backdrop-blur-sm ${difficultyColors[guide.difficulty as keyof typeof difficultyColors] || difficultyColors.beginner}`}>
                  {guide.difficulty}
                </span>
              </div>
            )}

            {/* Magical sparkle effect */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Sparkles className="w-5 h-5 text-white animate-pulse" />
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {guide.tags.slice(0, 2).map((tag: any) => (
                <span
                  key={tag.slug.current}
                  className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-xs font-medium rounded-full border border-indigo-200"
                >
                  {tag.title}
                </span>
              ))}
            </div>
          )}

          {/* Title */}
          <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-purple-600 transition-all duration-300">
            {guide.title}
          </h2>

          {/* Excerpt */}
          {guide.excerpt && (
            <p className="text-slate-600 mb-6 line-clamp-3 leading-relaxed">
              {guide.excerpt}
            </p>
          )}

          {/* Meta */}
          <div className="flex items-center justify-between text-sm text-slate-500 mb-6">
            <div className="flex items-center space-x-4">
              {guide.estimatedReadTime && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{guide.estimatedReadTime} min read</span>
                </div>
              )}
              <div className="flex items-center space-x-1">
                <BookOpen className="w-4 h-4" />
                <span>{publishedDate}</span>
              </div>
            </div>
          </div>

          {/* Read Guide Link */}
          <Link
            href={`/guide/${guide.slug.current}`}
            onClick={handleClick}
            className="group/link inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span>Start Learning</span>
            <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </div>
    </article>
  )
}
