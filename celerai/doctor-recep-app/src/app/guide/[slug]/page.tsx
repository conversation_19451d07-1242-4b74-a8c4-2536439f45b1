import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { getGuidePost, getGuidePosts, urlFor } from '@/lib/sanity/client'
import { PortableTextRenderer } from '@/components/sanity/portable-text-renderer'
import { BookOpen, Clock, ArrowLeft, Share2, Star } from 'lucide-react'

interface GuidePostPageProps {
  params: Promise<{ slug: string }>
}

// Generate static params for ISR
export async function generateStaticParams() {
  const guides = await getGuidePosts()
  return guides.map((guide) => ({
    slug: guide.slug.current,
  }))
}

// Generate metadata for SEO
export async function generateMetadata({ params }: GuidePostPageProps): Promise<Metadata> {
  const { slug } = await params
  const guide = await getGuidePost(slug)

  if (!guide) {
    return {
      title: 'Guide Not Found - Celer AI',
      description: 'The requested guide could not be found.',
    }
  }

  return {
    title: guide.seo?.title || `${guide.title} - Celer AI Guides`,
    description: guide.seo?.description || guide.excerpt || `Learn ${guide.title} with our comprehensive guide`,
    keywords: guide.seo?.keywords?.join(', ') || 'healthcare guides, AI documentation, medical tutorials',
    openGraph: {
      title: guide.title,
      description: guide.excerpt || '',
      type: 'article',
      publishedTime: guide.publishedAt,
      images: guide.mainImage ? [
        {
          url: urlFor(guide.mainImage).width(1200).height(630).fit('crop').auto('format').url(),
          width: 1200,
          height: 630,
          alt: guide.mainImage.alt || guide.title,
        }
      ] : undefined,
    },
  }
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function GuidePostPage({ params }: GuidePostPageProps) {
  const { slug } = await params
  const guide = await getGuidePost(slug)

  if (!guide) {
    notFound()
  }

  const publishedDate = new Date(guide.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const difficultyColors = {
    beginner: 'bg-green-100 text-green-700 border-green-200',
    intermediate: 'bg-yellow-100 text-yellow-700 border-yellow-200',
    advanced: 'bg-red-100 text-red-700 border-red-200'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            <Link
              href="/blog"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Blog
            </Link>
            <Link
              href="/guide"
              className="text-slate-900 text-sm font-medium"
            >
              Guides
            </Link>
            <Link
              href="/login"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Get Started
            </Link>
          </div>
        </div>
      </nav>

      {/* Magical Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
      </div>

      {/* Back to Guides */}
      <div className="relative max-w-4xl mx-auto px-6 pt-32 pb-8">
        <Link
          href="/guide"
          className="inline-flex items-center space-x-2 text-slate-600 hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 group animate-fade-in"
        >
          <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300" />
          <span>Back to Guides</span>
        </Link>
      </div>

      {/* Article */}
      <article className="relative max-w-4xl mx-auto px-6 pb-16">
        {/* Guide Meta */}
        <div className="flex flex-wrap items-center gap-4 mb-8 animate-fade-in">
          {/* Difficulty */}
          {guide.difficulty && (
            <span className={`px-4 py-2 text-sm font-medium rounded-full backdrop-blur-sm border ${difficultyColors[guide.difficulty as keyof typeof difficultyColors] || difficultyColors.beginner}`}>
              {guide.difficulty} level
            </span>
          )}

          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {guide.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag.slug.current}
                  className="px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-sm font-medium rounded-full border border-indigo-200"
                >
                  {tag.title}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Title */}
        <h1 className="text-4xl md:text-6xl font-bold mb-8 leading-tight animate-slide-up">
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600">
            {guide.title}
          </span>
        </h1>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-12 pb-8 border-b border-gradient-to-r from-indigo-200/50 to-purple-200/50 animate-slide-up delay-200">
          <div className="flex items-center space-x-2">
            <BookOpen className="w-5 h-5 text-indigo-500" />
            <span>{publishedDate}</span>
          </div>
          {guide.estimatedReadTime && (
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-purple-500" />
              <span>{guide.estimatedReadTime} min read</span>
            </div>
          )}
        </div>

        {/* Featured Image */}
        {guide.mainImage && (
          <div className="relative h-64 md:h-96 rounded-3xl overflow-hidden mb-12 shadow-2xl animate-slide-up delay-300">
            <Image
              src={urlFor(guide.mainImage).width(1200).height(600).fit('crop').auto('format').url()}
              alt={guide.mainImage.alt || guide.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
          </div>
        )}

        {/* Excerpt */}
        {guide.excerpt && (
          <div className="relative bg-gradient-to-br from-indigo-50 via-white to-purple-50 border border-indigo-200/50 rounded-2xl p-8 mb-12 animate-slide-up delay-400">
            <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-cyan-500/20 rounded-2xl blur-lg opacity-50"></div>
            <div className="relative">
              <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-4">What you'll learn</h2>
              <p className="text-slate-700 leading-relaxed text-lg">{guide.excerpt}</p>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg prose-slate max-w-none animate-slide-up delay-500">
          <PortableTextRenderer content={guide.body} />
        </div>

        {/* Share Section */}
        <div className="mt-16 pt-8 border-t border-gradient-to-r from-indigo-200/50 to-purple-200/50 animate-slide-up delay-600">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-2">
                Found this guide magical?
              </h3>
              <p className="text-slate-600 text-lg">
                Share the magic with your healthcare team
              </p>
            </div>
            <button
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: guide.title,
                    text: guide.excerpt || '',
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert('Link copied to clipboard!')
                }
              }}
              className="group flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-8 py-4 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <Share2 className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
              <span>Share the Magic</span>
            </button>
          </div>
        </div>
      </article>
    </div>
  )
}
