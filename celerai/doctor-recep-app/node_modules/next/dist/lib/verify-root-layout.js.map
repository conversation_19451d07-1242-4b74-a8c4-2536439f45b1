{"version": 3, "sources": ["../../src/lib/verify-root-layout.ts"], "sourcesContent": ["import path from 'path'\nimport * as Log from '../build/output/log'\nimport { promises as fs } from 'fs'\nimport { bold } from './picocolors'\nimport { APP_DIR_ALIAS } from './constants'\nimport type { PageExtensions } from '../build/page-extensions-type'\n\nconst globOrig =\n  require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\nconst glob = (cwd: string, pattern: string): Promise<string[]> => {\n  return new Promise((resolve, reject) => {\n    globOrig(pattern, { cwd }, (err, files) => {\n      if (err) {\n        return reject(err)\n      }\n      resolve(files)\n    })\n  })\n}\n\nfunction getRootLayout(isTs: boolean) {\n  if (isTs) {\n    return `export const metadata = {\n  title: 'Next.js',\n  description: 'Generated by Next.js',\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"en\">\n      <body>{children}</body>\n    </html>\n  )\n}\n`\n  }\n\n  return `export const metadata = {\n  title: 'Next.js',\n  description: 'Generated by Next.js',\n}\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\">\n      <body>{children}</body>\n    </html>\n  )\n}\n`\n}\n\nexport async function verifyRootLayout({\n  dir,\n  appDir,\n  tsconfigPath,\n  pagePath,\n  pageExtensions,\n}: {\n  dir: string\n  appDir: string\n  tsconfigPath: string\n  pagePath: string\n  pageExtensions: PageExtensions\n}): Promise<[boolean, string | undefined]> {\n  let rootLayoutPath: string | undefined\n  try {\n    const layoutFiles = await glob(\n      appDir,\n      `**/layout.{${pageExtensions.join(',')}}`\n    )\n    const isFileUnderAppDir = pagePath.startsWith(`${APP_DIR_ALIAS}/`)\n    const normalizedPagePath = pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n    const pagePathSegments = normalizedPagePath.split('/')\n\n    // Find an available dir to place the layout file in, the layout file can't affect any other layout.\n    // Place the layout as close to app/ as possible.\n    let availableDir: string | undefined\n\n    if (isFileUnderAppDir) {\n      if (layoutFiles.length === 0) {\n        // If there's no other layout file we can place the layout file in the app dir.\n        // However, if the page is within a route group directly under app (e.g. app/(routegroup)/page.js)\n        // prefer creating the root layout in that route group.\n        const firstSegmentValue = pagePathSegments[0]\n        availableDir = firstSegmentValue.startsWith('(')\n          ? firstSegmentValue\n          : ''\n      } else {\n        pagePathSegments.pop() // remove the page from segments\n\n        let currentSegments: string[] = []\n        for (const segment of pagePathSegments) {\n          currentSegments.push(segment)\n          // Find the dir closest to app/ where a layout can be created without affecting other layouts.\n          if (\n            !layoutFiles.some((file) =>\n              file.startsWith(currentSegments.join('/'))\n            )\n          ) {\n            availableDir = currentSegments.join('/')\n            break\n          }\n        }\n      }\n    } else {\n      availableDir = ''\n    }\n\n    if (typeof availableDir === 'string') {\n      const resolvedTsConfigPath = path.join(dir, tsconfigPath)\n      const hasTsConfig = await fs.access(resolvedTsConfigPath).then(\n        () => true,\n        () => false\n      )\n\n      rootLayoutPath = path.join(\n        appDir,\n        availableDir,\n        `layout.${hasTsConfig ? 'tsx' : 'js'}`\n      )\n      await fs.writeFile(rootLayoutPath, getRootLayout(hasTsConfig))\n\n      Log.warn(\n        `Your page ${bold(\n          `app/${normalizedPagePath}`\n        )} did not have a root layout. We created ${bold(\n          `app${rootLayoutPath.replace(appDir, '')}`\n        )} for you.`\n      )\n\n      // Created root layout\n      return [true, rootLayoutPath]\n    }\n  } catch (e) {\n    console.error(e)\n  }\n\n  // Didn't create root layout\n  return [false, rootLayoutPath]\n}\n"], "names": ["verifyRootLayout", "globOrig", "require", "glob", "cwd", "pattern", "Promise", "resolve", "reject", "err", "files", "getRootLayout", "isTs", "dir", "appDir", "tsconfigPath", "pagePath", "pageExtensions", "rootLayoutPath", "layoutFiles", "join", "isFileUnderAppDir", "startsWith", "APP_DIR_ALIAS", "normalizedPagePath", "replace", "pagePathSegments", "split", "availableDir", "length", "firstSegmentValue", "pop", "currentSegments", "segment", "push", "some", "file", "resolvedTsConfigPath", "path", "hasTsConfig", "fs", "access", "then", "writeFile", "Log", "warn", "bold", "e", "console", "error"], "mappings": ";;;;+BAwDsBA;;;eAAAA;;;6DAxDL;6DACI;oBACU;4BACV;2BACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG9B,MAAMC,WACJC,QAAQ;AACV,MAAMC,OAAO,CAACC,KAAaC;IACzB,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BP,SAASI,SAAS;YAAED;QAAI,GAAG,CAACK,KAAKC;YAC/B,IAAID,KAAK;gBACP,OAAOD,OAAOC;YAChB;YACAF,QAAQG;QACV;IACF;AACF;AAEA,SAASC,cAAcC,IAAa;IAClC,IAAIA,MAAM;QACR,OAAO,CAAC;;;;;;;;;;;;;;;;AAgBZ,CAAC;IACC;IAEA,OAAO,CAAC;;;;;;;;;;;;AAYV,CAAC;AACD;AAEO,eAAeZ,iBAAiB,EACrCa,GAAG,EACHC,MAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,cAAc,EAOf;IACC,IAAIC;IACJ,IAAI;QACF,MAAMC,cAAc,MAAMhB,KACxBW,QACA,CAAC,WAAW,EAAEG,eAAeG,IAAI,CAAC,KAAK,CAAC,CAAC;QAE3C,MAAMC,oBAAoBL,SAASM,UAAU,CAAC,GAAGC,wBAAa,CAAC,CAAC,CAAC;QACjE,MAAMC,qBAAqBR,SAASS,OAAO,CAAC,GAAGF,wBAAa,CAAC,CAAC,CAAC,EAAE;QACjE,MAAMG,mBAAmBF,mBAAmBG,KAAK,CAAC;QAElD,oGAAoG;QACpG,iDAAiD;QACjD,IAAIC;QAEJ,IAAIP,mBAAmB;YACrB,IAAIF,YAAYU,MAAM,KAAK,GAAG;gBAC5B,+EAA+E;gBAC/E,kGAAkG;gBAClG,uDAAuD;gBACvD,MAAMC,oBAAoBJ,gBAAgB,CAAC,EAAE;gBAC7CE,eAAeE,kBAAkBR,UAAU,CAAC,OACxCQ,oBACA;YACN,OAAO;gBACLJ,iBAAiBK,GAAG,GAAG,gCAAgC;;gBAEvD,IAAIC,kBAA4B,EAAE;gBAClC,KAAK,MAAMC,WAAWP,iBAAkB;oBACtCM,gBAAgBE,IAAI,CAACD;oBACrB,8FAA8F;oBAC9F,IACE,CAACd,YAAYgB,IAAI,CAAC,CAACC,OACjBA,KAAKd,UAAU,CAACU,gBAAgBZ,IAAI,CAAC,QAEvC;wBACAQ,eAAeI,gBAAgBZ,IAAI,CAAC;wBACpC;oBACF;gBACF;YACF;QACF,OAAO;YACLQ,eAAe;QACjB;QAEA,IAAI,OAAOA,iBAAiB,UAAU;YACpC,MAAMS,uBAAuBC,aAAI,CAAClB,IAAI,CAACP,KAAKE;YAC5C,MAAMwB,cAAc,MAAMC,YAAE,CAACC,MAAM,CAACJ,sBAAsBK,IAAI,CAC5D,IAAM,MACN,IAAM;YAGRxB,iBAAiBoB,aAAI,CAAClB,IAAI,CACxBN,QACAc,cACA,CAAC,OAAO,EAAEW,cAAc,QAAQ,MAAM;YAExC,MAAMC,YAAE,CAACG,SAAS,CAACzB,gBAAgBP,cAAc4B;YAEjDK,KAAIC,IAAI,CACN,CAAC,UAAU,EAAEC,IAAAA,gBAAI,EACf,CAAC,IAAI,EAAEtB,oBAAoB,EAC3B,wCAAwC,EAAEsB,IAAAA,gBAAI,EAC9C,CAAC,GAAG,EAAE5B,eAAeO,OAAO,CAACX,QAAQ,KAAK,EAC1C,SAAS,CAAC;YAGd,sBAAsB;YACtB,OAAO;gBAAC;gBAAMI;aAAe;QAC/B;IACF,EAAE,OAAO6B,GAAG;QACVC,QAAQC,KAAK,CAACF;IAChB;IAEA,4BAA4B;IAC5B,OAAO;QAAC;QAAO7B;KAAe;AAChC"}