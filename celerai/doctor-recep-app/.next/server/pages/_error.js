const CHUNK_PUBLIC_PATH = "server/pages/_error.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__c9b4b591._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__5402c70b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_9babd674._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ad3e0e0e._.js");
runtime.getOrInstantiateRuntimeModule(710516, CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule(710516, CHUNK_PUBLIC_PATH).exports;
