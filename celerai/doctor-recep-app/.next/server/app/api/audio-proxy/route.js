const CHUNK_PUBLIC_PATH = "server/app/api/audio-proxy/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__e34776b1._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__86bee23c._.js");
runtime.getOrInstantiateRuntimeModule(439215, CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule(494990, CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule(494990, CHUNK_PUBLIC_PATH).exports;
