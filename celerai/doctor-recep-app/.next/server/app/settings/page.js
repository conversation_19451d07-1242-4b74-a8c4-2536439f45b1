const CHUNK_PUBLIC_PATH = "server/app/settings/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d666751f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_af5dfab8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c43b618f._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__f2513a0e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/src_app_global-error_tsx_d6ef94b5._.js");
runtime.loadChunk("server/chunks/ssr/src_lib_actions_4f0c67fa._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_2bdf796c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_bcryptjs_index_174bbc90.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_8af3edd5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_367ca84c._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__1e050229._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_1ddcfd6f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_cd3fc520._.js");
runtime.loadChunk("server/chunks/ssr/_af2fd9f7._.js");
runtime.loadChunk("server/chunks/ssr/_e4d54259._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_jose_dist_webapi_63f4176e._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__c254815f._.js");
runtime.getOrInstantiateRuntimeModule(655425, CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule(670946, CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule(670946, CHUNK_PUBLIC_PATH).exports;
