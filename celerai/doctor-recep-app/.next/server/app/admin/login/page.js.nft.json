{"version": 1, "files": ["../../../../../ANALYTICS_AND_CMS_SETUP.md", "../../../../../DEPLOYMENT.md", "../../../../../IMPLEMENTATION_SUMMARY.md", "../../../../../PORTS_CONFIG.md", "../../../../../QUOTA_ADMIN_SETUP.md", "../../../../../README.md", "../../../../../context.md", "../../../../../database/create-functions.sql", "../../../../../database/fix-discount-calculation.sql", "../../../../../database/fix-referral-functions.sql", "../../../../../database/fix-rls-policies.sql", "../../../../../database/fix-rls-uuid-error.sql", "../../../../../database/fixed-functions.sql", "../../../../../database/migrations/001_add_quota_and_admin_system.sql", "../../../../../database/migrations/002_add_referral_system.sql", "../../../../../database/migrations/003_add_billing_system.sql", "../../../../../database/migrations/004_add_consultation_types_and_notes.sql", "../../../../../database/migrations/005_expand_consultation_types.sql", "../../../../../database/schema.sql", "../../../../../database/update-patient-numbering.sql", "../../../../../deploy-frontend.sh", "../../../../../eslint.config.mjs", "../../../../../next.config.js", "../../../../../node_modules/@opentelemetry/api/build/src/api/context.js", "../../../../../node_modules/@opentelemetry/api/build/src/api/diag.js", "../../../../../node_modules/@opentelemetry/api/build/src/api/metrics.js", "../../../../../node_modules/@opentelemetry/api/build/src/api/propagation.js", "../../../../../node_modules/@opentelemetry/api/build/src/api/trace.js", "../../../../../node_modules/@opentelemetry/api/build/src/baggage/context-helpers.js", "../../../../../node_modules/@opentelemetry/api/build/src/baggage/internal/baggage-impl.js", "../../../../../node_modules/@opentelemetry/api/build/src/baggage/internal/symbol.js", "../../../../../node_modules/@opentelemetry/api/build/src/baggage/utils.js", "../../../../../node_modules/@opentelemetry/api/build/src/context-api.js", "../../../../../node_modules/@opentelemetry/api/build/src/context/NoopContextManager.js", "../../../../../node_modules/@opentelemetry/api/build/src/context/context.js", "../../../../../node_modules/@opentelemetry/api/build/src/diag-api.js", "../../../../../node_modules/@opentelemetry/api/build/src/diag/ComponentLogger.js", "../../../../../node_modules/@opentelemetry/api/build/src/diag/consoleLogger.js", "../../../../../node_modules/@opentelemetry/api/build/src/diag/internal/logLevelLogger.js", "../../../../../node_modules/@opentelemetry/api/build/src/diag/types.js", "../../../../../node_modules/@opentelemetry/api/build/src/index.js", "../../../../../node_modules/@opentelemetry/api/build/src/internal/global-utils.js", "../../../../../node_modules/@opentelemetry/api/build/src/internal/semver.js", "../../../../../node_modules/@opentelemetry/api/build/src/metrics-api.js", "../../../../../node_modules/@opentelemetry/api/build/src/metrics/Metric.js", "../../../../../node_modules/@opentelemetry/api/build/src/metrics/NoopMeter.js", "../../../../../node_modules/@opentelemetry/api/build/src/metrics/NoopMeterProvider.js", "../../../../../node_modules/@opentelemetry/api/build/src/platform/index.js", "../../../../../node_modules/@opentelemetry/api/build/src/platform/node/globalThis.js", "../../../../../node_modules/@opentelemetry/api/build/src/platform/node/index.js", "../../../../../node_modules/@opentelemetry/api/build/src/propagation-api.js", "../../../../../node_modules/@opentelemetry/api/build/src/propagation/NoopTextMapPropagator.js", "../../../../../node_modules/@opentelemetry/api/build/src/propagation/TextMapPropagator.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace-api.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/NonRecordingSpan.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/NoopTracer.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/NoopTracerProvider.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/ProxyTracer.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/ProxyTracerProvider.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/SamplingResult.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/context-utils.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/tracestate-impl.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/tracestate-validators.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/utils.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/invalid-span-constants.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/span_kind.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/spancontext-utils.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/status.js", "../../../../../node_modules/@opentelemetry/api/build/src/trace/trace_flags.js", "../../../../../node_modules/@opentelemetry/api/build/src/version.js", "../../../../../node_modules/@opentelemetry/api/package.json", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/index.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/loaders/index.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/loaders/prefixLoader.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/loaders/valueInjectionLoader.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/loaders/wrappingLoader.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/apiWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/middlewareWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/pageWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/requestAsyncStorageShim.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/routeHandlerWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/sentryInitWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/templates/serverComponentWrapperTemplate.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/util.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/webpack.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/webpackPluginOptions.js", "../../../../../node_modules/@sentry/nextjs/build/cjs/config/withSentryConfig.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/async.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/caller.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/core.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/core.json", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/homedir.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/is-core.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/node-modules-paths.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/normalize-options.js", "../../../../../node_modules/@sentry/nextjs/node_modules/resolve/lib/sync.js", "../../../../../node_modules/debug/package.json", "../../../../../node_modules/debug/src/browser.js", "../../../../../node_modules/debug/src/common.js", "../../../../../node_modules/debug/src/index.js", "../../../../../node_modules/debug/src/node.js", "../../../../../node_modules/function-bind/implementation.js", "../../../../../node_modules/function-bind/index.js", "../../../../../node_modules/function-bind/package.json", "../../../../../node_modules/has-flag/index.js", "../../../../../node_modules/has-flag/package.json", "../../../../../node_modules/hasown/index.js", "../../../../../node_modules/hasown/package.json", "../../../../../node_modules/import-in-the-middle/index.js", "../../../../../node_modules/import-in-the-middle/lib/register.js", "../../../../../node_modules/import-in-the-middle/package.json", "../../../../../node_modules/is-core-module/core.json", "../../../../../node_modules/is-core-module/index.js", "../../../../../node_modules/is-core-module/package.json", "../../../../../node_modules/module-details-from-path/index.js", "../../../../../node_modules/module-details-from-path/package.json", "../../../../../node_modules/ms/index.js", "../../../../../node_modules/ms/package.json", "../../../../../node_modules/next/dist/client/components/app-router-headers.js", "../../../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../../../node_modules/next/dist/compiled/next-server/app-page-turbo.runtime.prod.js", "../../../../../node_modules/next/dist/server/app-render/action-async-storage-instance.js", "../../../../../node_modules/next/dist/server/app-render/action-async-storage.external.js", "../../../../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js", "../../../../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js", "../../../../../node_modules/next/dist/server/app-render/async-local-storage.js", "../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot-instance.js", "../../../../../node_modules/next/dist/server/app-render/clean-async-snapshot.external.js", "../../../../../node_modules/next/dist/server/app-render/work-async-storage-instance.js", "../../../../../node_modules/next/dist/server/app-render/work-async-storage.external.js", "../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js", "../../../../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js", "../../../../../node_modules/next/dist/server/lib/incremental-cache/tags-manifest.external.js", "../../../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../../../node_modules/next/dist/shared/lib/is-thenable.js", "../../../../../node_modules/next/package.json", "../../../../../node_modules/path-parse/index.js", "../../../../../node_modules/path-parse/package.json", "../../../../../node_modules/require-in-the-middle/index.js", "../../../../../node_modules/require-in-the-middle/package.json", "../../../../../node_modules/resolve/index.js", "../../../../../node_modules/resolve/lib/async.js", "../../../../../node_modules/resolve/lib/caller.js", "../../../../../node_modules/resolve/lib/core.js", "../../../../../node_modules/resolve/lib/core.json", "../../../../../node_modules/resolve/lib/homedir.js", "../../../../../node_modules/resolve/lib/is-core.js", "../../../../../node_modules/resolve/lib/node-modules-paths.js", "../../../../../node_modules/resolve/lib/normalize-options.js", "../../../../../node_modules/resolve/lib/sync.js", "../../../../../node_modules/resolve/package.json", "../../../../../node_modules/supports-color/index.js", "../../../../../node_modules/supports-color/package.json", "../../../../../package-lock.json", "../../../../../package.json", "../../../../../postcss.config.mjs", "../../../../../public/celer-ai-logo.svg", "../../../../../public/favicon.ico", "../../../../../public/file.svg", "../../../../../public/globe.svg", "../../../../../public/icons/apple-touch-icon-120x120.png", "../../../../../public/icons/apple-touch-icon-152x152.png", "../../../../../public/icons/apple-touch-icon-180x180.png", "../../../../../public/icons/celer-ai-logo.svg", "../../../../../public/icons/favicon-16x16.png", "../../../../../public/icons/favicon-32x32.png", "../../../../../public/icons/favicon-48x48.png", "../../../../../public/icons/icon-128x128.png", "../../../../../public/icons/icon-144x144.png", "../../../../../public/icons/icon-152x152.png", "../../../../../public/icons/icon-192x192.png", "../../../../../public/icons/icon-384x384.png", "../../../../../public/icons/icon-512x512.png", "../../../../../public/icons/icon-72x72.png", "../../../../../public/icons/icon-96x96.png", "../../../../../public/icons/logo-1024x1024.png", "../../../../../public/manifest.json", "../../../../../public/next.svg", "../../../../../public/vercel.svg", "../../../../../public/window.svg", "../../../../../python-backend/Dockerfile", "../../../../../python-backend/README.md", "../../../../../python-backend/__pycache__/main.cpython-312.pyc", "../../../../../python-backend/__pycache__/main.cpython-313.pyc", "../../../../../python-backend/cloudbuild.yaml", "../../../../../python-backend/deploy.sh", "../../../../../python-backend/main.py", "../../../../../python-backend/prompts.json", "../../../../../python-backend/requirements.txt", "../../../../../python-backend/start.sh", "../../../../../r2-cors-config.json", "../../../../../r2-cors-final.json", "../../../../../r2-cors-simple.json", "../../../../../scripts/analyze-database-performance.js", "../../../../../scripts/check-and-fix-doctors.js", "../../../../../scripts/check-complete-payment.js", "../../../../../scripts/check-db-functions.js", "../../../../../scripts/check-discount-statuses.js", "../../../../../scripts/check-discount-sync.js", "../../../../../scripts/check-existing-files.js", "../../../../../scripts/check-url-status.js", "../../../../../scripts/create-admin.js", "../../../../../scripts/create-database-functions.js", "../../../../../scripts/create-referral-functions.js", "../../../../../scripts/create-test-admin.js", "../../../../../scripts/create-test-doctor.js", "../../../../../scripts/debug-billing-stats.js", "../../../../../scripts/discount-fix-sql.sql", "../../../../../scripts/execute-discount-fix.js", "../../../../../scripts/fix-db-functions-properly.sql", "../../../../../scripts/fix-discount-issues.js", "../../../../../scripts/fix-discount-sql.js", "../../../../../scripts/inspect-supabase-config.js", "../../../../../scripts/migrate-supabase-to-r2.js", "../../../../../scripts/reset-admin-password.js", "../../../../../scripts/test-current-behavior.js", "../../../../../scripts/test-frontend-calculations.js", "../../../../../scripts/test-functions.js", "../../../../../scripts/test-r2-connection.js", "../../../../../scripts/test-referral-functions.js", "../../../../../scripts/update-discount-calculation.js", "../../../../../scripts/verify-migration-setup.js", "../../../../../scripts/verify-multitenant.js", "../../../../../scripts/verify-r2-migration.js", "../../../../../sentry.edge.config.ts", "../../../../../sentry.server.config.ts", "../../../../../src/app/admin/dashboard/page.tsx", "../../../../../src/app/admin/login/page.tsx", "../../../../../src/app/api/audio-proxy/route.ts", "../../../../../src/app/api/contact-founder/route.ts", "../../../../../src/app/api/generate-summary-stream/route.ts", "../../../../../src/app/api/sentry-example-api/route.ts", "../../../../../src/app/blog/[slug]/page.tsx", "../../../../../src/app/blog/page.tsx", "../../../../../src/app/dashboard/page.tsx", "../../../../../src/app/global-error.tsx", "../../../../../src/app/globals.css", "../../../../../src/app/guide/[slug]/page.tsx", "../../../../../src/app/guide/page.tsx", "../../../../../src/app/info/page.tsx", "../../../../../src/app/layout.tsx", "../../../../../src/app/login/page.tsx", "../../../../../src/app/mobile/page.tsx", "../../../../../src/app/page-original-backup.tsx", "../../../../../src/app/page.tsx", "../../../../../src/app/privacy/page.tsx", "../../../../../src/app/sentry-example-page/page.tsx", "../../../../../src/app/settings/page.tsx", "../../../../../src/app/signup/page.tsx", "../../../../../src/app/templates/page.tsx", "../../../../../src/app/terms/page.tsx", "../../../../../src/components/admin/admin-auth-wrapper.tsx", "../../../../../src/components/admin/admin-dashboard-header.tsx", "../../../../../src/components/admin/admin-dashboard-stats.tsx", "../../../../../src/components/admin/admin-login-form.tsx", "../../../../../src/components/admin/billing-management.tsx", "../../../../../src/components/admin/doctors-table.tsx", "../../../../../src/components/analytics/analytics-provider.tsx", "../../../../../src/components/analytics/consultation-modal.tsx", "../../../../../src/components/analytics/consultations-list.tsx", "../../../../../src/components/analytics/dashboard-stats.tsx", "../../../../../src/components/analytics/quota-card.tsx", "../../../../../src/components/analytics/quota-warning-modal.tsx", "../../../../../src/components/analytics/referral-card.tsx", "../../../../../src/components/analytics/referral-stats.tsx", "../../../../../src/components/auth/login-form.tsx", "../../../../../src/components/auth/signup-form.tsx", "../../../../../src/components/data/admin-data.tsx", "../../../../../src/components/data/dashboard-data.tsx", "../../../../../src/components/data/info-data.tsx", "../../../../../src/components/mobile/audio-recorder.tsx", "../../../../../src/components/mobile/image-capture.tsx", "../../../../../src/components/mobile/recording-interface.tsx", "../../../../../src/components/mobile/submission-form.tsx", "../../../../../src/components/pwa/pwa-install-prompt.tsx", "../../../../../src/components/recording/consultations-sidebar.tsx", "../../../../../src/components/recording/content-editable-editor.tsx", "../../../../../src/components/recording/new-navbar.tsx", "../../../../../src/components/recording/new-recording-interface.tsx", "../../../../../src/components/recording/recording-main-area.tsx", "../../../../../src/components/recording/streamlined-recording-area.tsx", "../../../../../src/components/sanity/portable-text-renderer.tsx", "../../../../../src/components/settings/password-change-modal.tsx", "../../../../../src/components/settings/settings-form.tsx", "../../../../../src/components/shared/dashboard-client.tsx", "../../../../../src/components/shared/dashboard-header.tsx", "../../../../../src/components/shared/referred-welcome-modal.tsx", "../../../../../src/components/templates/coming-soon-overlay.tsx", "../../../../../src/components/templates/template-editor.tsx", "../../../../../src/components/templates/templates-interface.tsx", "../../../../../src/components/templates/templates-sidebar.tsx", "../../../../../src/components/ui/autosave-indicator.tsx", "../../../../../src/components/ui/badge.tsx", "../../../../../src/components/ui/button.tsx", "../../../../../src/components/ui/card.tsx", "../../../../../src/components/ui/confirmation-dialog.tsx", "../../../../../src/components/ui/input.tsx", "../../../../../src/components/ui/loading-wrapper.tsx", "../../../../../src/components/ui/payment-details-modal.tsx", "../../../../../src/components/ui/plan-selection-modal.tsx", "../../../../../src/components/ui/separator.tsx", "../../../../../src/components/ui/skeleton-loaders.tsx", "../../../../../src/hooks/useAutosave.ts", "../../../../../src/instrumentation-client.ts", "../../../../../src/instrumentation.ts", "../../../../../src/lib/actions/admin-auth.ts", "../../../../../src/lib/actions/admin.ts", "../../../../../src/lib/actions/auth.ts", "../../../../../src/lib/actions/billing.ts", "../../../../../src/lib/actions/consultations.ts", "../../../../../src/lib/actions/contact-requests.ts", "../../../../../src/lib/actions/referrals.ts", "../../../../../src/lib/actions/settings.ts", "../../../../../src/lib/analytics.ts", "../../../../../src/lib/auth/admin-dal.ts", "../../../../../src/lib/auth/admin-session.ts", "../../../../../src/lib/auth/dal.ts", "../../../../../src/lib/auth/session.ts", "../../../../../src/lib/sanity/client.ts", "../../../../../src/lib/storage.ts", "../../../../../src/lib/supabase/client.ts", "../../../../../src/lib/supabase/server.ts", "../../../../../src/lib/types.ts", "../../../../../src/lib/utils.ts", "../../../../../src/lib/validations.ts", "../../../../../start-all.sh", "../../../../../start-frontend.sh", "../../../../../tests/ai-prompt-test.js", "../../../../../tests/auth-test.js", "../../../../../tests/browser-login-test.js", "../../../../../tests/complete-e2e-test.js", "../../../../../tests/complete-test-results.json", "../../../../../tests/database-test.js", "../../../../../tests/e2e-example-files-test.js", "../../../../../tests/e2e-test.js", "../../../../../tests/final-verification.js", "../../../../../tests/gemini-files-api.test.py", "../../../../../tests/quota-system-test.js", "../../../../../tests/run-all-tests.js", "../../../../../tests/session-test.js", "../../../../../tests/test-results.json", "../../../../../tests/user-check.js", "../../../../../tsconfig.json", "../../../../../vercel.json", "../../../chunks/ssr/[externals]_node:inspector_10d23a46._.js", "../../../chunks/ssr/[root-of-the-server]__65230092._.js", "../../../chunks/ssr/[root-of-the-server]__7a29843d._.js", "../../../chunks/ssr/[root-of-the-server]__c254815f._.js", "../../../chunks/ssr/[root-of-the-server]__d666751f._.js", "../../../chunks/ssr/[root-of-the-server]__d96be563._.js", "../../../chunks/ssr/[root-of-the-server]__f2513a0e._.js", "../../../chunks/ssr/[root-of-the-server]__fe8c6a5f._.js", "../../../chunks/ssr/[turbopack]_runtime.js", "../../../chunks/ssr/_af2fd9f7._.js", "../../../chunks/ssr/_c477e631._.js", "../../../chunks/ssr/_e4d54259._.js", "../../../chunks/ssr/_fd3dfc2f._.js", "../../../chunks/ssr/node_modules_96715ba7._.js", "../../../chunks/ssr/node_modules_b55cea7f._.js", "../../../chunks/ssr/node_modules_bcryptjs_index_174bbc90.js", "../../../chunks/ssr/node_modules_cd3fc520._.js", "../../../chunks/ssr/node_modules_jose_dist_webapi_63f4176e._.js", "../../../chunks/ssr/node_modules_next_dist_17e4cda8._.js", "../../../chunks/ssr/node_modules_next_dist_367ca84c._.js", "../../../chunks/ssr/node_modules_next_dist_764f4e55._.js", "../../../chunks/ssr/node_modules_next_dist_8af3edd5._.js", "../../../chunks/ssr/node_modules_next_dist_af5dfab8._.js", "../../../chunks/ssr/node_modules_next_dist_c43b618f._.js", "../../../chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js", "../../../chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js", "../../../chunks/ssr/node_modules_ws_58f5cae3._.js", "../../../chunks/ssr/src_app_global-error_tsx_d6ef94b5._.js", "./page/react-loadable-manifest.json", "./page_client-reference-manifest.js"]}