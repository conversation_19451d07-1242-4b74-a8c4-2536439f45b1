const CHUNK_PUBLIC_PATH = "server/app/privacy/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d666751f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_af5dfab8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c43b618f._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__f2513a0e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/src_app_global-error_tsx_d6ef94b5._.js");
runtime.loadChunk("server/chunks/ssr/_d0da81a4._.js");
runtime.getOrInstantiateRuntimeModule(601519, CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule(260528, CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule(260528, CHUNK_PUBLIC_PATH).exports;
