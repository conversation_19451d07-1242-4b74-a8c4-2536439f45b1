{"version": 3, "sources": ["turbopack:///[project]/src/app/api/audio-proxy/route.ts", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const audioUrl = searchParams.get('url')\n\n    if (!audioUrl) {\n      return NextResponse.json(\n        { error: 'Missing audio URL parameter' },\n        { status: 400 }\n      )\n    }\n\n    // Validate that the URL is from our trusted R2 domain\n    const allowedDomains = [\n      'celerai.tallyup.pro',\n      process.env.R2_PUBLIC_URL?.replace('https://', ''),\n    ].filter(Boolean)\n\n    const urlObj = new URL(audioUrl)\n    if (!allowedDomains.some(domain => urlObj.hostname === domain)) {\n      return NextResponse.json(\n        { error: 'Unauthorized audio source' },\n        { status: 403 }\n      )\n    }\n\n    // Fetch the audio file from R2\n    const response = await fetch(audioUrl, {\n      headers: {\n        'User-Agent': 'CelerAI-AudioProxy/1.0',\n      },\n    })\n\n    if (!response.ok) {\n      console.error(`Failed to fetch audio: ${response.status} ${response.statusText}`)\n      return NextResponse.json(\n        { error: 'Failed to fetch audio file' },\n        { status: response.status }\n      )\n    }\n\n    // Get the content type from the original response\n    const contentType = response.headers.get('Content-Type') || 'audio/webm'\n    const contentLength = response.headers.get('Content-Length')\n\n    // Create response headers with CORS support\n    const headers = new Headers({\n      'Content-Type': contentType,\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET',\n      'Access-Control-Allow-Headers': 'Content-Type',\n      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour\n    })\n\n    if (contentLength) {\n      headers.set('Content-Length', contentLength)\n    }\n\n    // Stream the audio data through the proxy\n    return new NextResponse(response.body, {\n      status: 200,\n      headers,\n    })\n\n  } catch (error) {\n    console.error('Audio proxy error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n", "import {\n  AppRouteRouteModule,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks"], "mappings": "ykCAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAW,EAAa,GAAG,CAAC,OAElC,GAAI,CAAC,EACH,OAAO,CADM,CACN,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,iBACkC,EACvC,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAiB,CACrB,sBACA,QAAQ,GAAG,CAAC,aAAa,EAAE,QAAQ,WAAY,IAChD,CAAC,MAAM,CAAC,SAEH,EAAS,IAAI,IAAI,GACvB,GAAI,CAAC,EAAe,IAAI,CAAC,GAAU,EAAO,QAAQ,GAAK,GACrD,MAD8D,CACvD,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,eACgC,EACrC,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAW,MAAM,MAAM,EAAU,CACrC,QAAS,CACP,aAAc,wBAChB,CACF,GAEA,GAAI,CAAC,EAAS,EAAE,CAEd,CAFgB,MAChB,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,EAAS,MAAM,CAAC,CAAC,EAAE,EAAS,UAAU,CAAA,CAAE,EACzE,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,gBACiC,EACtC,CAAE,OAAQ,EAAS,MAAM,AAAC,GAK9B,IAAM,EAAc,EAAS,OAAO,CAAC,GAAG,CAAC,iBAAmB,aACtD,EAAgB,EAAS,OAAO,CAAC,GAAG,CAAC,kBAGrC,EAAU,IAAI,QAAQ,CAC1B,eAAgB,EAChB,8BAA+B,IAC/B,+BAAgC,MAChC,+BAAgC,eAChC,gBAAiB,sBACnB,GAOA,OALI,GACF,EAAQ,GAAG,CAAC,MADK,WACa,GAIzB,IAAI,EAAA,YAAY,CAAC,EAAS,IAAI,CAAE,CACrC,OAAQ,SADC,GAET,CACF,EAEF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,WAC4B,EACjC,CAAE,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe,IACpB,OAAO,IAAI,EAAA,YAAY,CAAC,KAAM,CAC5B,OAAQ,IACR,OAFS,CAEA,CACP,8BAA+B,IAC/B,+BAAgC,eAChC,+BAAgC,cAClC,CACF,EACF,iKCrFA,IAAA,EAGO,EAAA,CAFLA,AAEK,CAAA,QACP,EAA0B,EAAyB,CAA1CC,AAA0C,CAAA,EAAA,EAH9B,GAEwC,CAC3C,AAClB,EAA0C,EAFnC,AAEmC,CAAjCC,AAAiC,CAAA,EADhB,EAC8C,GAExE,EAAwC,EAAA,CAAA,CAAA,AAFjBC,EAEXC,MAWZ,GAbkC,CAa5BC,EAAc,EAXM,EAWN,CAbsB,CAalBL,WAXgB,QAWhBA,CAAoB,CAC1CM,WAAY,CACVC,KAAMN,EAAAA,SAAAA,CAAUO,SAAS,CACzBC,KAAM,yBACNC,SAAU,mBACVC,SAAU,QACVC,WAAY,EACd,EACAC,iBAAkB,6CAClBC,iBAXF,CAA0B,WAYxBV,CACF,GAKM,kBAAEW,CAAgB,CAAEC,sBAAoB,aAAEC,CAAW,CAAE,CAAGZ,EAEhE,SAASH,IACP,MAAA,CAAA,EAAA,EAAOC,UAAAA,EAAY,kBACjBY,uBACAC,CACF,EACF", "ignoreList": [1]}