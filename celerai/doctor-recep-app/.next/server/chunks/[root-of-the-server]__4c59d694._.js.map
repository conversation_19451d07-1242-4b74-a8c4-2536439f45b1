{"version": 3, "sources": ["turbopack:///[project]/src/app/api/sentry-example-api/route.ts", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\n\nexport const dynamic = \"force-dynamic\";\nclass SentryExampleAPIError extends Error {\n  constructor(message: string | undefined) {\n    super(message);\n    this.name = \"SentryExampleAPIError\";\n  }\n}\n// A faulty API route to test Sentry's error monitoring\nexport function GET() {\n  throw new SentryExampleAPIError(\"This error is raised on the backend called by the example page.\");\n  return NextResponse.json({ data: \"Testing Sentry Error...\" });\n}\n", "import {\n  AppRouteRouteModule,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks"], "mappings": "0kCAAA,EAAA,CAAA,CAAA,QAEO,IAAM,EAAU,eACvB,OAAM,UAA8B,MAClC,YAAY,CAA2B,CAAE,CACvC,KAAK,CAAC,GACN,IAAI,CAAC,IAAI,CAAG,uBACd,CACF,CAEO,SAAS,IACd,MAAM,IAAI,EAAsB,kEAElC,kKCbA,IAAA,EAGO,EAAA,CAFLA,AAEK,CAAA,QACP,EAA0B,EAAyB,CAA1CC,AAA0C,CAAA,EAAA,EAH9B,GAEwC,CAC3C,AAClB,EAA0C,EAFnC,AAEmC,CAAjCC,AAAiC,CAAA,EADhB,EAC8C,GAExE,EAAwC,EAAA,CAAA,CAFjBC,AAEiB,EAA5BC,MAWZ,GAbkC,CAa5BC,EAAc,EAXM,EAWN,CAbsB,CAalBL,WAXgB,QAWhBA,CAAoB,CAC1CM,WAAY,CACVC,KAAMN,EAAAA,SAAAA,CAAUO,SAAS,CACzBC,KAAM,gCACNC,SAAU,0BACVC,SAAU,QACVC,WAAY,EACd,EACAC,iBAAkB,oDAClBC,iBAXF,CAA0B,WAYxBV,CACF,GAKM,kBAAEW,CAAgB,sBAAEC,CAAoB,aAAEC,CAAW,CAAE,CAAGZ,EAEhE,SAASH,IACP,MAAA,CAAA,EAAA,EAAOC,UAAAA,EAAY,kBACjBY,uBACAC,CACF,EACF", "ignoreList": [1]}