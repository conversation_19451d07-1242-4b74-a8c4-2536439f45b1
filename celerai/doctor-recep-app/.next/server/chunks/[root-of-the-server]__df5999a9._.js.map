{"version": 3, "sources": ["turbopack:///[project]/src/app/api/generate-summary-stream/route.ts", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Verify user session and check quota before proceeding\n    const session = await verifySession()\n    if (!session) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const supabase = await createClient()\n\n    // Check and update quota\n    const { data: quotaCheck } = await supabase\n      .rpc('check_and_update_quota', { doctor_uuid: session.userId })\n\n    if (!quotaCheck) {\n      return NextResponse.json(\n        { error: 'Quota exceeded. You have reached your monthly AI generation limit. Please contact admin or wait for next month.' },\n        { status: 429 }\n      )\n    }\n\n    const body = await request.json()\n\n    console.log('🔄 Next.js API Route - Received request body:', {\n      primary_audio_url: body.primary_audio_url ? '✅ Present' : '❌ Missing',\n      additional_audio_urls: body.additional_audio_urls?.length || 0,\n      image_urls: body.image_urls?.length || 0,\n      submitted_by: body.submitted_by,\n      consultation_type: body.consultation_type,\n      patient_name: body.patient_name\n    })\n\n    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005'\n    console.log('🔄 Next.js API Route - Forwarding to:', `${apiUrl}/api/generate-summary-stream`)\n\n    // Forward the request to the Python backend\n    const response = await fetch(`${apiUrl}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(body),\n    })\n\n    console.log('🔄 Next.js API Route - Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('🔄 Next.js API Route - Backend error:', errorText)\n      return NextResponse.json(\n        { error: 'Failed to connect to backend service', details: errorText },\n        { status: response.status }\n      )\n    }\n\n    // Create a readable stream for the response\n    const readable = new ReadableStream({\n      start(controller) {\n        const reader = response.body?.getReader()\n        if (!reader) {\n          controller.close()\n          return\n        }\n\n        function pump(): Promise<void> {\n          return reader!.read().then(({ done, value }) => {\n            if (done) {\n              controller.close()\n              return\n            }\n            controller.enqueue(value)\n            return pump()\n          })\n        }\n\n        return pump()\n      },\n    })\n\n    return new Response(readable, {\n      headers: {\n        'Content-Type': 'text/plain',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'POST',\n        'Access-Control-Allow-Headers': 'Content-Type',\n      },\n    })\n  } catch (error) {\n    console.error('Streaming API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}", "import {\n  AppRouteRouteModule,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks"], "mappings": "6tDAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,IAAM,EAAU,MAAM,GAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,OADY,AACL,EAAA,KAFa,OAED,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,EACmB,EACxB,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAW,MAAM,GAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAU,CAAE,CAAG,MAAM,EAChC,GAAG,CAAC,AAJgB,yBAIU,CAAE,YAAa,EAAQ,MAAM,AAAC,GAE/D,GAAI,CAAC,EACH,OAAO,EAAA,CADQ,WACI,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,qGACsH,EAC3H,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAO,MAAM,EAAQ,IAAI,GAE/B,QAAQ,GAAG,CAAC,gDAAiD,CAC3D,kBAAmB,EAAK,iBAAiB,CAAG,YAAc,YAC1D,sBAAuB,EAAK,qBAAqB,EAAE,QAAU,EAC7D,WAAY,EAAK,UAAU,EAAE,QAAU,EACvC,aAAc,EAAK,YAAY,CAC/B,kBAAmB,EAAK,iBAAiB,CACzC,aAAc,EAAK,YAAY,AACjC,GAEA,IAAM,EAAS,OAAA,iBACf,IADkD,IAC1C,GAAG,CAAC,wCAAyC,CAAA,EAAG,EAAO,4BAA4B,CAAC,EAG5F,IAAM,EAAW,MAAM,MAAM,CAAA,EAAG,EAAO,4BAA4B,CAAC,CAAE,CACpE,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,EACvB,GAIA,GAFA,QAAQ,GAAG,CAAC,kDAAmD,EAAS,MAAM,EAE1E,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAY,MAAM,EAAS,IAAI,GAErC,OADA,QAAQ,KAAK,CAAC,wCAAyC,GAChD,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,2BAC4C,QAAS,CAAU,EACpE,CAAE,OAAQ,EAAS,MAAM,AAAC,EAE9B,CAGA,IAAM,EAAW,IAAI,eAAe,CAClC,MAAM,CAAU,EACd,IAAM,EAAS,EAAS,IAAI,EAAE,mBAC9B,AAAK,EAKL,AAWO,EAhBH,IAAS,GAKJ,IACP,OAAO,EAAQ,IAAI,GAAG,IAAI,CAAC,CAAC,MAAE,CAAI,OAAE,CAAK,CAAE,GACzC,AAAI,MAAM,CACR,EAAW,KAAK,IAGlB,EAAW,OAAO,CAAC,GACZ,KAEX,SAbE,EAAW,KAAK,EAgBpB,CACF,GAEA,OAAO,IAAI,SAAS,EAAU,CAC5B,QAAS,CACP,eAAgB,aAChB,gBAAiB,WACjB,WAAc,aACd,8BAA+B,IAC/B,+BAAgC,OAChC,+BAAgC,cAClC,CACF,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,uBAAwB,GAC/B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,YADJ,WAC4B,EACjC,CAAE,OAAQ,GAAI,EAElB,CACF,iKCvGA,IAAA,EAGO,EAAA,CAAA,AAFLA,CAEK,QACP,EAA0B,EAAyB,CAA1CC,AAA0C,CAAA,EAAA,EAH9B,GAEwC,CAC3C,AAClB,EAA0C,EAAA,AAFnC,CAEEC,AAAiC,CAAA,EADhB,EAC8C,GAExE,EAAwC,EAAA,CAAA,CAFjBC,AAEiB,EAA5BC,MAWZ,GAbkC,CAa5BC,EAAc,EAXM,EAWN,CAbsB,CAalBL,WAXgB,QAWhBA,CAAoB,CAC1CM,WAAY,CACVC,KAAMN,EAAAA,SAAAA,CAAUO,SAAS,CACzBC,KAAM,qCACNC,SAAU,+BACVC,SAAU,QACVC,WAAY,EACd,EACAC,iBAAkB,yDAClBC,iBAXF,CAA0B,WAYxBV,CACF,GAKM,kBAAEW,CAAgB,sBAAEC,CAAoB,aAAEC,CAAW,CAAE,CAAGZ,EAEhE,SAASH,IACP,MAAA,CAAA,EAAA,EAAOC,UAAAA,EAAY,kBACjBY,uBACAC,CACF,EACF", "ignoreList": [1]}