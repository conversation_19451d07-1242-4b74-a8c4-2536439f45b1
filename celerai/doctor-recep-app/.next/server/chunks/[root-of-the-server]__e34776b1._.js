module.exports={929549:function(e){var{g:r,__dirname:t,m:o,e:s}=e;o.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},983943:function(e){var{g:r,__dirname:t,m:o,e:s}=e;o.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},86103:function(e){var{g:r,__dirname:t,m:o,e:s}=e;o.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},945935:function(e){var{g:r,__dirname:t,m:o,e:s}=e;o.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},348629:function(e){var{g:r,__dirname:t,m:o,e:s}=e;o.exports=e.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},439215:function(e){var{g:r,__dirname:t,m:o,e:s}=e},924194:e=>{"use strict";var{g:r,__dirname:t}=e;e.s({GET:()=>s,OPTIONS:()=>n});var o=e.i(125427);async function s(e){try{let{searchParams:r}=new URL(e.url),t=r.get("url");if(!t)return o.NextResponse.json({error:"Missing audio URL parameter"},{status:400});let s=["celerai.tallyup.pro",process.env.R2_PUBLIC_URL?.replace("https://","")].filter(Boolean),n=new URL(t);if(!s.some(e=>n.hostname===e))return o.NextResponse.json({error:"Unauthorized audio source"},{status:403});let a=await fetch(t,{headers:{"User-Agent":"CelerAI-AudioProxy/1.0"}});if(!a.ok)return console.error(`Failed to fetch audio: ${a.status} ${a.statusText}`),o.NextResponse.json({error:"Failed to fetch audio file"},{status:a.status});let i=a.headers.get("Content-Type")||"audio/webm",u=a.headers.get("Content-Length"),p=new Headers({"Content-Type":i,"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET","Access-Control-Allow-Headers":"Content-Type","Cache-Control":"public, max-age=3600"});return u&&p.set("Content-Length",u),new o.NextResponse(a.body,{status:200,headers:p})}catch(e){return console.error("Audio proxy error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function n(){return new o.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}},494990:e=>{"use strict";var{g:r,__dirname:t}=e;{e.s({patchFetch:()=>i,routeModule:()=>r,serverHooks:()=>p,workAsyncStorage:()=>t,workUnitAsyncStorage:()=>u});var o=e.i(854885),s=e.i(814689),n=e.i(25402),a=e.i(924194);let r=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/audio-proxy/route",pathname:"/api/audio-proxy",filename:"route",bundlePath:""},resolvedPagePath:"[project]/src/app/api/audio-proxy/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:t,workUnitAsyncStorage:u,serverHooks:p}=r;function i(){return(0,n.patchFetch)({workAsyncStorage:t,workUnitAsyncStorage:u})}}}};

//# sourceMappingURL=%5Broot-of-the-server%5D__e34776b1._.js.map