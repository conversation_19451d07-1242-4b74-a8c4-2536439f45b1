module.exports={470286:function(e){var{g:t,__dirname:r,m:s,e:i}=e},281376:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return p},RequestCookiesAdapter:function(){return g},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return u},getModifiedCookieValues:function(){return o},responseCookiesToRequestCookies:function(){return d},wrapWithMutableAccessCheck:function(){return c}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let t=e.r(196596),r=e.r(179719),s=e.r(86103),f=e.r(983943);class p extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new p}}class g{static seal(e){return new Proxy(e,{get(e,t,s){switch(t){case"clear":case"delete":case"set":return p.callable;default:return r.ReflectAdapter.get(e,t,s)}}})}}let y=Symbol.for("next.mutated.cookies");function o(e){let t=e[y];return t&&Array.isArray(t)&&0!==t.length?t:[]}function l(e,r){let s=o(r);if(0===s.length)return!1;let i=new t.ResponseCookies(e),n=i.getAll();for(let e of s)i.set(e);for(let e of n)i.set(e);return!0}class m{static wrap(e,i){let n=new t.ResponseCookies(new Headers);for(let t of e.getAll())n.set(t);let a=[],o=new Set,l=()=>{let e=s.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=n.getAll().filter(e=>o.has(e.name)),i){let e=[];for(let r of a){let s=new t.ResponseCookies(new Headers);s.set(r),e.push(s.toString())}i(e)}},c=new Proxy(n,{get(e,t,s){switch(t){case y:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return r.ReflectAdapter.get(e,t,s)}}});return c}}function c(e){let t=new Proxy(e,{get(e,s,i){switch(s){case"delete":return function(...r){return h("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return h("cookies().set"),e.set(...r),t};default:return r.ReflectAdapter.get(e,s,i)}}});return t}function u(e){return"action"===e.phase}function h(e){if(!u((0,f.getExpectedRequestStore)(e)))throw new p}function d(e){let r=new t.RequestCookies(new Headers);for(let t of e.getAll())r.set(t);return r}}},993948:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return a}});let t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(e.r(535540));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let r={current:null},s="function"==typeof t.cache?t.cache:e=>e,o=console.warn;function a(e){return function(...t){o(e(...t))}}s(e=>{try{o(r.current)}finally{r.current=null}})}},546372:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"cookies",{enumerable:!0,get:function(){return n}});let t=e.r(281376),r=e.r(196596),s=e.r(86103),h=e.r(983943),d=e.r(950521),f=e.r(889157),p=e.r(615714),g=e.r(993948);e.r(952004);let y=e.r(492144);function n(){let e="cookies",i=s.workAsyncStorage.getStore(),n=h.workUnitAsyncStorage.getStore();if(i){if(n&&"after"===n.phase&&!(0,y.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${i.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(i.forceStatic)return a(t.RequestCookiesAdapter.seal(new r.RequestCookies(new Headers({}))));if(n){if("cache"===n.type)throw Object.defineProperty(Error(`Route ${i.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===n.type)throw Object.defineProperty(Error(`Route ${i.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(i.dynamicShouldError)throw Object.defineProperty(new f.StaticGenBailoutError(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(n)if("prerender"===n.type){var c=i.route,u=n;let e=m.get(u);if(e)return e;let t=(0,p.makeHangingPromise)(u.renderSignal,"`cookies()`");return m.set(u,t),Object.defineProperties(t,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},size:{get(){let e="`cookies().size`",t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${o(arguments[0])})\``;let t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${o(arguments[0])})\``;let t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${o(arguments[0])})\``;let t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${o(t)}, ...)\``:"`cookies().set(...)`"}let t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${o(arguments[0])})\``:`\`cookies().delete(${o(arguments[0])}, ...)\``;let t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},clear:{value:function(){let e="`cookies().clear()`",t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}},toString:{value:function(){let e="`cookies().toString()`",t=l(c,e);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(c,e,t,u)}}}),t}else"prerender-ppr"===n.type?(0,d.postponeWithTracking)(i.route,e,n.dynamicTracking):"prerender-legacy"===n.type&&(0,d.throwToInterruptStaticGeneration)(e,i,n);(0,d.trackDynamicDataInDynamicRender)(i,n)}let g=(0,h.getExpectedRequestStore)(e);return a((0,t.areCookiesMutableInCurrentPhase)(g)?g.userspaceMutableCookies:g.cookies)}let m=new WeakMap;function a(e){let t=m.get(e);if(t)return t;let r=Promise.resolve(e);return m.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):c.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):u.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function o(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}function l(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function c(){return this.getAll().map(e=>[e.name,e]).values()}function u(e){for(let e of this.getAll())this.delete(e.name);return e}(0,g.createDedupedByCallsiteServerErrorLoggerDev)(l)}},242068:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={HeadersAdapter:function(){return s},ReadonlyHeadersError:function(){return r}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let t=e.r(179719);class r extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new r}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(r,s,i){if("symbol"==typeof s)return t.ReflectAdapter.get(r,s,i);let n=s.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==a)return t.ReflectAdapter.get(r,a,i)},set(r,s,i,n){if("symbol"==typeof s)return t.ReflectAdapter.set(r,s,i,n);let a=s.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return t.ReflectAdapter.set(r,o??s,i,n)},has(r,s){if("symbol"==typeof s)return t.ReflectAdapter.has(r,s);let i=s.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==n&&t.ReflectAdapter.has(r,n)},deleteProperty(r,s){if("symbol"==typeof s)return t.ReflectAdapter.deleteProperty(r,s);let i=s.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===n||t.ReflectAdapter.deleteProperty(r,n)}})}static seal(e){return new Proxy(e,{get(e,s,i){switch(s){case"append":case"delete":case"set":return r.callable;default:return t.ReflectAdapter.get(e,s,i)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}}},700099:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"headers",{enumerable:!0,get:function(){return n}});let t=e.r(242068),r=e.r(86103),s=e.r(983943),c=e.r(950521),u=e.r(889157),h=e.r(615714),d=e.r(993948);e.r(952004);let f=e.r(492144);function n(){let e=r.workAsyncStorage.getStore(),i=s.workUnitAsyncStorage.getStore();if(e){if(i&&"after"===i.phase&&!(0,f.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return a(t.HeadersAdapter.seal(new Headers({})));if(i){if("cache"===i.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===i.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new u.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(i)if("prerender"===i.type){var n=e.route,d=i;let t=p.get(d);if(t)return t;let r=(0,h.makeHangingPromise)(d.renderSignal,"`headers()`");return p.set(d,r),Object.defineProperties(r,{append:{value:function(){let e=`\`headers().append(${o(arguments[0])}, ...)\``,t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},delete:{value:function(){let e=`\`headers().delete(${o(arguments[0])})\``,t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},get:{value:function(){let e=`\`headers().get(${o(arguments[0])})\``,t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},has:{value:function(){let e=`\`headers().has(${o(arguments[0])})\``,t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},set:{value:function(){let e=`\`headers().set(${o(arguments[0])}, ...)\``,t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},keys:{value:function(){let e="`headers().keys()`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},values:{value:function(){let e="`headers().values()`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},entries:{value:function(){let e="`headers().entries()`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=l(n,e);(0,c.abortAndThrowOnSynchronousRequestDataAccess)(n,e,t,d)}}}),r}else"prerender-ppr"===i.type?(0,c.postponeWithTracking)(e.route,"headers",i.dynamicTracking):"prerender-legacy"===i.type&&(0,c.throwToInterruptStaticGeneration)("headers",e,i);(0,c.trackDynamicDataInDynamicRender)(e,i)}return a((0,s.getExpectedRequestStore)("headers").headers)}let p=new WeakMap;function a(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function o(e){return"string"==typeof e?`'${e}'`:"..."}function l(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}(0,d.createDedupedByCallsiteServerErrorLoggerDev)(l)}},410511:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"draftMode",{enumerable:!0,get:function(){return n}});let t=e.r(983943),r=e.r(86103),s=e.r(950521),c=e.r(993948),u=e.r(889157),h=e.r(603172);function n(){let e=r.workAsyncStorage.getStore(),s=t.workUnitAsyncStorage.getStore();switch((!e||!s)&&(0,t.throwForMissingRequestStore)("draftMode"),s.type){case"request":return a(s.draftMode,e);case"cache":case"unstable-cache":let i=(0,t.getDraftModeProviderForCacheScope)(e,s);if(i)return a(i,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return o(null);default:return s}}function a(e,t){let r,s=d.get(n);return s||(r=o(e),d.set(e,r),r)}let d=new WeakMap;function o(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class f{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){l("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){l("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}function l(e){let i=r.workAsyncStorage.getStore(),n=t.workUnitAsyncStorage.getStore();if(i){if(n){if("cache"===n.type)throw Object.defineProperty(Error(`Route ${i.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===n.type)throw Object.defineProperty(Error(`Route ${i.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===n.phase)throw Object.defineProperty(Error(`Route ${i.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(i.dynamicShouldError)throw Object.defineProperty(new u.StaticGenBailoutError(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(n){if("prerender"===n.type){let t=Object.defineProperty(Error(`Route ${i.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,s.abortAndThrowOnSynchronousRequestDataAccess)(i.route,e,t,n)}else if("prerender-ppr"===n.type)(0,s.postponeWithTracking)(i.route,e,n.dynamicTracking);else if("prerender-legacy"===n.type){n.revalidate=0;let t=Object.defineProperty(new h.DynamicServerError(`Route ${i.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw i.dynamicUsageDescription=e,i.dynamicUsageStack=t.stack,t}}}}(0,c.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})}},998322:function(e){var{g:t,__dirname:r,m:s,e:i}=e;s.exports.cookies=e.r(546372).cookies,s.exports.headers=e.r(700099).headers,s.exports.draftMode=e.r(410511).draftMode},131267:function(e){var t,{g:r,__dirname:s,m:i,e:n}=e;"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"RedirectStatusCode",{enumerable:!0,get:function(){return a}});var a=((t={})[t.SeeOther=303]="SeeOther",t[t.TemporaryRedirect=307]="TemporaryRedirect",t[t.PermanentRedirect=308]="PermanentRedirect",t);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),i.exports=n.default)},36774:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n,a={REDIRECT_ERROR_CODE:function(){return r},RedirectType:function(){return l},isRedirectError:function(){return c}};for(var o in a)Object.defineProperty(i,o,{enumerable:!0,get:a[o]});let t=e.r(131267),r="NEXT_REDIRECT";var l=((n={}).push="push",n.replace="replace",n);function c(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let s=e.digest.split(";"),[i,n]=s,a=s.slice(2,-2).join(";"),o=Number(s.at(-2));return i===r&&("replace"===n||"push"===n)&&"string"==typeof a&&!isNaN(o)&&o in t.RedirectStatusCode}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},644749:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return l}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let t=e.r(131267),r=e.r(36774),f=e.r(174538).actionAsyncStorage;function o(e,s,i){void 0===i&&(i=t.RedirectStatusCode.TemporaryRedirect);let n=Object.defineProperty(Error(r.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=r.REDIRECT_ERROR_CODE+";"+s+";"+e+";"+i+";",n}function l(e,s){var i;throw null!=s||(s=(null==f||null==(i=f.getStore())?void 0:i.isAction)?r.RedirectType.push:r.RedirectType.replace),o(e,s,t.RedirectStatusCode.TemporaryRedirect)}function c(e,s){throw void 0===s&&(s=r.RedirectType.replace),o(e,s,t.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,r.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function h(e){if(!(0,r.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,r.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},860949:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={HTTPAccessErrorStatus:function(){return e},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return r},getAccessFallbackErrorTypeByStatus:function(){return c},getAccessFallbackHTTPStatus:function(){return l},isHTTPAccessFallbackError:function(){return o}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let e={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},t=new Set(Object.values(e)),r="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[s,i]=e.digest.split(";");return s===r&&t.has(Number(i))}function l(e){return Number(e.digest.split(";")[1])}function c(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},351307:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"notFound",{enumerable:!0,get:function(){return n}});let t=""+e.r(860949).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=t,e}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},626474:function(e){var{g:t,__dirname:r,m:s,e:i}=e;"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"forbidden",{enumerable:!0,get:function(){return n}}),e.r(860949).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)},899298:function(e){var{g:t,__dirname:r,m:s,e:i}=e;"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"unauthorized",{enumerable:!0,get:function(){return n}}),e.r(860949).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)},628403:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isPostpone",{enumerable:!0,get:function(){return n}});let e=Symbol.for("react.postpone");function n(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}}},141312:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={BailoutToCSRError:function(){return t},isBailoutToCSRError:function(){return o}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let e="BAILOUT_TO_CLIENT_SIDE_RENDERING";class t extends Error{constructor(t){super("Bail out to client-side rendering: "+t),this.reason=t,this.digest=e}}function o(t){return"object"==typeof t&&null!==t&&"digest"in t&&t.digest===e}}},326110:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isNextRouterError",{enumerable:!0,get:function(){return n}});let t=e.r(860949),r=e.r(36774);function n(e){return(0,r.isRedirectError)(e)||(0,t.isHTTPAccessFallbackError)(e)}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},654923:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"unstable_rethrow",{enumerable:!0,get:function(){return function e(s){if((0,a.isNextRouterError)(s)||(0,n.isBailoutToCSRError)(s)||(0,l.isDynamicServerError)(s)||(0,o.isDynamicPostpone)(s)||(0,r.isPostpone)(s)||(0,t.isHangingPromiseRejectionError)(s))throw s;s instanceof Error&&"cause"in s&&e(s.cause)}}});let t=e.r(615714),r=e.r(628403),n=e.r(141312),a=e.r(326110),o=e.r(950521),l=e.r(603172);("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},534931:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"unstable_rethrow",{enumerable:!0,get:function(){return t}});let t=e.r(654923).unstable_rethrow;("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},807285:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return r.RedirectType},forbidden:function(){return l.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return t.permanentRedirect},redirect:function(){return t.redirect},unauthorized:function(){return c.unauthorized},unstable_rethrow:function(){return u.unstable_rethrow}};for(var a in n)Object.defineProperty(i,a,{enumerable:!0,get:n[a]});let t=e.r(644749),r=e.r(36774),o=e.r(351307),l=e.r(626474),c=e.r(899298),u=e.r(534931);class h extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new h}delete(){throw new h}set(){throw new h}sort(){throw new h}}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),s.exports=i.default)}},310311:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(807285)},83581:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(807285),e.i(310311)},792930:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({concat:()=>s,decoder:()=>r,encoder:()=>t,uint32be:()=>a,uint64be:()=>n});let t=new TextEncoder,r=new TextDecoder;function s(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let s of e)t.set(s,r),r+=s.length;return t}function i(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function n(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return i(r,t,0),i(r,e%0x100000000,4),r}function a(e){let t=new Uint8Array(4);return i(t,e),t}}},981628:e=>{"use strict";var{g:t,__dirname:r}=e;function s(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))}function i(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}e.s({decodeBase64:()=>i,encodeBase64:()=>s})},836202:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({decode:()=>n,encode:()=>a});var s=e.i(792930),i=e.i(981628);function n(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:s.decoder.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=s.decoder.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return(0,i.decodeBase64)(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function a(e){let t=e;return("string"==typeof t&&(t=s.encoder.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(0,i.encodeBase64)(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}},237626:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({JOSEAlgNotAllowed:()=>i,JOSEError:()=>t,JOSENotSupported:()=>n,JWEDecryptionFailed:()=>a,JWEInvalid:()=>o,JWKInvalid:()=>u,JWKSInvalid:()=>h,JWKSMultipleMatchingKeys:()=>f,JWKSNoMatchingKey:()=>d,JWKSTimeout:()=>p,JWSInvalid:()=>l,JWSSignatureVerificationFailed:()=>g,JWTClaimValidationFailed:()=>r,JWTExpired:()=>s,JWTInvalid:()=>c});class t extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class r extends t{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",s="unspecified"){super(e,{cause:{claim:r,reason:s,payload:t}}),this.claim=r,this.reason=s,this.payload=t}}class s extends t{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",s="unspecified"){super(e,{cause:{claim:r,reason:s,payload:t}}),this.claim=r,this.reason=s,this.payload=t}}class i extends t{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class n extends t{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class a extends t{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class o extends t{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class l extends t{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class c extends t{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class u extends t{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class h extends t{static code="ERR_JWKS_INVALID";code="ERR_JWKS_INVALID"}class d extends t{static code="ERR_JWKS_NO_MATCHING_KEY";code="ERR_JWKS_NO_MATCHING_KEY";constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t)}}class f extends t{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class p extends t{static code="ERR_JWKS_TIMEOUT";code="ERR_JWKS_TIMEOUT";constructor(e="request timed out",t){super(e,t)}}class g extends t{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}}},401231:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(237626);let t=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new s.JOSENotSupported(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}}},463262:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}}}},159271:e=>{"use strict";var{g:t,__dirname:r}=e;function s(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function i(e,t){return e.name===t}function n(e){return parseInt(e.name.slice(4),10)}function a(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)}function o(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!i(e.algorithm,"HMAC"))throw s("HMAC");let r=parseInt(t.slice(2),10);if(n(e.algorithm.hash)!==r)throw s(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!i(e.algorithm,"RSASSA-PKCS1-v1_5"))throw s("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(n(e.algorithm.hash)!==r)throw s(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!i(e.algorithm,"RSA-PSS"))throw s("RSA-PSS");let r=parseInt(t.slice(2),10);if(n(e.algorithm.hash)!==r)throw s(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!i(e.algorithm,"Ed25519"))throw s("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!i(e.algorithm,"ECDSA"))throw s("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw s(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}a(e,r)}function l(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!i(e.algorithm,"AES-GCM"))throw s("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw s(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!i(e.algorithm,"AES-KW"))throw s("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw s(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw s("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!i(e.algorithm,"PBKDF2"))throw s("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!i(e.algorithm,"RSA-OAEP"))throw s("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(n(e.algorithm.hash)!==r)throw s(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}a(e,r)}e.s({checkEncCryptoKey:()=>l,checkSigCryptoKey:()=>o})},524776:e=>{"use strict";var{g:t,__dirname:r}=e;{function s(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}e.s({default:()=>t,withAlg:()=>i});let t=(e,...t)=>s("Key must be ",e,...t);function i(e,t,...r){return s(`Key for the ${e} algorithm must be `,t,...r)}}},297754:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(159271),i=e.i(524776);let t=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError((0,i.default)(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return(0,s.checkSigCryptoKey)(t,e,r),t}}},370:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(401231),i=e.i(463262),n=e.i(297754);let t=async(e,t,r)=>{let a=await (0,n.default)(e,t,"sign");return(0,i.default)(e,a),new Uint8Array(await crypto.subtle.sign((0,s.default)(e,a.algorithm),a,r))}}},564779:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0}}},925621:e=>{"use strict";var{g:t,__dirname:r}=e;{function s(e){if(!i(e))throw Error("CryptoKey instance expected")}function i(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function n(e){return e?.[Symbol.toStringTag]==="KeyObject"}e.s({assertCryptoKey:()=>s,default:()=>t,isCryptoKey:()=>i,isKeyObject:()=>n});let t=e=>i(e)||n(e)}},66372:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}}},906967:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({isJWK:()=>i,isPrivateJWK:()=>n,isPublicJWK:()=>a,isSecretJWK:()=>o});var s=e.i(66372);function i(e){return(0,s.default)(e)&&"string"==typeof e.kty}function n(e){return"oct"!==e.kty&&"string"==typeof e.d}function a(e){return"oct"!==e.kty&&void 0===e.d}function o(e){return"oct"===e.kty&&"string"==typeof e.k}},524471:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>l});var s=e.i(524776),i=e.i(925621),n=e.i(906967);let t=e=>e?.[Symbol.toStringTag],r=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let s;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):s=r;break;case e.startsWith("PBES2"):s="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):s=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):s="wrapKey";break;case"decrypt"===r:s=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(s&&t.key_ops?.includes?.(s)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${s}" when present`)}return!0},a=(e,a,o)=>{if(!(a instanceof Uint8Array)){if((0,n.isJWK)(a)){if((0,n.isSecretJWK)(a)&&r(e,a,o))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!(0,i.default)(a))throw TypeError((0,s.withAlg)(e,a,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==a.type)throw TypeError(`${t(a)} instances for symmetric algorithms must be of type "secret"`)}},o=(e,a,o)=>{if((0,n.isJWK)(a))switch(o){case"decrypt":case"sign":if((0,n.isPrivateJWK)(a)&&r(e,a,o))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if((0,n.isPublicJWK)(a)&&r(e,a,o))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!(0,i.default)(a))throw TypeError((0,s.withAlg)(e,a,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===a.type)throw TypeError(`${t(a)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===a.type)switch(o){case"sign":throw TypeError(`${t(a)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${t(a)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===a.type)switch(o){case"verify":throw TypeError(`${t(a)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${t(a)} instances for asymmetric algorithm encryption must be of type "public"`)}},l=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?a(e,t,r):o(e,t,r)}}},918201:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(237626);let t=(e,t,r,i,n)=>{let a;if(void 0!==n.crit&&i?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!i||void 0===i.crit)return new Set;if(!Array.isArray(i.crit)||0===i.crit.length||i.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,i.crit)){if(!a.has(o))throw new s.JOSENotSupported(`Extension Header Parameter "${o}" is not recognized`);if(void 0===n[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===i[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(i.crit)}}},90838:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(237626);let t=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new s.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new s.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new s.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new s.JOSENotSupported('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),i={...e};return delete i.alg,delete i.use,crypto.subtle.importKey("jwk",i,t,e.ext??!e.d,e.key_ops??r)}}},14723:e=>{"use strict";var{g:t,__dirname:r}=e;{let t;e.s({default:()=>l});var s=e.i(906967),i=e.i(836202),n=e.i(90838),a=e.i(925621);let r=async(e,r,s,i=!1)=>{let a=(t||=new WeakMap).get(e);if(a?.[s])return a[s];let o=await (0,n.default)({...r,alg:s});return i&&Object.freeze(e),a?a[s]=o:t.set(e,{[s]:o}),o},o=(e,r)=>{let s,i=(t||=new WeakMap).get(e);if(i?.[r])return i[r];let n="public"===e.type,a=!!n;if("x25519"===e.asymmetricKeyType){switch(r){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}s=e.toCryptoKey(e.asymmetricKeyType,a,n?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==r&&"Ed25519"!==r)throw TypeError("given KeyObject instance cannot be used for this algorithm");s=e.toCryptoKey(e.asymmetricKeyType,a,[n?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let t;switch(r){case"RSA-OAEP":t="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":t="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":t="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":t="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(r.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:t},a,n?["encrypt"]:["decrypt"]);s=e.toCryptoKey({name:r.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:t},a,[n?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let t=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!t)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===r&&"P-256"===t&&(s=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[n?"verify":"sign"])),"ES384"===r&&"P-384"===t&&(s=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[n?"verify":"sign"])),"ES512"===r&&"P-521"===t&&(s=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[n?"verify":"sign"])),r.startsWith("ECDH-ES")&&(s=e.toCryptoKey({name:"ECDH",namedCurve:t},a,n?[]:["deriveBits"]))}if(!s)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[r]=s:t.set(e,{[r]:s}),s},l=async(e,t)=>{if(e instanceof Uint8Array||(0,a.isCryptoKey)(e))return e;if((0,a.isKeyObject)(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return o(e,t)}catch(e){if(e instanceof TypeError)throw e}let s=e.export({format:"jwk"});return r(e,s,t)}if((0,s.isJWK)(e))return e.k?(0,i.decode)(e.k):r(e,e,t,!0);throw Error("unreachable")}}},588342:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({FlattenedSign:()=>t});var s=e.i(836202),i=e.i(370),n=e.i(564779),a=e.i(237626),o=e.i(792930),l=e.i(524471),c=e.i(918201),u=e.i(14723);class t{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new a.JWSInvalid("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,n.default)(this.#t,this.#r))throw new a.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let h={...this.#t,...this.#r},d=(0,c.default)(a.JWSInvalid,new Map([["b64",!0]]),t?.crit,this.#t,h),f=!0;if(d.has("b64")&&"boolean"!=typeof(f=this.#t.b64))throw new a.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:p}=h;if("string"!=typeof p||!p)throw new a.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,l.default)(p,e,"sign");let g=this.#e;f&&(g=o.encoder.encode((0,s.encode)(g))),r=this.#t?o.encoder.encode((0,s.encode)(JSON.stringify(this.#t))):o.encoder.encode("");let y=(0,o.concat)(r,o.encoder.encode("."),g),m=await (0,u.default)(e,p),_=await (0,i.default)(p,m,y),v={signature:(0,s.encode)(_),payload:""};return f&&(v.payload=o.decoder.decode(g)),this.#r&&(v.header=this.#r),this.#t&&(v.protected=o.decoder.decode(r)),v}}}},480380:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({CompactSign:()=>t});var s=e.i(588342);class t{#s;constructor(e){this.#s=new s.FlattenedSign(e)}setProtectedHeader(e){return this.#s.setProtectedHeader(e),this}async sign(e,t){let r=await this.#s.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}}},826059:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=e=>Math.floor(e.getTime()/1e3)}},121226:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>r});let t=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,r=e=>{let r,s=t.exec(e);if(!s||s[4]&&s[1])throw TypeError("Invalid time period format");let i=parseFloat(s[2]);switch(s[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":r=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":r=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":r=Math.round(3600*i);break;case"day":case"days":case"d":r=Math.round(86400*i);break;case"week":case"weeks":case"w":r=Math.round(604800*i);break;default:r=Math.round(0x1e187e0*i)}return"-"===s[1]||"ago"===s[4]?-r:r}}},11288:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({JWTClaimsBuilder:()=>u,validateClaimsSet:()=>c});var s=e.i(237626),i=e.i(792930),n=e.i(826059),a=e.i(121226),o=e.i(66372);function l(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let t=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,r=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));function c(e,l,u={}){let h,d;try{h=JSON.parse(i.decoder.decode(l))}catch{}if(!(0,o.default)(h))throw new s.JWTInvalid("JWT Claims Set must be a top-level JSON object");let{typ:f}=u;if(f&&("string"!=typeof e.typ||t(e.typ)!==t(f)))throw new s.JWTClaimValidationFailed('unexpected "typ" JWT header value',h,"typ","check_failed");let{requiredClaims:p=[],issuer:g,subject:y,audience:m,maxTokenAge:_}=u,v=[...p];for(let e of(void 0!==_&&v.push("iat"),void 0!==m&&v.push("aud"),void 0!==y&&v.push("sub"),void 0!==g&&v.push("iss"),new Set(v.reverse())))if(!(e in h))throw new s.JWTClaimValidationFailed(`missing required "${e}" claim`,h,e,"missing");if(g&&!(Array.isArray(g)?g:[g]).includes(h.iss))throw new s.JWTClaimValidationFailed('unexpected "iss" claim value',h,"iss","check_failed");if(y&&h.sub!==y)throw new s.JWTClaimValidationFailed('unexpected "sub" claim value',h,"sub","check_failed");if(m&&!r(h.aud,"string"==typeof m?[m]:m))throw new s.JWTClaimValidationFailed('unexpected "aud" claim value',h,"aud","check_failed");switch(typeof u.clockTolerance){case"string":d=(0,a.default)(u.clockTolerance);break;case"number":d=u.clockTolerance;break;case"undefined":d=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:b}=u,w=(0,n.default)(b||new Date);if((void 0!==h.iat||_)&&"number"!=typeof h.iat)throw new s.JWTClaimValidationFailed('"iat" claim must be a number',h,"iat","invalid");if(void 0!==h.nbf){if("number"!=typeof h.nbf)throw new s.JWTClaimValidationFailed('"nbf" claim must be a number',h,"nbf","invalid");if(h.nbf>w+d)throw new s.JWTClaimValidationFailed('"nbf" claim timestamp check failed',h,"nbf","check_failed")}if(void 0!==h.exp){if("number"!=typeof h.exp)throw new s.JWTClaimValidationFailed('"exp" claim must be a number',h,"exp","invalid");if(h.exp<=w-d)throw new s.JWTExpired('"exp" claim timestamp check failed',h,"exp","check_failed")}if(_){let e=w-h.iat;if(e-d>("number"==typeof _?_:(0,a.default)(_)))throw new s.JWTExpired('"iat" claim timestamp check failed (too far in the past)',h,"iat","check_failed");if(e<0-d)throw new s.JWTClaimValidationFailed('"iat" claim timestamp check failed (it should be in the past)',h,"iat","check_failed")}return h}class u{#e;constructor(e){if(!(0,o.default)(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return i.encoder.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=l("setNotBefore",e):e instanceof Date?this.#e.nbf=l("setNotBefore",(0,n.default)(e)):this.#e.nbf=(0,n.default)(new Date)+(0,a.default)(e)}set exp(e){"number"==typeof e?this.#e.exp=l("setExpirationTime",e):e instanceof Date?this.#e.exp=l("setExpirationTime",(0,n.default)(e)):this.#e.exp=(0,n.default)(new Date)+(0,a.default)(e)}set iat(e){void 0===e?this.#e.iat=(0,n.default)(new Date):e instanceof Date?this.#e.iat=l("setIssuedAt",(0,n.default)(e)):"string"==typeof e?this.#e.iat=l("setIssuedAt",(0,n.default)(new Date)+(0,a.default)(e)):this.#e.iat=l("setIssuedAt",e)}}}},590421:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({SignJWT:()=>t});var s=e.i(480380),i=e.i(237626),n=e.i(11288);class t{#t;#i;constructor(e={}){this.#i=new n.JWTClaimsBuilder(e)}setIssuer(e){return this.#i.iss=e,this}setSubject(e){return this.#i.sub=e,this}setAudience(e){return this.#i.aud=e,this}setJti(e){return this.#i.jti=e,this}setNotBefore(e){return this.#i.nbf=e,this}setExpirationTime(e){return this.#i.exp=e,this}setIssuedAt(e){return this.#i.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new s.CompactSign(this.#i.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new i.JWTInvalid("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}}},361946:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(401231),i=e.i(463262),n=e.i(297754);let t=async(e,t,r,a)=>{let o=await (0,n.default)(e,t,"verify");(0,i.default)(e,o);let l=(0,s.default)(e,o.algorithm);try{return await crypto.subtle.verify(l,o,r,a)}catch{return!1}}}},229300:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)}}},359424:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({flattenedVerify:()=>f});var s=e.i(836202),i=e.i(361946),n=e.i(237626),a=e.i(792930),o=e.i(564779),l=e.i(66372),c=e.i(524471),u=e.i(918201),h=e.i(229300),d=e.i(14723);async function f(e,t,r){let f,p;if(!(0,l.default)(e))throw new n.JWSInvalid("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new n.JWSInvalid('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new n.JWSInvalid("JWS Protected Header incorrect type");if(void 0===e.payload)throw new n.JWSInvalid("JWS Payload missing");if("string"!=typeof e.signature)throw new n.JWSInvalid("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,l.default)(e.header))throw new n.JWSInvalid("JWS Unprotected Header incorrect type");let g={};if(e.protected)try{let t=(0,s.decode)(e.protected);g=JSON.parse(a.decoder.decode(t))}catch{throw new n.JWSInvalid("JWS Protected Header is invalid")}if(!(0,o.default)(g,e.header))throw new n.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let y={...g,...e.header},m=(0,u.default)(n.JWSInvalid,new Map([["b64",!0]]),r?.crit,g,y),_=!0;if(m.has("b64")&&"boolean"!=typeof(_=g.b64))throw new n.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:v}=y;if("string"!=typeof v||!v)throw new n.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');let b=r&&(0,h.default)("algorithms",r.algorithms);if(b&&!b.has(v))throw new n.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter value not allowed');if(_){if("string"!=typeof e.payload)throw new n.JWSInvalid("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new n.JWSInvalid("JWS Payload must be a string or an Uint8Array instance");let w=!1;"function"==typeof t&&(t=await t(g,e),w=!0),(0,c.default)(v,t,"verify");let E=(0,a.concat)(a.encoder.encode(e.protected??""),a.encoder.encode("."),"string"==typeof e.payload?a.encoder.encode(e.payload):e.payload);try{f=(0,s.decode)(e.signature)}catch{throw new n.JWSInvalid("Failed to base64url decode the signature")}let S=await (0,d.default)(t,v);if(!await (0,i.default)(v,S,f,E))throw new n.JWSSignatureVerificationFailed;if(_)try{p=(0,s.decode)(e.payload)}catch{throw new n.JWSInvalid("Failed to base64url decode the payload")}else p="string"==typeof e.payload?a.encoder.encode(e.payload):e.payload;let A={payload:p};return(void 0!==e.protected&&(A.protectedHeader=g),void 0!==e.header&&(A.unprotectedHeader=e.header),w)?{...A,key:S}:A}},751562:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({compactVerify:()=>a});var s=e.i(359424),i=e.i(237626),n=e.i(792930);async function a(e,t,r){if(e instanceof Uint8Array&&(e=n.decoder.decode(e)),"string"!=typeof e)throw new i.JWSInvalid("Compact JWS must be a string or Uint8Array");let{0:a,1:o,2:l,length:c}=e.split(".");if(3!==c)throw new i.JWSInvalid("Invalid Compact JWS");let u=await (0,s.flattenedVerify)({payload:o,protected:a,signature:l},t,r),h={payload:u.payload,protectedHeader:u.protectedHeader};return"function"==typeof t?{...h,key:u.key}:h}},674963:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({jwtVerify:()=>a});var s=e.i(751562),i=e.i(11288),n=e.i(237626);async function a(e,t,r){let a=await (0,s.compactVerify)(e,t,r);if(a.protectedHeader.crit?.includes("b64")&&!1===a.protectedHeader.b64)throw new n.JWTInvalid("JWTs MUST NOT use unencoded payload");let o={payload:(0,i.validateClaimsSet)(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...o,key:a.key}:o}},14156:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({createSession:()=>l,decrypt:()=>o,deleteSession:()=>h,encrypt:()=>a,refreshSession:()=>u,updateSession:()=>c}),e.i(470286);var s=e.i(590421),i=e.i(674963),n=e.i(998322);let t=process.env.SESSION_SECRET,r=new TextEncoder().encode(t);async function a(e){return new s.SignJWT(e).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(r)}async function o(e=""){try{if(!e)return null;let{payload:t}=await (0,i.jwtVerify)(e,r,{algorithms:["HS256"]});return t}catch{return console.log("Failed to verify session"),null}}async function l(e){let t=new Date(Date.now()+6048e5),r=await a({userId:e,expiresAt:t}),s=await (0,n.cookies)();console.log("DEBUG: Creating session for user:",e),console.log("DEBUG: Session expires at:",t),s.set("session",r,{httpOnly:!0,secure:!1,expires:t,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function c(){let e=await (0,n.cookies)(),t=e.get("session")?.value,r=await o(t);if(console.log("DEBUG: Updating session - session exists:",!!t),console.log("DEBUG: Updating session - payload valid:",!!r),!t||!r)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let s=new Date(Date.now()+6048e5);e.set("session",t,{httpOnly:!0,secure:!1,expires:s,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function u(e){console.log("DEBUG: Refreshing session for user:",e),await h(),await l(e),console.log("DEBUG: Session refresh completed")}async function h(){let e=await (0,n.cookies)();console.log("DEBUG: Deleting session cookie"),e.delete("session"),console.log("DEBUG: Session cookie deleted")}}},126268:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({resolveFetch:()=>t});let t=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>e.r(609329)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)}}},248263:e=>{"use strict";var t,{g:r,__dirname:s}=e;{e.s({FunctionRegion:()=>t,FunctionsError:()=>r,FunctionsFetchError:()=>s,FunctionsHttpError:()=>n,FunctionsRelayError:()=>i});class r extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class s extends r{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class i extends r{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class n extends r{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(t||(t={}))}},966735:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({FunctionsClient:()=>t});var s=e.i(126268),i=e.i(248263),n=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};class t{constructor(e,{headers:t={},customFetch:r,region:n=i.FunctionRegion.Any}={}){this.url=e,this.headers=t,this.region=n,this.fetch=(0,s.resolveFetch)(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return n(this,void 0,void 0,function*(){try{let s,n,{headers:a,method:o,body:l}=t,c={},{region:u}=t;u||(u=this.region),u&&"any"!==u&&(c["x-region"]=u),l&&(a&&!Object.prototype.hasOwnProperty.call(a,"Content-Type")||!a)&&("undefined"!=typeof Blob&&l instanceof Blob||l instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",s=l):"string"==typeof l?(c["Content-Type"]="text/plain",s=l):"undefined"!=typeof FormData&&l instanceof FormData?s=l:(c["Content-Type"]="application/json",s=JSON.stringify(l)));let h=yield this.fetch(`${this.url}/${e}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),a),body:s}).catch(e=>{throw new i.FunctionsFetchError(e)}),d=h.headers.get("x-relay-error");if(d&&"true"===d)throw new i.FunctionsRelayError(h);if(!h.ok)throw new i.FunctionsHttpError(h);let f=(null!=(r=h.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===f?yield h.json():"application/octet-stream"===f?yield h.blob():"text/event-stream"===f?h:"multipart/form-data"===f?yield h.formData():yield h.text(),error:null}}catch(e){return{data:null,error:e}}})}}}},280387:function(e){var{g:t,__dirname:r,m:s,e:i}=e;"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}},770649:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=n(e.r(455544)),r=n(e.r(280387));i.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,i;let n=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let r=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),i=null==(s=e.headers.get("content-range"))?void 0:s.split("/");r&&i&&i.length>1&&(o=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(n={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(a=[],n=null,l=200,c="OK")}catch(r){404===e.status&&""===t?(l=204,c="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null==(i=null==n?void 0:n.details)?void 0:i.includes("0 rows"))&&(n=null,l=200,c="OK"),n&&this.shouldThrowOnError)throw new r.default(n)}return{error:n,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,r,s;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(s=null==e?void 0:e.code)?s:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}}},819680:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=n(e.r(770649));class r extends t.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){let n=i?`${i}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){let i=void 0===s?"offset":`${s}.offset`,n=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}i.default=r}},584211:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=n(e.r(819680));class r extends t.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let i="";"plain"===s?i="pl":"phrase"===s?i="ph":"websearch"===s&&(i="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}i.default=r}},295450:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=n(e.r(584211));i.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:r=!1,count:s}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),s&&(this.headers.Prefer=`count=${s}`),new t.default({method:r?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:r,defaultToNull:s=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push(`count=${r}`),s||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:r,ignoreDuplicates:s=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==r&&this.url.searchParams.set("on_conflict",r),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:r}={}){let s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),r&&s.push(`count=${r}`),this.headers.Prefer=s.join(","),new t.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let r=[];return e&&r.push(`count=${e}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new t.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}}},601709:function(e){var{g:t,__dirname:r,m:s,e:i}=e;"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.version=void 0,i.version="0.0.0-automated"},920675:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.DEFAULT_HEADERS=void 0;let t=e.r(601709);i.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${t.version}`}}},226380:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=n(e.r(295450)),r=n(e.r(584211)),s=e.r(920675);class a{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let r=new URL(`${this.url}/${e}`);return new t.default(r,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new a(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:i=!1,count:n}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);s||i?(a=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return n&&(c.Prefer=`count=${n}`),new r.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}i.default=a}},112803:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0}),i.PostgrestError=i.PostgrestBuilder=i.PostgrestTransformBuilder=i.PostgrestFilterBuilder=i.PostgrestQueryBuilder=i.PostgrestClient=void 0;let t=n(e.r(226380));i.PostgrestClient=t.default;let r=n(e.r(295450));i.PostgrestQueryBuilder=r.default;let s=n(e.r(584211));i.PostgrestFilterBuilder=s.default;let a=n(e.r(819680));i.PostgrestTransformBuilder=a.default;let o=n(e.r(770649));i.PostgrestBuilder=o.default;let l=n(e.r(280387));i.PostgrestError=l.default,i.default={PostgrestClient:t.default,PostgrestQueryBuilder:r.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:o.default,PostgrestError:l.default}}},810742:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({PostgrestBuilder:()=>n,PostgrestClient:()=>t,PostgrestError:()=>a,PostgrestFilterBuilder:()=>s,PostgrestQueryBuilder:()=>r,PostgrestTransformBuilder:()=>i,default:()=>o});let{PostgrestClient:t,PostgrestQueryBuilder:r,PostgrestFilterBuilder:s,PostgrestTransformBuilder:i,PostgrestBuilder:n,PostgrestError:a}=e.i(112803).default,o={PostgrestClient:t,PostgrestQueryBuilder:r,PostgrestFilterBuilder:s,PostgrestTransformBuilder:i,PostgrestBuilder:n,PostgrestError:a}}},347235:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({version:()=>t});let t="2.11.2"}},725019:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({CHANNEL_EVENTS:()=>n,CHANNEL_STATES:()=>i,CONNECTION_STATE:()=>o,DEFAULT_HEADERS:()=>t,DEFAULT_TIMEOUT:()=>c,SOCKET_STATES:()=>s,TRANSPORTS:()=>a,VSN:()=>r,WS_CLOSE_NORMAL:()=>u});var s,i,n,a,o,l=e.i(347235);let t={"X-Client-Info":`realtime-js/${l.version}`},r="1.0.0",c=1e4,u=1e3;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(s||(s={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(i||(i={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(n||(n={})),(a||(a={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(o||(o={}))}},265291:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});class t{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let s=t.getUint8(1),i=t.getUint8(2),n=this.HEADER_LENGTH+2,a=r.decode(e.slice(n,n+s));n+=s;let o=r.decode(e.slice(n,n+i));return n+=i,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}}},639483:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});class t{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}}},914728:e=>{"use strict";var t,{g:r,__dirname:s}=e;{e.s({PostgresTypes:()=>t,convertCell:()=>i,convertChangeData:()=>r,convertColumn:()=>s,httpEndpointURL:()=>h,toArray:()=>c,toBoolean:()=>a,toJson:()=>l,toNumber:()=>o,toTimestampString:()=>u}),function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(t||(t={}));let r=(e,t,r={})=>{var i;let n=null!=(i=r.skipTypes)?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=s(i,e,t,n),r),{})},s=(e,t,r,s)=>{let a=t.find(t=>t.name===e),o=null==a?void 0:a.type,l=r[e];return o&&!s.includes(o)?i(o,l):n(l)},i=(e,r)=>{if("_"===e.charAt(0))return c(r,e.slice(1,e.length));switch(e){case t.bool:return a(r);case t.float4:case t.float8:case t.int2:case t.int4:case t.int8:case t.numeric:case t.oid:return o(r);case t.json:case t.jsonb:return l(r);case t.timestamp:return u(r);case t.abstime:case t.date:case t.daterange:case t.int4range:case t.int8range:case t.money:case t.reltime:case t.text:case t.time:case t.timestamptz:case t.timetz:case t.tsrange:case t.tstzrange:default:return n(r)}},n=e=>e,a=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},o=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},c=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s,n=e.slice(1,r);try{s=JSON.parse("["+n+"]")}catch(e){s=n?n.split(","):[]}return s.map(e=>i(t,e))}return e},u=e=>"string"==typeof e?e.replace(" ","T"):e,h=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")}}},783930:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(725019);class t{constructor(e,t,r={},i=s.DEFAULT_TIMEOUT){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}}},421246:e=>{"use strict";var t,{g:r,__dirname:s}=e;{e.s({REALTIME_PRESENCE_LISTEN_EVENTS:()=>t,default:()=>r}),function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(t||(t={}));class r{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{let{onJoin:t,onLeave:s,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=r.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=r.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],i()}),this.channel._on(s.diff,{},e=>{let{onJoin:t,onLeave:s,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=r.syncDiff(this.state,e,t,s),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){let i=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(i,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let r=i[e];if(r){let s=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),n=t.filter(e=>0>i.indexOf(e.presence_ref)),l=r.filter(e=>0>s.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(i,{joins:a,leaves:o},r,s)}static syncDiff(e,t,r,s){let{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(t,s)=>{var i;let n=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(s),n.length>0){let r=e[t].map(e=>e.presence_ref),s=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...s)}r(t,n,s)}),this.map(n,(t,r)=>{let i=e[t];if(!i)return;let n=r.map(e=>e.presence_ref);i=i.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=i,s(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let s=e[r];return"metas"in s?t[r]=s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}}},726128:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({REALTIME_CHANNEL_STATES:()=>t,REALTIME_LISTEN_TYPES:()=>i,REALTIME_POSTGRES_CHANGES_LISTEN_EVENT:()=>s,REALTIME_SUBSCRIBE_STATES:()=>n,default:()=>r});var s,i,n,a=e.i(725019),o=e.i(783930),l=e.i(639483),c=e.i(421246),u=e.i(914728);!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(s||(s={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(i||(i={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(n||(n={}));let t=a.CHANNEL_STATES;class r{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=a.CHANNEL_STATES.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new o.default(this,a.CHANNEL_EVENTS.join,this.params,this.timeout),this.rejoinTimer=new l.default(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=a.CHANNEL_STATES.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=a.CHANNEL_STATES.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=a.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=a.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this._on(a.CHANNEL_EVENTS.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new c.default(this),this.broadcastEndpointURL=(0,u.httpEndpointURL)(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:a,private:o}}=this.params;this._onError(t=>null==e?void 0:e(n.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(n.CLOSED));let l={},c={broadcast:i,presence:a,postgres_changes:null!=(s=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?s:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(n.SUBSCRIBED);return}{let s=this.bindings.postgres_changes,i=null!=(r=null==s?void 0:s.length)?r:0,a=[];for(let r=0;r<i;r++){let i=s[r],{filter:{event:o,schema:l,table:c,filter:u}}=i,h=t&&t[r];if(h&&h.event===o&&h.schema===l&&h.table===c&&h.filter===u)a.push(Object.assign(Object.assign({},i),{id:h.id}));else{this.unsubscribe(),null==e||e(n.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=a,e&&e(n.SUBSCRIBED);return}}).receive("error",t=>{null==e||e(n.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(n.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,i,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(i=null==(s=this.params)?void 0:s.config)?void 0:i.broadcast)?void 0:n.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:i,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(r=t.timeout)?r:this.timeout);return await (null==(s=e.body)?void 0:s.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=a.CHANNEL_STATES.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(a.CHANNEL_EVENTS.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{let s=new o.default(this,a.CHANNEL_EVENTS.leave,{},e);s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}async _fetchWithTimeout(e,t,r){let s=new AbortController,i=setTimeout(()=>s.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(i),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new o.default(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,i;let n=e.toLocaleLowerCase(),{close:o,error:l,leave:c,join:u}=a.CHANNEL_EVENTS;if(r&&[o,l,c,u].indexOf(n)>=0&&r!==this._joinRef())return;let h=this._onMessage(n,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null==(s=this.bindings.postgres_changes)||s.filter(e=>{var t,r,s;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(s=null==(r=e.filter)?void 0:r.event)?void 0:s.toLocaleLowerCase())===n}).map(e=>e.callback(h,r)):null==(i=this.bindings[n])||i.filter(e=>{var r,s,i,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null==(r=e.filter)?void 0:r.event;return n&&(null==(s=t.ids)?void 0:s.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let r=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof h&&"ids"in h){let e=h.data,{schema:t,table:r,commit_timestamp:s,type:i,errors:n}=e;h=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:s,eventType:i,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===a.CHANNEL_STATES.closed}_isJoined(){return this.state===a.CHANNEL_STATES.joined}_isJoining(){return this.state===a.CHANNEL_STATES.joining}_isLeaving(){return this.state===a.CHANNEL_STATES.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let s=e.toLocaleLowerCase(),i={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(e,t){let s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var i;return!((null==(i=e.type)?void 0:i.toLocaleLowerCase())===s&&r.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(a.CHANNEL_EVENTS.close,{},e)}_onError(e){this._on(a.CHANNEL_EVENTS.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=a.CHANNEL_STATES.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=(0,u.convertChangeData)(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=(0,u.convertChangeData)(e.columns,e.old_record)),t}}}},39268:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>c});var s=e.i(725019),i=e.i(265291),n=e.i(639483),a=e.i(914728),o=e.i(726128);let t=()=>{},r="undefined"!=typeof WebSocket,l=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class c{constructor(r,o){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=s.DEFAULT_HEADERS,this.params={},this.timeout=s.DEFAULT_TIMEOUT,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=t,this.conn=null,this.sendBuffer=[],this.serializer=new i.default,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>e.r(609329)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)},this.endPoint=`${r}/${s.TRANSPORTS.websocket}`,this.httpEndpoint=(0,a.httpEndpointURL)(r),(null==o?void 0:o.transport)?this.transport=o.transport:this.transport=null,(null==o?void 0:o.params)&&(this.params=o.params),(null==o?void 0:o.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),o.headers)),(null==o?void 0:o.timeout)&&(this.timeout=o.timeout),(null==o?void 0:o.logger)&&(this.logger=o.logger),(null==o?void 0:o.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=o.heartbeatIntervalMs);let c=null==(l=null==o?void 0:o.params)?void 0:l.apikey;c&&(this.accessTokenValue=c,this.apiKey=c),this.reconnectAfterMs=(null==o?void 0:o.reconnectAfterMs)?o.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==o?void 0:o.encode)?o.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==o?void 0:o.decode)?o.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new n.default(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==o?void 0:o.fetch),(null==o?void 0:o.worker)&&(this.worker=(null==o?void 0:o.worker)||!1,this.workerUrl=null==o?void 0:o.workerUrl),this.accessToken=(null==o?void 0:o.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(r){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new u(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),e.r(302749)(e.i).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:s.VSN}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case s.SOCKET_STATES.connecting:return s.CONNECTION_STATE.Connecting;case s.SOCKET_STATES.open:return s.CONNECTION_STATE.Open;case s.SOCKET_STATES.closing:return s.CONNECTION_STATE.Closing;default:return s.CONNECTION_STATE.Closed}}isConnected(){return this.connectionState()===s.CONNECTION_STATE.Open}channel(e,t={config:{}}){let r=new o.default(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){let{topic:t,event:r,payload:s,ref:i}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${i})`,s),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(e&&e.exp&&!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`);this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(s.CHANNEL_EVENTS.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null==(e=this.conn)||e.close(s.WS_CLOSE_NORMAL,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:i}=e;i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,s),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(s.CHANNEL_EVENTS.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",s=new URLSearchParams(t);return`${e}${r}${s}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([l],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class u{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=s.SOCKET_STATES.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}}},742961:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(39268),e.i(726128),e.i(421246)},622764:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(39268),e.i(726128),e.i(421246),e.i(742961)},505053:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({RealtimeClient:()=>s.default});var s=e.i(39268)},41910:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({StorageApiError:()=>r,StorageError:()=>t,StorageUnknownError:()=>i,isStorageError:()=>s});class t extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function s(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class r extends t{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class i extends t{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}}},129371:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({recursiveToCamel:()=>i,resolveFetch:()=>t,resolveResponse:()=>r});var s=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};let t=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>e.r(609329)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)},r=()=>s(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield e.r(609329)(e.i)).Response:Response}),i=e=>{if(Array.isArray(e))return e.map(e=>i(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=i(r)}),t}}},100306:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({get:()=>o,head:()=>u,post:()=>l,put:()=>c,remove:()=>h});var s=e.i(41910),i=e.i(129371),n=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};let t=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),r=(e,r,a)=>n(void 0,void 0,void 0,function*(){e instanceof(yield(0,i.resolveResponse)())&&!(null==a?void 0:a.noResolveJson)?e.json().then(i=>{r(new s.StorageApiError(t(i),e.status||500))}).catch(e=>{r(new s.StorageUnknownError(t(e),e))}):r(new s.StorageUnknownError(t(e),e))}),d=(e,t,r,s)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(i.body=JSON.stringify(s)),Object.assign(Object.assign({},i),r))};function a(e,t,s,i,a,o){return n(this,void 0,void 0,function*(){return new Promise((n,l)=>{e(s,d(t,i,a,o)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>n(e)).catch(e=>r(e,l,i))})})}function o(e,t,r,s){return n(this,void 0,void 0,function*(){return a(e,"GET",t,r,s)})}function l(e,t,r,s,i){return n(this,void 0,void 0,function*(){return a(e,"POST",t,s,i,r)})}function c(e,t,r,s,i){return n(this,void 0,void 0,function*(){return a(e,"PUT",t,s,i,r)})}function u(e,t,r,s){return n(this,void 0,void 0,function*(){return a(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}function h(e,t,r,s,i){return n(this,void 0,void 0,function*(){return a(e,"DELETE",t,s,i,r)})}}},904274:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>o});var s=e.i(41910),i=e.i(100306),n=e.i(129371),a=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};let t={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},r={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class o{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=(0,n.resolveFetch)(s)}uploadOrUpdate(e,t,i,n){return a(this,void 0,void 0,function*(){try{let s,a=Object.assign(Object.assign({},r),n),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(a.upsert)}),l=a.metadata;"undefined"!=typeof Blob&&i instanceof Blob?((s=new FormData).append("cacheControl",a.cacheControl),l&&s.append("metadata",this.encodeMetadata(l)),s.append("",i)):"undefined"!=typeof FormData&&i instanceof FormData?((s=i).append("cacheControl",a.cacheControl),l&&s.append("metadata",this.encodeMetadata(l))):(s=i,o["cache-control"]=`max-age=${a.cacheControl}`,o["content-type"]=a.contentType,l&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),(null==n?void 0:n.headers)&&(o=Object.assign(Object.assign({},o),n.headers));let c=this._removeEmptyFolders(t),u=this._getFinalPath(c),h=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:s,headers:o},(null==a?void 0:a.duplex)?{duplex:a.duplex}:{})),d=yield h.json();if(h.ok)return{data:{path:c,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}upload(e,t,r){return a(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,i,n){return a(this,void 0,void 0,function*(){let a=this._removeEmptyFolders(e),o=this._getFinalPath(a),l=new URL(this.url+`/object/upload/sign/${o}`);l.searchParams.set("token",t);try{let e,t=Object.assign({upsert:r.upsert},n),s=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&i instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",i)):"undefined"!=typeof FormData&&i instanceof FormData?(e=i).append("cacheControl",t.cacheControl):(e=i,s["cache-control"]=`max-age=${t.cacheControl}`,s["content-type"]=t.contentType);let o=yield this.fetch(l.toString(),{method:"PUT",body:e,headers:s}),c=yield o.json();if(o.ok)return{data:{path:a,fullPath:c.Key},error:null};return{data:null,error:c}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return a(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(n["x-upsert"]="true");let a=yield(0,i.post)(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:n}),o=new URL(this.url+a.url),l=o.searchParams.get("token");if(!l)throw new s.StorageError("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:l},error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}update(e,t,r){return a(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return a(this,void 0,void 0,function*(){try{return{data:yield(0,i.post)(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}copy(e,t,r){return a(this,void 0,void 0,function*(){try{return{data:{path:(yield(0,i.post)(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return a(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),a=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n={signedUrl:encodeURI(`${this.url}${n.signedURL}${a}`)},error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return a(this,void 0,void 0,function*(){try{let s=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}download(e,t){return a(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),n=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),a=n?`?${n}`:"";try{let t=this._getFinalPath(e),s=yield(0,i.get)(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${a}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}info(e){return a(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield(0,i.get)(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:(0,n.recursiveToCamel)(e),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}exists(e){return a(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield(0,i.head)(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if((0,s.isStorageError)(e)&&e instanceof s.StorageUnknownError){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),s=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&s.push(i);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&s.push(a);let o=s.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return a(this,void 0,void 0,function*(){try{return{data:yield(0,i.remove)(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}list(e,r,n){return a(this,void 0,void 0,function*(){try{let s=Object.assign(Object.assign(Object.assign({},t),r),{prefix:e||""});return{data:yield(0,i.post)(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},n),error:null}}catch(e){if((0,s.isStorageError)(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}}},801930:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({version:()=>t});let t="2.7.1"}},145146:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({DEFAULT_HEADERS:()=>t});var s=e.i(801930);let t={"X-Client-Info":`storage-js/${s.version}`}}},291537:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(145146),i=e.i(41910),n=e.i(100306),a=e.i(129371),o=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};class t{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),t),this.fetch=(0,a.resolveFetch)(r)}listBuckets(){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.get)(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}getBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.get)(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.post)(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.put)(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}emptyBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.post)(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}deleteBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,n.remove)(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}}}},158077:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({StorageClient:()=>t});var s=e.i(904274),i=e.i(291537);class t extends i.default{constructor(e,t={},r){super(e,t,r)}from(e){return new s.default(this.url,this.headers,e,this.fetch)}}}},830558:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({version:()=>t});let t="2.49.8"}},641842:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({DEFAULT_AUTH_OPTIONS:()=>a,DEFAULT_DB_OPTIONS:()=>n,DEFAULT_GLOBAL_OPTIONS:()=>i,DEFAULT_HEADERS:()=>r,DEFAULT_REALTIME_OPTIONS:()=>o});var s=e.i(830558);let t="";t="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let r={"X-Client-Info":`supabase-js-${t}/${s.version}`},i={headers:r},n={schema:"public"},a={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},o={}}},58609:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({fetchWithAuth:()=>n,resolveFetch:()=>t,resolveHeadersConstructor:()=>r});var s=e.i(455544),i=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};let t=e=>{let t;return t=e||("undefined"==typeof fetch?s.default:fetch),(...e)=>t(...e)},r=()=>"undefined"==typeof Headers?s.Headers:Headers,n=(e,s,n)=>{let a=t(n),o=r();return(t,r)=>i(void 0,void 0,void 0,function*(){var i;let n=null!=(i=yield s())?i:e,l=new o(null==r?void 0:r.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${n}`),a(t,Object.assign(Object.assign({},r),{headers:l}))})}}},230447:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({applySettingDefaults:()=>a,ensureTrailingSlash:()=>n,isBrowser:()=>t,uuid:()=>i});var s=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}function n(e){return e.endsWith("/")?e:e+"/"}let t=()=>!1;function a(e,t){var r,i;let{db:n,auth:a,realtime:o,global:l}=e,{db:c,auth:u,realtime:h,global:d}=t,f={db:Object.assign(Object.assign({},c),n),auth:Object.assign(Object.assign({},u),a),realtime:Object.assign(Object.assign({},h),o),global:Object.assign(Object.assign(Object.assign({},d),l),{headers:Object.assign(Object.assign({},null!=(r=null==d?void 0:d.headers)?r:{}),null!=(i=null==l?void 0:l.headers)?i:{})}),accessToken:()=>s(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}}},215800:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({version:()=>t});let t="2.69.1"}},759907:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({API_VERSIONS:()=>h,API_VERSION_HEADER_NAME:()=>u,AUDIENCE:()=>o,AUTO_REFRESH_TICK_DURATION_MS:()=>t,AUTO_REFRESH_TICK_THRESHOLD:()=>r,BASE64URL_REGEX:()=>d,DEFAULT_HEADERS:()=>l,EXPIRY_MARGIN_MS:()=>i,GOTRUE_URL:()=>n,JWKS_TTL:()=>f,NETWORK_FAILURE:()=>c,STORAGE_KEY:()=>a});var s=e.i(215800);let t=3e4,r=3,i=9e4,n="http://localhost:9999",a="supabase.auth.token",o="",l={"X-Client-Info":`gotrue-js/${s.version}`},c={MAX_RETRIES:10,RETRY_INTERVAL:2},u="X-Supabase-Api-Version",h={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},d=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,f=6e5}},926363:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({AuthApiError:()=>r,AuthError:()=>t,AuthImplicitGrantRedirectError:()=>p,AuthInvalidCredentialsError:()=>f,AuthInvalidJwtError:()=>_,AuthInvalidTokenResponseError:()=>d,AuthPKCEGrantCodeExchangeError:()=>g,AuthRetryableFetchError:()=>y,AuthSessionMissingError:()=>h,AuthUnknownError:()=>c,AuthWeakPasswordError:()=>m,CustomAuthError:()=>u,isAuthApiError:()=>i,isAuthError:()=>s,isAuthImplicitGrantRedirectError:()=>a,isAuthRetryableFetchError:()=>o,isAuthSessionMissingError:()=>n,isAuthWeakPasswordError:()=>l});class t extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function s(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class r extends t{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}function i(e){return s(e)&&"AuthApiError"===e.name}class c extends t{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class u extends t{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class h extends u{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function n(e){return s(e)&&"AuthSessionMissingError"===e.name}class d extends u{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class f extends u{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class p extends u{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function a(e){return s(e)&&"AuthImplicitGrantRedirectError"===e.name}class g extends u{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class y extends u{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function o(e){return s(e)&&"AuthRetryableFetchError"===e.name}class m extends u{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}function l(e){return s(e)&&"AuthWeakPasswordError"===e.name}class _ extends u{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}}},815792:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({base64UrlToUint8Array:()=>u,byteFromBase64URL:()=>i,byteToBase64URL:()=>s,codepointToUTF8:()=>o,stringFromBase64URL:()=>a,stringFromUTF8:()=>c,stringToBase64URL:()=>n,stringToUTF8:()=>l,stringToUint8Array:()=>h});let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),r=" 	\n\r=".split(""),d=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<r.length;t+=1)e[r[t].charCodeAt(0)]=-2;for(let r=0;r<t.length;r+=1)e[t[r].charCodeAt(0)]=r;return e})();function s(e,r,s){if(null!==e)for(r.queue=r.queue<<8|e,r.queuedBits+=8;r.queuedBits>=6;)s(t[r.queue>>r.queuedBits-6&63]),r.queuedBits-=6;else if(r.queuedBits>0)for(r.queue=r.queue<<6-r.queuedBits,r.queuedBits=6;r.queuedBits>=6;)s(t[r.queue>>r.queuedBits-6&63]),r.queuedBits-=6}function i(e,t,r){let s=d[e];if(s>-1)for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===s)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function n(e){let t=[],r=e=>{t.push(e)},i={queue:0,queuedBits:0};return l(e,e=>{s(e,i,r)}),s(null,i,r),t.join("")}function a(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},a=e=>{c(e,s,r)};for(let t=0;t<e.length;t+=1)i(e.charCodeAt(t),n,a);return t.join("")}function o(e,t){if(e<=127)return void t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function l(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){let t=(s-55296)*1024&65535;s=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}o(s,t)}}function c(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}function u(e){let t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)i(e.charCodeAt(t),r,s);return new Uint8Array(t)}function h(e){let t=[];return l(e,e=>t.push(e)),new Uint8Array(t)}}},939092:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({Deferred:()=>T,decodeJWT:()=>c,expiresAt:()=>a,generatePKCEChallenge:()=>g,generatePKCEVerifier:()=>f,getAlgorithm:()=>v,getCodeChallengeAndMethod:()=>y,getItemAsync:()=>S,isBrowser:()=>t,looksLikeFetchResponse:()=>w,parseParametersFromURL:()=>l,parseResponseAPIVersion:()=>m,removeItemAsync:()=>A,resolveFetch:()=>b,retryable:()=>h,setItemAsync:()=>E,sleep:()=>u,supportsLocalStorage:()=>r,uuid:()=>o,validateExp:()=>_});var s=e.i(759907),i=e.i(926363),n=e.i(815792);function a(e){return Math.round(Date.now()/1e3)+e}function o(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}let t=()=>!1,r=()=>{if(!t())return!1};function l(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}let b=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>e.r(609329)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)},w=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,E=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},S=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},A=async(e,t)=>{await e.removeItem(t)};class T{constructor(){this.promise=new T.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function c(e){let t=e.split(".");if(3!==t.length)throw new i.AuthInvalidJwtError("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!s.BASE64URL_REGEX.test(t[e]))throw new i.AuthInvalidJwtError("JWT not in base64url format");return{header:JSON.parse((0,n.stringFromBase64URL)(t[0])),payload:JSON.parse((0,n.stringFromBase64URL)(t[1])),signature:(0,n.base64UrlToUint8Array)(t[2]),raw:{header:t[0],payload:t[1]}}}async function u(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function h(e,t){return new Promise((r,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{let s=await e(i);if(!t(i,null,s))return void r(s)}catch(e){if(!t(i,e))return void s(e)}})()})}function d(e){return("0"+e.toString(16)).substr(-2)}function f(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,d).join("")}async function p(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function g(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await p(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function y(e,t,r=!1){let s=f(),i=s;r&&(i+="/PASSWORD_RECOVERY"),await E(e,`${t}-code-verifier`,i);let n=await g(s),a=s===n?"plain":"s256";return[n,a]}T.promiseConstructor=Promise;let k=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function m(e){let t=e.headers.get(s.API_VERSION_HEADER_NAME);if(!t||!t.match(k))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}function _(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}function v(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}}},656696:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({_generateLinkResponse:()=>p,_noResolveJsonResponse:()=>g,_request:()=>l,_sessionResponse:()=>u,_sessionResponsePassword:()=>h,_ssoResponse:()=>f,_userResponse:()=>d,handleError:()=>o});var s=e.i(759907),i=e.i(939092),n=e.i(926363),a=this&&this.__rest||function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(e);i<s.length;i++)0>t.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]]);return r};let t=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),r=[502,503,504];async function o(e){var a;let o,l;if(!(0,i.looksLikeFetchResponse)(e))throw new n.AuthRetryableFetchError(t(e),0);if(r.includes(e.status))throw new n.AuthRetryableFetchError(t(e),e.status);try{o=await e.json()}catch(e){throw new n.AuthUnknownError(t(e),e)}let c=(0,i.parseResponseAPIVersion)(e);if(c&&c.getTime()>=s.API_VERSIONS["2024-01-01"].timestamp&&"object"==typeof o&&o&&"string"==typeof o.code?l=o.code:"object"==typeof o&&o&&"string"==typeof o.error_code&&(l=o.error_code),l){if("weak_password"===l)throw new n.AuthWeakPasswordError(t(o),e.status,(null==(a=o.weak_password)?void 0:a.reasons)||[]);else if("session_not_found"===l)throw new n.AuthSessionMissingError}else if("object"==typeof o&&o&&"object"==typeof o.weak_password&&o.weak_password&&Array.isArray(o.weak_password.reasons)&&o.weak_password.reasons.length&&o.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new n.AuthWeakPasswordError(t(o),e.status,o.weak_password.reasons);throw new n.AuthApiError(t(o),e.status||500,l)}let y=(e,t,r,s)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(s),Object.assign(Object.assign({},i),r))};async function l(e,t,r,i){var n;let a=Object.assign({},null==i?void 0:i.headers);a[s.API_VERSION_HEADER_NAME]||(a[s.API_VERSION_HEADER_NAME]=s.API_VERSIONS["2024-01-01"].name),(null==i?void 0:i.jwt)&&(a.Authorization=`Bearer ${i.jwt}`);let o=null!=(n=null==i?void 0:i.query)?n:{};(null==i?void 0:i.redirectTo)&&(o.redirect_to=i.redirectTo);let l=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",u=await c(e,t,r+l,{headers:a,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(u):{data:Object.assign({},u),error:null}}async function c(e,r,s,i,a,l){let c,u=y(r,i,a,l);try{c=await e(s,Object.assign({},u))}catch(e){throw console.error(e),new n.AuthRetryableFetchError(t(e),0)}if(c.ok||await o(c),null==i?void 0:i.noResolveJson)return c;try{return await c.json()}catch(e){await o(e)}}function u(e){var t,r;let s=null;return(r=e).access_token&&r.refresh_token&&r.expires_in&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(0,i.expiresAt)(e.expires_in))),{data:{session:s,user:null!=(t=e.user)?t:e},error:null}}function h(e){let t=u(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function d(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function f(e){return{data:e,error:null}}function p(e){let{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n},user:Object.assign({},a(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function g(e){return e}}},257679:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(656696),i=e.i(939092),n=e.i(926363),a=this&&this.__rest||function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(e);i<s.length;i++)0>t.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]]);return r};class t{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=(0,i.resolveFetch)(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await (0,s._request)(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await (0,s._request)(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:s._userResponse})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=a(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await (0,s._request)(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:s._generateLinkResponse,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if((0,n.isAuthError)(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await (0,s._request)(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:s._userResponse})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,a,o,l,c;try{let n={nextPage:null,lastPage:0,total:0},u=await (0,s._request)(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(a=null==(i=null==e?void 0:e.perPage)?void 0:i.toString())?a:""},xform:s._noResolveJsonResponse});if(u.error)throw u.error;let h=await u.json(),d=null!=(o=u.headers.get("x-total-count"))?o:0,f=null!=(c=null==(l=u.headers.get("link"))?void 0:l.split(","))?c:[];return f.length>0&&(f.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);n[`${r}Page`]=t}),n.total=parseInt(d)),{data:Object.assign(Object.assign({},h),n),error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){try{return await (0,s._request)(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:s._userResponse})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){try{return await (0,s._request)(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:s._userResponse})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){try{return await (0,s._request)(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:s._userResponse})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){try{let{data:t,error:r}=await (0,s._request)(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async _deleteFactor(e){try{return{data:await (0,s._request)(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}}}},287383:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({localStorageAdapter:()=>t,memoryLocalStorageAdapter:()=>i});var s=e.i(939092);let t={getItem:e=>(0,s.supportsLocalStorage)()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{(0,s.supportsLocalStorage)()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{(0,s.supportsLocalStorage)()&&globalThis.localStorage.removeItem(e)}};function i(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}}},780223:e=>{"use strict";var{g:t,__dirname:r}=e;function s(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}e.s({polyfillGlobalThis:()=>s})},254247:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({LockAcquireTimeoutError:()=>r,NavigatorLockAcquireTimeoutError:()=>a,ProcessLockAcquireTimeoutError:()=>o,internals:()=>t,navigatorLock:()=>i,processLock:()=>n});var s=e.i(939092);let t={debug:!!(globalThis&&(0,s.supportsLocalStorage)()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class r extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class a extends r{}class o extends r{}async function i(e,r,s){t.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,r);let i=new globalThis.AbortController;return r>0&&setTimeout(()=>{i.abort(),t.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},r),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===r?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){t.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await s()}finally{t.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}if(0===r)throw t.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new a(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(t.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}))}let l={};async function n(e,t,r){var s;let i=null!=(s=l[e])?s:Promise.resolve(),n=Promise.race([i.catch(()=>null),t>=0?new Promise((r,s)=>{setTimeout(()=>{s(new o(`Acquring process lock with name "${e}" timed out`))},t)}):null].filter(e=>e)).catch(e=>{if(e&&e.isAcquireTimeout)throw e;return null}).then(async()=>await r());return l[e]=n.catch(async e=>{if(e&&e.isAcquireTimeout)return await i,null;throw e}),await n}}},461229:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>r});var s=e.i(257679),i=e.i(759907),n=e.i(926363),a=e.i(656696),o=e.i(939092),l=e.i(287383),c=e.i(780223),u=e.i(215800),h=e.i(254247),d=e.i(815792);(0,c.polyfillGlobalThis)();let t={url:i.GOTRUE_URL,storageKey:i.STORAGE_KEY,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:i.DEFAULT_HEADERS,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function f(e,t,r){return await r()}class r{constructor(e){var i,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=r.nextInstanceID,r.nextInstanceID+=1,this.instanceID>0&&(0,o.isBrowser)()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let a=Object.assign(Object.assign({},t),e);if(this.logDebugMessages=!!a.debug,"function"==typeof a.debug&&(this.logger=a.debug),this.persistSession=a.persistSession,this.storageKey=a.storageKey,this.autoRefreshToken=a.autoRefreshToken,this.admin=new s.default({url:a.url,headers:a.headers,fetch:a.fetch}),this.url=a.url,this.headers=a.headers,this.fetch=(0,o.resolveFetch)(a.fetch),this.lock=a.lock||f,this.detectSessionInUrl=a.detectSessionInUrl,this.flowType=a.flowType,this.hasCustomAuthorizationHeader=a.hasCustomAuthorizationHeader,a.lock?this.lock=a.lock:(0,o.isBrowser)()&&(null==(i=null==globalThis?void 0:globalThis.navigator)?void 0:i.locks)?this.lock=h.navigatorLock:this.lock=f,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?a.storage?this.storage=a.storage:(0,o.supportsLocalStorage)()?this.storage=l.localStorageAdapter:(this.memoryStorage={},this.storage=(0,l.memoryLocalStorageAdapter)(this.memoryStorage)):(this.memoryStorage={},this.storage=(0,l.memoryLocalStorageAdapter)(this.memoryStorage)),(0,o.isBrowser)()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(n=this.broadcastChannel)||n.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${u.version}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=(0,o.parseParametersFromURL)(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),(0,o.isBrowser)()&&this.detectSessionInUrl&&"none"!==r){let{data:s,error:i}=await this._getSessionFromURL(t,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),(0,n.isAuthImplicitGrantRedirectError)(i)){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:a,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",a,"redirect type",o),await this._saveSession(a),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if((0,n.isAuthError)(e))return{error:e};return{error:new n.AuthUnknownError("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{let{data:i,error:n}=await (0,a._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(s=null==e?void 0:e.options)?void 0:s.captchaToken}},xform:a._sessionResponse});if(n||!i)return{data:{user:null,session:null},error:n};let o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,s;try{let i;if("email"in e){let{email:r,password:s,options:n}=e,l=null,c=null;"pkce"===this.flowType&&([l,c]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey)),i=await (0,a._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:s,data:null!=(t=null==n?void 0:n.data)?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:l,code_challenge_method:c},xform:a._sessionResponse})}else if("phone"in e){let{phone:t,password:n,options:o}=e;i=await (0,a._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!=(r=null==o?void 0:o.data)?r:{},channel:null!=(s=null==o?void 0:o.channel)?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:a._sessionResponse})}else throw new n.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:l,error:c}=i;if(c||!l)return{data:{user:null,session:null},error:c};let u=l.session,h=l.user;return l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",u)),{data:{user:h,session:u},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:s,options:i}=e;t=await (0,a._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:a._sessionResponsePassword})}else if("phone"in e){let{phone:r,password:s,options:i}=e;t=await (0,a._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:a._sessionResponsePassword})}else throw new n.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:r,error:s}=t;if(s)return{data:{user:null,session:null},error:s};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new n.AuthInvalidTokenResponseError};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,s,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(s=e.options)?void 0:s.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){let t=await (0,o.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await (0,a._request)(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:a._sessionResponse});if(await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new n.AuthInvalidTokenResponseError};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:i}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:s,access_token:i,nonce:o}=e,{data:l,error:c}=await (0,a._request)(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:a._sessionResponse});if(c)return{data:{user:null,session:null},error:c};if(!l||!l.session||!l.user)return{data:{user:null,session:null},error:new n.AuthInvalidTokenResponseError};return l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,s,i,l;try{if("email"in e){let{email:s,options:i}=e,n=null,l=null;"pkce"===this.flowType&&([n,l]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{error:c}=await (0,a._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(r=null==i?void 0:i.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:n,code_challenge_method:l},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:c}}if("phone"in e){let{phone:t,options:r}=e,{data:n,error:o}=await (0,a._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(s=null==r?void 0:r.data)?s:{},create_user:null==(i=null==r?void 0:r.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(l=null==r?void 0:r.channel)?l:"sms"}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new n.AuthInvalidCredentialsError("You must provide either an email or phone number.")}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let s,i;"options"in e&&(s=null==(t=e.options)?void 0:t.redirectTo,i=null==(r=e.options)?void 0:r.captchaToken);let{data:n,error:o}=await (0,a._request)(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:a._sessionResponse});if(o)throw o;if(!n)throw Error("An error occurred on token verification.");let l=n.session,c=n.user;return(null==l?void 0:l.access_token)&&(await this._saveSession(l),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,s;try{let i=null,n=null;return"pkce"===this.flowType&&([i,n]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey)),await (0,a._request)(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(s=null==e?void 0:e.options)?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:n}),headers:this.headers,xform:a._ssoResponse})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new n.AuthSessionMissingError;let{error:s}=await (0,a._request)(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:s,options:i}=e,{error:n}=await (0,a._request)(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:s,options:i}=e,{data:n,error:o}=await (0,a._request)(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new n.AuthInvalidCredentialsError("You must provide either an email or phone number and a type")}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await (0,o.getItemAsync)(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<i.EXPIRY_MARGIN_MS;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}let{session:s,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{session:null},error:n};return{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await (0,a._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:a._userResponse});return await this._useSession(async e=>{var t,r,s;let{data:i,error:o}=e;if(o)throw o;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await (0,a._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(s=null==(r=i.session)?void 0:r.access_token)?s:void 0,xform:a._userResponse}):{data:{user:null},error:new n.AuthSessionMissingError}})}catch(e){if((0,n.isAuthError)(e))return(0,n.isAuthSessionMissingError)(e)&&(await this._removeSession(),await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:s,error:i}=r;if(i)throw i;if(!s.session)throw new n.AuthSessionMissingError;let l=s.session,c=null,u=null;"pkce"===this.flowType&&null!=e.email&&([c,u]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{data:h,error:d}=await (0,a._request)(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:c,code_challenge_method:u}),jwt:l.access_token,xform:a._userResponse});if(d)throw d;return l.user=h.user,await this._saveSession(l),await this._notifyAllSubscribers("USER_UPDATED",l),{data:{user:l.user},error:null}})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new n.AuthSessionMissingError;let t=Date.now()/1e3,r=t,s=!0,i=null,{payload:a}=(0,o.decodeJWT)(e.access_token);if(a.exp&&(s=(r=a.exp)<=t),s){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:s,error:n}=await this._getUser(e.access_token);if(n)throw n;i={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:s,error:i}=t;if(i)throw i;e=null!=(r=s.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new n.AuthSessionMissingError;let{session:s,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if((0,n.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!(0,o.isBrowser)())throw new n.AuthImplicitGrantRedirectError("No browser detected.");if(e.error||e.error_description||e.error_code)throw new n.AuthImplicitGrantRedirectError(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new n.AuthPKCEGrantCodeExchangeError("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new n.AuthImplicitGrantRedirectError("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new n.AuthPKCEGrantCodeExchangeError("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:s,access_token:a,refresh_token:l,expires_in:c,expires_at:u,token_type:h}=e;if(!a||!c||!l||!h)throw new n.AuthImplicitGrantRedirectError("No session defined in URL");let d=Math.round(Date.now()/1e3),f=parseInt(c),p=d+f;u&&(p=parseInt(u));let g=p-d;1e3*g<=i.AUTO_REFRESH_TICK_DURATION_MS&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${f}s`);let y=p-f;d-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,p,d):d-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,p,d);let{data:m,error:_}=await this._getUser(a);if(_)throw _;let v={provider_token:r,provider_refresh_token:s,access_token:a,expires_in:f,expires_at:p,refresh_token:l,token_type:h,user:m.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await (0,o.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:s,error:i}=t;if(i)return{error:i};let a=null==(r=s.session)?void 0:r.access_token;if(a){let{error:t}=await this.admin.signOut(a,e);if(t&&!((0,n.isAuthApiError)(t)&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t=(0,o.uuid)(),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{let{data:{session:s},error:i}=t;if(i)throw i;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(t){await (null==(s=this.stateChangeEmitters.get(e))?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey,!0));try{return await (0,a._request)(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:s}=await this._useSession(async t=>{var r,s,i,n,o;let{data:l,error:c}=t;if(c)throw c;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(s=e.options)?void 0:s.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await (0,a._request)(this.fetch,"GET",u,{headers:this.headers,jwt:null!=(o=null==(n=l.session)?void 0:n.access_token)?o:void 0})});if(s)throw s;return!(0,o.isBrowser)()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if((0,n.isAuthError)(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;let{data:i,error:n}=t;if(n)throw n;return await (0,a._request)(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(s=null==(r=i.session)?void 0:r.access_token)?s:void 0})})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{let r=Date.now();return await (0,o.retryable)(async r=>(r>0&&await (0,o.sleep)(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await (0,a._request)(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:a._sessionResponse})),(e,t)=>{let s=200*Math.pow(2,e);return t&&(0,n.isAuthRetryableFetchError)(t)&&Date.now()+s-r<i.AUTO_REFRESH_TICK_DURATION_MS})}catch(e){if(this._debug(t,"error",e),(0,n.isAuthError)(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),(0,o.isBrowser)()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await (0,o.getItemAsync)(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let s=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<i.EXPIRY_MARGIN_MS;if(this._debug(t,`session has${s?"":" not"} expired with margin of ${i.EXPIRY_MARGIN_MS}s`),s){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),(0,n.isAuthRetryableFetchError)(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new n.AuthSessionMissingError;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new o.Deferred;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new n.AuthSessionMissingError;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(e){if(this._debug(s,"error",e),(0,n.isAuthError)(e)){let r={session:null,error:e};return(0,n.isAuthRetryableFetchError)(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){let s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let s=[],i=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){s.push(e)}});if(await Promise.all(i),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await (0,o.setItemAsync)(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await (0,o.removeItemAsync)(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&(0,o.isBrowser)()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),i.AUTO_REFRESH_TICK_DURATION_MS);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let s=Math.floor((1e3*r.expires_at-e)/i.AUTO_REFRESH_TICK_DURATION_MS);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${i.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${i.AUTO_REFRESH_TICK_THRESHOLD} ticks`),s<=i.AUTO_REFRESH_TICK_THRESHOLD&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof h.LockAcquireTimeoutError)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!(0,o.isBrowser)()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;return i?{data:null,error:i}:await (0,a._request)(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==s?void 0:s.session)?void 0:r.access_token})})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;let{data:i,error:n}=t;if(n)return{data:null,error:n};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=await (0,a._request)(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return c?{data:null,error:c}:("totp"===e.factorType&&(null==(s=null==l?void 0:l.totp)?void 0:s.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;if(i)return{data:null,error:i};let{data:n,error:o}=await (0,a._request)(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==s?void 0:s.session)?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;return i?{data:null,error:i}:await (0,a._request)(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==s?void 0:s.session)?void 0:r.access_token})})}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:s},error:i}=e;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=(0,o.decodeJWT)(s.access_token),a=null;n.aal&&(a=n.aal);let l=a;return(null!=(r=null==(t=s.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(l="aal2"),{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+i.JWKS_TTL>Date.now())return r;let{data:s,error:o}=await (0,a._request)(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!s.keys||0===s.keys.length)throw new n.AuthInvalidJwtError("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),!(r=s.keys.find(t=>t.kid===e)))throw new n.AuthInvalidJwtError("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:s,payload:i,signature:a,raw:{header:l,payload:c}}=(0,o.decodeJWT)(r);if((0,o.validateExp)(i.exp),!s.kid||"HS256"===s.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:i,header:s,signature:a},error:null}}let u=(0,o.getAlgorithm)(s.alg),h=await this.fetchJwk(s.kid,t),f=await crypto.subtle.importKey("jwk",h,u,!0,["verify"]);if(!await crypto.subtle.verify(u,f,a,(0,d.stringToUint8Array)(`${l}.${c}`)))throw new n.AuthInvalidJwtError("Invalid JWT signature");return{data:{claims:i,header:s,signature:a},error:null}}catch(e){if((0,n.isAuthError)(e))return{data:null,error:e};throw e}}}r.nextInstanceID=0}},514775:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=e.i(257679).default}},868177:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});let t=e.i(461229).default}},656890:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({})},449484:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(257679),e.i(461229),e.i(514775),e.i(868177),e.i(656890),e.i(926363),e.i(254247)},61438:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(257679),e.i(461229),e.i(514775),e.i(868177),e.i(656890),e.i(926363),e.i(254247),e.i(449484)},22952:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({AuthClient:()=>s.default});var s=e.i(868177)},576436:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({SupabaseAuthClient:()=>t}),e.i(61438);var s=e.i(22952);class t extends s.AuthClient{constructor(e){super(e)}}}},814071:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t});var s=e.i(966735),i=e.i(810742);e.i(622764);var n=e.i(505053),a=e.i(158077),o=e.i(641842),l=e.i(58609),c=e.i(230447),u=e.i(576436),h=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))(function(i,n){function a(e){try{l(s.next(e))}catch(e){n(e)}}function o(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((s=s.apply(e,t||[])).next())})};class t{constructor(e,t,r){var s,n,a;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let u=new URL((0,c.ensureTrailingSlash)(e));this.realtimeUrl=new URL("realtime/v1",u),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",u),this.storageUrl=new URL("storage/v1",u),this.functionsUrl=new URL("functions/v1",u);let h=`sb-${u.hostname.split(".")[0]}-auth-token`,d={db:o.DEFAULT_DB_OPTIONS,realtime:o.DEFAULT_REALTIME_OPTIONS,auth:Object.assign(Object.assign({},o.DEFAULT_AUTH_OPTIONS),{storageKey:h}),global:o.DEFAULT_GLOBAL_OPTIONS},f=(0,c.applySettingDefaults)(null!=r?r:{},d);this.storageKey=null!=(s=f.auth.storageKey)?s:"",this.headers=null!=(n=f.global.headers)?n:{},f.accessToken?(this.accessToken=f.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(a=f.auth)?a:{},this.headers,f.global.fetch),this.fetch=(0,l.fetchWithAuth)(t,this._getAccessToken.bind(this),f.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},f.realtime)),this.rest=new i.PostgrestClient(new URL("rest/v1",u).href,{headers:this.headers,schema:f.db.schema,fetch:this.fetch}),f.accessToken||this._listenForAuthEvents()}get functions(){return new s.FunctionsClient(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new a.StorageClient(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return h(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:i,flowType:n,lock:a,debug:o},l,c){let h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new u.SupabaseAuthClient({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new n.RealtimeClient(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}}},462184:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({createClient:()=>t});var s=e.i(814071);e.i(61438),e.i(810742),e.i(622764);let t=(e,t,r)=>new s.default(e,t,r)}},669701:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({VERSION:()=>t});let t="0.6.1"}},447203:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.parse=function(e,t){let r=new c,s=e.length;if(s<2)return r;let i=t?.decode||o,l=0;do{let t=e.indexOf("=",l);if(-1===t)break;let o=e.indexOf(";",l),c=-1===o?s:o;if(t>c){l=e.lastIndexOf(";",t-1)+1;continue}let u=n(e,l,t),h=a(e,t,u),d=e.slice(u,h);if(void 0===r[d]){let s=n(e,t+1,c),o=a(e,c,s),l=i(e.slice(s,o));r[d]=l}l=c+1}while(l<s)return r},i.serialize=function(i,n,a){let o=a?.encode||encodeURIComponent;if(!e.test(i))throw TypeError(`argument name is invalid: ${i}`);let c=o(n);if(!t.test(c))throw TypeError(`argument val is invalid: ${n}`);let u=i+"="+c;if(!a)return u;if(void 0!==a.maxAge){if(!Number.isInteger(a.maxAge))throw TypeError(`option maxAge is invalid: ${a.maxAge}`);u+="; Max-Age="+a.maxAge}if(a.domain){if(!r.test(a.domain))throw TypeError(`option domain is invalid: ${a.domain}`);u+="; Domain="+a.domain}if(a.path){if(!s.test(a.path))throw TypeError(`option path is invalid: ${a.path}`);u+="; Path="+a.path}if(a.expires){var h;if(h=a.expires,"[object Date]"!==l.call(h)||!Number.isFinite(a.expires.valueOf()))throw TypeError(`option expires is invalid: ${a.expires}`);u+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(u+="; HttpOnly"),a.secure&&(u+="; Secure"),a.partitioned&&(u+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${a.priority}`)}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${a.sameSite}`)}return u};let e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,l=Object.prototype.toString,c=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r)return r}function a(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function o(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}},958826:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({isBrowser:()=>a,parse:()=>t,parseCookieHeader:()=>i,serialize:()=>r,serializeCookieHeader:()=>n});var s=e.i(447203);let t=s.parse,r=s.serialize;function i(e){let t=(0,s.parse)(e);return Object.keys(t??{}).map(e=>({name:e,value:t[e]}))}function n(e,t,r){return(0,s.serialize)(e,t,r)}function a(){return!1}}},16428:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({DEFAULT_COOKIE_OPTIONS:()=>t});let t={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4}}},719979:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({MAX_CHUNK_SIZE:()=>t,combineChunks:()=>n,createChunks:()=>i,deleteChunks:()=>a,isChunkLike:()=>s});let t=3180,r=/^(.*)[.](0|[1-9][0-9]*)$/;function s(e,t){if(e===t)return!0;let s=e.match(r);return!!s&&s[1]===t}function i(e,r,s){let i=s??t,n=encodeURIComponent(r);if(n.length<=i)return[{name:e,value:r}];let a=[];for(;n.length>0;){let e=n.slice(0,i),t=e.lastIndexOf("%");t>i-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}a.push(r),n=n.slice(e.length)}return a.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function n(e,t){let r=await t(e);if(r)return r;let s=[];for(let r=0;;r++){let i=`${e}.${r}`,n=await t(i);if(!n)break;s.push(n)}return s.length>0?s.join(""):null}async function a(e,t,r){await t(e)&&await r(e);for(let s=0;;s++){let i=`${e}.${s}`;if(!await t(i))break;await r(i)}}}},995922:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({codepointToUTF8:()=>n,stringFromBase64URL:()=>i,stringFromUTF8:()=>o,stringToBase64URL:()=>s,stringToUTF8:()=>a});let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),r=" 	\n\r=".split(""),l=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<r.length;t+=1)e[r[t].charCodeAt(0)]=-2;for(let r=0;r<t.length;r+=1)e[t[r].charCodeAt(0)]=r;return e})();function s(e){let r=[],s=0,i=0;if(a(e,e=>{for(s=s<<8|e,i+=8;i>=6;){let e=s>>i-6&63;r.push(t[e]),i-=6}}),i>0)for(s<<=6-i,i=6;i>=6;){let e=s>>i-6&63;r.push(t[e]),i-=6}return r.join("")}function i(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},i=0,n=0;for(let t=0;t<e.length;t+=1){let a=l[e.charCodeAt(t)];if(a>-1)for(i=i<<6|a,n+=6;n>=8;)o(i>>n-8&255,s,r),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}function n(e,t){if(e<=127)return void t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function a(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){let t=(s-55296)*1024&65535;s=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}n(s,t)}}function o(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}}},782312:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(958826),e.i(16428),e.i(719979),e.i(995922)},302344:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(958826),e.i(16428),e.i(719979),e.i(995922),e.i(782312)},281023:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({applyServerStorage:()=>c,createStorageFromOptions:()=>l});var s=e.i(447203);e.i(302344);var i=e.i(16428),n=e.i(719979),a=e.i(958826),o=e.i(995922);let t="base64-";function l(e,r){let l,u,h=e.cookies??null,d=e.cookieEncoding,f={},p={};if(h)if("get"in h){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let s=await h.get(t[e]);(s||"string"==typeof s)&&r.push({name:t[e],value:s})}return r};if(l=async t=>await e(t),"set"in h&&"remove"in h)u=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:s,options:i}=e[t];s?await h.set(r,s,i):await h.remove(r,i)}};else if(r)u=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in h)if(l=async()=>await h.getAll(),"setAll"in h)u=h.setAll;else if(r)u=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${r?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,a.isBrowser)()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!r&&(0,a.isBrowser)()){let e=()=>{let e=(0,s.parse)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};l=()=>e(),u=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,s.serialize)(e,t,r)})}}else if(r)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else l=()=>[],u=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return r?{getAll:l,setAll:u,setItems:f,removedItems:p,storage:{isServer:!0,getItem:async e=>{if("string"==typeof f[e])return f[e];if(p[e])return null;let r=await l([e]),s=await (0,n.combineChunks)(e,async e=>{let t=r?.find(({name:t})=>t===e)||null;return t?t.value:null});if(!s)return null;let i=s;return"string"==typeof s&&s.startsWith(t)&&(i=(0,o.stringFromBase64URL)(s.substring(t.length))),i},setItem:async(t,r)=>{t.endsWith("-code-verifier")&&await c({getAll:l,setAll:u,setItems:{[t]:r},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:d}),f[t]=r,delete p[t]},removeItem:async e=>{delete f[e],p[e]=!0}}}:{getAll:l,setAll:u,setItems:f,removedItems:p,storage:{isServer:!1,getItem:async e=>{let r=await l([e]),s=await (0,n.combineChunks)(e,async e=>{let t=r?.find(({name:t})=>t===e)||null;return t?t.value:null});if(!s)return null;let i=s;return s.startsWith(t)&&(i=(0,o.stringFromBase64URL)(s.substring(t.length))),i},setItem:async(r,s)=>{let a=await l([r]),c=new Set((a?.map(({name:e})=>e)||[]).filter(e=>(0,n.isChunkLike)(e,r))),h=s;"base64url"===d&&(h=t+(0,o.stringToBase64URL)(s));let f=(0,n.createChunks)(r,h);f.forEach(({name:e})=>{c.delete(e)});let p={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:0},g={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:i.DEFAULT_COOKIE_OPTIONS.maxAge};delete p.name,delete g.name;let y=[...[...c].map(e=>({name:e,value:"",options:p})),...f.map(({name:e,value:t})=>({name:e,value:t,options:g}))];y.length>0&&await u(y)},removeItem:async t=>{let r=await l([t]),s=(r?.map(({name:e})=>e)||[]).filter(e=>(0,n.isChunkLike)(e,t)),a={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:0};delete a.name,s.length>0&&await u(s.map(e=>({name:e,value:"",options:a})))}}}}async function c({getAll:e,setAll:r,setItems:s,removedItems:a},l){let c=l.cookieEncoding,u=l.cookieOptions??null,h=await e([...s?Object.keys(s):[],...a?Object.keys(a):[]]),d=h?.map(({name:e})=>e)||[],f=Object.keys(a).flatMap(e=>d.filter(t=>(0,n.isChunkLike)(t,e))),p=Object.keys(s).flatMap(e=>{let r=new Set(d.filter(t=>(0,n.isChunkLike)(t,e))),i=s[e];"base64url"===c&&(i=t+(0,o.stringToBase64URL)(i));let a=(0,n.createChunks)(e,i);return a.forEach(e=>{r.delete(e.name)}),f.push(...r),a}),g={...i.DEFAULT_COOKIE_OPTIONS,...u,maxAge:0},y={...i.DEFAULT_COOKIE_OPTIONS,...u,maxAge:i.DEFAULT_COOKIE_OPTIONS.maxAge};delete g.name,delete y.name,await r([...f.map(e=>({name:e,value:"",options:g})),...p.map(({name:e,value:t})=>({name:e,value:t,options:y}))])}}},947852:e=>{"use strict";var{g:t,__dirname:r}=e;{let t;e.s({createBrowserClient:()=>o});var s=e.i(462184),i=e.i(669701);e.i(302344);var n=e.i(958826),a=e.i(281023);function o(e,r,o){let l=o?.isSingleton===!0||(!o||!("isSingleton"in o))&&(0,n.isBrowser)();if(l&&t)return t;if(!e||!r)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:c}=(0,a.createStorageFromOptions)({...o,cookieEncoding:o?.cookieEncoding??"base64url"},!1),u=(0,s.createClient)(e,r,{...o,global:{...o?.global,headers:{...o?.global?.headers,"X-Client-Info":`supabase-ssr/${i.VERSION} createBrowserClient`}},auth:{...o?.auth,...o?.cookieOptions?.name?{storageKey:o.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:(0,n.isBrowser)(),detectSessionInUrl:(0,n.isBrowser)(),persistSession:!0,storage:c}});return l&&(t=u),u}}},138318:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({createServerClient:()=>a});var s=e.i(462184),i=e.i(669701),n=e.i(281023);function a(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:a,getAll:o,setAll:l,setItems:c,removedItems:u}=(0,n.createStorageFromOptions)({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),h=(0,s.createClient)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":`supabase-ssr/${i.VERSION} createServerClient`}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:a}});return h.auth.onAuthStateChange(async e=>{(Object.keys(c).length>0||Object.keys(u).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await (0,n.applyServerStorage)({getAll:o,setAll:l,setItems:c,removedItems:u},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),h}},474363:function(e){var{g:t,__dirname:r,m:s,e:i}=e},120031:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(947852),e.i(138318),e.i(474363),e.i(302344)},70784:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(947852),e.i(138318),e.i(474363),e.i(302344),e.i(120031)},994820:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({createClient:()=>n}),e.i(70784);var s=e.i(138318),i=e.i(998322);async function n(){let e=await (0,i.cookies)();return(0,s.createServerClient)("https://tzjelqzwdgidsjqhmvkr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6amVscXp3ZGdpZHNqcWhtdmtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTAsImV4cCI6MjA2MzkwNDY5MH0.MVkBlC_QI1IT7licw9CzLoS9yyo-QGXR7EFyXYZDyZc",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set(t,r,s))}catch{}}}})}},794437:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({checkSession:()=>r,getDoctorQuota:()=>u,getUser:()=>l,getUserById:()=>c,verifySession:()=>t}),e.i(470286);var s=e.i(535540),i=e.i(998322);e.i(83581);var n=e.i(807285),a=e.i(14156),o=e.i(994820);let t=(0,s.cache)(async()=>{let e=await (0,i.cookies)(),t=e.get("session")?.value,r=await (0,a.decrypt)(t);return r?.userId||(0,n.redirect)("/login"),{isAuth:!0,userId:r.userId}}),r=(0,s.cache)(async()=>{let e=await (0,i.cookies)(),t=e.get("session")?.value,r=await (0,a.decrypt)(t);return r?.userId?{isAuth:!0,userId:r.userId}:null}),l=(0,s.cache)(async()=>{let e=await t();if(!e)return null;try{let t=await (0,o.createClient)(),{data:r,error:s}=await t.from("doctors").select("*").eq("id",e.userId).single();if(s)return console.error("Failed to fetch user:",s.message||s),null;if(!r)return null;return{...r,password_hash:r.password_hash}}catch(e){return console.error("Failed to fetch user:",e instanceof Error?e.message:e),null}}),c=(0,s.cache)(async e=>{try{let t=await (0,o.createClient)(),{data:r,error:s}=await t.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",e).single();if(s)return console.error("Failed to fetch user by ID:",s.message||s),null;if(!r)return null;return{...r}}catch(e){return console.error("Failed to fetch user by ID:",e instanceof Error?e.message:e),null}}),u=(0,s.cache)(async e=>{try{let t=await (0,o.createClient)(),{data:r,error:s}=await t.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",e).single();if(s)return console.error("Failed to fetch quota:",s.message||s),null;let i=r.monthly_quota-r.quota_used,n=Math.round(r.quota_used/r.monthly_quota*100),a=new Date(r.quota_reset_at),l=Math.ceil((a.getTime()-Date.now())/864e5);return{monthly_quota:r.monthly_quota,quota_used:r.quota_used,quota_remaining:i,quota_percentage:n,quota_reset_at:r.quota_reset_at,days_until_reset:Math.max(0,l)}}catch(e){return console.error("Failed to fetch quota:",e instanceof Error?e.message:e),null}})}}};

//# sourceMappingURL=_aafe93da._.js.map