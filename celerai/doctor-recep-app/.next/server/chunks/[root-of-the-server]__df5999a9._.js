module.exports={929549:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},983943:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},86103:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},174538:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},945935:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},348629:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},771485:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("url",()=>require("url"))},62445:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("http",()=>require("http"))},348388:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("https",()=>require("https"))},109651:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("stream",()=>require("stream"))},794045:function(e){var{g:t,__dirname:r,m:n,e:a}=e;n.exports=e.x("zlib",()=>require("zlib"))},433077:function(e){var{g:t,__dirname:r,m:n,e:a}=e},460338:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({POST:()=>o});var n=e.i(125427),a=e.i(794437),s=e.i(994820);async function o(e){try{let t=await (0,a.verifySession)();if(!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await (0,s.createClient)(),{data:o}=await r.rpc("check_and_update_quota",{doctor_uuid:t.userId});if(!o)return n.NextResponse.json({error:"Quota exceeded. You have reached your monthly AI generation limit. Please contact admin or wait for next month."},{status:429});let i=await e.json();console.log("🔄 Next.js API Route - Received request body:",{primary_audio_url:i.primary_audio_url?"✅ Present":"❌ Missing",additional_audio_urls:i.additional_audio_urls?.length||0,image_urls:i.image_urls?.length||0,submitted_by:i.submitted_by,consultation_type:i.consultation_type,patient_name:i.patient_name});let u="http://localhost:3005";console.log("🔄 Next.js API Route - Forwarding to:",`${u}/api/generate-summary-stream`);let p=await fetch(`${u}/api/generate-summary-stream`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(console.log("🔄 Next.js API Route - Backend response status:",p.status),!p.ok){let e=await p.text();return console.error("🔄 Next.js API Route - Backend error:",e),n.NextResponse.json({error:"Failed to connect to backend service",details:e},{status:p.status})}let c=new ReadableStream({start(e){let t=p.body?.getReader();return t?function r(){return t.read().then(({done:t,value:n})=>t?void e.close():(e.enqueue(n),r()))}():void e.close()}});return new Response(c,{headers:{"Content-Type":"text/plain","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST","Access-Control-Allow-Headers":"Content-Type"}})}catch(e){return console.error("Streaming API error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}},332573:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({patchFetch:()=>i,routeModule:()=>t,serverHooks:()=>p,workAsyncStorage:()=>r,workUnitAsyncStorage:()=>u});var n=e.i(854885),a=e.i(814689),s=e.i(25402),o=e.i(460338);let t=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/generate-summary-stream/route",pathname:"/api/generate-summary-stream",filename:"route",bundlePath:""},resolvedPagePath:"[project]/src/app/api/generate-summary-stream/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:r,workUnitAsyncStorage:u,serverHooks:p}=t;function i(){return(0,s.patchFetch)({workAsyncStorage:r,workUnitAsyncStorage:u})}}},609329:e=>{var{g:t,__dirname:r}=e;e.v(e=>Promise.resolve().then(()=>e(455544)))},302749:e=>{var{g:t,__dirname:r}=e;e.v(t=>Promise.all(["server/chunks/[root-of-the-server]__76cf8bdc._.js","server/chunks/node_modules_ws_daabdc74._.js"].map(t=>e.l(t))).then(()=>t(947087)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__df5999a9._.js.map