{"version": 3, "sources": [], "sections": [{"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/instrumentation.ts"], "sourcesContent": ["import * as Sentry from '@sentry/nextjs';\n\nexport async function register() {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    await import('../sentry.server.config');\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    await import('../sentry.edge.config');\n  }\n}\n\nexport const onRequestError = Sentry.captureRequestError;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,wCAA2C;QACzC;IACF;IAEA,uCAAyC;;IAEzC;AACF;AAEO,MAAM,iBAAiB,0KAAA,CAAA,sBAA0B", "debugId": null}}]}