module.exports={929549:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},983943:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},86103:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},174538:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},945935:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},348629:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},771485:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("url",()=>require("url"))},62445:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("http",()=>require("http"))},348388:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("https",()=>require("https"))},109651:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("stream",()=>require("stream"))},794045:function(e){var{g:r,__dirname:t,m:n,e:s}=e;n.exports=e.x("zlib",()=>require("zlib"))},640352:function(e){var{g:r,__dirname:t,m:n,e:s}=e},464002:e=>{"use strict";var{g:r,__dirname:t}=e;e.s({POST:()=>a});var n=e.i(125427),s=e.i(994820),o=e.i(794437);async function a(e){try{let r=await (0,o.verifySession)();if(!r)return n.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{message:t}=await e.json(),a=await (0,s.createClient)(),{data:i,error:u}=await a.from("doctors").select("name, email, clinic_name, phone, quota_used, monthly_quota").eq("id",r.userId).single();if(u||!i)return n.NextResponse.json({success:!1,error:"Doctor not found"},{status:404});let{error:c}=await a.from("contact_requests").insert({doctor_id:r.userId,doctor_name:i.name,doctor_email:i.email,clinic_name:i.clinic_name||"",phone_number:i.phone||"",current_quota_used:i.quota_used||0,monthly_quota:i.monthly_quota||0,request_type:"contact_founder",message:t||"Doctor requested to contact founder",status:"pending"});if(c)return console.error("Error inserting contact request:",c),n.NextResponse.json({success:!1,error:"Failed to send contact request"},{status:500});return n.NextResponse.json({success:!0})}catch(e){return console.error("Contact founder API error:",e),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}},423300:e=>{"use strict";var{g:r,__dirname:t}=e;{e.s({patchFetch:()=>i,routeModule:()=>r,serverHooks:()=>c,workAsyncStorage:()=>t,workUnitAsyncStorage:()=>u});var n=e.i(854885),s=e.i(814689),o=e.i(25402),a=e.i(464002);let r=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/contact-founder/route",pathname:"/api/contact-founder",filename:"route",bundlePath:""},resolvedPagePath:"[project]/src/app/api/contact-founder/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:t,workUnitAsyncStorage:u,serverHooks:c}=r;function i(){return(0,o.patchFetch)({workAsyncStorage:t,workUnitAsyncStorage:u})}}},609329:e=>{var{g:r,__dirname:t}=e;e.v(e=>Promise.resolve().then(()=>e(455544)))},302749:e=>{var{g:r,__dirname:t}=e;e.v(r=>Promise.all(["server/chunks/[root-of-the-server]__76cf8bdc._.js","server/chunks/node_modules_ws_daabdc74._.js"].map(r=>e.l(r))).then(()=>r(947087)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__b98ceff0._.js.map