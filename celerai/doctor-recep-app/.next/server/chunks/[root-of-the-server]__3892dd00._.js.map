{"version": 3, "sources": ["turbopack:///[project]/src/instrumentation.ts"], "sourcesContent": ["import * as Sentry from '@sentry/nextjs';\n\nexport async function register() {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    await import('../sentry.server.config');\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    await import('../sentry.edge.config');\n  }\n}\n\nexport const onRequestError = Sentry.captureRequestError;\n"], "names": [], "mappings": "w9bAEO,eAAe,IAElB,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,CAMJ,4CAEO,IAAM,EAZb,AAY8B,EAZ9B,CAAA,CAAA,QAY8B,mBAA0B,QAA1B"}