module.exports={946900:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let e=["nodebuffer","arraybuffer","fragments"],t="undefined"!=typeof Blob;t&&e.push("blob"),s.exports={BINARY_TYPES:e,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:t,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}}},810834:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{EMPTY_BUFFER:t}=e.r(946900),r=Buffer[Symbol.species];function o(e,t,r,s,i){for(let o=0;o<i;o++)r[s+o]=e[o]^t[3&o]}function n(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new r(e):ArrayBuffer.isView(e)?t=new r(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(s.exports={concat:function(e,s){if(0===e.length)return t;if(1===e.length)return e[0];let i=Buffer.allocUnsafe(s),o=0;for(let t=0;t<e.length;t++){let r=e[t];i.set(r,o),o+=r.length}return o<s?new r(i.buffer,i.byteOffset,o):i},mask:o,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:n},!process.env.WS_NO_BUFFER_UTIL)try{let e=(()=>{let e=Error("Cannot find module 'bufferutil'");throw e.code="MODULE_NOT_FOUND",e})();s.exports.mask=function(t,r,s,i,n){n<48?o(t,r,s,i,n):e.mask(t,r,s,i,n)},s.exports.unmask=function(t,r){t.length<32?n(t,r):e.unmask(t,r)}}catch(e){}}},768352:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let e=Symbol("kDone"),t=Symbol("kRun");s.exports=class{constructor(r){this[e]=()=>{this.pending--,this[t]()},this.concurrency=r||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[t]()}[t](){if(this.pending!==this.concurrency&&this.jobs.length){let t=this.jobs.shift();this.pending++,t(this[e])}}}}},626599:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let t,r=e.r(794045),i=e.r(810834),h=e.r(768352),{kStatusCode:l}=e.r(946900),c=Buffer[Symbol.species],d=Buffer.from([0,0,255,255]),f=Symbol("permessage-deflate"),u=Symbol("total-length"),_=Symbol("callback"),p=Symbol("buffers"),m=Symbol("error");function o(e){this[p].push(e),this[u]+=e.length}function n(e){if(this[u]+=e.length,this[f]._maxPayload<1||this[u]<=this[f]._maxPayload)return void this[p].push(e);this[m]=RangeError("Max payload size exceeded"),this[m].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[m][l]=1009,this.removeListener("data",n),this.reset()}function a(e){if(this[f]._inflate=null,this[m])return void this[_](this[m]);e[l]=1007,this[_](e)}s.exports=class{constructor(e,r,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!r,this._deflate=null,this._inflate=null,this.params=null,t||(t=new h(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[_];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,r,s){t.add(t=>{this._decompress(e,r,(e,r)=>{t(),s(e,r)})})}compress(e,r,s){t.add(t=>{this._compress(e,r,(e,r)=>{t(),s(e,r)})})}_decompress(e,t,s){let o=this._isServer?"client":"server";if(!this._inflate){let e=`${o}_max_window_bits`,t="number"!=typeof this.params[e]?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=r.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[f]=this,this._inflate[u]=0,this._inflate[p]=[],this._inflate.on("error",a),this._inflate.on("data",n)}this._inflate[_]=s,this._inflate.write(e),t&&this._inflate.write(d),this._inflate.flush(()=>{let e=this._inflate[m];if(e){this._inflate.close(),this._inflate=null,s(e);return}let r=i.concat(this._inflate[p],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[p]=[],t&&this.params[`${o}_no_context_takeover`]&&this._inflate.reset()),s(null,r)})}_compress(e,t,s){let n=this._isServer?"server":"client";if(!this._deflate){let e=`${n}_max_window_bits`,t="number"!=typeof this.params[e]?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=r.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[p]=[],this._deflate.on("data",o)}this._deflate[_]=s,this._deflate.write(e),this._deflate.flush(r.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[p],this._deflate[u]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[_]=null,this._deflate[u]=0,this._deflate[p]=[],t&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}}},950163:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{isUtf8:t}=e.r(963018),{hasBlob:r}=e.r(946900);function o(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(s.exports={isBlob:function(e){return r&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:o,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},t)s.exports.isValidUTF8=function(e){return e.length<24?o(e):t(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let e=(()=>{let e=Error("Cannot find module 'utf-8-validate'");throw e.code="MODULE_NOT_FOUND",e})();s.exports.isValidUTF8=function(t){return t.length<32?o(t):e(t)}}catch(e){}}},804461:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{Writable:t}=e.r(109651),r=e.r(626599),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:n,kWebSocket:a}=e.r(946900),{concat:h,toArrayBuffer:l,unmask:c}=e.r(810834),{isValidStatusCode:d,isValidUTF8:f}=e.r(950163),u=Buffer[Symbol.species];s.exports=class extends t{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[a]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new u(t.buffer,t.byteOffset+e,t.length-e),new u(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],s=t.length-e;e>=r.length?t.set(this._buffers.shift(),s):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),s),this._buffers[0]=new u(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0)return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop)this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let s=(64&t[0])==64;if(s&&!this._extensions[r.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=s}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(s)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&c(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[r.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let s;s="nodebuffer"===this._binaryType?h(r,t):"arraybuffer"===this._binaryType?l(h(r,t)):"blob"===this._binaryType?new Blob(r):r,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!0),this._state=0,this.startLoop(e)}))}else{let s=h(r,t);if(!this._skipUTF8Validation&&!f(s))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",s,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let r=e.readUInt16BE(0);if(!d(r))return void t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let s=new u(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!f(s))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",r,s),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,s,i){this._loop=!1,this._errored=!0;let o=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=i,o[n]=s,o}}}},920383:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let t,{Duplex:r}=e.r(109651),{randomFillSync:i}=e.r(329295),a=e.r(626599),{EMPTY_BUFFER:h,kWebSocket:l,NOOP:c}=e.r(946900),{isBlob:d,isValidStatusCode:f}=e.r(950163),{mask:u,toBuffer:_}=e.r(810834),p=Symbol("kByteLength"),m=Buffer.alloc(4),y=8192;class g{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=c,this[l]=void 0}static frame(e,r){let s,o,n=!1,a=2,h=!1;r.mask&&(s=r.maskBuffer||m,r.generateMask?r.generateMask(s):(8192===y&&(void 0===t&&(t=Buffer.alloc(8192)),i(t,0,8192),y=0),s[0]=t[y++],s[1]=t[y++],s[2]=t[y++],s[3]=t[y++]),h=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?o=(!r.mask||h)&&void 0!==r[p]?r[p]:(e=Buffer.from(e)).length:(o=e.length,n=r.mask&&r.readOnly&&!h);let l=o;o>=65536?(a+=8,l=127):o>125&&(a+=2,l=126);let c=Buffer.allocUnsafe(n?o+a:a);return(c[0]=r.fin?128|r.opcode:r.opcode,r.rsv1&&(c[0]|=64),c[1]=l,126===l?c.writeUInt16BE(o,2):127===l&&(c[2]=c[3]=0,c.writeUIntBE(o,4,6)),r.mask)?(c[1]|=128,c[a-4]=s[0],c[a-3]=s[1],c[a-2]=s[2],c[a-1]=s[3],h)?[c,e]:n?(u(e,s,c,a,o),[c]):(u(e,s,e,0,o),[c,e]):[c,e]}close(e,t,r,s){let i;if(void 0===e)i=h;else if("number"==typeof e&&f(e))if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let o={[p]:i.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,i,!1,o,s]):this.sendFrame(g.frame(i,o),s)}ping(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):d(e)?(s=e.size,i=!1):(s=(e=_(e)).length,i=_.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};d(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,r]):this.getBlobData(e,!1,o,r):0!==this._state?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(g.frame(e,o),r)}pong(e,t,r){let s,i;if("string"==typeof e?(s=Buffer.byteLength(e),i=!1):d(e)?(s=e.size,i=!1):(s=(e=_(e)).length,i=_.readOnly),s>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[p]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};d(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,r]):this.getBlobData(e,!1,o,r):0!==this._state?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(g.frame(e,o),r)}send(e,t,r){let s,i,o=this._extensions[a.extensionName],n=t.binary?2:1,h=t.compress;"string"==typeof e?(s=Buffer.byteLength(e),i=!1):d(e)?(s=e.size,i=!1):(s=(e=_(e)).length,i=_.readOnly),this._firstFragment?(this._firstFragment=!1,h&&o&&o.params[o._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(h=s>=o._threshold),this._compress=h):(h=!1,n=0),t.fin&&(this._firstFragment=!0);let l={[p]:s,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:n,readOnly:i,rsv1:h};d(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,l,r]):this.getBlobData(e,this._compress,l,r):0!==this._state?this.enqueue([this.dispatch,e,this._compress,l,r]):this.dispatch(e,this._compress,l,r)}getBlobData(e,t,r,s){this._bufferedBytes+=r[p],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(o,this,e,s);return}this._bufferedBytes-=r[p];let i=_(e);t?this.dispatch(i,t,r,s):(this._state=0,this.sendFrame(g.frame(i,r),s),this.dequeue())}).catch(e=>{process.nextTick(n,this,e,s)})}dispatch(e,t,r,s){if(!t)return void this.sendFrame(g.frame(e,r),s);let i=this._extensions[a.extensionName];this._bufferedBytes+=r[p],this._state=1,i.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed)return void o(this,Error("The socket was closed while data was being compressed"),s);this._bufferedBytes-=r[p],this._state=0,r.readOnly=!1,this.sendFrame(g.frame(t,r),s),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][p],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][p],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function o(e,t,r){"function"==typeof r&&r(t);for(let r=0;r<e._queue.length;r++){let s=e._queue[r],i=s[s.length-1];"function"==typeof i&&i(t)}}function n(e,t,r){o(e,t,r),e.onerror(t)}s.exports=g}},733022:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{kForOnEventAttribute:t,kListener:r}=e.r(946900),i=Symbol("kCode"),n=Symbol("kData"),a=Symbol("kError"),h=Symbol("kMessage"),l=Symbol("kReason"),c=Symbol("kTarget"),d=Symbol("kType"),f=Symbol("kWasClean");class u{constructor(e){this[c]=null,this[d]=e}get target(){return this[c]}get type(){return this[d]}}Object.defineProperty(u.prototype,"target",{enumerable:!0}),Object.defineProperty(u.prototype,"type",{enumerable:!0});class _ extends u{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[l]}get wasClean(){return this[f]}}Object.defineProperty(_.prototype,"code",{enumerable:!0}),Object.defineProperty(_.prototype,"reason",{enumerable:!0}),Object.defineProperty(_.prototype,"wasClean",{enumerable:!0});class p extends u{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[h]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[h]}}Object.defineProperty(p.prototype,"error",{enumerable:!0}),Object.defineProperty(p.prototype,"message",{enumerable:!0});class m extends u{constructor(e,t={}){super(e),this[n]=void 0===t.data?null:t.data}get data(){return this[n]}}function o(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),s.exports={CloseEvent:_,ErrorEvent:p,Event:u,EventTarget:{addEventListener(e,s,i={}){let n;for(let o of this.listeners(e))if(!i[t]&&o[r]===s&&!o[t])return;if("message"===e)n=function(e,t){let r=new m("message",{data:t?e:e.toString()});r[c]=this,o(s,this,r)};else if("close"===e)n=function(e,t){let r=new _("close",{code:e,reason:t.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[c]=this,o(s,this,r)};else if("error"===e)n=function(e){let t=new p("error",{error:e,message:e.message});t[c]=this,o(s,this,t)};else{if("open"!==e)return;n=function(){let e=new u("open");e[c]=this,o(s,this,e)}}n[t]=!!i[t],n[r]=s,i.once?this.once(e,n):this.on(e,n)},removeEventListener(e,s){for(let i of this.listeners(e))if(i[r]===s&&!i[t]){this.removeListener(e,i);break}}},MessageEvent:m}}},910087:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{tokenChars:t}=e.r(950163);function o(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}s.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let r,s,i=Object.create(null),n=Object.create(null),a=!1,h=!1,l=!1,c=-1,d=-1,f=-1,u=0;for(;u<e.length;u++)if(d=e.charCodeAt(u),void 0===r)if(-1===f&&1===t[d])-1===c&&(c=u);else if(0!==u&&(32===d||9===d))-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let t=e.slice(c,f);44===d?(o(i,t,n),n=Object.create(null)):r=t,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);else if(void 0===s)if(-1===f&&1===t[d])-1===c&&(c=u);else if(32===d||9===d)-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u),o(n,e.slice(c,f),!0),44===d&&(o(i,r,n),n=Object.create(null),r=void 0),c=f=-1}else if(61===d&&-1!==c&&-1===f)s=e.slice(c,u),c=f=-1;else throw SyntaxError(`Unexpected character at index ${u}`);else if(h){if(1!==t[d])throw SyntaxError(`Unexpected character at index ${u}`);-1===c?c=u:a||(a=!0),h=!1}else if(l)if(1===t[d])-1===c&&(c=u);else if(34===d&&-1!==c)l=!1,f=u;else if(92===d)h=!0;else throw SyntaxError(`Unexpected character at index ${u}`);else if(34===d&&61===e.charCodeAt(u-1))l=!0;else if(-1===f&&1===t[d])-1===c&&(c=u);else if(-1!==c&&(32===d||9===d))-1===f&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let t=e.slice(c,f);a&&(t=t.replace(/\\/g,""),a=!1),o(n,s,t),44===d&&(o(i,r,n),n=Object.create(null),r=void 0),s=void 0,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);if(-1===c||l||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===f&&(f=u);let _=e.slice(c,f);return void 0===r?o(i,_,n):(void 0===s?o(n,_,!0):a?o(n,s,_.replace(/\\/g,"")):o(n,s,_),o(i,r,n)),i}}}},138809:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let t=e.r(420467),r=e.r(348388),i=e.r(62445),x=e.r(629918),w=e.r(655804),{randomBytes:O,createHash:T}=e.r(329295),{Duplex:C,Readable:N}=e.r(109651),{URL:L}=e.r(771485),P=e.r(626599),B=e.r(804461),R=e.r(920383),{isBlob:U}=e.r(950163),{BINARY_TYPES:I,EMPTY_BUFFER:W,GUID:D,kForOnEventAttribute:M,kListener:A,kStatusCode:F,kWebSocket:$,NOOP:j}=e.r(946900),{EventTarget:{addEventListener:G,removeEventListener:V}}=e.r(733022),{format:q,parse:z}=e.r(910087),{toBuffer:H}=e.r(810834),X=Symbol("kAborted"),K=[8,13],Z=["CONNECTING","OPEN","CLOSING","CLOSED"],Y=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class J extends t{constructor(e,t,s){super(),this._binaryType=I[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=W,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=J.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,l,c){let d,f,u,_,p={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:K[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...c,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=p.autoPong,!K.includes(p.protocolVersion))throw RangeError(`Unsupported protocol version: ${p.protocolVersion} (supported versions: ${K.join(", ")})`);if(s instanceof L)d=s;else try{d=new L(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===d.protocol?d.protocol="ws:":"https:"===d.protocol&&(d.protocol="wss:"),t._url=d.href;let m="wss:"===d.protocol,y="ws+unix:"===d.protocol;if("ws:"===d.protocol||m||y?y&&!d.pathname?f="The URL's pathname is empty":d.hash&&(f="The URL contains a fragment identifier"):f='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',f){let e=SyntaxError(f);if(0!==t._redirects)return void o(t,e);throw e}let g=m?443:80,v=O(16).toString("base64"),b=m?r.request:i.request,S=new Set;if(p.createConnection=p.createConnection||(m?a:n),p.defaultPort=p.defaultPort||g,p.port=d.port||g,p.host=d.hostname.startsWith("[")?d.hostname.slice(1,-1):d.hostname,p.headers={...p.headers,"Sec-WebSocket-Version":p.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},p.path=d.pathname+d.search,p.timeout=p.handshakeTimeout,p.perMessageDeflate&&(u=new P(!0!==p.perMessageDeflate?p.perMessageDeflate:{},!1,p.maxPayload),p.headers["Sec-WebSocket-Extensions"]=q({[P.extensionName]:u.offer()})),l.length){for(let e of l){if("string"!=typeof e||!Y.test(e)||S.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");S.add(e)}p.headers["Sec-WebSocket-Protocol"]=l.join(",")}if(p.origin&&(p.protocolVersion<13?p.headers["Sec-WebSocket-Origin"]=p.origin:p.headers.Origin=p.origin),(d.username||d.password)&&(p.auth=`${d.username}:${d.password}`),y){let e=p.path.split(":");p.socketPath=e[0],p.path=e[1]}if(p.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=m,t._originalHostOrSocketPath=y?p.socketPath:d.host;let e=c&&c.headers;if(c={...c,headers:{}},e)for(let[t,r]of Object.entries(e))c.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&p.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&d.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete p.headers.authorization,delete p.headers.cookie,e||delete p.headers.host,p.auth=void 0)}p.auth&&!c.headers.authorization&&(c.headers.authorization="Basic "+Buffer.from(p.auth).toString("base64")),_=t._req=b(p),t._redirects&&t.emit("redirect",t.url,_)}else _=t._req=b(p);p.timeout&&_.on("timeout",()=>{h(t,_,"Opening handshake has timed out")}),_.on("error",e=>{null===_||_[X]||(_=t._req=null,o(t,e))}),_.on("response",r=>{let i=r.headers.location,n=r.statusCode;if(i&&p.followRedirects&&n>=300&&n<400){let r;if(++t._redirects>p.maxRedirects)return void h(t,_,"Maximum redirects exceeded");_.abort();try{r=new L(i,s)}catch(e){o(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,r,l,c)}else t.emit("unexpected-response",_,r)||h(t,_,`Unexpected server response: ${r.statusCode}`)}),_.on("upgrade",(e,r,s)=>{let i;if(t.emit("upgrade",e),t.readyState!==J.CONNECTING)return;_=t._req=null;let o=e.headers.upgrade;if(void 0===o||"websocket"!==o.toLowerCase())return void h(t,r,"Invalid Upgrade header");let n=T("sha1").update(v+D).digest("base64");if(e.headers["sec-websocket-accept"]!==n)return void h(t,r,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?S.size?S.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":S.size&&(i="Server sent no subprotocol"),i)return void h(t,r,i);a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!u)return void h(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=z(l)}catch(e){h(t,r,"Invalid Sec-WebSocket-Extensions header");return}let s=Object.keys(e);if(1!==s.length||s[0]!==P.extensionName)return void h(t,r,"Server indicated an extension that was not requested");try{u.accept(e[P.extensionName])}catch(e){h(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[P.extensionName]=u}t.setSocket(r,s,{allowSynchronousEvents:p.allowSynchronousEvents,generateMask:p.generateMask,maxPayload:p.maxPayload,skipUTF8Validation:p.skipUTF8Validation})}),p.finishRequest?p.finishRequest(_,t):_.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){I.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let s=new B({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation}),i=new R(e,this._extensions,r.generateMask);this._receiver=s,this._sender=i,this._socket=e,s[$]=this,i[$]=this,e[$]=this,s.on("conclude",c),s.on("drain",d),s.on("error",f),s.on("message",_),s.on("ping",p),s.on("pong",m),i.onerror=g,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",b),e.on("data",S),e.on("end",E),e.on("error",k),this._readyState=J.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=J.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[P.extensionName]&&this._extensions[P.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=J.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==J.CLOSED){if(this.readyState===J.CONNECTING)return void h(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===J.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=J.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),v(this)}}pause(){this.readyState!==J.CONNECTING&&this.readyState!==J.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===J.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==J.OPEN)return void l(this,e,r);void 0===t&&(t=!this._isServer),this._sender.ping(e||W,t,r)}pong(e,t,r){if(this.readyState===J.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==J.OPEN)return void l(this,e,r);void 0===t&&(t=!this._isServer),this._sender.pong(e||W,t,r)}resume(){this.readyState!==J.CONNECTING&&this.readyState!==J.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===J.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==J.OPEN)return void l(this,e,r);let s={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[P.extensionName]||(s.compress=!1),this._sender.send(e||W,s,r)}terminate(){if(this.readyState!==J.CLOSED){if(this.readyState===J.CONNECTING)return void h(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=J.CLOSING,this._socket.destroy())}}}function o(e,t){e._readyState=J.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function n(e){return e.path=e.socketPath,x.connect(e)}function a(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=x.isIP(e.host)?"":e.host),w.connect(e)}function h(e,t,r){e._readyState=J.CLOSING;let s=Error(r);Error.captureStackTrace(s,h),t.setHeader?(t[X]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(o,e,s)):(t.destroy(s),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function l(e,t,r){if(t){let r=U(t)?t.size:H(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${Z[e.readyState]})`);process.nextTick(r,t)}}function c(e,t){let r=this[$];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[$]&&(r._socket.removeListener("data",S),process.nextTick(y,r._socket),1005===e?r.close():r.close(e,t))}function d(){let e=this[$];e.isPaused||e._socket.resume()}function f(e){let t=this[$];void 0!==t._socket[$]&&(t._socket.removeListener("data",S),process.nextTick(y,t._socket),t.close(e[F])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function u(){this[$].emitClose()}function _(e,t){this[$].emit("message",e,t)}function p(e){let t=this[$];t._autoPong&&t.pong(e,!this._isServer,j),t.emit("ping",e)}function m(e){this[$].emit("pong",e)}function y(e){e.resume()}function g(e){let t=this[$];t.readyState!==J.CLOSED&&(t.readyState===J.OPEN&&(t._readyState=J.CLOSING,v(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function v(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function b(){let e,t=this[$];this.removeListener("close",b),this.removeListener("data",S),this.removeListener("end",E),t._readyState=J.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[$]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",u),t._receiver.on("finish",u))}function S(e){this[$]._receiver.write(e)||this.pause()}function E(){let e=this[$];e._readyState=J.CLOSING,e._receiver.end(),this.end()}function k(){let e=this[$];this.removeListener("error",k),this.on("error",j),e&&(e._readyState=J.CLOSING,this.destroy())}Object.defineProperty(J,"CONNECTING",{enumerable:!0,value:Z.indexOf("CONNECTING")}),Object.defineProperty(J.prototype,"CONNECTING",{enumerable:!0,value:Z.indexOf("CONNECTING")}),Object.defineProperty(J,"OPEN",{enumerable:!0,value:Z.indexOf("OPEN")}),Object.defineProperty(J.prototype,"OPEN",{enumerable:!0,value:Z.indexOf("OPEN")}),Object.defineProperty(J,"CLOSING",{enumerable:!0,value:Z.indexOf("CLOSING")}),Object.defineProperty(J.prototype,"CLOSING",{enumerable:!0,value:Z.indexOf("CLOSING")}),Object.defineProperty(J,"CLOSED",{enumerable:!0,value:Z.indexOf("CLOSED")}),Object.defineProperty(J.prototype,"CLOSED",{enumerable:!0,value:Z.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(J.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(J.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[M])return t[A];return null},set(t){for(let t of this.listeners(e))if(t[M]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[M]:!0})}})}),J.prototype.addEventListener=G,J.prototype.removeEventListener=V,s.exports=J}},21237:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";e.r(138809);let{Duplex:t}=e.r(109651);function o(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function a(e){this.removeListener("error",a),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}s.exports=function(e,r){let s=!0,i=new t({...r,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,r){let s=!r&&i._readableState.objectMode?t.toString():t;i.push(s)||e.pause()}),e.once("error",function(e){i.destroyed||(s=!1,i.destroy(e))}),e.once("close",function(){i.destroyed||i.push(null)}),i._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(o,i);return}let n=!1;e.once("error",function(e){n=!0,r(e)}),e.once("close",function(){n||r(t),process.nextTick(o,i)}),s&&e.terminate()},i._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){i._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),i._readableState.endEmitted&&i.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},i._read=function(){e.isPaused&&e.resume()},i._write=function(t,r,s){if(e.readyState===e.CONNECTING)return void e.once("open",function(){i._write(t,r,s)});e.send(t,s)},i.on("end",n),i.on("error",a),i}}},969441:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let{tokenChars:t}=e.r(950163);s.exports={parse:function(e){let r=new Set,s=-1,i=-1,o=0;for(;o<e.length;o++){let n=e.charCodeAt(o);if(-1===i&&1===t[n])-1===s&&(s=o);else if(0!==o&&(32===n||9===n))-1===i&&-1!==s&&(i=o);else if(44===n){if(-1===s)throw SyntaxError(`Unexpected character at index ${o}`);-1===i&&(i=o);let t=e.slice(s,i);if(r.has(t))throw SyntaxError(`The "${t}" subprotocol is duplicated`);r.add(t),s=i=-1}else throw SyntaxError(`Unexpected character at index ${o}`)}if(-1===s||-1!==i)throw SyntaxError("Unexpected end of input");let n=e.slice(s,o);if(r.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);return r.add(n),r}}}},520782:function(e){var{g:t,__dirname:r,m:s,e:i}=e;{"use strict";let t=e.r(420467),r=e.r(62445),{Duplex:i}=e.r(109651),{createHash:l}=e.r(329295),c=e.r(910087),d=e.r(626599),f=e.r(969441),u=e.r(138809),{GUID:_,kWebSocket:p}=e.r(946900),m=/^[+/0-9A-Za-z]{22}==$/;function o(e){e._state=2,e.emit("close")}function n(){this.destroy()}function a(e,t,s,i){s=s||r.STATUS_CODES[t],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...i},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${r.STATUS_CODES[t]}\r
`+Object.keys(i).map(e=>`${e}: ${i[e]}`).join("\r\n")+"\r\n\r\n"+s)}function h(e,t,r,s,i){if(e.listenerCount("wsClientError")){let s=Error(i);Error.captureStackTrace(s,h),e.emit("wsClientError",s,r,t)}else a(r,s,i)}s.exports=class extends t{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:u,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=r.createServer((e,t)=>{let s=r.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,s)=>{this.handleUpgrade(t,r,s,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(o,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(o,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{o(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,s){t.on("error",n);let i=e.headers["sec-websocket-key"],o=e.headers.upgrade,l=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void h(this,e,t,405,"Invalid HTTP method");if(void 0===o||"websocket"!==o.toLowerCase())return void h(this,e,t,400,"Invalid Upgrade header");if(void 0===i||!m.test(i))return void h(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==l&&13!==l)return void h(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(e))return void a(t,400);let u=e.headers["sec-websocket-protocol"],_=new Set;if(void 0!==u)try{_=f.parse(u)}catch(r){h(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let p=e.headers["sec-websocket-extensions"],y={};if(this.options.perMessageDeflate&&void 0!==p){let r=new d(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=c.parse(p);e[d.extensionName]&&(r.accept(e[d.extensionName]),y[d.extensionName]=r)}catch(r){h(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let o={origin:e.headers[`${8===l?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(o,(o,n,h,l)=>{if(!o)return a(t,n||401,h,l);this.completeUpgrade(y,i,_,e,t,r,s)});if(!this.options.verifyClient(o))return a(t,401)}this.completeUpgrade(y,i,_,e,t,r,s)}completeUpgrade(e,t,r,s,i,h,f){if(!i.readable||!i.writable)return i.destroy();if(i[p])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return a(i,503);let u=l("sha1").update(t+_).digest("base64"),m=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${u}`],y=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,s):r.values().next().value;e&&(m.push(`Sec-WebSocket-Protocol: ${e}`),y._protocol=e)}if(e[d.extensionName]){let t=e[d.extensionName].params,r=c.format({[d.extensionName]:[t]});m.push(`Sec-WebSocket-Extensions: ${r}`),y._extensions=e}this.emit("headers",m,s),i.write(m.concat("\r\n").join("\r\n")),i.removeListener("error",n),y.setSocket(i,h,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(y),y.on("close",()=>{this.clients.delete(y),this._shouldEmitClose&&!this.clients.size&&process.nextTick(o,this)})),f(y,s)}}}},417453:e=>{"use strict";var{g:t,__dirname:r}=e;{e.s({default:()=>t}),e.i(21237),e.i(804461),e.i(920383);var s=e.i(138809);e.i(520782);let t=s.default}},327746:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({}),e.i(21237),e.i(804461),e.i(920383),e.i(138809),e.i(520782),e.i(417453)},10450:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({Receiver:()=>i.default,Sender:()=>o.default,WebSocket:()=>n.default,WebSocketServer:()=>a.default,createWebSocketStream:()=>s.default,default:()=>h.default});var s=e.i(21237),i=e.i(804461),o=e.i(920383),n=e.i(138809),a=e.i(520782),h=e.i(417453)},947087:e=>{"use strict";var{g:t,__dirname:r}=e;e.s({Receiver:()=>s.Receiver,Sender:()=>s.Sender,WebSocket:()=>s.WebSocket,WebSocketServer:()=>s.WebSocketServer,createWebSocketStream:()=>s.createWebSocketStream,default:()=>s.default}),e.i(327746);var s=e.i(10450)}};

//# sourceMappingURL=node_modules_ws_daabdc74._.js.map