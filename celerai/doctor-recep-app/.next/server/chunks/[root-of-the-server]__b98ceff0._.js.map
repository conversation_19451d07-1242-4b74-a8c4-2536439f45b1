{"version": 3, "sources": ["turbopack:///[project]/src/app/api/contact-founder/route.ts", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return NextResponse.json(\n        { success: false, error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const body = await request.json()\n    const { message } = body\n\n    const supabase = await createClient()\n\n    // Get doctor info and current quota\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select('name, email, clinic_name, phone, quota_used, monthly_quota')\n      .eq('id', session.userId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return NextResponse.json(\n        { success: false, error: 'Doctor not found' },\n        { status: 404 }\n      )\n    }\n\n    // Insert contact request into database\n    const { error } = await supabase\n      .from('contact_requests')\n      .insert({\n        doctor_id: session.userId,\n        doctor_name: doctor.name,\n        doctor_email: doctor.email,\n        clinic_name: doctor.clinic_name || '',\n        phone_number: doctor.phone || '',\n        current_quota_used: doctor.quota_used || 0,\n        monthly_quota: doctor.monthly_quota || 0,\n        request_type: 'contact_founder',\n        message: message || 'Doctor requested to contact founder',\n        status: 'pending'\n      })\n\n    if (error) {\n      console.error('Error inserting contact request:', error)\n      return NextResponse.json(\n        { success: false, error: 'Failed to send contact request' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('Contact founder API error:', error)\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}", "import {\n  AppRouteRouteModule,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks"], "mappings": "6tDAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAY,AAAZ,IACtB,GAAI,CAAC,EACH,OADY,AACL,EAAA,KAFa,OAED,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,CADpB,aACmC,EACxC,CAAE,OAAQ,GAAI,GAKlB,GAAM,SAAE,CAAO,CAAE,CADJ,EACO,IADD,EAAQ,IAAI,GAGzB,EAAW,MAAM,GAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAM,CAAE,MAAO,CAAW,CAAE,CAAG,IAHtB,EAG4B,EAChD,IAAI,CAAC,WACL,MAAM,CAAC,8DACP,EAAE,CAAC,KAAM,EAAQ,MAAM,EACvB,MAAM,GAET,GAAI,GAAe,CAAC,EAClB,MAD0B,CACnB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,CADpB,iBACuC,EAC5C,CAAE,OAAQ,GAAI,GAKlB,GAAM,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,IAAI,CAAC,oBACL,MAAM,CAAC,CACN,UAAW,EAAQ,MAAM,CACzB,YAAa,EAAO,IAAI,CACxB,aAAc,EAAO,KAAK,CAC1B,YAAa,EAAO,WAAW,EAAI,GACnC,aAAc,EAAO,KAAK,EAAI,GAC9B,mBAAoB,EAAO,UAAU,EAAI,EACzC,cAAe,EAAO,aAAa,EAAI,EACvC,aAAc,kBACd,QAAS,GAAW,sCACpB,OAAQ,SACV,GAEF,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,CADpB,+BACqD,EAC1D,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,SAAS,CAAK,EAC3C,CAAE,KADO,CACA,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,CADpB,sBAC4C,EACjD,CAAE,OAAQ,GAAI,EAElB,CACF,iKCjEA,IAAA,EAGO,EAAA,CAFLA,AAEK,CAAA,QACP,EAA0B,EAAyB,CAA1CC,AAA0C,CAAA,EAAA,EAH9B,GAEwC,CAC3C,AAClB,EAA0C,EAFnC,AAEmC,CAAA,AAAjCC,CAAiC,EADhB,EAC8C,GAExE,EAAwC,EAAA,CAAA,CAFjBC,AAEiB,EAA5BC,MAWZ,GAbkC,CAa5BC,EAAc,EAXM,EAWN,CAbsB,CAalBL,WAXgB,QAWhBA,CAAoB,CAC1CM,WAAY,CACVC,KAAMN,EAAAA,SAAAA,CAAUO,SAAS,CACzBC,KAAM,6BACNC,SAAU,uBACVC,SAAU,QACVC,WAAY,EACd,EACAC,iBAAkB,iDAClBC,iBAXF,CAA0B,WAYxBV,CACF,GAKM,kBAAEW,CAAgB,sBAAEC,CAAoB,CAAEC,aAAW,CAAE,CAAGZ,EAEhE,SAASH,IACP,MAAA,CAAA,EAAA,EAAOC,UAAAA,EAAY,kBACjBY,EACAC,sBACF,EACF", "ignoreList": [1]}