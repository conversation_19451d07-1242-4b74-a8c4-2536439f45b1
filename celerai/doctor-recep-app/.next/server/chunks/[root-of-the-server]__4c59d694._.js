module.exports={929549:function(e){var{g:r,__dirname:t,m:n,e:a}=e;n.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},983943:function(e){var{g:r,__dirname:t,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},86103:function(e){var{g:r,__dirname:t,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},945935:function(e){var{g:r,__dirname:t,m:n,e:a}=e;n.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},348629:function(e){var{g:r,__dirname:t,m:n,e:a}=e;n.exports=e.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},640017:function(e){var{g:r,__dirname:t,m:n,e:a}=e},480276:e=>{"use strict";var{g:r,__dirname:t}=e;{e.s({GET:()=>n,dynamic:()=>r}),e.i(125427);let r="force-dynamic";class t extends Error{constructor(e){super(e),this.name="SentryExampleAPIError"}}function n(){throw new t("This error is raised on the backend called by the example page.")}}},772651:e=>{"use strict";var{g:r,__dirname:t}=e;{e.s({patchFetch:()=>i,routeModule:()=>r,serverHooks:()=>u,workAsyncStorage:()=>t,workUnitAsyncStorage:()=>p});var n=e.i(854885),a=e.i(814689),s=e.i(25402),o=e.i(480276);let r=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/sentry-example-api/route",pathname:"/api/sentry-example-api",filename:"route",bundlePath:""},resolvedPagePath:"[project]/src/app/api/sentry-example-api/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:t,workUnitAsyncStorage:p,serverHooks:u}=r;function i(){return(0,s.patchFetch)({workAsyncStorage:t,workUnitAsyncStorage:p})}}}};

//# sourceMappingURL=%5Broot-of-the-server%5D__4c59d694._.js.map