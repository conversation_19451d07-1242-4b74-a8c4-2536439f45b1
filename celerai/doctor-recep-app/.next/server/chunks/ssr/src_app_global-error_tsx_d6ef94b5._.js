module.exports={235147:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/global-error.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/global-error.tsx <module evaluation>","default")}},356616:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/global-error.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/global-error.tsx","default")}},919184:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(235147);var d=a.i(356616);a.n(d)}};

//# sourceMappingURL=src_app_global-error_tsx_d6ef94b5._.js.map