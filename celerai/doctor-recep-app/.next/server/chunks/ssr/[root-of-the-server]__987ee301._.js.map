{"version": 3, "sources": ["turbopack:///[project]/src/lib/storage.ts"], "sourcesContent": ["import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'\n\n// Storage configuration - Migrated to Cloudflare R2\nexport const STORAGE_CONFIG = {\n  // R2 Configuration\n  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',\n  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',\n\n  // Folder prefixes (replaces separate buckets)\n  AUDIO_PREFIX: 'consultation-audio',\n  IMAGE_PREFIX: 'consultation-images',\n\n  // File limits\n  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file\n  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation\n  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],\n  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],\n  RETENTION_DAYS: 30\n}\n\n// R2 Client configuration\nconst createR2Client = () => {\n  return new S3Client({\n    region: 'auto',\n    endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,\n    credentials: {\n      accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',\n      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************',\n    },\n  })\n}\n\n// File validation\nexport function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {\n  // Check file size\n  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {\n    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }\n  }\n\n  // Check file type\n  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES\n  if (!allowedTypes.includes(file.type)) {\n    return { valid: false, error: `File type ${file.type} is not allowed` }\n  }\n\n  return { valid: true }\n}\n\n// Generate storage path with folder prefix for R2\nexport function generateStoragePath(\n  doctorId: string,\n  consultationId: string,\n  fileName: string,\n  type: 'audio' | 'image'\n): string {\n  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')\n  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`\n}\n\n// Upload file to Cloudflare R2\nexport async function uploadFile(\n  file: File,\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; url?: string; error?: string }> {\n  try {\n    // Validate file\n    const validation = validateFile(file, type)\n    if (!validation.valid) {\n      return { success: false, error: validation.error }\n    }\n\n    const r2Client = createR2Client()\n    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)\n\n    // Convert file to buffer\n    const fileBuffer = await file.arrayBuffer()\n\n    // Upload to R2 - preserve exact Content-Type from file\n    const uploadCommand = new PutObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n      Body: new Uint8Array(fileBuffer),\n      ContentType: file.type, // Use original file.type to preserve codec info\n      CacheControl: 'public, max-age=3600',\n    })\n\n    await r2Client.send(uploadCommand)\n\n    // Generate public URL\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    return { success: true, url: publicUrl }\n  } catch (error) {\n    console.error('R2 upload error:', error)\n    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\n// Upload multiple files\nexport async function uploadMultipleFiles(\n  files: File[],\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {\n  const results = await Promise.all(\n    files.map(file => uploadFile(file, doctorId, consultationId, type))\n  )\n\n  const successful = results.filter(r => r.success)\n  const failed = results.filter(r => !r.success)\n\n  if (failed.length > 0) {\n    return {\n      success: false,\n      errors: failed.map(f => f.error || 'Unknown error')\n    }\n  }\n\n  return {\n    success: true,\n    urls: successful.map(s => s.url!).filter(Boolean)\n  }\n}\n\n// Delete file from R2 storage\nexport async function deleteFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const r2Client = createR2Client()\n\n    const deleteCommand = new DeleteObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n    })\n\n    await r2Client.send(deleteCommand)\n\n    return { success: true }\n  } catch (error) {\n    console.error('R2 delete error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }\n  }\n}\n\n// Extract file path from R2 URL\nexport function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {\n  try {\n    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n    const prefixPath = `/${prefix}/`\n    const index = url.indexOf(prefixPath)\n\n    if (index === -1) return null\n\n    return url.substring(url.indexOf(prefix))\n  } catch {\n    return null\n  }\n}\n\n// Download file from R2 storage\nexport async function downloadFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; data?: Blob; error?: string }> {\n  try {\n    // For public files, we can fetch directly from the custom domain\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    const response = await fetch(publicUrl)\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.blob()\n    return { success: true, data }\n  } catch (error) {\n    console.error('R2 download error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }\n  }\n}\n\n// Calculate total file size\nexport function calculateTotalFileSize(files: File[]): number {\n  return files.reduce((total, file) => total + file.size, 0)\n}\n\n// Validate total consultation file size\nexport function validateTotalSize(files: File[]): { valid: boolean; error?: string } {\n  const totalSize = calculateTotalFileSize(files)\n  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {\n    return {\n      valid: false,\n      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`\n    }\n  }\n  return { valid: true }\n}\n"], "names": [], "mappings": "2hBAAA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QAGO,IAAM,EAAiB,CAE5B,YAAa,QAAQ,GAAG,CAAC,cAAc,EAAI,kBAC3C,WAAY,QAAQ,GAAG,CAAC,aAAa,EAAI,8BAGzC,aAAc,qBACd,aAAc,sBAGd,cAAe,MAAM,IACrB,GAD4B,YACZ,MAAM,IACtB,GAD6B,iBACR,CAAC,aAAc,YAAa,YAAa,YAAa,aAAc,YAAa,YAAY,CAClH,oBAAqB,CAAC,aAAc,YAAa,YAAa,aAAc,aAAa,CACzF,eAAgB,EAClB,EAGM,EAAiB,IACd,IAAI,EAAA,QAAQ,CAAC,CAClB,OAAQ,OACR,SAAU,CAAC,GAFF,KAEU,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAI,mCAAmC,yBAAyB,CAAC,CAC/G,YAAa,CACX,YAAa,QAAQ,GAAG,CAAC,gBAAgB,EAAI,mCAC7C,gBAAiB,QAAQ,GAAG,CAAC,oBAAoB,EAAI,kEACvD,CACF,GAIK,SAAS,EAAa,CAAU,CAAE,CAAuB,SAE9D,AAAI,EAAK,IAAI,CAAG,EAAe,aAAa,CACnC,CADqC,AACnC,OAAO,EAAO,MAAO,CAAC,kBAAkB,EAAE,EAAe,aAAa,CAAG,KAAO,KAAK,QAAQ,CAAC,AAAC,EAKrG,CADgB,AAAS,YAAU,EAAe,mBAAmB,CAAG,EAAe,mBAAA,AAAmB,EAC7F,QAAQ,CAAC,EAAK,IAAI,EAI7B,CAAE,AAJ8B,OAIvB,CAAK,EAHZ,CAAE,OAAO,EAAO,MAAO,CAAC,UAAU,EAAE,EAAK,IAAI,CAAC,eAAe,CAAC,AAAC,CAI1E,CAGO,SAAS,EACd,CAAgB,CAChB,CAAsB,CACtB,CAAgB,CAChB,CAAuB,EAEvB,IAAM,EAAoB,EAAS,OAAO,CAAC,kBAAmB,KACxD,EAAkB,UAAT,EAAmB,EAAe,YAAY,CAAG,EAAe,YAAY,CAC3F,MAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAS,CAAC,EAAE,EAAe,CAAC,EAAE,EAAA,CACpD,AADuE,CAIhE,eAAe,EACpB,CAAU,CACV,CAAgB,CAChB,CAAsB,CACtB,CAAuB,EAEvB,GAAI,CAEF,IAAM,EAAa,EAAa,EAAM,GACtC,GAAI,CAAC,EAAW,KAAK,CACnB,CADqB,KACd,CAAE,SAAS,EAAO,MAAO,EAAW,KAAK,AAAC,EAGnD,IAAM,EAAW,IACX,EAAW,EAAoB,EAAU,EAAgB,EAAK,IAAI,CAAE,GAGpE,EAAa,MAAM,EAAK,WAAW,GAGnC,EAAgB,IAAI,EAAA,gBAAgB,CAAC,CACzC,OAAQ,EAAe,UADC,CACU,CAClC,IAAK,EACL,KAAM,IAAI,WAAW,GACrB,YAAa,EAAK,IAAI,CACtB,aAAc,sBAChB,EAEA,OAAM,EAAS,IAAI,CAAC,GAGpB,IAAM,EAAY,CAAA,EAAG,EAAe,UAAU,CAAC,CAAC,EAAE,EAAA,CAAU,CAE5D,MAAO,CAAE,SAAS,EAAM,IAAK,CAAU,CACzC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mBAAoB,GAC3B,CAAE,SAAS,EAAO,MAAO,CAAC,eAAe,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,gBAAA,CAAiB,AAAC,CAC/G,CACF,CAGO,eAAe,EACpB,CAAa,CACb,CAAgB,CAChB,CAAsB,CACtB,CAAuB,EAEvB,IAAM,EAAU,MAAM,QAAQ,GAAG,CAC/B,EAAM,GAAG,CAAC,GAAQ,EAAW,EAAM,EAAU,EAAgB,KAGzD,EAAa,EAAQ,MAAM,CAAC,GAAK,EAAE,OAAO,EAC1C,EAAS,EAAQ,MAAM,CAAC,GAAK,CAAC,EAAE,OAAO,SAE7C,AAAI,EAAO,MAAM,CAAG,EACX,CADc,AAEnB,SAAS,EACT,OAAQ,EAAO,GAAG,CAAC,GAAK,EAAE,KAAK,EAAI,gBACrC,EAGK,CACL,QAAS,GACT,KAAM,EAAW,GAAG,CAAC,GAAK,EAAE,GAAG,EAAG,MAAM,CAAC,QAC3C,CACF,CAGO,eAAe,EACpB,CAAgB,CAChB,CAAwB,EAExB,GAAI,CACF,IAAM,EAAW,IAEX,EAAgB,IAAI,EAAA,mBAAmB,CAAC,CAC5C,OAAQ,EAAe,OADC,IACU,CAClC,IAAK,CACP,GAIA,OAFA,MAAM,EAAS,IAAI,CAAC,GAEb,CAAE,SAAS,CAAK,CACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mBAAoB,GAC3B,CAAE,SAAS,EAAO,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,eAAgB,CAC3F,CACF,CAGO,SAAS,EAAuB,CAAW,CAAE,CAAuB,EACzE,GAAI,CACF,IAAM,EAAkB,UAAT,EAAmB,EAAe,YAAY,CAAG,EAAe,YAAY,CACrF,EAAa,CAAC,CAAC,EAAE,EAAO,CAAC,CAAC,CAC1B,EAAQ,EAAI,OAAO,CAAC,GAE1B,GAAc,CAAC,IAAX,EAAc,OAAO,KAEzB,OAAO,EAAI,SAAS,CAAC,EAAI,OAAO,CAAC,GACnC,CAAE,KAAM,CACN,OAAO,IACT,CACF,CAGO,eAAe,EACpB,CAAgB,CAChB,CAAwB,EAExB,GAAI,CAEF,IAAM,EAAY,CAAA,EAAG,EAAe,UAAU,CAAC,CAAC,EAAE,EAAA,CAAU,CAEtD,EAAW,MAAM,MAAM,GAC7B,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,CAAC,KAAK,EAAE,EAAS,MAAM,CAAC,EAAE,EAAE,EAAS,UAAU,CAAA,CAAE,EAGnE,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,MAAO,CAAE,SAAS,OAAM,CAAK,CAC/B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,CAAE,QAAS,GAAO,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,iBAAkB,CAC7F,CACF,CAGO,SAAS,EAAuB,CAAa,EAClD,OAAO,EAAM,MAAM,CAAC,CAAC,EAAO,IAAS,EAAQ,EAAK,IAAI,CAAE,EAC1D,CAGO,SAAS,EAAkB,CAAa,SAC3B,AAClB,AAAI,EADqC,GACzB,EAAe,cAAc,CACpC,CADsC,AAE3C,OAAO,EACP,MAAO,CAAC,wBAAwB,EAAE,EAAe,cAAc,CAAG,KAAO,KAAK,QAAQ,CAAC,AACzF,EAEK,CAAE,OAAO,CAAK,CACvB"}