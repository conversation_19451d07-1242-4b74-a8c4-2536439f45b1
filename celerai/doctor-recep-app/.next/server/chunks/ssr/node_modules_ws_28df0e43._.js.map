{"version": 3, "sources": ["turbopack:///[project]/node_modules/ws/lib/constants.js", "turbopack:///[project]/node_modules/ws/lib/buffer-util.js", "turbopack:///[project]/node_modules/ws/lib/limiter.js", "turbopack:///[project]/node_modules/ws/lib/permessage-deflate.js", "turbopack:///[project]/node_modules/ws/lib/validation.js", "turbopack:///[project]/node_modules/ws/lib/receiver.js", "turbopack:///[project]/node_modules/ws/lib/sender.js", "turbopack:///[project]/node_modules/ws/lib/event-target.js", "turbopack:///[project]/node_modules/ws/lib/extension.js", "turbopack:///[project]/node_modules/ws/lib/websocket.js", "turbopack:///[project]/node_modules/ws/lib/stream.js", "turbopack:///[project]/node_modules/ws/lib/subprotocol.js", "turbopack:///[project]/node_modules/ws/lib/websocket-server.js", "turbopack:///[project]/node_modules/ws/wrapper.mjs"], "sourcesContent": ["'use strict';\n\nconst BINARY_TYPES = ['nodebuffer', 'arraybuffer', 'fragments'];\nconst hasBlob = typeof Blob !== 'undefined';\n\nif (hasBlob) BINARY_TYPES.push('blob');\n\nmodule.exports = {\n  BINARY_TYPES,\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: '258EAFA5-E914-47DA-95CA-C5AB0DC85B11',\n  hasBlob,\n  kForOnEventAttribute: Symbol('kIsForOnEventAttribute'),\n  kListener: Symbol('kListener'),\n  kStatusCode: Symbol('status-code'),\n  kWebSocket: Symbol('websocket'),\n  NOOP: () => {}\n};\n", "'use strict';\n\nconst { EMPTY_BUFFER } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\n\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {<PERSON>uffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */\nfunction concat(list, totalLength) {\n  if (list.length === 0) return EMPTY_BUFFER;\n  if (list.length === 1) return list[0];\n\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n\n  if (offset < totalLength) {\n    return new FastBuffer(target.buffer, target.byteOffset, offset);\n  }\n\n  return target;\n}\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {<PERSON>uffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nfunction _mask(source, mask, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n}\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nfunction _unmask(buffer, mask) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n}\n\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */\nfunction toArrayBuffer(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */\nfunction toBuffer(data) {\n  toBuffer.readOnly = true;\n\n  if (Buffer.isBuffer(data)) return data;\n\n  let buf;\n\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer.readOnly = false;\n  }\n\n  return buf;\n}\n\nmodule.exports = {\n  concat,\n  mask: _mask,\n  toArrayBuffer,\n  toBuffer,\n  unmask: _unmask\n};\n\n/* istanbul ignore else  */\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil = require('bufferutil');\n\n    module.exports.mask = function (source, mask, output, offset, length) {\n      if (length < 48) _mask(source, mask, output, offset, length);\n      else bufferUtil.mask(source, mask, output, offset, length);\n    };\n\n    module.exports.unmask = function (buffer, mask) {\n      if (buffer.length < 32) _unmask(buffer, mask);\n      else bufferUtil.unmask(buffer, mask);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n", "'use strict';\n\nconst kDone = Symbol('kDone');\nconst kRun = Symbol('kRun');\n\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */\nclass Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency) return;\n\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n}\n\nmodule.exports = Limiter;\n", "'use strict';\n\nconst zlib = require('zlib');\n\nconst bufferUtil = require('./buffer-util');\nconst Limiter = require('./limiter');\nconst { kStatusCode } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0x00, 0x00, 0xff, 0xff]);\nconst kPerMessageDeflate = Symbol('permessage-deflate');\nconst kTotalLength = Symbol('total-length');\nconst kCallback = Symbol('callback');\nconst kBuffers = Symbol('buffers');\nconst kError = Symbol('error');\n\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n\n/**\n * permessage-deflate implementation.\n */\nclass PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold =\n      this._options.threshold !== undefined ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n\n    this.params = null;\n\n    if (!zlibLimiter) {\n      const concurrency =\n        this._options.concurrencyLimit !== undefined\n          ? this._options.concurrencyLimit\n          : 10;\n      zlibLimiter = new Limiter(concurrency);\n    }\n  }\n\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return 'permessage-deflate';\n  }\n\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n\n    return params;\n  }\n\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n\n    this.params = this._isServer\n      ? this.acceptAsServer(configurations)\n      : this.acceptAsClient(configurations);\n\n    return this.params;\n  }\n\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n\n      this._deflate.close();\n      this._deflate = null;\n\n      if (callback) {\n        callback(\n          new Error(\n            'The deflate stream was closed while data was being processed'\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (\n        (opts.serverNoContextTakeover === false &&\n          params.server_no_context_takeover) ||\n        (params.server_max_window_bits &&\n          (opts.serverMaxWindowBits === false ||\n            (typeof opts.serverMaxWindowBits === 'number' &&\n              opts.serverMaxWindowBits > params.server_max_window_bits))) ||\n        (typeof opts.clientMaxWindowBits === 'number' &&\n          !params.client_max_window_bits)\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n\n    if (!accepted) {\n      throw new Error('None of the extension offers can be accepted');\n    }\n\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === 'number') {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === 'number') {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (\n      accepted.client_max_window_bits === true ||\n      opts.clientMaxWindowBits === false\n    ) {\n      delete accepted.client_max_window_bits;\n    }\n\n    return accepted;\n  }\n\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n\n    if (\n      this._options.clientNoContextTakeover === false &&\n      params.client_no_context_takeover\n    ) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === 'number') {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (\n      this._options.clientMaxWindowBits === false ||\n      (typeof this._options.clientMaxWindowBits === 'number' &&\n        params.client_max_window_bits > this._options.clientMaxWindowBits)\n    ) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n\n    return params;\n  }\n\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n\n        value = value[0];\n\n        if (key === 'client_max_window_bits') {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === 'server_max_window_bits') {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (\n          key === 'client_no_context_takeover' ||\n          key === 'server_no_context_takeover'\n        ) {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n\n        params[key] = value;\n      });\n    });\n\n    return configurations;\n  }\n\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? 'client' : 'server';\n\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on('error', inflateOnError);\n      this._inflate.on('data', inflateOnData);\n    }\n\n    this._inflate[kCallback] = callback;\n\n    this._inflate.write(data);\n    if (fin) this._inflate.write(TRAILER);\n\n    this._inflate.flush(() => {\n      const err = this._inflate[kError];\n\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n\n      const data = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n\n      callback(null, data);\n    });\n  }\n\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? 'server' : 'client';\n\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      this._deflate.on('data', deflateOnData);\n    }\n\n    this._deflate[kCallback] = callback;\n\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        //\n        // The deflate stream was closed while data was being processed.\n        //\n        return;\n      }\n\n      let data = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n\n      if (fin) {\n        data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n      }\n\n      //\n      // Ensure that the callback will not be called again in\n      // `PerMessageDeflate#cleanup()`.\n      //\n      this._deflate[kCallback] = null;\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n\n      callback(null, data);\n    });\n  }\n}\n\nmodule.exports = PerMessageDeflate;\n\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n\n  if (\n    this[kPerMessageDeflate]._maxPayload < 1 ||\n    this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload\n  ) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n\n  this[kError] = new RangeError('Max payload size exceeded');\n  this[kError].code = 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH';\n  this[kError][kStatusCode] = 1009;\n  this.removeListener('data', inflateOnData);\n\n  //\n  // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n  // fact that in Node.js versions prior to 13.10.0, the callback for\n  // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n  // `zlib.reset()` ensures that either the callback is invoked or an error is\n  // emitted.\n  //\n  this.reset();\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */\nfunction inflateOnError(err) {\n  //\n  // There is no need to call `Zlib#close()` as the handle is automatically\n  // closed when an error is emitted.\n  //\n  this[kPerMessageDeflate]._inflate = null;\n\n  if (this[kError]) {\n    this[kCallback](this[kError]);\n    return;\n  }\n\n  err[kStatusCode] = 1007;\n  this[kCallback](err);\n}\n", "'use strict';\n\nconst { isUtf8 } = require('buffer');\n\nconst { hasBlob } = require('./constants');\n\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 0 - 15\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 16 - 31\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, // 32 - 47\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, // 48 - 63\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 64 - 79\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, // 80 - 95\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 96 - 111\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0 // 112 - 127\n];\n\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */\nfunction isValidStatusCode(code) {\n  return (\n    (code >= 1000 &&\n      code <= 1014 &&\n      code !== 1004 &&\n      code !== 1005 &&\n      code !== 1006) ||\n    (code >= 3000 && code <= 4999)\n  );\n}\n\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n\n  while (i < len) {\n    if ((buf[i] & 0x80) === 0) {\n      // 0xxxxxxx\n      i++;\n    } else if ((buf[i] & 0xe0) === 0xc0) {\n      // 110xxxxx 10xxxxxx\n      if (\n        i + 1 === len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i] & 0xfe) === 0xc0 // Overlong\n      ) {\n        return false;\n      }\n\n      i += 2;\n    } else if ((buf[i] & 0xf0) === 0xe0) {\n      // 1110xxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 2 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80) || // Overlong\n        (buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0) // Surrogate (U+D800 - U+DFFF)\n      ) {\n        return false;\n      }\n\n      i += 3;\n    } else if ((buf[i] & 0xf8) === 0xf0) {\n      // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 3 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i + 3] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80) || // Overlong\n        (buf[i] === 0xf4 && buf[i + 1] > 0x8f) ||\n        buf[i] > 0xf4 // > U+10FFFF\n      ) {\n        return false;\n      }\n\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */\nfunction isBlob(value) {\n  return (\n    hasBlob &&\n    typeof value === 'object' &&\n    typeof value.arrayBuffer === 'function' &&\n    typeof value.type === 'string' &&\n    typeof value.stream === 'function' &&\n    (value[Symbol.toStringTag] === 'Blob' ||\n      value[Symbol.toStringTag] === 'File')\n  );\n}\n\nmodule.exports = {\n  isBlob,\n  isValidStatusCode,\n  isValidUTF8: _isValidUTF8,\n  tokenChars\n};\n\nif (isUtf8) {\n  module.exports.isValidUTF8 = function (buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF8 = require('utf-8-validate');\n\n    module.exports.isValidUTF8 = function (buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n", "'use strict';\n\nconst { Writable } = require('stream');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  kStatusCode,\n  kWebSocket\n} = require('./constants');\nconst { concat, toArrayBuffer, unmask } = require('./buffer-util');\nconst { isValidStatusCode, isValidUTF8 } = require('./validation');\n\nconst FastBuffer = Buffer[Symbol.species];\n\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */\nclass Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n\n    this._allowSynchronousEvents =\n      options.allowSynchronousEvents !== undefined\n        ? options.allowSynchronousEvents\n        : true;\n    this._binaryType = options.binaryType || BINARY_TYPES[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket] = undefined;\n\n    this._bufferedBytes = 0;\n    this._buffers = [];\n\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = undefined;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n\n    this._errored = false;\n    this._loop = false;\n    this._state = GET_INFO;\n  }\n\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n\n    if (n === this._buffers[0].length) return this._buffers.shift();\n\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n\n    const dst = Buffer.allocUnsafe(n);\n\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n\n      n -= buf.length;\n    } while (n > 0);\n\n    return dst;\n  }\n\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    this._loop = true;\n\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          this.getInfo(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          this.getPayloadLength16(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          this.getPayloadLength64(cb);\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          this.getData(cb);\n          break;\n        case INFLATING:\n        case DEFER_EVENT:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n\n    if (!this._errored) cb();\n  }\n\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getInfo(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(2);\n\n    if ((buf[0] & 0x30) !== 0x00) {\n      const error = this.createError(\n        RangeError,\n        'RSV2 and RSV3 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_2_3'\n      );\n\n      cb(error);\n      return;\n    }\n\n    const compressed = (buf[0] & 0x40) === 0x40;\n\n    if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n      const error = this.createError(\n        RangeError,\n        'RSV1 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_1'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._fin = (buf[0] & 0x80) === 0x80;\n    this._opcode = buf[0] & 0x0f;\n    this._payloadLength = buf[1] & 0x7f;\n\n    if (this._opcode === 0x00) {\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (!this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          'invalid opcode 0',\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n      if (this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._compressed = compressed;\n    } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n      if (!this._fin) {\n        const error = this.createError(\n          RangeError,\n          'FIN must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_FIN'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (\n        this._payloadLength > 0x7d ||\n        (this._opcode === 0x08 && this._payloadLength === 1)\n      ) {\n        const error = this.createError(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    } else {\n      const error = this.createError(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        'WS_ERR_INVALID_OPCODE'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n    this._masked = (buf[1] & 0x80) === 0x80;\n\n    if (this._isServer) {\n      if (!this._masked) {\n        const error = this.createError(\n          RangeError,\n          'MASK must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_MASK'\n        );\n\n        cb(error);\n        return;\n      }\n    } else if (this._masked) {\n      const error = this.createError(\n        RangeError,\n        'MASK must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_MASK'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n    else this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength16(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength64(cb) {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n\n    //\n    // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n    // if payload length is greater than this number.\n    //\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      const error = this.createError(\n        RangeError,\n        'Unsupported WebSocket frame: payload length > 2^53 - 1',\n        false,\n        1009,\n        'WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  haveLength(cb) {\n    if (this._payloadLength && this._opcode < 0x08) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        const error = this.createError(\n          RangeError,\n          'Max payload size exceeded',\n          false,\n          1009,\n          'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    }\n\n    if (this._masked) this._state = GET_MASK;\n    else this._state = GET_DATA;\n  }\n\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER;\n\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n\n      data = this.consume(this._payloadLength);\n\n      if (\n        this._masked &&\n        (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0\n      ) {\n        unmask(data, this._mask);\n      }\n    }\n\n    if (this._opcode > 0x07) {\n      this.controlMessage(data, cb);\n      return;\n    }\n\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n\n    if (data.length) {\n      //\n      // This message is not compressed so its length is the sum of the payload\n      // length of all fragments.\n      //\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n\n    this.dataMessage(cb);\n  }\n\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err) return cb(err);\n\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          const error = this.createError(\n            RangeError,\n            'Max payload size exceeded',\n            false,\n            1009,\n            'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._fragments.push(buf);\n      }\n\n      this.dataMessage(cb);\n      if (this._state === GET_INFO) this.startLoop(cb);\n    });\n  }\n\n  /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  dataMessage(cb) {\n    if (!this._fin) {\n      this._state = GET_INFO;\n      return;\n    }\n\n    const messageLength = this._messageLength;\n    const fragments = this._fragments;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragmented = 0;\n    this._fragments = [];\n\n    if (this._opcode === 2) {\n      let data;\n\n      if (this._binaryType === 'nodebuffer') {\n        data = concat(fragments, messageLength);\n      } else if (this._binaryType === 'arraybuffer') {\n        data = toArrayBuffer(concat(fragments, messageLength));\n      } else if (this._binaryType === 'blob') {\n        data = new Blob(fragments);\n      } else {\n        data = fragments;\n      }\n\n      if (this._allowSynchronousEvents) {\n        this.emit('message', data, true);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', data, true);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    } else {\n      const buf = concat(fragments, messageLength);\n\n      if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n        const error = this.createError(\n          Error,\n          'invalid UTF-8 sequence',\n          true,\n          1007,\n          'WS_ERR_INVALID_UTF8'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (this._state === INFLATING || this._allowSynchronousEvents) {\n        this.emit('message', buf, false);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', buf, false);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    }\n  }\n\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data, cb) {\n    if (this._opcode === 0x08) {\n      if (data.length === 0) {\n        this._loop = false;\n        this.emit('conclude', 1005, EMPTY_BUFFER);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n\n        if (!isValidStatusCode(code)) {\n          const error = this.createError(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            'WS_ERR_INVALID_CLOSE_CODE'\n          );\n\n          cb(error);\n          return;\n        }\n\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          const error = this.createError(\n            Error,\n            'invalid UTF-8 sequence',\n            true,\n            1007,\n            'WS_ERR_INVALID_UTF8'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._loop = false;\n        this.emit('conclude', code, buf);\n        this.end();\n      }\n\n      this._state = GET_INFO;\n      return;\n    }\n\n    if (this._allowSynchronousEvents) {\n      this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n      this._state = GET_INFO;\n    } else {\n      this._state = DEFER_EVENT;\n      setImmediate(() => {\n        this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n        this._state = GET_INFO;\n        this.startLoop(cb);\n      });\n    }\n  }\n\n  /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */\n  createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n    this._loop = false;\n    this._errored = true;\n\n    const err = new ErrorCtor(\n      prefix ? `Invalid WebSocket frame: ${message}` : message\n    );\n\n    Error.captureStackTrace(err, this.createError);\n    err.code = errorCode;\n    err[kStatusCode] = statusCode;\n    return err;\n  }\n}\n\nmodule.exports = Receiver;\n", "/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */\n\n'use strict';\n\nconst { Duplex } = require('stream');\nconst { randomFillSync } = require('crypto');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = require('./constants');\nconst { isBlob, isValidStatusCode } = require('./validation');\nconst { mask: applyMask, toBuffer } = require('./buffer-util');\n\nconst kByteLength = Symbol('kByteLength');\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\n\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n\n/**\n * HyBi Sender implementation.\n */\nclass Sender {\n  /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */\n  constructor(socket, extensions, generateMask) {\n    this._extensions = extensions || {};\n\n    if (generateMask) {\n      this._generateMask = generateMask;\n      this._maskBuffer = Buffer.alloc(4);\n    }\n\n    this._socket = socket;\n\n    this._firstFragment = true;\n    this._compress = false;\n\n    this._bufferedBytes = 0;\n    this._queue = [];\n    this._state = DEFAULT;\n    this.onerror = NOOP;\n    this[kWebSocket] = undefined;\n  }\n\n  /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */\n  static frame(data, options) {\n    let mask;\n    let merge = false;\n    let offset = 2;\n    let skipMasking = false;\n\n    if (options.mask) {\n      mask = options.maskBuffer || maskBuffer;\n\n      if (options.generateMask) {\n        options.generateMask(mask);\n      } else {\n        if (randomPoolPointer === RANDOM_POOL_SIZE) {\n          /* istanbul ignore else  */\n          if (randomPool === undefined) {\n            //\n            // This is lazily initialized because server-sent frames must not\n            // be masked so it may never be used.\n            //\n            randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n          }\n\n          randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n          randomPoolPointer = 0;\n        }\n\n        mask[0] = randomPool[randomPoolPointer++];\n        mask[1] = randomPool[randomPoolPointer++];\n        mask[2] = randomPool[randomPoolPointer++];\n        mask[3] = randomPool[randomPoolPointer++];\n      }\n\n      skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n      offset = 6;\n    }\n\n    let dataLength;\n\n    if (typeof data === 'string') {\n      if (\n        (!options.mask || skipMasking) &&\n        options[kByteLength] !== undefined\n      ) {\n        dataLength = options[kByteLength];\n      } else {\n        data = Buffer.from(data);\n        dataLength = data.length;\n      }\n    } else {\n      dataLength = data.length;\n      merge = options.mask && options.readOnly && !skipMasking;\n    }\n\n    let payloadLength = dataLength;\n\n    if (dataLength >= 65536) {\n      offset += 8;\n      payloadLength = 127;\n    } else if (dataLength > 125) {\n      offset += 2;\n      payloadLength = 126;\n    }\n\n    const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n\n    target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n    if (options.rsv1) target[0] |= 0x40;\n\n    target[1] = payloadLength;\n\n    if (payloadLength === 126) {\n      target.writeUInt16BE(dataLength, 2);\n    } else if (payloadLength === 127) {\n      target[2] = target[3] = 0;\n      target.writeUIntBE(dataLength, 4, 6);\n    }\n\n    if (!options.mask) return [target, data];\n\n    target[1] |= 0x80;\n    target[offset - 4] = mask[0];\n    target[offset - 3] = mask[1];\n    target[offset - 2] = mask[2];\n    target[offset - 1] = mask[3];\n\n    if (skipMasking) return [target, data];\n\n    if (merge) {\n      applyMask(data, mask, target, offset, dataLength);\n      return [target];\n    }\n\n    applyMask(data, mask, data, 0, dataLength);\n    return [target, data];\n  }\n\n  /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  close(code, data, mask, cb) {\n    let buf;\n\n    if (code === undefined) {\n      buf = EMPTY_BUFFER;\n    } else if (typeof code !== 'number' || !isValidStatusCode(code)) {\n      throw new TypeError('First argument must be a valid error code number');\n    } else if (data === undefined || !data.length) {\n      buf = Buffer.allocUnsafe(2);\n      buf.writeUInt16BE(code, 0);\n    } else {\n      const length = Buffer.byteLength(data);\n\n      if (length > 123) {\n        throw new RangeError('The message must not be greater than 123 bytes');\n      }\n\n      buf = Buffer.allocUnsafe(2 + length);\n      buf.writeUInt16BE(code, 0);\n\n      if (typeof data === 'string') {\n        buf.write(data, 2);\n      } else {\n        buf.set(data, 2);\n      }\n    }\n\n    const options = {\n      [kByteLength]: buf.length,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x08,\n      readOnly: false,\n      rsv1: false\n    };\n\n    if (this._state !== DEFAULT) {\n      this.enqueue([this.dispatch, buf, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(buf, options), cb);\n    }\n  }\n\n  /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  ping(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else if (isBlob(data)) {\n      byteLength = data.size;\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x09,\n      readOnly,\n      rsv1: false\n    };\n\n    if (isBlob(data)) {\n      if (this._state !== DEFAULT) {\n        this.enqueue([this.getBlobData, data, false, options, cb]);\n      } else {\n        this.getBlobData(data, false, options, cb);\n      }\n    } else if (this._state !== DEFAULT) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  pong(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else if (isBlob(data)) {\n      byteLength = data.size;\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x0a,\n      readOnly,\n      rsv1: false\n    };\n\n    if (isBlob(data)) {\n      if (this._state !== DEFAULT) {\n        this.enqueue([this.getBlobData, data, false, options, cb]);\n      } else {\n        this.getBlobData(data, false, options, cb);\n      }\n    } else if (this._state !== DEFAULT) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  send(data, options, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n    let opcode = options.binary ? 2 : 1;\n    let rsv1 = options.compress;\n\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else if (isBlob(data)) {\n      byteLength = data.size;\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (this._firstFragment) {\n      this._firstFragment = false;\n      if (\n        rsv1 &&\n        perMessageDeflate &&\n        perMessageDeflate.params[\n          perMessageDeflate._isServer\n            ? 'server_no_context_takeover'\n            : 'client_no_context_takeover'\n        ]\n      ) {\n        rsv1 = byteLength >= perMessageDeflate._threshold;\n      }\n      this._compress = rsv1;\n    } else {\n      rsv1 = false;\n      opcode = 0;\n    }\n\n    if (options.fin) this._firstFragment = true;\n\n    const opts = {\n      [kByteLength]: byteLength,\n      fin: options.fin,\n      generateMask: this._generateMask,\n      mask: options.mask,\n      maskBuffer: this._maskBuffer,\n      opcode,\n      readOnly,\n      rsv1\n    };\n\n    if (isBlob(data)) {\n      if (this._state !== DEFAULT) {\n        this.enqueue([this.getBlobData, data, this._compress, opts, cb]);\n      } else {\n        this.getBlobData(data, this._compress, opts, cb);\n      }\n    } else if (this._state !== DEFAULT) {\n      this.enqueue([this.dispatch, data, this._compress, opts, cb]);\n    } else {\n      this.dispatch(data, this._compress, opts, cb);\n    }\n  }\n\n  /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  getBlobData(blob, compress, options, cb) {\n    this._bufferedBytes += options[kByteLength];\n    this._state = GET_BLOB_DATA;\n\n    blob\n      .arrayBuffer()\n      .then((arrayBuffer) => {\n        if (this._socket.destroyed) {\n          const err = new Error(\n            'The socket was closed while the blob was being read'\n          );\n\n          //\n          // `callCallbacks` is called in the next tick to ensure that errors\n          // that might be thrown in the callbacks behave like errors thrown\n          // outside the promise chain.\n          //\n          process.nextTick(callCallbacks, this, err, cb);\n          return;\n        }\n\n        this._bufferedBytes -= options[kByteLength];\n        const data = toBuffer(arrayBuffer);\n\n        if (!compress) {\n          this._state = DEFAULT;\n          this.sendFrame(Sender.frame(data, options), cb);\n          this.dequeue();\n        } else {\n          this.dispatch(data, compress, options, cb);\n        }\n      })\n      .catch((err) => {\n        //\n        // `onError` is called in the next tick for the same reason that\n        // `callCallbacks` above is.\n        //\n        process.nextTick(onError, this, err, cb);\n      });\n  }\n\n  /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  dispatch(data, compress, options, cb) {\n    if (!compress) {\n      this.sendFrame(Sender.frame(data, options), cb);\n      return;\n    }\n\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    this._bufferedBytes += options[kByteLength];\n    this._state = DEFLATING;\n    perMessageDeflate.compress(data, options.fin, (_, buf) => {\n      if (this._socket.destroyed) {\n        const err = new Error(\n          'The socket was closed while data was being compressed'\n        );\n\n        callCallbacks(this, err, cb);\n        return;\n      }\n\n      this._bufferedBytes -= options[kByteLength];\n      this._state = DEFAULT;\n      options.readOnly = false;\n      this.sendFrame(Sender.frame(buf, options), cb);\n      this.dequeue();\n    });\n  }\n\n  /**\n   * Executes queued send operations.\n   *\n   * @private\n   */\n  dequeue() {\n    while (this._state === DEFAULT && this._queue.length) {\n      const params = this._queue.shift();\n\n      this._bufferedBytes -= params[3][kByteLength];\n      Reflect.apply(params[0], this, params.slice(1));\n    }\n  }\n\n  /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */\n  enqueue(params) {\n    this._bufferedBytes += params[3][kByteLength];\n    this._queue.push(params);\n  }\n\n  /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  sendFrame(list, cb) {\n    if (list.length === 2) {\n      this._socket.cork();\n      this._socket.write(list[0]);\n      this._socket.write(list[1], cb);\n      this._socket.uncork();\n    } else {\n      this._socket.write(list[0], cb);\n    }\n  }\n}\n\nmodule.exports = Sender;\n\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */\nfunction callCallbacks(sender, err, cb) {\n  if (typeof cb === 'function') cb(err);\n\n  for (let i = 0; i < sender._queue.length; i++) {\n    const params = sender._queue[i];\n    const callback = params[params.length - 1];\n\n    if (typeof callback === 'function') callback(err);\n  }\n}\n\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */\nfunction onError(sender, err, cb) {\n  callCallbacks(sender, err, cb);\n  sender.onerror(err);\n}\n", "'use strict';\n\nconst { kForOnEventAttribute, kListener } = require('./constants');\n\nconst kCode = Symbol('kCode');\nconst kData = Symbol('kData');\nconst kError = Symbol('kError');\nconst kMessage = Symbol('kMessage');\nconst kReason = Symbol('kReason');\nconst kTarget = Symbol('kTarget');\nconst kType = Symbol('kType');\nconst kWasClean = Symbol('kWasClean');\n\n/**\n * Class representing an event.\n */\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\n\nObject.defineProperty(Event.prototype, 'target', { enumerable: true });\nObject.defineProperty(Event.prototype, 'type', { enumerable: true });\n\n/**\n * Class representing a close event.\n *\n * @extends Event\n */\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kCode] = options.code === undefined ? 0 : options.code;\n    this[kReason] = options.reason === undefined ? '' : options.reason;\n    this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\n\nObject.defineProperty(CloseEvent.prototype, 'code', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'reason', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'wasClean', { enumerable: true });\n\n/**\n * Class representing an error event.\n *\n * @extends Event\n */\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kError] = options.error === undefined ? null : options.error;\n    this[kMessage] = options.message === undefined ? '' : options.message;\n  }\n\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\n\nObject.defineProperty(ErrorEvent.prototype, 'error', { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, 'message', { enumerable: true });\n\n/**\n * Class representing a message event.\n *\n * @extends Event\n */\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kData] = options.data === undefined ? null : options.data;\n  }\n\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\n\nObject.defineProperty(MessageEvent.prototype, 'data', { enumerable: true });\n\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (\n        !options[kForOnEventAttribute] &&\n        listener[kListener] === handler &&\n        !listener[kForOnEventAttribute]\n      ) {\n        return;\n      }\n    }\n\n    let wrapper;\n\n    if (type === 'message') {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent('message', {\n          data: isBinary ? data : data.toString()\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'close') {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent('close', {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'error') {\n      wrapper = function onError(error) {\n        const event = new ErrorEvent('error', {\n          error,\n          message: error.message\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'open') {\n      wrapper = function onOpen() {\n        const event = new Event('open');\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n\n    wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n    wrapper[kListener] = handler;\n\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\n\nmodule.exports = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\n\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === 'object' && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\n", "'use strict';\n\nconst { tokenChars } = require('./validation');\n\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */\nfunction push(dest, name, elem) {\n  if (dest[name] === undefined) dest[name] = [elem];\n  else dest[name].push(elem);\n}\n\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */\nfunction parse(header) {\n  const offers = Object.create(null);\n  let params = Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (extensionName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (\n        i !== 0 &&\n        (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n      ) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b /* ';' */ || code === 0x2c /* ',' */) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        const name = header.slice(start, end);\n        if (code === 0x2c) {\n          push(offers, name, params);\n          params = Object.create(null);\n        } else {\n          extensionName = name;\n        }\n\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (code === 0x20 || code === 0x09) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        start = end = -1;\n      } else if (code === 0x3d /* '=' */ && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      //\n      // The value of a quoted-string after unescaping must conform to the\n      // token ABNF, so only token characters are valid.\n      // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n      //\n      if (isEscaping) {\n        if (tokenChars[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1) start = i;\n        else if (!mustUnescape) mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars[code] === 1) {\n          if (start === -1) start = i;\n        } else if (code === 0x22 /* '\"' */ && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 0x5c /* '\\' */) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n        if (end === -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, '');\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        paramName = undefined;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n\n  if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  if (end === -1) end = i;\n  const token = header.slice(start, end);\n  if (extensionName === undefined) {\n    push(offers, token, params);\n  } else {\n    if (paramName === undefined) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, ''));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n\n  return offers;\n}\n\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */\nfunction format(extensions) {\n  return Object.keys(extensions)\n    .map((extension) => {\n      let configurations = extensions[extension];\n      if (!Array.isArray(configurations)) configurations = [configurations];\n      return configurations\n        .map((params) => {\n          return [extension]\n            .concat(\n              Object.keys(params).map((k) => {\n                let values = params[k];\n                if (!Array.isArray(values)) values = [values];\n                return values\n                  .map((v) => (v === true ? k : `${k}=${v}`))\n                  .join('; ');\n              })\n            )\n            .join('; ');\n        })\n        .join(', ');\n    })\n    .join(', ');\n}\n\nmodule.exports = { format, parse };\n", "/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst https = require('https');\nconst http = require('http');\nconst net = require('net');\nconst tls = require('tls');\nconst { randomBytes, createHash } = require('crypto');\nconst { Duplex, Readable } = require('stream');\nconst { URL } = require('url');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst Receiver = require('./receiver');\nconst Sender = require('./sender');\nconst { isBlob } = require('./validation');\n\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket,\n  NOOP\n} = require('./constants');\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = require('./event-target');\nconst { format, parse } = require('./extension');\nconst { toBuffer } = require('./buffer-util');\n\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol('kAborted');\nconst protocolVersions = [8, 13];\nconst readyStates = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */\nclass WebSocket extends EventEmitter {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._errorEmitted = false;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = '';\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n\n      if (protocols === undefined) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === 'object' && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._autoPong = options.autoPong;\n      this._isServer = true;\n    }\n  }\n\n  /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type)) return;\n\n    this._binaryType = type;\n\n    //\n    // Allow to change `binaryType` on the fly.\n    //\n    if (this._receiver) this._receiver._binaryType = type;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket) return this._bufferedAmount;\n\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver = new Receiver({\n      allowSynchronousEvents: options.allowSynchronousEvents,\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n\n    const sender = new Sender(socket, this._extensions, options.generateMask);\n\n    this._receiver = receiver;\n    this._sender = sender;\n    this._socket = socket;\n\n    receiver[kWebSocket] = this;\n    sender[kWebSocket] = this;\n    socket[kWebSocket] = this;\n\n    receiver.on('conclude', receiverOnConclude);\n    receiver.on('drain', receiverOnDrain);\n    receiver.on('error', receiverOnError);\n    receiver.on('message', receiverOnMessage);\n    receiver.on('ping', receiverOnPing);\n    receiver.on('pong', receiverOnPong);\n\n    sender.onerror = senderOnError;\n\n    //\n    // These methods may not be available if `socket` is just a `Duplex`.\n    //\n    if (socket.setTimeout) socket.setTimeout(0);\n    if (socket.setNoDelay) socket.setNoDelay();\n\n    if (head.length > 0) socket.unshift(head);\n\n    socket.on('close', socketOnClose);\n    socket.on('data', socketOnData);\n    socket.on('end', socketOnEnd);\n    socket.on('error', socketOnError);\n\n    this._readyState = WebSocket.OPEN;\n    this.emit('open');\n  }\n\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit('close', this._closeCode, this._closeMessage);\n      return;\n    }\n\n    if (this._extensions[PerMessageDeflate.extensionName]) {\n      this._extensions[PerMessageDeflate.extensionName].cleanup();\n    }\n\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit('close', this._closeCode, this._closeMessage);\n  }\n\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this.readyState === WebSocket.CLOSING) {\n      if (\n        this._closeFrameSent &&\n        (this._closeFrameReceived || this._receiver._writableState.errorEmitted)\n      ) {\n        this._socket.end();\n      }\n\n      return;\n    }\n\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      //\n      // This error is handled by the `'error'` listener on the socket. We only\n      // want to know if the close frame has been sent here.\n      //\n      if (err) return;\n\n      this._closeFrameSent = true;\n\n      if (\n        this._closeFrameReceived ||\n        this._receiver._writableState.errorEmitted\n      ) {\n        this._socket.end();\n      }\n    });\n\n    setCloseTimer(this);\n  }\n\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = true;\n    this._socket.pause();\n  }\n\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain) this._socket.resume();\n  }\n\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    const opts = {\n      binary: typeof data !== 'string',\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n\n    if (!this._extensions[PerMessageDeflate.extensionName]) {\n      opts.compress = false;\n    }\n\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n}\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n[\n  'binaryType',\n  'bufferedAmount',\n  'extensions',\n  'isPaused',\n  'protocol',\n  'readyState',\n  'url'\n].forEach((property) => {\n  Object.defineProperty(WebSocket.prototype, property, { enumerable: true });\n});\n\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n['open', 'error', 'close', 'message'].forEach((method) => {\n  Object.defineProperty(WebSocket.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) return listener[kListener];\n      }\n\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n\n      if (typeof handler !== 'function') return;\n\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\n\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\n\nmodule.exports = WebSocket;\n\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */\nfunction initAsClient(websocket, address, protocols, options) {\n  const opts = {\n    allowSynchronousEvents: true,\n    autoPong: true,\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    socketPath: undefined,\n    hostname: undefined,\n    protocol: undefined,\n    timeout: undefined,\n    method: 'GET',\n    host: undefined,\n    path: undefined,\n    port: undefined\n  };\n\n  websocket._autoPong = opts.autoPong;\n\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} ` +\n        `(supported versions: ${protocolVersions.join(', ')})`\n    );\n  }\n\n  let parsedUrl;\n\n  if (address instanceof URL) {\n    parsedUrl = address;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n  }\n\n  if (parsedUrl.protocol === 'http:') {\n    parsedUrl.protocol = 'ws:';\n  } else if (parsedUrl.protocol === 'https:') {\n    parsedUrl.protocol = 'wss:';\n  }\n\n  websocket._url = parsedUrl.href;\n\n  const isSecure = parsedUrl.protocol === 'wss:';\n  const isIpcUrl = parsedUrl.protocol === 'ws+unix:';\n  let invalidUrlMessage;\n\n  if (parsedUrl.protocol !== 'ws:' && !isSecure && !isIpcUrl) {\n    invalidUrlMessage =\n      'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' +\n      '\"http:\", \"https:\", or \"ws+unix:\"';\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = 'The URL contains a fragment identifier';\n  }\n\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n\n    if (websocket._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket, err);\n      return;\n    }\n  }\n\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString('base64');\n  const request = isSecure ? https.request : http.request;\n  const protocolSet = new Set();\n  let perMessageDeflate;\n\n  opts.createConnection =\n    opts.createConnection || (isSecure ? tlsConnect : netConnect);\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith('[')\n    ? parsedUrl.hostname.slice(1, -1)\n    : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    'Sec-WebSocket-Version': opts.protocolVersion,\n    'Sec-WebSocket-Key': key,\n    Connection: 'Upgrade',\n    Upgrade: 'websocket'\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers['Sec-WebSocket-Extensions'] = format({\n      [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (\n        typeof protocol !== 'string' ||\n        !subprotocolRegex.test(protocol) ||\n        protocolSet.has(protocol)\n      ) {\n        throw new SyntaxError(\n          'An invalid or duplicated subprotocol was specified'\n        );\n      }\n\n      protocolSet.add(protocol);\n    }\n\n    opts.headers['Sec-WebSocket-Protocol'] = protocols.join(',');\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers['Sec-WebSocket-Origin'] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n\n  if (isIpcUrl) {\n    const parts = opts.path.split(':');\n\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n\n  let req;\n\n  if (opts.followRedirects) {\n    if (websocket._redirects === 0) {\n      websocket._originalIpc = isIpcUrl;\n      websocket._originalSecure = isSecure;\n      websocket._originalHostOrSocketPath = isIpcUrl\n        ? opts.socketPath\n        : parsedUrl.host;\n\n      const headers = options && options.headers;\n\n      //\n      // Shallow copy the user provided options so that headers can be changed\n      // without mutating the original object.\n      //\n      options = { ...options, headers: {} };\n\n      if (headers) {\n        for (const [key, value] of Object.entries(headers)) {\n          options.headers[key.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket.listenerCount('redirect') === 0) {\n      const isSameHost = isIpcUrl\n        ? websocket._originalIpc\n          ? opts.socketPath === websocket._originalHostOrSocketPath\n          : false\n        : websocket._originalIpc\n          ? false\n          : parsedUrl.host === websocket._originalHostOrSocketPath;\n\n      if (!isSameHost || (websocket._originalSecure && !isSecure)) {\n        //\n        // Match curl 7.77.0 behavior and drop the following headers. These\n        // headers are also dropped when following a redirect to a subdomain.\n        //\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n\n        if (!isSameHost) delete opts.headers.host;\n\n        opts.auth = undefined;\n      }\n    }\n\n    //\n    // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n    // If the `Authorization` header is set, then there is nothing to do as it\n    // will take precedence.\n    //\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization =\n        'Basic ' + Buffer.from(opts.auth).toString('base64');\n    }\n\n    req = websocket._req = request(opts);\n\n    if (websocket._redirects) {\n      //\n      // Unlike what is done for the `'upgrade'` event, no early exit is\n      // triggered here if the user calls `websocket.close()` or\n      // `websocket.terminate()` from a listener of the `'redirect'` event. This\n      // is because the user can also call `request.destroy()` with an error\n      // before calling `websocket.close()` or `websocket.terminate()` and this\n      // would result in an error being emitted on the `request` object with no\n      // `'error'` event listeners attached.\n      //\n      websocket.emit('redirect', websocket.url, req);\n    }\n  } else {\n    req = websocket._req = request(opts);\n  }\n\n  if (opts.timeout) {\n    req.on('timeout', () => {\n      abortHandshake(websocket, req, 'Opening handshake has timed out');\n    });\n  }\n\n  req.on('error', (err) => {\n    if (req === null || req[kAborted]) return;\n\n    req = websocket._req = null;\n    emitErrorAndClose(websocket, err);\n  });\n\n  req.on('response', (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n\n    if (\n      location &&\n      opts.followRedirects &&\n      statusCode >= 300 &&\n      statusCode < 400\n    ) {\n      if (++websocket._redirects > opts.maxRedirects) {\n        abortHandshake(websocket, req, 'Maximum redirects exceeded');\n        return;\n      }\n\n      req.abort();\n\n      let addr;\n\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket, err);\n        return;\n      }\n\n      initAsClient(websocket, addr, protocols, options);\n    } else if (!websocket.emit('unexpected-response', req, res)) {\n      abortHandshake(\n        websocket,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n\n  req.on('upgrade', (res, socket, head) => {\n    websocket.emit('upgrade', res);\n\n    //\n    // The user may have closed the connection from a listener of the\n    // `'upgrade'` event.\n    //\n    if (websocket.readyState !== WebSocket.CONNECTING) return;\n\n    req = websocket._req = null;\n\n    const upgrade = res.headers.upgrade;\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      abortHandshake(websocket, socket, 'Invalid Upgrade header');\n      return;\n    }\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    if (res.headers['sec-websocket-accept'] !== digest) {\n      abortHandshake(websocket, socket, 'Invalid Sec-WebSocket-Accept header');\n      return;\n    }\n\n    const serverProt = res.headers['sec-websocket-protocol'];\n    let protError;\n\n    if (serverProt !== undefined) {\n      if (!protocolSet.size) {\n        protError = 'Server sent a subprotocol but none was requested';\n      } else if (!protocolSet.has(serverProt)) {\n        protError = 'Server sent an invalid subprotocol';\n      }\n    } else if (protocolSet.size) {\n      protError = 'Server sent no subprotocol';\n    }\n\n    if (protError) {\n      abortHandshake(websocket, socket, protError);\n      return;\n    }\n\n    if (serverProt) websocket._protocol = serverProt;\n\n    const secWebSocketExtensions = res.headers['sec-websocket-extensions'];\n\n    if (secWebSocketExtensions !== undefined) {\n      if (!perMessageDeflate) {\n        const message =\n          'Server sent a Sec-WebSocket-Extensions header but no extension ' +\n          'was requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      let extensions;\n\n      try {\n        extensions = parse(secWebSocketExtensions);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      const extensionNames = Object.keys(extensions);\n\n      if (\n        extensionNames.length !== 1 ||\n        extensionNames[0] !== PerMessageDeflate.extensionName\n      ) {\n        const message = 'Server indicated an extension that was not requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      websocket._extensions[PerMessageDeflate.extensionName] =\n        perMessageDeflate;\n    }\n\n    websocket.setSocket(socket, head, {\n      allowSynchronousEvents: opts.allowSynchronousEvents,\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket);\n  } else {\n    req.end();\n  }\n}\n\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */\nfunction emitErrorAndClose(websocket, err) {\n  websocket._readyState = WebSocket.CLOSING;\n  //\n  // The following assignment is practically useless and is done only for\n  // consistency.\n  //\n  websocket._errorEmitted = true;\n  websocket.emit('error', err);\n  websocket.emitClose();\n}\n\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\n\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */\nfunction tlsConnect(options) {\n  options.path = undefined;\n\n  if (!options.servername && options.servername !== '') {\n    options.servername = net.isIP(options.host) ? '' : options.host;\n  }\n\n  return tls.connect(options);\n}\n\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */\nfunction abortHandshake(websocket, stream, message) {\n  websocket._readyState = WebSocket.CLOSING;\n\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake);\n\n  if (stream.setHeader) {\n    stream[kAborted] = true;\n    stream.abort();\n\n    if (stream.socket && !stream.socket.destroyed) {\n      //\n      // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n      // called after the request completed. See\n      // https://github.com/websockets/ws/issues/1869.\n      //\n      stream.socket.destroy();\n    }\n\n    process.nextTick(emitErrorAndClose, websocket, err);\n  } else {\n    stream.destroy(err);\n    stream.once('error', websocket.emit.bind(websocket, 'error'));\n    stream.once('close', websocket.emitClose.bind(websocket));\n  }\n}\n\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */\nfunction sendAfterClose(websocket, data, cb) {\n  if (data) {\n    const length = isBlob(data) ? data.size : toBuffer(data).length;\n\n    //\n    // The `_bufferedAmount` property is used only when the peer is a client and\n    // the opening handshake fails. Under these circumstances, in fact, the\n    // `setSocket()` method is not called, so the `_socket` and `_sender`\n    // properties are set to `null`.\n    //\n    if (websocket._socket) websocket._sender._bufferedBytes += length;\n    else websocket._bufferedAmount += length;\n  }\n\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket.readyState} ` +\n        `(${readyStates[websocket.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */\nfunction receiverOnConclude(code, reason) {\n  const websocket = this[kWebSocket];\n\n  websocket._closeFrameReceived = true;\n  websocket._closeMessage = reason;\n  websocket._closeCode = code;\n\n  if (websocket._socket[kWebSocket] === undefined) return;\n\n  websocket._socket.removeListener('data', socketOnData);\n  process.nextTick(resume, websocket._socket);\n\n  if (code === 1005) websocket.close();\n  else websocket.close(code, reason);\n}\n\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */\nfunction receiverOnDrain() {\n  const websocket = this[kWebSocket];\n\n  if (!websocket.isPaused) websocket._socket.resume();\n}\n\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */\nfunction receiverOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._socket[kWebSocket] !== undefined) {\n    websocket._socket.removeListener('data', socketOnData);\n\n    //\n    // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n    // https://github.com/websockets/ws/issues/1940.\n    //\n    process.nextTick(resume, websocket._socket);\n\n    websocket.close(err[kStatusCode]);\n  }\n\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */\nfunction receiverOnFinish() {\n  this[kWebSocket].emitClose();\n}\n\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket].emit('message', data, isBinary);\n}\n\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */\nfunction receiverOnPing(data) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n  websocket.emit('ping', data);\n}\n\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */\nfunction receiverOnPong(data) {\n  this[kWebSocket].emit('pong', data);\n}\n\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */\nfunction resume(stream) {\n  stream.resume();\n}\n\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */\nfunction senderOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket.readyState === WebSocket.CLOSED) return;\n  if (websocket.readyState === WebSocket.OPEN) {\n    websocket._readyState = WebSocket.CLOSING;\n    setCloseTimer(websocket);\n  }\n\n  //\n  // `socket.end()` is used instead of `socket.destroy()` to allow the other\n  // peer to finish sending queued data. There is no need to set a timer here\n  // because `CLOSING` means that it is already set or not needed.\n  //\n  this._socket.end();\n\n  if (!websocket._errorEmitted) {\n    websocket._errorEmitted = true;\n    websocket.emit('error', err);\n  }\n}\n\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */\nfunction setCloseTimer(websocket) {\n  websocket._closeTimer = setTimeout(\n    websocket._socket.destroy.bind(websocket._socket),\n    closeTimeout\n  );\n}\n\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */\nfunction socketOnClose() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('close', socketOnClose);\n  this.removeListener('data', socketOnData);\n  this.removeListener('end', socketOnEnd);\n\n  websocket._readyState = WebSocket.CLOSING;\n\n  let chunk;\n\n  //\n  // The close frame might not have been received or the `'end'` event emitted,\n  // for example, if the socket was destroyed due to an error. Ensure that the\n  // `receiver` stream is closed after writing any remaining buffered data to\n  // it. If the readable side of the socket is in flowing mode then there is no\n  // buffered data as everything has been already written and `readable.read()`\n  // will return `null`. If instead, the socket is paused, any possible buffered\n  // data will be read as a single chunk.\n  //\n  if (\n    !this._readableState.endEmitted &&\n    !websocket._closeFrameReceived &&\n    !websocket._receiver._writableState.errorEmitted &&\n    (chunk = websocket._socket.read()) !== null\n  ) {\n    websocket._receiver.write(chunk);\n  }\n\n  websocket._receiver.end();\n\n  this[kWebSocket] = undefined;\n\n  clearTimeout(websocket._closeTimer);\n\n  if (\n    websocket._receiver._writableState.finished ||\n    websocket._receiver._writableState.errorEmitted\n  ) {\n    websocket.emitClose();\n  } else {\n    websocket._receiver.on('error', receiverOnFinish);\n    websocket._receiver.on('finish', receiverOnFinish);\n  }\n}\n\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\n\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */\nfunction socketOnEnd() {\n  const websocket = this[kWebSocket];\n\n  websocket._readyState = WebSocket.CLOSING;\n  websocket._receiver.end();\n  this.end();\n}\n\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */\nfunction socketOnError() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('error', socketOnError);\n  this.on('error', NOOP);\n\n  if (websocket) {\n    websocket._readyState = WebSocket.CLOSING;\n    this.destroy();\n  }\n}\n", "/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */\n'use strict';\n\nconst WebSocket = require('./websocket');\nconst { Duplex } = require('stream');\n\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */\nfunction emitClose(stream) {\n  stream.emit('close');\n}\n\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\n\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */\nfunction duplexOnError(err) {\n  this.removeListener('error', duplexOnError);\n  this.destroy();\n  if (this.listenerCount('error') === 0) {\n    // Do not suppress the throwing behavior.\n    this.emit('error', err);\n  }\n}\n\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */\nfunction createWebSocketStream(ws, options) {\n  let terminateOnDestroy = true;\n\n  const duplex = new Duplex({\n    ...options,\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  });\n\n  ws.on('message', function message(msg, isBinary) {\n    const data =\n      !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n\n    if (!duplex.push(data)) ws.pause();\n  });\n\n  ws.once('error', function error(err) {\n    if (duplex.destroyed) return;\n\n    // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n    //\n    // - If the `'error'` event is emitted before the `'open'` event, then\n    //   `ws.terminate()` is a noop as no socket is assigned.\n    // - Otherwise, the error is re-emitted by the listener of the `'error'`\n    //   event of the `Receiver` object. The listener already closes the\n    //   connection by calling `ws.close()`. This allows a close frame to be\n    //   sent to the other peer. If `ws.terminate()` is called right after this,\n    //   then the close frame might not be sent.\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n\n  ws.once('close', function close() {\n    if (duplex.destroyed) return;\n\n    duplex.push(null);\n  });\n\n  duplex._destroy = function (err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose, duplex);\n      return;\n    }\n\n    let called = false;\n\n    ws.once('error', function error(err) {\n      called = true;\n      callback(err);\n    });\n\n    ws.once('close', function close() {\n      if (!called) callback(err);\n      process.nextTick(emitClose, duplex);\n    });\n\n    if (terminateOnDestroy) ws.terminate();\n  };\n\n  duplex._final = function (callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n\n    // If the value of the `_socket` property is `null` it means that `ws` is a\n    // client websocket and the handshake failed. In fact, when this happens, a\n    // socket is never assigned to the websocket. Wait for the `'error'` event\n    // that will be emitted by the websocket.\n    if (ws._socket === null) return;\n\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted) duplex.destroy();\n    } else {\n      ws._socket.once('finish', function finish() {\n        // `duplex` is not destroyed here because the `'end'` event will be\n        // emitted on `duplex` after this `'finish'` event. The EOF signaling\n        // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n        callback();\n      });\n      ws.close();\n    }\n  };\n\n  duplex._read = function () {\n    if (ws.isPaused) ws.resume();\n  };\n\n  duplex._write = function (chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n\n    ws.send(chunk, callback);\n  };\n\n  duplex.on('end', duplexOnEnd);\n  duplex.on('error', duplexOnError);\n  return duplex;\n}\n\nmodule.exports = createWebSocketStream;\n", "'use strict';\n\nconst { tokenChars } = require('./validation');\n\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */\nfunction parse(header) {\n  const protocols = new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1) start = i;\n    } else if (\n      i !== 0 &&\n      (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n    ) {\n      if (end === -1 && start !== -1) end = i;\n    } else if (code === 0x2c /* ',' */) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n\n      if (end === -1) end = i;\n\n      const protocol = header.slice(start, end);\n\n      if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n      }\n\n      protocols.add(protocol);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  const protocol = header.slice(start, i);\n\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n\n  protocols.add(protocol);\n  return protocols;\n}\n\nmodule.exports = { parse };\n", "/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst http = require('http');\nconst { Duplex } = require('stream');\nconst { createHash } = require('crypto');\n\nconst extension = require('./extension');\nconst PerMessageDeflate = require('./permessage-deflate');\nconst subprotocol = require('./subprotocol');\nconst WebSocket = require('./websocket');\nconst { GUID, kWebSocket } = require('./constants');\n\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\n\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n\n    options = {\n      allowSynchronousEvents: true,\n      autoPong: true,\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null, // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket,\n      ...options\n    };\n\n    if (\n      (options.port == null && !options.server && !options.noServer) ||\n      (options.port != null && (options.server || options.noServer)) ||\n      (options.server && options.noServer)\n    ) {\n      throw new TypeError(\n        'One and only one of the \"port\", \"server\", or \"noServer\" options ' +\n          'must be specified'\n      );\n    }\n\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n\n        res.writeHead(426, {\n          'Content-Length': body.length,\n          'Content-Type': 'text/plain'\n        });\n        res.end(body);\n      });\n      this._server.listen(\n        options.port,\n        options.host,\n        options.backlog,\n        callback\n      );\n    } else if (options.server) {\n      this._server = options.server;\n    }\n\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, 'connection');\n\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, 'listening'),\n        error: this.emit.bind(this, 'error'),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n\n    if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = new Set();\n      this._shouldEmitClose = false;\n    }\n\n    this.options = options;\n    this._state = RUNNING;\n  }\n\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n\n    if (!this._server) return null;\n    return this._server.address();\n  }\n\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once('close', () => {\n          cb(new Error('The server is not running'));\n        });\n      }\n\n      process.nextTick(emitClose, this);\n      return;\n    }\n\n    if (cb) this.once('close', cb);\n\n    if (this._state === CLOSING) return;\n    this._state = CLOSING;\n\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n\n      //\n      // The HTTP/S server was created internally. Close it, and rely on its\n      // `'close'` event.\n      //\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf('?');\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n\n      if (pathname !== this.options.path) return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on('error', socketOnError);\n\n    const key = req.headers['sec-websocket-key'];\n    const upgrade = req.headers.upgrade;\n    const version = +req.headers['sec-websocket-version'];\n\n    if (req.method !== 'GET') {\n      const message = 'Invalid HTTP method';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      const message = 'Invalid Upgrade header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (key === undefined || !keyRegex.test(key)) {\n      const message = 'Missing or invalid Sec-WebSocket-Key header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (version !== 8 && version !== 13) {\n      const message = 'Missing or invalid Sec-WebSocket-Version header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n\n    const secWebSocketProtocol = req.headers['sec-websocket-protocol'];\n    let protocols = new Set();\n\n    if (secWebSocketProtocol !== undefined) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Protocol header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    const secWebSocketExtensions = req.headers['sec-websocket-extensions'];\n    const extensions = {};\n\n    if (\n      this.options.perMessageDeflate &&\n      secWebSocketExtensions !== undefined\n    ) {\n      const perMessageDeflate = new PerMessageDeflate(\n        this.options.perMessageDeflate,\n        true,\n        this.options.maxPayload\n      );\n\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n\n        if (offers[PerMessageDeflate.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n          extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message =\n          'Invalid or unacceptable Sec-WebSocket-Extensions header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    //\n    // Optionally call external client verification handler.\n    //\n    if (this.options.verifyClient) {\n      const info = {\n        origin:\n          req.headers[`${version === 8 ? 'sec-websocket-origin' : 'origin'}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n\n          this.completeUpgrade(\n            extensions,\n            key,\n            protocols,\n            req,\n            socket,\n            head,\n            cb\n          );\n        });\n        return;\n      }\n\n      if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n    }\n\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    //\n    // Destroy the socket if the client has already sent a FIN packet.\n    //\n    if (!socket.readable || !socket.writable) return socket.destroy();\n\n    if (socket[kWebSocket]) {\n      throw new Error(\n        'server.handleUpgrade() was called more than once with the same ' +\n          'socket, possibly due to a misconfiguration'\n      );\n    }\n\n    if (this._state > RUNNING) return abortHandshake(socket, 503);\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    const headers = [\n      'HTTP/1.1 101 Switching Protocols',\n      'Upgrade: websocket',\n      'Connection: Upgrade',\n      `Sec-WebSocket-Accept: ${digest}`\n    ];\n\n    const ws = new this.options.WebSocket(null, undefined, this.options);\n\n    if (protocols.size) {\n      //\n      // Optionally call external protocol selection handler.\n      //\n      const protocol = this.options.handleProtocols\n        ? this.options.handleProtocols(protocols, req)\n        : protocols.values().next().value;\n\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n\n    if (extensions[PerMessageDeflate.extensionName]) {\n      const params = extensions[PerMessageDeflate.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n\n    //\n    // Allow external modification/inspection of handshake headers.\n    //\n    this.emit('headers', headers, req);\n\n    socket.write(headers.concat('\\r\\n').join('\\r\\n'));\n    socket.removeListener('error', socketOnError);\n\n    ws.setSocket(socket, head, {\n      allowSynchronousEvents: this.options.allowSynchronousEvents,\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on('close', () => {\n        this.clients.delete(ws);\n\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n\n    cb(ws, req);\n  }\n}\n\nmodule.exports = WebSocketServer;\n\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map)) server.on(event, map[event]);\n\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\n\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit('close');\n}\n\n/**\n * Handle socket errors.\n *\n * @private\n */\nfunction socketOnError() {\n  this.destroy();\n}\n\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */\nfunction abortHandshake(socket, code, message, headers) {\n  //\n  // The socket is writable unless the user destroyed or ended it before calling\n  // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n  // error. Handling this does not make much sense as the worst that can happen\n  // is that some of the data written by the user might be discarded due to the\n  // call to `socket.end()` below, which triggers an `'error'` event that in\n  // turn causes the socket to be destroyed.\n  //\n  message = message || http.STATUS_CODES[code];\n  headers = {\n    Connection: 'close',\n    'Content-Type': 'text/html',\n    'Content-Length': Buffer.byteLength(message),\n    ...headers\n  };\n\n  socket.once('finish', socket.destroy);\n\n  socket.end(\n    `HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` +\n      Object.keys(headers)\n        .map((h) => `${h}: ${headers[h]}`)\n        .join('\\r\\n') +\n      '\\r\\n\\r\\n' +\n      message\n  );\n}\n\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @private\n */\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount('wsClientError')) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n\n    server.emit('wsClientError', err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}\n", "import createWebSocketStream from './lib/stream.js';\nimport Receiver from './lib/receiver.js';\nimport Sender from './lib/sender.js';\nimport WebSocket from './lib/websocket.js';\nimport WebSocketServer from './lib/websocket-server.js';\n\nexport { createWebSocketStream, Receiver, Sender, WebSocket, WebSocketServer };\nexport default WebSocket;\n"], "names": [], "mappings": "mEAAA,aAEA,IAAM,EAAe,CAAC,aAAc,cAAe,YAAY,CACzD,EAA0B,aAAhB,OAAO,IAEnB,IAAS,EAAa,IAAI,CAAC,QAE/B,EAAO,OAAO,CAAG,cACf,EACA,aAAc,OAAO,KAAK,CAAC,GAC3B,KAAM,+CACN,EACA,qBAAsB,OAAO,0BAC7B,UAAW,OAAO,aAClB,YAAa,OAAO,eACpB,WAAY,OAAO,aACnB,KAAM,KAAO,CACf,uDCjBA,aAEA,GAAM,cAAE,CAAY,CAAE,CAAA,EAAA,CAAA,CAAA,QAEhB,EAAa,MAAM,CAAC,OAAO,OAAO,CAAC,CAwCzC,SAAS,EAAM,CAAM,CAAE,CAAI,CAAE,CAAM,CAAE,CAAM,CAAE,CAAM,EACjD,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,IAC1B,AAD+B,CACzB,CAAC,EAAS,EAAE,CAAG,CAAM,CAAC,EAAE,CAAG,CAAI,CAAK,EAAJ,EAAM,AAEhD,CASA,SAAS,EAAQ,CAAM,CAAE,CAAI,EAC3B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAO,MAAM,CAAE,IAAK,AACtC,CAAM,CAAC,EAAE,EAAI,CAAI,CAAK,EAAJ,EAAM,AAE5B,CAyBA,SAAS,EAAS,CAAI,MAKhB,QAFJ,CAFA,EAAS,QAAQ,EAAG,EAEhB,OAAO,QAAQ,CAAC,IAAc,GAAP,AAIvB,aAAgB,YAClB,CAD+B,CACzB,IAAI,EAAW,GACZ,YAAY,MAAM,CAAC,GAC5B,EAAM,EAD6B,EACzB,EAAW,EAAK,MAAM,CAAE,EAAK,UAAU,CAAE,EAAK,UAAU,GAElE,EAAM,OAAO,IAAI,CAAC,GAClB,EAAS,QAAQ,EAAG,GAGf,EACT,CAWA,GATA,EAAO,OAAO,CAAG,CACf,OA5FF,SAAS,AAAO,CAAI,CAAE,CAAW,EAC/B,GAAoB,IAAhB,EAAK,MAAM,CAAQ,OAAO,EAC9B,GAAoB,IAAhB,EAAK,MAAM,CAAQ,OAAO,CAAI,CAAC,EAAE,CAErC,IAAM,EAAS,OAAO,WAAW,CAAC,GAC9B,EAAS,EAEb,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,MAAM,CAAE,IAAK,CACpC,IAAM,EAAM,CAAI,CAAC,EAAE,CACnB,EAAO,GAAG,CAAC,EAAK,GAChB,GAAU,EAAI,MAAM,AACtB,QAEA,AAAI,EAAS,EACJ,IAAI,EAAW,EAAO,GADL,GACW,CAAE,EAAO,UAAU,CAAE,GAGnD,CACT,EA2EE,KAAM,EACN,cAtCF,SAAS,AAAc,CAAG,SACpB,AAAJ,EAAQ,MAAM,GAAK,EAAI,MAAM,CAAC,UAAU,CAC/B,CADiC,CAC7B,MAAM,CAGZ,EAAI,MAAM,CAAC,KAAK,CAAC,EAAI,UAAU,CAAE,EAAI,UAAU,CAAG,EAAI,MAAM,CACrE,WAiCE,EACA,OAAQ,CACV,EAGI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,CAChC,CADkC,EAC9B,CACF,IAAM,EAAA,CAAA,0FAEN,EAAO,OAAO,CAAC,IAAI,CAAG,SAAU,CAAM,CAAE,CAAI,CAAE,CAAM,CAAE,CAAM,CAAE,CAAM,EAC9D,EAAS,GAAI,EAAM,EAAQ,EAAM,EAAQ,EAAQ,GAChD,EAAW,IAAI,CAAC,EAAQ,EAAM,EAAQ,EAAQ,EACrD,EAEA,EAAO,OAAO,CAAC,MAAM,CAAG,SAAU,CAAM,CAAE,CAAI,EACxC,EAAO,MAAM,CAAG,GAAI,EAAQ,EAAQ,GACnC,EAAW,MAAM,CAAC,EAAQ,EACjC,CACF,CAAE,MAAO,EAAG,CAEZ,uDCjIF,aAEA,IAAM,EAAQ,OAAO,SACf,EAAO,OAAO,QAmDpB,EAAO,OAAO,CA7Cd,EA6CiB,IAtCf,AAPI,YAOQ,CAAW,CAAE,CACvB,IAAI,CAAC,EAAM,CAAG,KACZ,IAAI,CAAC,OAAO,GACZ,IAAI,CAAC,EAAK,EACZ,EACA,IAAI,CAAC,WAAW,CAAG,GAAe,IAClC,IAAI,CAAC,IAAI,CAAG,EAAE,CACd,IAAI,CAAC,OAAO,CAAG,CACjB,CAQA,IAAI,CAAG,CAAE,CACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GACf,IAAI,CAAC,EAAK,EACZ,CAOA,CAAC,EAAK,EAAG,CACP,GAAI,IAAI,CAAC,OAAO,GAAK,IAAI,CAAC,WAAW,EAAE,AAEnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAE,CACpB,IAAM,EAAM,IAAI,CAAC,IAAI,CAAC,KAAK,GAE3B,IAAI,CAAC,OAAO,GACZ,EAAI,IAAI,CAAC,EAAM,CACjB,CACF,CACF,uDCpDA,iBAuBI,EArBE,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,aAAE,CAAW,CAAE,CAAA,EAAA,CAAA,CAAA,QAEf,EAAa,MAAM,CAAC,OAAO,OAAO,CAAC,CACnC,EAAU,OAAO,IAAI,CAAC,CAAC,EAAM,EAAM,IAAM,IAAK,EAC9C,EAAqB,OAAO,sBAC5B,EAAe,OAAO,gBACtB,EAAY,OAAO,YACnB,EAAW,OAAO,WAClB,EAAS,OAAO,SAwctB,SAAS,EAAc,CAAK,EAC1B,IAAI,CAAC,EAAS,CAAC,IAAI,CAAC,GACpB,IAAI,CAAC,EAAa,EAAI,EAAM,MAAM,AACpC,CAQA,SAAS,EAAc,CAAK,EAG1B,GAFA,IAAI,CAAC,EAAa,EAAI,EAAM,MAAM,CAGhC,IAAI,CAAC,EAAmB,CAAC,WAAW,CAAG,GACvC,IAAI,CAAC,EAAa,EAAI,IAAI,CAAC,EAAmB,CAAC,WAAW,CAC1D,YACA,IAAI,CAAC,EAAS,CAAC,IAAI,CAAC,GAItB,IAAI,CAAC,EAAO,CAAG,AAAI,WAAW,6BAC9B,IAAI,CAAC,EAAO,CAAC,IAAI,CAAG,oCACpB,IAAI,CAAC,EAAO,CAAC,EAAY,CAAG,KAC5B,IAAI,CAAC,cAAc,CAAC,OAAQ,GAS5B,IAAI,CAAC,KAAK,EACZ,CAQA,SAAS,EAAe,CAAG,EAOzB,GAFA,IAAI,CAAC,EAAmB,CAAC,QAAQ,CAAG,KAEhC,IAAI,CAAC,EAAO,CAAE,YAChB,IAAI,CAAC,EAAU,CAAC,IAAI,CAAC,EAAO,EAI9B,CAAG,CAAC,EAAY,CAAG,KACnB,IAAI,CAAC,EAAU,CAAC,EAClB,CAjEA,EAAO,OAAO,CAlbd,EAkbiB,IAlbX,AAyBJ,YAAY,CAAO,CAAE,CAAQ,CAAE,CAAU,CAAE,CACzC,IAAI,CAAC,WAAW,CAAgB,EAAb,EACnB,IAAI,CAAC,QAAQ,CAAG,GAAW,CAAC,EAC5B,IAAI,CAAC,UAAU,MACe,IAA5B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAiB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAG,KACpE,IAAI,CAAC,SAAS,CAAG,CAAC,CAAC,EACnB,IAAI,CAAC,QAAQ,CAAG,KAChB,IAAI,CAAC,QAAQ,CAAG,KAEhB,IAAI,CAAC,MAAM,CAAG,KAET,GAKH,GAAc,IAAI,GALF,IAEqB,CAGX,GAHxB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC1B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC9B,GACoB,CAE9B,CAKA,WAAW,eAAgB,CACzB,MAAO,oBACT,CAQA,OAAQ,CACN,IAAM,EAAS,CAAC,EAiBhB,OAfI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CACzC,EAAO,0BAA0B,CAAG,EAAA,EAElC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CACzC,EAAO,0BAA0B,EAAG,CAAA,EAElC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CACrC,EAAO,sBAAsB,CAAG,IAAI,CAAC,QAAQ,CAAC,mBAAA,AAAmB,EAE/D,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACnC,CADqC,CAC9B,sBAAsB,CAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACnB,MAArC,AAA2C,IAAvC,CAAC,QAAQ,CAAC,mBAAmB,GAC1C,EAAO,sBAAsB,EAAG,CAAA,EAG3B,CACT,CASA,OAAO,CAAc,CAAE,CAOrB,OANA,EAAiB,IAAI,CAAC,eAAe,CAAC,GAEtC,IAAI,CAAC,MAAM,CAAG,IAAI,CAAC,SAAS,CACxB,IAAI,CAAC,cAAc,CAAC,GACpB,IAAI,CAAC,cAAc,CAAC,GAEjB,IAAI,CAAC,MAAM,AACpB,CAOA,SAAU,CAMR,GALI,IAAI,CAAC,QAAQ,EAAE,CACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,GACnB,IAAI,CAAC,QAAQ,CAAG,MAGd,IAAI,CAAC,QAAQ,CAAE,CACjB,IAAM,EAAW,IAAI,CAAC,QAAQ,CAAC,EAAU,CAEzC,IAAI,CAAC,QAAQ,CAAC,KAAK,GACnB,IAAI,CAAC,QAAQ,CAAG,KAEZ,GACF,EACE,AAAI,KAFM,CAGR,gEAIR,CACF,CASA,eAAe,CAAM,CAAE,CACrB,IAAM,EAAO,IAAI,CAAC,QAAQ,CACpB,EAAW,EAAO,IAAI,CAAC,AAAC,KAEQ,IAAjC,EAAK,uBAAuB,GAC3B,EAAO,0BAAA,AAA0B,KAClC,EAAO,sBAAsB,EACE,AAA7B,EAAD,KAAM,mBAAmB,GACc,UAApC,OAAO,EAAK,mBAAmB,IAC9B,EAAK,mBAAmB,CAAG,EAAO,sBAAA,CAAA,CAAuB,GAC9D,CAAoC,iBAA7B,EAAK,mBAAmB,GAC9B,CAAC,EAAO,sBAAA,AAAsB,EAChC,CAOJ,GAAI,CAAC,EACH,MAAU,AAAJ,EADO,IACG,gDAqBlB,OAlBI,EAAK,uBAAuB,EAAE,CAChC,EAAS,0BAA0B,CAAG,EAAA,EAEpC,EAAK,uBAAuB,EAAE,AAChC,GAAS,0BAA0B,EAAG,CAAA,EAEA,UAApC,AAA8C,OAAvC,EAAK,mBAAmB,GACjC,EAAS,sBAAsB,CAAG,EAAK,mBAAA,AAAmB,EAEpB,UAApC,AAA8C,OAAvC,EAAK,mBAAmB,CACjC,EAAS,sBAAsB,CAAG,EAAK,mBAAmB,GAEtB,IAApC,EAAS,sBAAsB,GACF,IAA7B,EAAK,mBAAmB,AAAK,GAC7B,AACA,OAAO,EAAS,sBAAsB,CAGjC,CACT,CASA,eAAe,CAAQ,CAAE,CACvB,IAAM,EAAS,CAAQ,CAAC,EAAE,CAE1B,IAC4C,IAA1C,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EACrC,EAAO,0BAA0B,CAEjC,CADA,KACM,AAAI,MAAM,qDAGlB,GAAK,CAAD,CAAQ,sBAAsB,EAAE,AAI7B,IACiC,IAAtC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACa,UAA7C,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACvC,EAAO,sBAAsB,CAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAEnE,CADA,KACM,AAAI,MACR,2DAEJ,KAXmD,UAA7C,AAAuD,OAAhD,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAC1C,EAAO,sBAAsB,CAAG,IAAI,CAAC,QAAQ,CAAC,mBAAA,AAAmB,EAYrE,OAAO,CACT,CASA,gBAAgB,CAAc,CAAE,CAkD9B,OAjDA,EAAe,OAAO,CAAC,AAAC,IACtB,OAAO,IAAI,CAAC,GAAQ,OAAO,CAAC,AAAC,IAC3B,IAAI,EAAQ,CAAM,CAAC,EAAI,CAEvB,GAAI,EAAM,MAAM,CAAG,EACjB,CADoB,KACd,AAAI,MAAM,CAAC,WAAW,EAAE,EAAI,+BAA+B,CAAC,EAKpE,GAFA,EAAQ,CAAK,CAAC,EAAE,CAEJ,0BAA0B,CAAlC,GACF,IAAc,IAAV,EAAgB,CAClB,IAAM,EAAM,CAAC,EACb,GAAI,CAAC,OAAO,SAAS,CAAC,IAAQ,EAAM,GAAK,EAAM,GAC7C,CADiD,KAC3C,AAAI,UACR,CAAC,6BAA6B,EAAE,EAAI,GAAG,EAAE,EAAA,CAAO,EAGpD,EAAQ,CACV,MAAO,GAAI,CAAC,IAAI,CAAC,SAAS,CACxB,CAD0B,KACpB,AAAI,UACR,CAAC,6BAA6B,EAAE,EAAI,GAAG,EAAE,EAAA,CAAO,CAEpD,MACK,GAAY,2BAAR,EAAkC,CAC3C,IAAM,EAAM,CAAC,EACb,GAAI,CAAC,OAAO,SAAS,CAAC,IAAQ,EAAM,GAAK,EAAM,GAC7C,CADiD,KAC3C,AAAI,UACR,CAAC,6BAA6B,EAAE,EAAI,GAAG,EAAE,EAAA,CAAO,EAGpD,EAAQ,CACV,MAAO,GACG,+BAAR,GACQ,8BACR,CADA,GAEA,IAAc,IAAV,EAAgB,AAClB,MAAM,AAAI,UACR,CAAC,6BAA6B,EAAE,EAAI,GAAG,EAAE,EAAA,CAAO,CAEpD,MAEA,MAAM,AAAI,MAAM,CAAC,mBAAmB,EAAE,EAAI,CAAC,CAAC,EAG9C,CAAM,CAAC,EAAI,CAAG,CAChB,EACF,GAEO,CACT,CAUA,WAAW,CAAI,CAAE,CAAG,CAAE,CAAQ,CAAE,CAC9B,EAAY,GAAG,CAAC,AAAC,IACf,IAAI,CAAC,WAAW,CAAC,EAAM,EAAK,CAAC,EAAK,KAChC,IACA,EAAS,EAAK,EAChB,EACF,EACF,CAUA,SAAS,CAAI,CAAE,CAAG,CAAE,CAAQ,CAAE,CAC5B,EAAY,GAAG,CAAC,AAAC,IACf,IAAI,CAAC,SAAS,CAAC,EAAM,EAAK,CAAC,EAAK,KAC9B,IACA,EAAS,EAAK,EAChB,EACF,EACF,CAUA,YAAY,CAAI,CAAE,CAAG,CAAE,CAAQ,CAAE,CAC/B,IAAM,EAAW,IAAI,CAAC,SAAS,CAAG,SAAW,SAE7C,GAAI,CAAC,IAAI,CAAC,QAAQ,CAAE,CAClB,IAAM,EAAM,CAAA,EAAG,EAAS,gBAAgB,CAAC,CACnC,EACwB,UAA5B,OAAO,IAAI,CAAC,MAAM,CAAC,EAAI,CACnB,EAAK,oBAAoB,CACzB,IAAI,CAAC,MAAM,CAAC,EAAI,CAEtB,IAAI,CAAC,QAAQ,CAAG,EAAK,gBAAgB,CAAC,CACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,YACnC,CACF,GACA,IAAI,CAAC,QAAQ,CAAC,EAAmB,CAAG,IAAI,CACxC,IAAI,CAAC,QAAQ,CAAC,EAAa,CAAG,EAC9B,IAAI,CAAC,QAAQ,CAAC,EAAS,CAAG,EAAE,CAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAS,GAC1B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAQ,EAC3B,CAEA,IAAI,CAAC,QAAQ,CAAC,EAAU,CAAG,EAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAChB,GAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAE7B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAClB,IAAM,EAAM,IAAI,CAAC,QAAQ,CAAC,EAAO,CAEjC,GAAI,EAAK,CACP,IAAI,CAAC,QAAQ,CAAC,KAAK,GACnB,IAAI,CAAC,QAAQ,CAAG,KAChB,EAAS,GACT,MACF,CAEA,IAAM,EAAO,EAAW,MAAM,CAC5B,IAAI,CAAC,QAAQ,CAAC,EAAS,CACvB,IAAI,CAAC,QAAQ,CAAC,EAAa,EAGzB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,EAAE,AAC3C,IAAI,CAAC,QAAQ,CAAC,KAAK,GACnB,IAAI,CAAC,QAAQ,CAAG,OAEhB,IAAI,CAAC,QAAQ,CAAC,EAAa,CAAG,EAC9B,IAAI,CAAC,QAAQ,CAAC,EAAS,CAAG,EAAE,CAExB,GAAO,IAAI,CAAC,MAAM,CAAC,CAAA,EAAG,EAAS,oBAAoB,CAAC,CAAC,EAAE,AACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,IAIvB,EAAS,KAAM,EACjB,EACF,CAUA,UAAU,CAAI,CAAE,CAAG,CAAE,CAAQ,CAAE,CAC7B,IAAM,EAAW,IAAI,CAAC,SAAS,CAAG,SAAW,SAE7C,GAAI,CAAC,IAAI,CAAC,QAAQ,CAAE,CAClB,IAAM,EAAM,CAAA,EAAG,EAAS,gBAAgB,CAAC,CACnC,EACwB,UAA5B,OAAO,IAAI,CAAC,MAAM,CAAC,EAAI,CACnB,EAAK,oBAAoB,CACzB,IAAI,CAAC,MAAM,CAAC,EAAI,CAEtB,IAAI,CAAC,QAAQ,CAAG,EAAK,gBAAgB,CAAC,CACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,YACnC,CACF,GAEA,IAAI,CAAC,QAAQ,CAAC,EAAa,CAAG,EAC9B,IAAI,CAAC,QAAQ,CAAC,EAAS,CAAG,EAAE,CAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAQ,EAC3B,CAEA,IAAI,CAAC,QAAQ,CAAC,EAAU,CAAG,EAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAK,YAAY,CAAE,KACrC,GAAI,CAAC,IAAI,CAAC,QAAQ,CAIhB,CAJkB,MAOpB,IAAI,EAAO,EAAW,MAAM,CAC1B,IAAI,CAAC,QAAQ,CAAC,EAAS,CACvB,IAAI,CAAC,QAAQ,CAAC,EAAa,EAGzB,IACF,CADO,CACA,IAAI,EAAW,EAAK,MAAM,CAAE,EAAK,UAAU,CAAE,EAAK,MAAM,CAAG,EAAA,EAOpE,IAAI,CAAC,QAAQ,CAAC,EAAU,CAAG,KAE3B,IAAI,CAAC,QAAQ,CAAC,EAAa,CAAG,EAC9B,IAAI,CAAC,QAAQ,CAAC,EAAS,CAAG,EAAE,CAExB,GAAO,IAAI,CAAC,MAAM,CAAC,CAAA,EAAG,EAAS,oBAAoB,CAAC,CAAC,EAAE,AACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAGrB,EAAS,KAAM,EACjB,EACF,CACF,uDC5cA,aAEA,GAAM,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QAEV,SAAE,CAAO,CAAE,CAAA,EAAA,CAAA,CAAA,QAoDjB,SAAS,EAAa,CAAG,EACvB,IAAM,EAAM,EAAI,MAAM,CAClB,EAAI,EAER,KAAO,EAAI,GACT,CADc,EACV,CAAU,IAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,EAEtB,CAFyB,QAGpB,GAAI,CAAU,IAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,IAAM,CAEnC,GACE,EAAI,IAAM,GACV,AAAC,CAAa,KAAV,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KACxB,CAAU,IAAT,CAAG,CAAC,EAAK,AAAH,CAAO,EAAM,IAEpB,CAFyB,MAElB,EAGT,GALsC,AAKjC,CACP,MAAO,GAAI,CAAU,IAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,IAAM,CAEnC,GACE,EAAI,GAAK,GACT,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KACxB,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KACZ,MAAX,CAAG,CAAC,EAAE,EAAa,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KAChC,MAAX,CAAG,CAAC,EAAE,EAAa,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,IAE5C,CAFkD,MAE3C,EAGT,GAAK,CACP,KAAqC,CAA9B,IAAc,AAAV,IAAC,CAAG,CAAC,EAAE,AAAG,AAN+D,CAM3D,EAAM,KAG3B,EAAI,GAAK,GACT,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KACxB,AAAC,CAAa,KAAV,CAAC,EAAI,EAAK,AAAH,CAAO,EAAM,KACxB,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KACZ,MAAX,CAAG,CAAC,EAAE,EAAa,CAAc,IAAb,CAAG,CAAC,EAAI,EAAE,AAAG,CAAI,EAAM,KAChC,MAAX,CAAG,CAAC,EAAE,EAAa,CAAG,CAAC,EAAI,EAAE,CAAG,KACjC,CAAG,CAAC,EAAE,CAAG,IAOX,CAPgB,MAOT,EAFP,GAAK,CACP,CAN+B,AAWjC,MALS,AAKF,EACT,CA4BA,GAPA,EAAO,OAAO,CAAG,CACf,OAbF,SAAS,AAAO,CAAK,EACnB,OACE,GACiB,UAAjB,OAAO,GACsB,YAA7B,OAAO,EAAM,WAAW,EACF,UAAtB,OAAO,EAAM,IAAI,EACO,YAAxB,EACA,KADO,EAAM,MAAM,GACY,SAA9B,CAAK,CAAC,OAAO,WAAW,CAAC,EACM,SAA9B,CAAK,CAAC,OAAO,WAAW,CAAC,AAAK,CAAM,AAE1C,EAIE,kBAhGF,SAAS,AAAkB,CAAI,EAC7B,OACG,GAAQ,KACP,GAAQ,MACC,OAAT,GACS,OAAT,GACS,OAAT,GACD,GAAQ,KAAQ,GAAQ,IAE7B,EAwFE,YAAa,EACb,WApHiB,CACjB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC7C,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAC9C,AA4GD,AA7GiD,EA+G7C,EACF,EAAO,IADG,EA/GiD,CAgH7C,CAAC,WAAW,CAAG,SAAU,CAAG,EACxC,OAAO,EAAI,MAAM,CAAG,GAAK,EAAa,GAAO,EAAO,EACtD,OACiC,GAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB,CACtE,CADwE,EACpE,CACF,IAAM,EAAA,CAAA,8FAEN,EAAO,OAAO,CAAC,WAAW,CAAG,SAAU,CAAG,EACxC,OAAO,EAAI,MAAM,CAAG,GAAK,EAAa,GAAO,EAAY,EAC3D,CACF,CAAE,MAAO,EAAG,CAEZ,uDCtJF,aAEA,GAAM,CAAE,UAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,QAEZ,EAAA,EAAA,CAAA,CAAA,QACA,cACJ,CAAY,cACZ,CAAY,aACZ,CAAW,CACX,YAAU,CACX,CAAA,EAAA,CAAA,CAAA,QACK,QAAE,CAAM,eAAE,CAAa,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QACjC,mBAAE,CAAiB,aAAE,CAAW,CAAE,CAAA,EAAA,CAAA,CAAA,QAElC,EAAa,MAAM,CAAC,OAAO,OAAO,CAAC,CAmrBzC,EAAO,OAAO,CApqBd,EAoqBiB,IApqBX,QAAiB,EAiBrB,YAAY,EAAU,CAAC,CAAC,CAAE,CACxB,KAAK,GAEL,IAAI,CAAC,uBAAuB,MACS,IAAnC,EAAQ,sBAAsB,EAC1B,EAAQ,sBAAsB,CAEpC,EADM,EACF,CAAC,WAAW,CAAG,EAAQ,UAAU,EAAI,CAAY,CAAC,EAAE,CACxD,IAAI,CAAC,WAAW,CAAG,EAAQ,UAAU,EAAI,CAAC,EAC1C,IAAI,CAAC,SAAS,CAAG,CAAC,CAAC,EAAQ,QAAQ,CACnC,IAAI,CAAC,WAAW,CAAwB,EAArB,EAAQ,UAAU,CACrC,IAAI,CAAC,mBAAmB,CAAG,CAAC,CAAC,EAAQ,kBAAkB,CACvD,IAAI,CAAC,EAAW,CAAG,OAEnB,IAAI,CAAC,cAAc,CAAG,EACtB,IAAI,CAAC,QAAQ,CAAG,EAAE,CAElB,IAAI,CAAC,WAAW,EAAG,EACnB,IAAI,CAAC,cAAc,CAAG,EACtB,IAAI,CAAC,KAAK,MAAG,EACb,IAAI,CAAC,WAAW,CAAG,EACnB,IAAI,CAAC,OAAO,EAAG,EACf,IAAI,CAAC,IAAI,EAAG,EACZ,IAAI,CAAC,OAAO,CAAG,EAEf,IAAI,CAAC,mBAAmB,CAAG,EAC3B,IAAI,CAAC,cAAc,CAAG,EACtB,IAAI,CAAC,UAAU,CAAG,EAAE,CAEpB,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,KAAK,EAAG,EACb,IAAI,CAAC,MAAM,EACb,CAUA,AAXgB,OAWT,CAAK,CAAE,CAAQ,CAAE,CAAE,CAAE,CAC1B,GAAqB,IAAjB,IAAI,CAAC,OAAO,EAAa,OAAI,CAAC,MAAM,CAAc,GAAV,IAAiB,IAE7D,IAAI,CAAC,cAAc,EAAI,EAAM,MAAM,CACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GACnB,IAAI,CAAC,SAAS,CAAC,EACjB,CASA,QAAQ,CAAC,CAAE,CAGT,GAFA,IAAI,CAAC,cAAc,EAAI,EAEnB,IAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,GAE7D,GAAI,EAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAE,CAC/B,IAAM,EAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAO5B,OANA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAG,IAAI,EACrB,EAAI,MAAM,CACV,EAAI,UAAU,CAAG,EACjB,EAAI,MAAM,CAAG,GAGR,IAAI,EAAW,EAAI,MAAM,CAAE,EAAI,UAAU,CAAE,EACpD,CAEA,IAAM,EAAM,OAAO,WAAW,CAAC,GAE/B,EAAG,CACD,IAAM,EAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CACtB,EAAS,EAAI,MAAM,CAAG,EAExB,GAAK,EAAI,MAAM,CACjB,CADmB,CACf,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAI,IAE/B,EAAI,GAAG,CAAC,IAAI,WAAW,EAAI,MAAM,CAAE,EAAI,UAAU,CAAE,GAAI,GACvD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAG,IAAI,EACrB,EAAI,MAAM,CACV,EAAI,UAAU,CAAG,EACjB,EAAI,MAAM,CAAG,IAIjB,GAAK,EAAI,MAAM,AACjB,OAAS,EAAI,EAAG,AAEhB,OAAO,CACT,CAQA,UAAU,CAAE,CAAE,CACZ,IAAI,CAAC,KAAK,EAAG,EAEb,GAAG,AACD,OAAQ,IAAI,CAAC,MAAM,EACjB,KAAK,EACH,IAAI,CAAC,OAAO,CAAC,GACb,KACF,KAAK,GACH,IAAI,CAAC,kBAAkB,CAAC,GACxB,KACF,KAAK,GACH,IAAI,CAAC,kBAAkB,CAAC,GACxB,KACF,KAAK,GACH,IAAI,CAAC,OAAO,GACZ,KACF,KAAK,GACH,IAAI,CAAC,OAAO,CAAC,GACb,KACF,KAAK,GACL,KAAK,AAnJO,EAoJV,IAAI,CAAC,KAAK,EAAG,EACb,MACJ,OACO,IAAI,CAAC,KAAK,CAAE,AAEjB,AAAC,IAAI,CAAC,QAAQ,EAAE,GACtB,CAQA,QAAQ,CAAE,CAAE,CACV,GAAI,IAAI,CAAC,cAAc,CAAG,EAAG,CAC3B,IAAI,CAAC,KAAK,EAAG,EACb,MACF,CAEA,IAAM,EAAM,IAAI,CAAC,OAAO,CAAC,GAEzB,GAAI,CAAU,GAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,EAAM,YAS5B,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,+BACA,EACA,KACA,8BAOJ,IAAM,EAAa,CAAU,GAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,GAEvC,GAAI,GAAc,CAAC,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,CAAE,YASpE,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,sBACA,EACA,KACA,4BAWJ,GAJA,IAAI,CAAC,IAAI,CAAG,CAAU,IAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,IAChC,IAAI,CAAC,OAAO,CAAY,GAAT,CAAG,CAAC,EAAE,CACrB,IAAI,CAAC,cAAc,CAAY,IAAT,CAAG,CAAC,EAAE,CAEP,IAAjB,IAAI,CAAC,OAAO,CAAW,CACzB,GAAI,EAAY,YASd,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,qBACA,GACA,KACA,4BAOJ,GAAI,CAAC,IAAI,CAAC,WAAW,CAAE,YASrB,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,oBACA,EACA,KACA,0BAOJ,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,WAAW,AACjC,MAAO,GAAI,AAAiB,QAAb,CAAC,OAAO,EAAa,AAAiB,QAAb,CAAC,OAAO,CAAW,CACzD,GAAI,IAAI,CAAC,WAAW,CAAE,YASpB,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAA,CAAE,CAChC,GACA,KACA,yBAOJ,KAAI,CAAC,WAAW,CAAG,CACrB,KAAuD,CAAhD,KAAI,IAAI,CAAC,OAAO,CAAG,CAAA,KAAQ,IAAI,CAAC,OAAO,CAAG,EAAA,EA0C1C,YASL,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAA,CAAE,EAChC,EACA,KACA,0BA/CF,GAAI,CAAC,IAAI,CAAC,IAAI,CAAE,YASd,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,mBACA,EACA,KACA,wBAOJ,GAAI,EAAY,YASd,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,sBACA,EACA,KACA,4BAOJ,GACE,IAAI,CAAC,cAAc,CAAG,KACJ,IAAjB,IAAI,CAAC,OAAO,EAAqC,IAAxB,IAAI,CAAC,cAAc,CAC7C,YASA,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,CAAA,CAAE,CAC/C,GACA,KACA,yCAMN,CAgBA,GAHI,AAAC,IAAI,CAAC,IAAI,EAAK,EAAD,EAAK,CAAC,WAAW,GAAE,IAAI,CAAC,WAAW,CAAG,IAAI,CAAC,OAAA,AAAO,EACpE,IAAI,CAAC,OAAO,CAAG,CAAU,IAAT,CAAG,CAAC,EAAE,AAAG,CAAI,EAAM,IAE/B,IAAI,CAAC,SAAS,EAAE,AAClB,GAAI,CAAC,IAAI,CAAC,OAAO,CAAE,YASjB,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,oBACA,EACA,KACA,wBAKJ,MACK,GAAI,IAAI,CAAC,OAAO,CAAE,YASvB,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,sBACA,EACA,KACA,0BAOA,CAAwB,UAApB,CAAC,cAAc,CAAU,IAAI,CAAC,MAAM,CA9UlB,EA+UO,AADc,MACtC,IAAI,CAAC,cAAc,CAAU,IAAI,CAAC,MAAM,CA9UvB,EA8U0B,AAC/C,IAAI,CAAC,UAAU,CAAC,EACvB,CAQA,mBAAmB,CAAE,CAAE,CACrB,GAAI,IAAI,CAAC,cAAc,CAAG,EAAG,CAC3B,IAAI,CAAC,KAAK,EAAG,EACb,MACF,CAEA,IAAI,CAAC,cAAc,CAAG,IAAI,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,GACnD,IAAI,CAAC,UAAU,CAAC,EAClB,CAQA,mBAAmB,CAAE,CAAE,CACrB,GAAI,IAAI,CAAC,cAAc,CAAG,EAAG,CAC3B,IAAI,CAAC,KAAK,EAAG,EACb,MACF,CAEA,IAAM,EAAM,IAAI,CAAC,OAAO,CAAC,GACnB,EAAM,EAAI,YAAY,CAAC,GAM7B,GAAI,EAAM,KAAK,GAAG,AAAkB,CAAjB,GAAG,KAAK,GASzB,EARc,CAQX,AAT4B,GACb,CAAC,WAAW,CAC5B,WACA,0DACA,EACA,KACA,2CAOJ,IAAI,CAAC,cAAc,CAAG,AAAM,KAAK,GAAG,CAAC,GAAG,EAAM,EAAI,YAAY,CAAC,GAC/D,IAAI,CAAC,UAAU,CAAC,EAClB,CAQA,WAAW,CAAE,CAAE,CACb,GAAI,IAAI,CAAC,cAAc,EAAI,IAAI,CAAC,OAAO,CAAG,IACxC,EAD8C,EAC1C,CAAC,mBAAmB,EAAI,IAAI,CAAC,cAAc,CAC3C,IAAI,CAAC,mBAAmB,CAAG,IAAI,CAAC,WAAW,EAAI,IAAI,CAAC,WAAW,CAAG,GAAG,YASvE,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,6BACA,EACA,KACA,sCAQF,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,MAAM,CA5ZhB,EA4ZmB,AAC3B,IAAI,CAAC,MAAM,EAClB,CADqB,AAQrB,SAAU,CACR,GAAI,IAAI,CAAC,cAAc,CAAG,EAAG,CAC3B,IAAI,CAAC,KAAK,EAAG,EACb,MACF,CAEA,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,OAAO,CAAC,GAC1B,IAAI,CAAC,MAAM,CA3aE,CA4af,CADgB,AAShB,QAAQ,CAAE,CAAE,CACV,IAAI,EAAO,EAEX,GAAI,IAAI,CAAC,cAAc,CAAE,CACvB,GAAI,IAAI,CAAC,cAAc,CAAG,IAAI,CAAC,cAAc,CAAE,CAC7C,IAAI,CAAC,KAAK,EAAG,EACb,MACF,CAEA,EAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAGrC,IAAI,CAAC,OAAO,EACZ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAG,IAAI,CAAC,KAAK,CAAC,EAAA,AAAE,GAAM,GACpE,AACA,EAAO,EAAM,IAAI,CAAC,KAAK,CAE3B,CAEA,GAAI,IAAI,CAAC,OAAO,CAAG,EAAM,YACvB,IAAI,CAAC,cAAc,CAAC,EAAM,GAI5B,GAAI,IAAI,CAAC,WAAW,CAAE,CACpB,IAAI,CAAC,MAAM,GAAG,AACd,IAAI,CAAC,UAAU,CAAC,EAAM,GACtB,MACF,CAEI,EAAK,MAAM,EAAE,CAKf,IAAI,CAAC,cAAc,CAAG,IAAI,CAAC,mBAAmB,CAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAGvB,IAAI,CAAC,WAAW,CAAC,EACnB,CASA,WAAW,CAAI,CAAE,CAAE,CAAE,CACO,AAE1B,IAF8B,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,CAEzD,UAAU,CAAC,EAAM,IAAI,CAAC,IAAI,CAAE,CAAC,EAAK,KAClD,GAAI,EAAK,OAAO,EAAG,GAEnB,GAAI,EAAI,MAAM,CAAE,CAEd,GADA,IAAI,CAAC,cAAc,EAAI,EAAI,MAAM,CAC7B,IAAI,CAAC,cAAc,CAAG,IAAI,CAAC,WAAW,EAAI,IAAI,CAAC,WAAW,CAAG,EAAG,YASlE,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,6BACA,EACA,KACA,sCAOJ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EACvB,CAEA,IAAI,CAAC,WAAW,CAAC,OACb,IAAI,CAAC,MAAM,EAAe,GAAV,CAAc,CAAC,SAAS,CAAC,EAC/C,EACF,CAQA,YAAY,CAAE,CAAE,CACd,GAAI,CAAC,IAAI,CAAC,IAAI,CAAE,CACd,IAAI,CAAC,MAAM,GAAG,AACd,MACF,CAEA,IAAM,EAAgB,IAAI,CAAC,cAAc,CACnC,EAAY,IAAI,CAAC,UAAU,CAOjC,GALA,IAAI,CAAC,mBAAmB,CAAG,EAC3B,IAAI,CAAC,cAAc,CAAG,EACtB,IAAI,CAAC,WAAW,CAAG,EACnB,IAAI,CAAC,UAAU,CAAG,EAAE,CAEC,IAAjB,IAAI,CAAC,OAAO,CAAQ,CACtB,IAAI,EAGF,EADuB,cAAc,CAAnC,IAAI,CAAC,WAAW,CACX,EAAO,EAAW,GACK,AAArB,eAAoC,KAAhC,CAAC,WAAW,CAClB,EAAc,EAAO,EAAW,IACT,QAAQ,CAA7B,IAAI,CAAC,WAAW,CAClB,IAAI,KAAK,GAET,EAGL,IAAI,CAAC,uBAAuB,EAC9B,AADgC,IAC5B,CAAC,IAAI,CAAC,UAAW,GAAM,GAC3B,IAAI,CAAC,MAAM,GAAG,EAEd,IAAI,CAAC,MAAM,GAAG,AACd,aAAa,KACX,IAAI,CAAC,IAAI,CAAC,UAAW,GAAM,GAC3B,IAAI,CAAC,MAAM,GAAG,AACd,IAAI,CAAC,SAAS,CAAC,EACjB,GAEJ,KAAO,CACL,IAAM,EAAM,EAAO,EAAW,GAE9B,GAAI,CAAC,IAAI,CAAC,mBAAmB,EAAI,CAAC,EAAY,GAAM,YASlD,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,MACA,0BACA,EACA,KACA,uBAOA,CA7jBQ,QA6jBJ,CAAC,MAAM,EAAkB,GAAb,CAAiB,CAAC,uBAAuB,EAAE,AAC7D,IAAI,CAAC,IAAI,CAAC,UAAW,GAAK,GAC1B,IAAI,CAAC,MAAM,GAAG,EAEd,IAAI,CAAC,MAAM,GAAG,AACd,aAAa,KACX,IAAI,CAAC,IAAI,CAAC,UAAW,GAAK,GAC1B,IAAI,CAAC,MAAM,GAAG,AACd,IAAI,CAAC,SAAS,CAAC,EACjB,GAEJ,CACF,CASA,eAAe,CAAI,CAAE,CAAE,CAAE,CACvB,GAAI,AAAiB,QAAb,CAAC,OAAO,CAAW,CACzB,GAAoB,GAAG,CAAnB,EAAK,MAAM,CACb,IAAI,CAAC,KAAK,EAAG,EACb,IAAI,CAAC,IAAI,CAAC,WAAY,KAAM,GAC5B,IAAI,CAAC,GAAG,OACH,CACL,IAAM,EAAO,EAAK,YAAY,CAAC,GAE/B,GAAI,CAAC,EAAkB,GAAO,YAS5B,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,WACA,CAAC,oBAAoB,EAAE,EAAA,CAAM,EAC7B,EACA,KACA,8BAOJ,IAAM,EAAM,IAAI,EACd,EAAK,MAAM,CACX,EAAK,UAAU,CAAG,EAClB,EAAK,MAAM,CAAG,GAGhB,GAAI,CAAC,IAAI,CAAC,mBAAmB,EAAI,CAAC,EAAY,GAAM,YASlD,EARc,CAQX,GARe,CAAC,WAAW,CAC5B,MACA,0BACA,EACA,KACA,wBAOJ,IAAI,CAAC,KAAK,CAAG,GACb,IAAI,CAAC,IAAI,CAAC,WAAY,EAAM,GAC5B,IAAI,CAAC,GAAG,EACV,CAEA,IAAI,CAAC,MAAM,GAAG,AACd,MACF,CAEI,IAAI,CAAC,uBAAuB,EAC9B,AADgC,IAC5B,CAAC,IAAI,CAAkB,IAAjB,IAAI,CAAC,OAAO,CAAY,OAAS,OAAQ,GACnD,IAAI,CAAC,MAAM,GAAG,EAEd,IAAI,CAAC,MAAM,GAAG,AACd,aAAa,KACX,IAAI,CAAC,IAAI,CAAkB,IAAjB,IAAI,CAAC,OAAO,CAAY,OAAS,OAAQ,GACnD,IAAI,CAAC,MAAM,CAhpBF,EAipBT,AADc,IACV,CAAC,SAAS,CAAC,EACjB,GAEJ,CAcA,YAAY,CAAS,CAAE,CAAO,CAAE,CAAM,CAAE,CAAU,CAAE,CAAS,CAAE,CAC7D,IAAI,CAAC,KAAK,EAAG,EACb,IAAI,CAAC,QAAQ,EAAG,EAEhB,IAAM,EAAM,IAAI,EACd,EAAS,CAAC,yBAAyB,EAAE,EAAA,CAAS,CAAG,GAMnD,OAHA,MAAM,iBAAiB,CAAC,EAAK,IAAI,CAAC,WAAW,EAC7C,EAAI,IAAI,CAAG,EACX,CAAG,CAAC,EAAY,CAAG,EACZ,CACT,CACF,uDC7rBA,iBAaI,EAXE,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QACV,CAAE,gBAAc,CAAE,CAAA,EAAA,CAAA,CAAA,QAElB,EAAA,EAAA,CAAA,CAAA,QACA,cAAE,CAAY,YAAE,CAAU,MAAE,CAAI,CAAE,CAAA,EAAA,CAAA,CAAA,QAClC,CAAE,QAAM,CAAE,mBAAiB,CAAE,CAAA,EAAA,CAAA,CAAA,QAC7B,CAAE,KAAM,CAAS,UAAE,CAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,QAE7B,EAAc,OAAO,eACrB,EAAa,OAAO,KAAK,CAAC,GAG5B,MASJ,OAAM,EASJ,KAlBsB,OAkBV,CAAM,CAAE,CAAU,CAAE,CAAY,CAAE,CAC5C,IAAI,CAAC,WAAW,CAAG,GAAc,CAAC,EAE9B,IACF,IAAI,CAAC,KADW,QACE,CAAG,EACrB,IAAI,CAAC,WAAW,CAAG,OAAO,KAAK,CAAC,IAGlC,IAAI,CAAC,OAAO,CAAG,EAEf,IAAI,CAAC,cAAc,EAAG,EACtB,IAAI,CAAC,SAAS,EAAG,EAEjB,IAAI,CAAC,cAAc,CAAG,EACtB,IAAI,CAAC,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC,MAAM,GAAG,AACd,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,EAAW,MAAG,CACrB,CAuBA,OAAO,MAAM,CAAI,CAAE,CAAO,CAAE,CAE1B,IADI,EAmCA,EAlCA,EAAQ,GACR,EAAS,EACT,GAAc,EAEd,EAAQ,IAAI,EAAE,CAChB,EAAO,EAAQ,UAAU,EAAI,EAEzB,EAAQ,YAAY,CACtB,CADwB,CAChB,YAAY,CAAC,WAEjB,SAEiB,IAAf,IAKF,EAAa,CALe,EAFN,IAOF,KAAK,CAAC,KAAA,EAG5B,CAV0C,CAU3B,EAAY,EAnFZ,CAmFe,GAnFX,EAoFnB,EAAoB,GAGtB,CAAI,CAAC,EAAE,CAAG,CAAU,CAAC,IAAoB,CACzC,CAAI,CAAC,EAAE,CAAG,CAAU,CAAC,IAAoB,CACzC,CAAI,CAAC,EAAE,CAAG,CAAU,CAAC,IAAoB,CACzC,CAAI,CAAC,EAAE,CAAG,CAAU,CAAC,IAAoB,EAG3C,EAAc,AAAC,EAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,AAAF,GAAQ,EAC1D,EAAS,GAKS,UAAU,AAA1B,OAAO,EAKP,EAHA,CAAC,CAAC,EAAQ,IAAI,EAAI,CAAA,CAAW,EACJ,SAAzB,CAAO,CAAC,AACR,EADoB,CAEP,CAAO,CAAC,EAAY,CAGpB,CADb,EAAO,OAAO,IAAI,CAAC,EAAA,EACD,MAAM,EAG1B,EAAa,EAAK,MAAM,CACxB,EAAQ,EAAQ,IAAI,EAAI,EAAQ,QAAQ,EAAI,CAAC,GAG/C,IAAI,EAAgB,EAEhB,GAAc,OAAO,AACvB,GAAU,EACV,EAAgB,KACP,EAAa,KAAK,CAC3B,GAAU,EACV,EAAgB,KAGlB,IAAM,EAAS,OAAO,WAAW,CAAC,EAAQ,EAAa,EAAS,SAchE,CAZA,CAAM,CAAC,CAYH,CAZK,CAAG,EAAQ,GAAG,CAAoB,IAAjB,EAAQ,MAAM,CAAU,EAAQ,MAAM,CAC5D,EAAQ,IAAI,GAAE,CAAM,CAAC,EAAE,EAAI,EAAA,EAE/B,CAAM,CAAC,EAAE,CAAG,EAEU,KAAK,CAAvB,EACF,EAAO,aAAa,CAAC,EAAY,GACN,KAAK,CAAvB,IACT,CAAM,CAAC,EAAE,CAAG,CAAM,CAAC,EAAE,CAAG,EACxB,EAAO,WAAW,CAAC,EAAY,EAAG,IAG/B,EAAQ,IAAI,EAAE,CAEnB,CAAM,CAAC,EAAE,EAAI,IACb,CAAM,CAAC,EAAS,EAAE,CAAG,CAAI,CAAC,EAAE,CAC5B,CAAM,CAAC,EAAS,EAAE,CAAG,CAAI,CAAC,EAAE,CAC5B,CAAM,CAAC,EAAS,EAAE,CAAG,CAAI,CAAC,EAAE,CAC5B,CAAM,CAAC,EAAS,EAAE,CAAG,CAAI,CAAC,EAAE,CAExB,GAAoB,CAAC,EAAQ,EAAK,CAElC,GACF,CAHe,CAGL,EADD,AACO,EAAM,EAAQ,EAAQ,GAC/B,CAAC,EAAO,GAGjB,EAAU,EAAM,EAAM,EAAM,EAAG,GACxB,CAAC,EAAQ,EAAK,EAhBK,CAAC,EAAQ,EAiBrC,AAjB0C,CA4B1C,MAAM,CAAI,CAAE,CAAI,CAAE,CAAI,CAAE,CAAE,CAAE,KACtB,EAEJ,QAAa,IAAT,EACF,EAAM,GADgB,IAEjB,GAAoB,UAAhB,EAA4B,KAArB,GAAsB,EAAkB,GAEnD,GAAI,CAFsD,IAE7C,OAAc,EAAK,IAAN,EAAY,CAGtC,CAHwC,AAI7C,IAAM,EAAS,OAAO,UAAU,CAAC,GAEjC,GAAI,EAAS,IACX,CADgB,KACV,AAAI,WAAW,kDAIvB,CADA,EAAM,OAAO,WAAW,CAAC,EAAI,EAAA,EACzB,aAAa,CAAC,EAAM,GAEJ,UAAhB,AAA0B,OAAnB,EACT,EAAI,KAAK,CAAC,EAAM,GAEhB,EAAI,GAAG,CAAC,EAAM,EAElB,KAhBE,CADA,EAAM,OAAO,WAAW,CAAC,EAAA,EACrB,aAAa,CAAC,EAAM,QAHxB,MAAM,AAAI,UAAU,oDAqBtB,IAAM,EAAU,CACd,CAAC,EAAY,CAAE,EAAI,MAAM,CACzB,KAAK,EACL,aAAc,IAAI,CAAC,aAAa,MAChC,EACA,WAAY,IAAI,CAAC,WAAW,CAC5B,OAAQ,EACR,UAAU,EACV,MAAM,CACR,MAEI,IAAI,CAAC,MAAM,CACb,IAAI,AADc,CACb,OAAO,CADe,AACd,CAAC,IAAI,CAAC,QAAQ,CAAE,GAAK,EAAO,EAAS,EAAG,EAErD,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAK,GAAU,EAE/C,CAUA,KAAK,CAAI,CAAE,CAAI,CAAE,CAAE,CAAE,KACf,EACA,EAcJ,GAZoB,UAAhB,AAA0B,OAAnB,GACT,EAAa,OAAO,UAAU,CAAC,GAC/B,GAAW,GACF,EAAO,IAChB,EAAa,CADU,CACL,IAAI,CACtB,GAAW,IAGX,EADA,AACa,GADN,EAAS,EAAA,EACE,MAAM,CACxB,EAAW,EAAS,QAAQ,EAG1B,EAAa,IACf,CADoB,KACd,AAAI,WAAW,oDAGvB,IAAM,EAAU,CACd,CAAC,EAAY,CAAE,EACf,KAAK,EACL,aAAc,IAAI,CAAC,aAAa,MAChC,EACA,WAAY,IAAI,CAAC,WAAW,CAC5B,OAAQ,WACR,EACA,MAAM,CACR,EAEI,EAAO,OAAO,AACZ,IAAI,CAAC,MAAM,CACb,IADkB,AACd,CAAC,OAAO,CADe,AACd,CAAC,IAAI,CAAC,WAAW,CAAE,EAAM,GAAO,EAAS,EAAG,EAEzD,IAAI,CAAC,WAAW,CAAC,GAAM,EAAO,EAAS,OAEhC,IAAI,CAAC,MAAM,CACpB,IAAI,AADqB,CACpB,OAAO,CAAC,AADqB,CACpB,IAAI,CAAC,QAAQ,CAAE,GAAM,EAAO,EAAS,EAAG,EAEtD,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAM,GAAU,EAEhD,CAUA,KAAK,CAAI,CAAE,CAAI,CAAE,CAAE,CAAE,KACf,EACA,EAcJ,GAZoB,AAAhB,UAA0B,OAAnB,GACT,EAAa,OAAO,UAAU,CAAC,GAC/B,GAAW,GACF,EAAO,IAChB,EAAa,CADU,CACL,IAAI,CACtB,EAAW,KAGX,EAAa,CADb,EAAO,EAAS,EAAA,EACE,MAAM,CACxB,EAAW,EAAS,QAAQ,EAG1B,EAAa,IACf,CADoB,KACd,AAAI,WAAW,oDAGvB,IAAM,EAAU,CACd,CAAC,EAAY,CAAE,EACf,KAAK,EACL,aAAc,IAAI,CAAC,aAAa,MAChC,EACA,WAAY,IAAI,CAAC,WAAW,CAC5B,OAAQ,YACR,EACA,MAAM,CACR,EAEI,EAAO,OAAO,AACZ,IAAI,CAAC,MAAM,CACb,IADkB,AACd,CAAC,OAAO,CADe,AACd,CAAC,IAAI,CAAC,WAAW,CAAE,EAAM,GAAO,EAAS,EAAG,EAEzD,IAAI,CAAC,WAAW,CAAC,GAAM,EAAO,EAAS,OAEhC,IAAI,CAAC,MAAM,CACpB,IADyB,AACrB,CAAC,OAAO,CADsB,AACrB,CAAC,IAAI,CAAC,QAAQ,CAAE,GAAM,EAAO,EAAS,EAAG,EAEtD,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAM,GAAU,EAEhD,CAkBA,KAAK,CAAI,CAAE,CAAO,CAAE,CAAE,CAAE,CACtB,IAII,EACA,EALE,EAAoB,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,CACvE,EAAS,EAAQ,MAAM,CAAG,EAAI,EAC9B,EAAO,EAAQ,QAAQ,CAKP,UAAhB,AAA0B,OAAnB,GACT,EAAa,OAAO,UAAU,CAAC,GAC/B,GAAW,GACF,EAAO,IAChB,EAAa,CADU,CACL,IAAI,CACtB,GAAW,IAGX,EAAa,CADb,EAAO,EAAS,EAAA,EACE,MAAM,CACxB,EAAW,EAAS,QAAQ,EAG1B,IAAI,CAAC,cAAc,EAAE,AACvB,IAAI,CAAC,cAAc,EAAG,EAEpB,GACA,GACA,EAAkB,MAAM,CACtB,EAAkB,SAAS,CACvB,6BACA,6BACL,EACD,CACA,EAAO,GAAc,EAAkB,UAAA,AAAU,EAEnD,IAAI,CAAC,SAAS,CAAG,IAEjB,EAAO,GACP,EAAS,GAGP,EAAQ,GAAG,GAAE,IAAI,CAAC,cAAc,EAAG,CAAA,EAEvC,IAAM,EAAO,CACX,CAAC,EAAY,CAAE,EACf,IAAK,EAAQ,GAAG,CAChB,aAAc,IAAI,CAAC,aAAa,CAChC,KAAM,EAAQ,IAAI,CAClB,WAAY,IAAI,CAAC,WAAW,QAC5B,WACA,OACA,CACF,EAEI,EAAO,OAAO,AACZ,IAAI,CAAC,MAAM,CACb,IADkB,AACd,CAAC,OAAO,CAAC,AADc,CACb,IAAI,CAAC,WAAW,CAAE,EAAM,IAAI,CAAC,SAAS,CAAE,EAAM,EAAG,EAE/D,IAAI,CAAC,WAAW,CAAC,EAAM,IAAI,CAAC,SAAS,CAAE,EAAM,OAEtC,IAAI,CAAC,MAAM,CACpB,IADyB,AACrB,CAAC,OAAO,CADsB,AACrB,CAAC,IAAI,CAAC,QAAQ,CAAE,EAAM,IAAI,CAAC,SAAS,CAAE,EAAM,EAAG,EAE5D,IAAI,CAAC,QAAQ,CAAC,EAAM,IAAI,CAAC,SAAS,CAAE,EAAM,EAE9C,CAyBA,YAAY,CAAI,CAAE,CAAQ,CAAE,CAAO,CAAE,CAAE,CAAE,CACvC,IAAI,CAAC,cAAc,EAAI,CAAO,CAAC,EAAY,CAC3C,IAAI,CAAC,MAAM,CA/ZO,EA+ZJ,AAEd,EACG,WAAW,GACX,IAAI,CAAE,AAAD,IACJ,GAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAE,CAC1B,IAAM,EAAM,AAAI,MACd,uDAQF,QAAQ,QAAQ,CAAC,EAAe,IAAI,CAAE,EAAK,GAC3C,MACF,CAEA,IAAI,CAAC,cAAc,EAAI,CAAO,CAAC,EAAY,CAC3C,IAAM,EAAO,EAAS,GAEjB,EAKH,IAAI,CAAC,GALQ,KAKA,CAAC,EAAM,EAAU,EAAS,IAJvC,IAAI,CAAC,MAAM,GAAG,AACd,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAM,GAAU,GAC5C,IAAI,CAAC,OAAO,GAIhB,GACC,KAAK,CAAC,AAAC,IAKN,QAAQ,QAAQ,CAAC,EAAS,IAAI,CAAE,EAAK,EACvC,EACJ,CAyBA,SAAS,CAAI,CAAE,CAAQ,CAAE,CAAO,CAAE,CAAE,CAAE,CACpC,GAAI,CAAC,EAAU,YACb,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAM,GAAU,GAI9C,IAAM,EAAoB,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,CAE3E,IAAI,CAAC,cAAc,EAAI,CAAO,CAAC,EAAY,CAC3C,IAAI,CAAC,MAAM,CAveG,EAueA,AACd,EAAkB,QAAQ,CAAC,EAAM,EAAQ,GAAG,CAAE,CAAC,EAAG,KAChD,GAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAE,YAK1B,EAAc,IAAI,CAJN,AAAI,CAII,KAHlB,yDAGuB,GAI3B,IAAI,CAAC,cAAc,EAAI,CAAO,CAAC,EAAY,CAC3C,IAAI,CAAC,MAAM,GAAG,AACd,EAAQ,QAAQ,EAAG,EACnB,IAAI,CAAC,SAAS,CAAC,EAAO,KAAK,CAAC,EAAK,GAAU,GAC3C,IAAI,CAAC,OAAO,EACd,EACF,CAOA,SAAU,CACR,KAjgBY,IAigBL,IAAI,CAAC,MAAM,EAAgB,GAAX,CAAe,CAAC,MAAM,CAAC,MAAM,EAAE,CACpD,IAAM,EAAS,IAAI,CAAC,MAAM,CAAC,KAAK,GAEhC,IAAI,CAAC,cAAc,EAAI,CAAM,CAAC,EAAE,CAAC,EAAY,CAC7C,QAAQ,KAAK,CAAC,CAAM,CAAC,EAAE,CAAE,IAAI,CAAE,EAAO,KAAK,CAAC,GAC9C,CACF,CAQA,QAAQ,CAAM,CAAE,CACd,IAAI,CAAC,cAAc,EAAI,CAAM,CAAC,EAAE,CAAC,EAAY,CAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EACnB,CASA,UAAU,CAAI,CAAE,CAAE,CAAE,CACE,GAAG,CAAnB,EAAK,MAAM,EACb,IAAI,CAAC,OAAO,CAAC,IAAI,GACjB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAI,CAAC,EAAE,EAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAI,CAAC,EAAE,CAAE,GAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,IAEnB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAI,CAAC,EAAE,CAAE,EAEhC,CACF,CAYA,SAAS,EAAc,CAAM,CAAE,CAAG,CAAE,CAAE,EAChC,AAAc,mBAAP,GAAmB,EAAG,GAEjC,IAAK,IAAI,EAAI,EAAG,EAAI,EAAO,MAAM,CAAC,MAAM,CAAE,IAAK,CAC7C,IAAM,EAAS,EAAO,MAAM,CAAC,EAAE,CACzB,EAAW,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,CAElB,YAApB,OAAO,GAAyB,EAAS,EAC/C,CACF,CAUA,SAAS,EAAQ,CAAM,CAAE,CAAG,CAAE,CAAE,EAC9B,EAAc,EAAQ,EAAK,GAC3B,EAAO,OAAO,CAAC,EACjB,CAhCA,EAAO,OAAO,CAAG,uDCzjBjB,aAEA,GAAM,sBAAE,CAAoB,WAAE,CAAS,CAAE,CAAA,EAAA,CAAA,CAAA,QAEnC,EAAQ,OAAO,SACf,EAAQ,OAAO,SACf,EAAS,OAAO,UAChB,EAAW,OAAO,YAClB,EAAU,OAAO,WACjB,EAAU,OAAO,WACjB,EAAQ,OAAO,SACf,EAAY,OAAO,YAKzB,OAAM,EAOJ,YAAY,CAAI,CAAE,CAChB,IAAI,CAAC,EAAQ,CAAG,KAChB,IAAI,CAAC,EAAM,CAAG,CAChB,CAKA,IAAI,QAAS,CACX,OAAO,IAAI,CAAC,EAAQ,AACtB,CAKA,IAAI,MAAO,CACT,OAAO,IAAI,CAAC,EACd,AADoB,CAEtB,CAEA,OAAO,cAAc,CAAC,EAAM,SAAS,CAAE,SAAU,CAAE,YAAY,CAAK,GACpE,OAAO,cAAc,CAAC,EAAM,SAAS,CAAE,OAAQ,CAAE,YAAY,CAAK,EAOlE,OAAM,UAAmB,EAcvB,YAAY,CAAI,CAAE,EAAU,CAAC,CAAC,CAAE,CAC9B,KAAK,CAAC,GAEN,IAAI,CAAC,EAAM,MAAoB,IAAjB,EAAQ,IAAI,CAAiB,EAAI,EAAQ,IAAI,CAC3D,IAAI,CAAC,EAAQ,CAAG,KAAmB,MAAX,MAAM,CAAiB,GAAK,EAAQ,MAAM,CAClE,IAAI,CAAC,EAAU,MAAwB,IAArB,EAAQ,MAAyB,EAAjB,EAAyB,EAAQ,QAAQ,AAC7E,CAKA,IAAI,MAAO,CACT,OAAO,IAAI,CAAC,EAAM,AACpB,CAKA,IAAI,QAAS,CACX,OAAO,IAAI,CAAC,EAAQ,AACtB,CAKA,IAAI,UAAW,CACb,OAAO,IAAI,CAAC,EAAU,AACxB,CACF,CAEA,OAAO,cAAc,CAAC,EAAW,SAAS,CAAE,OAAQ,CAAE,YAAY,CAAK,GACvE,OAAO,cAAc,CAAC,EAAW,SAAS,CAAE,SAAU,CAAE,YAAY,CAAK,GACzE,OAAO,cAAc,CAAC,EAAW,SAAS,CAAE,WAAY,CAAE,YAAY,CAAK,EAO3E,OAAM,UAAmB,EAUvB,YAAY,CAAI,CAAE,EAAU,CAAC,CAAC,CAAE,CAC9B,KAAK,CAAC,GAEN,IAAI,CAAC,EAAO,MAAqB,IAAlB,EAAQ,KAAK,CAAiB,KAAO,EAAQ,KAAK,CACjE,IAAI,CAAC,EAAS,MAAuB,IAApB,EAAQ,OAAO,CAAiB,GAAK,EAAQ,OAAO,AACvE,CAKA,IAAI,OAAQ,CACV,OAAO,IAAI,CAAC,EAAO,AACrB,CAKA,IAAI,SAAU,CACZ,OAAO,IAAI,CAAC,EAAS,AACvB,CACF,CAEA,OAAO,cAAc,CAAC,EAAW,SAAS,CAAE,QAAS,CAAE,YAAY,CAAK,GACxE,OAAO,cAAc,CAAC,EAAW,SAAS,CAAE,UAAW,CAAE,WAAY,EAAK,EAO1E,OAAM,UAAqB,EASzB,YAAY,CAAI,CAAE,EAAU,CAAC,CAAC,CAAE,CAC9B,KAAK,CAAC,GAEN,IAAI,CAAC,EAAM,MAAoB,IAAjB,EAAQ,IAAI,CAAiB,KAAO,EAAQ,IAAI,AAChE,CAKA,IAAI,MAAO,CACT,OAAO,IAAI,CAAC,EACd,AADoB,CAEtB,CAwHA,SAAS,EAAa,CAAQ,CAAE,CAAO,CAAE,CAAK,EACpB,UAApB,OAAO,GAAyB,EAAS,WAAW,CACtD,CADwD,CAC/C,WAAW,CAAC,IAAI,CAAC,EAAU,GAEpC,EAAS,IAAI,CAAC,EAAS,EAE3B,CA5HA,OAAO,cAAc,CAAC,EAAa,SAAS,CAAE,OAAQ,CAAE,WAAY,EAAK,GAsGzE,EAAO,OAAO,CAAG,CACf,wBACA,EACA,QACA,YAlGkB,CAalB,iBAAiB,CAAI,CAAE,CAAO,CAAE,EAAU,CAAC,CAAC,MAWtC,EAVJ,IAAK,IAAM,KAAY,IAAI,CAAC,SAAS,CAAC,GACpC,GAD2C,AAEzC,CAAC,CAAO,CAAC,EAAqB,EAC9B,CAAQ,CAAC,EAAU,GAAK,GACxB,CAAC,CAAQ,CAAC,EAAqB,CAE/B,CADA,MAOJ,GAAa,WAAW,CAApB,EACF,EAAU,SAAS,AAAU,CAAI,CAAE,CAAQ,EACzC,IAAM,EAAQ,IAAI,EAAa,UAAW,CACxC,KAAM,EAAW,EAAO,EAAK,QAAQ,EACvC,GAEA,CAAK,CAAC,EAAQ,CAAG,IAAI,CACrB,EAAa,EAAS,IAAI,CAAE,EAC9B,OACK,GAAI,AAAS,SAAS,GAC3B,EAAU,SAAS,AAAQ,CAAI,CAAE,CAAO,EACtC,IAAM,EAAQ,IAAI,EAAW,QAAS,MACpC,EACA,OAAQ,EAAQ,QAAQ,GACxB,SAAU,IAAI,CAAC,mBAAmB,EAAI,IAAI,CAAC,eAAe,AAC5D,GAEA,CAAK,CAAC,EAAQ,CAAG,IAAI,CACrB,EAAa,EAAS,IAAI,CAAE,EAC9B,OACK,GAAa,SAAS,CAAlB,EACT,EAAU,SAAS,AAAQ,CAAK,EAC9B,IAAM,EAAQ,IAAI,EAAW,QAAS,OACpC,EACA,QAAS,EAAM,OAAO,AACxB,GAEA,CAAK,CAAC,EAAQ,CAAG,IAAI,CACrB,EAAa,EAAS,IAAI,CAAE,EAC9B,OACK,GAAa,QAAQ,CAAjB,EAQT,OAPA,EAAU,SAAS,EACjB,IAAM,EAAQ,IAAI,EAAM,QAExB,CAAK,CAAC,EAAQ,CAAG,IAAI,CACrB,EAAa,EAAS,IAAI,CAAE,EAC9B,EAKF,CAAO,CAAC,EAAqB,CAAG,CAAC,CAAC,CAAO,CAAC,EAAqB,CAC/D,CAAO,CAAC,EAAU,CAAG,EAEjB,EAAQ,IAAI,CACd,CADgB,GACZ,CAAC,IAAI,CAAC,EAAM,GAEhB,IAAI,CAAC,EAAE,CAAC,EAAM,EAElB,EASA,oBAAoB,CAAI,CAAE,CAAO,EAC/B,IAAK,IAAM,KAAY,IAAI,CAAC,SAAS,CAAC,GACpC,GAD2C,AACvC,CAAQ,CAAC,EAAU,GAAK,GAAW,CAAC,CAAQ,CAAC,EAAqB,CAAE,CACtE,IAAI,CAAC,cAAc,CAAC,EAAM,GAC1B,KACF,CAEJ,CACF,eAOE,CACF,uDCnRA,aAEA,GAAM,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,QAYpB,SAAS,EAAK,CAAI,CAAE,CAAI,CAAE,CAAI,OACT,IAAf,CAAI,CAAC,EAAK,CAAgB,CAAI,CAAC,EAAK,CAAG,CAAC,EAAK,CAC5C,CAAI,CAAC,EAAK,CAAC,IAAI,CAAC,EACvB,CAyLA,EAAO,OAAO,CAAG,CAAE,OAxBnB,SAAS,AAAO,CAAU,EACxB,OAAO,OAAO,IAAI,CAAC,GAChB,GAAG,CAAE,AAAD,IACH,IAAI,EAAiB,CAAU,CAAC,EAAU,CAE1C,OADI,AAAC,MAAM,OAAO,CAAC,KAAiB,EAAiB,CAAC,EAAe,EAC9D,EACJ,GAAG,CAAC,AAAC,GACG,CAAC,EAAU,CACf,MAAM,CACL,OAAO,IAAI,CAAC,GAAQ,GAAG,CAAE,AAAD,IACtB,IAAI,EAAS,CAAM,CAAC,EAAE,CAEtB,OADI,AAAC,MAAM,OAAO,CAAC,IAAS,GAAS,CAAC,EAAO,EACtC,EACJ,GAAG,CAAC,AAAC,IAAa,IAAN,EAAa,EAAI,CAAA,EAAG,EAAE,CAAC,EAAE,EAAA,CAAG,EACxC,IAAI,CAAC,KACV,IAED,IAAI,CAAC,OAET,IAAI,CAAC,KACV,GACC,IAAI,CAAC,KACV,EAE2B,MAhL3B,SAAS,AAAM,CAAM,EACnB,IAKI,EACA,EANE,EAAS,OAAO,MAAM,CAAC,MACzB,EAAS,OAAO,MAAM,CAAC,MACvB,GAAe,EACf,GAAa,EACb,GAAW,EAGX,EAAQ,CAAC,EACT,EAAO,CAAC,EACR,EAAM,CAAC,EACP,EAAI,EAER,KAAO,EAAI,EAAO,MAAM,CAAE,IAGxB,AAH6B,GAC7B,EAAO,EAAO,UAAU,CAAC,QAEH,IAAlB,EACF,GAAY,CAAC,CADkB,GAC3B,GAAmC,GAAG,CAAxB,CAAU,CAAC,EAAK,CAC5B,AAAU,CAAC,OAAG,IAAQ,OACrB,GACC,IAAN,CACA,GAAU,KAAT,AAAc,GAAsB,IAAf,AAAM,CAAS,CAAI,CAE7B,CAAC,CADb,GACI,GAAwB,CAAC,IAAX,IAAc,GAAM,OACjC,GAAa,KAAK,AAAd,GAAoC,IAAf,CAAoB,AAAd,EAAyB,CAC7D,GAAc,CAD2C,AAC1C,GAAG,CAAd,EACF,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,EAGhD,CAAC,IAAT,IAAY,GAAM,EACtB,IAAM,EAAO,EAAO,KAAK,CAAC,EAAO,GACpB,KAAT,CAAe,EACjB,EAAK,EAAQ,EAAM,GACnB,EAAS,OAAO,MAAM,CAAC,OAEvB,EAAgB,EAGlB,EAAQ,EAAM,CAAC,CACjB,MACE,CADK,KACC,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,OAEvD,QAAkB,IAAd,EACT,GAAY,CAAC,CADqB,GAC9B,GAAmC,GAAG,CAAxB,CAAU,CAAC,EAAK,CAClB,CAAC,IAAX,GAAc,IAAQ,OACrB,GAAa,KAAT,GAA0B,IAAT,EAAe,AAC7B,CAAC,IAAT,GAAc,AAAU,CAAC,OAAG,IAAM,OACjC,GAAa,KAAT,GAA0B,KAAT,EAAe,CACzC,GAAc,CAAC,GAAG,CAAd,EACF,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,EAGhD,CAAC,IAAT,IAAY,GAAM,EACtB,EAAK,EAAQ,EAAO,KAAK,CAAC,EAAO,IAAM,GAC1B,KAAT,CAAe,GACjB,EAAK,EAAQ,EAAe,GAC5B,EAAS,OAAO,MAAM,CAAC,MACvB,OAAgB,GAGlB,EAAQ,EAAM,CAAC,CACjB,MAAO,GAAa,KAAT,AAAc,GAAuB,AAAV,CAAW,GAAjB,IAA8B,AAAR,CAAS,GAAG,GAChE,EAAY,EAAO,KAAK,CAAC,EAAO,GAChC,EAAQ,EAAM,CAAC,OAEf,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,OAQ5D,GAAI,EAAY,CACd,GAAI,AAAqB,GAAG,EAAd,CAAC,EAAK,CAClB,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,CAExD,CAAU,CAAC,MAAG,EAAQ,EACjB,AAAC,IAAc,GAAe,CAAA,EACvC,GAAa,CACf,MAAO,GAAI,EACT,GAAyB,GAAG,CAAxB,CAAU,AADK,CACJ,EAAK,CACJ,CAAC,IAAX,IAAc,GAAQ,OACrB,GAAa,KAAT,AAAc,GAAa,AAAU,CAAC,GAAjB,AAAoB,GAClD,GAAW,EACX,EAAM,OACD,GAAa,KAAT,AAAc,EACvB,GAAa,EADiB,KAG9B,AAHkC,MAG5B,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,OAEvD,GAAa,KAAT,GAAiB,AAA6B,MAAM,CAA5B,UAAU,CAAC,EAAI,GAChD,GAAW,OACN,GAAY,CAAC,IAAT,GAAmC,GAAG,CAAxB,CAAU,CAAC,EAAK,CACnC,AAAU,CAAC,QAAG,GAAQ,OACrB,GAAc,CAAC,IAAX,CAAgB,GAAU,KAAT,GAAiB,AAAS,KAAA,CAAI,CAC5C,CAAC,CAD8C,GACvD,IAAY,GAAM,OACjB,GAAa,KAAT,GAA0B,KAAT,EAAe,CACzC,GAAc,CAAC,GAAG,CAAd,EACF,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,CAGxD,AAAQ,CAAC,SAAG,GAAM,EACtB,IAAI,EAAQ,EAAO,KAAK,CAAC,EAAO,GAC5B,IACF,EAAQ,EAAM,MADE,CACK,CAAC,MAAO,IAC7B,GAAe,GAEjB,EAAK,EAAQ,EAAW,GACX,KAAT,CAAe,GACjB,EAAK,EAAQ,EAAe,GAC5B,EAAS,OAAO,MAAM,CAAC,MACvB,OAAgB,GAGlB,OAAY,EACZ,EAAQ,EAAM,CAAC,CACjB,MACE,CADK,KACC,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,EAKhE,GAAc,CAAC,IAAX,GAAgB,GAAqB,KAAT,GAA0B,IAAT,EAAe,AAC9D,MAAU,AAAJ,YAAgB,2BAGZ,CAAC,IAAT,IAAY,GAAM,EACtB,IAAM,EAAQ,EAAO,KAAK,CAAC,EAAO,GAclC,YAbsB,IAAlB,EACF,EAAK,EAAQ,CADkB,CACX,SAEF,IAAd,EACF,EAAK,EAAQ,CADc,EACP,GACX,EACT,EAAK,EAAQ,EAAW,EAAM,IADP,GACc,CAAC,MAAO,KAE7C,EAAK,EAAQ,EAAW,GAE1B,EAAK,EAAQ,EAAe,IAGvB,CACT,CAiCiC,uDCxMjC,aAEA,IAAM,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,aAAE,CAAW,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,QAC3B,QAAE,CAAM,UAAE,CAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,QACpB,KAAE,CAAG,CAAE,CAAA,EAAA,CAAA,CAAA,QAEP,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QAEV,cACJ,CAAY,cACZ,CAAY,MACZ,CAAI,sBACJ,CAAoB,WACpB,CAAS,aACT,CAAW,YACX,CAAU,MACV,CAAI,CACL,CAAA,EAAA,CAAA,CAAA,QACK,CACJ,YAAa,kBAAE,CAAgB,qBAAE,CAAmB,CAAE,CACvD,CAAA,EAAA,CAAA,CAAA,QACK,QAAE,CAAM,OAAE,CAAK,CAAE,CAAA,EAAA,CAAA,CAAA,QACjB,UAAE,CAAQ,CAAE,CAAA,EAAA,CAAA,CAAA,QAGZ,EAAW,OAAO,YAClB,EAAmB,CAAC,EAAG,GAAG,CAC1B,EAAc,CAAC,aAAc,OAAQ,UAAW,SAAS,CACzD,EAAmB,gCAOzB,OAAM,UAAkB,EAQtB,YAAY,CAAO,CAAE,CAAS,CAAE,CAAO,CAAE,CACvC,KAAK,GAEL,IAAI,CAAC,WAAW,CAAG,CAAY,CAAC,EAAE,CAClC,IAAI,CAAC,UAAU,CAAG,KAClB,IAAI,CAAC,mBAAmB,EAAG,EAC3B,IAAI,CAAC,eAAe,EAAG,EACvB,IAAI,CAAC,aAAa,CAAG,EACrB,IAAI,CAAC,WAAW,CAAG,KACnB,IAAI,CAAC,aAAa,CAAG,GACrB,IAAI,CAAC,WAAW,CAAG,CAAC,EACpB,IAAI,CAAC,OAAO,EAAG,EACf,IAAI,CAAC,SAAS,CAAG,GACjB,IAAI,CAAC,WAAW,CAAG,EAAU,UAAU,CACvC,IAAI,CAAC,SAAS,CAAG,KACjB,IAAI,CAAC,OAAO,CAAG,KACf,IAAI,CAAC,OAAO,CAAG,KAEC,MAAM,CAAlB,GACF,IAAI,CAAC,eAAe,CAAG,EACvB,IAAI,CAAC,SAAS,EAAG,EACjB,IAAI,CAAC,UAAU,CAAG,OAEA,IAAd,EACF,EAAY,EAAE,CADa,AAEjB,MAAM,OAAO,CAAC,KACC,OADW,GAChC,OAAO,GAAwC,MAAM,CAApB,GACnC,EAAU,EACV,EAAY,EAAE,EAEd,EAAY,CAAC,EAAU,EAI3B,AAsjBN,SAAS,EAAa,CAAS,CAAE,CAAO,CAAE,CAAS,CAAE,CAAO,EAC1D,IA6BI,EAsBA,EA2BA,EAgEA,EA9IE,EAAO,CACX,wBAAwB,EACxB,UAAU,EACV,gBAAiB,CAAgB,CAAC,EAAE,CACpC,WAAY,MAAM,IAClB,GADyB,iBACL,EACpB,mBAAmB,EACnB,iBAAiB,EACjB,aAAc,GACd,GAAG,CAAO,CACV,gBAAY,EACZ,cAAU,EACV,cAAU,EACV,aAAS,EACT,OAAQ,MACR,KAAM,OACN,UAAM,EACN,UAAM,CACR,EAIA,GAFA,EAAU,SAAS,CAAG,EAAK,QAAQ,CAE/B,CAAC,EAAiB,QAAQ,CAAC,EAAK,eAAe,EACjD,CADoD,KAC9C,AAAI,WACR,CAAC,8BAA8B,EAAE,EAAK,eAAe,CAAC,AACnD,CADoD,qBAC/B,EAAE,EAAiB,IAAI,CAAC,MAAM,CAAC,CADC,AACA,EAM5D,CANM,EAMF,aAAmB,EACrB,EAAY,CADc,MAG1B,GAAI,CACF,EAAY,IAAI,EAAI,EACtB,CAAE,MAAO,EAAG,CACV,MAAM,AAAI,YAAY,CAAC,aAAa,EAAE,EAAA,CAAS,CACjD,CAGyB,SAAS,CAAhC,EAAU,QAAQ,CACpB,EAAU,QAAQ,CAAG,MACW,UAAU,CAAjC,EAAU,QAAQ,GAC3B,EAAU,QAAQ,CAAG,MAAA,EAGvB,EAAU,IAAI,CAAG,EAAU,IAAI,CAE/B,IAAM,EAAkC,SAAvB,EAAU,QAAQ,CAC7B,EAAkC,aAAvB,EAAU,QAAQ,CAanC,GAV2B,QAAvB,CAAgC,CAAtB,QAAQ,EAAe,GAAa,EAIvC,GAAY,CAAC,EAAU,CAJe,CAAW,MAIlB,CACxC,CAD0C,CACtB,8BACX,EAAU,IAAI,EAAE,CACzB,EAAoB,wCAAA,EANpB,EACE,uDACA,8BAOA,EAAmB,CACrB,IAAM,EAAM,AAAI,YAAY,GAE5B,GAA6B,GAAG,CAA5B,EAAU,UAAU,CAEjB,YACL,EAAkB,EAAW,EAF7B,OAAM,CAKV,CAEA,IAAM,EAAc,EAAW,IAAM,GAC/B,EAAM,EAAY,IAAI,QAAQ,CAAC,UAC/B,EAAU,EAAW,EAAM,OAAO,CAAG,EAAK,OAAO,CACjD,EAAc,IAAI,IA8BxB,GA3BA,EAAK,gBAAgB,CACnB,EAAK,gBAAgB,GAAK,CAAD,CAAY,EAAa,CAAA,CAAU,CAC9D,EAAK,WAAW,CAAG,EAAK,WAAW,EAAI,EACvC,EAAK,IAAI,CAAG,EAAU,IAAI,EAAI,EAC9B,EAAK,IAAI,CAAG,EAAU,QAAQ,CAAC,UAAU,CAAC,KACtC,EAAU,QAAQ,CAAC,KAAK,CAAC,EAAG,CAAC,GAC7B,EAAU,QAAQ,CACtB,EAAK,OAAO,CAAG,CACb,GAAG,EAAK,OAAO,CACf,wBAAyB,EAAK,eAAe,CAC7C,oBAAqB,EACrB,WAAY,UACZ,QAAS,WACX,EACA,EAAK,IAAI,CAAG,EAAU,QAAQ,CAAG,EAAU,MAAM,CACjD,EAAK,OAAO,CAAG,EAAK,gBAAgB,CAEhC,EAAK,iBAAiB,EAAE,CAC1B,EAAoB,IAAI,GACK,IAA3B,EAAK,iBAAiB,CAAY,EAAK,iBAAiB,CAAG,CAAC,GAC5D,EACA,EAAK,UAAU,EAEjB,EAAK,OAAO,CAAC,2BAA2B,CAAG,EAAO,CAChD,CAAC,EAAkB,aAAa,CAAC,CAAE,EAAkB,KAAK,EAC5D,IAEE,EAAU,MAAM,CAAE,CACpB,IAAK,IAAM,KAAY,EAAW,CAChC,GACsB,UAApB,OAAO,GACP,CAAC,EAAiB,IAAI,CAAC,IACvB,EAAY,GAAG,CAAC,GAEhB,MAAM,AAAI,EADV,UAEE,sDAIJ,EAAY,GAAG,CAAC,EAClB,CAEA,EAAK,OAAO,CAAC,yBAAyB,CAAG,EAAU,IAAI,CAAC,IAC1D,CAYA,GAXI,EAAK,MAAM,EAAE,CACX,EAAK,eAAe,CAAG,GACzB,CAD6B,CACxB,OAAO,CAAC,uBAAuB,CAAG,EAAK,MAAM,CAElD,EAAK,OAAO,CAAC,MAAM,CAAG,EAAK,MAAM,GAGjC,EAAU,QAAQ,EAAI,EAAU,QAAA,AAAQ,EAAE,EAC5C,EAAK,IAAI,CAAG,CAAA,EAAG,EAAU,QAAQ,CAAC,CAAC,EAAE,EAAU,QAAQ,CAAA,CAAA,AAAE,EAGvD,EAAU,CACZ,IAAM,EAAQ,EAAK,IAAI,CAAC,KAAK,CAAC,KAE9B,EAAK,UAAU,CAAG,CAAK,CAAC,EAAE,CAC1B,EAAK,IAAI,CAAG,CAAK,CAAC,EACpB,AADsB,CAKtB,GAAI,EAAK,eAAe,CAAE,CACxB,GAAI,AAAyB,MAAf,UAAU,CAAQ,CAC9B,EAAU,YAAY,CAAG,EACzB,EAAU,eAAe,CAAG,EAC5B,EAAU,yBAAyB,CAAG,EAClC,EAAK,UAAU,CACf,EAAU,IAAI,CAElB,IAAM,EAAU,GAAW,EAAQ,OAAO,CAQ1C,GAFA,EAAU,CAAE,GAAG,CAAO,CAAE,QAAS,CAAC,CAAE,EAEhC,EACF,IAAK,GADM,AACA,CAAC,EAAK,EAAM,GAAI,OAAO,OAAO,CAAC,GACxC,EAAQ,IAD0C,GACnC,CAAC,EAAI,WAAW,GAAG,CAAG,CAG3C,MAAO,GAA4C,IAAxC,EAAU,aAAa,CAAC,YAAmB,CACpD,IAAM,EAAa,IACf,EAAU,YAAY,EACpB,EAAK,UAAU,GAAK,EAAU,yBAAyB,CAEzD,EADE,CACQ,YAAY,EAEpB,CADA,CACU,IAAI,GAAK,EAAU,yBAAyB,CAEvD,KAAe,EAAU,eAAe,EAAK,CAAA,CAAD,EAAY,CAK3D,OAAO,EAAK,OAAO,CAAC,aAAa,CACjC,OAAO,EAAK,OAAO,CAAC,MAAM,CAEtB,AAAC,GAAY,OAAO,EAAK,OAAO,CAAC,IAAI,CAEzC,EAAK,IAAI,MAAG,EAEhB,CAOI,EAAK,IAAI,EAAI,CAAC,EAAQ,OAAO,CAAC,aAAa,EAAE,CAC/C,EAAQ,OAAO,CAAC,aAAa,CAC3B,SAAW,OAAO,IAAI,CAAC,EAAK,IAAI,EAAE,QAAQ,CAAC,SAAA,EAG/C,EAAM,EAAU,IAAI,CAAG,EAAQ,GAE3B,EAAU,UAAU,EAAE,AAUxB,EAAU,IAAI,CAAC,WAAY,EAAU,GAAG,CAAE,EAE9C,MACE,CADK,CACC,EAAU,IAAI,CAAG,EAAQ,GAG7B,EAAK,OAAO,EAAE,AAChB,EAAI,EAAE,CAAC,UAAW,KAChB,EAAe,EAAW,EAAK,kCACjC,GAGF,EAAI,EAAE,CAAC,QAAS,AAAC,IACH,OAAR,GAAgB,CAAG,CAAC,EAAS,EAAE,CAEnC,EAAM,EAAU,IAAI,CAAG,KACvB,EAAkB,EAAW,GAC/B,GAEA,EAAI,EAAE,CAAC,WAAY,AAAC,IAClB,IAAM,EAAW,EAAI,OAAO,CAAC,QAAQ,CAC/B,EAAa,EAAI,UAAU,CAEjC,GACE,GACA,EAAK,eAAe,EACpB,GAAc,KACd,EAAa,IACb,KAQI,EAPJ,GAAI,EAAE,EAAU,UAAU,CAAG,EAAK,YAAY,CAAE,YAC9C,EAAe,EAAW,EAAK,8BAIjC,EAAI,KAAK,GAIT,GAAI,CACF,EAAO,IAAI,EAAI,EAAU,EAC3B,CAAE,MAAO,EAAG,CAEV,EAAkB,EADN,AAAI,SACa,GADD,CAAC,aAAa,EAAE,EAAA,CAAU,GAEtD,MACF,CAEA,EAAa,EAAW,EAAM,EAAW,EAC3C,MAAW,AAAC,CAAL,CAAe,IAAI,CAAC,sBAAuB,EAAK,IACrD,EAD2D,AAEzD,EACA,EACA,CAAC,4BAA4B,EAAE,EAAI,UAAU,CAAA,CAAE,CAGrD,GAEA,EAAI,EAAE,CAAC,UAAW,CAAC,EAAK,EAAQ,SA4B1B,EArBJ,GANA,EAAU,IAAI,CAAC,UAAW,GAMtB,EAAU,UAAU,GAAK,EAAU,UAAU,CAAE,OAEnD,EAAM,EAAU,IAAI,CAAG,KAEvB,IAAM,EAAU,EAAI,OAAO,CAAC,OAAO,CAEnC,QAAgB,IAAZ,GAAyB,AAA0B,gBAAlB,WAAW,GAAoB,YAClE,EAAe,EAAW,EAAQ,0BAIpC,IAAM,EAAS,EAAW,QACvB,MAAM,CAAC,EAAM,GACb,MAAM,CAAC,UAEV,GAAI,EAAI,OAAO,CAAC,uBAAuB,GAAK,EAAQ,YAClD,EAAe,EAAW,EAAQ,uCAIpC,IAAM,EAAa,EAAI,OAAO,CAAC,yBAAyB,CAaxD,QAVmB,IAAf,EACG,EAAY,GADW,CACP,CAEV,AAAC,CAFW,CAEC,GAAG,CAAC,KAC1B,EAAY,MAD2B,8BAC3B,EAFZ,EAAY,mDAIL,EAAY,IAAI,EAAE,AAC3B,GAAY,4BAAA,EAGV,EAAW,YACb,EAAe,EAAW,EAAQ,GAIhC,IAAY,EAAU,SAAS,CAAG,CAAA,EAEtC,IAAM,EAAyB,EAAI,OAAO,CAAC,2BAA2B,CAEtE,GAAI,AAA2B,WAAW,KASpC,EARJ,GAAI,CAAC,EAAmB,YAItB,EAAe,EAAW,EAFxB,MAEgC,8DADhC,YAOJ,GAAI,CACF,EAAa,EAAM,EACrB,CAAE,MAAO,EAAK,CAEZ,EAAe,EAAW,EADV,MACkB,qCAClC,MACF,CAEA,IAAM,EAAiB,OAAO,IAAI,CAAC,GAEnC,GAC4B,IAA1B,EAAe,MAAM,EACrB,CAAc,CAAC,EAAE,GAAK,EAAkB,aAAa,CACrD,YAEA,EAAe,EAAW,EADV,MACkB,kDAIpC,GAAI,CACF,EAAkB,MAAM,CAAC,CAAU,CAAC,EAAkB,aAAa,CAAC,CACtE,CAAE,MAAO,EAAK,CAEZ,EAAe,EAAW,EADV,MACkB,qCAClC,MACF,CAEA,EAAU,WAAW,CAAC,EAAkB,aAAa,CAAC,CACpD,CACJ,CAEA,EAAU,SAAS,CAAC,EAAQ,EAAM,CAChC,uBAAwB,EAAK,sBAAsB,CACnD,aAAc,EAAK,YAAY,CAC/B,WAAY,EAAK,UAAU,CAC3B,mBAAoB,EAAK,kBAAkB,AAC7C,EACF,GAEI,EAAK,aAAa,CACpB,CADsB,CACjB,aAAa,CAAC,EAAK,GAExB,EAAI,GAAG,EAEX,EAz6BmB,IAAI,CAAE,EAAS,EAAW,KAEvC,IAAI,CAAC,SAAS,CAAG,EAAQ,QAAQ,CACjC,IAAI,CAAC,SAAS,EAAG,EAErB,CAQA,IAAI,YAAa,CACf,OAAO,IAAI,CAAC,WAAW,AACzB,CAEA,IAAI,WAAW,CAAI,CAAE,CACd,EAAa,QAAQ,CAAC,KAE3B,EAFkC,EAE9B,CAAC,WAAW,CAAG,EAKf,IAAI,CAAC,SAAS,GAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAG,CAAA,EACnD,CAKA,IAAI,gBAAiB,QACnB,AAAK,IAAD,AAAK,CAAC,OAAO,CAEV,CAFY,GAER,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAF7C,IAAI,CAAC,eAGjC,AAHgD,CAQhD,IAAI,YAAa,CACf,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAC3C,CAKA,IAAI,UAAW,CACb,OAAO,IAAI,CAAC,OAAO,AACrB,CAMA,IAAI,SAAU,CACZ,OAAO,IACT,CAMA,IAAI,SAAU,CACZ,OAAO,IACT,CAMA,IAAI,QAAS,CACX,OAAO,IACT,CAMA,IAAI,WAAY,CACd,OAAO,IACT,CAKA,IAAI,UAAW,CACb,OAAO,IAAI,CAAC,SAAS,AACvB,CAKA,IAAI,YAAa,CACf,OAAO,IAAI,CAAC,WAAW,AACzB,CAKA,IAAI,KAAM,CACR,OAAO,IAAI,CAAC,IAAI,AAClB,CAkBA,UAAU,CAAM,CAAE,CAAI,CAAE,CAAO,CAAE,CAC/B,IAAM,EAAW,IAAI,EAAS,CAC5B,uBAAwB,EAAQ,sBAAsB,CACtD,WAAY,IAAI,CAAC,UAAU,CAC3B,WAAY,IAAI,CAAC,WAAW,CAC5B,SAAU,IAAI,CAAC,SAAS,CACxB,WAAY,EAAQ,UAAU,CAC9B,mBAAoB,EAAQ,kBAAkB,AAChD,GAEM,EAAS,IAAI,EAAO,EAAQ,IAAI,CAAC,WAAW,CAAE,EAAQ,YAAY,EAExE,IAAI,CAAC,SAAS,CAAG,EACjB,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,OAAO,CAAG,EAEf,CAAQ,CAAC,EAAW,CAAG,IAAI,CAC3B,CAAM,CAAC,EAAW,CAAG,IAAI,CACzB,CAAM,CAAC,EAAW,CAAG,IAAI,CAEzB,EAAS,EAAE,CAAC,WAAY,GACxB,EAAS,EAAE,CAAC,QAAS,GACrB,EAAS,EAAE,CAAC,QAAS,GACrB,EAAS,EAAE,CAAC,UAAW,GACvB,EAAS,EAAE,CAAC,OAAQ,GACpB,EAAS,EAAE,CAAC,OAAQ,GAEpB,EAAO,OAAO,CAAG,EAKb,EAAO,UAAU,EAAE,EAAO,UAAU,CAAC,GACrC,EAAO,UAAU,EAAE,EAAO,UAAU,GAEpC,EAAK,MAAM,CAAG,GAAG,EAAO,OAAO,CAAC,GAEpC,EAAO,EAAE,CAAC,QAAS,GACnB,EAAO,EAAE,CAAC,OAAQ,GAClB,EAAO,EAAE,CAAC,MAAO,GACjB,EAAO,EAAE,CAAC,QAAS,GAEnB,IAAI,CAAC,WAAW,CAAG,EAAU,IAAI,CACjC,IAAI,CAAC,IAAI,CAAC,OACZ,CAOA,WAAY,CACV,GAAI,CAAC,IAAI,CAAC,OAAO,CAAE,CACjB,IAAI,CAAC,WAAW,CAAG,EAAU,MAAM,CACnC,IAAI,CAAC,IAAI,CAAC,QAAS,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,aAAa,EACtD,MACF,CAEI,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,EAAE,AACrD,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,CAAC,OAAO,GAG3D,IAAI,CAAC,SAAS,CAAC,kBAAkB,GACjC,IAAI,CAAC,WAAW,CAAG,EAAU,MAAM,CACnC,IAAI,CAAC,IAAI,CAAC,QAAS,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,aAAa,CACxD,CAsBA,MAAM,CAAI,CAAE,CAAI,CAAE,CAChB,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,MAAM,EAAE,AAC1C,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,CAAE,YAE5C,EAAe,IAAI,CAAE,IAAI,CAAC,IAAI,CADlB,CACoB,6DAIlC,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,OAAO,CAAE,CAEvC,IAAI,CAAC,eAAe,GACnB,CAAD,GAAK,CAAC,mBAAmB,EAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,AAAZ,GAE3D,AADA,IACI,CAAC,OAAO,CAAC,GAAG,GAGlB,MACF,CAEA,IAAI,CAAC,WAAW,CAAG,EAAU,OAAO,CACpC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAM,EAAM,CAAC,IAAI,CAAC,SAAS,CAAG,AAAD,KAK1C,IAEJ,CAFS,GAEL,CAAC,eAAe,EAAG,GAGrB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAA,AAAY,EAC1C,CACA,IAAI,CAAC,OAAO,CAAC,GAAG,GAEpB,GAEA,EAAc,IAAI,EACpB,CAOA,OAAQ,CAEJ,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,EACxC,IAAI,CAAC,UAAU,GAAK,EAAU,MAAM,EACpC,CAIF,IAAI,CAAC,OAAO,EAAG,EACf,IAAI,CAAC,OAAO,CAAC,KAAK,GACpB,CAUA,KAAK,CAAI,CAAE,CAAI,CAAE,CAAE,CAAE,CACnB,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,CAC1C,CAD4C,KACtC,AAAI,MAAM,oDAalB,GAVI,AAAgB,YAAY,OAArB,GACT,EAAK,EACL,EAAO,OAAO,GACW,YAAhB,AAA4B,OAArB,IAChB,EAAK,EACL,OAAO,GAGW,UAAhB,OAAO,IAAmB,EAAO,EAAK,QAAQ,EAAA,EAE9C,IAAI,CAAC,UAAU,GAAK,EAAU,IAAI,CAAE,YACtC,EAAe,IAAI,CAAE,EAAM,QAIhB,IAAT,IAAoB,EAAO,CAAC,IAAI,CAAC,SAAA,AAAS,EAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAQ,EAAc,EAAM,EAChD,CAUA,KAAK,CAAI,CAAE,CAAI,CAAE,CAAE,CAAE,CACnB,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,CAC1C,CAD4C,KACtC,AAAI,MAAM,oDAalB,GAVoB,YAAY,AAA5B,OAAO,GACT,EAAK,EACL,EAAO,OAAO,GACW,YAAY,AAA5B,OAAO,IAChB,EAAK,EACL,OAAO,GAGL,AAAgB,iBAAT,IAAmB,EAAO,EAAK,QAAQ,EAAA,EAE9C,IAAI,CAAC,UAAU,GAAK,EAAU,IAAI,CAAE,YACtC,EAAe,IAAI,CAAE,EAAM,QAIhB,IAAT,IAAoB,EAAO,CAAC,IAAI,CAAC,SAAA,AAAS,EAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAQ,EAAc,EAAM,EAChD,CAOA,QAAS,CAEL,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,EACxC,IAAI,CAAC,UAAU,GAAK,EAAU,MAAM,EACpC,CAIF,IAAI,CAAC,OAAO,EAAG,EACX,AAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GACnE,CAiBA,KAAK,CAAI,CAAE,CAAO,CAAE,CAAE,CAAE,CACtB,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,CAC1C,CAD4C,KACtC,AAAI,MAAM,oDAUlB,GAPuB,YAAnB,AAA+B,OAAxB,IACT,EAAK,EACL,EAAU,CAAC,GAGO,UAAhB,OAAO,IAAmB,EAAO,EAAK,QAAQ,EAAA,EAE9C,IAAI,CAAC,UAAU,GAAK,EAAU,IAAI,CAAE,YACtC,EAAe,IAAI,CAAE,EAAM,GAI7B,IAAM,EAAO,CACX,OAAwB,UAAhB,OAAO,EACf,KAAM,CAAC,IAAI,CAAC,SAAS,CACrB,UAAU,EACV,KAAK,EACL,GAAG,CAAO,AACZ,CAEI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAkB,aAAa,CAAC,EAAE,CACtD,EAAK,QAAQ,EAAG,CAAA,EAGlB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAQ,EAAc,EAAM,EAChD,CAOA,WAAY,CACV,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,MAAM,EAAE,AAC1C,GAAI,IAAI,CAAC,UAAU,GAAK,EAAU,UAAU,CAAE,YAE5C,EAAe,IAAI,CAAE,IAAI,CAAC,IAAI,CADlB,CACoB,6DAI9B,IAAI,CAAC,OAAO,EAAE,CAChB,IAAI,CAAC,WAAW,CAAG,EAAU,OAAO,CACpC,IAAI,CAAC,OAAO,CAAC,OAAO,IAExB,CACF,CAwhBA,SAAS,EAAkB,CAAS,CAAE,CAAG,EACvC,EAAU,WAAW,CAAG,EAAU,OAAO,CAKzC,EAAU,aAAa,EAAG,EAC1B,EAAU,IAAI,CAAC,QAAS,GACxB,EAAU,SAAS,EACrB,CASA,SAAS,EAAW,CAAO,EAEzB,OADA,EAAQ,IAAI,CAAG,EAAQ,UAAU,CAC1B,EAAI,OAAO,CAAC,EACrB,CASA,SAAS,EAAW,CAAO,EAOzB,OANA,EAAQ,IAAI,MAAG,EAEV,AAAD,EAAS,UAAU,EAA2B,IAAI,CAA3B,EAAQ,UAAU,GAC3C,EAAQ,UAAU,CAAG,EAAI,IAAI,CAAC,EAAQ,IAAI,EAAI,GAAK,EAAQ,IAAA,AAAI,EAG1D,EAAI,OAAO,CAAC,EACrB,CAWA,SAAS,EAAe,CAAS,CAAE,CAAM,CAAE,CAAO,EAChD,EAAU,WAAW,CAAG,EAAU,OAAO,CAEzC,IAAM,EAAM,AAAI,MAAM,GACtB,MAAM,iBAAiB,CAAC,EAAK,GAEzB,EAAO,SAAS,EAAE,AACpB,CAAM,CAAC,EAAS,EAAG,EACnB,EAAO,KAAK,GAER,EAAO,MAAM,EAAI,CAAC,EAAO,MAAM,CAAC,SAAS,EAAE,AAM7C,EAAO,MAAM,CAAC,OAAO,GAGvB,QAAQ,QAAQ,CAAC,EAAmB,EAAW,KAE/C,EAAO,OAAO,CAAC,GACf,EAAO,IAAI,CAAC,QAAS,EAAU,IAAI,CAAC,IAAI,CAAC,EAAW,UACpD,EAAO,IAAI,CAAC,QAAS,EAAU,SAAS,CAAC,IAAI,CAAC,IAElD,CAWA,SAAS,EAAe,CAAS,CAAE,CAAI,CAAE,CAAE,EACzC,GAAI,EAAM,CACR,IAAM,EAAS,EAAO,GAAQ,EAAK,IAAI,CAAG,EAAS,GAAM,MAAM,CAQ3D,EAAU,OAAO,CAAE,EAAU,OAAO,CAAC,cAAc,EAAI,EACtD,EAAU,eAAe,EAAI,CACpC,CAEA,GAAI,EAAI,CACN,IAAM,EAAM,AAAI,MACd,CAAC,kCAAkC,EAAE,EAAU,UAAU,CAAC,AACvD,CADwD,CACvD,EAAE,CAAW,CAAC,EAAU,UAAU,CAAC,CAAC,CAAC,CADmB,AAClB,EAE5C,CAFI,OAEI,QAAQ,CAAC,EAAI,EACvB,CACF,CASA,SAAS,EAAmB,CAAI,CAAE,CAAM,EACtC,IAAM,EAAY,IAAI,CAAC,EAAW,CAElC,EAAU,mBAAmB,EAAG,EAChC,EAAU,aAAa,CAAG,EAC1B,EAAU,UAAU,CAAG,EAEe,SAAlC,EAAU,AAAmC,OAA5B,CAAC,EAAW,GAEjC,EAAU,OAAO,CAAC,cAAc,CAAC,OAAQ,GACzC,QAAQ,QAAQ,CAAC,EAAQ,EAAU,OAAO,EAE7B,OAAT,EAAe,EAAU,KAAK,GAC7B,EAAU,KAAK,CAAC,EAAM,GAC7B,CAOA,SAAS,IACP,IAAM,EAAY,IAAI,CAAC,EAAW,AAE9B,CAAC,EAAU,QAAQ,EAAE,EAAU,OAAO,CAAC,MAAM,EACnD,CAQA,SAAS,EAAgB,CAAG,EAC1B,IAAM,EAAY,IAAI,CAAC,EAAW,MAEI,IAAlC,EAAU,KAAmC,EAA5B,CAAC,EAAW,GAC/B,EAAU,OAAO,CAAC,cAAc,CAAC,OAAQ,GAMzC,QAAQ,QAAQ,CAAC,EAAQ,EAAU,OAAO,EAE1C,EAAU,KAAK,CAAC,CAAG,CAAC,EAAY,GAG7B,EAAU,aAAa,EAAE,CAC5B,EAAU,aAAa,EAAG,EAC1B,EAAU,IAAI,CAAC,QAAS,GAE5B,CAOA,SAAS,IACP,IAAI,CAAC,EAAW,CAAC,SAAS,EAC5B,CASA,SAAS,EAAkB,CAAI,CAAE,CAAQ,EACvC,IAAI,CAAC,EAAW,CAAC,IAAI,CAAC,UAAW,EAAM,EACzC,CAQA,SAAS,EAAe,CAAI,EAC1B,IAAM,EAAY,IAAI,CAAC,EAAW,CAE9B,EAAU,SAAS,EAAE,EAAU,IAAI,CAAC,EAAM,CAAC,IAAI,CAAC,SAAS,CAAE,GAC/D,EAAU,IAAI,CAAC,OAAQ,EACzB,CAQA,SAAS,EAAe,CAAI,EAC1B,IAAI,CAAC,EAAW,CAAC,IAAI,CAAC,OAAQ,EAChC,CAQA,SAAS,EAAO,CAAM,EACpB,EAAO,MAAM,EACf,CAQA,SAAS,EAAc,CAAG,EACxB,IAAM,EAAY,IAAI,CAAC,EAAW,CAE9B,EAAU,UAAU,GAAK,EAAU,MAAM,EAAE,CAC3C,EAAU,UAAU,GAAK,EAAU,IAAI,EAAE,CAC3C,EAAU,WAAW,CAAG,EAAU,OAAO,CACzC,EAAc,IAQhB,IAAI,CAAC,OAAO,CAAC,GAAG,GAEX,EAAU,aAAa,EAAE,CAC5B,EAAU,aAAa,EAAG,EAC1B,EAAU,IAAI,CAAC,QAAS,IAE5B,CAQA,SAAS,EAAc,CAAS,EAC9B,EAAU,WAAW,CAAG,WACtB,EAAU,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAU,OAAO,EAzuC/B,CA0uCjB,GAEJ,CAOA,AAnvC0B,SAmvCjB,IACP,IAQI,EARE,EAAY,IAAI,CAAC,EAAW,CAElC,IAAI,CAAC,cAAc,CAAC,QAAS,GAC7B,IAAI,CAAC,cAAc,CAAC,OAAQ,GAC5B,IAAI,CAAC,cAAc,CAAC,MAAO,GAE3B,EAAU,WAAW,CAAG,EAAU,OAAO,CAcvC,AAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAC9B,EAAD,AAAW,mBAAmB,EAC7B,EAAD,AAAW,SAAS,CAAC,cAAc,CAAC,YAAY,EAChD,AAAuC,MACvC,EADC,EAAQ,EAAU,OAAO,CAAC,IAAI,EAAA,CAAE,EAEjC,EAAU,SAAS,CAAC,KAAK,CAAC,GAG5B,EAAU,SAAS,CAAC,GAAG,GAEvB,IAAI,CAAC,EAAW,MAAG,EAEnB,aAAa,EAAU,WAAW,EAGhC,EAAU,SAAS,CAAC,cAAc,CAAC,QAAQ,EAC3C,EAAU,SAAS,CAAC,cAAc,CAAC,YAAY,CAE/C,CADA,CACU,SAAS,IAEnB,EAAU,SAAS,CAAC,EAAE,CAAC,QAAS,GAChC,EAAU,SAAS,CAAC,EAAE,CAAC,SAAU,GAErC,CAQA,SAAS,EAAa,CAAK,EACrB,AAAC,IAAI,CAAC,EAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IACpC,IAD4C,AACxC,CAAC,KAAK,EAEd,CAOA,SAAS,IACP,IAAM,EAAY,IAAI,CAAC,EAAW,AAElC,GAAU,WAAW,CAAG,EAAU,OAAO,CACzC,EAAU,SAAS,CAAC,GAAG,GACvB,IAAI,CAAC,GAAG,EACV,CAOA,SAAS,IACP,IAAM,EAAY,IAAI,CAAC,EAAW,CAElC,IAAI,CAAC,cAAc,CAAC,QAAS,GAC7B,IAAI,CAAC,EAAE,CAAC,QAAS,GAEb,IACF,EAAU,KADG,MACQ,CAAG,EAAU,OAAO,CACzC,IAAI,CAAC,OAAO,GAEhB,CAp3BA,OAAO,cAAc,CAAC,EAAW,aAAc,CAC7C,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,aAC7B,GAMA,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,aAAc,CACvD,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,aAC7B,GAMA,OAAO,cAAc,CAAC,EAAW,OAAQ,CACvC,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,OAC7B,GAMA,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,OAAQ,CACjD,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,OAC7B,GAMA,OAAO,cAAc,CAAC,EAAW,UAAW,CAC1C,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,UAC7B,GAMA,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,UAAW,CACpD,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,UAC7B,GAMA,OAAO,cAAc,CAAC,EAAW,SAAU,CACzC,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,SAC7B,GAMA,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,SAAU,CACnD,YAAY,EACZ,MAAO,EAAY,OAAO,CAAC,SAC7B,GAEA,CACE,aACA,iBACA,aACA,WACA,WACA,aACA,MACD,CAAC,OAAO,CAAC,AAAC,IACT,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,EAAU,CAAE,WAAY,EAAK,EAC1E,GAMA,CAAC,OAAQ,QAAS,QAAS,UAAU,CAAC,OAAO,CAAC,AAAC,IAC7C,OAAO,cAAc,CAAC,EAAU,SAAS,CAAE,CAAC,EAAE,EAAE,EAAA,CAAQ,CAAE,CACxD,YAAY,EACZ,MACE,IAAK,IAAM,KAAY,IAAI,CAAC,SAAS,CAAC,GACpC,GAAI,CAAQ,CADiC,AAChC,EAAqB,CAAE,OAAO,CAAQ,CAAC,EAAU,CAGhE,OAAO,IACT,EACA,IAAI,CAAO,EACT,IAAK,IAAM,KAAY,IAAI,CAAC,SAAS,CAAC,GACpC,GAAI,CAAQ,CAAC,AADgC,EACX,CAAE,CAClC,IAAI,CAAC,cAAc,CAAC,EAAQ,GAC5B,KACF,CAGqB,YAAnB,AAA+B,OAAxB,GAEX,IAAI,CAAC,gBAAgB,CAAC,EAAQ,EAAS,CACrC,CAAC,EAAqB,EAAE,CAC1B,EACF,CACF,EACF,GAEA,EAAU,SAAS,CAAC,gBAAgB,CAAG,EACvC,EAAU,SAAS,CAAC,mBAAmB,CAAG,EAE1C,EAAO,OAAO,CAAG,uDCxmBjB,aAEM,EAAA,CAAA,CAAA,QACN,GAAM,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QAQhB,SAAS,EAAU,CAAM,EACvB,EAAO,IAAI,CAAC,QACd,CAOA,SAAS,IACH,CAAC,IAAI,CAAC,SAAS,EAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,AACnD,IAAI,CAAC,OAAO,EAEhB,CAQA,SAAS,EAAc,CAAG,EACxB,IAAI,CAAC,cAAc,CAAC,QAAS,GAC7B,IAAI,CAAC,OAAO,GACwB,GAAG,CAAnC,IAAI,CAAC,aAAa,CAAC,UAErB,IAAI,CAAC,IAAI,CAAC,QAAS,EAEvB,CAwHA,EAAO,OAAO,CA9Gd,EA8GiB,OA9GR,AAAsB,CAAE,CAAE,CAAO,EACxC,IAAI,EAAqB,GAEnB,EAAS,IAAI,EAAO,CACxB,GAAG,CAAO,CACV,aAAa,EACb,WAAW,EACX,YAAY,EACZ,oBAAoB,CACtB,GAkGA,OAhGA,EAAG,EAAE,CAAC,UAAW,SAAS,AAAQ,CAAG,CAAE,CAAQ,EAC7C,IAAM,EACJ,CAAC,GAAY,EAAO,cAAc,CAAC,UAAU,CAAG,EAAI,QAAQ,GAAK,CAE/D,CAAC,EAAO,IAAI,CAAC,IAAO,EAAG,KAAK,EAClC,GAEA,EAAG,IAAI,CAAC,QAAS,SAAS,AAAM,CAAG,EAC7B,EAAO,SAAS,EAAE,CAWtB,GAAqB,EACrB,EAAO,OAAO,CAAC,GACjB,GAEA,EAAG,IAAI,CAAC,QAAS,SAAS,EACpB,EAAO,SAAS,EAAE,AAEtB,EAAO,IAAI,CAAC,KACd,GAEA,EAAO,QAAQ,CAAG,SAAU,CAAG,CAAE,CAAQ,EACvC,GAAI,EAAG,UAAU,GAAK,EAAG,MAAM,CAAE,CAC/B,EAAS,GACT,QAAQ,QAAQ,CAAC,EAAW,GAC5B,MACF,CAEA,IAAI,GAAS,EAEb,EAAG,IAAI,CAAC,QAAS,SAAS,AAAM,CAAG,EACjC,GAAS,EACT,EAAS,EACX,GAEA,EAAG,IAAI,CAAC,QAAS,SAAS,EACpB,AAAC,GAAQ,EAAS,GACtB,QAAQ,QAAQ,CAAC,EAAW,EAC9B,GAEI,GAAoB,EAAG,SAAS,EACtC,EAEA,EAAO,MAAM,CAAG,SAAU,CAAQ,EAChC,GAAI,EAAG,UAAU,GAAK,EAAG,UAAU,CAAE,YACnC,EAAG,IAAI,CAAC,OAAQ,SAAS,EACvB,EAAO,MAAM,CAAC,EAChB,EAQiB,MAAM,EAArB,EAAG,OAAO,GAEV,EAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,AACtC,IACI,EAAO,cAAc,CAAC,UAAU,EAAE,EAAO,OAAO,KAEpD,EAAG,OAAO,CAAC,IAAI,CAAC,SAAU,SAAS,EAIjC,GACF,GACA,EAAG,KAAK,IAEZ,EAEA,EAAO,KAAK,CAAG,WACT,EAAG,QAAQ,EAAE,EAAG,MAAM,EAC5B,EAEA,EAAO,MAAM,CAAG,SAAU,CAAK,CAAE,CAAQ,CAAE,CAAQ,EACjD,GAAI,EAAG,UAAU,GAAK,EAAG,UAAU,CAAE,YACnC,EAAG,IAAI,CAAC,OAAQ,SAAS,EACvB,EAAO,MAAM,CAAC,EAAO,EAAU,EACjC,GAIF,EAAG,IAAI,CAAC,EAAO,EACjB,EAEA,EAAO,EAAE,CAAC,MAAO,GACjB,EAAO,EAAE,CAAC,QAAS,GACZ,CACT,uDC9JA,aAEA,GAAM,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,OA2DpB,GAAO,OAAO,CAAG,CAAE,MAlDnB,SAAS,AAAM,CAAM,EACnB,IAAM,EAAY,IAAI,IAClB,EAAQ,CAAC,EACT,EAAM,CAAC,EACP,EAAI,EAER,IAAK,CAAG,EAAI,EAAO,MAAM,CAAE,IAAK,CAC9B,IAAM,EAAO,EAAO,UAAU,CAAC,GAE/B,GAAI,AAAQ,CAAC,OAA0B,GAAG,CAAxB,CAAU,CAAC,EAAK,CAClB,CAAC,IAAX,IAAc,GAAQ,OACrB,GACC,IAAN,CACA,GAAU,KAAT,AAAc,GAAsB,AAAT,IAAN,CAAe,CAAI,CAE7B,CAAC,CADb,GACI,GAAwB,CAAC,IAAX,IAAc,GAAM,OACjC,GAAa,KAAT,AAAc,EAAW,CAClC,GAAc,CADgB,AACf,GAAG,CAAd,EACF,MAAM,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,EAGhD,CAAC,IAAT,GAAY,IAAM,EAEtB,IAAM,EAAW,EAAO,KAAK,CAAC,EAAO,GAErC,GAAI,EAAU,GAAG,CAAC,GAChB,MAAM,AAAI,EADiB,UACL,CAAC,KAAK,EAAE,EAAS,2BAA2B,CAAC,EAGrE,EAAU,GAAG,CAAC,GACd,EAAQ,EAAM,CAAC,CACjB,MACE,CADK,KACC,AAAI,YAAY,CAAC,8BAA8B,EAAE,EAAA,CAAG,CAE9D,CAEA,GAAc,CAAC,IAAX,GAAwB,CAAC,GAAG,CAAZ,EAClB,MAAM,AAAI,YAAY,2BAGxB,IAAM,EAAW,EAAO,KAAK,CAAC,EAAO,GAErC,GAAI,EAAU,GAAG,CAAC,GAChB,MAAM,AAAI,EADiB,UACL,CAAC,KAAK,EAAE,EAAS,2BAA2B,CAAC,EAIrE,OADA,EAAU,GAAG,CAAC,GACP,CACT,CAEyB,sDC3DzB,aAEA,IAAM,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,QAAE,CAAM,CAAE,CAAA,EAAA,CAAA,CAAA,QACV,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,QAEd,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,MAAE,CAAI,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,QAEpB,EAAW,wBAocjB,SAAS,EAAU,CAAM,EACvB,EAAO,MAAM,GAAG,AAChB,EAAO,IAAI,CAAC,QACd,CAOA,SAAS,IACP,IAAI,CAAC,OAAO,EACd,CAWA,SAAS,EAAe,CAAM,CAAE,CAAI,CAAE,CAAO,CAAE,CAAO,EASpD,EAAU,GAAW,EAAK,YAAY,CAAC,EAAK,CAC5C,EAAU,CACR,WAAY,QACZ,eAAgB,YAChB,iBAAkB,OAAO,UAAU,CAAC,GACpC,GAAG,CAAO,AACZ,EAEA,EAAO,IAAI,CAAC,SAAU,EAAO,OAAO,EAEpC,EAAO,GAAG,CACR,CAAC,SAAS,EAAE,EAAK,CAAC,EAAE,EAAK,YAAY,CAAC,EAAK,CAAC;AAAI,CAAC,CAC/C,OAAO,IAAI,CAAC,GACT,GAAG,CAAC,AAAC,GAAM,CAAA,EAAG,EAAE,EAAE,EAAE,CAAO,CAAC,EAAE,CAAA,CAAE,EAChC,IAAI,CAAC,QACR,WACA,EAEN,CAaA,SAAS,EAAkC,CAAM,CAAE,CAAG,CAAE,CAAM,CAAE,CAAI,CAAE,CAAO,EAC3E,GAAI,EAAO,aAAa,CAAC,iBAAkB,CACzC,IAAM,EAAM,AAAI,MAAM,GACtB,MAAM,iBAAiB,CAAC,EAAK,GAE7B,EAAO,IAAI,CAAC,gBAAiB,EAAK,EAAQ,EAC5C,MACE,CADK,CACU,EAAQ,EAAM,EAEjC,CApGA,EAAO,OAAO,CA7Zd,EA6ZiB,IA7ZX,QAAwB,EAgC5B,YAAY,CAAO,CAAE,CAAQ,CAAE,CAsB7B,GArBA,KAAK,GAsBF,AAAgB,OApBnB,EAAU,CACR,wBAAwB,EACxB,UAAU,EACV,WAAY,MAAM,IAClB,GADyB,iBACL,EACpB,mBAAmB,EACnB,gBAAiB,KACjB,gBAAgB,EAChB,aAAc,KACd,SAAU,GACV,QAAS,KACT,OAAQ,KACR,KAAM,KACN,KAAM,KACN,KAAM,eACN,EACA,GAAG,CAAO,CACZ,EAGW,IAAI,EAAY,CAAC,EAAQ,MAAM,EAAI,CAAC,EAAQ,QAAQ,EAC5C,MAAhB,EAAQ,AAAgB,IAAZ,GAAa,EAAQ,MAAM,EAAI,EAAQ,QAAA,AAAQ,GAC3D,EAAQ,MAAM,EAAI,EAAQ,QAAQ,CAEnC,CADA,KACM,AAAI,UACR,qEACE,gBAwBN,GApBoB,MAAhB,AAAsB,EAAd,IAAI,EACd,IAAI,CAAC,OAAO,CAAG,EAAK,YAAY,CAAC,CAAC,EAAK,KACrC,IAAM,EAAO,EAAK,YAAY,CAAC,IAAI,CAEnC,EAAI,SAAS,CAAC,IAAK,CACjB,iBAAkB,EAAK,MAAM,CAC7B,eAAgB,YAClB,GACA,EAAI,GAAG,CAAC,EACV,GACA,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,EAAQ,IAAI,CACZ,EAAQ,IAAI,CACZ,EAAQ,OAAO,CACf,IAEO,EAAQ,MAAM,EAAE,CACzB,IAAI,CAAC,OAAO,CAAG,EAAQ,MAAM,AAAN,EAGrB,IAAI,CAAC,OAAO,CAAE,CAChB,IAAM,EAAiB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,cAE5C,IAAI,CAAC,gBAAgB,CAAG,AAiV9B,SAAS,AAAa,CAAM,CAAE,CAAG,EAC/B,IAAK,IAAM,KAAS,OAAO,IAAI,CAAC,GAAM,EAAO,EAAE,CAAC,EAAO,CAAG,CAAC,EAAM,EAEjE,OAAO,SAAS,EACd,IAAK,IAAM,KAAS,OAAO,IAAI,CAAC,GAC9B,EADoC,AAC7B,cAAc,CAAC,EAAO,CAAG,CAAC,EAAM,CAE3C,CACF,EAzV2C,IAAI,CAAC,OAAO,CAAE,CACjD,UAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,aAChC,MAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,SAC5B,QAAS,CAAC,EAAK,EAAQ,KACrB,IAAI,CAAC,aAAa,CAAC,EAAK,EAAQ,EAAM,EACxC,CACF,EACF,CAEI,CAA8B,MAAtB,iBAAiB,EAAW,GAAQ,iBAAiB,CAAG,EAAC,EACjE,EAAQ,cAAc,EAAE,CAC1B,IAAI,CAAC,OAAO,CAAG,IAAI,IACnB,IAAI,CAAC,gBAAgB,EAAG,GAG1B,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,MAAM,EACb,CADgB,AAYhB,SAAU,CACR,GAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CACvB,CADyB,KACnB,AAAI,MAAM,qDAGb,AAAL,IAAS,AAAL,CAAM,OAAO,CACV,CADY,GACR,CAAC,OAAO,CAAC,OAAO,GADD,IAE5B,CASA,MAAM,CAAE,CAAE,CACR,GA5IW,IA4IP,IAAI,CAAC,MAAM,CAAa,CACtB,GADc,AAEhB,CADM,GACF,CAAC,IAAI,CAAC,QAAS,KACjB,EAAG,AAAI,MAAM,6BACf,GAGF,QAAQ,QAAQ,CAAC,EAAW,IAAI,EAChC,MACF,CAIA,GAFI,GAAI,IAAI,CAAC,IAAI,CAAC,QAAS,OAEvB,IAAI,CAAC,MAAM,CAGf,GAFA,CADoB,GAChB,CAAC,KADwB,CAClB,CA3JC,EA2JE,AAEV,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAC1C,CAD4C,GACxC,CAAC,OAAO,EAAE,CAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,gBAAgB,CAAG,IAAI,CAAC,OAAO,CAAG,MAGrC,IAAI,CAAC,OAAO,EAAE,AACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAGpB,CAHsB,GAGlB,CAAC,gBAAgB,EAAG,EAFxB,QAAQ,QAAQ,CAAC,EAAW,IAAI,MAO/B,CACL,IAAM,EAAS,IAAI,CAAC,OAAO,CAE3B,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,gBAAgB,CAAG,IAAI,CAAC,OAAO,CAAG,KAMvC,EAAO,KAAK,CAAC,KACX,EAAU,IAAI,CAChB,EACF,CACF,CASA,aAAa,CAAG,CAAE,CAChB,GAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAE,CACrB,IAAM,EAAQ,EAAI,GAAG,CAAC,OAAO,CAAC,KAG9B,GAAI,CAFuB,CAAC,IAAX,EAAe,EAAI,GAAG,CAAC,KAAK,CAAC,EAAG,GAAS,EAAI,GAAA,AAAG,IAEhD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAE,OAAO,CAC7C,CAEA,OAAO,CACT,CAWA,cAAc,CAAG,CAAE,CAAM,CAAE,CAAI,CAAE,CAAE,CAAE,CACnC,EAAO,EAAE,CAAC,QAAS,GAEnB,IAAM,EAAM,EAAI,OAAO,CAAC,oBAAoB,CACtC,EAAU,EAAI,OAAO,CAAC,OAAO,CAC7B,EAAU,CAAC,EAAI,OAAO,CAAC,wBAAwB,CAErD,GAAmB,QAAf,EAAI,MAAM,CAAY,YAExB,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADrC,CAC0C,sBAI5D,QAAgB,IAAZ,GAAmD,cAA1B,EAAQ,WAAW,GAAoB,YAElE,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADrC,CAC0C,yBAI5D,QAAY,IAAR,GAAqB,CAAC,EAAS,IAAI,CAAC,GAAM,YAE5C,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADrC,CAC0C,8CAI5D,GAAI,AAAY,OAAiB,KAAZ,EAAgB,YAEnC,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADrC,CAC0C,kDAI5D,GAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAM,YAC3B,EAAe,EAAQ,KAIzB,IAAM,EAAuB,EAAI,OAAO,CAAC,yBAAyB,CAC9D,EAAY,IAAI,IAEpB,QAA6B,IAAzB,EACF,GAAI,CACF,CAFoC,CAExB,EAAY,KAAK,CAAC,EAChC,CAAE,MAAO,EAAK,CAEZ,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADrC,CAC0C,wCAC1D,MACF,CAGF,IAAM,EAAyB,EAAI,OAAO,CAAC,2BAA2B,CAChE,EAAa,CAAC,EAEpB,GACE,IAAI,CAAC,OAAO,CAAC,iBAAiB,OACH,IAA3B,EACA,CACA,IAAM,EAAoB,IAAI,EAC5B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,EACA,IAAI,CAAC,OAAO,CAAC,UAAU,EAGzB,GAAI,CACF,IAAM,EAAS,EAAU,KAAK,CAAC,GAE3B,CAAM,CAAC,EAAkB,aAAa,CAAC,EAAE,CAC3C,EAAkB,MAAM,CAAC,CAAM,CAAC,EAAkB,aAAa,CAAC,EAChE,CAAU,CAAC,EAAkB,aAAa,CAAC,CAAG,EAElD,CAAE,MAAO,EAAK,CAGZ,EAAkC,IAAI,CAAE,EAAK,EAAQ,IADnD,CACwD,0DAC1D,MACF,CACF,CAKA,GAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAE,CAC7B,IAAM,EAAO,CACX,OACE,EAAI,OAAO,CAAC,CAAA,EAAe,IAAZ,EAAgB,uBAAyB,SAAA,CAAU,CAAC,CACrE,OAAQ,CAAC,CAAC,CAAC,EAAI,MAAM,CAAC,UAAU,EAAI,EAAI,MAAM,CAAC,SAAA,AAAS,MACxD,CACF,EAEA,GAAyC,IAArC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAQ,YAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAM,CAAC,EAAU,EAAM,EAAS,KACxD,GAAI,CAAC,EACH,OAAO,CADM,CACS,EAAQ,GAAQ,IAAK,EAAS,GAGtD,IAAI,CAAC,eAAe,CAClB,EACA,EACA,EACA,EACA,EACA,EACA,EAEJ,GAIF,GAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAO,OAAO,EAAe,EAAQ,IACtE,CAEA,IAAI,CAAC,eAAe,CAAC,EAAY,EAAK,EAAW,EAAK,EAAQ,EAAM,EACtE,CAeA,gBAAgB,CAAU,CAAE,CAAG,CAAE,CAAS,CAAE,CAAG,CAAE,CAAM,CAAE,CAAI,CAAE,CAAE,CAAE,CAIjE,GAAI,CAAC,EAAO,QAAQ,EAAI,CAAC,EAAO,QAAQ,CAAE,OAAO,EAAO,OAAO,GAE/D,GAAI,CAAM,CAAC,EAAW,CACpB,CADsB,KAChB,AAAI,MACR,oEACE,yCAIN,GAAI,IAAI,CAAC,MAAM,CAnWH,EAmWM,AAAS,OAAO,EAAe,EAAQ,KAEzD,IAAM,EAAS,EAAW,QACvB,MAAM,CAAC,EAAM,GACb,MAAM,CAAC,UAEJ,EAAU,CACd,mCACA,qBACA,sBACA,CAAC,sBAAsB,EAAE,EAAA,CAAQ,CAClC,CAEK,EAAK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAM,EAAW,IAAI,CAAC,OAAO,EAEnE,GAAI,EAAU,IAAI,CAAE,CAIlB,IAAM,EAAW,IAAI,CAAC,OAAO,CAAC,eAAe,CACzC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAW,GACxC,EAAU,MAAM,GAAG,IAAI,GAAG,KAAK,CAE/B,IACF,EAAQ,IADI,AACA,CAAC,CAAC,wBAAwB,EAAE,EAAA,CAAU,EAClD,EAAG,SAAS,CAAG,EAEnB,CAEA,GAAI,CAAU,CAAC,EAAkB,aAAa,CAAC,CAAE,CAC/C,IAAM,EAAS,CAAU,CAAC,EAAkB,aAAa,CAAC,CAAC,MAAM,CAC3D,EAAQ,EAAU,MAAM,CAAC,CAC7B,CAAC,EAAkB,aAAa,CAAC,CAAE,CAAC,EAAO,AAC7C,GACA,EAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,EAAA,CAAO,EACjD,EAAG,WAAW,CAAG,CACnB,CAKA,IAAI,CAAC,IAAI,CAAC,UAAW,EAAS,GAE9B,EAAO,KAAK,CAAC,EAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC,SACzC,EAAO,cAAc,CAAC,QAAS,GAE/B,EAAG,SAAS,CAAC,EAAQ,EAAM,CACzB,uBAAwB,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAC3D,WAAY,IAAI,CAAC,OAAO,CAAC,UAAU,CACnC,mBAAoB,IAAI,CAAC,OAAO,CAAC,kBAAkB,AACrD,GAEI,IAAI,CAAC,OAAO,EAAE,CAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GACjB,EAAG,EAAE,CAAC,QAAS,KACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAEhB,IAAI,CAAC,gBAAgB,EAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,AAC/C,QAAQ,QAAQ,CAAC,EAAW,IAAI,CAEpC,IAGF,EAAG,EAAI,EACT,CACF,yECrbA,EAAA,CAAA,CAAA,QACA,EAAA,CAAA,CAAA,QACA,EAAA,CAAA,CAAA,QACA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,CAAA,CAAA,aAGe,EAAA,OAAS,8BAAT", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}