module.exports={922736:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],c=(0,d.default)("lock",b)}},177092:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Lock:()=>d.default});var d=a.i(922736)},182960:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],c=(0,d.default)("eye-off",b)}},616227:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({EyeOff:()=>d.default});var d=a.i(182960)},758257:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({changePassword:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("608ed2498189cfd7c9da6c6017df4df3d3e1da70c2",d.callServer,void 0,d.findSourceMapURL,"changePassword")},274115:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({PasswordChangeModal:()=>k});var d=a.i(674420),e=a.i(722851),f=a.i(767332),g=a.i(614820),h=a.i(616227),i=a.i(177092),j=a.i(758257);function k({isOpen:a,onClose:b,doctorId:c}){let[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(null),u=async a=>{r(!0),t(null);try{let d=await (0,j.changePassword)(c,a);t(d),d.success&&setTimeout(()=>{b(),t(null)},2e3)}catch(a){t({success:!1,message:"An unexpected error occurred"})}finally{r(!1)}};return a?(0,d.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto bg-black/50 backdrop-blur-sm",children:(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen p-4",children:(0,d.jsxs)("div",{className:"w-full max-w-sm sm:max-w-md bg-white rounded-xl shadow-2xl transform transition-all",children:[(0,d.jsx)("div",{className:"px-4 sm:px-6 py-4 border-b border-orange-100 bg-gradient-to-r from-orange-50 to-amber-50 rounded-t-xl",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,d.jsx)(i.Lock,{className:"w-4 h-4 text-orange-600"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-slate-800",children:"Change Password"})]}),(0,d.jsx)("button",{onClick:b,disabled:q,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-orange-100 disabled:opacity-50",children:(0,d.jsx)(f.X,{className:"w-5 h-5"})})]})}),(0,d.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,d.jsxs)("form",{action:u,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-slate-700 mb-2",children:"Current Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{id:"currentPassword",name:"currentPassword",type:k?"text":"password",required:!0,className:"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all",placeholder:"Enter current password"}),(0,d.jsx)("button",{type:"button",onClick:()=>l(!k),className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors",children:k?(0,d.jsx)(h.EyeOff,{className:"w-4 h-4"}):(0,d.jsx)(g.Eye,{className:"w-4 h-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-slate-700 mb-2",children:"New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{id:"newPassword",name:"newPassword",type:m?"text":"password",required:!0,minLength:8,className:"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all",placeholder:"Enter new password"}),(0,d.jsx)("button",{type:"button",onClick:()=>n(!m),className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors",children:m?(0,d.jsx)(h.EyeOff,{className:"w-4 h-4"}):(0,d.jsx)(g.Eye,{className:"w-4 h-4"})})]}),(0,d.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"Must be at least 8 characters long"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-slate-700 mb-2",children:"Confirm New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:o?"text":"password",required:!0,minLength:8,className:"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all",placeholder:"Confirm new password"}),(0,d.jsx)("button",{type:"button",onClick:()=>p(!o),className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors",children:o?(0,d.jsx)(h.EyeOff,{className:"w-4 h-4"}):(0,d.jsx)(g.Eye,{className:"w-4 h-4"})})]})]}),s&&(0,d.jsxs)("div",{className:`p-3 rounded-lg border text-sm ${s.success?"bg-green-50 border-green-200 text-green-700":"bg-red-50 border-red-200 text-red-700"}`,children:[(0,d.jsx)("p",{className:"font-medium",children:s.message}),s.fieldErrors&&(0,d.jsx)("div",{className:"mt-2 space-y-1",children:Object.entries(s.fieldErrors).map(([a,b])=>(0,d.jsxs)("p",{className:"text-xs",children:[a,": ",b.join(", ")]},a))})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-2",children:[(0,d.jsx)("button",{type:"button",onClick:b,disabled:q,className:"px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all disabled:opacity-50 order-2 sm:order-1",children:"Cancel"}),(0,d.jsx)("button",{type:"submit",disabled:q,className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 border border-transparent rounded-lg hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-[1.02] active:scale-[0.98] order-1 sm:order-2",children:q?(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,d.jsx)("span",{children:"Changing..."})]}):"Change Password"})]})]})})]})})}):null}},659204:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({SettingsForm:()=>j});var d=a.i(674420),e=a.i(722851),f=a.i(12372),g=a.i(455558),h=a.i(177092),i=a.i(274115);function j({doctorId:a,doctorName:b,doctorEmail:c}){let[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(""),[n,o]=(0,e.useState)("success"),[p,q]=(0,e.useState)(!1),r=async()=>{k(!0),m("");try{o("success"),m("Settings updated successfully!")}catch{o("error"),m("An unexpected error occurred")}finally{k(!1),setTimeout(()=>m(""),3e3)}};return(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-800 mb-2 sm:mb-0",children:"Doctor Information"}),(0,d.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-fit",children:[(0,d.jsx)(h.Lock,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Change Password"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-slate-600",children:"Name:"}),(0,d.jsx)("span",{className:"ml-2 text-slate-800",children:b})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-slate-600",children:"Email:"}),(0,d.jsx)("span",{className:"ml-2 text-slate-800",children:c})]})]})]}),l&&(0,d.jsx)("div",{className:`p-4 rounded-xl border ${"success"===n?"bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200":"bg-gradient-to-r from-red-50 to-pink-50 border-red-200"}`,children:(0,d.jsxs)("p",{className:`text-sm font-medium flex items-center space-x-2 ${"success"===n?"text-emerald-800":"text-red-800"}`,children:[(0,d.jsx)("span",{className:`w-2 h-2 rounded-full ${"success"===n?"bg-emerald-400":"bg-red-400"}`}),(0,d.jsx)("span",{children:l})]})}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-white/30",children:[(0,d.jsxs)("button",{onClick:()=>{m("")},className:"flex items-center space-x-2 px-4 py-3 border border-slate-200 text-sm font-medium rounded-xl text-slate-700 bg-white/70 hover:bg-gradient-to-r hover:from-slate-50 hover:to-indigo-50 transition-all duration-300 transform hover:scale-105 active:scale-95",children:[(0,d.jsx)(g.RotateCcw,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Reset to Defaults"})]}),(0,d.jsxs)("button",{onClick:r,disabled:j,className:"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:[(0,d.jsx)(f.Save,{className:`w-4 h-4 ${j?"animate-spin":""}`}),(0,d.jsx)("span",{children:j?"Saving...":"Save Settings"})]})]}),(0,d.jsx)(i.PasswordChangeModal,{isOpen:p,onClose:()=>q(!1),doctorId:a})]})}}};

//# sourceMappingURL=_ac744579._.js.map