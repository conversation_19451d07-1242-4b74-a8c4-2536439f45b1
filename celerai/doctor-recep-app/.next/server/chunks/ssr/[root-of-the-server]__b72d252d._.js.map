{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/auth/session.ts"], "sourcesContent": ["import 'server-only'\nimport { SignJWT, jwtVerify } from 'jose'\nimport { cookies } from 'next/headers'\nimport { SessionPayload } from '@/lib/types' // UNCOMMENT THIS LINE\n// TODO: Remove this line (type SessionPayload = any)\n// type SessionPayload = any // REMOVE THIS LINE\n\nconst secretKey = process.env.SESSION_SECRET\nconst encodedKey = new TextEncoder().encode(secretKey)\n\nexport async function encrypt(payload: SessionPayload) {\n  // Fix: Cast payload to Record<string, unknown> for SignJWT\n  return new SignJWT(payload as unknown as Record<string, unknown>) // Keep this cast\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('7d')\n    .sign(encodedKey)\n}\n\nexport async function decrypt(session: string | undefined = '') {\n  try {\n    if (!session) {\n      return null\n    }\n\n    const { payload } = await jwtVerify(session, encodedKey, {\n      algorithms: ['HS256'],\n    })\n    // Fix: cast to unknown first, then to SessionPayload for type safety\n    return payload as unknown as SessionPayload // Keep this cast\n  } catch {\n    console.log('Failed to verify session')\n    return null\n  }\n}\n\nexport async function createSession(userId: string) {\n  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n  const session = await encrypt({ userId, expiresAt })\n  const cookieStore = await cookies()\n\n  console.log('DEBUG: Creating session for user:', userId)\n  console.log('DEBUG: Session expires at:', expiresAt)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expiresAt,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session cookie set successfully')\n}\n\nexport async function updateSession() {\n  const cookieStore = await cookies()\n  const session = cookieStore.get('session')?.value\n  const payload = await decrypt(session)\n\n  console.log('DEBUG: Updating session - session exists:', !!session)\n  console.log('DEBUG: Updating session - payload valid:', !!payload)\n\n  if (!session || !payload) {\n    console.log('DEBUG: Cannot update session - missing session or payload')\n    return null\n  }\n\n  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expires,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session updated successfully')\n}\n\nexport async function refreshSession(userId: string) {\n  // Delete old session and create new one\n  console.log('DEBUG: Refreshing session for user:', userId)\n  await deleteSession()\n  await createSession(userId)\n  console.log('DEBUG: Session refresh completed')\n}\n\nexport async function deleteSession() {\n  const cookieStore = await cookies()\n  console.log('DEBUG: Deleting session cookie')\n  cookieStore.delete('session')\n  console.log('DEBUG: Session cookie deleted')\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAEA,qDAAqD;AACrD,gDAAgD;AAEhD,MAAM,YAAY,QAAQ,GAAG,CAAC,cAAc;AAC5C,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;AAErC,eAAe,QAAQ,OAAuB;IACnD,2DAA2D;IAC3D,OAAO,IAAI,qJAAA,CAAA,UAAO,CAAC,SAA+C,iBAAiB;KAChF,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,MAClB,IAAI,CAAC;AACV;AAEO,eAAe,QAAQ,UAA8B,EAAE;IAC5D,IAAI;QACF,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;YACvD,YAAY;gBAAC;aAAQ;QACvB;QACA,qEAAqE;QACrE,OAAO,SAAqC,iBAAiB;IAC/D,EAAE,OAAM;QACN,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;AACF;AAEO,eAAe,cAAc,MAAc;IAChD,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAC3D,MAAM,UAAU,MAAM,QAAQ;QAAE;QAAQ;IAAU;IAClD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,QAAQ,GAAG,CAAC,qCAAqC;IACjD,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,UAAU,YAAY,GAAG,CAAC,YAAY;IAC5C,MAAM,UAAU,MAAM,QAAQ;IAE9B,QAAQ,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,IAAI,CAAC,WAAW,CAAC,SAAS;QACxB,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAEzD,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe,eAAe,MAAc;IACjD,wCAAwC;IACxC,QAAQ,GAAG,CAAC,uCAAuC;IACnD,MAAM;IACN,MAAM,cAAc;IACpB,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,QAAQ,GAAG,CAAC;IACZ,YAAY,MAAM,CAAC;IACnB,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from '@/lib/types'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/auth/dal.ts"], "sourcesContent": ["import 'server-only'\nimport { cache } from 'react'\nimport { cookies } from 'next/headers'\nimport { redirect } from 'next/navigation'\nimport { decrypt } from './session'\nimport { createClient } from '@/lib/supabase/server'\nimport { <PERSON> } from '@/lib/types'\n\n\n\nexport const verifySession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    redirect('/login')\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const checkSession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    return null\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const getUser = cache(async (): Promise<Doctor | null> => {\n  const session = await verifySession()\n  if (!session) return null\n\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('*')\n      .eq('id', session.userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n      password_hash: user.password_hash\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\nexport const getUserById = cache(async (userId: string): Promise<Doctor | null> => {\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user by ID:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user by ID:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\n// Get quota information for a doctor\nexport const getDoctorQuota = cache(async (userId: string) => {\n  try {\n    const supabase = await createClient()\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('monthly_quota, quota_used, quota_reset_at')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch quota:', error.message || error)\n      return null\n    }\n\n    const quotaRemaining = doctor.monthly_quota - doctor.quota_used\n    const quotaPercentage = Math.round((doctor.quota_used / doctor.monthly_quota) * 100)\n    const resetDate = new Date(doctor.quota_reset_at)\n    const daysUntilReset = Math.ceil((resetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))\n\n    return {\n      monthly_quota: doctor.monthly_quota,\n      quota_used: doctor.quota_used,\n      quota_remaining: quotaRemaining,\n      quota_percentage: quotaPercentage,\n      quota_reset_at: doctor.quota_reset_at,\n      days_until_reset: Math.max(0, daysUntilReset),\n    }\n  } catch (error) {\n    console.error('Failed to fetch quota:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AAKO,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACjC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,YAAY,GAAG,CAAC,YAAY;IAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE;IAE9B,IAAI,CAAC,SAAS,QAAQ;QACpB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,OAAO;QAAE,QAAQ;QAAM,QAAQ,QAAQ,MAAM;IAAC;AAChD;AAEO,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAA<PERSON>,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,YAAY,GAAG,CAAC,YAAY;IAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE;IAE9B,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,OAAO;QAAE,QAAQ;QAAM,QAAQ,QAAQ,MAAM;IAAC;AAChD;AAEO,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAC3B,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,MAAM,EACvB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB,MAAM,OAAO,IAAI;YACxD,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,OAAO;QAClB,oEAAoE;QACpE,OAAO;YACL,GAAG,IAAI;YACP,eAAe,KAAK,aAAa;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChF,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC,6JACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B,MAAM,OAAO,IAAI;YAC9D,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,OAAO;QAClB,oEAAoE;QACpE,OAAO;YACL,GAAG,IAAI;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtF,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC,6CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B,MAAM,OAAO,IAAI;YACzD,OAAO;QACT;QAEA,MAAM,iBAAiB,OAAO,aAAa,GAAG,OAAO,UAAU;QAC/D,MAAM,kBAAkB,KAAK,KAAK,CAAC,AAAC,OAAO,UAAU,GAAG,OAAO,aAAa,GAAI;QAChF,MAAM,YAAY,IAAI,KAAK,OAAO,cAAc;QAChD,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,UAAU,OAAO,KAAK,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1F,OAAO;YACL,eAAe,OAAO,aAAa;YACnC,YAAY,OAAO,UAAU;YAC7B,iBAAiB;YACjB,kBAAkB;YAClB,gBAAgB,OAAO,cAAc;YACrC,kBAAkB,KAAK,GAAG,CAAC,GAAG;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjF,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport Image from 'next/image'\nimport { <PERSON><PERSON>, ArrowR<PERSON>, Clock, Sparkles, Wand2, FileCheck, Zap, ChevronDown, Timer, Headphones } from 'lucide-react'\nimport { checkSession } from '@/lib/auth/dal'\nimport { redirect } from 'next/navigation'\n\nexport default async function Home() {\n  // Check if user is already logged in and redirect to dashboard\n  const session = await checkSession()\n  if (session) {\n    redirect('/dashboard')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden\">\n      {/* Floating Navigation */}\n      <nav className=\"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20\">\n        <div className=\"flex items-center space-x-8\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"relative w-8 h-8\">\n              <Image\n                src=\"/celer-ai-logo.svg\"\n                alt=\"Celer AI\"\n                width={32}\n                height={32}\n                className=\"rounded-lg\"\n              />\n            </div>\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold\">\n              Celer AI\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <Link\n              href=\"/login\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Sign In\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200\"\n            >\n              Start Free\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <main className=\"relative\">\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse\"></div>\n          <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6 pt-32 pb-16\">\n          <div className=\"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]\">\n            \n            {/* Left Column */}\n            <div className=\"space-y-8\">\n              <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2\">\n                <Sparkles className=\"w-4 h-4 text-indigo-600 animate-pulse\" />\n                <span className=\"text-indigo-700 text-sm font-medium\">AI Magic for Medical Docs</span>\n                <Wand2 className=\"w-4 h-4 text-purple-600\" />\n              </div>\n\n              <div className=\"space-y-4\">\n                <h1 className=\"text-6xl md:text-7xl font-black text-slate-900 leading-none\">\n                  Speak.\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse\">\n                    Generate.\n                  </span>\n                  <span className=\"block text-slate-700\">\n                    Done.\n                  </span>\n                </h1>\n                \n                <div className=\"flex items-center space-x-3 text-lg text-slate-600\">\n                  <Timer className=\"w-5 h-5 text-emerald-500\" />\n                  <span>Medical reports in 30 seconds</span>\n                  <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-ping\"></div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-slate-200\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\">\n                      <Mic className=\"w-6 h-6 text-white animate-pulse\" />\n                    </div>\n                    <div>\n                      <p className=\"font-semibold text-slate-800\">Try saying:</p>\n                      <p className=\"text-slate-600 italic\">&ldquo;Patient has fever, cough, prescribed antibiotics...&rdquo;</p>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-slate-400\" />\n                </div>\n              </div>\n\n              <div>\n                <Link\n                  href=\"/signup\"\n                  className=\"group relative bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\"\n                >\n                  <Zap className=\"w-5 h-5\" />\n                  <span>Start Creating Magic</span>\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-6 pt-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-emerald-600\">30s</div>\n                  <div className=\"text-sm text-slate-600\">Average Time</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">90%</div>\n                  <div className=\"text-sm text-slate-600\">Accuracy</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column - Video */}\n            <div className=\"relative -mt-35\">\n              <div className=\"relative\">\n                <div className=\"absolute -inset-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-30 animate-pulse\"></div>\n                <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden\">\n                  <div style={{position: 'relative', paddingBottom: '52.708333333333336%', height: 0}}>\n                    <iframe\n                      src=\"https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7\"\n                      className=\"absolute top-0 left-0 w-full h-full border-0\"\n                      allowFullScreen\n                      title=\"Celer AI Demo\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"absolute -top-6 -left-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <Headphones className=\"w-4 h-4 text-indigo-600\" />\n                  <span className=\"text-sm font-medium text-slate-700\">Voice Input</span>\n                </div>\n              </div>\n\n              <div className=\"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <FileCheck className=\"w-4 h-4 text-emerald-600\" />\n                  <span className=\"text-sm font-medium text-slate-700\">Perfect Report</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ChevronDown className=\"w-6 h-6 text-slate-400\" />\n        </div>\n      </main>\n\n      {/* Time Section - Side Layout Like Hero */}\n      <section className=\"py-32 relative overflow-hidden\">\n        {/* Same background style as hero but darker */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-slate-900\"></div>\n        \n        {/* Floating elements like hero */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-200/20 to-cyan-200/20 rounded-full blur-xl animate-pulse\"></div>\n          <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-cyan-200/10 to-blue-200/10 rounded-full blur-xl animate-pulse delay-2000\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6\">\n          <div className=\"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]\">\n            \n            {/* Left Column - Problem & Solution */}\n            <div className=\"space-y-8 text-white\">\n              {/* Time Badge - Same style as hero */}\n              <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-50/10 to-cyan-50/10 border border-emerald-200/20 rounded-full px-4 py-2\">\n                <Clock className=\"w-4 h-4 text-emerald-400 animate-spin\" />\n                <span className=\"text-emerald-300 text-sm font-medium\">Reclaim Your Time</span>\n                <Timer className=\"w-4 h-4 text-cyan-400\" />\n              </div>\n\n              {/* Headline - Same style as hero */}\n              <div className=\"space-y-4\">\n                <h2 className=\"text-6xl md:text-7xl font-black text-white leading-none\">\n                  Get Your\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 via-cyan-400 to-purple-400 animate-pulse\">\n                    Life Back\n                  </span>\n                </h2>\n                \n                <div className=\"flex items-center space-x-3 text-lg text-slate-300\">\n                  <Timer className=\"w-5 h-5 text-emerald-400\" />\n                  <span>Save 2+ hours daily with voice documentation</span>\n                  <div className=\"w-2 h-2 bg-emerald-400 rounded-full animate-ping\"></div>\n                </div>\n              </div>\n\n              {/* Time Comparison - Same style as hero demo box */}\n              <div className=\"bg-gradient-to-r from-slate-50/5 to-emerald-50/5 rounded-2xl p-6 border border-white/10\">\n                <div className=\"grid grid-cols-2 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-red-400 text-4xl font-black mb-2\">15-20</div>\n                    <div className=\"text-slate-300 text-sm\">minutes per patient</div>\n                    <div className=\"text-red-400 text-xs mt-2\">😰 Stress & paperwork</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-emerald-400 text-4xl font-black mb-2\">30</div>\n                    <div className=\"text-slate-300 text-sm\">seconds total</div>\n                    <div className=\"text-emerald-400 text-xs mt-2\">😌 Perfect & effortless</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* CTA - Same style as hero */}\n              <div>\n                <Link\n                  href=\"/signup\"\n                  className=\"group relative bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\"\n                >\n                  <Timer className=\"w-5 h-5\" />\n                  <span>Start Saving Time Now</span>\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </div>\n\n              {/* Trust indicators - Same style as hero */}\n              <div className=\"grid grid-cols-2 gap-6 pt-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-emerald-400\">2+ hrs</div>\n                  <div className=\"text-sm text-slate-300\">Daily Savings</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-cyan-400\">90%</div>\n                  <div className=\"text-sm text-slate-300\">Accuracy</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column - Life Activities */}\n            <div className=\"relative\">\n              {/* Container with same style as video frame */}\n              <div className=\"relative\">\n                <div className=\"absolute -inset-4 bg-gradient-to-r from-emerald-500 via-cyan-500 to-purple-500 rounded-3xl blur-lg opacity-30 animate-pulse\"></div>\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-white/20\">\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-white mb-8 text-center\">\n                      What will you do with 2 extra hours daily?\n                    </h3>\n                    \n                    <div className=\"grid grid-cols-2 gap-6\">\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">👨‍👩‍👧‍👦</div>\n                        <div className=\"text-white font-medium\">Family Time</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Quality moments</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">🏃‍♂️</div>\n                        <div className=\"text-white font-medium\">Exercise</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Stay healthy</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">📚</div>\n                        <div className=\"text-white font-medium\">Learning</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Grow skills</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">😴</div>\n                        <div className=\"text-white font-medium\">Rest</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Recharge</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Floating elements like hero */}\n              <div className=\"absolute -top-6 -left-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20\">\n                <div className=\"flex items-center space-x-2\">\n                  <Clock className=\"w-4 h-4 text-emerald-400\" />\n                  <span className=\"text-sm font-medium text-white\">2+ Hours Saved</span>\n                </div>\n              </div>\n\n              <div className=\"absolute -bottom-6 -right-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20\">\n                <div className=\"flex items-center space-x-2\">\n                  <Timer className=\"w-4 h-4 text-cyan-400\" />\n                  <span className=\"text-sm font-medium text-white\">Every Day</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Minimal Footer */}\n      <footer className=\"bg-slate-50 border-t border-slate-200\">\n        <div className=\"max-w-7xl mx-auto px-6 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-3 mb-4 md:mb-0\">\n              <div className=\"relative w-8 h-8\">\n                <Image\n                  src=\"/celer-ai-logo.svg\"\n                  alt=\"Celer AI\"\n                  width={32}\n                  height={32}\n                  className=\"rounded-lg\"\n                />\n              </div>\n              <span className=\"font-semibold text-slate-800\">Celer AI</span>\n              <span className=\"text-slate-500 text-sm\">• Built for Indian doctors</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-6 text-sm text-slate-500\">\n              <Link href=\"/privacy\" className=\"hover:text-slate-700 transition-colors\">Privacy</Link>\n              <Link href=\"/terms\" className=\"hover:text-slate-700 transition-colors\">Terms</Link>\n              <a href=\"mailto:<EMAIL>\" className=\"hover:text-slate-700 transition-colors\">Support</a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;;AAEe,eAAe;IAC5B,+DAA+D;IAC/D,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACjC,IAAI,SAAS;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAK,WAAU;8CAA8H;;;;;;;;;;;;sCAIhJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,8OAAC,+MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAA8D;sEAE1E,8OAAC;4DAAK,WAAU;sEAAgH;;;;;;sEAGhI,8OAAC;4DAAK,WAAU;sEAAuB;;;;;;;;;;;;8DAKzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA+B;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,OAAO;4DAAC,UAAU;4DAAY,eAAe;4DAAuB,QAAQ;wDAAC;kEAChF,cAAA,8OAAC;4DACC,KAAI;4DACJ,WAAU;4DACV,eAAe;4DACf,OAAM;;;;;;;;;;;;;;;;;;;;;;sDAMd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;sDAIzD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;8DACvD,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAA0D;sEAEtE,8OAAC;4DAAK,WAAU;sEAAiH;;;;;;;;;;;;8DAKnI,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAwC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAA4B;;;;;;;;;;;;kEAE7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA4C;;;;;;0EAC3D,8OAAC;gEAAI,WAAU;0EAAyB;;;;;;0EACxC,8OAAC;gEAAI,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;sDAMrD,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAK1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiD;;;;;;0EAI/D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAgB;;;;;;0FAC/B,8OAAC;gFAAI,WAAU;0FAAyB;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAA8B;;;;;;;;;;;;kFAE/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAgB;;;;;;0FAC/B,8OAAC;gFAAI,WAAU;0FAAyB;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAA8B;;;;;;;;;;;;kFAE/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAgB;;;;;;0FAC/B,8OAAC;gFAAI,WAAU;0FAAyB;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAA8B;;;;;;;;;;;;kFAE/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAgB;;;;;;0FAC/B,8OAAC;gFAAI,WAAU;0FAAyB;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;sDAIrD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;kDAC/C,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAG3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAyC;;;;;;kDACzE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAyC;;;;;;kDACvE,8OAAC;wCAAE,MAAK;wCAA6B,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtG", "debugId": null}}]}