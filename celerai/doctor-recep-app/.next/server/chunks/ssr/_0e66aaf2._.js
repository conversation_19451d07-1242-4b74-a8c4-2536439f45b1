module.exports={676957:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],c=(0,d.default)("users",b)}},485878:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Users:()=>d.default});var d=a.i(676957)},942921:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]],c=(0,d.default)("gift",b)}},605754:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Gift:()=>d.default});var d=a.i(942921)},436269:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],c=(0,d.default)("check",b)}},24353:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Check:()=>d.default});var d=a.i(436269)},217649:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({FormSkeleton:()=>g,LoadingWrapper:()=>e,SkeletonBox:()=>f});var d=a.i(674420);function e({isLoading:a,children:b,loadingText:c="Loading...",className:e=""}){return a?(0,d.jsx)("div",{className:`animate-pulse ${e}`,children:(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-4 h-4 bg-orange-400 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-4 h-4 bg-orange-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-4 h-4 bg-orange-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),(0,d.jsx)("span",{className:"ml-2 text-slate-600",children:c})]})})}):(0,d.jsx)(d.Fragment,{children:b})}function f({className:a="",height:b="h-4"}){return(0,d.jsx)("div",{className:`${b} bg-slate-200 rounded animate-pulse ${a}`})}function g(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(f,{height:"h-6",className:"w-1/3"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)(f,{height:"h-10"}),(0,d.jsx)(f,{height:"h-10"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(f,{height:"h-6",className:"w-1/4"}),(0,d.jsx)(f,{height:"h-10",className:"max-w-md"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(f,{height:"h-6",className:"w-1/4"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:Array.from({length:6}).map((a,b)=>(0,d.jsx)(f,{height:"h-8"},b))})]}),(0,d.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,d.jsx)(f,{height:"h-10",className:"w-32"}),(0,d.jsx)(f,{height:"h-10",className:"w-32"})]})]})}},947593:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AdminDashboardSkeleton:()=>h,ConsultationsListSkeleton:()=>i,DashboardSkeleton:()=>f,DashboardStatsSkeleton:()=>j,InfoPageSkeleton:()=>g,QuotaCardSkeleton:()=>k,ReferralStatsSkeleton:()=>l});var d=a.i(674420),e=a.i(217649);function f(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-48"}),(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-32"})]})}),(0,d.jsxs)("div",{className:"flex h-[calc(100vh-80px)]",children:[(0,d.jsxs)("div",{className:"w-80 border-r border-gray-200 p-6 space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"}),(0,d.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((a,b)=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-3/4"})]},b))})]}),(0,d.jsxs)("div",{className:"flex-1 p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"text-center space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-12",className:"w-12 rounded-full mx-auto"}),(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-48 mx-auto"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-64 mx-auto"})]}),(0,d.jsxs)("div",{className:"max-w-md mx-auto space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-32",className:"w-full"})]})]})]})]})}function g(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-32"})}),(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{className:"p-4 bg-white rounded-lg border space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-16"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"})]},b))})}),(0,d.jsxs)("div",{className:"p-6 bg-white rounded-lg border space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-2",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"})]}),(0,d.jsxs)("div",{className:"p-6 bg-white rounded-lg border space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-3/4"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg border p-6 space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-48"}),(0,d.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-48"})]}),(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-20"})]},b))})]})]})]})}function h(){return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("div",{className:"bg-white border-b p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-48"}),(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-32"})]})}),(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"p-6 bg-white rounded-lg border space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-16"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"})]},b))}),(0,d.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,d.jsx)("div",{className:"p-6 border-b",children:(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"})}),(0,d.jsx)("div",{className:"p-6 space-y-4",children:Array.from({length:10}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border-b",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-10 rounded-full"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-48"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-20"}),(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-20"})]})]},b))})]})]})]})}function i(){return(0,d.jsx)("div",{className:"space-y-3",children:Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"}),(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-16"})]}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-3/4"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-16"}),(0,d.jsx)(e.SkeletonBox,{height:"h-3",className:"w-20"})]})]},b))})}function j(){return(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{className:"p-4 bg-white rounded-lg border space-y-2",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-8",className:"w-16"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"})]},b))})}function k(){return(0,d.jsxs)("div",{className:"p-6 bg-white rounded-lg border space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-full"}),(0,d.jsx)(e.SkeletonBox,{height:"h-2",className:"w-full"}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-16"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-20"})]})]})}function l(){return(0,d.jsxs)("div",{className:"p-6 bg-white rounded-lg border space-y-4",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-6",className:"w-32"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-24"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-8"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-32"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-12"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-28"}),(0,d.jsx)(e.SkeletonBox,{height:"h-4",className:"w-16"})]})]}),(0,d.jsx)(e.SkeletonBox,{height:"h-10",className:"w-full"})]})}}};

//# sourceMappingURL=_0e66aaf2._.js.map