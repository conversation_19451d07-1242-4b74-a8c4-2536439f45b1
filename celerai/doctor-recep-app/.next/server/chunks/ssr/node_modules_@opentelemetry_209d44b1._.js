module.exports = {

"[project]/node_modules/@opentelemetry/instrumentation-undici/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Creates a const map from the given values
 * @param values - An array of values to be used as keys and values in the map.
 * @returns A populated version of the map with the values and keys derived from the values.
 */ /*#__NO_SIDE_EFFECTS__*/ __turbopack_context__.s({
    "createConstMap": (()=>createConstMap)
});
function createConstMap(values) {
    // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any
    var res = {};
    var len = values.length;
    for(var lp = 0; lp < len; lp++){
        var val = values[lp];
        if (val) {
            res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;
        }
    }
    return res;
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation-undici/node_modules/@opentelemetry/semantic-conventions/build/esm/resource/SemanticResourceAttributes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "AWSECSLAUNCHTYPEVALUES_EC2": (()=>AWSECSLAUNCHTYPEVALUES_EC2),
    "AWSECSLAUNCHTYPEVALUES_FARGATE": (()=>AWSECSLAUNCHTYPEVALUES_FARGATE),
    "AwsEcsLaunchtypeValues": (()=>AwsEcsLaunchtypeValues),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC),
    "CLOUDPLATFORMVALUES_AWS_EC2": (()=>CLOUDPLATFORMVALUES_AWS_EC2),
    "CLOUDPLATFORMVALUES_AWS_ECS": (()=>CLOUDPLATFORMVALUES_AWS_ECS),
    "CLOUDPLATFORMVALUES_AWS_EKS": (()=>CLOUDPLATFORMVALUES_AWS_EKS),
    "CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK": (()=>CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK),
    "CLOUDPLATFORMVALUES_AWS_LAMBDA": (()=>CLOUDPLATFORMVALUES_AWS_LAMBDA),
    "CLOUDPLATFORMVALUES_AZURE_AKS": (()=>CLOUDPLATFORMVALUES_AZURE_AKS),
    "CLOUDPLATFORMVALUES_AZURE_APP_SERVICE": (()=>CLOUDPLATFORMVALUES_AZURE_APP_SERVICE),
    "CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES": (()=>CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES),
    "CLOUDPLATFORMVALUES_AZURE_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_AZURE_FUNCTIONS),
    "CLOUDPLATFORMVALUES_AZURE_VM": (()=>CLOUDPLATFORMVALUES_AZURE_VM),
    "CLOUDPLATFORMVALUES_GCP_APP_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_APP_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_RUN": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_RUN),
    "CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE),
    "CLOUDPROVIDERVALUES_ALIBABA_CLOUD": (()=>CLOUDPROVIDERVALUES_ALIBABA_CLOUD),
    "CLOUDPROVIDERVALUES_AWS": (()=>CLOUDPROVIDERVALUES_AWS),
    "CLOUDPROVIDERVALUES_AZURE": (()=>CLOUDPROVIDERVALUES_AZURE),
    "CLOUDPROVIDERVALUES_GCP": (()=>CLOUDPROVIDERVALUES_GCP),
    "CloudPlatformValues": (()=>CloudPlatformValues),
    "CloudProviderValues": (()=>CloudProviderValues),
    "HOSTARCHVALUES_AMD64": (()=>HOSTARCHVALUES_AMD64),
    "HOSTARCHVALUES_ARM32": (()=>HOSTARCHVALUES_ARM32),
    "HOSTARCHVALUES_ARM64": (()=>HOSTARCHVALUES_ARM64),
    "HOSTARCHVALUES_IA64": (()=>HOSTARCHVALUES_IA64),
    "HOSTARCHVALUES_PPC32": (()=>HOSTARCHVALUES_PPC32),
    "HOSTARCHVALUES_PPC64": (()=>HOSTARCHVALUES_PPC64),
    "HOSTARCHVALUES_X86": (()=>HOSTARCHVALUES_X86),
    "HostArchValues": (()=>HostArchValues),
    "OSTYPEVALUES_AIX": (()=>OSTYPEVALUES_AIX),
    "OSTYPEVALUES_DARWIN": (()=>OSTYPEVALUES_DARWIN),
    "OSTYPEVALUES_DRAGONFLYBSD": (()=>OSTYPEVALUES_DRAGONFLYBSD),
    "OSTYPEVALUES_FREEBSD": (()=>OSTYPEVALUES_FREEBSD),
    "OSTYPEVALUES_HPUX": (()=>OSTYPEVALUES_HPUX),
    "OSTYPEVALUES_LINUX": (()=>OSTYPEVALUES_LINUX),
    "OSTYPEVALUES_NETBSD": (()=>OSTYPEVALUES_NETBSD),
    "OSTYPEVALUES_OPENBSD": (()=>OSTYPEVALUES_OPENBSD),
    "OSTYPEVALUES_SOLARIS": (()=>OSTYPEVALUES_SOLARIS),
    "OSTYPEVALUES_WINDOWS": (()=>OSTYPEVALUES_WINDOWS),
    "OSTYPEVALUES_Z_OS": (()=>OSTYPEVALUES_Z_OS),
    "OsTypeValues": (()=>OsTypeValues),
    "SEMRESATTRS_AWS_ECS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_ECS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_ECS_CONTAINER_ARN": (()=>SEMRESATTRS_AWS_ECS_CONTAINER_ARN),
    "SEMRESATTRS_AWS_ECS_LAUNCHTYPE": (()=>SEMRESATTRS_AWS_ECS_LAUNCHTYPE),
    "SEMRESATTRS_AWS_ECS_TASK_ARN": (()=>SEMRESATTRS_AWS_ECS_TASK_ARN),
    "SEMRESATTRS_AWS_ECS_TASK_FAMILY": (()=>SEMRESATTRS_AWS_ECS_TASK_FAMILY),
    "SEMRESATTRS_AWS_ECS_TASK_REVISION": (()=>SEMRESATTRS_AWS_ECS_TASK_REVISION),
    "SEMRESATTRS_AWS_EKS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_EKS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_LOG_GROUP_ARNS": (()=>SEMRESATTRS_AWS_LOG_GROUP_ARNS),
    "SEMRESATTRS_AWS_LOG_GROUP_NAMES": (()=>SEMRESATTRS_AWS_LOG_GROUP_NAMES),
    "SEMRESATTRS_AWS_LOG_STREAM_ARNS": (()=>SEMRESATTRS_AWS_LOG_STREAM_ARNS),
    "SEMRESATTRS_AWS_LOG_STREAM_NAMES": (()=>SEMRESATTRS_AWS_LOG_STREAM_NAMES),
    "SEMRESATTRS_CLOUD_ACCOUNT_ID": (()=>SEMRESATTRS_CLOUD_ACCOUNT_ID),
    "SEMRESATTRS_CLOUD_AVAILABILITY_ZONE": (()=>SEMRESATTRS_CLOUD_AVAILABILITY_ZONE),
    "SEMRESATTRS_CLOUD_PLATFORM": (()=>SEMRESATTRS_CLOUD_PLATFORM),
    "SEMRESATTRS_CLOUD_PROVIDER": (()=>SEMRESATTRS_CLOUD_PROVIDER),
    "SEMRESATTRS_CLOUD_REGION": (()=>SEMRESATTRS_CLOUD_REGION),
    "SEMRESATTRS_CONTAINER_ID": (()=>SEMRESATTRS_CONTAINER_ID),
    "SEMRESATTRS_CONTAINER_IMAGE_NAME": (()=>SEMRESATTRS_CONTAINER_IMAGE_NAME),
    "SEMRESATTRS_CONTAINER_IMAGE_TAG": (()=>SEMRESATTRS_CONTAINER_IMAGE_TAG),
    "SEMRESATTRS_CONTAINER_NAME": (()=>SEMRESATTRS_CONTAINER_NAME),
    "SEMRESATTRS_CONTAINER_RUNTIME": (()=>SEMRESATTRS_CONTAINER_RUNTIME),
    "SEMRESATTRS_DEPLOYMENT_ENVIRONMENT": (()=>SEMRESATTRS_DEPLOYMENT_ENVIRONMENT),
    "SEMRESATTRS_DEVICE_ID": (()=>SEMRESATTRS_DEVICE_ID),
    "SEMRESATTRS_DEVICE_MODEL_IDENTIFIER": (()=>SEMRESATTRS_DEVICE_MODEL_IDENTIFIER),
    "SEMRESATTRS_DEVICE_MODEL_NAME": (()=>SEMRESATTRS_DEVICE_MODEL_NAME),
    "SEMRESATTRS_FAAS_ID": (()=>SEMRESATTRS_FAAS_ID),
    "SEMRESATTRS_FAAS_INSTANCE": (()=>SEMRESATTRS_FAAS_INSTANCE),
    "SEMRESATTRS_FAAS_MAX_MEMORY": (()=>SEMRESATTRS_FAAS_MAX_MEMORY),
    "SEMRESATTRS_FAAS_NAME": (()=>SEMRESATTRS_FAAS_NAME),
    "SEMRESATTRS_FAAS_VERSION": (()=>SEMRESATTRS_FAAS_VERSION),
    "SEMRESATTRS_HOST_ARCH": (()=>SEMRESATTRS_HOST_ARCH),
    "SEMRESATTRS_HOST_ID": (()=>SEMRESATTRS_HOST_ID),
    "SEMRESATTRS_HOST_IMAGE_ID": (()=>SEMRESATTRS_HOST_IMAGE_ID),
    "SEMRESATTRS_HOST_IMAGE_NAME": (()=>SEMRESATTRS_HOST_IMAGE_NAME),
    "SEMRESATTRS_HOST_IMAGE_VERSION": (()=>SEMRESATTRS_HOST_IMAGE_VERSION),
    "SEMRESATTRS_HOST_NAME": (()=>SEMRESATTRS_HOST_NAME),
    "SEMRESATTRS_HOST_TYPE": (()=>SEMRESATTRS_HOST_TYPE),
    "SEMRESATTRS_K8S_CLUSTER_NAME": (()=>SEMRESATTRS_K8S_CLUSTER_NAME),
    "SEMRESATTRS_K8S_CONTAINER_NAME": (()=>SEMRESATTRS_K8S_CONTAINER_NAME),
    "SEMRESATTRS_K8S_CRONJOB_NAME": (()=>SEMRESATTRS_K8S_CRONJOB_NAME),
    "SEMRESATTRS_K8S_CRONJOB_UID": (()=>SEMRESATTRS_K8S_CRONJOB_UID),
    "SEMRESATTRS_K8S_DAEMONSET_NAME": (()=>SEMRESATTRS_K8S_DAEMONSET_NAME),
    "SEMRESATTRS_K8S_DAEMONSET_UID": (()=>SEMRESATTRS_K8S_DAEMONSET_UID),
    "SEMRESATTRS_K8S_DEPLOYMENT_NAME": (()=>SEMRESATTRS_K8S_DEPLOYMENT_NAME),
    "SEMRESATTRS_K8S_DEPLOYMENT_UID": (()=>SEMRESATTRS_K8S_DEPLOYMENT_UID),
    "SEMRESATTRS_K8S_JOB_NAME": (()=>SEMRESATTRS_K8S_JOB_NAME),
    "SEMRESATTRS_K8S_JOB_UID": (()=>SEMRESATTRS_K8S_JOB_UID),
    "SEMRESATTRS_K8S_NAMESPACE_NAME": (()=>SEMRESATTRS_K8S_NAMESPACE_NAME),
    "SEMRESATTRS_K8S_NODE_NAME": (()=>SEMRESATTRS_K8S_NODE_NAME),
    "SEMRESATTRS_K8S_NODE_UID": (()=>SEMRESATTRS_K8S_NODE_UID),
    "SEMRESATTRS_K8S_POD_NAME": (()=>SEMRESATTRS_K8S_POD_NAME),
    "SEMRESATTRS_K8S_POD_UID": (()=>SEMRESATTRS_K8S_POD_UID),
    "SEMRESATTRS_K8S_REPLICASET_NAME": (()=>SEMRESATTRS_K8S_REPLICASET_NAME),
    "SEMRESATTRS_K8S_REPLICASET_UID": (()=>SEMRESATTRS_K8S_REPLICASET_UID),
    "SEMRESATTRS_K8S_STATEFULSET_NAME": (()=>SEMRESATTRS_K8S_STATEFULSET_NAME),
    "SEMRESATTRS_K8S_STATEFULSET_UID": (()=>SEMRESATTRS_K8S_STATEFULSET_UID),
    "SEMRESATTRS_OS_DESCRIPTION": (()=>SEMRESATTRS_OS_DESCRIPTION),
    "SEMRESATTRS_OS_NAME": (()=>SEMRESATTRS_OS_NAME),
    "SEMRESATTRS_OS_TYPE": (()=>SEMRESATTRS_OS_TYPE),
    "SEMRESATTRS_OS_VERSION": (()=>SEMRESATTRS_OS_VERSION),
    "SEMRESATTRS_PROCESS_COMMAND": (()=>SEMRESATTRS_PROCESS_COMMAND),
    "SEMRESATTRS_PROCESS_COMMAND_ARGS": (()=>SEMRESATTRS_PROCESS_COMMAND_ARGS),
    "SEMRESATTRS_PROCESS_COMMAND_LINE": (()=>SEMRESATTRS_PROCESS_COMMAND_LINE),
    "SEMRESATTRS_PROCESS_EXECUTABLE_NAME": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_NAME),
    "SEMRESATTRS_PROCESS_EXECUTABLE_PATH": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_PATH),
    "SEMRESATTRS_PROCESS_OWNER": (()=>SEMRESATTRS_PROCESS_OWNER),
    "SEMRESATTRS_PROCESS_PID": (()=>SEMRESATTRS_PROCESS_PID),
    "SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION": (()=>SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION),
    "SEMRESATTRS_PROCESS_RUNTIME_NAME": (()=>SEMRESATTRS_PROCESS_RUNTIME_NAME),
    "SEMRESATTRS_PROCESS_RUNTIME_VERSION": (()=>SEMRESATTRS_PROCESS_RUNTIME_VERSION),
    "SEMRESATTRS_SERVICE_INSTANCE_ID": (()=>SEMRESATTRS_SERVICE_INSTANCE_ID),
    "SEMRESATTRS_SERVICE_NAME": (()=>SEMRESATTRS_SERVICE_NAME),
    "SEMRESATTRS_SERVICE_NAMESPACE": (()=>SEMRESATTRS_SERVICE_NAMESPACE),
    "SEMRESATTRS_SERVICE_VERSION": (()=>SEMRESATTRS_SERVICE_VERSION),
    "SEMRESATTRS_TELEMETRY_AUTO_VERSION": (()=>SEMRESATTRS_TELEMETRY_AUTO_VERSION),
    "SEMRESATTRS_TELEMETRY_SDK_LANGUAGE": (()=>SEMRESATTRS_TELEMETRY_SDK_LANGUAGE),
    "SEMRESATTRS_TELEMETRY_SDK_NAME": (()=>SEMRESATTRS_TELEMETRY_SDK_NAME),
    "SEMRESATTRS_TELEMETRY_SDK_VERSION": (()=>SEMRESATTRS_TELEMETRY_SDK_VERSION),
    "SEMRESATTRS_WEBENGINE_DESCRIPTION": (()=>SEMRESATTRS_WEBENGINE_DESCRIPTION),
    "SEMRESATTRS_WEBENGINE_NAME": (()=>SEMRESATTRS_WEBENGINE_NAME),
    "SEMRESATTRS_WEBENGINE_VERSION": (()=>SEMRESATTRS_WEBENGINE_VERSION),
    "SemanticResourceAttributes": (()=>SemanticResourceAttributes),
    "TELEMETRYSDKLANGUAGEVALUES_CPP": (()=>TELEMETRYSDKLANGUAGEVALUES_CPP),
    "TELEMETRYSDKLANGUAGEVALUES_DOTNET": (()=>TELEMETRYSDKLANGUAGEVALUES_DOTNET),
    "TELEMETRYSDKLANGUAGEVALUES_ERLANG": (()=>TELEMETRYSDKLANGUAGEVALUES_ERLANG),
    "TELEMETRYSDKLANGUAGEVALUES_GO": (()=>TELEMETRYSDKLANGUAGEVALUES_GO),
    "TELEMETRYSDKLANGUAGEVALUES_JAVA": (()=>TELEMETRYSDKLANGUAGEVALUES_JAVA),
    "TELEMETRYSDKLANGUAGEVALUES_NODEJS": (()=>TELEMETRYSDKLANGUAGEVALUES_NODEJS),
    "TELEMETRYSDKLANGUAGEVALUES_PHP": (()=>TELEMETRYSDKLANGUAGEVALUES_PHP),
    "TELEMETRYSDKLANGUAGEVALUES_PYTHON": (()=>TELEMETRYSDKLANGUAGEVALUES_PYTHON),
    "TELEMETRYSDKLANGUAGEVALUES_RUBY": (()=>TELEMETRYSDKLANGUAGEVALUES_RUBY),
    "TELEMETRYSDKLANGUAGEVALUES_WEBJS": (()=>TELEMETRYSDKLANGUAGEVALUES_WEBJS),
    "TelemetrySdkLanguageValues": (()=>TelemetrySdkLanguageValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation-undici/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)");
;
//----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2
//----------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------
// Constant values for SemanticResourceAttributes
//----------------------------------------------------------------------------------------------------------
// Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUD_PROVIDER = 'cloud.provider';
var TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';
var TMP_CLOUD_REGION = 'cloud.region';
var TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';
var TMP_CLOUD_PLATFORM = 'cloud.platform';
var TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';
var TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';
var TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';
var TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';
var TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';
var TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';
var TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';
var TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';
var TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';
var TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';
var TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';
var TMP_CONTAINER_NAME = 'container.name';
var TMP_CONTAINER_ID = 'container.id';
var TMP_CONTAINER_RUNTIME = 'container.runtime';
var TMP_CONTAINER_IMAGE_NAME = 'container.image.name';
var TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';
var TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';
var TMP_DEVICE_ID = 'device.id';
var TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';
var TMP_DEVICE_MODEL_NAME = 'device.model.name';
var TMP_FAAS_NAME = 'faas.name';
var TMP_FAAS_ID = 'faas.id';
var TMP_FAAS_VERSION = 'faas.version';
var TMP_FAAS_INSTANCE = 'faas.instance';
var TMP_FAAS_MAX_MEMORY = 'faas.max_memory';
var TMP_HOST_ID = 'host.id';
var TMP_HOST_NAME = 'host.name';
var TMP_HOST_TYPE = 'host.type';
var TMP_HOST_ARCH = 'host.arch';
var TMP_HOST_IMAGE_NAME = 'host.image.name';
var TMP_HOST_IMAGE_ID = 'host.image.id';
var TMP_HOST_IMAGE_VERSION = 'host.image.version';
var TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';
var TMP_K8S_NODE_NAME = 'k8s.node.name';
var TMP_K8S_NODE_UID = 'k8s.node.uid';
var TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';
var TMP_K8S_POD_UID = 'k8s.pod.uid';
var TMP_K8S_POD_NAME = 'k8s.pod.name';
var TMP_K8S_CONTAINER_NAME = 'k8s.container.name';
var TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';
var TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';
var TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';
var TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';
var TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';
var TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';
var TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';
var TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';
var TMP_K8S_JOB_UID = 'k8s.job.uid';
var TMP_K8S_JOB_NAME = 'k8s.job.name';
var TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';
var TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';
var TMP_OS_TYPE = 'os.type';
var TMP_OS_DESCRIPTION = 'os.description';
var TMP_OS_NAME = 'os.name';
var TMP_OS_VERSION = 'os.version';
var TMP_PROCESS_PID = 'process.pid';
var TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';
var TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';
var TMP_PROCESS_COMMAND = 'process.command';
var TMP_PROCESS_COMMAND_LINE = 'process.command_line';
var TMP_PROCESS_COMMAND_ARGS = 'process.command_args';
var TMP_PROCESS_OWNER = 'process.owner';
var TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';
var TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';
var TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';
var TMP_SERVICE_NAME = 'service.name';
var TMP_SERVICE_NAMESPACE = 'service.namespace';
var TMP_SERVICE_INSTANCE_ID = 'service.instance.id';
var TMP_SERVICE_VERSION = 'service.version';
var TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';
var TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';
var TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';
var TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';
var TMP_WEBENGINE_NAME = 'webengine.name';
var TMP_WEBENGINE_VERSION = 'webengine.version';
var TMP_WEBENGINE_DESCRIPTION = 'webengine.description';
var SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;
var SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;
var SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;
var SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;
var SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;
var SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;
var SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;
var SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;
var SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;
var SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;
var SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;
var SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;
var SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;
var SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;
var SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;
var SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;
var SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;
var SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;
var SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;
var SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;
var SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;
var SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;
var SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;
var SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;
var SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;
var SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;
var SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;
var SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;
var SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;
var SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;
var SEMRESATTRS_HOST_ID = TMP_HOST_ID;
var SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;
var SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;
var SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;
var SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;
var SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;
var SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;
var SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;
var SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;
var SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;
var SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;
var SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;
var SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;
var SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;
var SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;
var SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;
var SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;
var SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;
var SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;
var SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;
var SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;
var SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;
var SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;
var SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;
var SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;
var SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;
var SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;
var SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;
var SEMRESATTRS_OS_NAME = TMP_OS_NAME;
var SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;
var SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;
var SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;
var SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;
var SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;
var SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;
var SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;
var SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;
var SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;
var SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;
var SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION = TMP_PROCESS_RUNTIME_DESCRIPTION;
var SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;
var SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;
var SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;
var SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;
var SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;
var SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;
var SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;
var SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;
var SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;
var SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;
var SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;
var SemanticResourceAttributes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUD_PROVIDER,
    TMP_CLOUD_ACCOUNT_ID,
    TMP_CLOUD_REGION,
    TMP_CLOUD_AVAILABILITY_ZONE,
    TMP_CLOUD_PLATFORM,
    TMP_AWS_ECS_CONTAINER_ARN,
    TMP_AWS_ECS_CLUSTER_ARN,
    TMP_AWS_ECS_LAUNCHTYPE,
    TMP_AWS_ECS_TASK_ARN,
    TMP_AWS_ECS_TASK_FAMILY,
    TMP_AWS_ECS_TASK_REVISION,
    TMP_AWS_EKS_CLUSTER_ARN,
    TMP_AWS_LOG_GROUP_NAMES,
    TMP_AWS_LOG_GROUP_ARNS,
    TMP_AWS_LOG_STREAM_NAMES,
    TMP_AWS_LOG_STREAM_ARNS,
    TMP_CONTAINER_NAME,
    TMP_CONTAINER_ID,
    TMP_CONTAINER_RUNTIME,
    TMP_CONTAINER_IMAGE_NAME,
    TMP_CONTAINER_IMAGE_TAG,
    TMP_DEPLOYMENT_ENVIRONMENT,
    TMP_DEVICE_ID,
    TMP_DEVICE_MODEL_IDENTIFIER,
    TMP_DEVICE_MODEL_NAME,
    TMP_FAAS_NAME,
    TMP_FAAS_ID,
    TMP_FAAS_VERSION,
    TMP_FAAS_INSTANCE,
    TMP_FAAS_MAX_MEMORY,
    TMP_HOST_ID,
    TMP_HOST_NAME,
    TMP_HOST_TYPE,
    TMP_HOST_ARCH,
    TMP_HOST_IMAGE_NAME,
    TMP_HOST_IMAGE_ID,
    TMP_HOST_IMAGE_VERSION,
    TMP_K8S_CLUSTER_NAME,
    TMP_K8S_NODE_NAME,
    TMP_K8S_NODE_UID,
    TMP_K8S_NAMESPACE_NAME,
    TMP_K8S_POD_UID,
    TMP_K8S_POD_NAME,
    TMP_K8S_CONTAINER_NAME,
    TMP_K8S_REPLICASET_UID,
    TMP_K8S_REPLICASET_NAME,
    TMP_K8S_DEPLOYMENT_UID,
    TMP_K8S_DEPLOYMENT_NAME,
    TMP_K8S_STATEFULSET_UID,
    TMP_K8S_STATEFULSET_NAME,
    TMP_K8S_DAEMONSET_UID,
    TMP_K8S_DAEMONSET_NAME,
    TMP_K8S_JOB_UID,
    TMP_K8S_JOB_NAME,
    TMP_K8S_CRONJOB_UID,
    TMP_K8S_CRONJOB_NAME,
    TMP_OS_TYPE,
    TMP_OS_DESCRIPTION,
    TMP_OS_NAME,
    TMP_OS_VERSION,
    TMP_PROCESS_PID,
    TMP_PROCESS_EXECUTABLE_NAME,
    TMP_PROCESS_EXECUTABLE_PATH,
    TMP_PROCESS_COMMAND,
    TMP_PROCESS_COMMAND_LINE,
    TMP_PROCESS_COMMAND_ARGS,
    TMP_PROCESS_OWNER,
    TMP_PROCESS_RUNTIME_NAME,
    TMP_PROCESS_RUNTIME_VERSION,
    TMP_PROCESS_RUNTIME_DESCRIPTION,
    TMP_SERVICE_NAME,
    TMP_SERVICE_NAMESPACE,
    TMP_SERVICE_INSTANCE_ID,
    TMP_SERVICE_VERSION,
    TMP_TELEMETRY_SDK_NAME,
    TMP_TELEMETRY_SDK_LANGUAGE,
    TMP_TELEMETRY_SDK_VERSION,
    TMP_TELEMETRY_AUTO_VERSION,
    TMP_WEBENGINE_NAME,
    TMP_WEBENGINE_VERSION,
    TMP_WEBENGINE_DESCRIPTION
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudProviderValues enum definition
 *
 * Name of the cloud provider.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';
var TMP_CLOUDPROVIDERVALUES_AWS = 'aws';
var TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';
var TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';
var CLOUDPROVIDERVALUES_ALIBABA_CLOUD = TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;
var CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;
var CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;
var CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;
var CloudProviderValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,
    TMP_CLOUDPROVIDERVALUES_AWS,
    TMP_CLOUDPROVIDERVALUES_AZURE,
    TMP_CLOUDPROVIDERVALUES_GCP
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudPlatformValues enum definition
 *
 * The cloud platform in use.
 *
 * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';
var TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';
var TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';
var TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';
var TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';
var TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';
var TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';
var TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = 'azure_container_instances';
var TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';
var TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';
var TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';
var TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';
var TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';
var TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;
var CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;
var CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;
var CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;
var CLOUDPLATFORMVALUES_AWS_LAMBDA = TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;
var CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;
var CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;
var CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;
var CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;
var CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;
var CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;
var CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;
var CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;
var CLOUDPLATFORMVALUES_GCP_APP_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;
var CloudPlatformValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,
    TMP_CLOUDPLATFORMVALUES_AWS_EC2,
    TMP_CLOUDPLATFORMVALUES_AWS_ECS,
    TMP_CLOUDPLATFORMVALUES_AWS_EKS,
    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,
    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,
    TMP_CLOUDPLATFORMVALUES_AZURE_VM,
    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,
    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,
    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,
    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,
    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for AwsEcsLaunchtypeValues enum definition
 *
 * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';
var TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';
var AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;
var AWSECSLAUNCHTYPEVALUES_FARGATE = TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;
var AwsEcsLaunchtypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_AWSECSLAUNCHTYPEVALUES_EC2,
    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for HostArchValues enum definition
 *
 * The CPU architecture the host system is running on.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_HOSTARCHVALUES_AMD64 = 'amd64';
var TMP_HOSTARCHVALUES_ARM32 = 'arm32';
var TMP_HOSTARCHVALUES_ARM64 = 'arm64';
var TMP_HOSTARCHVALUES_IA64 = 'ia64';
var TMP_HOSTARCHVALUES_PPC32 = 'ppc32';
var TMP_HOSTARCHVALUES_PPC64 = 'ppc64';
var TMP_HOSTARCHVALUES_X86 = 'x86';
var HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;
var HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;
var HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;
var HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;
var HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;
var HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;
var HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;
var HostArchValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_HOSTARCHVALUES_AMD64,
    TMP_HOSTARCHVALUES_ARM32,
    TMP_HOSTARCHVALUES_ARM64,
    TMP_HOSTARCHVALUES_IA64,
    TMP_HOSTARCHVALUES_PPC32,
    TMP_HOSTARCHVALUES_PPC64,
    TMP_HOSTARCHVALUES_X86
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for OsTypeValues enum definition
 *
 * The operating system type.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_OSTYPEVALUES_WINDOWS = 'windows';
var TMP_OSTYPEVALUES_LINUX = 'linux';
var TMP_OSTYPEVALUES_DARWIN = 'darwin';
var TMP_OSTYPEVALUES_FREEBSD = 'freebsd';
var TMP_OSTYPEVALUES_NETBSD = 'netbsd';
var TMP_OSTYPEVALUES_OPENBSD = 'openbsd';
var TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';
var TMP_OSTYPEVALUES_HPUX = 'hpux';
var TMP_OSTYPEVALUES_AIX = 'aix';
var TMP_OSTYPEVALUES_SOLARIS = 'solaris';
var TMP_OSTYPEVALUES_Z_OS = 'z_os';
var OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;
var OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;
var OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;
var OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;
var OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;
var OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;
var OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;
var OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;
var OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;
var OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;
var OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;
var OsTypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_OSTYPEVALUES_WINDOWS,
    TMP_OSTYPEVALUES_LINUX,
    TMP_OSTYPEVALUES_DARWIN,
    TMP_OSTYPEVALUES_FREEBSD,
    TMP_OSTYPEVALUES_NETBSD,
    TMP_OSTYPEVALUES_OPENBSD,
    TMP_OSTYPEVALUES_DRAGONFLYBSD,
    TMP_OSTYPEVALUES_HPUX,
    TMP_OSTYPEVALUES_AIX,
    TMP_OSTYPEVALUES_SOLARIS,
    TMP_OSTYPEVALUES_Z_OS
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for TelemetrySdkLanguageValues enum definition
 *
 * The language of the telemetry SDK.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';
var TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';
var TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';
var TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';
var TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';
var TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';
var TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';
var TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';
var TELEMETRYSDKLANGUAGEVALUES_CPP = TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;
var TELEMETRYSDKLANGUAGEVALUES_DOTNET = TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;
var TELEMETRYSDKLANGUAGEVALUES_ERLANG = TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;
var TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;
var TELEMETRYSDKLANGUAGEVALUES_JAVA = TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;
var TELEMETRYSDKLANGUAGEVALUES_NODEJS = TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;
var TELEMETRYSDKLANGUAGEVALUES_PHP = TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;
var TELEMETRYSDKLANGUAGEVALUES_PYTHON = TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;
var TELEMETRYSDKLANGUAGEVALUES_RUBY = TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;
var TELEMETRYSDKLANGUAGEVALUES_WEBJS = TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;
var TelemetrySdkLanguageValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$undici$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,
    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,
    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,
    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,
    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,
    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,
    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS
]); //# sourceMappingURL=SemanticResourceAttributes.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Creates a const map from the given values
 * @param values - An array of values to be used as keys and values in the map.
 * @returns A populated version of the map with the values and keys derived from the values.
 */ /*#__NO_SIDE_EFFECTS__*/ __turbopack_context__.s({
    "createConstMap": (()=>createConstMap)
});
function createConstMap(values) {
    // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any
    var res = {};
    var len = values.length;
    for(var lp = 0; lp < len; lp++){
        var val = values[lp];
        if (val) {
            res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;
        }
    }
    return res;
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/semantic-conventions/build/esm/resource/SemanticResourceAttributes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "AWSECSLAUNCHTYPEVALUES_EC2": (()=>AWSECSLAUNCHTYPEVALUES_EC2),
    "AWSECSLAUNCHTYPEVALUES_FARGATE": (()=>AWSECSLAUNCHTYPEVALUES_FARGATE),
    "AwsEcsLaunchtypeValues": (()=>AwsEcsLaunchtypeValues),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC),
    "CLOUDPLATFORMVALUES_AWS_EC2": (()=>CLOUDPLATFORMVALUES_AWS_EC2),
    "CLOUDPLATFORMVALUES_AWS_ECS": (()=>CLOUDPLATFORMVALUES_AWS_ECS),
    "CLOUDPLATFORMVALUES_AWS_EKS": (()=>CLOUDPLATFORMVALUES_AWS_EKS),
    "CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK": (()=>CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK),
    "CLOUDPLATFORMVALUES_AWS_LAMBDA": (()=>CLOUDPLATFORMVALUES_AWS_LAMBDA),
    "CLOUDPLATFORMVALUES_AZURE_AKS": (()=>CLOUDPLATFORMVALUES_AZURE_AKS),
    "CLOUDPLATFORMVALUES_AZURE_APP_SERVICE": (()=>CLOUDPLATFORMVALUES_AZURE_APP_SERVICE),
    "CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES": (()=>CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES),
    "CLOUDPLATFORMVALUES_AZURE_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_AZURE_FUNCTIONS),
    "CLOUDPLATFORMVALUES_AZURE_VM": (()=>CLOUDPLATFORMVALUES_AZURE_VM),
    "CLOUDPLATFORMVALUES_GCP_APP_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_APP_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_RUN": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_RUN),
    "CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE),
    "CLOUDPROVIDERVALUES_ALIBABA_CLOUD": (()=>CLOUDPROVIDERVALUES_ALIBABA_CLOUD),
    "CLOUDPROVIDERVALUES_AWS": (()=>CLOUDPROVIDERVALUES_AWS),
    "CLOUDPROVIDERVALUES_AZURE": (()=>CLOUDPROVIDERVALUES_AZURE),
    "CLOUDPROVIDERVALUES_GCP": (()=>CLOUDPROVIDERVALUES_GCP),
    "CloudPlatformValues": (()=>CloudPlatformValues),
    "CloudProviderValues": (()=>CloudProviderValues),
    "HOSTARCHVALUES_AMD64": (()=>HOSTARCHVALUES_AMD64),
    "HOSTARCHVALUES_ARM32": (()=>HOSTARCHVALUES_ARM32),
    "HOSTARCHVALUES_ARM64": (()=>HOSTARCHVALUES_ARM64),
    "HOSTARCHVALUES_IA64": (()=>HOSTARCHVALUES_IA64),
    "HOSTARCHVALUES_PPC32": (()=>HOSTARCHVALUES_PPC32),
    "HOSTARCHVALUES_PPC64": (()=>HOSTARCHVALUES_PPC64),
    "HOSTARCHVALUES_X86": (()=>HOSTARCHVALUES_X86),
    "HostArchValues": (()=>HostArchValues),
    "OSTYPEVALUES_AIX": (()=>OSTYPEVALUES_AIX),
    "OSTYPEVALUES_DARWIN": (()=>OSTYPEVALUES_DARWIN),
    "OSTYPEVALUES_DRAGONFLYBSD": (()=>OSTYPEVALUES_DRAGONFLYBSD),
    "OSTYPEVALUES_FREEBSD": (()=>OSTYPEVALUES_FREEBSD),
    "OSTYPEVALUES_HPUX": (()=>OSTYPEVALUES_HPUX),
    "OSTYPEVALUES_LINUX": (()=>OSTYPEVALUES_LINUX),
    "OSTYPEVALUES_NETBSD": (()=>OSTYPEVALUES_NETBSD),
    "OSTYPEVALUES_OPENBSD": (()=>OSTYPEVALUES_OPENBSD),
    "OSTYPEVALUES_SOLARIS": (()=>OSTYPEVALUES_SOLARIS),
    "OSTYPEVALUES_WINDOWS": (()=>OSTYPEVALUES_WINDOWS),
    "OSTYPEVALUES_Z_OS": (()=>OSTYPEVALUES_Z_OS),
    "OsTypeValues": (()=>OsTypeValues),
    "SEMRESATTRS_AWS_ECS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_ECS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_ECS_CONTAINER_ARN": (()=>SEMRESATTRS_AWS_ECS_CONTAINER_ARN),
    "SEMRESATTRS_AWS_ECS_LAUNCHTYPE": (()=>SEMRESATTRS_AWS_ECS_LAUNCHTYPE),
    "SEMRESATTRS_AWS_ECS_TASK_ARN": (()=>SEMRESATTRS_AWS_ECS_TASK_ARN),
    "SEMRESATTRS_AWS_ECS_TASK_FAMILY": (()=>SEMRESATTRS_AWS_ECS_TASK_FAMILY),
    "SEMRESATTRS_AWS_ECS_TASK_REVISION": (()=>SEMRESATTRS_AWS_ECS_TASK_REVISION),
    "SEMRESATTRS_AWS_EKS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_EKS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_LOG_GROUP_ARNS": (()=>SEMRESATTRS_AWS_LOG_GROUP_ARNS),
    "SEMRESATTRS_AWS_LOG_GROUP_NAMES": (()=>SEMRESATTRS_AWS_LOG_GROUP_NAMES),
    "SEMRESATTRS_AWS_LOG_STREAM_ARNS": (()=>SEMRESATTRS_AWS_LOG_STREAM_ARNS),
    "SEMRESATTRS_AWS_LOG_STREAM_NAMES": (()=>SEMRESATTRS_AWS_LOG_STREAM_NAMES),
    "SEMRESATTRS_CLOUD_ACCOUNT_ID": (()=>SEMRESATTRS_CLOUD_ACCOUNT_ID),
    "SEMRESATTRS_CLOUD_AVAILABILITY_ZONE": (()=>SEMRESATTRS_CLOUD_AVAILABILITY_ZONE),
    "SEMRESATTRS_CLOUD_PLATFORM": (()=>SEMRESATTRS_CLOUD_PLATFORM),
    "SEMRESATTRS_CLOUD_PROVIDER": (()=>SEMRESATTRS_CLOUD_PROVIDER),
    "SEMRESATTRS_CLOUD_REGION": (()=>SEMRESATTRS_CLOUD_REGION),
    "SEMRESATTRS_CONTAINER_ID": (()=>SEMRESATTRS_CONTAINER_ID),
    "SEMRESATTRS_CONTAINER_IMAGE_NAME": (()=>SEMRESATTRS_CONTAINER_IMAGE_NAME),
    "SEMRESATTRS_CONTAINER_IMAGE_TAG": (()=>SEMRESATTRS_CONTAINER_IMAGE_TAG),
    "SEMRESATTRS_CONTAINER_NAME": (()=>SEMRESATTRS_CONTAINER_NAME),
    "SEMRESATTRS_CONTAINER_RUNTIME": (()=>SEMRESATTRS_CONTAINER_RUNTIME),
    "SEMRESATTRS_DEPLOYMENT_ENVIRONMENT": (()=>SEMRESATTRS_DEPLOYMENT_ENVIRONMENT),
    "SEMRESATTRS_DEVICE_ID": (()=>SEMRESATTRS_DEVICE_ID),
    "SEMRESATTRS_DEVICE_MODEL_IDENTIFIER": (()=>SEMRESATTRS_DEVICE_MODEL_IDENTIFIER),
    "SEMRESATTRS_DEVICE_MODEL_NAME": (()=>SEMRESATTRS_DEVICE_MODEL_NAME),
    "SEMRESATTRS_FAAS_ID": (()=>SEMRESATTRS_FAAS_ID),
    "SEMRESATTRS_FAAS_INSTANCE": (()=>SEMRESATTRS_FAAS_INSTANCE),
    "SEMRESATTRS_FAAS_MAX_MEMORY": (()=>SEMRESATTRS_FAAS_MAX_MEMORY),
    "SEMRESATTRS_FAAS_NAME": (()=>SEMRESATTRS_FAAS_NAME),
    "SEMRESATTRS_FAAS_VERSION": (()=>SEMRESATTRS_FAAS_VERSION),
    "SEMRESATTRS_HOST_ARCH": (()=>SEMRESATTRS_HOST_ARCH),
    "SEMRESATTRS_HOST_ID": (()=>SEMRESATTRS_HOST_ID),
    "SEMRESATTRS_HOST_IMAGE_ID": (()=>SEMRESATTRS_HOST_IMAGE_ID),
    "SEMRESATTRS_HOST_IMAGE_NAME": (()=>SEMRESATTRS_HOST_IMAGE_NAME),
    "SEMRESATTRS_HOST_IMAGE_VERSION": (()=>SEMRESATTRS_HOST_IMAGE_VERSION),
    "SEMRESATTRS_HOST_NAME": (()=>SEMRESATTRS_HOST_NAME),
    "SEMRESATTRS_HOST_TYPE": (()=>SEMRESATTRS_HOST_TYPE),
    "SEMRESATTRS_K8S_CLUSTER_NAME": (()=>SEMRESATTRS_K8S_CLUSTER_NAME),
    "SEMRESATTRS_K8S_CONTAINER_NAME": (()=>SEMRESATTRS_K8S_CONTAINER_NAME),
    "SEMRESATTRS_K8S_CRONJOB_NAME": (()=>SEMRESATTRS_K8S_CRONJOB_NAME),
    "SEMRESATTRS_K8S_CRONJOB_UID": (()=>SEMRESATTRS_K8S_CRONJOB_UID),
    "SEMRESATTRS_K8S_DAEMONSET_NAME": (()=>SEMRESATTRS_K8S_DAEMONSET_NAME),
    "SEMRESATTRS_K8S_DAEMONSET_UID": (()=>SEMRESATTRS_K8S_DAEMONSET_UID),
    "SEMRESATTRS_K8S_DEPLOYMENT_NAME": (()=>SEMRESATTRS_K8S_DEPLOYMENT_NAME),
    "SEMRESATTRS_K8S_DEPLOYMENT_UID": (()=>SEMRESATTRS_K8S_DEPLOYMENT_UID),
    "SEMRESATTRS_K8S_JOB_NAME": (()=>SEMRESATTRS_K8S_JOB_NAME),
    "SEMRESATTRS_K8S_JOB_UID": (()=>SEMRESATTRS_K8S_JOB_UID),
    "SEMRESATTRS_K8S_NAMESPACE_NAME": (()=>SEMRESATTRS_K8S_NAMESPACE_NAME),
    "SEMRESATTRS_K8S_NODE_NAME": (()=>SEMRESATTRS_K8S_NODE_NAME),
    "SEMRESATTRS_K8S_NODE_UID": (()=>SEMRESATTRS_K8S_NODE_UID),
    "SEMRESATTRS_K8S_POD_NAME": (()=>SEMRESATTRS_K8S_POD_NAME),
    "SEMRESATTRS_K8S_POD_UID": (()=>SEMRESATTRS_K8S_POD_UID),
    "SEMRESATTRS_K8S_REPLICASET_NAME": (()=>SEMRESATTRS_K8S_REPLICASET_NAME),
    "SEMRESATTRS_K8S_REPLICASET_UID": (()=>SEMRESATTRS_K8S_REPLICASET_UID),
    "SEMRESATTRS_K8S_STATEFULSET_NAME": (()=>SEMRESATTRS_K8S_STATEFULSET_NAME),
    "SEMRESATTRS_K8S_STATEFULSET_UID": (()=>SEMRESATTRS_K8S_STATEFULSET_UID),
    "SEMRESATTRS_OS_DESCRIPTION": (()=>SEMRESATTRS_OS_DESCRIPTION),
    "SEMRESATTRS_OS_NAME": (()=>SEMRESATTRS_OS_NAME),
    "SEMRESATTRS_OS_TYPE": (()=>SEMRESATTRS_OS_TYPE),
    "SEMRESATTRS_OS_VERSION": (()=>SEMRESATTRS_OS_VERSION),
    "SEMRESATTRS_PROCESS_COMMAND": (()=>SEMRESATTRS_PROCESS_COMMAND),
    "SEMRESATTRS_PROCESS_COMMAND_ARGS": (()=>SEMRESATTRS_PROCESS_COMMAND_ARGS),
    "SEMRESATTRS_PROCESS_COMMAND_LINE": (()=>SEMRESATTRS_PROCESS_COMMAND_LINE),
    "SEMRESATTRS_PROCESS_EXECUTABLE_NAME": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_NAME),
    "SEMRESATTRS_PROCESS_EXECUTABLE_PATH": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_PATH),
    "SEMRESATTRS_PROCESS_OWNER": (()=>SEMRESATTRS_PROCESS_OWNER),
    "SEMRESATTRS_PROCESS_PID": (()=>SEMRESATTRS_PROCESS_PID),
    "SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION": (()=>SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION),
    "SEMRESATTRS_PROCESS_RUNTIME_NAME": (()=>SEMRESATTRS_PROCESS_RUNTIME_NAME),
    "SEMRESATTRS_PROCESS_RUNTIME_VERSION": (()=>SEMRESATTRS_PROCESS_RUNTIME_VERSION),
    "SEMRESATTRS_SERVICE_INSTANCE_ID": (()=>SEMRESATTRS_SERVICE_INSTANCE_ID),
    "SEMRESATTRS_SERVICE_NAME": (()=>SEMRESATTRS_SERVICE_NAME),
    "SEMRESATTRS_SERVICE_NAMESPACE": (()=>SEMRESATTRS_SERVICE_NAMESPACE),
    "SEMRESATTRS_SERVICE_VERSION": (()=>SEMRESATTRS_SERVICE_VERSION),
    "SEMRESATTRS_TELEMETRY_AUTO_VERSION": (()=>SEMRESATTRS_TELEMETRY_AUTO_VERSION),
    "SEMRESATTRS_TELEMETRY_SDK_LANGUAGE": (()=>SEMRESATTRS_TELEMETRY_SDK_LANGUAGE),
    "SEMRESATTRS_TELEMETRY_SDK_NAME": (()=>SEMRESATTRS_TELEMETRY_SDK_NAME),
    "SEMRESATTRS_TELEMETRY_SDK_VERSION": (()=>SEMRESATTRS_TELEMETRY_SDK_VERSION),
    "SEMRESATTRS_WEBENGINE_DESCRIPTION": (()=>SEMRESATTRS_WEBENGINE_DESCRIPTION),
    "SEMRESATTRS_WEBENGINE_NAME": (()=>SEMRESATTRS_WEBENGINE_NAME),
    "SEMRESATTRS_WEBENGINE_VERSION": (()=>SEMRESATTRS_WEBENGINE_VERSION),
    "SemanticResourceAttributes": (()=>SemanticResourceAttributes),
    "TELEMETRYSDKLANGUAGEVALUES_CPP": (()=>TELEMETRYSDKLANGUAGEVALUES_CPP),
    "TELEMETRYSDKLANGUAGEVALUES_DOTNET": (()=>TELEMETRYSDKLANGUAGEVALUES_DOTNET),
    "TELEMETRYSDKLANGUAGEVALUES_ERLANG": (()=>TELEMETRYSDKLANGUAGEVALUES_ERLANG),
    "TELEMETRYSDKLANGUAGEVALUES_GO": (()=>TELEMETRYSDKLANGUAGEVALUES_GO),
    "TELEMETRYSDKLANGUAGEVALUES_JAVA": (()=>TELEMETRYSDKLANGUAGEVALUES_JAVA),
    "TELEMETRYSDKLANGUAGEVALUES_NODEJS": (()=>TELEMETRYSDKLANGUAGEVALUES_NODEJS),
    "TELEMETRYSDKLANGUAGEVALUES_PHP": (()=>TELEMETRYSDKLANGUAGEVALUES_PHP),
    "TELEMETRYSDKLANGUAGEVALUES_PYTHON": (()=>TELEMETRYSDKLANGUAGEVALUES_PYTHON),
    "TELEMETRYSDKLANGUAGEVALUES_RUBY": (()=>TELEMETRYSDKLANGUAGEVALUES_RUBY),
    "TELEMETRYSDKLANGUAGEVALUES_WEBJS": (()=>TELEMETRYSDKLANGUAGEVALUES_WEBJS),
    "TelemetrySdkLanguageValues": (()=>TelemetrySdkLanguageValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)");
;
//----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2
//----------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------
// Constant values for SemanticResourceAttributes
//----------------------------------------------------------------------------------------------------------
// Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUD_PROVIDER = 'cloud.provider';
var TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';
var TMP_CLOUD_REGION = 'cloud.region';
var TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';
var TMP_CLOUD_PLATFORM = 'cloud.platform';
var TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';
var TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';
var TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';
var TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';
var TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';
var TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';
var TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';
var TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';
var TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';
var TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';
var TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';
var TMP_CONTAINER_NAME = 'container.name';
var TMP_CONTAINER_ID = 'container.id';
var TMP_CONTAINER_RUNTIME = 'container.runtime';
var TMP_CONTAINER_IMAGE_NAME = 'container.image.name';
var TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';
var TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';
var TMP_DEVICE_ID = 'device.id';
var TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';
var TMP_DEVICE_MODEL_NAME = 'device.model.name';
var TMP_FAAS_NAME = 'faas.name';
var TMP_FAAS_ID = 'faas.id';
var TMP_FAAS_VERSION = 'faas.version';
var TMP_FAAS_INSTANCE = 'faas.instance';
var TMP_FAAS_MAX_MEMORY = 'faas.max_memory';
var TMP_HOST_ID = 'host.id';
var TMP_HOST_NAME = 'host.name';
var TMP_HOST_TYPE = 'host.type';
var TMP_HOST_ARCH = 'host.arch';
var TMP_HOST_IMAGE_NAME = 'host.image.name';
var TMP_HOST_IMAGE_ID = 'host.image.id';
var TMP_HOST_IMAGE_VERSION = 'host.image.version';
var TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';
var TMP_K8S_NODE_NAME = 'k8s.node.name';
var TMP_K8S_NODE_UID = 'k8s.node.uid';
var TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';
var TMP_K8S_POD_UID = 'k8s.pod.uid';
var TMP_K8S_POD_NAME = 'k8s.pod.name';
var TMP_K8S_CONTAINER_NAME = 'k8s.container.name';
var TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';
var TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';
var TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';
var TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';
var TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';
var TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';
var TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';
var TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';
var TMP_K8S_JOB_UID = 'k8s.job.uid';
var TMP_K8S_JOB_NAME = 'k8s.job.name';
var TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';
var TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';
var TMP_OS_TYPE = 'os.type';
var TMP_OS_DESCRIPTION = 'os.description';
var TMP_OS_NAME = 'os.name';
var TMP_OS_VERSION = 'os.version';
var TMP_PROCESS_PID = 'process.pid';
var TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';
var TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';
var TMP_PROCESS_COMMAND = 'process.command';
var TMP_PROCESS_COMMAND_LINE = 'process.command_line';
var TMP_PROCESS_COMMAND_ARGS = 'process.command_args';
var TMP_PROCESS_OWNER = 'process.owner';
var TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';
var TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';
var TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';
var TMP_SERVICE_NAME = 'service.name';
var TMP_SERVICE_NAMESPACE = 'service.namespace';
var TMP_SERVICE_INSTANCE_ID = 'service.instance.id';
var TMP_SERVICE_VERSION = 'service.version';
var TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';
var TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';
var TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';
var TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';
var TMP_WEBENGINE_NAME = 'webengine.name';
var TMP_WEBENGINE_VERSION = 'webengine.version';
var TMP_WEBENGINE_DESCRIPTION = 'webengine.description';
var SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;
var SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;
var SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;
var SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;
var SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;
var SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;
var SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;
var SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;
var SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;
var SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;
var SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;
var SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;
var SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;
var SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;
var SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;
var SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;
var SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;
var SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;
var SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;
var SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;
var SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;
var SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;
var SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;
var SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;
var SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;
var SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;
var SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;
var SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;
var SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;
var SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;
var SEMRESATTRS_HOST_ID = TMP_HOST_ID;
var SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;
var SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;
var SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;
var SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;
var SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;
var SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;
var SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;
var SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;
var SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;
var SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;
var SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;
var SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;
var SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;
var SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;
var SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;
var SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;
var SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;
var SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;
var SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;
var SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;
var SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;
var SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;
var SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;
var SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;
var SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;
var SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;
var SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;
var SEMRESATTRS_OS_NAME = TMP_OS_NAME;
var SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;
var SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;
var SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;
var SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;
var SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;
var SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;
var SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;
var SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;
var SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;
var SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;
var SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION = TMP_PROCESS_RUNTIME_DESCRIPTION;
var SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;
var SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;
var SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;
var SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;
var SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;
var SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;
var SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;
var SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;
var SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;
var SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;
var SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;
var SemanticResourceAttributes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUD_PROVIDER,
    TMP_CLOUD_ACCOUNT_ID,
    TMP_CLOUD_REGION,
    TMP_CLOUD_AVAILABILITY_ZONE,
    TMP_CLOUD_PLATFORM,
    TMP_AWS_ECS_CONTAINER_ARN,
    TMP_AWS_ECS_CLUSTER_ARN,
    TMP_AWS_ECS_LAUNCHTYPE,
    TMP_AWS_ECS_TASK_ARN,
    TMP_AWS_ECS_TASK_FAMILY,
    TMP_AWS_ECS_TASK_REVISION,
    TMP_AWS_EKS_CLUSTER_ARN,
    TMP_AWS_LOG_GROUP_NAMES,
    TMP_AWS_LOG_GROUP_ARNS,
    TMP_AWS_LOG_STREAM_NAMES,
    TMP_AWS_LOG_STREAM_ARNS,
    TMP_CONTAINER_NAME,
    TMP_CONTAINER_ID,
    TMP_CONTAINER_RUNTIME,
    TMP_CONTAINER_IMAGE_NAME,
    TMP_CONTAINER_IMAGE_TAG,
    TMP_DEPLOYMENT_ENVIRONMENT,
    TMP_DEVICE_ID,
    TMP_DEVICE_MODEL_IDENTIFIER,
    TMP_DEVICE_MODEL_NAME,
    TMP_FAAS_NAME,
    TMP_FAAS_ID,
    TMP_FAAS_VERSION,
    TMP_FAAS_INSTANCE,
    TMP_FAAS_MAX_MEMORY,
    TMP_HOST_ID,
    TMP_HOST_NAME,
    TMP_HOST_TYPE,
    TMP_HOST_ARCH,
    TMP_HOST_IMAGE_NAME,
    TMP_HOST_IMAGE_ID,
    TMP_HOST_IMAGE_VERSION,
    TMP_K8S_CLUSTER_NAME,
    TMP_K8S_NODE_NAME,
    TMP_K8S_NODE_UID,
    TMP_K8S_NAMESPACE_NAME,
    TMP_K8S_POD_UID,
    TMP_K8S_POD_NAME,
    TMP_K8S_CONTAINER_NAME,
    TMP_K8S_REPLICASET_UID,
    TMP_K8S_REPLICASET_NAME,
    TMP_K8S_DEPLOYMENT_UID,
    TMP_K8S_DEPLOYMENT_NAME,
    TMP_K8S_STATEFULSET_UID,
    TMP_K8S_STATEFULSET_NAME,
    TMP_K8S_DAEMONSET_UID,
    TMP_K8S_DAEMONSET_NAME,
    TMP_K8S_JOB_UID,
    TMP_K8S_JOB_NAME,
    TMP_K8S_CRONJOB_UID,
    TMP_K8S_CRONJOB_NAME,
    TMP_OS_TYPE,
    TMP_OS_DESCRIPTION,
    TMP_OS_NAME,
    TMP_OS_VERSION,
    TMP_PROCESS_PID,
    TMP_PROCESS_EXECUTABLE_NAME,
    TMP_PROCESS_EXECUTABLE_PATH,
    TMP_PROCESS_COMMAND,
    TMP_PROCESS_COMMAND_LINE,
    TMP_PROCESS_COMMAND_ARGS,
    TMP_PROCESS_OWNER,
    TMP_PROCESS_RUNTIME_NAME,
    TMP_PROCESS_RUNTIME_VERSION,
    TMP_PROCESS_RUNTIME_DESCRIPTION,
    TMP_SERVICE_NAME,
    TMP_SERVICE_NAMESPACE,
    TMP_SERVICE_INSTANCE_ID,
    TMP_SERVICE_VERSION,
    TMP_TELEMETRY_SDK_NAME,
    TMP_TELEMETRY_SDK_LANGUAGE,
    TMP_TELEMETRY_SDK_VERSION,
    TMP_TELEMETRY_AUTO_VERSION,
    TMP_WEBENGINE_NAME,
    TMP_WEBENGINE_VERSION,
    TMP_WEBENGINE_DESCRIPTION
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudProviderValues enum definition
 *
 * Name of the cloud provider.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';
var TMP_CLOUDPROVIDERVALUES_AWS = 'aws';
var TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';
var TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';
var CLOUDPROVIDERVALUES_ALIBABA_CLOUD = TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;
var CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;
var CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;
var CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;
var CloudProviderValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,
    TMP_CLOUDPROVIDERVALUES_AWS,
    TMP_CLOUDPROVIDERVALUES_AZURE,
    TMP_CLOUDPROVIDERVALUES_GCP
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudPlatformValues enum definition
 *
 * The cloud platform in use.
 *
 * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';
var TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';
var TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';
var TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';
var TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';
var TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';
var TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';
var TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = 'azure_container_instances';
var TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';
var TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';
var TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';
var TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';
var TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';
var TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;
var CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;
var CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;
var CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;
var CLOUDPLATFORMVALUES_AWS_LAMBDA = TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;
var CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;
var CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;
var CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;
var CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;
var CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;
var CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;
var CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;
var CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;
var CLOUDPLATFORMVALUES_GCP_APP_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;
var CloudPlatformValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,
    TMP_CLOUDPLATFORMVALUES_AWS_EC2,
    TMP_CLOUDPLATFORMVALUES_AWS_ECS,
    TMP_CLOUDPLATFORMVALUES_AWS_EKS,
    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,
    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,
    TMP_CLOUDPLATFORMVALUES_AZURE_VM,
    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,
    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,
    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,
    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,
    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for AwsEcsLaunchtypeValues enum definition
 *
 * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';
var TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';
var AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;
var AWSECSLAUNCHTYPEVALUES_FARGATE = TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;
var AwsEcsLaunchtypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_AWSECSLAUNCHTYPEVALUES_EC2,
    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for HostArchValues enum definition
 *
 * The CPU architecture the host system is running on.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_HOSTARCHVALUES_AMD64 = 'amd64';
var TMP_HOSTARCHVALUES_ARM32 = 'arm32';
var TMP_HOSTARCHVALUES_ARM64 = 'arm64';
var TMP_HOSTARCHVALUES_IA64 = 'ia64';
var TMP_HOSTARCHVALUES_PPC32 = 'ppc32';
var TMP_HOSTARCHVALUES_PPC64 = 'ppc64';
var TMP_HOSTARCHVALUES_X86 = 'x86';
var HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;
var HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;
var HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;
var HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;
var HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;
var HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;
var HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;
var HostArchValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_HOSTARCHVALUES_AMD64,
    TMP_HOSTARCHVALUES_ARM32,
    TMP_HOSTARCHVALUES_ARM64,
    TMP_HOSTARCHVALUES_IA64,
    TMP_HOSTARCHVALUES_PPC32,
    TMP_HOSTARCHVALUES_PPC64,
    TMP_HOSTARCHVALUES_X86
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for OsTypeValues enum definition
 *
 * The operating system type.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_OSTYPEVALUES_WINDOWS = 'windows';
var TMP_OSTYPEVALUES_LINUX = 'linux';
var TMP_OSTYPEVALUES_DARWIN = 'darwin';
var TMP_OSTYPEVALUES_FREEBSD = 'freebsd';
var TMP_OSTYPEVALUES_NETBSD = 'netbsd';
var TMP_OSTYPEVALUES_OPENBSD = 'openbsd';
var TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';
var TMP_OSTYPEVALUES_HPUX = 'hpux';
var TMP_OSTYPEVALUES_AIX = 'aix';
var TMP_OSTYPEVALUES_SOLARIS = 'solaris';
var TMP_OSTYPEVALUES_Z_OS = 'z_os';
var OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;
var OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;
var OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;
var OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;
var OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;
var OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;
var OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;
var OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;
var OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;
var OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;
var OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;
var OsTypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_OSTYPEVALUES_WINDOWS,
    TMP_OSTYPEVALUES_LINUX,
    TMP_OSTYPEVALUES_DARWIN,
    TMP_OSTYPEVALUES_FREEBSD,
    TMP_OSTYPEVALUES_NETBSD,
    TMP_OSTYPEVALUES_OPENBSD,
    TMP_OSTYPEVALUES_DRAGONFLYBSD,
    TMP_OSTYPEVALUES_HPUX,
    TMP_OSTYPEVALUES_AIX,
    TMP_OSTYPEVALUES_SOLARIS,
    TMP_OSTYPEVALUES_Z_OS
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for TelemetrySdkLanguageValues enum definition
 *
 * The language of the telemetry SDK.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';
var TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';
var TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';
var TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';
var TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';
var TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';
var TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';
var TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';
var TELEMETRYSDKLANGUAGEVALUES_CPP = TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;
var TELEMETRYSDKLANGUAGEVALUES_DOTNET = TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;
var TELEMETRYSDKLANGUAGEVALUES_ERLANG = TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;
var TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;
var TELEMETRYSDKLANGUAGEVALUES_JAVA = TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;
var TELEMETRYSDKLANGUAGEVALUES_NODEJS = TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;
var TELEMETRYSDKLANGUAGEVALUES_PHP = TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;
var TELEMETRYSDKLANGUAGEVALUES_PYTHON = TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;
var TELEMETRYSDKLANGUAGEVALUES_RUBY = TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;
var TELEMETRYSDKLANGUAGEVALUES_WEBJS = TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;
var TelemetrySdkLanguageValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2d$fs$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,
    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,
    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,
    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,
    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,
    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,
    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS
]); //# sourceMappingURL=SemanticResourceAttributes.js.map
}}),
"[project]/node_modules/@opentelemetry/sql-common/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Creates a const map from the given values
 * @param values - An array of values to be used as keys and values in the map.
 * @returns A populated version of the map with the values and keys derived from the values.
 */ /*#__NO_SIDE_EFFECTS__*/ __turbopack_context__.s({
    "createConstMap": (()=>createConstMap)
});
function createConstMap(values) {
    // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any
    var res = {};
    var len = values.length;
    for(var lp = 0; lp < len; lp++){
        var val = values[lp];
        if (val) {
            res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;
        }
    }
    return res;
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/sql-common/node_modules/@opentelemetry/semantic-conventions/build/esm/resource/SemanticResourceAttributes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "AWSECSLAUNCHTYPEVALUES_EC2": (()=>AWSECSLAUNCHTYPEVALUES_EC2),
    "AWSECSLAUNCHTYPEVALUES_FARGATE": (()=>AWSECSLAUNCHTYPEVALUES_FARGATE),
    "AwsEcsLaunchtypeValues": (()=>AwsEcsLaunchtypeValues),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS),
    "CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC": (()=>CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC),
    "CLOUDPLATFORMVALUES_AWS_EC2": (()=>CLOUDPLATFORMVALUES_AWS_EC2),
    "CLOUDPLATFORMVALUES_AWS_ECS": (()=>CLOUDPLATFORMVALUES_AWS_ECS),
    "CLOUDPLATFORMVALUES_AWS_EKS": (()=>CLOUDPLATFORMVALUES_AWS_EKS),
    "CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK": (()=>CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK),
    "CLOUDPLATFORMVALUES_AWS_LAMBDA": (()=>CLOUDPLATFORMVALUES_AWS_LAMBDA),
    "CLOUDPLATFORMVALUES_AZURE_AKS": (()=>CLOUDPLATFORMVALUES_AZURE_AKS),
    "CLOUDPLATFORMVALUES_AZURE_APP_SERVICE": (()=>CLOUDPLATFORMVALUES_AZURE_APP_SERVICE),
    "CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES": (()=>CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES),
    "CLOUDPLATFORMVALUES_AZURE_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_AZURE_FUNCTIONS),
    "CLOUDPLATFORMVALUES_AZURE_VM": (()=>CLOUDPLATFORMVALUES_AZURE_VM),
    "CLOUDPLATFORMVALUES_GCP_APP_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_APP_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS),
    "CLOUDPLATFORMVALUES_GCP_CLOUD_RUN": (()=>CLOUDPLATFORMVALUES_GCP_CLOUD_RUN),
    "CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE),
    "CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE": (()=>CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE),
    "CLOUDPROVIDERVALUES_ALIBABA_CLOUD": (()=>CLOUDPROVIDERVALUES_ALIBABA_CLOUD),
    "CLOUDPROVIDERVALUES_AWS": (()=>CLOUDPROVIDERVALUES_AWS),
    "CLOUDPROVIDERVALUES_AZURE": (()=>CLOUDPROVIDERVALUES_AZURE),
    "CLOUDPROVIDERVALUES_GCP": (()=>CLOUDPROVIDERVALUES_GCP),
    "CloudPlatformValues": (()=>CloudPlatformValues),
    "CloudProviderValues": (()=>CloudProviderValues),
    "HOSTARCHVALUES_AMD64": (()=>HOSTARCHVALUES_AMD64),
    "HOSTARCHVALUES_ARM32": (()=>HOSTARCHVALUES_ARM32),
    "HOSTARCHVALUES_ARM64": (()=>HOSTARCHVALUES_ARM64),
    "HOSTARCHVALUES_IA64": (()=>HOSTARCHVALUES_IA64),
    "HOSTARCHVALUES_PPC32": (()=>HOSTARCHVALUES_PPC32),
    "HOSTARCHVALUES_PPC64": (()=>HOSTARCHVALUES_PPC64),
    "HOSTARCHVALUES_X86": (()=>HOSTARCHVALUES_X86),
    "HostArchValues": (()=>HostArchValues),
    "OSTYPEVALUES_AIX": (()=>OSTYPEVALUES_AIX),
    "OSTYPEVALUES_DARWIN": (()=>OSTYPEVALUES_DARWIN),
    "OSTYPEVALUES_DRAGONFLYBSD": (()=>OSTYPEVALUES_DRAGONFLYBSD),
    "OSTYPEVALUES_FREEBSD": (()=>OSTYPEVALUES_FREEBSD),
    "OSTYPEVALUES_HPUX": (()=>OSTYPEVALUES_HPUX),
    "OSTYPEVALUES_LINUX": (()=>OSTYPEVALUES_LINUX),
    "OSTYPEVALUES_NETBSD": (()=>OSTYPEVALUES_NETBSD),
    "OSTYPEVALUES_OPENBSD": (()=>OSTYPEVALUES_OPENBSD),
    "OSTYPEVALUES_SOLARIS": (()=>OSTYPEVALUES_SOLARIS),
    "OSTYPEVALUES_WINDOWS": (()=>OSTYPEVALUES_WINDOWS),
    "OSTYPEVALUES_Z_OS": (()=>OSTYPEVALUES_Z_OS),
    "OsTypeValues": (()=>OsTypeValues),
    "SEMRESATTRS_AWS_ECS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_ECS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_ECS_CONTAINER_ARN": (()=>SEMRESATTRS_AWS_ECS_CONTAINER_ARN),
    "SEMRESATTRS_AWS_ECS_LAUNCHTYPE": (()=>SEMRESATTRS_AWS_ECS_LAUNCHTYPE),
    "SEMRESATTRS_AWS_ECS_TASK_ARN": (()=>SEMRESATTRS_AWS_ECS_TASK_ARN),
    "SEMRESATTRS_AWS_ECS_TASK_FAMILY": (()=>SEMRESATTRS_AWS_ECS_TASK_FAMILY),
    "SEMRESATTRS_AWS_ECS_TASK_REVISION": (()=>SEMRESATTRS_AWS_ECS_TASK_REVISION),
    "SEMRESATTRS_AWS_EKS_CLUSTER_ARN": (()=>SEMRESATTRS_AWS_EKS_CLUSTER_ARN),
    "SEMRESATTRS_AWS_LOG_GROUP_ARNS": (()=>SEMRESATTRS_AWS_LOG_GROUP_ARNS),
    "SEMRESATTRS_AWS_LOG_GROUP_NAMES": (()=>SEMRESATTRS_AWS_LOG_GROUP_NAMES),
    "SEMRESATTRS_AWS_LOG_STREAM_ARNS": (()=>SEMRESATTRS_AWS_LOG_STREAM_ARNS),
    "SEMRESATTRS_AWS_LOG_STREAM_NAMES": (()=>SEMRESATTRS_AWS_LOG_STREAM_NAMES),
    "SEMRESATTRS_CLOUD_ACCOUNT_ID": (()=>SEMRESATTRS_CLOUD_ACCOUNT_ID),
    "SEMRESATTRS_CLOUD_AVAILABILITY_ZONE": (()=>SEMRESATTRS_CLOUD_AVAILABILITY_ZONE),
    "SEMRESATTRS_CLOUD_PLATFORM": (()=>SEMRESATTRS_CLOUD_PLATFORM),
    "SEMRESATTRS_CLOUD_PROVIDER": (()=>SEMRESATTRS_CLOUD_PROVIDER),
    "SEMRESATTRS_CLOUD_REGION": (()=>SEMRESATTRS_CLOUD_REGION),
    "SEMRESATTRS_CONTAINER_ID": (()=>SEMRESATTRS_CONTAINER_ID),
    "SEMRESATTRS_CONTAINER_IMAGE_NAME": (()=>SEMRESATTRS_CONTAINER_IMAGE_NAME),
    "SEMRESATTRS_CONTAINER_IMAGE_TAG": (()=>SEMRESATTRS_CONTAINER_IMAGE_TAG),
    "SEMRESATTRS_CONTAINER_NAME": (()=>SEMRESATTRS_CONTAINER_NAME),
    "SEMRESATTRS_CONTAINER_RUNTIME": (()=>SEMRESATTRS_CONTAINER_RUNTIME),
    "SEMRESATTRS_DEPLOYMENT_ENVIRONMENT": (()=>SEMRESATTRS_DEPLOYMENT_ENVIRONMENT),
    "SEMRESATTRS_DEVICE_ID": (()=>SEMRESATTRS_DEVICE_ID),
    "SEMRESATTRS_DEVICE_MODEL_IDENTIFIER": (()=>SEMRESATTRS_DEVICE_MODEL_IDENTIFIER),
    "SEMRESATTRS_DEVICE_MODEL_NAME": (()=>SEMRESATTRS_DEVICE_MODEL_NAME),
    "SEMRESATTRS_FAAS_ID": (()=>SEMRESATTRS_FAAS_ID),
    "SEMRESATTRS_FAAS_INSTANCE": (()=>SEMRESATTRS_FAAS_INSTANCE),
    "SEMRESATTRS_FAAS_MAX_MEMORY": (()=>SEMRESATTRS_FAAS_MAX_MEMORY),
    "SEMRESATTRS_FAAS_NAME": (()=>SEMRESATTRS_FAAS_NAME),
    "SEMRESATTRS_FAAS_VERSION": (()=>SEMRESATTRS_FAAS_VERSION),
    "SEMRESATTRS_HOST_ARCH": (()=>SEMRESATTRS_HOST_ARCH),
    "SEMRESATTRS_HOST_ID": (()=>SEMRESATTRS_HOST_ID),
    "SEMRESATTRS_HOST_IMAGE_ID": (()=>SEMRESATTRS_HOST_IMAGE_ID),
    "SEMRESATTRS_HOST_IMAGE_NAME": (()=>SEMRESATTRS_HOST_IMAGE_NAME),
    "SEMRESATTRS_HOST_IMAGE_VERSION": (()=>SEMRESATTRS_HOST_IMAGE_VERSION),
    "SEMRESATTRS_HOST_NAME": (()=>SEMRESATTRS_HOST_NAME),
    "SEMRESATTRS_HOST_TYPE": (()=>SEMRESATTRS_HOST_TYPE),
    "SEMRESATTRS_K8S_CLUSTER_NAME": (()=>SEMRESATTRS_K8S_CLUSTER_NAME),
    "SEMRESATTRS_K8S_CONTAINER_NAME": (()=>SEMRESATTRS_K8S_CONTAINER_NAME),
    "SEMRESATTRS_K8S_CRONJOB_NAME": (()=>SEMRESATTRS_K8S_CRONJOB_NAME),
    "SEMRESATTRS_K8S_CRONJOB_UID": (()=>SEMRESATTRS_K8S_CRONJOB_UID),
    "SEMRESATTRS_K8S_DAEMONSET_NAME": (()=>SEMRESATTRS_K8S_DAEMONSET_NAME),
    "SEMRESATTRS_K8S_DAEMONSET_UID": (()=>SEMRESATTRS_K8S_DAEMONSET_UID),
    "SEMRESATTRS_K8S_DEPLOYMENT_NAME": (()=>SEMRESATTRS_K8S_DEPLOYMENT_NAME),
    "SEMRESATTRS_K8S_DEPLOYMENT_UID": (()=>SEMRESATTRS_K8S_DEPLOYMENT_UID),
    "SEMRESATTRS_K8S_JOB_NAME": (()=>SEMRESATTRS_K8S_JOB_NAME),
    "SEMRESATTRS_K8S_JOB_UID": (()=>SEMRESATTRS_K8S_JOB_UID),
    "SEMRESATTRS_K8S_NAMESPACE_NAME": (()=>SEMRESATTRS_K8S_NAMESPACE_NAME),
    "SEMRESATTRS_K8S_NODE_NAME": (()=>SEMRESATTRS_K8S_NODE_NAME),
    "SEMRESATTRS_K8S_NODE_UID": (()=>SEMRESATTRS_K8S_NODE_UID),
    "SEMRESATTRS_K8S_POD_NAME": (()=>SEMRESATTRS_K8S_POD_NAME),
    "SEMRESATTRS_K8S_POD_UID": (()=>SEMRESATTRS_K8S_POD_UID),
    "SEMRESATTRS_K8S_REPLICASET_NAME": (()=>SEMRESATTRS_K8S_REPLICASET_NAME),
    "SEMRESATTRS_K8S_REPLICASET_UID": (()=>SEMRESATTRS_K8S_REPLICASET_UID),
    "SEMRESATTRS_K8S_STATEFULSET_NAME": (()=>SEMRESATTRS_K8S_STATEFULSET_NAME),
    "SEMRESATTRS_K8S_STATEFULSET_UID": (()=>SEMRESATTRS_K8S_STATEFULSET_UID),
    "SEMRESATTRS_OS_DESCRIPTION": (()=>SEMRESATTRS_OS_DESCRIPTION),
    "SEMRESATTRS_OS_NAME": (()=>SEMRESATTRS_OS_NAME),
    "SEMRESATTRS_OS_TYPE": (()=>SEMRESATTRS_OS_TYPE),
    "SEMRESATTRS_OS_VERSION": (()=>SEMRESATTRS_OS_VERSION),
    "SEMRESATTRS_PROCESS_COMMAND": (()=>SEMRESATTRS_PROCESS_COMMAND),
    "SEMRESATTRS_PROCESS_COMMAND_ARGS": (()=>SEMRESATTRS_PROCESS_COMMAND_ARGS),
    "SEMRESATTRS_PROCESS_COMMAND_LINE": (()=>SEMRESATTRS_PROCESS_COMMAND_LINE),
    "SEMRESATTRS_PROCESS_EXECUTABLE_NAME": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_NAME),
    "SEMRESATTRS_PROCESS_EXECUTABLE_PATH": (()=>SEMRESATTRS_PROCESS_EXECUTABLE_PATH),
    "SEMRESATTRS_PROCESS_OWNER": (()=>SEMRESATTRS_PROCESS_OWNER),
    "SEMRESATTRS_PROCESS_PID": (()=>SEMRESATTRS_PROCESS_PID),
    "SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION": (()=>SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION),
    "SEMRESATTRS_PROCESS_RUNTIME_NAME": (()=>SEMRESATTRS_PROCESS_RUNTIME_NAME),
    "SEMRESATTRS_PROCESS_RUNTIME_VERSION": (()=>SEMRESATTRS_PROCESS_RUNTIME_VERSION),
    "SEMRESATTRS_SERVICE_INSTANCE_ID": (()=>SEMRESATTRS_SERVICE_INSTANCE_ID),
    "SEMRESATTRS_SERVICE_NAME": (()=>SEMRESATTRS_SERVICE_NAME),
    "SEMRESATTRS_SERVICE_NAMESPACE": (()=>SEMRESATTRS_SERVICE_NAMESPACE),
    "SEMRESATTRS_SERVICE_VERSION": (()=>SEMRESATTRS_SERVICE_VERSION),
    "SEMRESATTRS_TELEMETRY_AUTO_VERSION": (()=>SEMRESATTRS_TELEMETRY_AUTO_VERSION),
    "SEMRESATTRS_TELEMETRY_SDK_LANGUAGE": (()=>SEMRESATTRS_TELEMETRY_SDK_LANGUAGE),
    "SEMRESATTRS_TELEMETRY_SDK_NAME": (()=>SEMRESATTRS_TELEMETRY_SDK_NAME),
    "SEMRESATTRS_TELEMETRY_SDK_VERSION": (()=>SEMRESATTRS_TELEMETRY_SDK_VERSION),
    "SEMRESATTRS_WEBENGINE_DESCRIPTION": (()=>SEMRESATTRS_WEBENGINE_DESCRIPTION),
    "SEMRESATTRS_WEBENGINE_NAME": (()=>SEMRESATTRS_WEBENGINE_NAME),
    "SEMRESATTRS_WEBENGINE_VERSION": (()=>SEMRESATTRS_WEBENGINE_VERSION),
    "SemanticResourceAttributes": (()=>SemanticResourceAttributes),
    "TELEMETRYSDKLANGUAGEVALUES_CPP": (()=>TELEMETRYSDKLANGUAGEVALUES_CPP),
    "TELEMETRYSDKLANGUAGEVALUES_DOTNET": (()=>TELEMETRYSDKLANGUAGEVALUES_DOTNET),
    "TELEMETRYSDKLANGUAGEVALUES_ERLANG": (()=>TELEMETRYSDKLANGUAGEVALUES_ERLANG),
    "TELEMETRYSDKLANGUAGEVALUES_GO": (()=>TELEMETRYSDKLANGUAGEVALUES_GO),
    "TELEMETRYSDKLANGUAGEVALUES_JAVA": (()=>TELEMETRYSDKLANGUAGEVALUES_JAVA),
    "TELEMETRYSDKLANGUAGEVALUES_NODEJS": (()=>TELEMETRYSDKLANGUAGEVALUES_NODEJS),
    "TELEMETRYSDKLANGUAGEVALUES_PHP": (()=>TELEMETRYSDKLANGUAGEVALUES_PHP),
    "TELEMETRYSDKLANGUAGEVALUES_PYTHON": (()=>TELEMETRYSDKLANGUAGEVALUES_PYTHON),
    "TELEMETRYSDKLANGUAGEVALUES_RUBY": (()=>TELEMETRYSDKLANGUAGEVALUES_RUBY),
    "TELEMETRYSDKLANGUAGEVALUES_WEBJS": (()=>TELEMETRYSDKLANGUAGEVALUES_WEBJS),
    "TelemetrySdkLanguageValues": (()=>TelemetrySdkLanguageValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sql-common/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [app-ssr] (ecmascript)");
;
//----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2
//----------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------
// Constant values for SemanticResourceAttributes
//----------------------------------------------------------------------------------------------------------
// Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUD_PROVIDER = 'cloud.provider';
var TMP_CLOUD_ACCOUNT_ID = 'cloud.account.id';
var TMP_CLOUD_REGION = 'cloud.region';
var TMP_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';
var TMP_CLOUD_PLATFORM = 'cloud.platform';
var TMP_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';
var TMP_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';
var TMP_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';
var TMP_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';
var TMP_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';
var TMP_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';
var TMP_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';
var TMP_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';
var TMP_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';
var TMP_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';
var TMP_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';
var TMP_CONTAINER_NAME = 'container.name';
var TMP_CONTAINER_ID = 'container.id';
var TMP_CONTAINER_RUNTIME = 'container.runtime';
var TMP_CONTAINER_IMAGE_NAME = 'container.image.name';
var TMP_CONTAINER_IMAGE_TAG = 'container.image.tag';
var TMP_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';
var TMP_DEVICE_ID = 'device.id';
var TMP_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';
var TMP_DEVICE_MODEL_NAME = 'device.model.name';
var TMP_FAAS_NAME = 'faas.name';
var TMP_FAAS_ID = 'faas.id';
var TMP_FAAS_VERSION = 'faas.version';
var TMP_FAAS_INSTANCE = 'faas.instance';
var TMP_FAAS_MAX_MEMORY = 'faas.max_memory';
var TMP_HOST_ID = 'host.id';
var TMP_HOST_NAME = 'host.name';
var TMP_HOST_TYPE = 'host.type';
var TMP_HOST_ARCH = 'host.arch';
var TMP_HOST_IMAGE_NAME = 'host.image.name';
var TMP_HOST_IMAGE_ID = 'host.image.id';
var TMP_HOST_IMAGE_VERSION = 'host.image.version';
var TMP_K8S_CLUSTER_NAME = 'k8s.cluster.name';
var TMP_K8S_NODE_NAME = 'k8s.node.name';
var TMP_K8S_NODE_UID = 'k8s.node.uid';
var TMP_K8S_NAMESPACE_NAME = 'k8s.namespace.name';
var TMP_K8S_POD_UID = 'k8s.pod.uid';
var TMP_K8S_POD_NAME = 'k8s.pod.name';
var TMP_K8S_CONTAINER_NAME = 'k8s.container.name';
var TMP_K8S_REPLICASET_UID = 'k8s.replicaset.uid';
var TMP_K8S_REPLICASET_NAME = 'k8s.replicaset.name';
var TMP_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';
var TMP_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';
var TMP_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';
var TMP_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';
var TMP_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';
var TMP_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';
var TMP_K8S_JOB_UID = 'k8s.job.uid';
var TMP_K8S_JOB_NAME = 'k8s.job.name';
var TMP_K8S_CRONJOB_UID = 'k8s.cronjob.uid';
var TMP_K8S_CRONJOB_NAME = 'k8s.cronjob.name';
var TMP_OS_TYPE = 'os.type';
var TMP_OS_DESCRIPTION = 'os.description';
var TMP_OS_NAME = 'os.name';
var TMP_OS_VERSION = 'os.version';
var TMP_PROCESS_PID = 'process.pid';
var TMP_PROCESS_EXECUTABLE_NAME = 'process.executable.name';
var TMP_PROCESS_EXECUTABLE_PATH = 'process.executable.path';
var TMP_PROCESS_COMMAND = 'process.command';
var TMP_PROCESS_COMMAND_LINE = 'process.command_line';
var TMP_PROCESS_COMMAND_ARGS = 'process.command_args';
var TMP_PROCESS_OWNER = 'process.owner';
var TMP_PROCESS_RUNTIME_NAME = 'process.runtime.name';
var TMP_PROCESS_RUNTIME_VERSION = 'process.runtime.version';
var TMP_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';
var TMP_SERVICE_NAME = 'service.name';
var TMP_SERVICE_NAMESPACE = 'service.namespace';
var TMP_SERVICE_INSTANCE_ID = 'service.instance.id';
var TMP_SERVICE_VERSION = 'service.version';
var TMP_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';
var TMP_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';
var TMP_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';
var TMP_TELEMETRY_AUTO_VERSION = 'telemetry.auto.version';
var TMP_WEBENGINE_NAME = 'webengine.name';
var TMP_WEBENGINE_VERSION = 'webengine.version';
var TMP_WEBENGINE_DESCRIPTION = 'webengine.description';
var SEMRESATTRS_CLOUD_PROVIDER = TMP_CLOUD_PROVIDER;
var SEMRESATTRS_CLOUD_ACCOUNT_ID = TMP_CLOUD_ACCOUNT_ID;
var SEMRESATTRS_CLOUD_REGION = TMP_CLOUD_REGION;
var SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = TMP_CLOUD_AVAILABILITY_ZONE;
var SEMRESATTRS_CLOUD_PLATFORM = TMP_CLOUD_PLATFORM;
var SEMRESATTRS_AWS_ECS_CONTAINER_ARN = TMP_AWS_ECS_CONTAINER_ARN;
var SEMRESATTRS_AWS_ECS_CLUSTER_ARN = TMP_AWS_ECS_CLUSTER_ARN;
var SEMRESATTRS_AWS_ECS_LAUNCHTYPE = TMP_AWS_ECS_LAUNCHTYPE;
var SEMRESATTRS_AWS_ECS_TASK_ARN = TMP_AWS_ECS_TASK_ARN;
var SEMRESATTRS_AWS_ECS_TASK_FAMILY = TMP_AWS_ECS_TASK_FAMILY;
var SEMRESATTRS_AWS_ECS_TASK_REVISION = TMP_AWS_ECS_TASK_REVISION;
var SEMRESATTRS_AWS_EKS_CLUSTER_ARN = TMP_AWS_EKS_CLUSTER_ARN;
var SEMRESATTRS_AWS_LOG_GROUP_NAMES = TMP_AWS_LOG_GROUP_NAMES;
var SEMRESATTRS_AWS_LOG_GROUP_ARNS = TMP_AWS_LOG_GROUP_ARNS;
var SEMRESATTRS_AWS_LOG_STREAM_NAMES = TMP_AWS_LOG_STREAM_NAMES;
var SEMRESATTRS_AWS_LOG_STREAM_ARNS = TMP_AWS_LOG_STREAM_ARNS;
var SEMRESATTRS_CONTAINER_NAME = TMP_CONTAINER_NAME;
var SEMRESATTRS_CONTAINER_ID = TMP_CONTAINER_ID;
var SEMRESATTRS_CONTAINER_RUNTIME = TMP_CONTAINER_RUNTIME;
var SEMRESATTRS_CONTAINER_IMAGE_NAME = TMP_CONTAINER_IMAGE_NAME;
var SEMRESATTRS_CONTAINER_IMAGE_TAG = TMP_CONTAINER_IMAGE_TAG;
var SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = TMP_DEPLOYMENT_ENVIRONMENT;
var SEMRESATTRS_DEVICE_ID = TMP_DEVICE_ID;
var SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = TMP_DEVICE_MODEL_IDENTIFIER;
var SEMRESATTRS_DEVICE_MODEL_NAME = TMP_DEVICE_MODEL_NAME;
var SEMRESATTRS_FAAS_NAME = TMP_FAAS_NAME;
var SEMRESATTRS_FAAS_ID = TMP_FAAS_ID;
var SEMRESATTRS_FAAS_VERSION = TMP_FAAS_VERSION;
var SEMRESATTRS_FAAS_INSTANCE = TMP_FAAS_INSTANCE;
var SEMRESATTRS_FAAS_MAX_MEMORY = TMP_FAAS_MAX_MEMORY;
var SEMRESATTRS_HOST_ID = TMP_HOST_ID;
var SEMRESATTRS_HOST_NAME = TMP_HOST_NAME;
var SEMRESATTRS_HOST_TYPE = TMP_HOST_TYPE;
var SEMRESATTRS_HOST_ARCH = TMP_HOST_ARCH;
var SEMRESATTRS_HOST_IMAGE_NAME = TMP_HOST_IMAGE_NAME;
var SEMRESATTRS_HOST_IMAGE_ID = TMP_HOST_IMAGE_ID;
var SEMRESATTRS_HOST_IMAGE_VERSION = TMP_HOST_IMAGE_VERSION;
var SEMRESATTRS_K8S_CLUSTER_NAME = TMP_K8S_CLUSTER_NAME;
var SEMRESATTRS_K8S_NODE_NAME = TMP_K8S_NODE_NAME;
var SEMRESATTRS_K8S_NODE_UID = TMP_K8S_NODE_UID;
var SEMRESATTRS_K8S_NAMESPACE_NAME = TMP_K8S_NAMESPACE_NAME;
var SEMRESATTRS_K8S_POD_UID = TMP_K8S_POD_UID;
var SEMRESATTRS_K8S_POD_NAME = TMP_K8S_POD_NAME;
var SEMRESATTRS_K8S_CONTAINER_NAME = TMP_K8S_CONTAINER_NAME;
var SEMRESATTRS_K8S_REPLICASET_UID = TMP_K8S_REPLICASET_UID;
var SEMRESATTRS_K8S_REPLICASET_NAME = TMP_K8S_REPLICASET_NAME;
var SEMRESATTRS_K8S_DEPLOYMENT_UID = TMP_K8S_DEPLOYMENT_UID;
var SEMRESATTRS_K8S_DEPLOYMENT_NAME = TMP_K8S_DEPLOYMENT_NAME;
var SEMRESATTRS_K8S_STATEFULSET_UID = TMP_K8S_STATEFULSET_UID;
var SEMRESATTRS_K8S_STATEFULSET_NAME = TMP_K8S_STATEFULSET_NAME;
var SEMRESATTRS_K8S_DAEMONSET_UID = TMP_K8S_DAEMONSET_UID;
var SEMRESATTRS_K8S_DAEMONSET_NAME = TMP_K8S_DAEMONSET_NAME;
var SEMRESATTRS_K8S_JOB_UID = TMP_K8S_JOB_UID;
var SEMRESATTRS_K8S_JOB_NAME = TMP_K8S_JOB_NAME;
var SEMRESATTRS_K8S_CRONJOB_UID = TMP_K8S_CRONJOB_UID;
var SEMRESATTRS_K8S_CRONJOB_NAME = TMP_K8S_CRONJOB_NAME;
var SEMRESATTRS_OS_TYPE = TMP_OS_TYPE;
var SEMRESATTRS_OS_DESCRIPTION = TMP_OS_DESCRIPTION;
var SEMRESATTRS_OS_NAME = TMP_OS_NAME;
var SEMRESATTRS_OS_VERSION = TMP_OS_VERSION;
var SEMRESATTRS_PROCESS_PID = TMP_PROCESS_PID;
var SEMRESATTRS_PROCESS_EXECUTABLE_NAME = TMP_PROCESS_EXECUTABLE_NAME;
var SEMRESATTRS_PROCESS_EXECUTABLE_PATH = TMP_PROCESS_EXECUTABLE_PATH;
var SEMRESATTRS_PROCESS_COMMAND = TMP_PROCESS_COMMAND;
var SEMRESATTRS_PROCESS_COMMAND_LINE = TMP_PROCESS_COMMAND_LINE;
var SEMRESATTRS_PROCESS_COMMAND_ARGS = TMP_PROCESS_COMMAND_ARGS;
var SEMRESATTRS_PROCESS_OWNER = TMP_PROCESS_OWNER;
var SEMRESATTRS_PROCESS_RUNTIME_NAME = TMP_PROCESS_RUNTIME_NAME;
var SEMRESATTRS_PROCESS_RUNTIME_VERSION = TMP_PROCESS_RUNTIME_VERSION;
var SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION = TMP_PROCESS_RUNTIME_DESCRIPTION;
var SEMRESATTRS_SERVICE_NAME = TMP_SERVICE_NAME;
var SEMRESATTRS_SERVICE_NAMESPACE = TMP_SERVICE_NAMESPACE;
var SEMRESATTRS_SERVICE_INSTANCE_ID = TMP_SERVICE_INSTANCE_ID;
var SEMRESATTRS_SERVICE_VERSION = TMP_SERVICE_VERSION;
var SEMRESATTRS_TELEMETRY_SDK_NAME = TMP_TELEMETRY_SDK_NAME;
var SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = TMP_TELEMETRY_SDK_LANGUAGE;
var SEMRESATTRS_TELEMETRY_SDK_VERSION = TMP_TELEMETRY_SDK_VERSION;
var SEMRESATTRS_TELEMETRY_AUTO_VERSION = TMP_TELEMETRY_AUTO_VERSION;
var SEMRESATTRS_WEBENGINE_NAME = TMP_WEBENGINE_NAME;
var SEMRESATTRS_WEBENGINE_VERSION = TMP_WEBENGINE_VERSION;
var SEMRESATTRS_WEBENGINE_DESCRIPTION = TMP_WEBENGINE_DESCRIPTION;
var SemanticResourceAttributes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUD_PROVIDER,
    TMP_CLOUD_ACCOUNT_ID,
    TMP_CLOUD_REGION,
    TMP_CLOUD_AVAILABILITY_ZONE,
    TMP_CLOUD_PLATFORM,
    TMP_AWS_ECS_CONTAINER_ARN,
    TMP_AWS_ECS_CLUSTER_ARN,
    TMP_AWS_ECS_LAUNCHTYPE,
    TMP_AWS_ECS_TASK_ARN,
    TMP_AWS_ECS_TASK_FAMILY,
    TMP_AWS_ECS_TASK_REVISION,
    TMP_AWS_EKS_CLUSTER_ARN,
    TMP_AWS_LOG_GROUP_NAMES,
    TMP_AWS_LOG_GROUP_ARNS,
    TMP_AWS_LOG_STREAM_NAMES,
    TMP_AWS_LOG_STREAM_ARNS,
    TMP_CONTAINER_NAME,
    TMP_CONTAINER_ID,
    TMP_CONTAINER_RUNTIME,
    TMP_CONTAINER_IMAGE_NAME,
    TMP_CONTAINER_IMAGE_TAG,
    TMP_DEPLOYMENT_ENVIRONMENT,
    TMP_DEVICE_ID,
    TMP_DEVICE_MODEL_IDENTIFIER,
    TMP_DEVICE_MODEL_NAME,
    TMP_FAAS_NAME,
    TMP_FAAS_ID,
    TMP_FAAS_VERSION,
    TMP_FAAS_INSTANCE,
    TMP_FAAS_MAX_MEMORY,
    TMP_HOST_ID,
    TMP_HOST_NAME,
    TMP_HOST_TYPE,
    TMP_HOST_ARCH,
    TMP_HOST_IMAGE_NAME,
    TMP_HOST_IMAGE_ID,
    TMP_HOST_IMAGE_VERSION,
    TMP_K8S_CLUSTER_NAME,
    TMP_K8S_NODE_NAME,
    TMP_K8S_NODE_UID,
    TMP_K8S_NAMESPACE_NAME,
    TMP_K8S_POD_UID,
    TMP_K8S_POD_NAME,
    TMP_K8S_CONTAINER_NAME,
    TMP_K8S_REPLICASET_UID,
    TMP_K8S_REPLICASET_NAME,
    TMP_K8S_DEPLOYMENT_UID,
    TMP_K8S_DEPLOYMENT_NAME,
    TMP_K8S_STATEFULSET_UID,
    TMP_K8S_STATEFULSET_NAME,
    TMP_K8S_DAEMONSET_UID,
    TMP_K8S_DAEMONSET_NAME,
    TMP_K8S_JOB_UID,
    TMP_K8S_JOB_NAME,
    TMP_K8S_CRONJOB_UID,
    TMP_K8S_CRONJOB_NAME,
    TMP_OS_TYPE,
    TMP_OS_DESCRIPTION,
    TMP_OS_NAME,
    TMP_OS_VERSION,
    TMP_PROCESS_PID,
    TMP_PROCESS_EXECUTABLE_NAME,
    TMP_PROCESS_EXECUTABLE_PATH,
    TMP_PROCESS_COMMAND,
    TMP_PROCESS_COMMAND_LINE,
    TMP_PROCESS_COMMAND_ARGS,
    TMP_PROCESS_OWNER,
    TMP_PROCESS_RUNTIME_NAME,
    TMP_PROCESS_RUNTIME_VERSION,
    TMP_PROCESS_RUNTIME_DESCRIPTION,
    TMP_SERVICE_NAME,
    TMP_SERVICE_NAMESPACE,
    TMP_SERVICE_INSTANCE_ID,
    TMP_SERVICE_VERSION,
    TMP_TELEMETRY_SDK_NAME,
    TMP_TELEMETRY_SDK_LANGUAGE,
    TMP_TELEMETRY_SDK_VERSION,
    TMP_TELEMETRY_AUTO_VERSION,
    TMP_WEBENGINE_NAME,
    TMP_WEBENGINE_VERSION,
    TMP_WEBENGINE_DESCRIPTION
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudProviderValues enum definition
 *
 * Name of the cloud provider.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';
var TMP_CLOUDPROVIDERVALUES_AWS = 'aws';
var TMP_CLOUDPROVIDERVALUES_AZURE = 'azure';
var TMP_CLOUDPROVIDERVALUES_GCP = 'gcp';
var CLOUDPROVIDERVALUES_ALIBABA_CLOUD = TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD;
var CLOUDPROVIDERVALUES_AWS = TMP_CLOUDPROVIDERVALUES_AWS;
var CLOUDPROVIDERVALUES_AZURE = TMP_CLOUDPROVIDERVALUES_AZURE;
var CLOUDPROVIDERVALUES_GCP = TMP_CLOUDPROVIDERVALUES_GCP;
var CloudProviderValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPROVIDERVALUES_ALIBABA_CLOUD,
    TMP_CLOUDPROVIDERVALUES_AWS,
    TMP_CLOUDPROVIDERVALUES_AZURE,
    TMP_CLOUDPROVIDERVALUES_GCP
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for CloudPlatformValues enum definition
 *
 * The cloud platform in use.
 *
 * Note: The prefix of the service SHOULD match the one specified in `cloud.provider`.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = 'alibaba_cloud_ecs';
var TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = 'alibaba_cloud_fc';
var TMP_CLOUDPLATFORMVALUES_AWS_EC2 = 'aws_ec2';
var TMP_CLOUDPLATFORMVALUES_AWS_ECS = 'aws_ecs';
var TMP_CLOUDPLATFORMVALUES_AWS_EKS = 'aws_eks';
var TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA = 'aws_lambda';
var TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = 'aws_elastic_beanstalk';
var TMP_CLOUDPLATFORMVALUES_AZURE_VM = 'azure_vm';
var TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = 'azure_container_instances';
var TMP_CLOUDPLATFORMVALUES_AZURE_AKS = 'azure_aks';
var TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = 'azure_functions';
var TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = 'azure_app_service';
var TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = 'gcp_compute_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = 'gcp_cloud_run';
var TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = 'gcp_kubernetes_engine';
var TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = 'gcp_cloud_functions';
var TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE = 'gcp_app_engine';
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS;
var CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC;
var CLOUDPLATFORMVALUES_AWS_EC2 = TMP_CLOUDPLATFORMVALUES_AWS_EC2;
var CLOUDPLATFORMVALUES_AWS_ECS = TMP_CLOUDPLATFORMVALUES_AWS_ECS;
var CLOUDPLATFORMVALUES_AWS_EKS = TMP_CLOUDPLATFORMVALUES_AWS_EKS;
var CLOUDPLATFORMVALUES_AWS_LAMBDA = TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA;
var CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK;
var CLOUDPLATFORMVALUES_AZURE_VM = TMP_CLOUDPLATFORMVALUES_AZURE_VM;
var CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES;
var CLOUDPLATFORMVALUES_AZURE_AKS = TMP_CLOUDPLATFORMVALUES_AZURE_AKS;
var CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS;
var CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE;
var CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN;
var CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE;
var CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS;
var CLOUDPLATFORMVALUES_GCP_APP_ENGINE = TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE;
var CloudPlatformValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,
    TMP_CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC,
    TMP_CLOUDPLATFORMVALUES_AWS_EC2,
    TMP_CLOUDPLATFORMVALUES_AWS_ECS,
    TMP_CLOUDPLATFORMVALUES_AWS_EKS,
    TMP_CLOUDPLATFORMVALUES_AWS_LAMBDA,
    TMP_CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK,
    TMP_CLOUDPLATFORMVALUES_AZURE_VM,
    TMP_CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES,
    TMP_CLOUDPLATFORMVALUES_AZURE_AKS,
    TMP_CLOUDPLATFORMVALUES_AZURE_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_AZURE_APP_SERVICE,
    TMP_CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_RUN,
    TMP_CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE,
    TMP_CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS,
    TMP_CLOUDPLATFORMVALUES_GCP_APP_ENGINE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for AwsEcsLaunchtypeValues enum definition
 *
 * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_AWSECSLAUNCHTYPEVALUES_EC2 = 'ec2';
var TMP_AWSECSLAUNCHTYPEVALUES_FARGATE = 'fargate';
var AWSECSLAUNCHTYPEVALUES_EC2 = TMP_AWSECSLAUNCHTYPEVALUES_EC2;
var AWSECSLAUNCHTYPEVALUES_FARGATE = TMP_AWSECSLAUNCHTYPEVALUES_FARGATE;
var AwsEcsLaunchtypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_AWSECSLAUNCHTYPEVALUES_EC2,
    TMP_AWSECSLAUNCHTYPEVALUES_FARGATE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for HostArchValues enum definition
 *
 * The CPU architecture the host system is running on.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_HOSTARCHVALUES_AMD64 = 'amd64';
var TMP_HOSTARCHVALUES_ARM32 = 'arm32';
var TMP_HOSTARCHVALUES_ARM64 = 'arm64';
var TMP_HOSTARCHVALUES_IA64 = 'ia64';
var TMP_HOSTARCHVALUES_PPC32 = 'ppc32';
var TMP_HOSTARCHVALUES_PPC64 = 'ppc64';
var TMP_HOSTARCHVALUES_X86 = 'x86';
var HOSTARCHVALUES_AMD64 = TMP_HOSTARCHVALUES_AMD64;
var HOSTARCHVALUES_ARM32 = TMP_HOSTARCHVALUES_ARM32;
var HOSTARCHVALUES_ARM64 = TMP_HOSTARCHVALUES_ARM64;
var HOSTARCHVALUES_IA64 = TMP_HOSTARCHVALUES_IA64;
var HOSTARCHVALUES_PPC32 = TMP_HOSTARCHVALUES_PPC32;
var HOSTARCHVALUES_PPC64 = TMP_HOSTARCHVALUES_PPC64;
var HOSTARCHVALUES_X86 = TMP_HOSTARCHVALUES_X86;
var HostArchValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_HOSTARCHVALUES_AMD64,
    TMP_HOSTARCHVALUES_ARM32,
    TMP_HOSTARCHVALUES_ARM64,
    TMP_HOSTARCHVALUES_IA64,
    TMP_HOSTARCHVALUES_PPC32,
    TMP_HOSTARCHVALUES_PPC64,
    TMP_HOSTARCHVALUES_X86
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for OsTypeValues enum definition
 *
 * The operating system type.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_OSTYPEVALUES_WINDOWS = 'windows';
var TMP_OSTYPEVALUES_LINUX = 'linux';
var TMP_OSTYPEVALUES_DARWIN = 'darwin';
var TMP_OSTYPEVALUES_FREEBSD = 'freebsd';
var TMP_OSTYPEVALUES_NETBSD = 'netbsd';
var TMP_OSTYPEVALUES_OPENBSD = 'openbsd';
var TMP_OSTYPEVALUES_DRAGONFLYBSD = 'dragonflybsd';
var TMP_OSTYPEVALUES_HPUX = 'hpux';
var TMP_OSTYPEVALUES_AIX = 'aix';
var TMP_OSTYPEVALUES_SOLARIS = 'solaris';
var TMP_OSTYPEVALUES_Z_OS = 'z_os';
var OSTYPEVALUES_WINDOWS = TMP_OSTYPEVALUES_WINDOWS;
var OSTYPEVALUES_LINUX = TMP_OSTYPEVALUES_LINUX;
var OSTYPEVALUES_DARWIN = TMP_OSTYPEVALUES_DARWIN;
var OSTYPEVALUES_FREEBSD = TMP_OSTYPEVALUES_FREEBSD;
var OSTYPEVALUES_NETBSD = TMP_OSTYPEVALUES_NETBSD;
var OSTYPEVALUES_OPENBSD = TMP_OSTYPEVALUES_OPENBSD;
var OSTYPEVALUES_DRAGONFLYBSD = TMP_OSTYPEVALUES_DRAGONFLYBSD;
var OSTYPEVALUES_HPUX = TMP_OSTYPEVALUES_HPUX;
var OSTYPEVALUES_AIX = TMP_OSTYPEVALUES_AIX;
var OSTYPEVALUES_SOLARIS = TMP_OSTYPEVALUES_SOLARIS;
var OSTYPEVALUES_Z_OS = TMP_OSTYPEVALUES_Z_OS;
var OsTypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_OSTYPEVALUES_WINDOWS,
    TMP_OSTYPEVALUES_LINUX,
    TMP_OSTYPEVALUES_DARWIN,
    TMP_OSTYPEVALUES_FREEBSD,
    TMP_OSTYPEVALUES_NETBSD,
    TMP_OSTYPEVALUES_OPENBSD,
    TMP_OSTYPEVALUES_DRAGONFLYBSD,
    TMP_OSTYPEVALUES_HPUX,
    TMP_OSTYPEVALUES_AIX,
    TMP_OSTYPEVALUES_SOLARIS,
    TMP_OSTYPEVALUES_Z_OS
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for TelemetrySdkLanguageValues enum definition
 *
 * The language of the telemetry SDK.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_TELEMETRYSDKLANGUAGEVALUES_CPP = 'cpp';
var TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET = 'dotnet';
var TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG = 'erlang';
var TMP_TELEMETRYSDKLANGUAGEVALUES_GO = 'go';
var TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA = 'java';
var TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS = 'nodejs';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PHP = 'php';
var TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON = 'python';
var TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY = 'ruby';
var TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS = 'webjs';
var TELEMETRYSDKLANGUAGEVALUES_CPP = TMP_TELEMETRYSDKLANGUAGEVALUES_CPP;
var TELEMETRYSDKLANGUAGEVALUES_DOTNET = TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET;
var TELEMETRYSDKLANGUAGEVALUES_ERLANG = TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG;
var TELEMETRYSDKLANGUAGEVALUES_GO = TMP_TELEMETRYSDKLANGUAGEVALUES_GO;
var TELEMETRYSDKLANGUAGEVALUES_JAVA = TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA;
var TELEMETRYSDKLANGUAGEVALUES_NODEJS = TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS;
var TELEMETRYSDKLANGUAGEVALUES_PHP = TMP_TELEMETRYSDKLANGUAGEVALUES_PHP;
var TELEMETRYSDKLANGUAGEVALUES_PYTHON = TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON;
var TELEMETRYSDKLANGUAGEVALUES_RUBY = TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY;
var TELEMETRYSDKLANGUAGEVALUES_WEBJS = TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS;
var TelemetrySdkLanguageValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sql$2d$common$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_TELEMETRYSDKLANGUAGEVALUES_CPP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_DOTNET,
    TMP_TELEMETRYSDKLANGUAGEVALUES_ERLANG,
    TMP_TELEMETRYSDKLANGUAGEVALUES_GO,
    TMP_TELEMETRYSDKLANGUAGEVALUES_JAVA,
    TMP_TELEMETRYSDKLANGUAGEVALUES_NODEJS,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PHP,
    TMP_TELEMETRYSDKLANGUAGEVALUES_PYTHON,
    TMP_TELEMETRYSDKLANGUAGEVALUES_RUBY,
    TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS
]); //# sourceMappingURL=SemanticResourceAttributes.js.map
}}),

};

//# sourceMappingURL=node_modules_%40opentelemetry_209d44b1._.js.map