{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/lib/constants.ts", "turbopack:///[project]/node_modules/next/src/server/lib/clone-response.ts", "turbopack:///[project]/node_modules/next/src/server/lib/dedupe-fetch.ts", "turbopack:///[project]/node_modules/next/src/server/response-cache/types.ts", "turbopack:///[project]/node_modules/next/src/lib/batcher.ts", "turbopack:///[project]/node_modules/next/src/server/request-meta.ts", "turbopack:///[project]/node_modules/next/src/server/web/utils.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/i18n/detect-domain-locale.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/parse-path.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/add-path-suffix.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/add-locale.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/format-next-pathname-info.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/get-hostname.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/i18n/normalize-locale-path.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/remove-path-prefix.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/get-next-pathname-info.ts", "turbopack:///[project]/node_modules/next/src/server/web/next-url.ts", "turbopack:///[project]/node_modules/next/src/server/web/error.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/request.ts", "turbopack:///[project]/node_modules/next/src/server/base-http/helpers.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/adapters/next-request.ts", "turbopack:///[project]/node_modules/next/src/server/client-component-renderer-logger.ts", "turbopack:///[project]/node_modules/next/src/server/pipe-readable.ts", "turbopack:///[project]/node_modules/next/src/server/render-result.ts", "turbopack:///[project]/node_modules/next/src/server/route-kind.ts", "turbopack:///[project]/node_modules/next/src/server/response-cache/utils.ts", "turbopack:///[project]/node_modules/next/src/server/response-cache/index.ts", "turbopack:///[project]/node_modules/next/src/server/lib/patch-fetch.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/unstable-cache.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/sorted-routes.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/interception-routes.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/is-dynamic.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/index.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/revalidate.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/unstable-no-store.ts", "turbopack:///[project]/node_modules/next/src/server/use-cache/cache-life.ts", "turbopack:///[project]/node_modules/next/src/server/use-cache/cache-tag.ts", "turbopack:///[project]/node_modules/next/cache.js"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n", "/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n  })\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n  })\n\n  return [cloned1, cloned2]\n}\n", "/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */\nimport * as React from 'react'\nimport { cloneResponse } from './clone-response'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n\nfunction generateCacheKey(request: Request): string {\n  // We pick the fields that goes into the key used to dedupe requests.\n  // We don't include the `cache` field, because we end up using whatever\n  // caching resulted from the first request.\n  // Notably we currently don't consider non-standard (or future) options.\n  // This might not be safe. TODO: warn for non-standard extensions differing.\n  // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n  return JSON.stringify([\n    request.method,\n    Array.from(request.headers.entries()),\n    request.mode,\n    request.redirect,\n    request.credentials,\n    request.referrer,\n    request.referrerPolicy,\n    request.integrity,\n  ])\n}\n\ntype CacheEntry = [\n  key: string,\n  promise: Promise<Response>,\n  response: Response | null,\n]\n\nexport function createDedupeFetch(originalFetch: typeof fetch) {\n  const getCacheEntries = React.cache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url: string): CacheEntry[] => []\n  )\n\n  return function dedupeFetch(\n    resource: URL | RequestInfo,\n    options?: RequestInit\n  ): Promise<Response> {\n    if (options && options.signal) {\n      // If we're passed a signal, then we assume that\n      // someone else controls the lifetime of this object and opts out of\n      // caching. It's effectively the opt-out mechanism.\n      // Ideally we should be able to check this on the Request but\n      // it always gets initialized with its own signal so we don't\n      // know if it's supposed to override - unless we also override the\n      // Request constructor.\n      return originalFetch(resource, options)\n    }\n    // Normalize the Request\n    let url: string\n    let cacheKey: string\n    if (typeof resource === 'string' && !options) {\n      // Fast path.\n      cacheKey = simpleCacheKey\n      url = resource\n    } else {\n      // Normalize the request.\n      // if resource is not a string or a URL (its an instance of Request)\n      // then do not instantiate a new Request but instead\n      // reuse the request as to not disturb the body in the event it's a ReadableStream.\n      const request =\n        typeof resource === 'string' || resource instanceof URL\n          ? new Request(resource, options)\n          : resource\n      if (\n        (request.method !== 'GET' && request.method !== 'HEAD') ||\n        request.keepalive\n      ) {\n        // We currently don't dedupe requests that might have side-effects. Those\n        // have to be explicitly cached. We assume that the request doesn't have a\n        // body if it's GET or HEAD.\n        // keepalive gets treated the same as if you passed a custom cache signal.\n        return originalFetch(resource, options)\n      }\n      cacheKey = generateCacheKey(request)\n      url = request.url\n    }\n\n    const cacheEntries = getCacheEntries(url)\n    for (let i = 0, j = cacheEntries.length; i < j; i += 1) {\n      const [key, promise] = cacheEntries[i]\n      if (key === cacheKey) {\n        return promise.then(() => {\n          const response = cacheEntries[i][2]\n          if (!response) throw new InvariantError('No cached response')\n\n          // We're cloning the response using this utility because there exists\n          // a bug in the undici library around response cloning. See the\n          // following pull request for more details:\n          // https://github.com/vercel/next.js/pull/73274\n          const [cloned1, cloned2] = cloneResponse(response)\n          cacheEntries[i][2] = cloned2\n          return cloned1\n        })\n      }\n    }\n\n    // We pass the original arguments here in case normalizing the Request\n    // doesn't include all the options in this environment.\n    const promise = originalFetch(resource, options)\n    const entry: CacheEntry = [cacheKey, promise, null]\n    cacheEntries.push(entry)\n\n    return promise.then((response) => {\n      // We're cloning the response using this utility because there exists\n      // a bug in the undici library around response cloning. See the\n      // following pull request for more details:\n      // https://github.com/vercel/next.js/pull/73274\n      const [cloned1, cloned2] = cloneResponse(response)\n      entry[2] = cloned2\n      return cloned1\n    })\n  }\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport type RenderResult from '../render-result'\nimport type { CacheControl, Revalidate } from '../lib/cache-control'\nimport type { RouteKind } from '../route-kind'\n\nexport interface ResponseCacheBase {\n  get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalCache\n      /**\n       * This is a hint to the cache to help it determine what kind of route\n       * this is so it knows where to look up the cache entry from. If not\n       * provided it will test the filesystem to check.\n       */\n      routeKind: RouteKind\n\n      /**\n       * True if this is a fallback request.\n       */\n      isFallback?: boolean\n\n      /**\n       * True if the route is enabled for PPR.\n       */\n      isRoutePPREnabled?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null>\n}\n\n// The server components HMR cache might store other data as well in the future,\n// at which point this should be refactored to a discriminated union type.\nexport interface ServerComponentsHmrCache {\n  get(key: string): CachedFetchData | undefined\n  set(key: string, data: CachedFetchData): void\n}\n\nexport type CachedFetchData = {\n  headers: Record<string, string>\n  body: string\n  url: string\n  status?: number\n}\n\nexport const enum CachedRouteKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  REDIRECT = 'REDIRECT',\n  IMAGE = 'IMAGE',\n}\n\nexport interface CachedFetchValue {\n  kind: CachedRouteKind.FETCH\n  data: CachedFetchData\n  // tags are only present with file-system-cache\n  // fetch cache stores tags outside of cache entry\n  tags?: string[]\n  revalidate: number\n}\n\nexport interface CachedRedirectValue {\n  kind: CachedRouteKind.REDIRECT\n  props: Object\n}\n\nexport interface CachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  rscData: Buffer | undefined\n  status: number | undefined\n  postponed: string | undefined\n  headers: OutgoingHttpHeaders | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface CachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  pageData: Object\n  status: number | undefined\n  headers: OutgoingHttpHeaders | undefined\n}\n\nexport interface CachedRouteValue {\n  kind: CachedRouteKind.APP_ROUTE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  body: Buffer\n  status: number\n  headers: OutgoingHttpHeaders\n}\n\nexport interface CachedImageValue {\n  kind: CachedRouteKind.IMAGE\n  etag: string\n  upstreamEtag: string\n  buffer: Buffer\n  extension: string\n  isMiss?: boolean\n  isStale?: boolean\n}\n\nexport interface IncrementalCachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  rscData: Buffer | undefined\n  headers: OutgoingHttpHeaders | undefined\n  postponed: string | undefined\n  status: number | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface IncrementalCachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  pageData: Object\n  headers: OutgoingHttpHeaders | undefined\n  status: number | undefined\n}\n\nexport interface IncrementalResponseCacheEntry {\n  cacheControl?: CacheControl\n  /**\n   * timestamp in milliseconds to revalidate after\n   */\n  revalidateAfter?: Revalidate\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n  value: Exclude<IncrementalCacheValue, CachedFetchValue> | null\n}\n\nexport interface IncrementalFetchCacheEntry {\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  value: CachedFetchValue\n}\n\nexport type IncrementalCacheEntry =\n  | IncrementalResponseCacheEntry\n  | IncrementalFetchCacheEntry\n\nexport type IncrementalCacheValue =\n  | CachedRedirectValue\n  | IncrementalCachedPageValue\n  | IncrementalCachedAppPageValue\n  | CachedImageValue\n  | CachedFetchValue\n  | CachedRouteValue\n\nexport type ResponseCacheValue =\n  | CachedRedirectValue\n  | CachedPageValue\n  | CachedAppPageValue\n  | CachedImageValue\n  | CachedRouteValue\n\nexport type ResponseCacheEntry = {\n  cacheControl?: CacheControl\n  value: ResponseCacheValue | null\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n}\n\n/**\n * @param hasResolved whether the responseGenerator has resolved it's promise\n * @param previousCacheEntry the previous cache entry if it exists or the current\n */\nexport type ResponseGenerator = (state: {\n  hasResolved: boolean\n  previousCacheEntry?: IncrementalResponseCacheEntry | null\n  isRevalidating?: boolean\n}) => Promise<ResponseCacheEntry | null>\n\nexport const enum IncrementalCacheKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  IMAGE = 'IMAGE',\n}\n\nexport interface GetIncrementalFetchCacheContext {\n  kind: IncrementalCacheKind.FETCH\n  revalidate?: Revalidate\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  softTags?: string[]\n}\n\nexport interface GetIncrementalResponseCacheContext {\n  kind: Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH>\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback: boolean\n}\n\nexport interface SetIncrementalFetchCacheContext {\n  fetchCache: true\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n}\n\nexport interface SetIncrementalResponseCacheContext {\n  fetchCache?: false\n  cacheControl?: CacheControl\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback?: boolean\n}\n\nexport interface IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n\nexport interface IncrementalCache extends IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n", "import type { SchedulerFn } from './scheduler'\n\nimport { DetachedPromise } from './detached-promise'\n\ntype CacheKeyFn<K, C extends string | number | null> = (\n  key: K\n) => PromiseLike<C> | C\n\ntype BatcherOptions<K, C extends string | number | null> = {\n  cacheKeyFn?: CacheKeyFn<K, C>\n  schedulerFn?: SchedulerFn<void>\n}\n\ntype WorkFn<V, C> = (\n  key: C,\n  resolve: (value: V | PromiseLike<V>) => void\n) => Promise<V>\n\n/**\n * A wrapper for a function that will only allow one call to the function to\n * execute at a time.\n */\nexport class Batcher<K, V, C extends string | number | null> {\n  private readonly pending = new Map<C, Promise<V>>()\n\n  protected constructor(\n    private readonly cacheKeyFn?: CacheKeyFn<K, C>,\n    /**\n     * A function that will be called to schedule the wrapped function to be\n     * executed. This defaults to a function that will execute the function\n     * immediately.\n     */\n    private readonly schedulerFn: SchedulerFn<void> = (fn) => fn()\n  ) {}\n\n  /**\n   * Creates a new instance of PendingWrapper. If the key extends a string or\n   * number, the key will be used as the cache key. If the key is an object, a\n   * cache key function must be provided.\n   */\n  public static create<K extends string | number | null, V>(\n    options?: BatcherOptions<K, K>\n  ): Batcher<K, V, K>\n  public static create<K, V, C extends string | number | null>(\n    options: BatcherOptions<K, C> &\n      Required<Pick<BatcherOptions<K, C>, 'cacheKeyFn'>>\n  ): Batcher<K, V, C>\n  public static create<K, V, C extends string | number | null>(\n    options?: BatcherOptions<K, C>\n  ): Batcher<K, V, C> {\n    return new Batcher<K, V, C>(options?.cacheKeyFn, options?.schedulerFn)\n  }\n\n  /**\n   * Wraps a function in a promise that will be resolved or rejected only once\n   * for a given key. This will allow multiple calls to the function to be\n   * made, but only one will be executed at a time. The result of the first\n   * call will be returned to all callers.\n   *\n   * @param key the key to use for the cache\n   * @param fn the function to wrap\n   * @returns a promise that resolves to the result of the function\n   */\n  public async batch(key: K, fn: WorkFn<V, C>): Promise<V> {\n    const cacheKey = (this.cacheKeyFn ? await this.cacheKeyFn(key) : key) as C\n    if (cacheKey === null) {\n      return fn(cacheKey, Promise.resolve)\n    }\n\n    const pending = this.pending.get(cacheKey)\n    if (pending) return pending\n\n    const { promise, resolve, reject } = new DetachedPromise<V>()\n    this.pending.set(cacheKey, promise)\n\n    this.schedulerFn(async () => {\n      try {\n        const result = await fn(cacheKey, resolve)\n\n        // Resolving a promise multiple times is a no-op, so we can safely\n        // resolve all pending promises with the same result.\n        resolve(result)\n      } catch (err) {\n        reject(err)\n      } finally {\n        this.pending.delete(cacheKey)\n      }\n    })\n\n    return promise\n  }\n}\n", "/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the default route matches were set on the request during routing.\n   */\n  didSetDefaultRouteMatches?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n", "import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n", "import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n", "import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n", "export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n", "import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n", "import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n", "export class PageSignatureError extends Error {\n  constructor({ page }: { page: string }) {\n    super(`The middleware \"${page}\" accepts an async API directly with the form:\n  \n  export function middleware(request, event) {\n    return NextResponse.redirect('/new-location')\n  }\n  \n  Read more: https://nextjs.org/docs/messages/middleware-new-signature\n  `)\n  }\n}\n\nexport class RemovedPageError extends Error {\n  constructor() {\n    super(`The request.page has been deprecated in favour of \\`URLPattern\\`.\n  Read more: https://nextjs.org/docs/messages/middleware-request-page\n  `)\n  }\n}\n\nexport class RemovedUAError extends Error {\n  constructor() {\n    super(`The request.ua has been removed in favour of \\`userAgent\\` function.\n  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n  `)\n  }\n}\n", "import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n", "import type { BaseNextRequest, BaseNextResponse } from './'\nimport type { NodeNextRequest, NodeNextResponse } from './node'\nimport type { WebNextRequest, WebNextResponse } from './web'\n\n/**\n * This file provides some helpers that should be used in conjunction with\n * explicit environment checks. When combined with the environment checks, it\n * will ensure that the correct typings are used as well as enable code\n * elimination.\n */\n\n/**\n * Type guard to determine if a request is a WebNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base request is a WebNextRequest.\n */\nexport const isWebNextRequest = (req: BaseNextRequest): req is WebNextRequest =>\n  process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a response is a WebNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base response is a WebNextResponse.\n */\nexport const isWebNextResponse = (\n  res: BaseNextResponse\n): res is WebNextResponse => process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a request is a NodeNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base request is a NodeNextRequest.\n */\nexport const isNodeNextRequest = (\n  req: BaseNextRequest\n): req is NodeNextRequest => process.env.NEXT_RUNTIME !== 'edge'\n\n/**\n * Type guard to determine if a response is a NodeNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base response is a NodeNextResponse.\n */\nexport const isNodeNextResponse = (\n  res: BaseNextResponse\n): res is NodeNextResponse => process.env.NEXT_RUNTIME !== 'edge'\n", "import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n", "import type { AppPageModule } from './route-modules/app-page/module'\n\n// Combined load times for loading client components\nlet clientComponentLoadStart = 0\nlet clientComponentLoadTimes = 0\nlet clientComponentLoadCount = 0\n\nexport function wrapClientComponentLoader(\n  ComponentMod: AppPageModule\n): AppPageModule['__next_app__'] {\n  if (!('performance' in globalThis)) {\n    return ComponentMod.__next_app__\n  }\n\n  return {\n    require: (...args) => {\n      const startTime = performance.now()\n\n      if (clientComponentLoadStart === 0) {\n        clientComponentLoadStart = startTime\n      }\n\n      try {\n        clientComponentLoadCount += 1\n        return ComponentMod.__next_app__.require(...args)\n      } finally {\n        clientComponentLoadTimes += performance.now() - startTime\n      }\n    },\n    loadChunk: (...args) => {\n      const startTime = performance.now()\n      const result = ComponentMod.__next_app__.loadChunk(...args)\n      // Avoid wrapping `loadChunk`'s result in an extra promise in case something like React depends on its identity.\n      // We only need to know when it's settled.\n      result.finally(() => {\n        clientComponentLoadTimes += performance.now() - startTime\n      })\n      return result\n    },\n  }\n}\n\nexport function getClientComponentLoaderMetrics(\n  options: { reset?: boolean } = {}\n) {\n  const metrics =\n    clientComponentLoadStart === 0\n      ? undefined\n      : {\n          clientComponentLoadStart,\n          clientComponentLoadTimes,\n          clientComponentLoadCount,\n        }\n\n  if (options.reset) {\n    clientComponentLoadStart = 0\n    clientComponentLoadTimes = 0\n    clientComponentLoadCount = 0\n  }\n\n  return metrics\n}\n", "import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n", "import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the cache is warmed up before the render. This is attached\n   * to the metadata so that it can be used during the render.\n   */\n  devRenderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n", "export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n", "import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    isFallback: response.isFallback,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n", "import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  private minimalMode?: boolean\n\n  constructor(minimalMode: boolean) {\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      async (cacheKey, resolve) => {\n        // We keep the previous cache entry around to leverage when the\n        // incremental cache is disabled in minimal mode.\n        if (\n          this.minimalMode &&\n          this.previousCacheItem?.key === cacheKey &&\n          this.previousCacheItem.expiresAt > Date.now()\n        ) {\n          return this.previousCacheItem.entry\n        }\n\n        // Coerce the kindHint into a given kind for the incremental cache.\n        const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n        let resolved = false\n        let cachedResponse: IncrementalResponseCacheEntry | null = null\n        try {\n          cachedResponse = !this.minimalMode\n            ? await incrementalCache.get(key, {\n                kind,\n                isRoutePPREnabled: context.isRoutePPREnabled,\n                isFallback,\n              })\n            : null\n\n          if (cachedResponse && !isOnDemandRevalidate) {\n            resolve(cachedResponse)\n            resolved = true\n\n            if (!cachedResponse.isStale || context.isPrefetch) {\n              // The cached value is still valid, so we don't need\n              // to update it yet.\n              return null\n            }\n          }\n\n          const cacheEntry = await responseGenerator({\n            hasResolved: resolved,\n            previousCacheEntry: cachedResponse,\n            isRevalidating: true,\n          })\n\n          // If the cache entry couldn't be generated, we don't want to cache\n          // the result.\n          if (!cacheEntry) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          const resolveValue = await fromResponseCacheEntry({\n            ...cacheEntry,\n            isMiss: !cachedResponse,\n          })\n          if (!resolveValue) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          // For on-demand revalidate wait to resolve until cache is set.\n          // Otherwise resolve now.\n          if (!isOnDemandRevalidate && !resolved) {\n            resolve(resolveValue)\n            resolved = true\n          }\n\n          // We want to persist the result only if it has a cache control value\n          // defined.\n          if (resolveValue.cacheControl) {\n            if (this.minimalMode) {\n              this.previousCacheItem = {\n                key: cacheKey,\n                entry: resolveValue,\n                expiresAt: Date.now() + 1000,\n              }\n            } else {\n              await incrementalCache.set(key, resolveValue.value, {\n                cacheControl: resolveValue.cacheControl,\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n          }\n\n          return resolveValue\n        } catch (err) {\n          // When a path is erroring we automatically re-set the existing cache\n          // with new revalidate and expire times to prevent non-stop retrying.\n          if (cachedResponse?.cacheControl) {\n            const newRevalidate = Math.min(\n              Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n              30\n            )\n\n            const newExpire =\n              cachedResponse.cacheControl.expire === undefined\n                ? undefined\n                : Math.max(\n                    newRevalidate + 3,\n                    cachedResponse.cacheControl.expire\n                  )\n\n            await incrementalCache.set(key, cachedResponse.value, {\n              cacheControl: { revalidate: newRevalidate, expire: newExpire },\n              isRoutePPREnabled,\n              isFallback,\n            })\n          }\n\n          // While revalidating in the background we can't reject as we already\n          // resolved the cache entry so log the error here.\n          if (resolved) {\n            console.error(err)\n            return null\n          }\n\n          // We haven't resolved yet, so let's throw to indicate an error.\n          throw err\n        }\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n", "import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function. We don't set the type here, as it's\n  // verified as the return value of this function.\n  const patched = async (\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ) => {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        let currentFetchRevalidate = getNextField('revalidate')\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          // if we are inside of \"use cache\"/\"unstable_cache\"\n          // we shouldn't set the revalidate to 0 as it's overridden\n          // by the cache context\n          workUnitStore?.type !== 'cache' &&\n          (hasExplicitFetchCacheOptOut || noFetchConfigAndForceDynamic)\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n        const autoNoCache =\n          // this condition is hit for null/undefined\n          // eslint-disable-next-line eqeqeq\n          (hasNoExplicitCacheConfig &&\n            // we disable automatic no caching behavior during build time SSG so that we can still\n            // leverage the fetch cache between SSG workers\n            !workStore.isPrerendering) ||\n          ((hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore &&\n            revalidateStore.revalidate === 0)\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender'\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e. currentFetchRevalidate.\n          if (revalidateStore && currentFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock = () => Promise.resolve()\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (workUnitStore && workUnitStore.type === 'prerender') {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    { fetchCache: true, fetchUrl, fetchIdx, tags }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          { fetchCache: true, fetchUrl, fetchIdx, tags }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `no-store fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                return makeHangingPromise<Response>(\n                  workUnitStore.renderSignal,\n                  'fetch()'\n                )\n              } else {\n                markCurrentScopeAsDynamic(\n                  workStore,\n                  workUnitStore,\n                  `revalidate: 0 fetch ${input} ${workStore.route}`\n                )\n              }\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n", "import type { IncrementalCache } from '../../lib/incremental-cache'\n\nimport { CACHE_ONE_YEAR } from '../../../lib/constants'\nimport { validateRevalidate, validateTags } from '../../lib/patch-fetch'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport {\n  getDraftModeProviderForCacheScope,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../../response-cache'\nimport type { UnstableCacheStore } from '../../app-render/work-unit-async-storage.external'\n\ntype Callback = (...args: any[]) => Promise<any>\n\nlet noStoreFetchIdx = 0\n\nasync function cacheNewResult<T>(\n  result: T,\n  incrementalCache: IncrementalCache,\n  cacheKey: string,\n  tags: string[],\n  revalidate: number | false | undefined,\n  fetchIdx: number,\n  fetchUrl: string\n): Promise<unknown> {\n  await incrementalCache.set(\n    cacheKey,\n    {\n      kind: CachedRouteKind.FETCH,\n      data: {\n        headers: {},\n        // TODO: handle non-JSON values?\n        body: JSON.stringify(result),\n        status: 200,\n        url: '',\n      } satisfies CachedFetchData,\n      revalidate: typeof revalidate !== 'number' ? CACHE_ONE_YEAR : revalidate,\n    },\n    { fetchCache: true, tags, fetchIdx, fetchUrl }\n  )\n  return\n}\n\n/**\n * This function allows you to cache the results of expensive operations, like database queries, and reuse them across multiple requests.\n *\n * Read more: [Next.js Docs: `unstable_cache`](https://nextjs.org/docs/app/api-reference/functions/unstable_cache)\n */\nexport function unstable_cache<T extends Callback>(\n  cb: T,\n  keyParts?: string[],\n  options: {\n    /**\n     * The revalidation interval in seconds.\n     */\n    revalidate?: number | false\n    tags?: string[]\n  } = {}\n): T {\n  if (options.revalidate === 0) {\n    throw new Error(\n      `Invariant revalidate: 0 can not be passed to unstable_cache(), must be \"false\" or \"> 0\" ${cb.toString()}`\n    )\n  }\n\n  // Validate the tags provided are valid\n  const tags = options.tags\n    ? validateTags(options.tags, `unstable_cache ${cb.toString()}`)\n    : []\n\n  // Validate the revalidate options\n  validateRevalidate(\n    options.revalidate,\n    `unstable_cache ${cb.name || cb.toString()}`\n  )\n\n  // Stash the fixed part of the key at construction time. The invocation key will combine\n  // the fixed key with the arguments when actually called\n  // @TODO if cb.toString() is long we should hash it\n  // @TODO come up with a collision-free way to combine keyParts\n  // @TODO consider validating the keyParts are all strings. TS can't provide runtime guarantees\n  // and the error produced by accidentally using something that cannot be safely coerced is likely\n  // hard to debug\n  const fixedKey = `${cb.toString()}-${\n    Array.isArray(keyParts) && keyParts.join(',')\n  }`\n\n  const cachedCb = async (...args: any[]) => {\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // We must be able to find the incremental cache otherwise we throw\n    const maybeIncrementalCache:\n      | import('../../lib/incremental-cache').IncrementalCache\n      | undefined =\n      workStore?.incrementalCache || (globalThis as any).__incrementalCache\n\n    if (!maybeIncrementalCache) {\n      throw new Error(\n        `Invariant: incrementalCache missing in unstable_cache ${cb.toString()}`\n      )\n    }\n    const incrementalCache = maybeIncrementalCache\n\n    const cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n    try {\n      // If there's no request store, we aren't in a request (or we're not in app\n      // router)  and if there's no static generation store, we aren't in app\n      // router. Default to an empty pathname and search params when there's no\n      // request store or static generation store available.\n      const requestStore =\n        workUnitStore && workUnitStore.type === 'request'\n          ? workUnitStore\n          : undefined\n      const pathname = requestStore?.url.pathname ?? workStore?.route ?? ''\n      const searchParams = new URLSearchParams(requestStore?.url.search ?? '')\n\n      const sortedSearchKeys = [...searchParams.keys()].sort((a, b) => {\n        return a.localeCompare(b)\n      })\n      const sortedSearch = sortedSearchKeys\n        .map((key) => `${key}=${searchParams.get(key)}`)\n        .join('&')\n\n      // Construct the complete cache key for this function invocation\n      // @TODO stringify is likely not safe here. We will coerce undefined to null which will make\n      // the keyspace smaller than the execution space\n      const invocationKey = `${fixedKey}-${JSON.stringify(args)}`\n      const cacheKey = await incrementalCache.generateCacheKey(invocationKey)\n      // $urlWithPath,$sortedQueryStringKeys,$hashOfEveryThingElse\n      const fetchUrl = `unstable_cache ${pathname}${sortedSearch.length ? '?' : ''}${sortedSearch} ${cb.name ? ` ${cb.name}` : cacheKey}`\n      const fetchIdx =\n        (workStore ? workStore.nextFetchId : noStoreFetchIdx) ?? 1\n\n      const implicitTags = workUnitStore?.implicitTags\n\n      const innerCacheStore: UnstableCacheStore = {\n        type: 'unstable-cache',\n        phase: 'render',\n        implicitTags,\n        draftMode:\n          workUnitStore &&\n          workStore &&\n          getDraftModeProviderForCacheScope(workStore, workUnitStore),\n      }\n\n      if (workStore) {\n        workStore.nextFetchId = fetchIdx + 1\n\n        // We are in an App Router context. We try to return the cached entry if it exists and is valid\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        // We update the store's revalidate property if the option.revalidate is a higher precedence\n        if (\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n        ) {\n          // options.revalidate === undefined doesn't affect timing.\n          // options.revalidate === false doesn't shrink timing. it stays at the maximum.\n          if (typeof options.revalidate === 'number') {\n            if (workUnitStore.revalidate < options.revalidate) {\n              // The store is already revalidating on a shorter time interval, leave it alone\n            } else {\n              workUnitStore.revalidate = options.revalidate\n            }\n          }\n\n          // We need to accumulate the tags for this invocation within the store\n          const collectedTags = workUnitStore.tags\n          if (collectedTags === null) {\n            workUnitStore.tags = tags.slice()\n          } else {\n            for (const tag of tags) {\n              // @TODO refactor tags to be a set to avoid this O(n) lookup\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const isNestedUnstableCache =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n        if (\n          // when we are nested inside of other unstable_cache's\n          // we should bypass cache similar to fetches\n          !isNestedUnstableCache &&\n          workStore.fetchCache !== 'force-no-store' &&\n          !workStore.isOnDemandRevalidate &&\n          !incrementalCache.isOnDemandRevalidate &&\n          !workStore.isDraftMode\n        ) {\n          // We attempt to get the current cache entry from the incremental cache.\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            softTags: implicitTags?.tags,\n            fetchIdx,\n            fetchUrl,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              // @TODO the invocation key can have sensitive data in it. we should not log this entire object\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else {\n              // We have a valid cache entry so we will be returning it. We also check to see if we need\n              // to background revalidate it by checking if it is stale.\n              const cachedResponse =\n                cacheEntry.value.data.body !== undefined\n                  ? JSON.parse(cacheEntry.value.data.body)\n                  : undefined\n              if (cacheEntry.isStale) {\n                // In App Router we return the stale result and revalidate in the background\n                if (!workStore.pendingRevalidates) {\n                  workStore.pendingRevalidates = {}\n                }\n\n                // We run the cache function asynchronously and save the result when it completes\n                workStore.pendingRevalidates[invocationKey] =\n                  workUnitAsyncStorage\n                    .run(innerCacheStore, cb, ...args)\n                    .then((result) => {\n                      return cacheNewResult(\n                        result,\n                        incrementalCache,\n                        cacheKey,\n                        tags,\n                        options.revalidate,\n                        fetchIdx,\n                        fetchUrl\n                      )\n                    })\n                    // @TODO This error handling seems wrong. We swallow the error?\n                    .catch((err) =>\n                      console.error(\n                        `revalidating cache with key: ${invocationKey}`,\n                        err\n                      )\n                    )\n              }\n              // We had a valid cache entry so we return it here\n              return cachedResponse\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        if (!workStore.isDraftMode) {\n          cacheNewResult(\n            result,\n            incrementalCache,\n            cacheKey,\n            tags,\n            options.revalidate,\n            fetchIdx,\n            fetchUrl\n          )\n        }\n\n        return result\n      } else {\n        noStoreFetchIdx += 1\n        // We are in Pages Router or were called outside of a render. We don't have a store\n        // so we just call the callback directly when it needs to run.\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        if (!incrementalCache.isOnDemandRevalidate) {\n          // We aren't doing an on demand revalidation so we check use the cache if valid\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            fetchIdx,\n            fetchUrl,\n            softTags: implicitTags?.tags,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else if (!cacheEntry.isStale) {\n              // We have a valid cache entry and it is fresh so we return it\n              return cacheEntry.value.data.body !== undefined\n                ? JSON.parse(cacheEntry.value.data.body)\n                : undefined\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n        cacheNewResult(\n          result,\n          incrementalCache,\n          cacheKey,\n          tags,\n          options.revalidate,\n          fetchIdx,\n          fetchUrl\n        )\n        return result\n      }\n    } finally {\n      if (cacheSignal) {\n        cacheSignal.endRead()\n      }\n    }\n  }\n  // TODO: once AsyncLocalStorage.run() returns the correct types this override will no longer be necessary\n  return cachedCb as unknown as T\n}\n", "class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n", "export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n", "import {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n} from '../../app-render/dynamic-rendering'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils'\nimport {\n  NEXT_CACHE_IMPLICIT_TAG_ID,\n  NEXT_CACHE_SOFT_TAG_MAX_LENGTH,\n} from '../../../lib/constants'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `revalidateTag`](https://nextjs.org/docs/app/api-reference/functions/revalidateTag)\n */\nexport function revalidateTag(tag: string) {\n  return revalidate([tag], `revalidateTag ${tag}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `unstable_expirePath`](https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath)\n */\nexport function unstable_expirePath(\n  originalPath: string,\n  type?: 'layout' | 'page'\n) {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: expirePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"expirePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n  }\n  return revalidate([normalizedPath], `unstable_expirePath ${originalPath}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `unstable_expireTag`](https://nextjs.org/docs/app/api-reference/functions/unstable_expireTag)\n */\nexport function unstable_expireTag(...tags: string[]) {\n  return revalidate(tags, `unstable_expireTag ${tags.join(', ')}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `revalidatePath`](https://nextjs.org/docs/app/api-reference/functions/revalidatePath)\n */\nexport function revalidatePath(originalPath: string, type?: 'layout' | 'page') {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: revalidatePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"revalidatePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n  }\n  return revalidate([normalizedPath], `revalidatePath ${originalPath}`)\n}\n\nfunction revalidate(tags: string[], expression: string) {\n  const store = workAsyncStorage.getStore()\n  if (!store || !store.incrementalCache) {\n    throw new Error(\n      `Invariant: static generation store missing in ${expression}`\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a \"use cache\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n    if (workUnitStore.phase === 'render') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore.type === 'prerender') {\n      // dynamicIO Prerender\n      const error = new Error(\n        `Route ${store.route} used ${expression} without first calling \\`await connection()\\`.`\n      )\n      abortAndThrowOnSynchronousRequestDataAccess(\n        store.route,\n        expression,\n        error,\n        workUnitStore\n      )\n    } else if (workUnitStore.type === 'prerender-ppr') {\n      // PPR Prerender\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      // legacy Prerender\n      workUnitStore.revalidate = 0\n\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n\n  if (!store.pendingRevalidatedTags) {\n    store.pendingRevalidatedTags = []\n  }\n\n  for (const tag of tags) {\n    if (!store.pendingRevalidatedTags.includes(tag)) {\n      store.pendingRevalidatedTags.push(tag)\n    }\n  }\n\n  // TODO: only revalidate if the path matches\n  store.pathWasRevalidated = true\n}\n", "import { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { markCurrentScopeAsDynamic } from '../../app-render/dynamic-rendering'\n\n/**\n * This function can be used to declaratively opt out of static rendering and indicate a particular component should not be cached.\n *\n * It marks the current scope as dynamic.\n *\n * - In [non-PPR](https://nextjs.org/docs/app/api-reference/next-config-js/partial-prerendering) cases this will make a static render\n * halt and mark the page as dynamic.\n * - In PPR cases this will postpone the render at this location.\n *\n * If we are inside a cache scope then this function does nothing.\n *\n * @note It expects to be called within App Router and will error otherwise.\n *\n * Read more: [Next.js Docs: `unstable_noStore`](https://nextjs.org/docs/app/api-reference/functions/unstable_noStore)\n */\nexport function unstable_noStore() {\n  const callingExpression = 'unstable_noStore()'\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!store) {\n    // This generally implies we are being called in Pages router. We should probably not support\n    // unstable_noStore in contexts outside of `react-server` condition but since we historically\n    // have not errored here previously, we maintain that behavior for now.\n    return\n  } else if (store.forceStatic) {\n    return\n  } else {\n    store.isUnstableNoStore = true\n    if (workUnitStore && workUnitStore.type === 'prerender') {\n      // unstable_noStore() is a noop in Dynamic I/O.\n    } else {\n      markCurrentScopeAsDynamic(store, workUnitStore, callingExpression)\n    }\n  }\n}\n", "import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\nexport type CacheLife = {\n  // How long the client can cache a value without checking with the server.\n  stale?: number\n  // How frequently you want the cache to refresh on the server.\n  // Stale values may be served while revalidating.\n  revalidate?: number\n  // In the worst case scenario, where you haven't had traffic in a while,\n  // how stale can a value be until you prefer deopting to dynamic.\n  // Must be longer than revalidate.\n  expire?: number\n}\n// The equivalent header is kind of like:\n// Cache-Control: max-age=[stale],s-max-age=[revalidate],stale-while-revalidate=[expire-revalidate],stale-if-error=[expire-revalidate]\n// Except that stale-while-revalidate/stale-if-error only applies to shared caches - not private caches.\n\n// The default revalidates relatively frequently but doesn't expire to ensure it's always\n// able to serve fast results but by default doesn't hang.\n\n// This gets overridden by the next-types-plugin\ntype CacheLifeProfiles =\n  | 'default'\n  | 'seconds'\n  | 'minutes'\n  | 'hours'\n  | 'days'\n  | 'weeks'\n  | 'max'\n  | (string & {})\n\nfunction validateCacheLife(profile: CacheLife) {\n  if (profile.stale !== undefined) {\n    if ((profile.stale as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the client forever ' +\n          'without checking with the server.'\n      )\n    } else if (typeof profile.stale !== 'number') {\n      throw new Error('The stale option must be a number of seconds.')\n    }\n  }\n  if (profile.revalidate !== undefined) {\n    if ((profile.revalidate as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you do not want to revalidate by time.'\n      )\n    } else if (typeof profile.revalidate !== 'number') {\n      throw new Error('The revalidate option must be a number of seconds.')\n    }\n  }\n  if (profile.expire !== undefined) {\n    if ((profile.expire as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the server forever ' +\n          'without checking with the origin.'\n      )\n    } else if (typeof profile.expire !== 'number') {\n      throw new Error('The expire option must be a number of seconds.')\n    }\n  }\n\n  if (profile.revalidate !== undefined && profile.expire !== undefined) {\n    if (profile.revalidate > profile.expire) {\n      throw new Error(\n        'If providing both the revalidate and expire options, ' +\n          'the expire option must be greater than the revalidate option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n\n  if (profile.stale !== undefined && profile.expire !== undefined) {\n    if (profile.stale > profile.expire) {\n      throw new Error(\n        'If providing both the stale and expire options, ' +\n          'the expire option must be greater than the stale option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n}\n\nexport function cacheLife(profile: CacheLifeProfiles | CacheLife): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheLife() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheLife() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  if (typeof profile === 'string') {\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new Error(\n        'cacheLife() can only be called during App Router rendering at the moment.'\n      )\n    }\n    if (!workStore.cacheLifeProfiles) {\n      throw new Error(\n        'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n      )\n    }\n\n    // TODO: This should be globally available and not require an AsyncLocalStorage.\n    const configuredProfile = workStore.cacheLifeProfiles[profile]\n    if (configuredProfile === undefined) {\n      if (workStore.cacheLifeProfiles[profile.trim()]) {\n        throw new Error(\n          `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n            `Did you mean \"${profile.trim()}\" without the spaces?`\n        )\n      }\n      throw new Error(\n        `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n          'module.exports = {\\n' +\n          '  experimental: {\\n' +\n          '    cacheLife: {\\n' +\n          `      \"${profile}\": ...\\n` +\n          '    }\\n' +\n          '  }\\n' +\n          '}'\n      )\n    }\n    profile = configuredProfile\n  } else if (\n    typeof profile !== 'object' ||\n    profile === null ||\n    Array.isArray(profile)\n  ) {\n    throw new Error(\n      'Invalid cacheLife() option. Either pass a profile name or object.'\n    )\n  } else {\n    validateCacheLife(profile)\n  }\n\n  if (profile.revalidate !== undefined) {\n    // Track the explicit revalidate time.\n    if (\n      workUnitStore.explicitRevalidate === undefined ||\n      workUnitStore.explicitRevalidate > profile.revalidate\n    ) {\n      workUnitStore.explicitRevalidate = profile.revalidate\n    }\n  }\n  if (profile.expire !== undefined) {\n    // Track the explicit expire time.\n    if (\n      workUnitStore.explicitExpire === undefined ||\n      workUnitStore.explicitExpire > profile.expire\n    ) {\n      workUnitStore.explicitExpire = profile.expire\n    }\n  }\n  if (profile.stale !== undefined) {\n    // Track the explicit stale time.\n    if (\n      workUnitStore.explicitStale === undefined ||\n      workUnitStore.explicitStale > profile.stale\n    ) {\n      workUnitStore.explicitStale = profile.stale\n    }\n  }\n}\n", "import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { validateTags } from '../lib/patch-fetch'\n\nexport function cacheTag(...tags: string[]): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheTag() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheTag() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  const validTags = validateTags(tags, 'cacheTag()')\n\n  if (!workUnitStore.tags) {\n    workUnitStore.tags = validTags\n  } else {\n    workUnitStore.tags.push(...validTags)\n  }\n}\n", "const cacheExports = {\n  unstable_cache: require('next/dist/server/web/spec-extension/unstable-cache')\n    .unstable_cache,\n\n  revalidateTag: require('next/dist/server/web/spec-extension/revalidate')\n    .revalidateTag,\n  revalidatePath: require('next/dist/server/web/spec-extension/revalidate')\n    .revalidatePath,\n\n  unstable_expireTag: require('next/dist/server/web/spec-extension/revalidate')\n    .unstable_expireTag,\n  unstable_expirePath: require('next/dist/server/web/spec-extension/revalidate')\n    .unstable_expirePath,\n\n  unstable_noStore:\n    require('next/dist/server/web/spec-extension/unstable-no-store')\n      .unstable_noStore,\n  unstable_cacheLife: require('next/dist/server/use-cache/cache-life')\n    .cacheLife,\n  unstable_cacheTag: require('next/dist/server/use-cache/cache-tag').cacheTag,\n}\n\n// https://nodejs.org/api/esm.html#commonjs-namespaces\n// When importing CommonJS modules, the module.exports object is provided as the default export\nmodule.exports = cacheExports\n\n// make import { xxx } from 'next/cache' work\nexports.unstable_cache = cacheExports.unstable_cache\nexports.revalidatePath = cacheExports.revalidatePath\nexports.revalidateTag = cacheExports.revalidateTag\nexports.unstable_expireTag = cacheExports.unstable_expireTag\nexports.unstable_expirePath = cacheExports.unstable_expirePath\nexports.unstable_noStore = cacheExports.unstable_noStore\nexports.unstable_cacheLife = cacheExports.unstable_cacheLife\nexports.unstable_cacheTag = cacheExports.unstable_cacheTag\n"], "names": ["ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta", "cloneResponse", "original", "body", "body1", "body2", "tee", "cloned1", "Response", "status", "statusText", "headers", "Object", "defineProperty", "value", "url", "cloned2", "createDedupeFetch", "simple<PERSON><PERSON><PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "request", "JSON", "stringify", "method", "Array", "from", "entries", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "originalFetch", "getCacheEntries", "React", "cache", "dedupe<PERSON><PERSON>ch", "resource", "options", "signal", "cache<PERSON>ey", "URL", "Request", "keepalive", "cacheEntries", "i", "j", "length", "key", "promise", "then", "response", "InvariantError", "entry", "push", "CachedRouteKind", "IncrementalCacheKind", "<PERSON><PERSON>", "cacheKeyFn", "schedulerFn", "fn", "pending", "Map", "create", "batch", "Promise", "resolve", "get", "reject", "Detached<PERSON>romise", "set", "result", "err", "delete", "NEXT_REQUEST_META", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "Symbol", "for", "req", "meta", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "splitCookiesString", "toNodeOutgoingHttpHeaders", "validateURL", "nodeHeaders", "Headers", "values", "isArray", "v", "toString", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "test", "char<PERSON>t", "notSpecialChar", "substring", "cookies", "toLowerCase", "String", "error", "Error", "cause", "prefixes", "prefix", "startsWith", "detectDomainLocale", "domainItems", "hostname", "detectedLocale", "item", "domainHostname", "domain", "split", "defaultLocale", "locales", "some", "locale", "removeTrailingSlash", "route", "replace", "parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "query", "undefined", "hash", "slice", "addPathPrefix", "addPathSuffix", "suffix", "pathHasPrefix", "addLocale", "ignorePrefix", "lower", "formatNextPathnameInfo", "info", "buildId", "trailingSlash", "basePath", "endsWith", "getHostname", "parsed", "host", "normalizeLocalePath", "WeakMap", "lowercasedLocales", "map", "segments", "segment", "index", "removePathPrefix", "withoutPrefix", "getNextPathnameInfo", "i18n", "nextConfig", "pathnameNoDataPrefix", "paths", "join", "parseData", "i18nProvider", "analyze", "NextURL", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "base", "Internal", "constructor", "input", "baseOrOpts", "opts", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "domainLocale", "domains", "formatPathname", "forceLocale", "formatSearch", "search", "includes", "TypeError", "searchParams", "port", "protocol", "href", "origin", "password", "username", "toJSON", "clone", "PageSignatureError", "RemovedPageError", "RemovedUAError", "page", "INTERNALS", "NextRequest", "init", "NEXT_RUNTIME", "duplex", "nextUrl", "RequestCookies", "bodyUsed", "destination", "fromEntries", "ua", "isNodeNextRequest", "isNodeNextResponse", "isWebNextRequest", "isWebNextResponse", "res", "NextRequestAdapter", "ResponseAborted", "ResponseAbortedName", "createAbortController", "signalFromNodeResponse", "name", "controller", "AbortController", "once", "writableFinished", "abort", "errored", "destroyed", "AbortSignal", "fromBaseNextRequest", "fromNodeNextRequest", "aborted", "fromWebNextRequest", "getClientComponentLoaderMetrics", "wrapClientComponentLoader", "clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "ComponentMod", "globalThis", "__next_app__", "require", "args", "startTime", "performance", "now", "loadChunk", "finally", "metrics", "reset", "isAbortError", "pipeToNodeResponse", "e", "createWriterFromResponse", "waitUntilForEnd", "started", "drained", "onDrain", "on", "off", "finished", "WritableStream", "write", "chunk", "NEXT_OTEL_PERFORMANCE_PREFIX", "measure", "end", "flushHeaders", "getTracer", "trace", "NextNodeServerSpan", "startResponse", "spanName", "ok", "flush", "destroy", "close", "readable", "writer", "pipeTo", "RenderResult", "fromStatic", "contentType", "waitUntil", "assignMetadata", "assign", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "streamToBuffer", "<PERSON><PERSON><PERSON>", "toUnchunkedString", "streamToString", "<PERSON><PERSON><PERSON><PERSON>", "streamFromBuffer", "chainStreams", "chain", "responses", "streamFromString", "writable", "preventClose", "RouteKind", "fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "cacheEntry", "kind", "PAGES", "html", "pageData", "APP_PAGE", "postponed", "rscData", "segmentData", "isMiss", "isStale", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "routeKind", "IMAGE", "APP_ROUTE", "ResponseCache", "minimalMode", "batcher", "isOnDemandRevalidate", "scheduleOnNextTick", "minimalModeKey", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "isRoutePPREnabled", "previousCacheItem", "expiresAt", "Date", "resolved", "cachedResponse", "isPrefetch", "isRevalidating", "resolveValue", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "NEXT_PATCH_SYMBOL", "createPatchedFetcher", "patchFetch", "validateRevalidate", "validateTags", "isEdgeRuntime", "isFetchPatched", "revalidateVal", "normalizedRevalidate", "isNaN", "message", "tags", "description", "validTags", "invalidTags", "tag", "reason", "warn", "log", "trackFetchMetric", "workStore", "ctx", "requestEndedState", "ended", "isDebugBuild", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "isDevelopment", "NODE_ENV", "fetchMetrics", "<PERSON><PERSON><PERSON><PERSON>", "idx", "nextFetchId", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "fetchUrl", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "type", "beginRead", "internalFetch", "AppRenderSpan", "fetch", "SpanKind", "CLIENT", "filter", "Boolean", "attributes", "isDraftMode", "isRequestInput", "field", "finalRevalidate", "getNextField", "currentFetchRevalidate", "revalidateStore", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "hasUnCacheableHeader", "isUnCacheableMethod", "hasNoExplicitCacheConfig", "autoNoCache", "isPrerendering", "endRead", "makeHangingPromise", "renderSignal", "forceStatic", "markCurrentScopeAsDynamic", "isCacheableRevalidate", "useCacheOrRequestStore", "serverComponentsHmrCache", "fetchIdx", "handleUnlock", "doOriginalFetch", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "_ogBody", "otherInput", "clonedInit", "fetchType", "cacheStatus", "bodyBuffer", "arrayBuffer", "fetchedData", "FETCH", "data", "catch", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "softTags", "waitAtLeastOneReactRenderTask", "isRevalidate", "pendingRevalidates", "pendingRevalidate", "hasNextConfig", "pendingRevalidateKey", "revalidatedResult", "pendingResponse", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "unstable_cache", "noStoreFetchIdx", "cacheNewResult", "cb", "keyParts", "fixedKey", "cachedCb", "maybeIncrementalCache", "__incrementalCache", "requestStore", "URLSearchParams", "sortedSearchKeys", "keys", "sort", "a", "b", "localeCompare", "sortedSearch", "invocation<PERSON><PERSON>", "innerCacheStore", "phase", "draftMode", "getDraftModeProviderForCacheScope", "isNestedUnstableCache", "parse", "run", "getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "smoosh", "_smoosh", "childrenPaths", "children", "slug<PERSON><PERSON>", "splice", "restSlugName", "optionalRestSlugName", "routes", "c", "reduce", "prev", "curr", "placeholder", "r", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "nextSegment", "segmentName", "isOptional", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "has", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "sorted", "INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "find", "m", "interceptingRoute", "marker", "interceptedRoute", "normalizeAppPath", "concat", "splitInterceptingRoute", "isDynamicRoute", "TEST_ROUTE", "TEST_STRICT_ROUTE", "strict", "revalidatePath", "revalidateTag", "unstable_expirePath", "unstable_expireTag", "originalPath", "normalizedPath", "expression", "store", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "dynamicTracking", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "pendingRevalidatedTags", "pathWasRevalidated", "unstable_noStore", "callingExpression", "cacheLife", "validateCacheLife", "profile", "stale", "__NEXT_USE_CACHE", "cacheLifeProfiles", "configuredProfile", "trim", "explicitRevalidate", "explicitExpire", "explicitStale", "cacheTag"], "mappings": "wIAcaA,aAAa,CAAA,kBAAbA,GAuCAC,aAAa,CAAA,kBAAbA,GAnBAC,cAAc,CAAA,kBAAdA,GAiBAC,cAAc,CAAA,kBAAdA,GAsCAC,mBAAmB,CAAA,kBAAnBA,GAfAC,qBAAqB,CAAA,kBAArBA,GASAC,2BAA2B,CAAA,kBAA3BA,GAPAC,sBAAsB,CAAA,kBAAtBA,GArCAC,cAAc,CAAA,kBAAdA,GAOAC,6BAA6B,CAAA,kBAA7BA,GAzCAC,mBAAmB,CAAA,kBAAnBA,GAqCAC,mBAAmB,CAAA,kBAAnBA,GACAC,0BAA0B,CAAA,kBAA1BA,GA1BAC,gBAAgB,CAAA,kBAAhBA,GAcAC,0BAA0B,CAAA,kBAA1BA,GAXAC,kCAAkC,CAAA,kBAAlCA,GACAC,sCAAsC,CAAA,kBAAtCA,GASAC,8BAA8B,CAAA,kBAA9BA,GAXAC,sBAAsB,CAAA,kBAAtBA,GASAC,wBAAwB,CAAA,kBAAxBA,GACAC,yBAAyB,CAAA,kBAAzBA,GAdAC,gBAAgB,CAAA,kBAAhBA,GAZAC,+BAA+B,CAAA,kBAA/BA,GAaAC,gBAAgB,CAAA,kBAAhBA,GAdAC,uBAAuB,CAAA,kBAAvBA,GAsBAC,kBAAkB,CAAA,kBAAlBA,GA6DAC,qBAAqB,CAAA,kBAArBA,GAnCAC,eAAe,CAAA,kBAAfA,GA5CAC,2BAA2B,CAAA,kBAA3BA,GACAC,0CAA0C,CAAA,kBAA1CA,GAuDAC,8BAA8B,CAAA,kBAA9BA,GAVAC,cAAc,CAAA,kBAAdA,GAOAC,+BAA+B,CAAA,kBAA/BA,GADAC,2BAA2B,CAAA,kBAA3BA,GAFAC,sBAAsB,CAAA,kBAAtBA,GADAC,yBAAyB,CAAA,kBAAzBA,GAEAC,uBAAuB,CAAA,kBAAvBA,GAHAC,uBAAuB,CAAA,kBAAvBA,GA5CAC,mBAAmB,CAAA,kBAAnBA,GACAC,uBAAuB,CAAA,kBAAvBA,GACAC,kBAAkB,CAAA,kBAAlBA,GACAC,UAAU,CAAA,kBAAVA,GA2DAC,yBAAyB,CAAA,kBAAzBA,GANAC,oCAAoC,CAAA,kBAApCA,GAEAC,yBAAyB,CAAA,kBAAzBA,GAuBAC,cAAc,CAAA,kBAAdA,GAJAC,yBAAyB,CAAA,kBAAzBA,GAvBAC,8BAA8B,CAAA,kBAA9BA,GAMAC,0CAA0C,CAAA,kBAA1CA,GASAC,gCAAgC,CAAA,kBAAhCA,GAiIJC,cAAc,CAAA,kBAAdA,IAAgBC,wBAAwB,CAAA,kBAAxBA,wEA9MlB,IAAM3B,EAA0B,OAC1BF,EAAkC,OAElCZ,EAAsB,iBACtBkB,EAA8B,yBAC9BC,EACX,sCAEWS,EAAsB,gBACtBC,EAA0B,YAC1BC,EAAqB,eACrBC,EAAa,OACbzC,EAAgB,UAChBqB,EAAmB,QACnBE,EAAmB,QACnBV,EAAmB,QAEnBK,EAAyB,oBACzBH,EAAqC,0BACrCC,EACX,8BAEWS,EAAqB,cAIrBN,EAA2B,IAC3BC,EAA4B,IAC5BH,EAAiC,KACjCH,EAA6B,QAG7BZ,EAAiB,QAKjBM,EAAiB,WAGjBG,EAAsB,aACtBC,EAA6B,CAAC,SAAS,EAAED,EAAAA,CAAqB,CAG9DF,EAAgC,kBAIhCkB,EAAkB,qBAClBxB,EAAiB,mBACjB4B,EAAiB,wBACjB9B,EAAgB,uBAChBoC,EAA0B,iCAC1BF,EAA4B,mCAC5BD,EAAyB,oCACzBE,EAA0B,iCAC1BH,EAA8B,qCAC9BD,EACX,yCAEWF,EAAiC,CAAC,6KAA6K,CAAC,CAEhNiB,EAAiC,CAAC,mGAAmG,CAAC,CAEtIJ,EAAuC,CAAC,uFAAuF,CAAC,CAEhIC,EAA4B,CAAC,sHAAsH,CAAC,CAEpJI,EAA6C,CAAC,uGAAuG,CAAC,CAEtJN,EAA4B,CAAC,uHAE7BrC,AAFoJ,CAAC,CAGhK,6FACWE,EACX,iGAEW0C,EACX,uEACA,8BAEW3C,EAA8B,CAAC,wJAAwJ,CAAC,CAExLoB,EAAwB,CAAC,iNAAiN,CAAC,CAE3OoB,EAA4B,CAAC,sJAE7B1C,EAFqL,AAE/J,CAFgK,AAE/J,MAAO,QAAS,aAAc,MAAO,MAAM,CAElEyC,EAAgD,CAC3DO,KAAM,OACNC,iBAAkB,oBAClBC,OAAQ,QACV,EAMMC,EAAuB,CAI3BC,OAAQ,SAKRC,sBAAuB,MAIvBC,oBAAqB,MAIrBC,cAAe,iBAIfC,QAAS,WAITC,QAAS,WAITC,WAAY,aAIZC,WAAY,aAIZC,UAAW,aAIXC,gBAAiB,oBAIjBC,gBAAiB,oBAIjBC,aAAc,iBAIdC,aAAc,gBAChB,EAKMlB,GAAiB,CACrB,GAAGK,CAAoB,CACvBc,MAAO,CACLC,aAAc,CACZf,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CACnC,CACDY,WAAY,CACVhB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDU,cAAe,CAEbjB,EAAqBK,OAAO,CAC5BL,EAAqBM,OAAO,CAC7B,CACDY,WAAY,CACVlB,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACrC,CACDS,QAAS,CACPnB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBC,MAAM,CAC3BD,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDa,SAAU,CAERpB,EAAqBE,qBAAqB,CAC1CF,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBI,aAAa,CAEtC,AADG,CAEL,EAEMR,GAA2B,CAC/ByB,aAAc,0BACdC,SAAU,oBACVC,cAAe,0BACfC,kBAAmB,8BACrB,sDCnMC,aACM,SAASC,EAAcC,CAAkB,EAG9C,GAAI,CAACA,EAASC,IAAI,CAChB,CADkB,KACX,CAACD,EAAUA,EAAS,CAG7B,GAAM,CAACE,EAAOC,EAAM,CAAGH,EAASC,IAAI,CAACG,GAAG,GAElCC,EAAU,IAAIC,SAASJ,EAAO,CAClCK,OAAQP,EAASO,MAAM,CACvBC,WAAYR,EAASQ,UAAU,CAC/BC,QAAST,EAASS,OAAO,AAC3B,GAEAC,OAAOC,cAAc,CAACN,EAAS,MAAO,CACpCO,MAAOZ,EAASa,GAAG,AACrB,GAEA,IAAMC,EAAU,IAAIR,SAASH,EAAO,CAClCI,OAAQP,EAASO,MAAM,CACvBC,WAAYR,EAASQ,UAAU,CAC/BC,QAAST,EAASS,OAAO,AAC3B,GAMA,OAJAC,OAAOC,cAAc,CAACG,EAAS,MAAO,CACpCF,MAAOZ,EAASa,GAClB,AADqB,GAGd,CAACR,EAASS,EAAQ,AAC3B,0EA9BgBf,gBAAAA,qCAAAA,yDCVf,sFAgCegB,oBAAAA,qCAAAA,odA/BO,CAAA,CAAA,IAAA,SACO,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,mIA6BxB,SAASA,EAAkBgB,CAA2B,EAC3D,IAAMC,EAAkBC,EAAMC,KAAK,CACjC,AACCrB,GAA8B,EAAE,EAGnC,OAAO,SAASsB,AACdC,CAA2B,CAC3BC,CAAqB,MAajBxB,EACA0B,EAZJ,GAAIF,GAAWA,EAAQC,MAAM,CAQ3B,CAR6B,MAQtBP,EAAcK,EAAUC,GAKjC,GAAwB,UAApB,EAAgC,CArBiD,IAqB1ED,GAA0BC,EAI9B,CAKL,IAAMnB,EATsC,AAU1C,AAAoB,iBAAbkB,GAAyBA,aAAoBI,IAChD,IAAIC,QAAQL,EAAUC,GACtBD,EACN,GACGlB,AAAmB,UAAXG,MAAM,EAAiC,SAAnBH,EAAQG,MAAM,EAC3CH,EAAQwB,SAAS,CAMjB,CALA,MAKOX,EAAcK,EAAUC,GAEjCE,EAhEGpB,KAAKC,IAgEGH,KAhEM,CAAC,CAgEUC,AA/D9BA,EAAQG,MAAM,CACdC,MAAMC,IAAI,CAACL,EAAQT,OAAO,CAACe,OAAO,IAClCN,EAAQO,IAAI,CACZP,EAAQQ,QAAQ,CAChBR,EAAQS,WAAW,CACnBT,EAAQU,QAAQ,CAChBV,EAAQW,cAAc,CACtBX,EAAQY,SAAS,CAClB,EAwDGjB,EAAMK,EAAQL,GAAG,AACnB,MAvBE0B,EApDiB,SAoDNvB,sCApDqD,AAqDhEH,EAAMuB,EAwBR,IAAMO,EAAeX,EAAgBnB,GACrC,IAAK,IAAI+B,EAAI,EAAGC,EAAIF,EAAaG,MAAM,CAAEF,EAAIC,EAAGD,GAAK,EAAG,CACtD,EA/EkH,CA+E5G,CAACG,EAAKC,EAAQ,CAAGL,CAAY,CAACC,EAAE,CACtC,GAAIG,IAAQR,EACV,OAAOS,CADa,CACLC,IAAI,CAAC,KAClB,IAAMC,EAAWP,CAAY,CAACC,EAAE,CAAC,EAAE,CACnC,GAAI,CAACM,EAAU,MAAM,OAAA,cAAwC,CAAxC,IAAIC,EAAAA,cAAc,CAAC,sBAAnB,oBAAA,OAAA,mBAAA,gBAAA,CAAuC,GAM5D,GAAM,CAAC9C,EAASS,EAAQ,CAAGf,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACmD,GAEzC,OADAP,CAAY,CAACC,EAAE,CAAC,EAAE,CAAG9B,EACdT,CACT,EAEJ,CAIA,IAAM2C,EAAUjB,EAAcK,EAAUC,GAClCe,EAAoB,CAACb,EAAUS,EAAS,KAAK,CAGnD,OAFAL,EAAaU,IAAI,CAACD,GAEXJ,EAAQC,IAAI,CAAC,AAACC,IAKnB,GAAM,CAAC7C,EAASS,EAAQ,CAAGf,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACmD,GAEzC,OADAE,CAAK,CAAC,EAAE,CAAGtC,EACJT,CACT,EACF,CACF,wCCxEkBiD,EAkJAC,aAlJAD,OAkJAC,kEAlJAD,eAAe,CAAA,kBAAfA,GAkJAC,oBAAoB,CAAA,kBAApBA,uEAlJX,IAAWD,kBAAAA,WAAAA,GAAAA,4FAAAA,GAkJAC,uBAAAA,WAAAA,GAAAA,iEAAAA,6IC3KLC,UAAAA,qCAAAA,aApBmB,CAAA,CAAA,IAAA,GAoBzB,OAAMA,EAGX,YACmBC,CAA6B,CAM7BC,CALjB,CAKkD,AAACC,GAAOA,GAAI,CAC9D,MAPiBF,UAAAA,CAAAA,OAMAC,WAAAA,CAAAA,OATFE,OAAAA,CAAU,IAAIC,GAU5B,CAcH,OAAcC,OACZzB,CAA8B,CACZ,CAClB,OAAO,IAAImB,EAAiBnB,MAAAA,EAAAA,KAAAA,EAAAA,EAASoB,UAAU,CAAEpB,MAAAA,EAAAA,KAAAA,EAAAA,EAASqB,WAAW,CACvE,CAYA,MAAaK,MAAMhB,CAAM,CAAEY,CAAgB,CAAc,CACvD,IAAMpB,EAAY,IAAI,CAACkB,UAAU,CAAG,MAAM,IAAI,CAACA,UAAU,CAACV,GAAOA,EACjE,GAAiB,MAAM,CAAnBR,EACF,OAAOoB,EAAGpB,EAAUyB,QAAQC,OAAO,EAGrC,IAAML,EAAU,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC3B,GACjC,GAAIqB,EAAS,OAAOA,EAEpB,GAAM,SAAEZ,CAAO,SAAEiB,CAAO,QAAEE,CAAM,CAAE,CAAG,IAAIC,EAAAA,eAAe,CAiBxD,OAhBA,IAAI,CAACR,OAAO,CAACS,GAAG,CAAC9B,EAAUS,GAE3B,IAAI,CAACU,WAAW,CAAC,UACf,GAAI,CACF,IAAMY,EAAS,MAAMX,EAAGpB,EAAU0B,GAIlCA,EAAQK,EACV,CAAE,MAAOC,EAAK,CACZJ,EAAOI,EACT,QAAU,CACR,IAAI,CAACX,OAAO,CAACY,MAAM,CAACjC,EACtB,CACF,GAEOS,CACT,CACF,uDC3F+B,qEAWlByB,iBAAiB,CAAA,kBAAjBA,GA4NGC,cAAc,CAAA,kBAAdA,GA5BAC,cAAc,CAAA,kBAAdA,GA6CAC,iBAAiB,CAAA,kBAAjBA,GA9BAC,cAAc,CAAA,kBAAdA,uEA/MT,IAAMJ,EAAoBK,OAAOC,GAAG,CAAC,2BAgMrC,SAASJ,EACdK,CAAwB,CACxBjC,CAAO,EAEP,IAAMkC,EAAOD,CAAG,CAACP,EAAkB,EAAI,CAAC,EACxC,MAAsB,UAAf,OAAO1B,EAAmBkC,CAAI,CAAClC,EAAI,CAAGkC,CAC/C,CASO,SAASJ,EAAeG,CAAwB,CAAEC,CAAiB,EAExE,OADAD,CAAG,CAACP,EAAkB,CAAGQ,EAClBA,CACT,CAUO,SAASP,EACdxD,CAA4B,CAC5B6B,CAAM,CACNnC,CAAqB,EAErB,IAAMqE,EAAON,EAAezD,GAE5B,OADA+D,CAAI,CAAClC,EAAI,CAAGnC,EACLiE,EAAe3D,EAAS+D,EACjC,CASO,SAASL,EACd1D,CAA4B,CAC5B6B,CAAM,EAEN,IAAMkC,EAAON,EAAezD,GAE5B,OADA,OAAO+D,CAAI,CAAClC,EAAI,CACT8B,EAAe3D,EAAS+D,EACjC,4HChPgBC,2BAA2B,CAAA,kBAA3BA,GA8IAC,uBAAuB,CAAA,kBAAvBA,GAlHAC,kBAAkB,CAAA,kBAAlBA,GAyEAC,yBAAyB,CAAA,kBAAzBA,GAwBAC,WAAW,CAAA,kBAAXA,+EAxIT,CAAA,CAAA,IAAA,IAWA,SAASJ,EACdK,CAAgC,EAEhC,IAAM9E,EAAU,IAAI+E,QACpB,IAAK,GAAI,CAACzC,EAAKnC,EAAM,GAAIF,OAAOc,OAAO,CAAC+D,GAEtC,IAAK,IAAII,EAF2C,GACrCrE,AACDmE,MADOC,CACC,MADM,CAAC9E,GAASA,EAAQ,CAACA,EAAM,MAElC,IAAN+E,IACM,KADa,KACH,AAAvB,OAAOA,IACTA,EAAIA,EAAEC,QAAQ,EAAA,EAGhBnF,EAAQoF,MAAM,CAAC9C,EAAK4C,IAGxB,OAAOlF,CACT,CAYO,SAAS2E,EAAmBU,CAAqB,EACtD,IAEIG,EACAC,EACAC,EACAC,EACAC,EANAN,EAAiB,EAAE,CACnBC,EAAM,EAOV,SAASM,IACP,KAAON,EAAMF,EAAchD,MAAM,EAAI,KAAKyD,IAAI,CAACT,EAAcU,MAAM,CAACR,KAClEA,CADyE,EAClE,EAET,OAAOA,EAAMF,EAAchD,MAAM,AACnC,CAQA,KAAOkD,EAAMF,EAAchD,MAAM,EAAE,CAIjC,IAHAmD,EAAQD,EACRK,GAAwB,EAEjBC,KAEL,GAAIJ,AAAO,MADXA,GADuB,AAClBJ,EAAcU,MAAM,CAACR,EAAAA,EACV,CAQd,IANAG,EAAYH,EACZA,GAAO,EAEPM,IACAF,EAAYJ,EAELA,EAAMF,EAAchD,MAAM,EAjB9BoD,AAAO,EAiB2BO,KAnBzCP,EAAKJ,EAAcU,MAAM,CAACR,CAmBiC,CAnBjCA,GAEE,MAAPE,GAAqB,MAAPA,GAkB7BF,GAAO,EAILA,EAAMF,EAAchD,MAAM,EAAkC,KAAK,CAAnCgD,EAAcU,MAAM,CAACR,IAErDK,EAAwB,GAExBL,EAAMI,EACNL,EAAe1C,IAAI,CAACyC,EAAcY,SAAS,CAACT,EAAOE,IACnDF,EAAQD,GAIRA,EAAMG,EAAY,CAEtB,MACEH,CADK,EACE,GAIP,CAACK,GAAyBL,GAAOF,EAAchD,MAAAA,AAAM,EAAE,CACzDiD,EAAe1C,IAAI,CAACyC,EAAcY,SAAS,CAACT,EAAOH,EAAchD,MAAM,EAE3E,CAEA,OAAOiD,CACT,CASO,SAASV,EACd5E,CAAgB,EAEhB,IAAM8E,EAAmC,CAAC,EACpCoB,EAAoB,EAAE,CAC5B,GAAIlG,EACF,IAAK,GADM,AACA,CAACsC,EAAKnC,EAAM,GAAIH,EAAQe,OAAO,GAAI,AAClB,cAAc,CAApCuB,EAAI6D,WAAW,IAIjBD,EAAQtD,IAAI,IAAI+B,EAAmBxE,IACnC2E,CAAW,CAACxC,EAAI,CAAG4D,AAAmB,MAAX7D,MAAM,CAAS6D,CAAO,CAAC,EAAE,CAAGA,GAEvDpB,CAAW,CAACxC,EAAI,CAAGnC,EAIzB,OAAO2E,CACT,CAKO,SAASD,EAAYzE,CAAiB,EAC3C,GAAI,CACF,OAAOgG,OAAO,IAAIrE,IAAIqE,OAAOhG,IAC/B,CAAE,MAAOiG,EAAY,CACnB,MAAM,OAAA,cAKL,CALK,AAAIC,MACR,CAAC,kBAAkB,EAAEF,OACnBhG,GACA,4FAA4F,CAAC,CAC/F,CAAEmG,MAAOF,CAAM,GAJX,oBAAA,OAAA,kBAAA,gBAAA,CAKN,EACF,CACF,CAMO,SAAS3B,EAAwBpC,CAAW,EAEjD,IAAK,IAAMmE,IADM,CAAC3K,EAAAA,GACG0K,SAAU,WADU,CAAE5K,EAAAA,+BAA+B,CAAC,CAEzE,GAAI0G,IAAQmE,GAAUnE,EAAIoE,UAAU,CAACD,GACnC,MAD4C,CACrCnE,EAAI2D,SAAS,CAACQ,EAAOpE,MAAM,EAGtC,OAAO,IACT,mECnKO,SAASsE,EACdC,CAAqC,CACrCC,CAAiB,CACjBC,CAAuB,EAEvB,GAAKF,CAAD,CAMJ,IAAK,IAAMG,GANO,EAEdD,IACFA,EAAiBA,EAAeX,QADd,GACyB,EAAA,EAG1BS,GAAa,KAEPG,EAIrBA,EAHF,GACEF,IAFIG,CAA4B,MAAXD,EAERC,CAFQD,EAAKE,MAAAA,AAAM,EAAA,KAAA,EAAXF,EAAaG,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACf,WAAW,EAAA,GAG9DW,IAAmBC,EAAKI,aAAa,CAAChB,WAAW,IAAA,CACrC,CADqC,MACjDY,EAAAA,EAAKK,OAAAA,AAAO,EAAA,KAAA,EAAZL,EAAcM,IAAI,CAAC,AAACC,GAAWA,EAAOnB,WAAW,KAAOW,EAAAA,CAAAA,CAExD,EADA,KACOC,CAEX,CACF,0EAtBgBJ,qBAAAA,qCAAAA,wDCIf,aACM,SAASY,EAAoBC,CAAa,EAC/C,OAAOA,EAAMC,OAAO,CAAC,MAAO,KAAO,GACrC,0EAFgBF,sBAAAA,qCAAAA,wDCHf,aACM,SAASG,EAAUC,CAAY,EACpC,IAAMC,EAAYD,EAAKE,OAAO,CAAC,KACzBC,EAAaH,EAAKE,OAAO,CAAC,KAC1BE,EAAWD,EAAa,CAAC,IAAMF,CAAAA,CAAY,GAAKE,EAAaF,CAAAA,CAAQ,QAE3E,AAAIG,GAAYH,EAAY,CAAC,EACpB,CADuB,AAE5BI,SAAUL,EAAK1B,SAAS,CAAC,EAAG8B,EAAWD,EAAaF,GACpDK,MAAOF,EACHJ,EAAK1B,SAAS,CAAC6B,EAAYF,EAAY,CAAC,EAAIA,OAAYM,GACxD,GACJC,KAAMP,EAAY,CAAC,EAAID,EAAKS,KAAK,CAACR,GAAa,EACjD,EAGK,CAAEI,SAAUL,EAAMM,MAAO,GAAIE,KAAM,EAAG,CAC/C,0EAhBgBT,YAAAA,qCAAAA,+ICCAW,gBAAAA,qCAAAA,aANU,CAAA,CAAA,IAAA,IAMnB,SAASA,EAAcV,CAAY,CAAElB,CAAe,EACzD,GAAI,CAACkB,EAAKjB,UAAU,CAAC,MAAQ,CAACD,EAC5B,MADoC,CAC7BkB,EAGT,GAAM,UAAEK,CAAQ,OAAEC,CAAK,MAAEE,CAAI,CAAE,CAAGT,CAAAA,EAAAA,EAAAA,SAAS,AAATA,EAAUC,GAC5C,MAAQ,GAAElB,EAASuB,EAAWC,EAAQE,CACxC,6ICNgBG,gBAAAA,qCAAAA,aAPU,CAAA,CAAA,IAAA,IAOnB,SAASA,EAAcX,CAAY,CAAEY,CAAe,EACzD,GAAI,CAACZ,EAAKjB,UAAU,CAAC,MAAQ,CAAC6B,EAC5B,MADoC,CAC7BZ,EAGT,GAAM,UAAEK,CAAQ,OAAEC,CAAK,MAAEE,CAAI,CAAE,CAAGT,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAACC,GAC5C,MAAQ,GAAEK,EAAWO,EAASN,EAAQE,CACxC,6ICLgBK,gBAAAA,qCAAAA,aATU,CAAA,CAAA,IAAA,IASnB,SAASA,EAAcb,CAAY,CAAElB,CAAc,EACxD,GAAoB,UAAhB,AAA0B,OAAnBkB,EACT,OAAO,EAGT,GAAM,UAAEK,CAAQ,CAAE,CAAGN,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAACC,GAC/B,OAAOK,IAAavB,GAAUuB,EAAStB,UAAU,CAACD,EAAS,IAC7D,6ICRgBgC,YAAAA,qCAAAA,aARc,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,IAOvB,SAASA,EACdd,CAAY,CACZL,CAAuB,CACvBH,CAAsB,CACtBuB,CAAsB,EAItB,GAAI,CAACpB,GAAUA,IAAWH,EAAe,OAAOQ,EAEhD,IAAMgB,EAAQhB,EAAKxB,WAAW,SAI9B,AAAI,CAACuC,IACCF,CAAAA,EAAAA,EAAAA,KADa,QACbA,AAAa,EAACG,EAAO,SAAS,AAC9BH,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACG,EAAQ,IAAGrB,EAAOnB,WAAW,KADNwB,AACa,EAIjDU,GAAAA,EAJwDV,AAIxDU,aAAAA,AAAa,EAACV,EAAO,IAAGL,EACjC,6IClBgBsB,yBAAAA,qCAAAA,aAVoB,CAAA,CAAA,IAAA,QACN,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,IAOnB,SAASA,EAAuBC,CAAkB,EACvD,IAAIb,EAAWS,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EACtBI,EAAKb,QAAQ,CACba,EAAKvB,MAAM,CACXuB,EAAKC,OAAO,MAAGZ,EAAYW,EAAK1B,aAAa,CAC7C0B,EAAKH,YAAY,EAenB,OAZIG,EAAKC,OAAO,EAAI,CAACD,EAAKE,aAAAA,AAAa,EAAE,EACvCf,EAAWT,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACS,EAAAA,EAG7Ba,EAAKC,OAAO,EAAE,CAChBd,EAAWM,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EACtBD,CAAAA,EAAAA,EAAAA,aAAa,AAAbA,EAAcL,EAAW,eAAca,EAAKC,OAAO,EACjC,MAAlBD,EAAKb,QAAQ,CAAW,aAAe,QAAA,EAI3CA,EAAWK,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACL,EAAUa,EAAKG,QAAQ,EACzC,CAACH,EAAKC,OAAO,EAAID,EAAKE,aAAa,CACtC,AAACf,EAASiB,QAAQ,CAAC,KAEjBjB,EADAM,CAAAA,EAAAA,EAAAA,aAAa,AAAbA,EAAcN,EAAU,KAE1BT,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACS,EAC1B,mEC5BO,SAASkB,EACdC,CAAoC,CACpCnJ,CAA6B,EAI7B,IAAI6G,EACJ,GAAI7G,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASoJ,IAAAA,AAAI,GAAI,CAACvI,MAAMoE,OAAO,CAACjF,EAAQoJ,IAAI,EAC9CvC,CADiD,CACtC7G,EAAQoJ,IAAI,CAACjE,QAAQ,GAAG+B,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,MAC9C,IAAIiC,EAAOtC,QAAQ,CAEnB,CAFqB,MAC1BA,EAAWsC,EAAOtC,QAAQ,CAG5B,OAAOA,EAASV,WAAW,EAC7B,0EAdgB+C,cAAAA,qCAAAA,+ICaAG,sBAAAA,qCAAAA,KAXhB,IAAM5H,EAAQ,IAAI6H,QAWX,SAASD,EACdrB,CAAgB,CAChBZ,CAA2B,MAYvBN,EATJ,GAAI,CAACM,EAAS,MAAO,UAAEY,CAAS,EAGhC,IAAIuB,EAAoB9H,EAAMgC,GAAG,CAAC2D,GAC7BmC,IACHA,EAAoBnC,EAAQoC,GAAG,CAAC,AAAClC,GAAWA,EAAOnB,EAD7B,SACwC,IAC9D1E,EAAMmC,GAAG,CAACwD,EAASmC,IAOrB,IAAME,EAAWzB,EAASd,KAAK,CAAC,IAAK,GAIrC,GAAI,CAACuC,CAAQ,CAAC,EAAE,CAAE,MAAO,UAAEzB,CAAS,EAGpC,IAAM0B,EAAUD,CAAQ,CAAC,EAAE,CAACtD,WAAW,GAIjCwD,EAAQJ,EAAkB1B,OAAO,CAAC6B,UACxC,AAAIC,EAAQ,EAAU,CAAP,SAAS3B,CAAS,GAGjClB,EAAiBM,CAAO,CAACuC,EAAM,CAKxB,CAAE3B,SAFTA,EAAWA,EAASI,KAAK,CAACtB,EAAezE,MAAM,CAAG,IAAM,mBAErCyE,CAAe,EACpC,6IClDgB8C,mBAAAA,qCAAAA,aAVc,CAAA,CAAA,IAAA,IAUvB,SAASA,EAAiBjC,CAAY,CAAElB,CAAc,EAa3D,GAAI,CAAC+B,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACb,EAAMlB,GACvB,MADgC,CACzBkB,EAIT,IAAMkC,EAAgBlC,EAAKS,KAAK,CAAC3B,EAAOpE,MAAM,SAG9C,AAAIwH,EAAcnD,UAAU,CAAC,KACpBmD,CAD0B,CAM3B,IAAGA,CACb,6ICcgBC,sBAAAA,qCAAAA,aApDoB,CAAA,CAAA,IAAA,QACH,CAAA,CAAA,IAAA,QACH,CAAA,CAAA,IAAA,IAkDvB,SAASA,EACd9B,CAAgB,CAChBpG,CAAgB,MAE0BA,EAyCxBiC,EAzClB,GAAM,UAAEmF,CAAQ,MAAEe,CAAI,eAAEhB,CAAa,CAAE,CAAqB,AAAlBnH,OAAAA,EAAAA,EAAQoI,UAAAA,AAAU,EAAlBpI,EAAsB,CAAC,EAC3DiH,EAAyB,CAC7Bb,WACAe,cAA4B,MAAbf,EAAmBA,EAASiB,QAAQ,CAAC,KAAOF,CAC7D,EAEIC,GAAYR,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACK,EAAKb,QAAQ,CAAEgB,KAC3CH,EAAKb,IADiD,IACzC,CAAG4B,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAACf,EAAKb,QAAQ,CAAEgB,GAChDH,EAAKG,QAAQ,CAAGA,GAElB,IAAIiB,EAAuBpB,EAAKb,QAAQ,CAExC,GACEa,EAAKb,QAAQ,CAACtB,UAAU,CAAC,iBACzBmC,EAAKb,QAAQ,CAACiB,QAAQ,CAAC,SACvB,CACA,IAAMiB,EAAQrB,EAAKb,QAAQ,CACxBP,OAAO,CAAC,mBAAoB,IAC5BA,OAAO,CAAC,UAAW,IACnBP,KAAK,CAAC,IAGT2B,GAAKC,OAAO,CADIoB,CAAK,CAAC,AACPpB,EADS,CAExBmB,EACe,UAAbC,CAAK,CAAC,EAAE,CAAgB,IAAGA,EAAM9B,KAAK,CAAC,GAAG+B,IAAI,CAAC,KAAS,KAIhC,IAAtBvI,EAAQwI,AAAoB,SAAX,GACnBvB,EAAKb,QAAQ,CAAGiC,CAAAA,CAEpB,CAIA,GAAIF,EAAM,CACR,IAAIlG,EAASjC,EAAQyI,YAAY,CAC7BzI,EAAQyI,YAAY,CAACC,OAAO,CAACzB,EAAKb,QAAQ,EAC1CqB,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACR,EAAKb,QAAQ,CAAE+B,EAAK3C,OAAO,EAEnDyB,EAAKvB,MAAM,CAAGzD,EAAOiD,cAAc,CACnC+B,EAAKb,QAAQ,CAAkB,AAAfnE,OAAAA,EAAAA,EAAOmE,QAAAA,AAAQ,EAAfnE,EAAmBgF,EAAKb,QAAQ,CAE5C,CAACnE,EAAOiD,cAAc,EAAI+B,EAAKC,OAAO,EAAE,AAKtCjF,CAJJA,EAASjC,EAAQyI,YAAY,CACzBzI,EAAQyI,YAAY,CAACC,OAAO,CAACL,GAC7BZ,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACY,EAAsBF,EAAK3C,QAAO,EAE/CN,cAAc,EAAE,CACzB+B,EAAKvB,MAAM,CAAGzD,EAAOiD,cAAAA,AAAc,CAGzC,CACA,OAAO+B,CACT,6IC7Ea0B,UAAAA,qCAAAA,aA7BsB,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,QACX,CAAA,CAAA,IAAA,QACQ,CAAA,CAAA,IAAA,IAc9BC,EACJ,2FAEF,SAASC,EAASrK,CAAiB,CAAEsK,CAAmB,EACtD,OAAO,IAAI3I,IACTqE,OAAOhG,GAAKqH,OAAO,CAAC+C,EAA0B,aAC9CE,GAAQtE,OAAOsE,GAAMjD,OAAO,CAAC+C,EAA0B,aAE3D,CAEA,IAAMG,EAAWtG,OAAO,kBAEjB,OAAMkG,EAeXK,YACEC,CAAmB,CACnBC,CAAmC,CACnCC,CAAc,CACd,CACA,IAAIL,EACA9I,EAGqB,UAAtB,OAAOkJ,GAA2B,aAAcA,GAC3B,UAAtB,AACA,OADOA,GAEPJ,EAAOI,EACPlJ,EAAUmJ,GAAQ,CAAC,GAEnBnJ,EAAUmJ,GAAQD,GAAc,CAAC,EAGnC,IAAI,CAACH,EAAS,CAAG,CACfvK,IAAKqK,EAASI,EAAOH,GAAQ9I,EAAQ8I,IAAI,EACzC9I,QAASA,EACToH,SAAU,EACZ,EAEA,IAAI,CAACsB,OAAO,EACd,CAEQA,SAAU,KAcV,EAAA,EAKJ,EACA,EAAA,EAnBF,IAAMzB,EAAOiB,CAAAA,EAAAA,EAAAA,mBAAmB,AAAnBA,EAAoB,IAAI,CAACa,EAAS,CAACvK,GAAG,CAAC4H,QAAQ,CAAE,CAC5DgC,WAAY,IAAI,CAACW,EAAS,CAAC/I,OAAO,CAACoI,UAAU,CAC7CI,UAAW,CAACY,QAAQC,GAAG,CAACC,kCAAkC,CAC1Db,aAAc,IAAI,CAACM,EAAS,CAAC/I,OAAO,CAACyI,YAAY,AACnD,GAEMxD,EAAWqC,GAAAA,EAAAA,WAAW,AAAXA,EACf,IAAI,CAACyB,EAAS,CAACvK,GAAG,CAClB,IAAI,CAACuK,EAAS,CAAC/I,OAAO,CAAC5B,OAAO,EAEhC,IAAI,CAAC2K,EAAS,CAACQ,YAAY,CAAG,IAAI,CAACR,EAAS,CAAC/I,OAAO,CAACyI,YAAY,CAC7D,IAAI,CAACM,EAAS,CAAC/I,OAAO,CAACyI,YAAY,CAAC1D,kBAAkB,CAACE,GACvDF,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACiB,AADjB,MAChB,CAAiC,EAAjC,GAAiC,CAA7B,CAACgE,EAAS,CAAC/I,OAAO,CAACoI,UAAAA,AAAU,GAAA,AAAM,OAAvC,EAAA,EAAmCD,IAAAA,AAAI,EAAA,KAAA,EAAvC,EAAyCqB,OAAO,CAChDvE,GAGN,IAAMM,EACJ,CAA2B,OAA3B,EAAA,IAAI,CAACwD,EAAS,CAACQ,YAAY,AAAZA,EAAY,KAAA,EAA3B,EAA6BhE,aAAAA,AAAa,IACT,CADS,CAAA,KAC1C,AAAiC,EAAjC,GAAiC,CAA7B,CAACwD,EAAS,CAAC/I,OAAO,CAACoI,UAAAA,AAAU,GAAA,AAAM,OAAvC,EAAA,EAAmCD,IAAAA,AAAI,EAAA,KAAA,EAAvC,EAAyC5C,aAAa,EAExD,IAAI,CAACwD,EAAS,CAACvK,GAAG,CAAC4H,QAAQ,CAAGa,EAAKb,QAAQ,CAC3C,IAAI,CAAC2C,EAAS,CAACxD,aAAa,CAAGA,EAC/B,IAAI,CAACwD,EAAS,CAAC3B,QAAQ,CAAGH,EAAKG,QAAQ,EAAI,GAC3C,IAAI,CAAC2B,EAAS,CAAC7B,OAAO,CAAGD,EAAKC,OAAO,CACrC,IAAI,CAAC6B,EAAS,CAACrD,MAAM,CAAGuB,EAAKvB,MAAM,EAAIH,EACvC,IAAI,CAACwD,EAAS,CAAC5B,aAAa,CAAGF,EAAKE,aACtC,AADmD,CAG3CsC,gBAAiB,CACvB,MAAOzC,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAAC,CAC5BI,SAAU,IAAI,CAAC2B,EAAS,CAAC3B,QAAQ,CACjCF,QAAS,IAAI,CAAC6B,EAAS,CAAC7B,OAAO,CAC/B3B,cAAe,AAAC,IAAI,CAACwD,EAAS,CAAC/I,OAAO,CAAC0J,WAAW,MAE9CpD,EADA,IAAI,CAACyC,EAAS,CAACxD,aAAa,CAEhCG,OAAQ,IAAI,CAACqD,EAAS,CAACrD,MAAM,CAC7BU,SAAU,IAAI,CAAC2C,EAAS,CAACvK,GAAG,CAAC4H,QAAQ,CACrCe,cAAe,IAAI,CAAC4B,EAAS,CAAC5B,aAAa,AAC7C,EACF,CAEQwC,cAAe,CACrB,OAAO,IAAI,CAACZ,EAAS,CAACvK,GAAG,CAACoL,MAAM,AAClC,CAEA,IAAW1C,SAAU,CACnB,OAAO,IAAI,CAAC6B,EAAS,CAAC7B,OAAO,AAC/B,CAEA,IAAWA,QAAQA,CAA2B,CAAE,CAC9C,IAAI,CAAC6B,EAAS,CAAC7B,OAAO,CAAGA,CAC3B,CAEA,IAAWxB,QAAS,CAClB,OAAO,IAAI,CAACqD,EAAS,CAACrD,MAAM,EAAI,EAClC,CAEA,IAAWA,OAAOA,CAAc,CAAE,KAG7B,EAAA,EAFH,GACE,CAAC,IAAI,CAACqD,EAAS,CAACrD,MAAM,EACtB,CAAA,CAAA,AAAkC,OAAjC,AAAiC,EAAjC,GAAiC,CAA7B,CAACqD,EAAS,CAAC/I,OAAO,CAACoI,UAAAA,AAAU,GAAA,AAAM,OAAvC,EAAA,EAAmCD,IAAAA,AAAI,EAAA,KAAA,EAAvC,EAAyC3C,OAAO,CAACqE,QAAQ,CAACnE,IAE3D,GAF2DA,GAC3D,AACM,OAAA,cAEL,CAFK,AAAIoE,UACR,CAAC,8CAA8C,EAAEpE,EAAO,CAAC,CAAC,EADtD,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAI,CAACqD,EAAS,CAACrD,MAAM,CAAGA,CAC1B,CAEA,IAAIH,eAAgB,CAClB,OAAO,IAAI,CAACwD,EAAS,CAACxD,aAAa,AACrC,CAEA,IAAIgE,cAAe,CACjB,OAAO,IAAI,CAACR,EAAS,CAACQ,YAAY,AACpC,CAEA,IAAIQ,cAAe,CACjB,OAAO,IAAI,CAAChB,EAAS,CAACvK,GAAG,CAACuL,YAAY,AACxC,CAEA,IAAIvC,MAAO,CACT,OAAO,IAAI,CAACuB,EAAS,CAACvK,GAAG,CAACgJ,IAAI,AAChC,CAEA,IAAIA,KAAKjJ,CAAa,CAAE,CACtB,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAACgJ,IAAI,CAAGjJ,CAC5B,CAEA,IAAI0G,UAAW,CACb,OAAO,IAAI,CAAC8D,EAAS,CAACvK,GAAG,CAACyG,QAAQ,AACpC,CAEA,IAAIA,SAAS1G,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAACyG,QAAQ,CAAG1G,CAChC,CAEA,IAAIyL,MAAO,CACT,OAAO,IAAI,CAACjB,EAAS,CAACvK,GAAG,CAACwL,IAAI,AAChC,CAEA,IAAIA,KAAKzL,CAAa,CAAE,CACtB,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAACwL,IAAI,CAAGzL,CAC5B,CAEA,IAAI0L,UAAW,CACb,OAAO,IAAI,CAAClB,EAAS,CAACvK,GAAG,CAACyL,QAAQ,AACpC,CAEA,IAAIA,SAAS1L,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAACyL,QAAQ,CAAG1L,CAChC,CAEA,IAAI2L,MAAO,CACT,IAAM9D,EAAW,IAAI,CAACqD,cAAc,GAC9BG,EAAS,IAAI,CAACD,YAAY,GAChC,MAAO,CAAA,EAAG,IAAI,CAACM,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACzC,IAAI,CAAA,EAAGpB,EAAAA,EAAWwD,EAAAA,EAAS,IAAI,CAACrD,IAAI,CAAA,CAAE,AACzE,CAEA,IAAI2D,KAAK1L,CAAW,CAAE,CACpB,IAAI,CAACuK,EAAS,CAACvK,GAAG,CAAGqK,EAASrK,GAC9B,IAAI,CAACkK,OAAO,EACd,CAEA,IAAIyB,QAAS,CACX,OAAO,IAAI,CAACpB,EAAS,CAACvK,GAAG,CAAC2L,MAAM,AAClC,CAEA,IAAI/D,UAAW,CACb,OAAO,IAAI,CAAC2C,EAAS,CAACvK,GAAG,CAAC4H,QAAQ,AACpC,CAEA,IAAIA,SAAS7H,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAAC4H,QAAQ,CAAG7H,CAChC,CAEA,IAAIgI,MAAO,CACT,OAAO,IAAI,CAACwC,EAAS,CAACvK,GAAG,CAAC+H,IAAI,AAChC,CAEA,IAAIA,KAAKhI,CAAa,CAAE,CACtB,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAAC+H,IAAI,CAAGhI,CAC5B,CAEA,IAAIqL,QAAS,CACX,OAAO,IAAI,CAACb,EAAS,CAACvK,GAAG,CAACoL,MAAM,AAClC,CAEA,IAAIA,OAAOrL,CAAa,CAAE,CACxB,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAACoL,MAAM,CAAGrL,CAC9B,CAEA,IAAI6L,UAAW,CACb,OAAO,IAAI,CAACrB,EAAS,CAACvK,GAAG,CAAC4L,QAAQ,AACpC,CAEA,IAAIA,SAAS7L,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAAC4L,QAAQ,CAAG7L,CAChC,CAEA,IAAI8L,UAAW,CACb,OAAO,IAAI,CAACtB,EAAS,CAACvK,GAAG,CAAC6L,QAAQ,AACpC,CAEA,IAAIA,SAAS9L,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAACvK,GAAG,CAAC6L,QAAQ,CAAG9L,CAChC,CAEA,IAAI6I,UAAW,CACb,OAAO,IAAI,CAAC2B,EAAS,CAAC3B,QAAQ,AAChC,CAEA,IAAIA,SAAS7I,CAAa,CAAE,CAC1B,IAAI,CAACwK,EAAS,CAAC3B,QAAQ,CAAG7I,EAAMuG,UAAU,CAAC,KAAOvG,EAAQ,CAAC,CAAC,EAAEA,EAAAA,CAAO,AACvE,CAEAgF,UAAW,CACT,OAAO,IAAI,CAAC2G,IAAI,AAClB,CAEAI,QAAS,CACP,OAAO,IAAI,CAACJ,IAAI,AAClB,CAEA,CAACzH,OAAOC,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CACLwH,KAAM,IAAI,CAACA,IAAI,CACfC,OAAQ,IAAI,CAACA,MAAM,CACnBF,SAAU,IAAI,CAACA,QAAQ,CACvBI,SAAU,IAAI,CAACA,QAAQ,CACvBD,SAAU,IAAI,CAACA,QAAQ,CACvB5C,KAAM,IAAI,CAACA,IAAI,CACfvC,SAAU,IAAI,CAACA,QAAQ,CACvB+E,KAAM,IAAI,CAACA,IAAI,CACf5D,SAAU,IAAI,CAACA,QAAQ,CACvBwD,OAAQ,IAAI,CAACA,MAAM,CACnBG,aAAc,IAAI,CAACA,YAAY,CAC/BxD,KAAM,IAAI,CAACA,IAAI,AACjB,CACF,CAEAgE,OAAQ,CACN,OAAO,IAAI5B,EAAQnE,OAAO,IAAI,EAAG,IAAI,CAACuE,EAAS,CAAC/I,OAAO,CACzD,CACF,0HC1RawK,kBAAkB,CAAA,kBAAlBA,GAaAC,gBAAgB,CAAA,kBAAhBA,GAQAC,cAAc,CAAA,kBAAdA,sEArBN,OAAMF,UAA2B9F,MACtCsE,YAAY,MAAE2B,CAAI,CAAoB,CAAE,CACtC,KAAK,CAAC,CAAC,gBAAgB,EAAEA,EAAK;;;;;;;EAOhC,CAAC,CACD,CACF,CAEO,MAAMF,UAAyB/F,MACpCsE,aAAc,CACZ,KAAK,CAAC,CAAC;;EAET,CAAC,CACD,CACF,CAEO,MAAM0B,UAAuBhG,MAClCsE,aAAc,CACZ,KAAK,CAAC,CAAC;;EAET,CAAC,CACD,CACF,4HCrBa4B,SAAS,CAAA,kBAATA,GAOAC,WAAW,CAAA,kBAAXA,+EAZW,CAAA,CAAA,IAAA,QAC+B,CAAA,CAAA,IAAA,QACN,CAAA,CAAA,IAAA,MAClB,CAAA,CAAA,IAAA,IAElBD,EAAYnI,OAAO,mBAOzB,OAAMoI,UAAoBzK,QAO/B4I,YAAYC,CAAwB,CAAE6B,EAAoB,CAAC,CAAC,CAAE,CAC5D,IAAMtM,EACa,UAAjB,OAAOyK,GAAsB,QAASA,EAAQA,EAAMzK,GAAG,CAAGgG,OAAOyE,GAEnEhG,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACzE,GAMqB,AAC3BsM,EAAKlN,CAD8B,GAC1B,EAAIkN,AAAgB,QAAQ,GAAnBE,MAAM,GAC1BF,EAAKE,MAAM,CAAG,MAAA,EAId/B,aAAiB7I,QAAS,KAAK,CAAC6I,EAAO6B,GACtC,KAAK,CAACtM,EAAKsM,GAEhB,IAAMG,EAAU,IAAItC,EAAAA,OAAO,CAACnK,EAAK,CAC/BJ,QAAS4E,CAAAA,EAAAA,EAAAA,yBAAyB,AAAzBA,EAA0B,IAAI,CAAC5E,OAAO,EAC/CgK,WAAY0C,EAAK1C,UAAU,AAC7B,GACA,IAAI,CAACwC,EAAU,CAAG,CAChBtG,QAAS,IAAI4G,EAAAA,cAAc,CAAC,IAAI,CAAC9M,OAAO,EACxC6M,UACAzM,IAAK4K,QAAQC,GAAG,CAACC,kCAAkC,CAC/C9K,EACAyM,EAAQ1H,QAAQ,EACtB,CACF,CAEA,CAACd,OAAOC,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CACL4B,QAAS,IAAI,CAACA,OAAO,CACrB2G,QAAS,IAAI,CAACA,OAAO,CACrBzM,IAAK,IAAI,CAACA,GAAG,CAEb2M,SAAU,IAAI,CAACA,QAAQ,CACvBtL,MAAO,IAAI,CAACA,KAAK,CACjBP,YAAa,IAAI,CAACA,WAAW,CAC7B8L,YAAa,IAAI,CAACA,WAAW,CAC7BhN,QAASC,OAAOgN,WAAW,CAAC,IAAI,CAACjN,OAAO,EACxCqB,UAAW,IAAI,CAACA,SAAS,CACzBY,UAAW,IAAI,CAACA,SAAS,CACzBrB,OAAQ,IAAI,CAACA,MAAM,CACnBI,KAAM,IAAI,CAACA,IAAI,CACfC,SAAU,IAAI,CAACA,QAAQ,CACvBE,SAAU,IAAI,CAACA,QAAQ,CACvBC,eAAgB,IAAI,CAACA,cAAc,CACnCS,OAAQ,IAAI,CAACA,MAAM,AACrB,CACF,CAEA,IAAWqE,SAAU,CACnB,OAAO,IAAI,CAACsG,EAAU,CAACtG,OACzB,AADgC,CAGhC,IAAW2G,SAAU,CACnB,OAAO,IAAI,CAACL,EAAU,CAACK,OAAO,AAChC,CAOA,IAAWN,MAAO,CAChB,MAAM,IAAIF,EAAAA,gBAAgB,AAC5B,CAOA,IAAWa,IAAK,CACd,MAAM,IAAIZ,EAAAA,cACZ,AAD0B,CAG1B,IAAWlM,KAAM,CACf,OAAO,IAAI,CAACoM,EAAU,CAACpM,GACzB,AAD4B,CAE9B,4HCnEa+M,iBAAiB,CAAA,kBAAjBA,GAUAC,kBAAkB,CAAA,kBAAlBA,GA7BAC,gBAAgB,CAAA,kBAAhBA,GASAC,iBAAiB,CAAA,kBAAjBA,uEATN,IAAMD,EAAmB,AAAC9I,IAC/ByG,EAQWsC,EAAoB,AAC/BC,IATQtC,AAUmBD,EAQhBmC,CAlBA,CAkBoB,AAC/B5I,AAnBYoI,IAoBe3B,AAVQC,EAkBxBmC,CAlB2B,CAACT,AAkBP,AAChCY,IATmCtC,AAUPD,AA9BJ,GAoBc,CAAC2B,IAVY,AAoBf1B,GAAG,CAAC0B,IAVW,OApBtB,CA8BuB,OApBI,QAUA,QAUC,6ECO9Ca,kBAAkB,CAAA,kBAAlBA,GA5CAC,eAAe,CAAA,kBAAfA,GADAC,mBAAmB,CAAA,kBAAnBA,GAYGC,qBAAqB,CAAA,kBAArBA,GAuBAC,sBAAsB,CAAA,kBAAtBA,+EAxCe,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,QAChB,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,IAEvCF,EAAsB,iBAC5B,OAAMD,UAAwBnH,wBAA9B,KAAA,IAAA,GAAA,IAAA,CACWuH,IAAAA,CAAOH,EACzB,CASO,SAASC,EAAsBlL,CAAkB,EACtD,IAAMqL,EAAa,IAAIC,gBAWvB,OANAtL,EAASuL,IAAI,CAAC,QAAS,KACjBvL,EAASwL,gBAAgB,EAAE,AAE/BH,EAAWI,KAAK,CAAC,IAAIT,EACvB,GAEOK,CACT,CAUO,SAASF,EAAuBnL,CAAkB,EACvD,GAAM,SAAE0L,CAAO,WAAEC,CAAS,CAAE,CAAG3L,EAC/B,GAAI0L,GAAWC,EACb,OAAOC,EADiB,UACLH,KAAK,CAACC,GAAW,IAAIV,GAG1C,GAAM,QAAE5L,CAAM,CAAE,CAAG8L,EAAsBlL,GACzC,OAAOZ,CACT,CAEO,MAAM2L,EACX,OAAcc,oBACZ7N,CAAwB,CACxBoB,CAAmB,CACN,CAQN,GAILsL,CAHA,AACA,EAEAA,EAAAA,iBAAiB,AAAjBA,EAAkB1M,GAElB,OADA,AACO+M,EAAmBe,mBAAmB,CAAC9N,EAASoB,EAEvD,EAN6D,KAMvD,GAP+D,IAO/D,cAAoD,CAApD,AAAIyE,MAAM,2CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAmD,EAE7D,CAEA,OAAciI,oBACZ9N,CAAwB,CACxBoB,CAAmB,CACN,CAEb,IAMIzB,EANAZ,EAAwB,KAO5B,GANuB,QAAnBiB,EAAQG,MAAM,EAAiC,SAAnBH,EAAQG,MAAM,EAAeH,EAAQjB,IAAI,EAAE,CAEzEA,EAAOiB,EAAQjB,IAAAA,AAAI,EAIjBiB,EAAQL,GAAG,CAACsG,UAAU,CAAC,QACzBtG,CADkC,CAC5B,IAAI2B,IAAItB,EAAQL,GAAG,MACpB,CAEL,IAAMsK,EAAOxG,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACzD,EAAS,WAOnCL,EANE,AAACsK,GAASA,EAAKhE,GAAN,OAAgB,CAAC,QAMtB,CAN+B,GAM3B3E,IAAItB,EAAQL,GAAG,CAAEsK,GAFrB,IAAI3I,IAAItB,EAAQL,GAAG,CAAE,WAI/B,CAEA,OAAO,IAAIqM,EAAAA,WAAW,CAACrM,EAAK,CAC1BQ,OAAQH,EAAQG,MAAM,CACtBZ,QAASyE,GAAAA,EAAAA,2BAAAA,AAA2B,EAAChE,EAAQT,OAAO,EACpD4M,OAAQ,cACR/K,EAOA,GAAIA,EAAO2M,OAAO,CACd,CAAC,EACD,CACEhP,MACF,CACN,AADO,EAET,CAEA,OAAciP,mBAAmBhO,CAAuB,CAAe,CAErE,IAAIjB,EAA8B,KAKlC,MAJuB,QAAnBiB,EAAQG,MAAM,EAAiC,QAAQ,CAA3BH,EAAQG,MAAM,EAC5CpB,GAAOiB,EAAQjB,IAAAA,AAAI,EAGd,IAAIiN,EAAAA,WAAW,CAAChM,EAAQL,GAAG,CAAE,CAClCQ,OAAQH,EAAQG,MAAM,CACtBZ,QAASyE,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAAChE,EAAQT,OAAO,EACpD4M,OAAQ,OACR/K,OAAQpB,EAAQA,OAAO,CAACoB,MAAM,CAO9B,GAAIpB,EAAQA,OAAO,CAACoB,MAAM,CAAC2M,OAAO,CAC9B,CAAC,EACD,MACEhP,CACF,CAAC,AACP,EACF,CACF,4HC5GgBkP,+BAA+B,CAAA,kBAA/BA,GAnCAC,yBAAyB,CAAA,kBAAzBA,uEAJhB,IAAIC,EAA2B,EAC3BC,EAA2B,EAC3BC,EAA2B,EAExB,SAASH,EACdI,CAA2B,QAE3B,AAAM,IAAF,CAAE,WAAiBC,UAAS,CAIzB,CACLE,CALkC,OAKzB,CAAC,GAAGC,KACX,IAAMC,EAAYC,YAAYC,GAAG,GAEA,GAAG,CAAhCV,IACFA,EAA2BQ,CAAAA,EAG7B,GAAI,CAEF,OADAN,GAA4B,EACrBC,EAAaE,YAAY,CAACC,OAAO,IAAIC,EAC9C,QAAU,CACRN,GAA4BQ,YAAYC,GAAG,GAAKF,CAClD,CACF,EACAG,UAAW,CAAC,GAAGJ,KACb,IAAMC,EAAYC,YAAYC,GAAG,GAC3BzL,EAASkL,EAAaE,YAAY,CAACM,SAAS,IAAIJ,GAMtD,OAHAtL,EAAO2L,OAAO,CAAC,KACbX,GAA4BQ,YAAYC,GAAG,GAAKF,CAClD,GACOvL,CACT,CACF,EA5BSkL,EAAaE,YA6BxB,AA7BoC,CA+B7B,SAASP,EACd9M,EAA+B,CAAC,CAAC,EAEjC,IAAM6N,EACyB,IAA7Bb,OACI1G,EACA,0BACE0G,2BACAC,2BACAC,CACF,EAQN,OANIlN,EAAQ8N,KAAK,EAAE,CACjBd,EAA2B,EAC3BC,EAA2B,EAC3BC,EAA2B,GAGtBW,CACT,4HClDgBE,YAAY,CAAA,kBAAZA,GA+GMC,kBAAkB,CAAA,kBAAlBA,+EArHf,CAAA,CAAA,IAAA,QACyB,CAAA,CAAA,IAAA,QACN,CAAA,CAAA,IAAA,QACS,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,IAEzC,SAASD,EAAaE,CAAM,EACjC,MAAOA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAGhC,IAAI,AAAJA,IAAS,cAAgBgC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAGhC,IAAAA,AAAI,IAAKH,EAAAA,mBAAmB,AACpE,CA6GO,eAAekC,EACpB0B,CAAoC,CACpC/D,CAAmB,CACnBwC,CAAkC,EAElC,GAAI,CAEF,GAAM,CAAE5B,SAAO,WAAEC,CAAS,CAAE,CAAGb,EAC/B,GAAIY,GAAWC,EAAW,OAI1B,IAAMN,EAAaH,CAAAA,EAAAA,EAAAA,qBAAAA,AAAqB,EAACJ,GAEnCgE,EAASzB,AAzHnB,SAASA,AACPvC,CAAmB,CACnBwC,CAAkC,EAElC,IAAIC,GAAU,EAIVC,EAAU,IAAItM,EAAAA,eAAe,CACjC,SAASuM,IACPD,EAAQzM,OAAO,EACjB,CACA+J,EAAI4C,EAAE,CAAC,QAASD,GAIhB3C,EAAIS,IAAI,CAAC,QAAS,KAChBT,EAAI6C,GAAG,CAAC,QAASF,GACjBD,EAAQzM,OAAO,EACjB,GAIA,IAAM6M,EAAW,IAAI1M,EAAAA,eAAe,CAMpC,OALA4J,EAAIS,IAAI,CAAC,SAAU,KACjBqC,EAAS7M,OAAO,EAClB,GAGO,IAAI8M,eAA2B,CACpCC,MAAO,MAAOC,IAIZ,GAAI,CAACR,EAAS,CAGZ,GAFAA,EAAU,GAGR,gBAAiBhB,YACjBhE,QAAQC,GAAG,CAACwF,4BAA4B,CACxC,CACA,IAAMhB,EAAUf,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,IAC3Ce,GACFJ,MADW,MACCqB,OAAO,CACjB,CAAA,EAAG1F,QAAQC,GAAG,CAACwF,4BAA4B,CAAC,8BAA8B,CAAC,CAC3E,CACEjL,MAAOiK,EAAQb,wBAAwB,CACvC+B,IACElB,EAAQb,wBAAwB,CAChCa,EAAQZ,wBACZ,AADoC,EAI1C,CAEAtB,EAAIqD,YAAY,GAChBC,GAAAA,EAAAA,SAAAA,AAAS,IAAGC,KAAK,CACfC,EAAAA,kBAAkB,CAACC,aAAa,CAChC,CACEC,SAAU,gBACZ,EACA,SAAM/I,EAEV,CAEA,GAAI,CACF,IAAMgJ,EAAK3D,EAAIgD,KAAK,CAACC,GAIjB,UAAWjD,GAA4B,YAArB,AAAiC,OAA1BA,EAAI4D,KAAK,EACpC5D,EAAI4D,KAAK,GAKND,IAAI,AACP,MAAMjB,EAAQ1N,OAAO,CAGrB0N,EAAU,IAAItM,EAAAA,eAAe,CAEjC,CAAE,MAAOG,EAAK,CAEZ,MADAyJ,EAAIoD,GAAG,GACD,OAAA,cAA8D,CAA9D,AAAIrK,MAAM,oCAAqC,CAAEC,MAAOzC,CAAI,GAA5D,oBAAA,OAAA,mBAAA,gBAAA,CAA6D,EACrE,CACF,EACAoK,MAAQpK,AAAD,IACDyJ,EAAIU,gBAAgB,EAAE,AAE1BV,EAAI6D,OAAO,CAACtN,EACd,EACAuN,MAAO,UAOL,GAJItB,GACF,MAAMA,GAGJxC,EAAIU,GAJa,aAIG,CAGxB,CAH0B,MAE1BV,EAAIoD,GAAG,GACAN,EAAS9N,OAAO,AACzB,CACF,EACF,EAgB4CgL,EAAKwC,EAE7C,OAAMuB,EAASE,MAAM,CAACD,EAAQ,CAAE1P,OAAQiM,EAAWjM,MAAM,AAAC,EAC5D,CAAE,MAAOiC,EAAU,CAEjB,GAAI6L,EAAa7L,GAAM,MAEvB,OAAM,OAAA,cAAoD,CAApD,AAAIwC,MAAM,0BAA2B,CAAEC,MAAOzC,CAAI,GAAlD,oBAAA,OAAA,mBAAA,gBAAA,CAAmD,EAC3D,CACF,6ICvEA,UAAA,qCAAqB2N,aAhEd,CAAA,CAAA,IAAA,QAC0C,CAAA,CAAA,IAAA,GA+DlC,OAAMA,EA6BnB,OAAcC,WAAWvR,CAAsB,CAAE,CAC/C,OAAO,IAAIsR,EAAyCtR,EAAO,CAAEhB,SAAU,CAAC,CAAE,EAC5E,CAIAyL,YACEnI,CAA8B,CAC9B,aAAEkP,CAAW,CAAEC,WAAS,UAAEzS,CAAQ,CAAiC,CACnE,CACA,IAAI,CAACsD,QAAQ,CAAGA,EAChB,IAAI,CAACkP,WAAW,CAAGA,EACnB,IAAI,CAACxS,QAAQ,CAAGA,EAChB,IAAI,CAACyS,SAAS,CAAGA,CACnB,CAEOC,eAAe1S,CAAkB,CAAE,CACxCc,OAAO6R,MAAM,CAAC,IAAI,CAAC3S,QAAQ,CAAEA,EAC/B,CAMA,IAAW4S,QAAkB,CAC3B,OAAO,AAAkB,WAAd,CAACtP,QACd,AADsB,CAOtB,IAAWuP,WAAqB,CAC9B,MAAgC,UAAzB,OAAO,IAAI,CAACvP,QAAQ,AAC7B,CAIOwP,kBAAkBC,GAAS,CAAK,CAA4B,CACjE,GAAsB,MAAM,CAAxB,IAAI,CAACzP,QAAQ,CACf,MAAM,OAAA,cAA0D,CAA1D,AAAI6D,MAAM,iDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAyD,GAGjE,GAA6B,UAAzB,OAAO,IAAI,CAAC7D,QAAQ,CAAe,CACrC,GAAI,CAACyP,EACH,MADW,AACL,OAAA,cAEL,CAFK,AAAI5L,MACR,8EADI,oBAAA,OAAA,kBAAA,gBAAA,CAEN,GAGF,MAAO6L,CAAAA,EAAAA,EAAAA,cAAc,AAAdA,EAAe,IAAI,CAACb,QAAQ,CACrC,CAEA,OAAOc,OAAOtR,IAAI,CAAC,IAAI,CAAC2B,QAAQ,CAClC,CAWO4P,kBAAkBH,GAAS,CAAK,CAA4B,CACjE,GAAsB,MAAM,CAAxB,IAAI,CAACzP,QAAQ,CACf,MAAM,OAAA,cAA0D,CAA1D,AAAI6D,MAAM,iDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAyD,GAGjE,GAA6B,UAAzB,OAAO,IAAI,CAAC7D,QAAQ,CAAe,CACrC,GAAI,CAACyP,EACH,MADW,AACL,OAAA,cAEL,CAFK,AAAI5L,MACR,8EADI,oBAAA,OAAA,kBAAA,gBAAA,CAEN,GAGF,MAAOgM,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAAC,IAAI,CAAChB,QAAQ,CACrC,CAEA,OAAO,IAAI,CAAC7O,QAAQ,AACtB,CAMA,IAAY6O,UAAuC,CACjD,GAAsB,MAAM,CAAxB,IAAI,CAAC7O,QAAQ,CACf,MAAM,OAAA,cAAyD,CAAzD,AAAI6D,MAAM,gDAAV,oBAAA,OAAA,kBAAA,gBAAA,CAAwD,GAEhE,GAA6B,UAAU,AAAnC,OAAO,IAAI,CAAC7D,QAAQ,CACtB,MAAM,OAAA,cAA2D,CAA3D,AAAI6D,MAAM,kDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAA0D,UAGlE,AAAI8L,OAAOG,QAAQ,CAAC,IAAI,CAAC9P,QAAQ,EACxB+P,CAD2B,AAC3BA,EAAAA,EAAAA,gBAAAA,AAAgB,EAAC,IAAI,CAAC/P,QAAQ,EAInC5B,MAAMoE,OAAO,CAAC,IAAI,CAACxC,QAAQ,EACtBgQ,CADyB,EACzBA,EAAAA,YAAAA,AAAY,KAAI,IAAI,CAAChQ,QAAQ,EAG/B,IAAI,CAACA,QAAQ,AACtB,CAUOiQ,MAAMpB,CAAoC,CAAE,KAM7CqB,EALJ,GAAsB,MAAM,CAAxB,IAAI,CAAClQ,QAAQ,CACf,MAAM,OAAA,cAAkE,CAAlE,AAAI6D,MAAM,yDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiE,GAgBzEqM,CAVEA,EAD2B,UAAzB,AAAmC,OAA5B,IAAI,CAAClQ,QAAQ,CACV,CAACmQ,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAAC,IAAI,CAACnQ,QAAQ,EAAE,CACpC5B,MAAMoE,OAAO,CAAC,IAAI,CAACxC,QAAQ,EACxB,CAD2B,GACvB,CAACA,QAAQ,CAChB2P,OAAOG,QAAQ,CAAC,IAAI,CAAC9P,QAAQ,EAC1B,CAD6B,AAC5B+P,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAAC,IAAI,CAAC/P,QAAQ,EAAE,CAEjC,CAAC,IAAI,CAACA,QAAQ,CAAC,EAInBG,IAAI,CAAC0O,GAGf,IAAI,CAAC7O,QAAQ,CAAGkQ,CAClB,CASA,MAAanB,OAAOqB,CAAoC,CAAiB,CACvE,GAAI,CACF,MAAM,IAAI,CAACvB,QAAQ,CAACE,MAAM,CAACqB,EAAU,CAKnCC,cAAc,CAChB,GAII,IAAI,CAAClB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS,CAGxC,MAAMiB,EAASxB,KAAK,EACtB,CAAE,MAAOvN,EAAK,CAIZ,GAAI6L,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAAC7L,GAAM,YAErB,MAAM+O,EAAS3E,KAAK,CAACpK,EAQvB,OAAMA,CACR,CACF,CAQA,MAAa8L,mBAAmBrC,CAAmB,CAAE,CACnD,MAAMqC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC,IAAI,CAAC0B,QAAQ,CAAE/D,EAAK,IAAI,CAACqE,SAAS,CAC7D,CACF,2BCpSkBmB,SAAAA,2GAAAA,YAAAA,qCAAAA,KAAX,IAAWA,GAGf,OAAA,EAHeA,AAGf,GAAA,CAAA,OAHeA,CAOf,EAAA,AAPeA,OAOf,EAAA,CAAA,YAKA,EAAA,OAAA,CAAA,CAAA,WAKA,EAAA,OAAA,EAAA,CAAA,YAKA,EAAA,KAAA,CAAA,CAAA,OAtBeA,6HCYIC,sBAAsB,CAAA,kBAAtBA,GA6DNC,+BAA+B,CAAA,kBAA/BA,GAjCMC,oBAAoB,CAAA,kBAApBA,+EAjCf,CAAA,CAAA,IAAA,WAEkB,CAAA,CAAA,IAAA,qCACC,CAAA,CAAA,IAAA,IAEnB,eAAeF,EACpBG,CAA8B,MAK1BA,EAQIA,EAXR,MAAO,CACL,GAAGA,CAAU,CACbhT,MACEgT,CAAAA,AAAgB,OAAhBA,EAAAA,EAAWhT,KAAAA,AAAK,EAAA,KAAA,EAAhBgT,EAAkBC,IAAAA,AAAI,IAAKvQ,EAAAA,eAAe,CAACwQ,KAAK,CAC5C,CACED,KAAMvQ,EAAAA,eAAe,CAACwQ,KAAK,CAC3BC,KAAM,MAAMH,EAAWhT,KAAK,CAACmT,IAAI,CAACjB,iBAAiB,EAAC,GACpDkB,SAAUJ,EAAWhT,KAAK,CAACoT,QAAQ,CACnCvT,QAASmT,EAAWhT,KAAK,CAACH,OAAO,CACjCF,OAAQqT,EAAWhT,KAAK,CAACL,MAAM,AACjC,EACAqT,CAAgB,OAAhBA,EAAAA,EAAWhT,KAAAA,AAAK,EAAA,KAAA,EAAhBgT,EAAkBC,IAAAA,AAAI,IAAKvQ,EAAAA,eAAe,CAAC2Q,QAAQ,CACjD,CACEJ,KAAMvQ,EAAAA,eAAe,CAAC2Q,QAAQ,CAC9BF,KAAM,MAAMH,EAAWhT,KAAK,CAACmT,IAAI,CAACjB,iBAAiB,EAAC,GACpDoB,UAAWN,EAAWhT,KAAK,CAACsT,SAAS,CACrCC,QAASP,EAAWhT,KAAK,CAACuT,OAAO,CACjC1T,QAASmT,EAAWhT,KAAK,CAACH,OAAO,CACjCF,OAAQqT,EAAWhT,KAAK,CAACL,MAAM,CAC/B6T,YAAaR,EAAWhT,KAAK,CAACwT,WAChC,AAD2C,EAE3CR,EAAWhT,KAAK,AAC1B,CACF,CAEO,eAAe+S,EACpBzQ,CAA8C,MAU1CA,EAQIA,SAhBR,AAAKA,EAEE,CACLmR,CAHE,MAGMnR,AAHK,EAGImR,MAAM,CACvBC,QAASpR,EAASoR,OAAO,CACzBC,aAAcrR,EAASqR,YAAY,CACnCC,WAAYtR,EAASsR,UAAU,CAC/B5T,MACEsC,CAAc,AAAdA,OAAAA,EAAAA,EAAStC,KAAK,AAALA,EAAK,KAAA,EAAdsC,EAAgB2Q,IAAAA,AAAI,IAAKvQ,EAAAA,eAAe,CAACwQ,KAAK,CACzC,CACCD,KAAMvQ,EAAAA,eAAe,CAACwQ,KAAK,CAC3BC,KAAM7B,EAAAA,OAAY,CAACC,UAAU,CAACjP,EAAStC,KAAK,CAACmT,IAAI,EACjDC,SAAU9Q,EAAStC,KAAK,CAACoT,QAAQ,CACjCvT,QAASyC,EAAStC,KAAK,CAACH,OAAO,CAC/BF,OAAQ2C,EAAStC,KAAK,CAACL,MAAM,AAC/B,EACA2C,CAAAA,AAAc,MAAdA,GAAAA,EAAStC,KAAAA,AAAK,EAAA,KAAA,EAAdsC,EAAgB2Q,IAAI,AAAJA,IAASvQ,EAAAA,eAAe,CAAC2Q,QAAQ,CAC9C,CACCJ,KAAMvQ,EAAAA,eAAe,CAAC2Q,QAAQ,CAC9BF,KAAM7B,EAAAA,OAAY,CAACC,UAAU,CAACjP,EAAStC,KAAK,CAACmT,IAAI,EACjDI,QAASjR,EAAStC,KAAK,CAACuT,OAAO,CAC/B1T,QAASyC,EAAStC,KAAK,CAACH,OAAO,CAC/BF,OAAQ2C,EAAStC,KAAK,CAACL,MAAM,CAC7B2T,UAAWhR,EAAStC,KAAK,CAACsT,SAAS,CACnCE,YAAalR,EAAStC,KAAK,CAACwT,WAAW,AACzC,EACAlR,EAAStC,KAAK,AACxB,EA3BsB,IA4BxB,CAEO,SAAS8S,EACde,CAAoB,EAEpB,OAAQA,GACN,KAAKjB,EAAAA,SAAS,CAACM,KAAK,CAClB,OAAOvQ,EAAAA,oBAAoB,CAACuQ,KAC9B,AADmC,MAC9BN,EAAAA,SAAS,CAACS,QAAQ,CACrB,OAAO1Q,EAAAA,oBAAoB,CAAC0Q,QAAQ,AACtC,MAAKT,EAAAA,SAAS,CAACkB,KAAK,CAClB,OAAOnR,EAAAA,oBAAoB,CAACmR,KAAK,AACnC,MAAKlB,EAAAA,SAAS,CAACmB,SAAS,CACtB,OAAOpR,EAAAA,oBAAoB,CAACoR,SAAS,AACvC,SACE,MAAM,OAAA,cAA+C,CAA/C,AAAI5N,MAAM,CAAC,sBAAsB,EAAE0N,EAAAA,CAAW,EAA9C,oBAAA,OAAA,kBAAA,gBAAA,CAA8C,EACxD,CACF,kJCrEA,UAAA,qCAAqBG,aAXG,CAAA,CAAA,IAAA,QACW,CAAA,CAAA,IAAA,QAK5B,CAAA,CAAA,IAAA,OAGO,CAAA,CAAA,IAAA,MAAA,uKAEC,OAAMA,EAwBnBvJ,YAAYwJ,CAAoB,CAAE,MAvBjBC,OAAAA,CAAUtR,EAAAA,OAAO,CAACM,MAAM,CAIvC,CAGAL,WAAY,CAAC,KAAEV,CAAG,sBAAEgS,CAAoB,CAAE,GACxC,CAAA,EAAGhS,EAAI,CAAC,EAAEgS,EAAuB,IAAM,IAAA,CAAK,CAI9CrR,YAAasR,EAAAA,kBAAkB,AACjC,GAcE,IAAI,CADmB,AAClBC,WAAe,CAAGJ,CACzB,CAEA,MAAa3Q,IACXnB,CAAkB,CAClBmS,CAAoC,CACpCC,CAOC,CACmC,CAGpC,GAAI,CAACpS,EACH,GADQ,IACDmS,EAAkB,CAAEE,YAAa,GAAOC,mBAAoB,IAAK,GAG1E,GAAM,kBACJC,CAAgB,sBAChBP,GAAuB,CAAK,YAC5BP,GAAa,CAAK,CAClBe,qBAAoB,CAAK,CAC1B,CAAGJ,EAEEjS,EAAW,MAAM,IAAI,CAAC4R,OAAO,CAAC/Q,KAAK,CACvC,KAAEhB,uBAAKgS,CAAqB,EAC5B,MAAOxS,EAAU0B,SAKb,EAFF,GACE,IAAI,CAAC4Q,WAAW,EAChB,CAAsB,AAAtB,OAAA,EAAA,IAAI,CAACW,iBAAAA,AAAiB,EAAA,KAAA,EAAtB,EAAwBzS,GAAAA,AAAG,IAAKR,GAChC,IAAI,CAACiT,iBAAiB,CAACC,SAAS,CAAGC,KAAK3F,GAAG,GAE3C,CADA,MACO,IAAI,CAACyF,iBAAiB,CAACpS,KAAK,CAIrC,IAAMyQ,EAAOH,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACyB,EAAQV,SAAS,EAE1DkB,GAAW,EACXC,EAAuD,KAC3D,GAAI,CASF,GAAIA,CARJA,EAAiB,AAAC,IAAI,CAACf,WAAW,CAM9B,KALA,MAAMS,EAAiBpR,GAAG,CAACnB,EAAK,MAC9B8Q,EACA0B,kBAAmBJ,EAAQI,iBAAiB,YAC5Cf,CACF,EACA,GAEkB,CAACO,IACrB9Q,EAAQ2R,GACRD,GAAW,EAEP,CAACC,EAAetB,KAJuB,EAIhB,EAAIa,EAAQU,UAAU,EAAE,AAGjD,OAAO,KAIX,IAAMjC,EAAa,MAAMsB,EAAkB,CACzCE,YAAaO,EACbN,mBAAoBO,EACpBE,eAAgB,EAClB,GAIA,GAAI,CAAClC,EAGH,OADI,GAFW,CAEP,CAACiB,WAAW,GAAE,IAAI,CAACW,iBAAiB,MAAG7M,CAAAA,EACxC,KAGT,IAAMoN,EAAe,MAAMtC,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAAC,CAChD,GAAGG,CAAU,CACbS,OAAQ,CAACuB,CACX,GACA,GAAI,CAACG,EAGH,OADI,IAAI,CAFS,AAERlB,WAAW,GAAE,IAAI,CAACW,iBAAiB,MAAG7M,CAAAA,EACxC,KA4BT,OAvBKoM,GAAyBY,IAC5B1R,EAAQ8R,GACRJ,CAFsC,EAE3B,GAKTI,EAAaxB,IAPY,QAOA,EAAE,CACzB,IAAI,CAACM,WAAW,CAClB,CADoB,GAChB,CAACW,iBAAiB,CAAG,CACvBzS,IAAKR,EACLa,MAAO2S,EACPN,UAAWC,KAAK3F,GAAG,GAAK,GAC1B,EAEA,MAAMuF,EAAiBjR,GAAG,CAACtB,EAAKgT,EAAanV,KAAK,CAAE,CAClD2T,aAAcwB,EAAaxB,YAAY,mBACvCgB,aACAf,CACF,IAIGuB,CACT,CAAE,MAAOxR,EAAK,CAGZ,GAAIqR,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBrB,YAAY,CAAE,CAChC,IAAMyB,EAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAACP,EAAerB,YAAY,CAAC6B,UAAU,EAAI,EAAG,GACtD,IAGIC,OACmC1N,IAAvCiN,EAAerB,YAAY,CAAC+B,MAAM,MAC9B3N,EACAsN,KAAKE,GAAG,CACNH,EAAgB,EAChBJ,EAAerB,YAAY,CAAC+B,MAAM,CAG1C,OAAMhB,EAAiBjR,GAAG,CAACtB,EAAK6S,EAAehV,KAAK,CAAE,CACpD2T,aAAc,CAAE6B,WAAYJ,EAAeM,OAAQD,CAAU,oBAC7Dd,aACAf,CACF,EACF,CAIA,GAAImB,EAEF,OADAY,CADY,OACJzP,KAAK,CAACvC,GACP,IAIT,OAAMA,CACR,CACF,GAGF,MAAOoP,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACzQ,EAC9B,CACF,4HCtKasT,iBAAiB,CAAA,kBAAjBA,GAiHGC,oBAAoB,CAAA,kBAApBA,GAw0BAC,UAAU,CAAA,kBAAVA,GAn7BAC,kBAAkB,CAAA,kBAAlBA,GA8BAC,YAAY,CAAA,kBAAZA,+EAnEkC,CAAA,CAAA,IAAA,QACd,CAAA,CAAA,IAAA,QAM7B,CAAA,CAAA,IAAA,QACmC,CAAA,CAAA,IAAA,QACP,CAAA,CAAA,IAAA,QAED,CAAA,CAAA,IAAA,QAM3B,CAAA,CAAA,IAAA,QACuC,CAAA,CAAA,IAAA,QAChB,CAAA,CAAA,IAAA,IAYjBJ,EAAoB1R,OAAOC,GAAG,CAAC,cAMrC,SAAS4R,EACdI,CAAsB,CACtB9O,CAAa,EAEb,GAAI,CACF,IAAI+O,EAEJ,IAAsB,IAAlBD,EACFC,CAD2B,CACJzb,EAAAA,OAHsBoN,OAGR,MAChC,GACoB,UAAzB,OAAOoO,GACP,CAACE,MAAMF,IACPA,EAAgB,CAAC,EAEjBC,CADA,CACuBD,OAClB,GAAI,KAAyB,IAAlBA,EAChB,MAAM,CADyC,MACzC,cAEL,CAFShQ,AAAJ,MACJ,CAAC,0BAA0B,EAAEgQ,EAAc,MAAM,EAAE9O,EAAM,yCAAyC,CAAC,EAD/F,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,OAAO+O,CACT,CAAE,MAAOzS,EAAU,CAEjB,GAAIA,aAAewC,OAASxC,EAAI2S,OAAO,CAAChL,QAAQ,CAAC,sBAC/C,CADsE,KAChE3H,EAER,MACF,CADSoE,AAEX,CAEO,SAASiO,EAAaO,CAAW,CAAEC,CAAmB,EAC3D,IAAMC,EAAsB,EAAE,CACxBC,EAGD,EAAE,CAEP,IAAK,IAAI1U,EAAI,EAAGA,EAAIuU,EAAKrU,MAAM,CAAEF,IAAK,CACpC,IAAM2U,EAAMJ,CAAI,CAACvU,EAAE,CAanB,GAXmB,UAAf,AAAyB,OAAlB2U,EACTD,EAAYjU,IAAI,CAAC,KAAEkU,EAAKC,OAAQ,gCAAiC,GACxDD,EAAIzU,MAAM,CAAG3G,EAAAA,yBAAyB,CAC/Cmb,CADiD,CACrCjU,IAAI,CAAC,KACfkU,EACAC,OAAQ,CAAC,uBAAuB,EAAErb,EAAAA,yBAAyB,CAAA,CAAE,AAC/D,GAEAkb,EAAUhU,IAAI,CAACkU,GAGbF,EAAUvU,MAAM,CAAG5G,EAAAA,wBAAwB,CAAE,CAC/Cqa,QAAQkB,IAAI,CACV,CAAC,oCAAoC,EAAEL,EAAY,eAAe,CAAC,CACnED,EAAKtO,KAAK,CAACjG,GAAGgI,IAAI,CAAC,OAErB,KACF,CACF,CAEA,GAAI0M,EAAYxU,MAAM,CAAG,EAGvB,CAH0B,GAGrB,GAAM,KAAEyU,CAAG,QAAEC,CAAM,CAAE,GAF1BjB,QAAQkB,IAAI,CAAC,CAAC,gCAAgC,EAAEL,EAAY,EAAE,CAAC,EAEjCE,GAC5Bf,QAAQmB,CADiC,EAC9B,CAAC,CAAC,MAAM,EAAEH,EAAI,EAAE,EAAEC,EAAAA,CAAQ,EAGzC,OAAOH,CACT,CAEA,SAASM,EACPC,CAAoB,CACpBC,CAAqC,MAIjCD,EADJ,GAAKA,CAAD,GACJ,AAA+B,MAA3BA,CADY,EACZA,EAAUE,iBAAAA,AAAiB,GAAA,KAAA,GAA3BF,EAA6BG,KAAAA,AAAK,EAAE,AAWtC,CAACC,CARA,AAAEvM,CAAD,OAASC,GAAG,CAACuM,GASf,CAACG,YAT8B,EACU,MAAvC3M,QAAQC,GAAG,CAACwM,sBAAsB,AAAK,CAAE,EAC3CN,EAAUO,kBAAkB,EACR1M,CAMnB2M,GACD,CAIFR,EAAUU,CAXoB5M,GAAG,CAAC2M,OAWZ,CAXoB,EAWf,EAAE,CAE7BT,EAAUU,YAAY,CAACjV,IAAI,CAAC,CAC1B,GAAGwU,CAAG,CAduC,AAe7CzG,IAAKtB,YAAYyI,UAAU,CAAGzI,YAAYC,GAAG,GAC7CyI,IAAKZ,EAAUa,WAAW,EAAI,CAChC,GACF,CAOO,SAAShC,EACdiC,CAAoB,CACpB,kBAAEC,CAAgB,sBAAEC,CAAoB,CAAmB,EAI3D,IAAMC,EAAU,MACdvN,EACA6B,SAYeA,EAIKA,MAdhBtM,EACJ,GAAI,CAEFA,CADAA,EAAM,IAAI2B,IAAI8I,aAAiB7I,QAAU6I,EAAMzK,GAAG,CAAGyK,EAAAA,EACjDoB,QAAQ,CAAG,GACf7L,EAAI4L,QAAQ,CAAG,EACjB,CAAE,KAAM,CAEN5L,OAAM8H,CACR,CACA,IAAMmQ,EAAWjY,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAK0L,IAAI,AAAJA,GAAQ,GACxBlL,EAAS8L,CAAAA,MAAAA,CAAAA,EAAY,AAAZA,GAAAA,IAAAA,EAAAA,EAAM9L,MAAAA,AAAM,EAAA,KAAA,EAAZ8L,EAAc4L,WAAW,EAAA,GAAM,MAIxCC,EAAa,CAAC7L,MAAAA,CAAAA,EAAAA,AAAU,GAAVA,IAAAA,EAAAA,EAAM8L,IAAAA,AAAI,EAAA,KAAA,EAAV9L,EAAoB+L,QAAQ,KAAK,EAC/CC,EAAoD,MAAzC1N,QAAQC,GAAG,CAAC0N,wBAAwB,CAK/CC,EAAiCL,OACnCrQ,EACAmH,YAAYyI,UAAU,CAAGzI,YAAYC,GAAG,GAEtC6H,EAAYe,EAAiBW,QAAQ,GACrCC,EAAgBX,EAAqBU,QAAQ,GAG/CE,EACFD,GAAwC,cAAvBA,EAAcE,IAAI,CAC/BF,EAAcC,WAAW,CACzB,KACFA,GACFA,EAAYE,QADG,CACM,GAGvB,IAAMpV,EAASgN,GAAAA,EAAAA,SAAAA,AAAS,IAAGC,KAAK,CAC9ByH,EAAaxH,EAAAA,kBAAkB,CAACmI,aAAa,CAAGC,EAAAA,aAAa,CAACC,KAAK,CACnE,UACEV,EACAtF,KAAMiG,EAAAA,QAAQ,CAACC,MAAM,CACrBrI,SAAU,CAAC,QAASrQ,EAAQyX,EAAS,CAACkB,MAAM,CAACC,SAASrP,IAAI,CAAC,KAC3DsP,WAAY,CACV,WAAYpB,EACZ,cAAezX,EACf,eAAe,CAAER,QAAAA,KAAAA,EAAAA,EAAKyG,QAAQ,CAC9B,gBAAiBzG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAKwL,IAAAA,AAAI,QAAI1D,CAChC,CACF,EACA,cAkKIhE,MAjFEuW,EAiPA3Y,EAsMAka,EA1eAnC,EA5BJ,GAAItB,GAOA,CAACpB,GAMDA,EAAUuC,GAbE,CA4B0BxR,EArB1B,KAMS,CAZvB,CAYyB,MAZlB+P,EAAYpN,EAAO6B,GAgB5B,IAAMiN,EACJ9O,GACiB,UAAjB,OAAOA,GAC8B,UAArC,OAAQA,EAAkBjK,MAAM,CAE5BsD,EAAiB,AAAC0V,GAGfzZ,CADQuM,MAAAA,EAAAA,AACEiN,KADFjN,EAAAA,CAAc,CAACkN,EAAAA,AAAM,IACnBD,EAAkB9O,CAAa,CAAC+O,EAAM,CAAG,IAAA,CAAG,CAIzDE,EAAe,AAACF,QACNlN,EACVA,EAEE,EAHN,OAAO,KAA+B,EAA/B,GAAOA,MAAAA,CAAAA,EAAU,AAAVA,GAAAA,IAAAA,EAAAA,EAAM8L,IAAAA,AAAI,EAAA,KAAA,EAAV9L,CAAY,CAACkN,EAAM,EAC7BlN,MAAAA,CAAAA,EAAAA,AAAU,GAAVA,GAAAA,GAAAA,EAAM8L,IAAAA,AAAI,EAAA,KAAA,EAAV9L,CAAY,CAACkN,EAAM,CACnBD,EAAAA,AACqB,OAAnB,EAAC9O,EAAc2N,IAAAA,AAAI,EAAA,KAAA,EAAnB,CAAqB,CAACoB,EAAM,MAC5B1R,CACR,EAGI6R,EAAyBD,EAAa,cACpCpD,EAAiBP,EACrB2D,EAAa,SAAW,EAAE,CAC1B,CAAC,MAAM,EAAEjP,EAAM1F,QAAQ,GAAA,CAAI,EAGvB6U,EACJlB,IACwB,UAAvBA,EAAcE,CAAdF,GAAkB,EACjBA,AAAuB,gBAATE,IAAI,EACK,kBAAvBF,EAAcE,IAAI,EAClBF,AAAuB,uBAATE,IAAI,AAAK,CAAiB,CACtCF,EACA5Q,OAEN,GAAI8R,GACEnZ,MAAMoE,OAAO,CAACyR,AADC,GACM,CAEvB,IAAMuD,EACJD,EAAgBtD,IAAI,GAAKsD,CAAAA,CAAgBtD,IAAI,CAAG,EAAA,AAAC,EACnD,IAAK,IAAMI,KAAOJ,EACZ,AAACuD,EAAcxO,CADG,OACK,CAACqL,IAC1BmD,EADgC,AAClBrX,IAAI,CAACkU,EAGzB,CAGF,IAAMoD,EAAepB,QAAAA,KAAAA,EAAAA,EAAeoB,YAAY,CAI1CC,EACJrB,GAAwC,mBAAvBA,EAAcE,IAAI,CAC/B,iBACA7B,EAAUiD,UAAU,CAEpBC,EAAiB,CAAC,CAAClD,EAAUmD,iBAAiB,CAEhDC,EAA0BrW,EAAe,SACzCsW,EAAc,GAImB,UAAnC,OAAOD,GACP,KAAkC,IAA3BR,IAKwB,KAJ/B,MAGyC,KACvC,GACEA,AAA2B,KAC7B,EAC6B,aAA5BQ,CACER,GAAAA,EAAyB,IAAgC,IAA3BA,CAA2B,CAAI,IAGhEU,EAAe,CAAC,kBAAkB,EAAEF,AALsB,EAKE,mBAAmB,EAAER,EAAuB,gCAAgC,CAAC,CACzIQ,OAA0BrS,EAC1B6R,OAAyB7R,GAI7B,IAAMyS,EAEwB,aAD5B,GAE4B,aAA5BJ,CAF2C,AAG3C,EAEuB,mBAAvBJ,GACuB,kBAAvBA,EAOIS,EACJ,CAACT,GACD,CAACI,GACD,CAACR,GACD5C,EAAU0D,YAAY,CAKM,gBAA5BN,GACA,CApB6F,IAoB3D,IAA3BR,EAEPA,GAAyB,EAKzBjB,CAAAA,CANA,KAMAA,EAAAA,KAAAA,EAAAA,EAAeE,IAAAA,AAAI,IAAK,SACvB2B,EAAAA,EAA+BC,CAAAA,CAA2B,GAC3D,AACAb,GAAyB,GAIG,aAA5BQ,GAC4B,aAA5BA,CAA4B,GAC5B,AACAC,GAAc,CAAC,OAAO,EAAED,EAAAA,CAAAA,AAAyB,EAGnDV,EAAkB3D,EAChB6D,EACA5C,EAAU3P,KAAK,EAGjB,IAAMsT,EAAW5W,EAAe,WAC1B6W,EACqB,YAAzB,OAAA,AAAOD,MAAAA,EAAAA,KAAAA,EAAAA,EAAUrX,GAAAA,AAAG,EAChBqX,EACA,IAAI/V,QAAQ+V,GAAY,CAAC,GAEzBE,EACJD,EAAYtX,GAAG,CAAC,kBAAoBsX,EAAYtX,GAAG,CAAC,UAEhDwX,EAAsB,CAAC,CAAC,MAAO,OAAO,CAACxP,QAAQ,CACnDvH,CAAAA,AAAe,MAAfA,GAAAA,EAAe,SAAA,CAAA,CAAA,KAAA,EAAfA,EAA0BiC,WAAW,EAAA,CAAA,EAAM,OAavC+U,OAEkBhT,GAAtBiS,IAECI,KAHiC,AAGNrS,CAD5B,KAI8B,OAF5B,KAEAqS,CAA4B,CAAQ,KACtC,EAC0BrS,GAA1B6R,EACIoB,EAGHD,CAViC,EAahC,CAAC/D,EAAUiE,MAHb,AADA,AADA,QAK2B,EACzBJ,GATgC,CASRC,CAAAA,CAHxB,AAG0C,EAC1CjB,GAC+B,IAA/BA,EAAgBrE,OAPgB,GAON,CAE9B,GACEuF,EAX2C,CAY3CpC,EAlBiF,GAkB/D5Q,OACK,aACvB,CADA4Q,EAAcE,IAAI,CAQlB,OAJID,IACFA,EAAYsC,OADG,AACI,GACnBtC,EAhBsF,AAgBxE,MAETuC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBxC,EAAcyC,YAAY,CAC1B,WAIJ,OAAQpB,GACN,IAAK,iBACHK,EAAc,8BACd,KAEF,KAAK,gBACH,GAC8B,gBAA5BD,GACC,AAA2B,SAApBV,GAAmCA,EAAkB,EAE7D,CADA,KACM,OAAA,cAEL,CAFSvT,AAAJ,MACJ,CAAC,uCAAuC,EAAE+R,EAAS,gDAAgD,CAAC,EADhG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEFmC,EAAc,6BACd,KAEF,KAAK,aACH,GAAID,AAA4B,YAAY,GAC1C,MAAM,OAAA,cAEL,CAFK,AAAIjU,MACR,CAAC,oCAAoC,EAAE+R,EAAS,6CAA6C,CAAC,EAD1F,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,KAEF,KAAK,eAED,KAAkC,IAA3B0B,OACPA,CAA2B,GAC3B,CACAS,EAAc,2BACdX,EAAkB/e,EAAAA,cAAc,CAStC,CA0BA,GAxBI,CAyBF,AACA,IA1B6B,IAApB+e,EACkB,OADe,QA0BjB,GAzBrBM,CAA0C,EAACE,EAGpCF,AAAuB,cAH6B,MAwBV,AArBC,IACpDN,EAAkB,EAClBW,EAAc,iCACLH,GACTR,EAAkB,EAClBW,EAAc,OAFW,SAGhBW,GACTtB,EAAkB,EAClBW,EAAc,IAFQ,cAKtBA,EAAc,aACdX,EAAkBG,EACdA,EAAgBrE,UAAU,CAC1B7a,EAAAA,cAAc,GAhBlB+e,EAAkB/e,EAAAA,cAAc,CAChC0f,EAAc,8BAiBP,AAACA,IACVA,EAAc,CAAC,MADQ,MACI,EAAEX,EAAAA,CAAAA,AAAiB,EAM9C,CAAE1C,CAAAA,EAAUqE,WAAW,MAAI3B,CAAoB,CAAA,EAE/C,CAACsB,EADD,CAKAnB,GACAH,EAAkBG,EAAgBrE,KAJlC,KAI4C,CAC5C,CAGA,GAAwB,GAAG,CAAvBkE,EACF,GAAIf,GAAwC,aAAa,CAApCA,EAAcE,IAAI,CAKrC,KAhByD,EAYrDD,IACFA,EAAYsC,OADG,AACI,GACnBtC,CAZ6D,CAY/C,MAETuC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBxC,EAAcyC,YAAY,CAC1B,eAGFE,CAAAA,EAAAA,EAAAA,yBAAyB,AAAzBA,EACEtE,EACA2B,EACA,CAAC,oBAAoB,EAAEjO,EAAM,CAAC,EAAEsM,EAAU3P,KAAK,CAAA,CAAE,EAOnDwS,GAAmBD,IAA2BF,GAChDG,GAAgBrE,UAAU,CADuC,AACpCkE,CAAAA,CAEjC,CAEA,IAAM6B,EACuB,UAA3B,OAAO7B,GAAgCA,EAAkB,EAGrD,kBAAEhF,CAAgB,CAAE,CAAGsC,EAEvBwE,EACJ7C,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAeE,IAAAA,AAAI,IAAK,WAAaF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAeE,IAAAA,AAAI,IAAK,QACzDF,OACA5Q,EAEN,GACE2M,IACC6G,IACCC,MAAAA,EAAAA,IADDD,CACCC,EAAAA,EAAwBC,IADzBF,oBACyBE,AAAwB,CAAD,EAEjD,CADA,EACI,CACF9Z,EAAW,MAAM+S,EAAiBrU,gBAAgB,CAChD6X,EACAsB,EAAkB9O,EAAwB6B,EAE9C,CAAE,MAAO5I,EAAK,CACZgS,QAAQzP,KAAK,CAAC,CAAC,gCAAgC,CAAC,CAAEwE,EACpD,CAGF,IAAMgR,EAAW1E,EAAUa,WAAW,EAAI,CAC1Cb,GAAUa,WAAW,CAAG6D,EAAW,EAEnC,IAAIC,EAAe,IAAMvY,QAAQC,OAAO,GAElCuY,EAAkB,MACtBlI,EACAmI,KAEA,IAAMC,EAAqB,CACzB,QACA,cACA,UACA,YACA,YACA,SACA,OACA,WACA,WACA,iBACA,SACA,YAGIpI,EAAU,EAAE,CAAG,CAAC,SAAS,CAC9B,CAED,GAAI8F,EAAgB,CAClB,IAAMuC,EAAoBrR,EACpBsR,EAA0B,CAC9B3c,KAAO0c,EAAiBE,OAAO,EAAIF,EAAS1c,IAAI,AAClD,EAEA,IAAK,IAAMoa,KAASqC,EAElBE,CAAU,CAACvC,EAAM,CAAGsC,CAAQ,CAACtC,EAAM,CAErC/O,EAAQ,IAAI7I,CAJ4B,OAIpBka,EAAS9b,GAAG,CAAE+b,EACpC,MAAO,GAAIzP,EAAM,CACf,GAAM,SAAE0P,CAAO,MAAE5c,CAAI,QAAEqC,CAAM,CAAE,GAAGwa,EAAY,CAC5C3P,EACFA,EAAO,CACL,GAAG2P,CAAU,CACb7c,KAAM4c,GAAW5c,EACjBqC,OAAQgS,OAAU3L,EAAYrG,CAChC,CACF,CAGA,IAAMya,EAAa,CACjB,GAAG5P,CAAI,CACP8L,KAAM,IAAK9L,MAAAA,EAAAA,KAAAA,EAAAA,EAAM8L,IAAT,CAAe+D,UAAW,kBAAUV,CAAS,CACvD,EAEA,OAAO5D,EAAYpN,EAAOyR,GACvB9Z,IAAI,CAAC,MAAO+K,IAeX,GAdI,CAACsG,GAAW+E,GACd1B,EAAiBC,EAAW,CAC1B3R,IAFwB,EAEjBoT,EACPxY,IAAKiY,EACLmC,YAAawB,GAAuBxB,EACpCgC,YACsB,IAApB3C,GAAyBmC,EACrB,OACA,oBACNvB,EACA3a,OAAQyN,EAAIzN,MAAM,CAClBc,OAAQ0b,EAAW1b,MAAM,EAAI,KAC/B,GAGA2M,AAAe,QAAXzN,MAAM,EACV+U,GACA/S,IACC4Z,IACCC,IADDD,EACCC,EAAAA,KAAAA,EAAAA,EAAwBC,IADzBF,oBACyBE,CAAwB,CAAD,CACjD,CACA,IAAMrF,EACJsD,GAAmB/e,EAAAA,cAAc,CAC7BN,EAAAA,cAAc,CACdqf,EAEN,GAAIf,GAAwC,cAAvBA,EAAcE,IAAI,CAAkB,CAGvD,IAAMyD,EAAa,MAAMlP,EAAImP,WAAW,GAElCC,EAAc,CAClB3c,QAASC,OAAOgN,WAAW,CAACM,EAAIvN,OAAO,CAACe,OAAO,IAC/CvB,KAAM4S,OAAOtR,IAAI,CAAC2b,GAAYtX,QAAQ,CAAC,UACvCrF,OAAQyN,EAAIzN,MAAM,CAClBM,IAAKmN,EAAInN,GACX,AADc,EAkBd,OAZA,MAAMyU,EAAiBjR,GAAG,CACxB9B,EACA,CACEsR,KAAMvQ,EAAAA,eAAe,CAAC+Z,KAAK,CAC3BC,KAAMF,EACNhH,WAAYY,CACd,EACA,CAAE6D,YAAY,WAAM/B,EAAUwD,gBAAUnF,CAAK,GAE/C,MAAMoF,IAGC,IAAIjc,SAAS4c,EAAY,CAC9Bzc,QAASuN,EAAIvN,OAAO,CACpBF,OAAQyN,EAAIzN,MAAM,CAClBC,WAAYwN,EAAIxN,UAAU,AAC5B,EACF,CAAO,CAML,GAAM,CAACH,EAASS,EAAQ,CAAGf,GAAAA,EAAAA,aAAAA,AAAa,EAACiO,GAuCzC,OAlCA3N,EACG8c,WAAW,GACXla,IAAI,CAAC,MAAOka,QAUXf,EATA,IAAMc,EAAarK,OAAOtR,IAAI,CAAC4b,GAEzBC,EAAc,CAClB3c,QAASC,OAAOgN,WAAW,CAACrN,EAAQI,OAAO,CAACe,OAAO,IACnDvB,KAAMid,EAAWtX,QAAQ,CAAC,UAC1BrF,OAAQF,EAAQE,MAAM,CACtBM,IAAKR,EAAQQ,GAAG,AAClB,CAEAub,OAAAA,GAAAA,AAAgD,EAAhDA,IAAAA,CAAgD,EAAhDA,EAAwBC,CAAwB,uBAAA,AAAxBA,GAAxBD,EAAkD/X,GAAG,CACnD9B,EACA6a,GAGEjB,GACF,MAAM7G,EAAiBjR,GAAG,CACxB9B,EACA,CACEsR,KAAMvQ,AAJe,EAIfA,eAAe,CAAC+Z,KAAK,CAC3BC,KAAMF,EACNhH,WAAYY,CACd,EACA,CAAE6D,YAAY,EAAM/B,oBAAUwD,OAAUnF,CAAK,EAGnD,GACCoG,KAAK,CAAEzW,AAAD,GACLyP,QAAQkB,IAAI,CAAC,CAAC,yBAAyB,CAAC,CAAEnM,EAAOxE,IAElDmJ,OAAO,CAACsM,GAEJzb,CACT,CACF,CAMA,OAFA,MAAMyb,IAECvO,CACT,GACCuP,KAAK,CAAC,AAACzW,IAEN,MADAyV,IACMzV,CACR,EACJ,EAGI0W,GAAyB,EACzBC,GAAoB,EAExB,GAAIlb,GAAY+S,EAAkB,CAChC,IAAIoI,EAYJ,IATEtB,MAAAA,EAAAA,KAAAA,EAAAA,EAAwBuB,YAAY,AAAZA,GACxBvB,EAAuBC,wBAAwB,EAC/C,CACAqB,EACEtB,EAAuBC,wBAAwB,CAACnY,GAAG,CAAC3B,GAEtDkb,GAAoB,GAGlBtB,GAAyB,CAACuB,EAAiB,CAC7CnB,EAAe,MAAMjH,EAAiBsI,IAAI,CAACrb,GAC3C,IAAMa,EAAQwU,EAAU7C,oBAAoB,CACxC,KACA,MAAMO,EAAiBpR,GAAG,CAAC3B,EAAU,CACnCsR,KAAMtQ,EAAAA,oBAAoB,CAAC8Z,KAAK,CAChCjH,WAAYkE,EACZxB,WACAwD,WACAnF,OACA0G,QAAQ,CAAElD,MAAAA,EAAAA,KAAAA,EAAAA,EAAcxD,IAAI,AAC9B,GAkBJ,GAhBIwE,GAIEpC,GAAwC,aAAa,CAApCA,EAAcE,IAJP,AAIW,EACrC,MAAMqE,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,IAInC1a,EACF,KADS,CACHmZ,IAGNE,EAAsB,yCAGpBrZ,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOxC,KAAAA,AAAK,GAAIwC,EAAMxC,KAAK,CAACiT,IAAI,GAAKvQ,EAAAA,eAAe,CAAC+Z,KAAK,CAG5D,CAH8D,EAG1DzF,EAAUmG,YAAY,EAAI3a,EAAMkR,OAAO,CACzCkJ,CAD2C,EAClB,MACpB,CACL,GAAIpa,EAAMkR,OAAO,EAAE,CACjBsD,EAAUoG,kBAAkB,GAAK,CAAC,EAC9B,CAACpG,EAAUoG,kBAAkB,CAACzb,EAAS,EAAE,CAC3C,IAAM0b,EAAoBzB,GAAgB,GACvCvZ,IAAI,CAAC,MAAOC,IAAc,CACzBjD,KAAM,CADmB,KACbiD,EAASia,WAAW,GAChC1c,QAASyC,EAASzC,OAAO,CACzBF,OAAQ2C,EAAS3C,MAAM,CACvBC,WAAY0C,EAAS1C,UAAU,CACjC,CAAA,EACCyP,OAAO,CAAC,KACP2H,EAAUoG,kBAAkB,GAAK,CAAC,EAClC,OAAOpG,EAAUoG,kBAAkB,CAACzb,GAAY,GAAG,AACrD,GAIF0b,EAAkBV,KAAK,CAAChH,QAAQzP,KAAK,EAErC8Q,EAAUoG,kBAAkB,CAACzb,EAAS,CAAG0b,CAC3C,CAGFP,EAAkBta,EAAMxC,KAAK,CAAC0c,IAAI,AACpC,CAEJ,CAEA,GAAII,EAAiB,CACfrE,GACF1B,EAAiBC,EAAW,CAC1B3R,IAFY,EAELoT,EACPxY,IAAKiY,cACLmC,EACAgC,YAAaQ,EAAoB,MAAQ,mBACzCvC,EACA3a,OAAQmd,EAAgBnd,MAAM,EAAI,IAClCc,OAAQ8L,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAM9L,MAAAA,AAAM,GAAI,KAC1B,GAGF,IAAM6B,EAAW,IAAI5C,SACnBuS,OAAOtR,IAAI,CAACmc,EAAgBzd,IAAI,CAAE,UAClC,CACEQ,QAASid,EAAgBjd,OAAO,CAChCF,OAAQmd,EAAgBnd,MAAM,AAChC,GAOF,OAJAG,OAAOC,cAAc,CAACuC,EAAU,MAAO,CACrCtC,MAAO8c,EAAgB7c,GAAG,AAC5B,GAEOqC,CACT,CACF,CAEA,GAAI0U,EAAUO,kBAAkB,EAAIhL,GAAQ,AAAgB,iBAATA,EAAmB,CACpE,GAAM,OAAEjL,CAAK,CAAE,CAAGiL,EAKlB,GAAc,YAAY,CAAtBjL,EAEF,GAAIqX,GAAwC,aAAa,CAApCA,EAAcE,IAAI,CAKrC,OAJID,IACFA,EAAYsC,OADG,AACI,GACnBtC,EAAc,MAETuC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBxC,EAAcyC,YAAY,CAC1B,eAGFE,CAAAA,EAAAA,EAAAA,yBAAAA,AAAyB,EACvBtE,EACA2B,EACA,CAAC,eAAe,EAAEjO,EAAM,CAAC,EAAEsM,EAAU3P,KAAK,CAAA,CAAE,EAKlD,IAAMiW,EAAgB,SAAU/Q,EAC1B,MAAE8L,EAAO,CAAC,CAAC,CAAE,CAAG9L,EACtB,GAC6B,UAA3B,OAAO8L,EAAK7C,UAAU,EACtBqE,GACAxB,EAAK7C,UAAU,CAAGqE,EAAgBrE,UAAU,CAC5C,CACA,GAAwB,AAApB6C,GAAuB,GAAlB7C,UAAU,CAEjB,GAAImD,GAAwC,aAAa,CAApCA,EAAcE,IAAI,CACrC,MAAOsC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBxC,EAAcyC,YAAY,CAC1B,eAGFE,CAAAA,EAAAA,EAAAA,yBAAyB,AAAzBA,EACEtE,EACA2B,EACA,CAAC,oBAAoB,EAAEjO,EAAM,CAAC,EAAEsM,EAAU3P,KAAK,CAAA,CAAE,CAKnD,CAAC2P,EAAUqE,WAAW,EAAIhD,AAAoB,GAAG,GAAlB7C,UAAU,GAC3CqE,EAAgBrE,UAAU,CAAG6C,EAAK7C,UAAAA,AAAU,CAEhD,CACI8H,GAAe,OAAO/Q,EAAK8L,IAAI,AACrC,CAKA,GAAI1W,KAAYib,EA+Dd,OAAOhB,GAAgB,EAAOC,EA/DQ,EACtC,IAAM0B,EAAuB5b,EAC7BqV,EAAUoG,kBAAkB,GAAK,CAAC,EAClC,IAAIC,EACFrG,EAAUoG,kBAAkB,CAACG,EAAqB,CAEpD,GAAIF,EAAmB,CACrB,IAAMG,EAKF,MAAMH,EACV,OAAO,IAAI3d,SAAS8d,EAAkBne,IAAI,CAAE,CAC1CQ,QAAS2d,EAAkB3d,OAAO,CAClCF,OAAQ6d,EAAkB7d,MAAM,CAChCC,WAAY4d,EAAkB5d,UAAU,AAC1C,EACF,CAUA,IAAM6d,EAAkB7B,GAAgB,EAAMC,GAK3CxZ,IAAI,CAAClD,EAAAA,WAJN,EAImB,EA4BrB,MAJAke,CAtBAA,EAAoBI,EACjBpb,IAAI,CAAC,MAAOmQ,IACX,IAAMlQ,EAAWkQ,CAAS,CAAC,EAAE,CAC7B,MAAO,CACLnT,KAAM,MAAMiD,EAASia,CAVqC,UAU1B,GAChC1c,QAASyC,EAASzC,OAAO,CACzBF,OAAQ2C,EAAS3C,MAAM,CACvBC,WAAY0C,EAAS1C,UAAU,AACjC,CACF,GACCyP,OAAO,CAAC,SAGF2H,GAAD,AAA6B,OAA5BA,EAAAA,EAAUoG,kBAAAA,AAAkB,EAAA,KAAA,EAA5BpG,CAA8B,CAACuG,EAAqB,AAArBA,GAAuB,AAI3D,OAAOvG,EAAUoG,kBAAkB,CAACG,EAAqB,AAC3D,EAAA,EAIgBZ,KAAK,CAAC,KAAO,GAE/B3F,EAAUoG,kBAAkB,CAACG,EAAqB,CAAGF,EAE9CI,EAAgBpb,IAAI,CAAC,AAACmQ,GAAcA,CAAS,CAAC,EAAE,CACzD,CAGF,GAGF,GANW,AAMPoG,EACF,GAAI,CACF,OAAO,AAFM,MAEAlV,CACf,QAAU,CACJkV,GACFA,EAAYsC,OAAO,CADJ,CAGnB,CAEF,OAAOxX,CACT,EAWA,OALAuU,EAAQyF,aAAa,EAAG,EACxBzF,EAAQ0F,oBAAoB,CAAG,IAAM5F,EACrCE,EAAQ2F,kBAAkB,CAAG9F,EAC3BjJ,UAAsC,CAAC+G,EAAkB,EAAG,EAEvDqC,CACT,CAGO,SAASnC,EAAWrU,CAAwB,EAEjD,IAAIyU,AAx7BkE,IAA9DrH,UAAsC,CAAC+G,EAAkB,CAw7B3C,OAItB,IAAMxW,EAAWe,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC0O,WAAWoK,KAAK,CAGnDpK,YAAWoK,KAAK,CAAGpD,EAAqBzW,EAAUqC,EACpD,6ICn7BgBoc,iBAAAA,qCAAAA,aAlDe,CAAA,CAAA,IAAA,QACkB,CAAA,CAAA,IAAA,QAChB,CAAA,CAAA,IAAA,OAI1B,CAAA,CAAA,IAAA,QAKA,CAAA,CAAA,IAAA,IAKHC,EAAkB,EAEtB,eAAeC,EACbra,CAAS,CACTgR,CAAkC,CAClC/S,CAAgB,CAChB4U,CAAc,CACdf,CAAsC,CACtCkG,CAAgB,CAChBxD,CAAgB,EAEhB,MAAMxD,EAAiBjR,GAAG,CACxB9B,EACA,CACEsR,KAAMvQ,EAAAA,eAAe,CAAC+Z,KAAK,CAC3BC,KAAM,CACJ7c,QAAS,CAAC,EAEVR,KAAMkB,KAAKC,SAAS,CAACkD,GACrB/D,OAAQ,IACRM,IAAK,EACP,EACAuV,WAAkC,UAAtB,OAAOA,EAA0Bnb,EAAAA,cAAc,CAAGmb,CAChE,EACA,CAAEyE,WAAY,GAAM1D,gBAAMmF,WAAUxD,CAAS,EAGjD,CAOO,SAAS2F,EACdG,CAAK,CACLC,CAAmB,CACnBxc,EAMI,CAAC,CAAC,EAEN,GAA2B,GAAG,CAA1BA,EAAQ+T,UAAU,CACpB,MAAM,OAAA,cAEL,CAFK,AAAIrP,MACR,CAAC,wFAAwF,EAAE6X,EAAGhZ,QAAQ,GAAA,CAAI,EADtG,oBAAA,OAAA,kBAAA,eAAA,EAEN,GAIF,IAAMuR,EAAO9U,EAAQ8U,IAAI,CACrBP,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACvU,EAAQ8U,IAAI,CAAE,CAAC,eAAe,EAAEyH,EAAGhZ,QAAQ,GAAA,CAAI,EAC5D,EAAE,CAGN+Q,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChBtU,EAAQ+T,UAAU,CAClB,CAAC,eAAe,EAAEwI,EAAGtQ,IAAI,EAAIsQ,EAAGhZ,QAAQ,GAAA,CAAI,EAU9C,IAAMkZ,EAAW,CAAA,EAAGF,EAAGhZ,QAAQ,GAAG,CAAC,EACjCtE,MAAMoE,OAAO,CAACmZ,IAAaA,EAASjU,IAAI,CAAC,KAAA,CACzC,CAqQF,OAnQiB,AAmQVmU,MAnQiB,GAAGnP,KACzB,IAAMgI,EAAYe,EAAAA,gBAAgB,CAACW,QAAQ,GACrCC,EAAgBX,EAAAA,oBAAoB,CAACU,QAAQ,GAG7C0F,EAGJpH,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAWtC,gBAAAA,AAAgB,GAAK7F,WAAmBwP,kBAAkB,CAEvE,GAAI,CAACD,EACH,MAAM,OAAA,QADoB,MAGzB,CAFK,AAAIjY,MACR,CAAC,sDAAsD,EAAE6X,EAAGhZ,QAAQ,GAAA,CAAI,EADpE,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAIF,IAAM4T,EACJD,GAAiBA,AAAuB,gBAATE,IAAI,CAC/BF,EAAcC,WAAW,CACzB,KACFA,GACFA,EAAYE,QADG,CACM,GAEvB,GAAI,CAKF,IAAMwF,EACJ3F,GAAwC,YAAvBA,EAAcE,IAAI,CAC/BF,OACA5Q,EACAF,EAAWyW,CAAAA,QAAAA,KAAAA,EAAAA,EAAcre,GAAG,CAAC4H,QAAAA,AAAQ,IAAImP,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAW3P,KAAAA,AAAK,GAAI,GAC7DmE,EAAe,IAAI+S,gBAAgBD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAcre,GAAG,CAACoL,MAAAA,AAAM,GAAI,IAK/DyT,EAHmB,AAGJN,IAHQhT,EAAaiT,IAAI,GAAG,CAACC,IAAI,CAAC,CAACC,EAAGC,IAClDD,EAAEE,aAAa,CAACD,IAGtBvV,GAAG,CAAElH,AAAD,GAAS,CAAA,EAAGA,EAAI,CAAC,EAAEqJ,EAAalI,GAAG,CAACnB,GAAAA,CAAM,EAC9C6H,IAAI,CAAC,KAKF+U,EAAgB,CAAA,EAAGb,EAAS,CAAC,EAAE3d,KAAKC,SAAS,CAACwO,GAAAA,CAAO,CACrDrN,EAAW,MAAM+S,EAAiBrU,gBAAgB,CAAC0e,GAEnD7G,EAAW,CAAC,eAAe,EAAErQ,EAAAA,EAAWiX,EAAa5c,MAAM,CAAG,IAAM,GAAA,EAAK4c,EAAa,CAAC,EAAEd,EAAGtQ,IAAI,CAAG,CAAC,CAAC,EAAEsQ,EAAGtQ,IAAI,CAAA,CAAE,CAAG/L,EAAAA,CAAU,CAC7H+Z,EACH1E,GAAYA,EAAUa,WAAW,CAAGiG,CAAAA,CAAc,EAAM,EAErD/D,EAAepB,QAAAA,KAAAA,EAAAA,EAAeoB,YAAY,CAE1CiF,EAAsC,CAC1CnG,KAAM,iBACNoG,MAAO,sBACPlF,EACAmF,UACEvG,GACA3B,GACAmI,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EAACnI,EAAW2B,EACjD,EAEA,GAAI3B,EAAW,CAQb,GAPAA,EAAUa,WAAW,CAAG6D,EAAW,EAQjC/C,IACwB,UAAvBA,EAAcE,CAAdF,GAAkB,EACM,cAAvBA,EAAcE,IAAI,EACK,kBAAvBF,EAAcE,IAAI,EACK,qBAAvBF,EAAcE,IAAI,AAAK,CAAiB,CAC1C,CAGkC,UAAU,AAAxC,OAAOpX,EAAQ+T,UAAU,GACvBmD,EAAcnD,UAAU,CAAG/T,EAAQ+T,UAAU,EAAE,CAGjDmD,EAAcnD,UAAU,CAAG/T,EAAQ+T,UAAAA,AAAU,GAKjD,IAAMsE,EAAgBnB,EAAcpC,IAAI,CACxC,GAAsB,MAAM,CAAxBuD,EACFnB,EAAcpC,IAAI,CAAGA,EAAKtO,KAAK,QAE/B,IAAK,IAAM0O,KAAOJ,EAEZ,AAACuD,EAAcxO,CAFG,OAEK,CAACqL,IAC1BmD,EADgC,AAClBrX,IAAI,CAACkU,EAI3B,CAIA,GAGE,CAFA,AACA,AACCyI,CAJDzG,GAAwC,mBAAvBA,EAAcE,IAAS,AAAL,GAKnC7B,AAAyB,YAFmB,SAElCiD,CAH4C,SAGlC,EACpB,CAACjD,EAAU7C,oBAAoB,EAC/B,CAACO,EAAiBP,oBAAoB,EACtC,CAAC6C,EAAUuC,WAAW,CACtB,CAEA,IAAMvG,EAAa,MAAM0B,EAAiBpR,GAAG,CAAC3B,EAAU,CACtDsR,KAAMtQ,EAAAA,oBAAoB,CAAC8Z,KAAK,CAChCjH,WAAY/T,EAAQ+T,UAAU,MAC9Be,EACA0G,QAAQ,CAAElD,MAAAA,EAAAA,KAAAA,EAAAA,EAAcxD,IAAI,UAC5BmF,WACAxD,CACF,GAEA,GAAIlF,GAAcA,EAAWhT,KAAK,CAEhC,CAFkC,EAE9BgT,EAAWhT,KAAK,CAACiT,IAAI,GAAKvQ,EAAAA,eAAe,CAAC+Z,KAAK,CAKjD9G,CALmD,OAK3CzP,KAAK,CACX,CAAC,0CAA0C,EAAE6Y,EAAAA,CAAe,MAGzD,CAGL,IAAM/J,OAC2BjN,IAA/BiL,EAAWhT,KAAK,CAAC0c,IAAI,CAACrd,IAAI,CACtBkB,KAAK8e,KAAK,CAACrM,EAAWhT,KAAK,CAAC0c,IAAI,CAACrd,IAAI,OACrC0I,EA+BN,OA9BIiL,EAAWU,OAAO,EAAE,CAElB,AAACsD,EAAUoG,kBAAkB,EAAE,CACjCpG,EAAUoG,kBAAkB,CAAG,EAAC,EAIlCpG,EAAUoG,kBAAkB,CAAC2B,EAAc,CACzC/G,EAAAA,oBAAoB,CACjBsH,GAAG,CAACN,EAAiBhB,KAAOhP,GAC5B3M,IAAI,CAAC,AAACqB,GACEqa,EACLra,IAEA/B,EACA4U,EACA9U,AAHAiT,EAGQc,UAAU,CAClBkG,EACAxD,IAIHyE,KAAK,CAAC,AAAChZ,GACNgS,QAAQzP,KAAK,CACX,CAAC,6BAA6B,EAAE6Y,EAAAA,CAAe,CAC/Cpb,KAKHqR,CACT,CAEJ,CAGA,IAAMtR,EAAS,MAAMsU,EAAAA,oBAAoB,CAACsH,GAAG,CAC3CN,EACAhB,KACGhP,GAeL,OAZI,AAACgI,EAAUuC,WAAW,EAAE,AAC1BwE,EACEra,IAEA/B,EACA4U,EAFA7B,AAGAjT,EAAQ+T,UAAU,CAClBkG,EACAxD,GAIGxU,CACT,CAAO,CAOL,GANAoa,GAAmB,EAMf,CAACpJ,EAAiBP,oBAAoB,CAAE,CAE1C,IAAMnB,EAAa,MAAM0B,EAAiBpR,GAAG,CAAC3B,EAAU,CACtDsR,KAAMtQ,EAAAA,oBAAoB,CAAC8Z,KAAK,CAChCjH,WAAY/T,EAAQ+T,UAAU,MAC9Be,WACAmF,WACAxD,EACA+E,QAAQ,CAAElD,MAAAA,EAAAA,KAAAA,EAAAA,EAAcxD,IAAI,AAC9B,GAEA,GAAIvD,GAAcA,EAAWhT,KAAK,EAAE,AAElC,GAAIgT,EAAWhT,KAAK,CAACiT,IAAI,GAAKvQ,EAAAA,eAAe,CAAC+Z,KAAK,CAIjD9G,CAJmD,OAI3CzP,KAAK,CACX,CAAC,0CAA0C,EAAE6Y,EAAAA,CAAe,OAGzD,GAAI,CAAC/L,EAAWU,OAAO,CAE5B,CAF8B,WAEQ3L,IAA/BiL,EAAWhT,KAAK,CAAC0c,IAAI,CAACrd,IAAI,CAC7BkB,KAAK8e,KAAK,CAACrM,EAAWhT,KAAK,CAAC0c,IAAI,CAACrd,IAAI,OACrC0I,CACN,CAEJ,CAGA,IAAMrE,EAAS,MAAMsU,EAAAA,oBAAoB,CAACsH,GAAG,CAC3CN,EACAhB,KACGhP,GAWL,OATA+O,EACEra,EAnOmB0a,EAqOnBzc,EACA4U,EAFA7B,AAGAjT,EAAQ+T,UAAU,CAClBkG,EACAxD,GAEKxU,CACT,CACF,QAAU,CACJkV,GACFA,EAAYsC,OAAO,CADJ,CAGnB,CACF,CAGF,4HC9HgBqE,qBAAqB,CAAA,kBAArBA,GAtBAC,eAAe,CAAA,kBAAfA,sEA3MhB,OAAMC,EAOJC,OAAOC,CAAe,CAAQ,CAC5B,IAAI,CAACC,OAAO,CAACD,EAAQ5Y,KAAK,CAAC,KAAKqS,MAAM,CAACC,SAAU,EAAE,EAAE,EACvD,CAEAwG,QAAmB,CACjB,OAAO,IAAI,CAACC,OAAO,EACrB,CAEQA,QAAQxZ,CAAoB,CAAY,CAAhCA,KAAAA,QAAAA,EAAiB,GAAA,EAC/B,IAAMyZ,EAAgB,IAAI,IAAI,CAACC,QAAQ,CAACvB,IAAI,GAAG,CAACC,IAAI,EAC9B,MAAM,EAAxB,IAAI,CAACuB,QAAQ,EACfF,EAAcG,MAAM,CAACH,EAAcrY,OAAO,CAAC,MAAO,GAE1B,MAAM,CAA5B,IAAI,CAACyY,YAAY,EACnBJ,EAAcG,MAAM,CAACH,EAAcrY,OAAO,CAAC,SAAU,GAErB,MAAM,CAApC,IAAI,CAAC0Y,oBAAoB,EAC3BL,EAAcG,MAAM,CAACH,EAAcrY,OAAO,CAAC,WAAY,GAGzD,IAAM2Y,EAASN,EACZ1W,GAAG,CAAC,AAACiX,GAAM,IAAI,CAACN,QAAQ,CAAC1c,GAAG,CAACgd,GAAIR,OAAO,CAAE,GAAExZ,EAASga,EAAE,MACvDC,MAAM,CAAC,CAACC,EAAMC,IAAS,IAAID,KAASC,EAAK,CAAE,EAAE,EAQhD,GANsB,MAAM,CAAxB,IAAI,CAACR,QAAQ,EACfI,EAAO5d,IAAI,IACN,IAAI,CAACud,QAAQ,CAAC1c,GAAG,CAAC,MAAOwc,OAAO,CAAIxZ,EAAO,IAAG,IAAI,CAAC2Z,QAAQ,CAAC,OAI/D,CAAC,IAAI,CAACS,WAAW,CAAE,CACrB,IAAMC,EAAe,MAAXra,EAAiB,IAAMA,EAAO2B,KAAK,CAAC,EAAG,CAAC,GAClD,GAAiC,MAAM,AAAnC,IAAI,CAACmY,oBAAoB,CAC3B,MAAM,OAAA,cAEL,CAFK,AAAIja,MACP,uFAAsFwa,EAAE,UAASA,EAAE,QAAO,IAAI,CAACP,oBAAoB,CAAC,SADjI,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAGFC,EAAOO,OAAO,CAACD,EACjB,CAkBA,OAhB0B,MAAM,CAA5B,IAAI,CAACR,YAAY,EACnBE,EAAO5d,IAAI,IACN,IAAI,CAACud,QAAQ,CACb1c,GAAG,CAAC,SACJwc,OAAO,CAAIxZ,EAAO,OAAM,IAAI,CAAC6Z,YAAY,CAAC,OAIf,MAAM,CAApC,IAAI,CAACC,oBAAoB,EAC3BC,EAAO5d,IAAI,IACN,IAAI,CAACud,QAAQ,CACb1c,GAAG,CAAC,WACJwc,OAAO,CAAIxZ,EAAO,QAAO,IAAI,CAAC8Z,oBAAoB,CAAC,QAInDC,CACT,CAEQT,QACNiB,CAAkB,CAClBC,CAAmB,CACnBC,CAAmB,CACb,CACN,GAAwB,IAApBF,EAAS3e,MAAM,CAAQ,CACzB,IAAI,CAACwe,WAAW,CAAG,GACnB,MACF,CAEA,GAAIK,EACF,MAAM,IADQ,GACR,cAAwD,CAAxD,AAAI5a,MAAO,+CAAX,oBAAA,OAAA,mBAAA,gBAAA,CAAuD,GAI/D,IAAI6a,EAAcH,CAAQ,CAAC,EAAE,CAG7B,GAAIG,EAAYza,UAAU,CAAC,MAAQya,EAAYlY,QAAQ,CAAC,KAAM,CAE5D,IAAImY,EAAcD,EAAY/Y,KAAK,CAAC,EAAG,CAAC,GAEpCiZ,GAAa,EAOjB,GANID,EAAY1a,UAAU,CAAC,MAAQ0a,EAAYnY,QAAQ,CAAC,MAAM,CAE5DmY,EAAcA,EAAYhZ,KAAK,CAAC,EAAG,CAAC,GACpCiZ,GAAa,GAGXD,EAAY1a,UAAU,CAAC,KACzB,CAD+B,KACzB,OAAA,cAEL,CAFK,AAAIJ,MACP,6CAA4C8a,EAAY,6BADrD,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GASF,GANIA,EAAY1a,UAAU,CAAC,QAAQ,CAEjC0a,EAAcA,EAAYnb,SAAS,CAAC,GACpCib,GAAa,GAGXE,EAAY1a,UAAU,CAAC,MAAQ0a,EAAYnY,QAAQ,CAAC,KACtD,CAD4D,KACtD,OAAA,cAEL,CAFK,AAAI3C,MACP,4DAA2D8a,EAAY,OADpE,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIA,EAAY1a,UAAU,CAAC,KACzB,CAD+B,KACzB,OAAA,cAEL,CAFK,AAAIJ,MACP,wDAAuD8a,EAAY,OADhE,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,SAASE,EAAWC,CAA2B,CAAEC,CAAgB,EAC/D,GAAqB,MAAM,CAAvBD,GAMEA,IAAiBC,EAEnB,MAAM,EAFuB,KAEvB,cAEL,CAFK,AAAIlb,MACP,mEAAkEib,EAAa,UAASC,EAAS,OAD9F,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAIJP,EAAUQ,OAAO,CAAC,AAACC,IACjB,GAAIA,IAASF,EACX,MAAM,EADe,KACf,cAEL,CAFK,AAAIlb,MACP,uCAAsCkb,EAAS,yCAD5C,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIE,EAAKja,OAAO,CAAC,MAAO,MAAQ0Z,EAAY1Z,OAAO,CAAC,MAAO,IACzD,CAD8D,KACxD,OAAA,cAEL,CAFSnB,AAAJ,MACH,mCAAkCob,EAAK,UAASF,EAAS,kEADtD,oBAAA,OAAA,mBAAA,eAAA,EAEN,EAEJ,GAEAP,EAAUre,IAAI,CAAC4e,EACjB,CAEA,GAAIN,EACF,GAAIG,EAAY,CACd,GAAyB,CAFb,KAER,AAA2B,IAAvB,CAACf,YAAY,CACnB,MAAM,OAAA,cAEL,CAFK,AAAIha,MACP,wFAAuF,IAAI,CAACga,YAAY,CAAC,WAAUU,CAAQ,CAAC,EAAE,CAAC,QAD5H,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAGFM,EAAW,IAAI,CAACf,oBAAoB,CAAEa,GAEtC,IAAI,CAACb,oBAAoB,CAAGa,EAE5BD,EAAc,SAChB,KAAO,CACL,GAAiC,MAAM,AAAnC,IAAI,CAACZ,oBAAoB,CAC3B,MAAM,OAAA,cAEL,CAFK,AAAIja,MACP,yFAAwF,IAAI,CAACia,oBAAoB,CAAC,YAAWS,CAAQ,CAAC,EAAE,CAAC,OADtI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGFM,EAAW,IAAI,CAAChB,YAAY,CAAEc,GAE9B,IAAI,CAACd,YAAY,CAAGc,EAEpBD,EAAc,OAChB,KACK,CACL,GAAIE,EACF,MAAM,IADQ,GACR,cAEL,CAFK,AAAI/a,MACP,qDAAoD0a,CAAQ,CAAC,EAAE,CAAC,OAD7D,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEFM,EAAW,IAAI,CAAClB,QAAQ,CAAEgB,GAE1B,IAAI,CAAChB,QAAQ,CAAGgB,EAEhBD,EAAc,IAChB,CACF,CAGK,AAAD,IAAK,CAAChB,QAAQ,CAACwB,GAAG,CAACR,IACrB,IAAI,CAAChB,KAD8B,GACtB,CAACvc,GAAG,CAACud,EAAa,IAAIvB,GAGrC,IAAI,CAACO,QAAQ,CACV1c,GAAG,CAAC0d,GACJpB,OAAO,CAACiB,EAAS5Y,KAAK,CAAC,GAAI6Y,EAAWC,EAC3C,oBAvMAL,WAAAA,EAAuB,OACvBV,QAAAA,CAAiC,IAAI/c,SACrCgd,QAAAA,CAA0B,UAC1BE,YAAAA,CAA8B,UAC9BC,oBAAAA,CAAsC,KAoMxC,CAEO,SAASZ,EACdiC,CAAsC,EAatC,IAAMC,EAAO,IAAIjC,EAKjB,OAFAgC,EAAgBH,OAAO,CAAEK,AAAD,GAAcD,EAAKhC,MAAM,CAACiC,IAE3CD,EAAK7B,MAAM,EACpB,CAEO,SAASN,EACdqC,CAAY,CACZC,CAA0B,EAI1B,IAAMC,EAAkC,CAAC,EACnCC,EAAsB,EAAE,CAC9B,IAAK,IAAI/f,EAAI,EAAGA,EAAI4f,EAAQ1f,MAAM,CAAEF,IAAK,CACvC,IAAM6F,EAAWga,EAAOD,CAAO,CAAC5f,EAAE,EAClC8f,CAAO,CAACja,EAAS,CAAG7F,EACpB+f,CAAS,CAAC/f,EAAE,CAAG6F,CACjB,CAOA,OAJe2X,AAIRwC,EAJwBD,GAIjB1Y,GAAG,CAAC,AAACxB,GAAa+Z,CAAO,CAACE,CAAO,CAACja,EAAS,CAAC,CAC5D,4HClPaoa,0BAA0B,CAAA,kBAA1BA,GAkBGC,mCAAmC,CAAA,kBAAnCA,GAXAC,0BAA0B,CAAA,kBAA1BA,+EAViB,CAAA,CAAA,IAAA,IAGpBF,EAA6B,CACxC,WACA,MACA,OACA,QACD,CAEM,SAASE,EAA2B3a,CAAY,EAErD,OAKUO,SAJRP,EACGT,KAAK,CAAC,KACNqb,IAAI,CAAE7Y,AAAD,GACJ0Y,EAA2BG,IAAI,CAAC,AAACC,GAAM9Y,EAAQhD,UAAU,CAAC8b,IAGlE,CAEO,SAASH,EAAoC1a,CAAY,EAC9D,IAAI8a,EACFC,EACAC,EAEF,IAAK,IAAMjZ,KAAW/B,EAAKT,KAAK,CAAC,KAAM,AAErC,GADAwb,CACIA,CADKN,EAA2BG,IAAI,CAAC,AAACC,GAAM9Y,EAAQhD,UAAU,CAAC8b,IACvD,CACT,CAACC,EAAmBE,EAAiB,CAAGhb,EAAKT,KAAK,CAACwb,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACC,EACpC,MAAM,OAAA,GADgD,WAGrD,CAFK,AAAIrc,MACP,+BAA8BqB,EAAK,qFADhC,oBAAA,OAAA,kBAAA,gBAAA,EAEN,GAKF,OAFA8a,EAAoBG,CAAAA,EAAAA,EAAAA,gBAAgB,AAAhBA,EAAiBH,GAE7BC,GACN,IAAK,MAGDC,EADwB,CAL0B,IAKrB,CAA3BF,EACkB,IAAGE,EAEJF,EAAoB,IAAME,EAE/C,KACF,KAAK,OAEH,GAA0B,KAAK,CAA3BF,EACF,AAdmG,MAc7F,OAAA,cAEL,CAFK,AAAInc,MACP,+BAA8BqB,EAAK,gEADhC,oBAAA,OAAA,mBAAA,eAAA,EAEN,GAEFgb,EAAmBF,EAChBvb,KAAK,CAAC,KACNkB,KAAK,CAAC,EAAG,CAAC,GACVya,MAAM,CAACF,GACPxY,IAAI,CAAC,KACR,KACF,KAAK,QAEHwY,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAMG,EAAyBL,EAAkBvb,KAAK,CAAC,KACvD,GAAI4b,EAAuBzgB,MAAM,EAAI,EACnC,CADsC,KAChC,OAAA,cAEL,CAFK,AAAIiE,MACP,+BAA8BqB,EAAK,mEADhC,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGFgb,EAAmBG,EAChB1a,KAAK,CAAC,EAAG,CAAC,GACVya,MAAM,CAACF,GACPxY,IAAI,CAAC,KACR,KACF,SACE,MAAM,OAAA,cAAyC,CAAzC,AAAI7D,MAAM,gCAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAwC,EAClD,CAEA,MAAO,mBAAEmc,mBAAmBE,CAAiB,CAC/C,6ICtEgBI,iBAAAA,qCAAAA,aAfT,CAAA,CAAA,IAAA,IAGDC,EAAa,gCAGbC,EAAoB,sBASnB,SAASF,EAAevb,CAAa,CAAE0b,CAAsB,QAKlE,CAL4CA,KAAAA,IAAAA,IAAAA,GAAkB,CAAA,EAC1DZ,CAAAA,EAAAA,EAAAA,0BAAAA,AAA0B,EAAC9a,KAC7BA,EAAQ6a,CAD6B,AAC7BA,EAAAA,EAAAA,mCAAAA,AAAmC,EAAC7a,GAAOmb,gBAAgB,AAAhBA,EAGjDO,GACKD,EAAkBnd,GADf,CACmB,CAAC0B,GAGzBwb,EAAWld,IAAI,CAAC0B,EACzB,4HC5B0BkY,qBAAqB,CAAA,kBAArBA,EAAAA,qBAAqB,EAAtCC,eAAe,CAAA,kBAAfA,EAAAA,eAAe,EACfoD,cAAc,CAAA,kBAAdA,EAAAA,cAAc,8EADgC,CAAA,CAAA,IAAA,QACxB,CAAA,CAAA,IAAA,8HC+DfI,cAAc,CAAA,kBAAdA,GA9CAC,aAAa,CAAA,kBAAbA,GASAC,mBAAmB,CAAA,kBAAnBA,GA4BAC,kBAAkB,CAAA,kBAAlBA,+EApDT,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,QAIxB,CAAA,CAAA,IAAA,QAC0B,CAAA,CAAA,IAAA,OACI,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,IAO5B,SAASF,EAActM,CAAW,EACvC,OAAOnB,EAAW,CAACmB,EAAI,CAAE,CAAC,cAAc,EAAEA,EAAAA,CAAK,CACjD,CAOO,SAASuM,EACdE,CAAoB,CACpBvK,CAAwB,EAExB,GAAIuK,EAAalhB,MAAM,CAAG9G,EAAAA,8BAA8B,CAAE,YACxDua,QAAQkB,IAAI,CACV,CAAC,8BAA8B,EAAEuM,EAAa,+BAA+B,EAAEhoB,EAAAA,8BAA8B,CAAC,4FAA4F,CAAC,EAK/M,IAAIioB,EAAiB,CAAA,EAAGpoB,EAAAA,0BAA0B,CAAA,EAAGmoB,EAAAA,CAAc,CASnE,OAPIvK,EACFwK,GAAkB,CADV,AACU,EAAGA,EAAeva,QAAQ,CAAC,KAAO,GAAK,IAAA,EAAM+P,EAAAA,CAAM,CAC5D+J,GAAAA,EAAAA,cAAc,AAAdA,EAAeQ,IACxBzN,QAAQkB,GAD+B,CAC3B,CACV,CAAC,8BAA8B,EAAEuM,EAAa,4LAA4L,CAAC,EAGxO5N,EAAW,CAAC6N,EAAe,CAAE,CAAC,oBAAoB,EAAED,EAAAA,CAAc,CAC3E,CAOO,SAASD,EAAmB,GAAG5M,CAAc,EAClD,OAAOf,EAAWe,EAAM,CAAC,mBAAmB,EAAEA,EAAKvM,IAAI,CAAC,MAAA,CAAO,CACjE,CAOO,SAASgZ,EAAeI,CAAoB,CAAEvK,CAAwB,EAC3E,GAAIuK,EAAalhB,MAAM,CAAG9G,EAAAA,8BAA8B,CAAE,YACxDua,QAAQkB,IAAI,CACV,CAAC,kCAAkC,EAAEuM,EAAa,+BAA+B,EAAEhoB,EAAAA,8BAA8B,CAAC,uFAAuF,CAAC,EAK9M,IAAIioB,EAAiB,CAAA,EAAGpoB,EAAAA,0BAA0B,CAAA,EAAGmoB,EAAAA,CAAc,CASnE,OAPIvK,EACFwK,GAAkB,CADV,AACU,EAAGA,EAAeva,QAAQ,CAAC,KAAO,GAAK,IAAA,EAAM+P,EAAAA,CAAM,CAC5D+J,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACQ,IACxBzN,QAAQkB,GAD+B,CAC3B,CACV,CAAC,8BAA8B,EAAEuM,EAAa,2LAA2L,CAAC,EAGvO5N,EAAW,CAAC6N,EAAe,CAAE,CAAC,eAAe,EAAED,EAAAA,CAAc,CACtE,CAEA,SAAS5N,EAAWe,CAAc,CAAE+M,CAAkB,EACpD,IAAMC,EAAQxL,EAAAA,gBAAgB,CAACW,QAAQ,GACvC,GAAI,CAAC6K,GAAS,CAACA,EAAM7O,gBAAgB,CACnC,CADqC,KAC/B,OAAA,cAEL,CAFK,AAAIvO,MACR,CAAC,8CAA8C,EAAEmd,EAAAA,CAAY,EADzD,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAM3K,EAAgBX,EAAAA,oBAAoB,CAACU,QAAQ,GACnD,GAAIC,EAAe,CACjB,GAA2B,SAAS,CAAhCA,EAAcE,IAAI,CACpB,MAAM,OAAA,cAEL,CAFK,AAAI1S,MACR,CAAC,MAAM,EAAEod,EAAMlc,KAAK,CAAC,OAAO,EAAEic,EAAW,qRAAqR,CAAC,EAD3T,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GACK,GAA2B,kBAAkB,CAAzC3K,EAAcE,IAAI,CAC3B,MAAM,OAAA,cAEL,CAFK,AAAI1S,MACR,CAAC,MAAM,EAAEod,EAAMlc,KAAK,CAAC,OAAO,EAAEic,EAAW,oTAAoT,CAAC,EAD1V,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,GAA4B,UAAU,CAAlC3K,EAAcsG,KAAK,CACrB,MAAM,OAAA,cAEL,CAFK,AAAI9Y,MACR,CAAC,MAAM,EAAEod,EAAMlc,KAAK,CAAC,OAAO,EAAEic,EAAW,8QAA8Q,CAAC,EADpT,oBAAA,OAAA,iBAAA,gBAAA,CAEN,GAGF,GAA2B,cAAvB3K,EAAcE,IAAI,CAAkB,CAEtC,IAAM3S,EAAQ,OAAA,cAEb,CAFa,AAAIC,MAChB,CAAC,MAAM,EAAEod,EAAMlc,KAAK,CAAC,MAAM,EAAEic,EAAW,8CAA8C,CAAC,EAD3E,oBAAA,OAAA,mBAAA,gBAAA,CAEd,GACAE,CAAAA,EAAAA,EAAAA,2CAA2C,AAA3CA,EACED,EAAMlc,KAAK,CACXic,EACApd,EACAyS,EAEJ,MAAO,GAA2B,iBAAiB,CAAxCA,EAAcE,IAAI,CAE3B4K,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClBF,EAAMlc,KAAK,CACXic,EACA3K,EAAc+K,eAAe,OAE1B,GAA2B,qBAAvB/K,EAAcE,IAAI,CAAyB,CAEpDF,EAAcnD,UAAU,CAAG,EAE3B,IAAM7R,EAAM,OAAA,cAEX,CAFW,IAAIggB,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEJ,EAAMlc,KAAK,CAAC,mDAAmD,EAAEic,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHAC,EAAMK,uBAAuB,CAAGN,EAChCC,EAAMM,iBAAiB,CAAGlgB,EAAImgB,KAAK,CAE7BngB,CACR,CAOF,CAMA,IAAK,CAbI,GAaEgT,CAZP9L,IAQA,AAAC0Y,EAAMQ,EARCjZ,GAAG,CAAC2M,QAAQ,KAAK,GAQI,EAAE,CACjC8L,EAAMQ,EANJ,OAFApL,aAQ0B,CAAG,EAAA,AAAE,CAP/BA,CAUcpC,GACZ,AAACgN,EADiB,AACXQ,QAXKlL,IAAI,KAAK,KAWQ,CAACvN,QAAQ,CAACqL,IACzC4M,EAAMQ,AADyC,sBACnB,CAACthB,IAAI,CAACkU,GAKtC4M,EAAMS,kBAAkB,EAAG,CAC7B,6IC5IgBC,mBAAAA,qCAAAA,aAnBiB,CAAA,CAAA,IAAA,OACI,CAAA,CAAA,IAAA,QACK,CAAA,CAAA,IAAA,IAiBnC,SAASA,IAEd,IAAMV,EAAQxL,EAAAA,gBAAgB,CAACW,QAAQ,GACjCC,EAAgBX,EAAAA,oBAAoB,CAACU,QAAQ,GACnD,GAAK6K,CAAD,EAKOA,EAAMlI,EALL,SAKgB,EAAE,CAG5BkI,EAAMpJ,iBAAiB,EAAG,EACtBxB,GAAwC,aAAa,CAApCA,EAAcE,IAAI,EAGrCyC,CAAAA,EAAAA,EAAAA,yBAAAA,AAAyB,EAACiI,EAAO5K,EAfX,aAe0BuL,SAGtD,mECgDO,SAASC,EAAUE,CAAsC,EAE5D,MAAM,OAAA,cAEL,CAFK,AAAIle,MACR,wEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAmFJ,0EAvFgBge,YAAAA,qCAAAA,OAtFiB,CAAA,CAAA,IAAA,KACI,CAAA,CAAA,IAAA,oECE9B,SAASW,EAAS,GAAGvO,CAAc,EAEtC,MAAM,OAAA,cAEL,CAFK,AAAIpQ,MACR,uEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAiBJ,0EArBgB2e,WAAAA,qCAAAA,OAHqB,CAAA,CAAA,IAAA,MACR,CAAA,CAAA,IAAA,wDCD7B,IAAM,EAAe,CACnB,eAAgB,EAAA,CAAA,CAAA,QACb,cAAc,CAEjB,cAAe,EAAA,CAAA,CAAA,QACZ,aAAa,CAChB,eAAgB,EAAA,CAAA,CAAA,QACb,cAAc,CAEjB,mBAAoB,EAAA,CAAA,CAAA,QACjB,kBAAkB,CACrB,oBAAqB,EAAA,CAAA,CAAA,QAClB,mBAAmB,CAEtB,iBACE,EAAA,CAAA,CAAA,QACG,gBAAgB,CACrB,mBAAoB,EAAA,CAAA,CAAA,QACjB,SAAS,CACZ,kBAAmB,EAAA,CAAA,CAAA,QAAgD,QACrE,AAD6E,EAK7E,EAAO,OAAO,CAAG,EAGjB,EAAQ,cAAc,CAAG,EAAa,cAAc,CACpD,EAAQ,cAAc,CAAG,EAAa,cAAc,CACpD,EAAQ,aAAa,CAAG,EAAa,aAAa,CAClD,EAAQ,kBAAkB,CAAG,EAAa,kBAAkB,CAC5D,EAAQ,mBAAmB,CAAG,EAAa,mBAAmB,CAC9D,EAAQ,gBAAgB,CAAG,EAAa,gBAAgB,CACxD,EAAQ,kBAAkB,CAAG,EAAa,kBAAkB,CAC5D,EAAQ,iBAAiB,CAAG,EAAa,iBAAiB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}