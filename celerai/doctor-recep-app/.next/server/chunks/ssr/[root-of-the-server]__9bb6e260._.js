module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/analytics.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "initializeAnalytics": (()=>initializeAnalytics),
    "trackAuth": (()=>trackAuth),
    "trackConsultation": (()=>trackConsultation),
    "trackEvent": (()=>trackEvent),
    "trackPageView": (()=>trackPageView),
    "trackQuotaWarning": (()=>trackQuotaWarning)
});
'use client';
// Check if user is in authenticated app
function isAuthenticatedRoute() {
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
    const authenticatedPaths = undefined;
}
// Allowlist of safe events for authenticated zone
const SAFE_AUTHENTICATED_EVENTS = [
    'consultation_generated',
    'summary_approved',
    'quota_warning',
    'dashboard_viewed',
    'settings_accessed',
    'template_updated'
];
// Scrub any potentially sensitive parameters
function scrubParameters(params) {
    const scrubbed = {};
    // Only allow specific safe parameters
    const allowedParams = [
        'page_title',
        'consultation_type',
        'quota_percentage',
        'feature_used',
        'error_type'
    ];
    for (const [key, value] of Object.entries(params)){
        if (allowedParams.includes(key) && value !== undefined) {
            // Additional validation for specific params
            if (key === 'quota_percentage' && typeof value === 'number') {
                scrubbed[key] = Math.round(value) // Round to avoid precision-based identification
                ;
            } else if (typeof value === 'string' && value.length < 100) {
                scrubbed[key] = value;
            } else if (typeof value === 'boolean') {
                scrubbed[key] = value;
            }
        }
    }
    return scrubbed;
}
function trackEvent(event, parameters = {}) {
    // Only track in browser environment
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function trackPageView(pageTitle) {
    const isAuthenticated = isAuthenticatedRoute();
    if (isAuthenticated) {
        // In authenticated zone, only track generic dashboard view
        trackEvent('dashboard_viewed', {
            page_title: 'Dashboard'
        });
    } else {
        // In public zone, track full page view
        trackEvent('page_view', {
            page_title: pageTitle
        });
    }
}
function trackConsultation(type, consultationType) {
    if (!isAuthenticatedRoute()) return;
    if (type === 'generated') {
        trackEvent('consultation_generated', {
            consultation_type: consultationType
        });
    } else if (type === 'approved') {
        trackEvent('summary_approved', {
            consultation_type: consultationType
        });
    }
}
function trackQuotaWarning(percentage) {
    if (!isAuthenticatedRoute()) return;
    trackEvent('quota_warning', {
        quota_percentage: percentage
    });
}
function trackAuth(type) {
    // Only track in public zone
    if (isAuthenticatedRoute()) return;
    trackEvent(type);
}
function initializeAnalytics() {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
}}),
"[project]/src/components/analytics/analytics-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnalyticsProvider": (()=>AnalyticsProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analytics.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function AnalyticsProvider({ children }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Lazy initialize analytics after page is fully loaded
        const timer = setTimeout(()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initializeAnalytics"])();
        }, 100) // Small delay to ensure page is interactive
        ;
        return ()=>clearTimeout(timer);
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Lazy track page changes with debouncing
        const timer = setTimeout(()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trackPageView"])(document.title);
        }, 50);
        return ()=>clearTimeout(timer);
    }, [
        pathname
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9bb6e260._.js.map