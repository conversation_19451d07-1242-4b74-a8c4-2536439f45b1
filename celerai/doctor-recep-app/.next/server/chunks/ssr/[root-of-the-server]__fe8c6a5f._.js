module.exports={348629:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},30331:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("path",()=>require("path"))},124210:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"warnOnce",{enumerable:!0,get:function(){return a}});let a=a=>{}}},296565:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return f}});let b=a.r(722851),c="undefined"==typeof window,d=c?()=>{}:b.useLayoutEffect,g=c?()=>{}:b.useEffect;function f(a){let{headManager:e,reduceComponentsToState:f}=a;function h(){if(e&&e.mountedInstances){let c=b.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(f(c,a))}}if(c){var i;null==e||null==(i=e.mountedInstances)||i.add(a.children),h()}return d(()=>{var b;return null==e||null==(b=e.mountedInstances)||b.add(a.children),()=>{var b;null==e||null==(b=e.mountedInstances)||b.delete(a.children)}}),d(()=>(e&&(e._pendingUpdate=h),()=>{e&&(e._pendingUpdate=h)})),g(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}}},133183:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";d.exports=a.r(450442).vendored.contexts.AmpContext},406036:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return f}})},585410:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={default:function(){return r},defaultHead:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(995658),c=a.r(465578),k=a.r(674420),l=c._(a.r(722851)),m=b._(a.r(296565)),n=a.r(133183),o=a.r(598806),p=a.r(406036);function h(a){void 0===a&&(a=!1);let b=[(0,k.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,k.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function i(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===l.default.Fragment?a.concat(l.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}a.r(124210);let q=["name","httpEquiv","charSet","itemProp"];function j(a,b){let{inAmpMode:c}=b;return a.reduce(i,[]).reverse().concat(h(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=q.length;a<b;a++){let b=q[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let d=a.key||b;if(process.env.__NEXT_OPTIMIZE_FONTS&&!c&&"link"===a.type&&a.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(b=>a.props.href.startsWith(b))){let b={...a.props||{}};return b["data-href"]=b.href,b.href=void 0,b["data-optimized-fonts"]=!0,l.default.cloneElement(a,b)}return l.default.cloneElement(a,{key:d})})}let r=function(a){let{children:b}=a,c=(0,l.useContext)(n.AmpStateContext),d=(0,l.useContext)(o.HeadManagerContext);return(0,k.jsx)(m.default,{reduceComponentsToState:j,headManager:d,inAmpMode:(0,p.isInAmpMode)(c),children:b})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},420467:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("events",()=>require("events"))},804713:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("util",()=>require("util"))},936121:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:util",()=>require("node:util"))},341460:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@opentelemetry/api",()=>require("@opentelemetry/api"))},3254:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("perf_hooks",()=>require("perf_hooks"))},872978:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("require-in-the-middle",()=>require("require-in-the-middle"))},718741:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("import-in-the-middle",()=>require("import-in-the-middle"))},683886:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("fs",()=>require("fs"))},156116:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:diagnostics_channel",()=>require("node:diagnostics_channel"))},686513:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("diagnostics_channel",()=>require("diagnostics_channel"))},446309:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:child_process",()=>require("node:child_process"))},598701:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:fs",()=>require("node:fs"))},249934:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:os",()=>require("node:os"))},578579:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:path",()=>require("node:path"))},140945:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:readline",()=>require("node:readline"))},356103:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:worker_threads",()=>require("node:worker_threads"))},681635:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:http",()=>require("node:http"))},979966:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("async_hooks",()=>require("async_hooks"))},866098:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:https",()=>require("node:https"))},109175:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:stream",()=>require("node:stream"))},753652:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:zlib",()=>require("node:zlib"))},861255:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:net",()=>require("node:net"))},96856:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("node:tls",()=>require("node:tls"))},565126:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("worker_threads",()=>require("worker_threads"))},113442:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("os",()=>require("os"))},611861:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("process",()=>require("process"))},87485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("child_process",()=>require("child_process"))},815650:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("module",()=>require("module"))},749235:a=>{var{g:b,__dirname:c}=a;a.v(JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"node:sea":[">= 20.12 && < 21",">= 21.7"],"smalloc":">= 0.11.5 && < 3","node:sqlite":[">= 22.13 && < 23",">= 23.4"],"_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"test/mock_loader":">= 22.3 && < 22.7","node:test/mock_loader":">= 22.3 && < 22.7","node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}'))},501466:a=>{var{g:b,__dirname:c}=a;a.v(JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}'))},580162:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("tty",()=>require("tty"))},930466:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={NEXT_REQUEST_META:function(){return a},addRequestMeta:function(){return j},getRequestMeta:function(){return h},removeRequestMeta:function(){return k},setRequestMeta:function(){return i}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a=Symbol.for("NextInternalRequestMeta");function h(b,c){let d=b[a]||{};return"string"==typeof c?d[c]:d}function i(b,c){return b[a]=c,c}function j(a,b,c){let d=h(a);return d[b]=c,i(a,d)}function k(a,b){let c=h(a);return delete c[b],i(a,c)}}},292700:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return k}});let b=a.r(995658),c=a.r(674420),g=b._(a.r(722851)),h=b._(a.r(585410)),i={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function f(b){let c,{req:d,res:e,err:f}=b,g=e&&e.statusCode?e.statusCode:f?f.statusCode:404;if("undefined"!=typeof window)c=window.location.hostname;else if(d){let{getRequestMeta:b}=a.r(930466),e=b(d,"initURL");e&&(c=new URL(e).hostname)}return{statusCode:g,hostname:c}}let j={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class k extends g.default.Component{render(){let{statusCode:a,withDarkMode:b=!0}=this.props,d=this.props.title||i[a]||"An unexpected error has occurred";return(0,c.jsxs)("div",{style:j.error,children:[(0,c.jsx)(h.default,{children:(0,c.jsx)("title",{children:a?a+": "+d:"Application error: a client-side exception has occurred"})}),(0,c.jsxs)("div",{style:j.desc,children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(b?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),a?(0,c.jsx)("h1",{className:"next-error-h1",style:j.h1,children:a}):null,(0,c.jsx)("div",{style:j.wrap,children:(0,c.jsxs)("h2",{style:j.h2,children:[this.props.title||a?d:(0,c.jsxs)(c.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,c.jsxs)(c.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}k.displayName="ErrorPage",k.getInitialProps=f,k.origGetInitialProps=f,("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},459536:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(292700)},285390:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>h});var d=a.i(674420),e=a.i(973304),f=a.i(459536),g=a.i(722851);function h({error:a}){return(0,g.useEffect)(()=>{(0,e.captureException)(a)},[a]),(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:(0,d.jsx)(f.default,{statusCode:0})})})}},173825:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[externals]_node:inspector_10d23a46._.js"].map(b=>a.l(b))).then(()=>b(2885)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__fe8c6a5f._.js.map