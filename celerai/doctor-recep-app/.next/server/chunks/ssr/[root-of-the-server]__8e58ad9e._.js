module.exports={329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},137496:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSession:()=>i,decrypt:()=>h,deleteSession:()=>l,encrypt:()=>g,refreshSession:()=>k,updateSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify session"),null}}async function i(a){let b=new Date(Date.now()+6048e5),c=await g({userId:a,expiresAt:b}),d=await (0,f.cookies)();console.log("DEBUG: Creating session for user:",a),console.log("DEBUG: Session expires at:",b),d.set("session",c,{httpOnly:!0,secure:!1,expires:b,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("session")?.value,c=await h(b);if(console.log("DEBUG: Updating session - session exists:",!!b),console.log("DEBUG: Updating session - payload valid:",!!c),!b||!c)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let d=new Date(Date.now()+6048e5);a.set("session",b,{httpOnly:!0,secure:!1,expires:d,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function k(a){console.log("DEBUG: Refreshing session for user:",a),await l(),await i(a),console.log("DEBUG: Session refresh completed")}async function l(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting session cookie"),a.delete("session"),console.log("DEBUG: Session cookie deleted")}}},76803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkSession:()=>c,getDoctorQuota:()=>k,getUser:()=>i,getUserById:()=>j,verifySession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(137496),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId||(0,f.redirect)("/login"),{isAuth:!0,userId:c.userId}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId?{isAuth:!0,userId:c.userId}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("*").eq("id",a.userId).single();if(d)return console.error("Failed to fetch user:",d.message||d),null;if(!c)return null;return{...c,password_hash:c.password_hash}}catch(a){return console.error("Failed to fetch user:",a instanceof Error?a.message:a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",a).single();if(d)return console.error("Failed to fetch user by ID:",d.message||d),null;if(!c)return null;return{...c}}catch(a){return console.error("Failed to fetch user by ID:",a instanceof Error?a.message:a),null}}),k=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",a).single();if(d)return console.error("Failed to fetch quota:",d.message||d),null;let e=c.monthly_quota-c.quota_used,f=Math.round(c.quota_used/c.monthly_quota*100),g=new Date(c.quota_reset_at),i=Math.ceil((g.getTime()-Date.now())/864e5);return{monthly_quota:c.monthly_quota,quota_used:c.quota_used,quota_remaining:e,quota_percentage:f,quota_reset_at:c.quota_reset_at,days_until_reset:Math.max(0,i)}}catch(a){return console.error("Failed to fetch quota:",a instanceof Error?a.message:a),null}})}},81725:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},654129:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},870110:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({STORAGE_CONFIG:()=>b,calculateTotalFileSize:()=>l,deleteFile:()=>i,downloadFile:()=>k,extractFilePathFromUrl:()=>j,generateStoragePath:()=>f,uploadFile:()=>g,uploadMultipleFiles:()=>h,validateFile:()=>e,validateTotalSize:()=>m}),a.i(81725);var d=a.i(654129);let b={BUCKET_NAME:process.env.R2_BUCKET_NAME||"celerai-storage",PUBLIC_URL:process.env.R2_PUBLIC_URL||"https://celerai.tallyup.pro",AUDIO_PREFIX:"consultation-audio",IMAGE_PREFIX:"consultation-images",MAX_FILE_SIZE:0x6400000,MAX_TOTAL_SIZE:0xc800000,ALLOWED_AUDIO_TYPES:["audio/webm","audio/mp3","audio/wav","audio/m4a","audio/mpeg","audio/mp4","audio/ogg"],ALLOWED_IMAGE_TYPES:["image/jpeg","image/jpg","image/png","image/webp","image/heic"],RETENTION_DAYS:30},c=()=>new d.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID||"57014886c6cd87ebacf23a94e56a6e0c"}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID||"4dff08f96bf2f040b48bf3973813f7f0",secretAccessKey:process.env.R2_SECRET_ACCESS_KEY||"****************************************************************"}});function e(a,c){return a.size>b.MAX_FILE_SIZE?{valid:!1,error:`File size exceeds ${b.MAX_FILE_SIZE/1024/1024}MB limit`}:("audio"===c?b.ALLOWED_AUDIO_TYPES:b.ALLOWED_IMAGE_TYPES).includes(a.type)?{valid:!0}:{valid:!1,error:`File type ${a.type} is not allowed`}}function f(a,c,d,e){let f=d.replace(/[^a-zA-Z0-9.-]/g,"_"),g="audio"===e?b.AUDIO_PREFIX:b.IMAGE_PREFIX;return`${g}/${a}/${c}/${f}`}async function g(a,g,h,i){try{let j=e(a,i);if(!j.valid)return{success:!1,error:j.error};let k=c(),l=f(g,h,a.name,i),m=await a.arrayBuffer(),n=new d.PutObjectCommand({Bucket:b.BUCKET_NAME,Key:l,Body:new Uint8Array(m),ContentType:a.type,CacheControl:"public, max-age=3600"});await k.send(n);let o=`${b.PUBLIC_URL}/${l}`;return{success:!0,url:o}}catch(a){return console.error("R2 upload error:",a),{success:!1,error:`Upload failed: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(a,b,c,d){let e=await Promise.all(a.map(a=>g(a,b,c,d))),f=e.filter(a=>a.success),h=e.filter(a=>!a.success);return h.length>0?{success:!1,errors:h.map(a=>a.error||"Unknown error")}:{success:!0,urls:f.map(a=>a.url).filter(Boolean)}}async function i(a,e){try{let e=c(),f=new d.DeleteObjectCommand({Bucket:b.BUCKET_NAME,Key:a});return await e.send(f),{success:!0}}catch(a){return console.error("R2 delete error:",a),{success:!1,error:a instanceof Error?a.message:"Delete failed"}}}function j(a,c){try{let d="audio"===c?b.AUDIO_PREFIX:b.IMAGE_PREFIX,e=`/${d}/`,f=a.indexOf(e);if(-1===f)return null;return a.substring(a.indexOf(d))}catch{return null}}async function k(a,c){try{let c=`${b.PUBLIC_URL}/${a}`,d=await fetch(c);if(!d.ok)throw Error(`HTTP ${d.status}: ${d.statusText}`);let e=await d.blob();return{success:!0,data:e}}catch(a){return console.error("R2 download error:",a),{success:!1,error:a instanceof Error?a.message:"Download failed"}}}function l(a){return a.reduce((a,b)=>a+b.size,0)}function m(a){return l(a)>b.MAX_TOTAL_SIZE?{valid:!1,error:`Total file size exceeds ${b.MAX_TOTAL_SIZE/1024/1024}MB limit`}:{valid:!0}}}},480888:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardSkeleton:()=>b,ConsultationsListSkeleton:()=>c,DashboardSkeleton:()=>e,DashboardStatsSkeleton:()=>f,InfoPageSkeleton:()=>g,QuotaCardSkeleton:()=>h,ReferralStatsSkeleton:()=>i});var d=a.i(77624);let b=(0,d.registerClientReference)(function(){throw Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","AdminDashboardSkeleton"),c=(0,d.registerClientReference)(function(){throw Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","ConsultationsListSkeleton"),e=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","DashboardSkeleton"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","DashboardStatsSkeleton"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","InfoPageSkeleton"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","QuotaCardSkeleton"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","ReferralStatsSkeleton")}},473352:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardSkeleton:()=>b,ConsultationsListSkeleton:()=>c,DashboardSkeleton:()=>e,DashboardStatsSkeleton:()=>f,InfoPageSkeleton:()=>g,QuotaCardSkeleton:()=>h,ReferralStatsSkeleton:()=>i});var d=a.i(77624);let b=(0,d.registerClientReference)(function(){throw Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","AdminDashboardSkeleton"),c=(0,d.registerClientReference)(function(){throw Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","ConsultationsListSkeleton"),e=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","DashboardSkeleton"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","DashboardStatsSkeleton"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","InfoPageSkeleton"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","QuotaCardSkeleton"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","ReferralStatsSkeleton")}},968123:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(480888);var d=a.i(473352);a.n(d)},51346:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(755284),a.i(230122),a.i(957782)},211431:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(755284),a.i(230122),a.i(957782),a.i(51346)},622299:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c":()=>d.getConsultationStats,"008a705899f288d02ab102cb7194752ef57be976ca":()=>e.logout,"40181945d99bd1b1c806279418d8c4e384a1b0bd15":()=>d.getConsultations,"40757964b8c9b072995f44631743e71306c1784480":()=>f.getReferralInfo,"40cad8ac11d66cae6a36deaf0347461ff74e2f07d8":()=>d.clearEditedNote,"40d596bef0460f198d588bbf8dede026bc85cca740":()=>d.createConsultation,"6016d1147dadc5ab75f7387f44ab799f7cd595708b":()=>d.updateConsultationImages,"60648db8471ac7da6666e2d2e8b2cf27a97b9b4688":()=>d.approveConsultation,"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7":()=>d.updateAdditionalNotes,"608aaaf92844312537830e8c8d0c49debb89bda21b":()=>d.saveStreamingSummary,"6090ac31595b581fab8f32262309efcdcab1c7eb47":()=>d.saveEditedNote,"60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb":()=>d.updateConsultationType,"609aacf458083825c0e86748f13c86d5773450588b":()=>d.updatePatientName,"60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e":()=>d.addAdditionalImages,"60b3bf115b7639c2355ff16ee9fa53425dad65720c":()=>d.addAdditionalAudio,"60c855f485dc14093b0fee942b2204ae7dca69142e":()=>d.deleteConsultationImage,"60fe749e3c9b3a6222eaf37afc1421459616909387":()=>d.deleteAdditionalAudio,"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd":()=>d.generateSummaryStream,"7fc832fd06db7232f37f9b1b44631bd2956c9b940c":()=>d.createConsultationWithFiles});var d=a.i(755284),e=a.i(230122),f=a.i(957782);a.i(51346)},265765:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c":()=>d["0021892c2090f634723ac0a0d1cd1d8c637aae5b9c"],"008a705899f288d02ab102cb7194752ef57be976ca":()=>d["008a705899f288d02ab102cb7194752ef57be976ca"],"40181945d99bd1b1c806279418d8c4e384a1b0bd15":()=>d["40181945d99bd1b1c806279418d8c4e384a1b0bd15"],"40757964b8c9b072995f44631743e71306c1784480":()=>d["40757964b8c9b072995f44631743e71306c1784480"],"40cad8ac11d66cae6a36deaf0347461ff74e2f07d8":()=>d["40cad8ac11d66cae6a36deaf0347461ff74e2f07d8"],"40d596bef0460f198d588bbf8dede026bc85cca740":()=>d["40d596bef0460f198d588bbf8dede026bc85cca740"],"6016d1147dadc5ab75f7387f44ab799f7cd595708b":()=>d["6016d1147dadc5ab75f7387f44ab799f7cd595708b"],"60648db8471ac7da6666e2d2e8b2cf27a97b9b4688":()=>d["60648db8471ac7da6666e2d2e8b2cf27a97b9b4688"],"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7":()=>d["6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7"],"608aaaf92844312537830e8c8d0c49debb89bda21b":()=>d["608aaaf92844312537830e8c8d0c49debb89bda21b"],"6090ac31595b581fab8f32262309efcdcab1c7eb47":()=>d["6090ac31595b581fab8f32262309efcdcab1c7eb47"],"60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb":()=>d["60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb"],"609aacf458083825c0e86748f13c86d5773450588b":()=>d["609aacf458083825c0e86748f13c86d5773450588b"],"60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e":()=>d["60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e"],"60b3bf115b7639c2355ff16ee9fa53425dad65720c":()=>d["60b3bf115b7639c2355ff16ee9fa53425dad65720c"],"60c855f485dc14093b0fee942b2204ae7dca69142e":()=>d["60c855f485dc14093b0fee942b2204ae7dca69142e"],"60fe749e3c9b3a6222eaf37afc1421459616909387":()=>d["60fe749e3c9b3a6222eaf37afc1421459616909387"],"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd":()=>d["7f7dd3bdf6242116733f7ef527b1e8f307d343dacd"],"7fc832fd06db7232f37f9b1b44631bd2956c9b940c":()=>d["7fc832fd06db7232f37f9b1b44631bd2956c9b940c"]}),a.i(211431);var d=a.i(622299)},157045:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({NewRecordingInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call NewRecordingInterface() from the server but NewRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/recording/new-recording-interface.tsx <module evaluation>","NewRecordingInterface")}},692971:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({NewRecordingInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call NewRecordingInterface() from the server but NewRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/recording/new-recording-interface.tsx","NewRecordingInterface")}},374250:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(157045);var d=a.i(692971);a.n(d)},477954:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DashboardData:()=>h});var d=a.i(129629),e=a.i(76803),f=a.i(755284),g=a.i(374250);async function h({doctorId:a}){let[b,c]=await Promise.all([(0,e.getUser)(),(0,f.getConsultations)({page:1,pageSize:15})]),h=c.success&&c.data.consultations||[],i=!!c.success&&c.data.hasMore;return(0,d.jsx)(g.NewRecordingInterface,{user:b,consultations:h,hasMore:i,doctorId:a})}},891071:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DashboardClient:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call DashboardClient() from the server but DashboardClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/shared/dashboard-client.tsx <module evaluation>","DashboardClient")}},927737:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DashboardClient:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call DashboardClient() from the server but DashboardClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/shared/dashboard-client.tsx","DashboardClient")}},691140:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(891071);var d=a.i(927737);a.n(d)},532168:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PWAInstallPrompt:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call PWAInstallPrompt() from the server but PWAInstallPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/pwa/pwa-install-prompt.tsx <module evaluation>","PWAInstallPrompt")}},200275:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PWAInstallPrompt:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call PWAInstallPrompt() from the server but PWAInstallPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/pwa/pwa-install-prompt.tsx","PWAInstallPrompt")}},840220:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(532168);var d=a.i(200275);a.n(d)},11187:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>k,metadata:()=>b});var d=a.i(129629),e=a.i(465421),f=a.i(76803),g=a.i(477954),h=a.i(691140),i=a.i(840220),j=a.i(968123);let b={title:"Dashboard - Celer AI",description:"Create new patient consultations with AI-powered summaries"};async function k(){let a=await (0,f.verifySession)();return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(j.DashboardSkeleton,{}),children:(0,d.jsx)(g.DashboardData,{doctorId:a.userId})}),(0,d.jsx)(h.DashboardClient,{doctorId:a.userId}),(0,d.jsx)(i.PWAInstallPrompt,{})]})}}},342633:a=>{var{g:b,__dirname:c}=a;a.n(a.i(11187))},433774:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(342633),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["dashboard",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/dashboard/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/dashboard/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},848827:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(342633),a.i(660874),a.i(715847),a.i(781045),a.i(433774)},731083:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(433774)},941596:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(848827);var d=a.i(731083)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__65230092._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))},482970:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(870110)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__8e58ad9e._.js.map