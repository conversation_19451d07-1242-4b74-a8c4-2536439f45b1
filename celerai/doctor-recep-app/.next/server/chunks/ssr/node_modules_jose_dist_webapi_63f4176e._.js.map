{"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/buffer_utils.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/base64.js", "turbopack:///[project]/node_modules/jose/dist/webapi/util/base64url.js", "turbopack:///[project]/node_modules/jose/dist/webapi/util/errors.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/subtle_dsa.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/check_key_length.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/crypto_key.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/invalid_key_input.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/get_sign_verify_key.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/sign.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_disjoint.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_key_like.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_object.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_jwk.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/check_key_type.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/validate_crit.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/jwk_to_key.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/normalize_key.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jws/flattened/sign.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jws/compact/sign.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/epoch.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/secs.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/jwt_claims_set.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jwt/sign.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/verify.js", "turbopack:///[project]/node_modules/jose/dist/webapi/lib/validate_algorithms.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jws/flattened/verify.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jws/compact/verify.js", "turbopack:///[project]/node_modules/jose/dist/webapi/jwt/verify.js"], "sourcesContent": ["export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n", "export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n", "import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default (alg, algorithm) => {\n    const hash = `SHA-${alg.slice(-3)}`;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            return { hash, name: '<PERSON><PERSON>' };\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { hash, name: 'RSA-PSS', saltLength: parseInt(alg.slice(-3), 10) >> 3 };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { hash, name: 'RSASSA-PKCS1-v1_5' };\n        case 'ES256':\n        case 'ES384':\n        case 'ES512':\n            return { hash, name: 'ECDS<PERSON>', namedCurve: algorithm.namedCurve };\n        case 'Ed25519':\n        case 'EdDSA':\n            return { name: 'Ed25519' };\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\n", "export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nexport default async (alg, key, usage) => {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n        }\n        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);\n    }\n    checkSigCryptoKey(key, alg, usage);\n    return key;\n};\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport checkKey<PERSON>ength from './check_key_length.js';\nimport getSignKey from './get_sign_verify_key.js';\nexport default async (alg, key, data) => {\n    const cryptoKey = await getSignKey(alg, key, 'sign');\n    checkKeyLength(alg, cryptoKey);\n    const signature = await crypto.subtle.sign(subtleAlgorithm(alg, cryptoKey.algorithm), cryptoKey, data);\n    return new Uint8Array(signature);\n};\n", "export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n", "export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n", "import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n};\n", "import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n", "import { encode as b64u } from '../../util/base64url.js';\nimport sign from '../../lib/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport class FlattenedSign {\n    #payload;\n    #protectedHeader;\n    #unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this.#payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this.#protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this.#protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyType(alg, key, 'sign');\n        let payload = this.#payload;\n        if (b64) {\n            payload = encoder.encode(b64u(payload));\n        }\n        let protectedHeader;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const k = await normalizeKey(key, alg);\n        const signature = await sign(alg, k, data);\n        const jws = {\n            signature: b64u(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this.#unprotectedHeader) {\n            jws.header = this.#unprotectedHeader;\n        }\n        if (this.#protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    #flattened;\n    constructor(payload) {\n        this.#flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this.#flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class SignJWT {\n    #protectedHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(this.#jwt.data());\n        sig.setProtectedHeader(this.#protectedHeader);\n        if (Array.isArray(this.#protectedHeader?.crit) &&\n            this.#protectedHeader.crit.includes('b64') &&\n            this.#protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport check<PERSON><PERSON><PERSON>ength from './check_key_length.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nexport default async (alg, key, signature, data) => {\n    const cryptoKey = await getVerifyKey(alg, key, 'verify');\n    checkKeyLength(alg, cryptoKey);\n    const algorithm = subtleAlgorithm(alg, cryptoKey.algorithm);\n    try {\n        return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n    }\n    catch {\n        return false;\n    }\n};\n", "export default (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n", "import { decode as b64u } from '../../util/base64url.js';\nimport verify from '../../lib/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = b64u(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n    }\n    checkKeyType(alg, key, 'verify');\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = b64u(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const k = await normalizeKey(key, alg);\n    const verified = await verify(alg, k, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = b64u(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { compactVerify } from '../jws/compact/verify.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTInvalid } from '../util/errors.js';\nexport async function jwtVerify(jwt, key, options) {\n    const verified = await compactVerify(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = validateClaimsSet(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": "8IAAO,IAAM,EAAU,IAAI,YACd,EAAU,IAAI,YAEpB,SAAS,EAAO,GAAG,CAAO,EAE7B,IAAM,EAAM,IAAI,WAAW,AADd,EAAQ,MAAM,CAAC,CAAC,EAAK,QAAE,CAAM,CAAE,GAAK,EAAM,EAAQ,IAE3D,EAAI,EACR,IAAK,IAAM,KAAU,EACjB,EAAI,GAAG,CADmB,AAClB,EAAQ,GAChB,GAAK,EAAO,MAAM,CAEtB,OAAO,CACX,CACA,SAAS,EAAc,CAAG,CAAE,CAAK,CAAE,CAAM,EACrC,GAAI,EAAQ,GAAK,SAAS,MACtB,KADiC,CAC3B,AAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE,WAAW,EAAE,EAAA,CAAO,EAExF,EAAI,GAAG,CAAC,CAAC,IAAU,GAAI,IAAU,GAAI,IAAU,EAAW,IAAR,EAAa,CAAE,EACrE,CACO,SAAS,EAAS,CAAK,EAC1B,IAAM,EAAO,KAAK,KAAK,CAAC,QAAQ,OAE1B,EAAM,IAAI,WAAW,GAG3B,OAFA,EAAc,EAAK,EAAM,GACzB,EAAc,EAHF,EAnBE,CAsBK,IAtBA,CAmBC,MAGI,GACjB,CACX,CACO,SAAS,EAAS,CAAK,EAC1B,IAAM,EAAM,IAAI,WAAW,GAE3B,OADA,EAAc,EAAK,GACZ,CACX,mDC/BO,SAAS,EAAa,CAAK,EAC9B,GAAI,WAAW,SAAS,CAAC,QAAQ,CAC7B,CAD+B,MACxB,EAAM,QAAQ,GAGzB,IAAM,EAAM,EAAE,CACd,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,KAAK,IACnC,EAAI,IAAI,CADuC,AACtC,OAAO,YAAY,CAAC,KAAK,CAAC,KAAM,EAAM,QAAQ,CAAC,EAAG,EAH5C,EAGgD,OAEnE,OAAO,KAAK,EAAI,IAAI,CAAC,IACzB,CACO,SAAS,EAAa,CAAO,EAChC,GAAI,WAAW,UAAU,CACrB,CADuB,MAChB,WAAW,UAAU,CAAC,GAEjC,IAAM,EAAS,KAAK,GACd,EAAQ,IAAI,WAAW,EAAO,MAAM,EAC1C,IAAK,IAAI,EAAI,EAAG,EAAI,EAAO,MAAM,CAAE,IAAK,AACpC,CAAK,CAAC,EAAE,CAAG,EAAO,UAAU,CAAC,GAEjC,OAAO,CACX,+HCrBA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACO,SAAS,EAAO,CAAK,EACxB,GAAI,WAAW,UAAU,CACrB,CADuB,MAChB,WAAW,UAAU,CAAC,AAAiB,iBAAV,EAAqB,EAAQ,EAAA,OAAO,CAAC,MAAM,CAAC,GAAQ,CACpF,SAAU,SADmD,EAEjE,GAEJ,IAAI,EAAU,EACV,aAAmB,YAAY,CAC/B,EAAU,EAAA,OAAO,CAAC,MAAM,CAAC,EAAA,EAE7B,EAAU,EAAQ,OAAO,CAAC,KAAM,CAFlB,IAEuB,OAAO,CAAC,KAAM,KAAK,OAAO,CAAC,MAAO,IACvE,GAAI,CACA,MAAO,CAAA,EAAA,EAAA,YAAA,AAAW,EAAE,EACxB,CACA,KAAM,CACF,MAAM,AAAI,QAHH,EAGa,oDACxB,CACJ,CACO,SAAS,EAAO,CAAK,EACxB,IAAI,EAAY,QAIhB,CAHyB,UAAU,AAA/B,OAAO,IACP,EAAY,EAAA,OAAO,CAAC,MAAM,CAAC,EAAA,EAE3B,WAAW,OAFC,EAEQ,CAAC,QAAQ,EAAE,AACxB,EAAU,QAAQ,CAAC,CAAE,SAAU,YAAa,aAAa,CAAK,GAElE,CAAA,EAAA,EAAA,YAAA,AAAW,EAAE,GAAW,OAAO,CAAC,KAAM,IAAI,GAA1C,IAAiD,CAAC,MAAO,KAAK,OAAO,CAAC,MAAO,IACxF,sYC7BO,OAAM,UAAkB,MAC3B,OAAO,KAAO,kBAAmB,CACjC,KAAO,kBAAmB,AAC1B,aAAY,CAAO,CAAE,CAAO,CAAE,CAC1B,KAAK,CAAC,EAAS,GACf,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CACjC,MAAM,iBAAiB,GAAG,IAAI,CAAE,IAAI,CAAC,WAAW,CACpD,CACJ,CACO,MAAM,UAAiC,EAC1C,OAAO,KAAO,iCAAkC,CAChD,KAAO,iCAAkC,CACzC,KAAM,AACN,OAAO,CACP,OAAQ,AACR,aAAY,CAAO,CAAE,CAAO,CAAE,EAAQ,aAAa,CAAE,EAAS,aAAa,CAAE,CACzE,KAAK,CAAC,EAAS,CAAE,MAAO,OAAE,SAAO,UAAQ,CAAQ,CAAE,GACnD,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,OAAO,CAAG,CACnB,CACJ,CACO,MAAM,UAAmB,EAC5B,OAAO,KAAO,iBAAkB,CAChC,KAAO,iBAAkB,CACzB,KAAM,CACN,MAAO,CACP,OAAQ,AACR,aAAY,CAAO,CAAE,CAAO,CAAE,EAAQ,aAAa,CAAE,EAAS,aAAa,CAAE,CACzE,KAAK,CAAC,EAAS,CAAE,MAAO,OAAE,EAAO,iBAAQ,CAAQ,CAAE,GACnD,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,OAAO,CAAG,CACnB,CACJ,CACO,MAAM,UAA0B,EACnC,OAAO,KAAO,0BAA2B,AACzC,MAAO,0BACX,AADsC,CAE/B,MAAM,UAAyB,EAClC,OAAO,KAAO,wBAAyB,CACvC,KAAO,wBAAyB,AACpC,CACO,MAAM,UAA4B,EACrC,OAAO,KAAO,2BAA4B,CAC1C,KAAO,2BAA4B,AACnC,aAAY,EAAU,6BAA6B,CAAE,CAAO,CAAE,CAC1D,KAAK,CAAC,EAAS,EACnB,CACJ,CACO,MAAM,UAAmB,EAC5B,OAAO,KAAO,iBAAkB,CAChC,KAAO,iBAAkB,AAC7B,CACO,MAAM,UAAmB,EAC5B,OAAO,KAAO,iBAAkB,CAChC,KAAO,iBAAkB,AAC7B,CACO,MAAM,UAAmB,EAC5B,OAAO,KAAO,iBAAkB,CAChC,KAAO,iBAAkB,AAC7B,CACO,MAAM,UAAmB,EAC5B,OAAO,KAAO,iBAAkB,CAChC,KAAO,iBAAkB,AAC7B,CACO,MAAM,UAAoB,EAC7B,OAAO,KAAO,kBAAmB,CACjC,KAAO,kBAAmB,AAC9B,CACO,MAAM,UAA0B,EACnC,OAAO,KAAO,0BAA2B,CACzC,KAAO,0BAA2B,AAClC,aAAY,EAAU,iDAAiD,CAAE,CAAO,CAAE,CAC9E,KAAK,CAAC,EAAS,EACnB,CACJ,CACO,MAAM,UAAiC,EAC1C,CAAC,OAAO,aAAa,CAAC,AAAC,AACvB,QAAO,KAAO,iCAAkC,CAChD,KAAO,iCAAkC,AACzC,aAAY,EAAU,sDAAsD,CAAE,CAAO,CAAE,CACnF,KAAK,CAAC,EAAS,EACnB,CACJ,CACO,MAAM,UAAoB,EAC7B,OAAO,KAAO,kBAAmB,AACjC,MAAO,kBAAmB,AAC1B,aAAY,EAAU,mBAAmB,CAAE,CAAO,CAAE,CAChD,KAAK,CAAC,EAAS,EACnB,CACJ,CACO,MAAM,UAAuC,EAChD,OAAO,KAAO,uCAAwC,CACtD,KAAO,uCAAwC,AAC/C,aAAY,EAAU,+BAA+B,CAAE,CAAO,CAAE,CAC5D,KAAK,CAAC,EAAS,EACnB,CACJ,yEClGA,IAAA,EAAA,EAAA,CAAA,CAAA,cACe,CAAC,EAAK,KACjB,IAAM,EAAO,CAAC,IAAI,EAAE,EAAI,KAAK,CAAC,CAAC,GAAA,CAAI,CACnC,OAAQ,GACJ,IAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,MAAE,EAAM,KAAM,MAAO,CAChC,KAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,MAAE,EAAM,KAAM,UAAW,WAAY,SAAS,EAAI,KAAK,CAAC,CAAC,GAAI,KAAO,CAAE,CACjF,KAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,MAAE,EAAM,KAAM,mBAAoB,CAC7C,KAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,MAAE,EAAM,KAAM,QAAS,WAAY,EAAU,UAAU,AAAC,CACnE,KAAK,UACL,IAAK,QACD,MAAO,CAAE,KAAM,SAAU,CAC7B,SACI,MAAM,IAAI,EAAA,gBAAgB,CAAC,CAAC,IAAI,EAAE,EAAI,WAA5B,gDAAuF,CAAC,CAC1G,CACJ,8EC1Be,CAAC,EAAK,KACjB,GAAI,EAAI,UAAU,CAAC,OAAS,EAAI,UAAU,CAAC,MAAO,CAC9C,GAAM,eAAE,CAAa,CAAE,CAAG,EAAI,SAAS,CACvC,GAAI,AAAyB,iBAAlB,GAA8B,EAAgB,KACrD,CAD2D,KACjD,AAAJ,UAAc,CAAA,EAAG,EAAI,qDAAqD,CAAC,CAEzF,CACJ,mDCPA,SAAS,EAAS,CAAI,CAAE,EAAO,gBAAgB,EAC3C,OAAO,AAAI,UAAU,CAAC,+CAA+C,EAAE,EAAK,SAAS,EAAE,EAAA,CAAM,CACjG,CACA,SAAS,EAAY,CAAS,CAAE,CAAI,EAChC,OAAO,EAAU,IAAI,GAAK,CAC9B,CACA,SAAS,EAAc,CAAI,EACvB,OAAO,SAAS,EAAK,IAAI,CAAC,KAAK,CAAC,GAAI,GACxC,CAaA,SAAS,EAAW,CAAG,CAAE,CAAK,EAC1B,GAAI,GAAS,CAAC,EAAI,MAAM,CAAC,QAAQ,CAAC,GAC9B,KADsC,CAChC,AAAI,UAAU,CAAC,mEAAmE,EAAE,EAAM,CAAC,CAAC,CAE1G,CACO,SAAS,EAAkB,CAAG,CAAE,CAAG,CAAE,CAAK,EAC7C,OAAQ,GACJ,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,QAC5B,MAAM,EAAS,QACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,GAAI,IAExC,GAAI,AADW,EAAc,EAAI,SAAS,CAAC,IAAI,IAChC,EACX,MAAM,EAAS,CAAC,IAAI,EAAE,EAAA,CAAU,CAAE,kBACtC,KACJ,CACA,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,qBAC5B,MAAM,EAAS,qBACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,GAAI,IAExC,GADe,AACX,EADyB,EAAI,SAAS,CAAC,IAAI,IAChC,EACX,MAAM,EAAS,CAAC,IAAI,EAAE,EAAA,CAAU,CAAE,kBACtC,KACJ,CACA,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,WAC5B,MAAM,EAAS,WACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,GAAI,IAExC,GADe,AACX,EADyB,EAAI,SAAS,CAAC,IAAI,IAChC,EACX,MAAM,EAAS,CAAC,IAAI,EAAE,EAAA,CAAU,CAAE,kBACtC,KACJ,CACA,IAAK,UACL,IAAK,QACD,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,WAC5B,MAAM,EAAS,WACnB,KAEJ,KAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,SAC5B,MAAM,EAAS,SACnB,IAAM,EAAW,AA/D7B,SAAS,AAAc,CAAG,EACtB,OAAQ,GACJ,IAAK,QACD,MAAO,OACX,KAAK,QACD,MAAO,OACX,KAAK,QACD,MAAO,OACX,SACI,MAAM,AAAI,MAAM,cACxB,CACJ,EAoD2C,GAE/B,GADe,AACX,EADe,SAAS,CAAC,UAAU,GACxB,EACX,MAAM,EAAS,EAAU,wBAC7B,KACJ,CACA,QACI,MAAM,AAAI,UAAU,4CAC5B,CACA,EAAW,EAAK,EACpB,CACO,SAAS,EAAkB,CAAG,CAAE,CAAG,CAAE,CAAK,EAC7C,OAAQ,GACJ,IAAK,UACL,IAAK,UACL,IAAK,UAAW,CACZ,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,WAC5B,MAAM,EAAS,WACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,EAAG,GAAI,IAE3C,GADe,AACX,EADe,SAAS,CAAC,MAAM,GACpB,EACX,MAAM,EAAS,EAAU,oBAC7B,KACJ,CACA,IAAK,SACL,IAAK,SACL,IAAK,SAAU,CACX,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,UAC5B,MAAM,EAAS,UACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,EAAG,GAAI,IAE3C,GADe,AACX,EADe,SAAS,CAAC,MAAM,GACpB,EACX,MAAM,EAAS,EAAU,oBAC7B,KACJ,CACA,IAAK,OACD,OAAQ,EAAI,SAAS,CAAC,IAAI,EACtB,IAAK,OACL,IAAK,SACD,KACJ,SACI,MAAM,EAAS,iBACvB,CACA,KAEJ,KAAK,qBACL,IAAK,qBACL,IAAK,qBACD,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,UAC5B,MAAM,EAAS,UACnB,KACJ,KAAK,WACL,IAAK,eACL,IAAK,eACL,IAAK,eAAgB,CACjB,GAAI,CAAC,EAAY,EAAI,SAAS,CAAE,YAC5B,MAAM,EAAS,YACnB,IAAM,EAAW,SAAS,EAAI,KAAK,CAAC,GAAI,KAAO,EAE/C,GAAI,AADW,EAAc,EAAI,SAAS,CAAC,IAAI,IAChC,EACX,MAAM,EAAS,CAAC,IAAI,EAAE,EAAA,CAAU,CAAE,kBACtC,KACJ,CACA,QACI,MAAM,AAAI,UAAU,4CAC5B,CACA,EAAW,EAAK,EACpB,yGC3IA,SAAS,EAAQ,CAAG,CAAE,CAAM,CAAE,GAAG,CAAK,EAElC,GADA,AACI,GADI,EAAM,MAAM,CAAC,QAAA,EACX,MAAM,CAAG,EAAG,CAClB,IAAM,EAAO,EAAM,GAAG,GACtB,GAAO,CAAC,YAAY,EAAE,EAAM,IAAI,CAAC,MAAM,KAAK,EAAE,EAAK,CAAC,CAAC,AACzD,MAC0B,CAArB,EAAwB,CAApB,EAAM,MAAM,CACjB,GAAO,CAAC,YAAY,EAAE,CAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAGhD,GAAO,CAAC,QAAQ,EAAE,CAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAajC,OAXc,MAAV,AAAgB,EAChB,GAAO,CAAC,UAAU,EAAE,EAAA,CAAQ,CAEL,YAAlB,OAAO,GAAyB,EAAO,IAAI,CAChD,CADkD,EAC3C,CAAC,mBAAmB,EAAE,EAAO,IAAI,CAAA,CAAE,CAEnB,UAAlB,OAAO,GAAiC,MAAV,AAAgB,GAC/C,EAAO,WAAW,EAAE,MAAM,CAC1B,GAAO,CAAC,yBAAyB,EAAE,EAAO,WAAW,CAAC,IAAI,CAAA,CAAA,AAAE,EAG7D,CACX,0CACe,CAAC,EAAQ,GAAG,IAChB,EAAQ,eAAgB,KAAW,GAEvC,SAAS,EAAQ,CAAG,CAAE,CAAM,CAAE,GAAG,CAAK,EACzC,OAAO,EAAQ,CAAC,YAAY,EAAE,EAAI,mBAAmB,CAAC,CAAE,KAAW,EACvE,yEC9BA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,cACe,MAAO,EAAK,EAAK,KAC5B,GAAI,aAAe,WAAY,CAC3B,GAAI,CAAC,EAAI,UAAU,CAAC,MAChB,CADuB,KACjB,AAAI,UAAU,CAAA,EAAA,EAAA,OAAA,AAAc,EAAE,EAAK,YAAa,YAAa,EAA/C,eAExB,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,MAAO,EAAK,CAAE,KAAM,CAAC,IAAI,EAAE,EAAI,KAAK,CAAC,CAAC,GAAA,CAAI,CAAE,KAAM,MAAO,GAAG,EAAO,CAAC,EAAM,CAC7G,CAEA,MADA,CAAA,EAAA,EAAA,iBAAA,AAAgB,EAAE,EAAK,EAAK,GACrB,CACX,UAFI,+DCTJ,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,cACe,MAAO,EAAK,EAAK,KAC5B,IAAM,EAAY,MAAM,CAAA,EAAA,EAAA,OAAA,AAAS,EAAE,EAAK,EAAK,QAG7C,MAFA,CAAA,EAAA,EAAA,KADwB,EACX,AAAb,EAAe,EAAK,GAEb,IAAI,WAAW,AADJ,MAAM,CADxB,MAC+B,MAAM,CAAC,IAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAc,EAAE,EAAK,EAAU,SAAS,EAAG,EAAW,GAErG,QAF+C,uECNhC,CAAC,GAAG,KACf,IAII,EAJE,EAAU,EAAQ,MAAM,CAAC,SAC/B,GAAI,AAAmB,MAAX,MAAM,EAA6B,GAAG,CAAtB,EAAQ,MAAM,CACtC,OAAO,EAGX,IAAK,IAAM,KAAU,EAAS,CAC1B,IAAM,EAAa,OAAO,IAAI,CAAC,GAC/B,GAAI,CAAC,GAAO,AAAa,MAAT,IAAI,CAAQ,CACxB,EAAM,IAAI,IAAI,GACd,QACJ,CACA,IAAK,IAAM,KAAa,EAAY,CAChC,GAAI,EAAI,GAAG,CAAC,GACR,OAAO,EADa,AAGxB,EAAI,GAAG,CAAC,EACZ,CACJ,CACA,OAAO,CACX,oDCpBO,SAAS,EAAgB,CAAG,EAC/B,GAAI,CAAC,EAAY,GACb,GADmB,GACb,AAAI,MAAM,8BAExB,CACO,SAAS,EAAY,CAAG,EAC3B,OAAO,GAAK,CAAC,OAAO,WAAW,CAAC,GAAK,WACzC,CACO,SAAS,EAAY,CAAG,EAC3B,OAAO,GAAK,CAAC,OAAO,WAAW,CAAC,GAAK,WACzC,sFACe,AAAC,GACL,EAAY,IAAQ,EAAY,gFCT5B,AAAC,IACZ,GAAI,CAAC,AAJT,SAAS,AAAa,CAAK,EACvB,MAAwB,UAAjB,OAAO,GAAgC,OAAV,CACxC,EAEsB,IAAoD,mBAAmB,CAA7D,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GACvD,OAAO,EAEX,GAAI,AAAiC,MAAM,QAAhC,cAAc,CAAC,GACtB,OAAO,EAEX,IAAI,EAAQ,EACZ,KAAwC,KAAM,EAAvC,OAAO,cAAc,CAAC,IACzB,EAAQ,OAAO,cAAc,CAAC,GAElC,OAAO,OAAO,cAAc,CAAC,KAAW,CAC5C,6HCfA,IAAA,EAAA,EAAA,CAAA,CAAA,QACO,SAAS,EAAM,CAAG,EACrB,MAAO,CAAA,EAAA,EAAA,OAAA,AAAO,EAAE,IAA2B,UAAnB,OAAO,EAAI,GACvC,AAD0C,CAEnC,CAFI,QAEK,EAAa,CAAG,EAC5B,MAAmB,QAAZ,EAAI,GAAG,EAAc,AAAiB,iBAAV,EAAI,CAAC,AAC5C,CACO,SAAS,EAAY,CAAG,EAC3B,MAAO,AAAY,UAAR,GAAG,EAAc,AAAiB,SAAV,EAAI,CAC3C,AAD4C,CAErC,SAAS,EAAY,CAAG,EAC3B,MAAmB,QAAZ,EAAI,GAAG,EAA+B,UAAjB,OAAO,EAAI,CAAC,AAC5C,uECZA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,IAAM,EAAM,AAAC,GAAQ,GAAK,CAAC,OAAO,WAAW,CAAC,CACxC,EAAe,CAAC,EAAK,EAAK,KAC5B,GAAgB,SAAZ,EAAI,GAAG,CAAgB,CACvB,IAAI,EACJ,OAAQ,GACJ,IAAK,OACL,IAAK,SACD,EAAW,MACX,KACJ,KAAK,UACL,IAAK,UACD,EAAW,KAEnB,CACA,GAAI,EAAI,GAAG,GAAK,EACZ,MAAM,AAAI,EADY,QACF,CAAC,mDAAmD,EAAE,EAAS,cAAc,CAAC,CAE1G,CACA,QAAgB,IAAZ,EAAI,GAAG,EAAkB,EAAI,GAAG,GAAK,EACrC,GAD0C,GACpC,AAAI,UAAU,CAAC,mDAAmD,EAAE,EAAI,cAAc,CAAC,EAEjG,GAAI,MAAM,OAAO,CAAC,EAAI,OAAO,EAAG,CAC5B,IAAI,EACJ,QAAQ,GACJ,IAAe,SAAV,GAA8B,WAAV,EACzB,IAAK,AAAQ,UACb,KAAK,EAAI,QAAQ,CAAC,UACd,EAAgB,EAChB,KACJ,MAAK,EAAI,UAAU,CAAC,SAChB,EAAgB,aAChB,KACJ,KAAK,0BAA0B,IAAI,CAAC,GAE5B,EADA,CAAC,EAAI,QAAQ,CAAC,QAAU,EAAI,QAAQ,CAAC,MACX,CADkB,WAC5B,EAAsB,UAAY,YAGlC,EAEpB,KACJ,KAAe,YAAV,GAAuB,EAAI,UAAU,CAAC,OACvC,EAAgB,UAChB,KACJ,KAAe,YAAV,EACD,EAAgB,EAAI,UAAU,CAAC,OAAS,YAAc,YAE9D,CACA,GAAI,GAAiB,EAAI,OAAO,EAAE,WAAW,MAAmB,EAC5D,KADmE,CAC7D,AAAI,UAAU,CAAC,4DAA4D,EAAE,EAAc,cAAc,CAAC,CAExH,CACA,OAAO,CACX,EACM,EAAqB,CAAC,EAAK,EAAK,KAClC,IAAI,cAAe,UAAA,GACf,AACJ,GAAI,CAAA,EAAA,EAAA,KAAQ,AAAR,EAAU,GAAM,CAChB,GAAI,CAAA,EAAA,EAAA,WAAA,AAAc,EAAE,IAAQ,CAD5B,CACyC,EAAK,EAAK,GAC/C,MACJ,KAFI,EAEE,AAAI,UAAU,CAAC,uHAAuH,CAAC,CACjJ,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAQ,EAAE,GACX,GADiB,GACX,AAAI,UAAU,CAAA,EAAA,EAAA,IADnB,GACiC,AAAd,EAAgB,EAAK,EAAK,YAAa,YAAa,AAApD,eAAoE,eAE5F,GAAiB,UAAU,CAAvB,EAAI,IAAI,CACR,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,4DAA4D,CAAC,EAErG,EACM,EAAsB,CAAC,EAAK,EAAK,KACnC,GAAI,CAAA,EAAA,EAAA,KAAA,AAAQ,EAAE,GACV,GADgB,IACR,GACJ,IAAK,UACL,GAHJ,CAGS,OACD,GAAI,GAAA,EAAA,YAAA,AAAe,EAAE,IAAQ,EAAa,EAAK,EAAK,GAChD,MACJ,IAFI,GAEE,AAAI,UAAU,CAAC,gDAAgD,CAAC,CAC1E,KAAK,UACL,IAAK,SACD,GAAI,CAAA,EAAA,EAAA,WAAA,AAAc,EAAE,IAAQ,EAAa,EAAK,EAAK,GAC/C,MACJ,KAFI,EAEE,AAAI,UAAU,CAAC,+CAA+C,CAAC,CAC7E,CAEJ,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAQ,EAAE,GACX,GADiB,GACX,AAAI,UAAU,CAAA,EAAA,EAAA,IADnB,GACmB,AAAc,EAAE,EAAK,EAAK,YAAa,YAAvC,AAAoD,iBAE5E,GAAiB,UAAU,CAAvB,EAAI,IAAI,CACR,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,iEAAiE,CAAC,EAEtG,GAAiB,UAAU,CAAvB,EAAI,IAAI,CACR,OAAQ,GACJ,IAAK,OACD,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,qEAAqE,CAAC,CAC1G,KAAK,UACD,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,wEAAwE,CAAC,CAGjH,CAEJ,GAAiB,WAAW,CAAxB,EAAI,IAAI,CACR,OAAQ,GACJ,IAAK,SACD,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,sEAAsE,CAAC,CAC3G,KAAK,UACD,MAAM,AAAI,UAAU,CAAA,EAAG,EAAI,GAAK,uEAAuE,CAAC,CAGhH,CAER,IACe,CAAC,EAAK,EAAK,KACJ,EAAI,UAAU,CAAC,OACrB,QAAR,GACA,EAAI,UAAU,CAAC,UACf,oCAAoC,IAAI,CAAC,IACzC,0CAA0C,IAAI,CAAC,GAE/C,EAAmB,EAAK,EAAK,GAG7B,EAAoB,EAAK,EAAK,EAEtC,yEC7HA,IAAA,EAAA,EAAA,CAAA,CAAA,cACe,CAAC,EAAK,EAAmB,EAAkB,EAAiB,SAYnE,EAXJ,QAAwB,IAApB,EAAW,IAAI,EAAkB,GAAiB,YAAS,EAC3D,MAAM,GADgE,CAC5D,EAAI,kEAElB,GAAI,CAAC,QAA4C,IAAzB,EAAgB,IAAI,CAAgB,AACxD,OAAO,IAAI,IAEf,GAAI,CAAC,MAAM,OAAO,CAAC,EAAgB,IAAI,GACH,IAAhC,EAAgB,IAAI,CAAC,MAAM,EAC3B,EAAgB,IAAI,CAAC,IAAI,CAAC,AAAC,GAA2B,UAAjB,OAAO,GAAuC,IAAjB,AAAqB,EAAf,MAAM,EAC9E,MAAM,IAAI,EAAI,yFASlB,IAAK,IAAM,KALP,OADqB,IAArB,EACa,IAAI,CADe,GACX,IAAI,OAAO,OAAO,CAAC,MAAsB,EAAkB,OAAO,GAAG,EAG7E,EAEO,EAAgB,IAAI,EAAE,CAC1C,GAAI,CAAC,EAAW,GAAG,CAAC,GAChB,MAAM,GADsB,CAClB,EAAA,gBAAgB,CAAC,CAAC,mBAAlB,SAA8C,EAAE,EAAU,mBAAmB,CAAC,EAE5F,QAA8B,IAA1B,CAAU,CAAC,EAAU,CACrB,EADqC,IAC/B,IAAI,EAAI,CAAC,4BAA4B,EAAE,EAAU,YAAY,CAAC,EAExE,GAAI,EAAW,GAAG,CAAC,SAA6C,IAA/B,CAAe,CAAC,EAAU,CACvD,EADuE,IACjE,IAAI,EAAI,CAAC,4BAA4B,EAAE,EAAU,6BAA6B,CAAC,CAE7F,CACA,OAAO,IAAI,IAAI,EAAgB,IAAI,CACvC,yEChCA,IAAA,EAAA,EAAA,CAAA,CAAA,cAoFe,MAAO,IAClB,GAAI,CAAC,EAAI,GAAG,CACR,CADU,KACJ,AAAI,UAAU,4DAExB,GAAM,WAAE,CAAS,WAAE,CAAS,CAAE,CAAG,AAvFrC,SAAS,AAAc,CAAG,EACtB,IAAI,EACA,EACJ,OAAQ,EAAI,GAAG,EACX,IAAK,MACD,OAAQ,EAAI,GAAG,EACX,IAAK,QACL,IAAK,QACL,IAAK,QACD,EAAY,CAAE,KAAM,UAAW,KAAM,CAAC,IAAI,EAAE,EAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAA,CAAI,AAAC,EAChE,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,QACL,IAAK,QACL,IAAK,QACD,EAAY,CAAE,KAAM,oBAAqB,KAAM,CAAC,IAAI,EAAE,EAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAA,CAAI,AAAC,EAC1E,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,WACL,IAAK,eACL,IAAK,eACL,IAAK,eACD,EAAY,CACR,KAAM,WACN,KAAM,CAAC,IAAI,EAAE,SAAS,EAAI,GAAG,CAAC,KAAK,CAAC,CAAC,GAAI,KAAO,EAAA,CAAG,AACvD,EACA,EAAY,EAAI,CAAC,CAAG,CAAC,UAAW,YAAY,CAAG,CAAC,UAAW,UAAU,CACrE,KACJ,SACI,MAAM,IAAI,EAAA,gBAAgB,CAAC,oBAAjB,2CAClB,CACA,KAEJ,KAAK,KACD,OAAQ,EAAI,GAAG,EACX,IAAK,QACD,EAAY,CAAE,KAAM,QAAS,WAAY,OAAQ,EACjD,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,QACD,EAAY,CAAE,KAAM,QAAS,WAAY,OAAQ,EACjD,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,QACD,EAAY,CAAE,KAAM,QAAS,WAAY,OAAQ,EACjD,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,UACL,IAAK,iBACL,IAAK,iBACL,IAAK,iBACD,EAAY,CAAE,KAAM,OAAQ,WAAY,EAAI,GAAG,AAAC,EAChD,EAAY,EAAI,CAAC,CAAG,CAAC,aAAa,CAAG,EAAE,CACvC,KACJ,SACI,MAAM,IAAI,EAAA,gBAAgB,CAAC,oBAAjB,2CAClB,CACA,KAEJ,KAAK,MACD,OAAQ,EAAI,GAAG,EACX,IAAK,UACL,IAAK,QACD,EAAY,CAAE,KAAM,SAAU,EAC9B,EAAY,EAAI,CAAC,CAAG,CAAC,OAAO,CAAG,CAAC,SAAS,CACzC,KACJ,KAAK,UACL,IAAK,iBACL,IAAK,iBACL,IAAK,iBACD,EAAY,CAAE,KAAM,EAAI,GAAI,AAAD,EAC3B,EAAY,EAAI,CAAC,CAAG,CAAC,aAAa,CAAG,EAAE,CACvC,KACJ,SACI,MAAM,IAAI,EAAA,gBAAgB,CAAC,oBAAjB,2CAClB,CACA,KAEJ,SACI,MAAM,IAAI,EAAA,gBAAgB,CAAC,oBAAjB,0CAClB,CACA,MAAO,WAAE,YAAW,CAAU,CAClC,EAKmD,GACzC,EAAU,CAAE,GAAG,CAAG,AAAC,EAGzB,OAFA,OAAO,EAAQ,GAAG,CAClB,OAAO,EAAQ,GAAG,CACX,OAAO,MAAM,CAAC,SAAS,CAAC,MAAO,EAAS,EAAW,EAAI,GAAG,GAAK,CAAD,CAAK,CAAC,CAAkB,EAAf,AAAmB,OAAO,CAAlB,CAAsB,EAChH,CAD8F,uDCxF1F,uBAJJ,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,IAAM,EAAY,MAAO,EAAK,EAAK,EAAK,GAAS,CAAK,IAElD,IAAI,EAAS,CADb,IAAU,IAAI,OAAA,EACK,GAAG,CAAC,GACvB,GAAI,GAAQ,CAAC,EAAI,CACb,CADe,MACR,CAAM,CAAC,EAAI,CAEtB,IAAM,EAAY,MAAM,GAAA,EAAA,OAAA,AAAQ,EAAE,CAAE,GAAG,CAAG,KAAE,CAAI,GAShD,OARI,GACA,IAFoB,GAEb,MAAM,CAAC,GACb,EAID,CAAM,CAAC,EAAI,CAAG,CAJL,CACT,EAAM,GAAG,CAAC,EAAK,CAAE,CAAC,EAAI,CAAE,CAAU,GAK/B,CACX,EACM,EAAkB,CAAC,EAAW,KAEhC,IAMI,EANA,EAAS,CADb,IAAU,IAAI,OAAA,EACK,GAAG,CAAC,GACvB,GAAI,GAAQ,CAAC,EAAI,CACb,CADe,MACR,CAAM,CAAC,EAAI,CAEtB,IAAM,EAA8B,WAAnB,EAAU,IAAI,CACzB,IAAc,EAEpB,GAAoC,MAFL,KAE3B,EAFkC,AAExB,iBAAiB,CAAe,CAC1C,OAAQ,GACJ,IAAK,UACL,IAAK,iBACL,IAAK,iBACL,IAAK,iBACD,KACJ,SACI,MAAM,AAAI,UAAU,6DAC5B,CACA,EAAY,EAAU,WAAW,CAAC,EAAU,iBAAiB,CAAE,EAAa,EAAW,EAAE,CAAG,CAAC,aAAa,CAC9G,CACA,GAAI,AAAgC,cAAtB,iBAAiB,CAAgB,CAC3C,GAAY,UAAR,GAA2B,WAAW,CAAnB,EACnB,MAAU,AAAJ,UAAc,8DAExB,EAAY,EAAU,WAAW,CAAC,EAAU,iBAAiB,CAAE,EAAa,CACxE,EAAW,SAAW,OACzB,CACL,CACA,GAAoC,QAAhC,EAAU,iBAAiB,CAAY,CACvC,IAAI,EACJ,OAAQ,GACJ,IAAK,WACD,EAAO,QACP,KACJ,KAAK,QACL,IAAK,QACL,IAAK,eACD,EAAO,UACP,KACJ,KAAK,QACL,IAAK,QACL,IAAK,eACD,EAAO,UACP,KACJ,KAAK,QACL,IAAK,QACL,IAAK,eACD,EAAO,UACP,KACJ,SACI,MAAM,AAAI,UAAU,6DAC5B,CACA,GAAI,EAAI,UAAU,CAAC,YACf,CAD4B,MACrB,EAAU,WAAW,CAAC,CACzB,KAAM,gBACN,CACJ,EAAG,EAAa,EAAW,CAAC,UAAU,CAAG,CAAC,UAAU,EAExD,EAAY,EAAU,WAAW,CAAC,CAC9B,KAAM,EAAI,UAAU,CAAC,MAAQ,UAAY,yBACzC,CACJ,EAAG,EAAa,CAAC,EAAW,SAAW,OAAO,CAClD,CACA,GAAI,AAAgC,SAAtB,iBAAiB,CAAW,CAMtC,IAAM,EALO,AAKM,IALF,IAAI,CACjB,CAAC,aAAc,QAAQ,CACvB,CAAC,YAAa,QAAQ,CACtB,CAAC,YAAa,QAAQ,CACzB,EACuB,GAAG,CAAC,EAAU,oBAAoB,EAAE,YAC5D,GAAI,CAAC,EACD,MAAM,AAAI,IADG,MACO,8DAEZ,UAAR,GAAkC,SAAS,CAAxB,IACnB,EAAY,EAAU,WAAW,CAAC,CAC9B,KAAM,mBACN,CACJ,EAAG,EAAa,CAAC,EAAW,SAAW,QAAO,EAEtC,UAAR,GAAkC,AAAf,SAAwB,KAC3C,EAAY,EAAU,WAAW,CAAC,CAC9B,KAAM,mBACN,CACJ,EAAG,EAAa,CAAC,EAAW,SAAW,QAAO,EAE9C,AAAQ,aAA0B,SAAS,CAAxB,IACnB,EAAY,EAAU,WAAW,CAAC,CAC9B,KAAM,mBACN,CACJ,EAAG,EAAa,CAAC,EAAW,SAAW,QAAO,EAE9C,EAAI,UAAU,CAAC,YAAY,CAC3B,EAAY,EAAU,WAAW,CAAC,CAC9B,KAAM,kBACN,CACJ,EAAG,EAAa,EAAW,EAAE,CAAG,CAAC,cAAa,CAEtD,CACA,GAAI,CAAC,EACD,MAAM,AAAI,GADE,OACQ,8DAQxB,OANK,EAID,CAAM,CAAC,EAAI,CAAG,CAJL,CACT,EAAM,GAAG,CAAC,EAAW,CAAE,CAAC,EAAI,CAAE,CAAU,GAKrC,CACX,IACe,MAAO,EAAK,KACvB,GAAI,aAAe,YAGf,AAH2B,GAG3B,EAAA,WAAA,AAAU,EAAE,GAFZ,GAEkB,IAFX,EAKX,GAAI,CAAA,EAAA,EAAA,IAHA,OAGA,AAAU,EAAE,GAAM,CAClB,GAAiB,UAAU,CAAvB,EAAI,IADR,AACY,CACR,OAAO,EAAI,MAAM,GAErB,GAAI,gBAAiB,GAAkC,YAA3B,AAAuC,OAAhC,EAAI,WAAW,CAC9C,GAAI,CACA,OAAO,EAAgB,EAAK,EAChC,CACA,MAAO,EAAK,CACR,GAAI,aAAe,UACf,CAD0B,KACpB,CAEd,CAEJ,IAAI,EAAM,EAAI,MAAM,CAAC,CAAE,OAAQ,KAAM,GACrC,OAAO,EAAU,EAAK,EAAK,EAC/B,CACA,GAAI,GAAA,EAAA,KAAA,AAAI,EAAE,MAAM,IACZ,AAAI,EAAI,CAAC,CACE,CADA,AACA,EAAA,EAAA,MAAA,AAAK,EAAE,EAAI,CAAC,AAFvB,EAIO,EAAU,EAAK,EAAK,GAAK,EAEpC,OAAM,AAAI,MAAM,AAJD,cAKnB,+ECnKA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACO,OAAM,EACT,CAAA,CAAQ,AAAC,EACT,CAAA,AAAgB,AAAC,EACjB,CACA,AADA,AAAmB,AAAD,aACN,CAAO,CAAE,CACjB,GAAI,CAAC,CAAC,aAAmB,UAAA,CAAU,CAC/B,EADkC,IAC5B,AAAI,UAAU,6CAExB,IAAI,EAAC,CAAA,AAAQ,CAAG,CACpB,CACA,mBAAmB,CAAe,CAAE,CAChC,GAAI,IAAI,EAAC,CAAA,AAAgB,CACrB,CADuB,KACb,AAAJ,UAAc,8CAGxB,OADA,IAAI,EAAC,CAAgB,AAAhB,CAAmB,EACjB,IAAI,AACf,CACA,qBAAqB,CAAiB,CAAE,CACpC,GAAI,IAAI,EAAC,CAAA,AAAkB,CACvB,CADyB,KACnB,AAAI,UAAU,gDAGxB,OADA,IAAI,CAAC,CAAA,CAAkB,CAAG,EACnB,IAAI,AACf,CACA,MAAM,KAAK,CAAG,CAAE,CAAO,CAAE,KA4BjB,EA3BJ,GAAI,CAAC,IAAI,EAAC,CAAA,AAAgB,EAAI,CAAC,IAAI,EAAC,CAAA,AAAkB,CAClD,CADoD,KAC9C,IAAI,EAAA,UAAU,CAAC,0BAAX,yDAEd,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAS,EAAE,IAAI,CAAC,CAAA,CAAgB,CAAE,IAAI,CAAC,CAAA,CAAkB,EAC1D,CAD6D,KACvD,IAAI,CADT,CACS,UAAU,CAAC,0BAAX,mDAEd,IAAM,EAAa,CACf,GAAG,IAAI,EAAC,CAAA,AAAgB,CACxB,GAAG,IAAI,EAAC,CAAA,AAAkB,AAC9B,EACM,EAAa,CAAA,EAAA,EAAA,OAAA,AAAW,EAAE,EAAA,UAAU,CAAE,IAAI,IAAI,CAAC,CAAC,KAAnC,EAA0C,EAAK,CAAC,EAAG,GAAS,CAA/C,IAAqD,IAAI,EAAC,CAAA,AAAgB,CAAE,GACxG,GAAM,EACV,GAAI,EAAW,GAAG,CAAC,QAAQ,AAEJ,WAAf,AAA0B,MAD9B,CACW,EADL,IAAI,EAAC,CAAA,AAAgB,CAAC,GAAA,AAAG,EAE3B,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,iDAGlB,GAAM,KAAE,CAAG,CAAE,CAAG,EAChB,GAAI,AAAe,iBAAR,GAAoB,CAAC,EAC5B,GADiC,GAC3B,IAAI,EAAA,UAAU,CAAC,0BAAX,mCAEd,CAAA,EAAA,EAAA,OAAA,AAAW,EAAE,EAAK,EAAK,QACvB,IAAI,EAAU,IAAI,EAAC,CAAA,AAAQ,CACvB,CAFJ,GAGI,CADK,CACK,EAAA,OAAO,CAAC,MAAM,CAAC,CAAA,EAAA,EAAA,MAAA,AAAG,EAAE,GAAA,EAI9B,EADA,EAHU,EAGN,EAAC,CAAA,AAAgB,CACH,CADK,CACL,OAAO,CAAC,IAJD,EAIO,CAAC,CAAA,EAAA,EAAA,MAAA,AAAG,EAAE,KAAK,IAAzB,KAAkC,CAAC,IAAI,EAAC,CAAA,AAAgB,IAGxD,EAAA,CAHe,MAGR,CAAC,MAAM,CAAC,IAErC,IAAM,EAAO,CAAA,EAAA,EAAA,MAAA,AAAK,CAFI,CAEF,EAAiB,EAAA,OAAO,CAAC,MAAM,CAAC,KAAM,GACpD,EAAI,AADG,MACG,CAAA,EAAA,EAAA,CADqB,MACV,AAAX,EAAa,EAAK,GAC5B,EAAY,MAAM,CAAA,EAAA,EAAA,OAAA,AAAG,EAAE,CADb,CACkB,EAAG,GAC/B,EAAM,CACR,UAAW,CAAA,EAAA,EAAA,GAFS,GAET,AAAG,EAAE,GAChB,QAAS,EACb,EAUA,OATI,GACA,EADK,CACD,CAJO,MAIA,CAAG,EAAA,OAAO,CAAC,MAAM,CAAC,EAAA,EAE7B,IAAI,EAAC,CAAA,AAAkB,EAAE,CACzB,EAAI,MAHU,AAGJ,CAAG,IAAI,EAAC,CAAA,AAAkB,AAAlB,EAElB,IAAI,CAAC,CAAA,CAAgB,EAAE,AACvB,GAAI,SAAS,CAAG,EAAA,OAAO,CAAC,MAAM,CAAC,EAAA,EAE5B,CACX,CACJ,gBAJ4B,4DCjF5B,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,OAAM,GACT,CAAA,AAAU,AAAC,AACX,aAAY,CAAO,CAAE,CACjB,IAAI,EAAC,CAAA,AAAU,CAAG,IAAI,EAAA,aAAa,CAAC,EACxC,CACA,mBAAmB,CAAe,AAFR,CAEU,CAEhC,OADA,IAAI,EAAC,CAAA,AAAU,CAAC,kBAAkB,CAAC,GAC5B,IAAI,AACf,CACA,MAAM,KAAK,CAAG,CAAE,CAAO,CAAE,CACrB,IAAM,EAAM,MAAM,IAAI,EAAC,CAAA,AAAU,CAAC,IAAI,CAAC,EAAK,GAC5C,QAAoB,IAAhB,EAAI,KAAuB,EAAhB,CACX,MAAM,AAAI,UAAU,6DAExB,MAAO,CAAA,EAAG,EAAI,SAAS,CAAC,CAAC,EAAE,EAAI,OAAO,CAAC,CAAC,EAAE,EAAI,SAAS,CAAA,CAAE,AAC7D,CACJ,+ECjBe,AAAC,GAAS,KAAK,KAAK,CAAC,EAAK,OAAO,GAAK,4ECCrD,IAIM,EAAQ,AAJR,sIAKS,AAAC,IACZ,IAMI,EANE,EAAU,EAAM,IAAI,CAAC,GAC3B,GAAI,CAAC,GAAY,CAAO,CAAC,EAAE,EAAI,CAAO,CAAC,EAAE,CACrC,CADwC,KAClC,AAAI,UAAU,8BAExB,IAAM,EAAQ,WAAW,CAAO,CAAC,EAAE,EAGnC,OAFa,AAEL,CAFY,CAAC,EAAE,CAAC,WAAW,IAG/B,IAAK,MACL,IAAK,OACL,IAAK,SACL,IAAK,UACL,IAAK,IACD,EAAc,KAAK,KAAK,CAAC,GACzB,KACJ,KAAK,SACL,IAAK,UACL,IAAK,MACL,IAAK,OACL,IAAK,IACD,EAAc,KAAK,KAAK,CA3BrB,GA2BsB,GACzB,KADiC,AAErC,KAAK,OACL,IAAK,QACL,IAAK,KACL,IAAK,MACL,IAAK,IACD,EAAc,KAAK,KAAK,CAAC,AAjCxB,QAiCgC,AACjC,CAlCU,IAmCd,KAAK,MACL,IAAK,OACL,IAAK,IACD,EAAc,KAAK,KAAK,CArCxB,MAqCyB,CArClB,EAsCP,KADiC,AAErC,KAAK,OACL,IAAK,QACL,IAAK,IACD,EAAc,KAAK,KAAK,CAzCvB,MAAM,CAyCkB,GACzB,KADiC,AAErC,SACI,EAAc,KAAK,KAAK,CA3CvB,MAAM,IA2CkB,EAEjC,MAFyC,CAGzC,AAAmB,MAAf,CAAO,CAAC,EAAE,EAA2B,OAAO,CAAtB,CAAO,CAAC,EAAE,CACzB,CAAC,EAEL,CACX,0GCtDA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,SAAS,EAAc,CAAK,CAAE,CAAK,EAC/B,GAAI,CAAC,OAAO,QAAQ,CAAC,GACjB,KADyB,CACf,AAAJ,UAAc,CAAC,QAAQ,EAAE,EAAM,MAAM,CAAC,EAEhD,OAAO,CACX,CACA,IAAM,EAAe,AAAC,GAClB,AAAI,EAAM,QAAQ,CAAC,KACR,CADc,CACR,WAAW,GAErB,CAAC,YAAY,EAAE,EAAM,WAAW,GAAA,CAAI,CAEzC,EAAwB,CAAC,EAAY,IACb,AAA1B,UAAoC,AAAhC,OAAO,EACA,EAAU,QAAQ,CAAC,KAE1B,MAAM,OAAO,CAAC,IACP,EAAU,IAAI,CAAC,EADK,EACD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,KAItD,SAAS,EAAkB,CAAe,CAAE,CAAc,CAAE,EAAU,CAAC,CAAC,MACvE,EAyCA,EAxCJ,GAAI,CACA,EAAU,KAAK,KAAK,CAAC,EAAA,OAAO,CAAC,MAAM,CAAC,GACxC,CACA,KAAM,CACN,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,EAJoB,KAIpB,AAAO,EAAE,GACV,MAAM,CADc,GACV,EAAA,UAAU,CAAC,EADpB,wBACS,wBAEd,GAAM,KAAE,CAAG,CAAE,CAAG,EAChB,GAAI,IACgC,GAAhC,OAAC,OAAO,EAAgB,GAAG,EACvB,EAAa,EAAgB,GAAG,IAAM,EAAa,EAAA,CAAI,CAC3D,EAD8D,IACxD,IAAI,EAAA,wBAAwB,CAAC,YAAzB,wBAA8D,EAAS,MAAO,gBAE5F,GAAM,CAAE,iBAAiB,EAAE,CAAE,QAAM,SAAE,CAAO,UAAE,CAAQ,aAAE,CAAW,CAAE,CAAG,EAClE,EAAgB,IAAI,EAAe,CASzC,IAAK,IAAM,UARS,IAAhB,GACA,EAAc,IAAI,CAAC,YACN,IAAb,GACA,EAAc,IAAI,CAAC,YACP,IAAZ,GACA,EAAc,IAAI,CAAC,YACR,IAAX,GACA,EAAc,IAAI,CAAC,OACH,IAAI,IAAI,EAAc,OAAO,IAAK,CAClD,GAAI,CAAC,CAAC,KAAS,CAAA,CAAO,CAClB,EADqB,IACf,IAAI,EAAA,wBAAwB,CAAC,CAAC,WAA1B,OAA4C,EAAE,EAAM,OAAO,CAAC,CAAE,EAAS,EAAO,WAGhG,GAAI,GACA,CAAC,CAAC,MAAM,OAAO,CAAC,GAAU,EAAS,CAAC,EAAO,EAAE,QAAQ,CAAC,EAAQ,GAAG,EACjE,CADoE,KAC9D,IAAI,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,gBAEvF,GAAI,GAAW,EAAQ,GAAG,GAAK,EAC3B,MAAM,CAD8B,GAC1B,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,gBAEvF,GAAI,GACA,CAAC,EAAsB,EAAQ,GAAG,CAAsB,UAApB,OAAO,EAAwB,CAAC,EAAS,CAAG,GAChF,MAAM,EADqF,EACjF,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,gBAGvF,OAAQ,OAAO,EAAQ,cAAc,EACjC,IAAK,SACD,EAAY,CAAA,EAAA,EAAA,OAAA,AAAG,EAAE,EAAQ,cAAc,EACvC,KACJ,KAAK,AAFW,SAGZ,EAAY,EAAQ,cAAc,CAClC,KACJ,KAAK,YACD,EAAY,EACZ,KACJ,SACI,MAAM,AAAI,UAAU,qCAC5B,CACA,GAAM,CAAE,aAAW,CAAE,CAAG,EAClB,EAAM,CAAA,EAAA,EAAA,OAAA,AAAI,EAAE,GAAe,IAAI,MACrC,GAAI,MAAiB,IAAhB,EADO,AACC,GAAG,EAAkB,CAAA,CAAW,EAAK,AAAuB,UAAU,OAA1B,EAAQ,GAAG,CAChE,MAAM,IAAI,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,WAEvF,QAAoB,IAAhB,EAAQ,GAAG,CAAgB,CAC3B,GAA2B,UAAvB,AAAiC,OAA1B,EAAQ,GAAG,CAClB,MAAM,IAAI,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,WAEvF,GAAI,EAAQ,GAAG,CAAG,EAAM,EACpB,MAAM,GADyB,CACrB,EAAA,wBAAwB,CAAC,YAAzB,yBAA+D,EAAS,MAAO,eAEjG,CACA,GAAI,KAAgB,MAAR,GAAG,CAAgB,CAC3B,GAA2B,UAAvB,AAAiC,OAA1B,EAAQ,GAAG,CAClB,MAAM,IAAI,EAAA,wBAAwB,CAAC,YAAzB,mBAAyD,EAAS,MAAO,WAEvF,GAAI,EAAQ,GAAG,EAAI,EAAM,EACrB,MAAM,GAD0B,CACtB,EAAA,UAAU,CAAC,0BAAX,WAAiD,EAAS,MAAO,eAEnF,CACA,GAAI,EAAa,CACb,IAAM,EAAM,EAAM,EAAQ,GAAG,CAE7B,GAAI,EAAM,GADyB,SACb,CADV,IACe,GADR,EAA2B,EAAc,CAAA,EAAA,EAAA,OAAA,AAAG,EAAE,EAAA,EAE7D,MAAM,IAAI,EAAA,UAAU,CAAC,CAFmC,yBAE9C,iCAAuE,EAAS,MAAO,gBAErG,GAAI,EAAM,EAAI,EACV,MAAM,GADe,CACX,EAAA,wBAAwB,CAAC,YAAzB,oDAA0F,EAAS,MAAO,eAE5H,CACA,OAAO,CACX,CACO,MAAM,GACT,CAAQ,AACR,AADA,AAAS,aACG,CAAO,CAAE,CACjB,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAE,GACV,MAAM,AAAI,CADU,SACA,SADnB,2BAGL,IAAI,EAAC,CAAA,AAAQ,CAAG,gBAAgB,EACpC,CACA,MAAO,CACH,OAAO,EAAA,OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,CAAQ,AAA3C,EACX,CACA,IAAI,KAAM,CACN,OAAO,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,AAC5B,CACA,IAAI,IAAI,CAAK,CAAE,CACX,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,CACxB,CACA,IAAI,KAAM,CACN,OAAO,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,AAC5B,CACA,IAAI,IAAI,CAAK,CAAE,CACX,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,CACxB,CACA,IAAI,KAAM,CACN,OAAO,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,AAC5B,CACA,IAAI,IAAI,CAAK,CAAE,CACX,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,CACxB,CACA,IAAI,IAAI,CAAK,CAAE,CACX,IAAI,CAAC,CAAA,CAAQ,CAAC,GAAG,CAAG,CACxB,CACA,IAAI,IAAI,CAAK,CAAE,CACU,UAAjB,AAA2B,OAApB,EACP,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,EAAc,eAAgB,GAE7C,aAAiB,KACtB,CAD4B,GACxB,EAAC,CAAQ,AAAR,CAAS,GAAG,CAAG,EAAc,eAAgB,CAAA,EAAA,EAAA,OAAA,AAAI,EAAE,IAGxD,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,CAAA,EAAA,EAAA,OAAA,AAAI,AAH0B,EAGxB,IAAI,MAAU,GAAA,EAAA,OAAA,AAAG,EAAE,EAErD,CACA,CAH4B,GAGxB,IAAI,CAAK,CAAE,CACU,UAAjB,AAA2B,IAJa,GAIjC,EACP,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,EAAc,oBAAqB,GAElD,aAAiB,KACtB,CAD4B,GACxB,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,EAAc,oBAAqB,CAAA,EAAA,EAAA,OAAI,AAAJ,EAAM,IAG7D,IAAI,CAAC,CAAA,CAAQ,CAAC,GAAG,CAAG,CAAA,EAAA,EAAA,OAAA,AAAI,AAH+B,EAG7B,IAAI,MAAU,GAAA,EAAA,OAAA,AAAG,EAAE,EAErD,CACA,CAH4B,GAGxB,IAAI,CAAK,CAAE,CACP,KAAiB,IAAV,EACP,GALwC,CAKpC,CAAC,CAAA,CAAQ,AADiB,CAChB,GAAG,CAAG,CAAA,EAAA,EAAA,OAAA,AAAI,EAAE,IAAI,MAEzB,aAAiB,KAFF,AAGpB,CAD4B,GACxB,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAG,EAAc,cAAe,CAAA,EAAA,EAAA,OAAA,AAAI,EAAE,IAEjC,UAAjB,AAA2B,OAApB,EACZ,IAAI,CAH6C,CAG5C,CAAA,AAAQ,CAAC,GAAG,CAAG,EAAc,cAAe,CAAA,EAAA,EAAA,OAAA,AAAI,EAAE,IAAI,MAAU,CAAA,EAAA,EAAA,OAAA,AAAG,EAAE,IAG1E,AAHiD,IAG7C,CAAC,CAAA,CAAQ,CAAC,GAAG,CAAG,EAAc,UAHmC,IAGpB,EAEzD,CACJ,yECxLA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACO,OAAM,GACT,CAAA,AAAgB,AAAC,EACjB,CACA,AADK,AAAL,AAAI,aACQ,EAAU,CAAC,CAAC,CAAE,CACtB,IAAI,EAAC,CAAA,AAAI,CAAG,IAAI,EAAA,gBAAgB,CAAC,EACrC,CACA,UAAU,CAAM,CAAE,CAEd,IAJgB,GAGhB,IAAI,EAAC,CAAA,AAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,WAAW,CAAO,CAAE,CAEhB,OADA,IAAI,CAAC,CAAA,CAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,YAAY,CAAQ,CAAE,CAElB,OADA,IAAI,EAAC,CAAA,AAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,OAAO,CAAK,CAAE,CAEV,OADA,IAAI,CAAC,CAAA,CAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,aAAa,CAAK,CAAE,CAEhB,OADA,IAAI,EAAC,CAAA,AAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,kBAAkB,CAAK,CAAE,CAErB,OADA,IAAI,EAAC,CAAA,AAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,YAAY,CAAK,CAAE,CAEf,OADA,IAAI,EAAC,CAAA,AAAI,CAAC,GAAG,CAAG,EACT,IAAI,AACf,CACA,mBAAmB,CAAe,CAAE,CAEhC,OADA,IAAI,EAAC,CAAA,AAAgB,CAAG,EACjB,IAAI,AACf,CACA,MAAM,KAAK,CAAG,CAAE,CAAO,CAAE,CACrB,IAAM,EAAM,IAAI,EAAA,WAAW,CAAC,IAAI,EAAC,CAAA,AAAI,CAAC,IAAI,IAE1C,GADA,EAAI,GADY,eACM,CAAC,IAAI,CAAC,CAAA,CAAgB,EACxC,MAAM,OAAO,CAAC,IAAI,EAAC,CAAA,AAAgB,EAAE,OACrC,IAAI,EAAC,CAAA,AAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,QACN,KAA9B,EAAqC,EAAjC,CAAC,CAAA,CAAgB,CAAC,GAAG,CACzB,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,aAEd,OAAO,EAAI,IAAI,CAAC,EAAK,EACzB,CACJ,yECnDA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,cACe,MAAO,EAAK,EAAK,EAAW,KACvC,IAAM,EAAY,MAAM,CAAA,EAAA,EAAA,OAAW,AAAX,EAAa,EAAK,EAAK,UAC/C,CAAA,EAAA,EAAA,OAAA,AAAa,EAAE,AADS,EACJ,GACpB,IAAM,EAAY,CAAA,EAAA,EAAA,OAAA,AAAc,EAAE,EAAK,AADvC,EACiD,SAAS,EAC1D,GAAI,CACA,OAAO,EAFO,IAED,OAAO,MAAM,CAAC,MAAM,CAAC,EAAW,EAAW,EAAW,EACvE,CACA,KAAM,CACF,OAAO,CACX,CACJ,+ECbe,CAAC,EAAQ,KACpB,QAAmB,IAAf,IACC,CAAC,IAAF,EAAQ,OAAO,CAAC,IAAe,EAAW,IAAI,CAAE,AAAD,GAAoB,UAAb,OAAO,EAAM,CAAS,CAC5E,EAD+E,IACzE,AAAI,UAAU,CAAC,CAAC,EAAE,EAAO,oCAAoC,CAAC,EAExE,GAAK,CAAD,CAGJ,OAAO,GAHU,CAGN,IAAI,EACnB,gFCTA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACO,eAAe,EAAgB,CAAG,CAAE,CAAG,CAAE,CAAO,MAmE/C,EAYA,EA9EJ,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAE,GACV,GADgB,GACV,IAAI,EAAA,UAAU,CAAC,EADpB,wBACS,SAEd,GAAI,KAAkB,MAAd,SAAS,OAAiC,IAAf,EAAI,KAAsB,CAAhB,CACzC,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,+CAEd,QAAsB,IAAlB,EAAI,SAAS,EAA2C,UAAzB,AAAmC,OAA5B,EAAI,SAAS,CACnD,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,aAEd,QAAoB,IAAhB,EAAI,KAAuB,EAAhB,CACX,MAAM,IAAI,EAAA,UAAU,CAAC,uBAEzB,GAFc,AAEe,UAAzB,AAAmC,OAA5B,EAAI,SAAS,CACpB,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,iBAEd,QAAmB,IAAf,EAAI,MAAM,EAAkB,CAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAE,EAAI,MAAM,EAChD,CADmD,KAC7C,IAAI,EAAA,MADmB,IACT,CAAC,0BAAX,eAEd,IAAI,EAAa,CAAC,EAClB,GAAI,EAAI,SAAS,CACb,CADe,EACX,CACA,IAAM,EAAkB,CAAA,EAAA,EAAA,MAAA,AAAG,EAAE,EAAI,SAAS,EAC1C,EAAa,KAAK,KAAK,CAAC,EAAA,CADA,MACO,CAAC,MAAM,CAAC,GAC3C,CACA,KAAM,CACF,MAAM,IAAI,EAHc,AAGd,UAAU,CAAC,0BAAX,QACd,CAEJ,GAAI,CAAC,CAAA,EAAA,EAAA,OAAA,AAAS,EAAE,EAAY,EAAI,MAAM,EAClC,CADqC,KAC/B,IAAI,EAAA,IADT,MACmB,CAAC,0BAAX,mDAEd,IAAM,EAAa,CACf,GAAG,CAAU,CACb,GAAG,EAAI,MAAM,AACjB,EACM,EAAa,CAAA,EAAA,EAAA,OAAA,AAAW,EAAE,EAAA,UAAU,CAAE,IAAI,IAAI,CAAC,CAAC,KAAnC,EAA0C,EAAK,CAAC,EAAG,GAAS,CAA/C,IAAqD,EAAY,GAC7F,GAAM,EACV,GAAI,EAAW,GAAG,CAAC,QAAQ,AAEJ,WAAf,AAA0B,OAD9B,AACW,EADL,EAAW,GAAA,AAAG,EAEhB,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,iDAGlB,GAAM,KAAE,CAAG,CAAE,CAAG,EAChB,GAAmB,UAAf,OAAO,GAAoB,CAAC,EAC5B,GADiC,GAC3B,IAAI,EAAA,UAAU,CAAC,0BAAX,mCAEd,IAAM,EAAa,GAAW,CAAA,EAAA,EAAA,OAAiB,AAAjB,EAAmB,aAAc,EAAQ,UAAU,EACjF,CAD8B,EAC1B,GAAc,CAAC,EAAW,GAAG,CAAC,GAC9B,GADoC,GAC9B,IAAI,EAAA,iBAAiB,CAAC,mBAAlB,qCAEd,GAAI,GACA,EADK,CACsB,UAAU,AAAjC,OAAO,EAAI,OAAO,CAClB,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,KACd,MAEC,GAAI,AAAuB,iBAAhB,EAAI,OAAO,EAAiB,CAAC,CAAC,EAAI,OAAO,YAAY,UAAA,CAAU,CAC3E,EAD8E,IACxE,IAAI,EAAA,UAAU,CAAC,0BAAX,gCAEd,IAAI,EAAc,GACC,YAAf,AAA2B,OAApB,IACP,EAAM,MAAM,EAAI,EAAY,GAC5B,GAAc,GAElB,CAAA,EAAA,EAAA,OAAA,AAAW,EAAE,EAAK,EAAK,UACvB,IAAM,EAAO,CAAA,EAAA,EAAA,EADb,IACa,AAAK,EAAE,EAAA,OAAO,CAAC,MAAM,CAAC,EAAI,SAAS,CAAnC,CAAuC,IAAK,EAAA,GAArC,IAA4C,CAAC,MAAM,CAAC,KAA6B,UAAvB,OAAO,AAA5B,EAAgC,OAAO,CAAgB,EAAA,OAAO,CAAC,MAAM,CAAC,EAAI,OAAO,EAAI,EAAI,OAAO,EAAzC,AAEhH,GAAI,CACA,EAAY,CAAA,EAAA,EAAA,MAAA,AAAG,EAAE,EAAI,SAAS,CAClC,CACA,KAAM,CACF,MAAM,IAHM,AAGF,EAAA,UAAU,CAAC,0BAAX,iBACd,CACA,IAAM,EAAI,MAAM,CAAA,EAAA,EAAA,OAAA,AAAW,EAAE,EAAK,GAElC,GAAI,CADa,AACZ,MADkB,GAAA,CACR,CADQ,OAAA,AAAK,CADZ,CACc,EAAK,EAAG,EAAW,GAE7C,MAAM,IAAI,EAAA,OAFS,uBAEqB,CAG5C,GAAI,EACA,CAJU,EAIN,AADC,CAED,EAAU,CAAA,EAAA,EAAA,MAAA,AAAG,EAAE,EAAI,OAAO,CAC9B,CACA,KAAM,CACF,MAAM,IAAI,EAHA,AAGA,UAAU,CAAC,0BAAX,eACd,MAGA,EADK,AAAuB,UAAU,OAA1B,EAAI,OAAO,CACb,EAAA,OAAO,CAAC,MAAM,CAAC,EAAI,OAAO,EAG1B,EAAI,OAAO,CAEzB,CALc,GAKR,EAAS,SAAE,CAAQ,QAOzB,MANsB,IAAlB,EAAI,KAAyB,IAAhB,GACb,EAAO,eAAe,CAAG,CAAA,OAEV,IAAf,EAAI,KAAsB,CAAhB,GACV,EAAO,iBAAiB,CAAG,EAAI,MAAA,AAAM,EAErC,GACO,CAAE,GAAG,CAAM,CAAE,IADP,AACY,CAAE,EAExB,CACX,6ECnHA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACO,eAAe,EAAc,CAAG,CAAE,CAAG,CAAE,CAAO,EAIjD,GAHI,aAAe,YAAY,CAC3B,EAAM,EAAA,OAAO,CAAC,MAAM,CAAC,EAAA,EAEN,UAAf,AAAyB,OAAlB,CAFD,CAGN,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,oBAEd,GAAM,CAAE,EAAG,CAAe,CAAE,EAAG,CAAO,CAAE,EAAG,CAAS,CAAE,QAAM,CAAE,CAAG,EAAI,KAAK,CAAC,KAC3E,GAAe,GAAG,CAAd,EACA,MAAM,IAAI,EAAA,UAAU,CAAC,uBAEzB,GAFc,CAER,EAAW,MAAM,CAAA,EAAA,EAAA,eAAA,AAAc,EAAE,SAAE,EAAS,SAA3B,CAAsC,YAAiB,CAAU,EAAG,EAAK,GAC1F,EAAS,CAAE,QAAS,EAAS,OAAO,CAAE,gBAAiB,EAAS,eAAe,AAAC,QACtF,AAAmB,YAAf,AAA2B,OAApB,EACA,CAAE,GAAG,CAAM,CAAE,IAAK,EAAS,GAAG,AAAC,EAEnC,CACX,yECpBA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACO,eAAe,EAAU,CAAG,CAAE,CAAG,CAAE,CAAO,EAC7C,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,EAAE,EAAK,EAAK,GAC/C,GAAI,EAAS,UADU,KACK,CAAC,IAAI,EAAE,SAAS,QAA2C,KAAjC,EAAS,AAA+B,eAAhB,CAAC,GAAG,CAC9E,MAAM,IAAI,EAAA,UAAU,CAAC,0BAAX,aAGd,IAAM,EAAS,CAAE,QADD,CAAA,EAAA,EAAA,iBAAA,AAAgB,EAAE,EAAS,eAAe,CAA1C,AAA4C,EAAS,OAAO,CAAE,GACpD,gBAAiB,EAAS,eAAe,AAAC,QACpE,AAAmB,YAAf,AAA2B,OAApB,EACA,CAAE,GAAG,CAAM,CAAE,IAAK,EAAS,GAAG,AAAC,EAEnC,CACX", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]}