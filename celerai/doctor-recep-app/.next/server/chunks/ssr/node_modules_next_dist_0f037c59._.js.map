{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/router-reducer/router-reducer-types.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/is-thenable.ts", "turbopack:///[project]/node_modules/next/src/client/components/use-action-queue.ts", "turbopack:///[project]/node_modules/next/src/client/app-call-server.ts", "turbopack:///[project]/node_modules/next/src/client/app-find-source-map-url.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils/warn-once.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/side-effect.tsx", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/amp-context.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/amp-mode.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/head.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/image-blur-svg.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/image-config.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/get-img-props.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/image-config-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/router-context.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/image-loader.ts", "turbopack:///[project]/node_modules/next/src/client/image-component.tsx"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_PREFETCH = 'prefetch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  navigatedAt,\n  previousTree,\n  serverResponse,\n}: {\n  navigatedAt: number\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  prefetchCache?: AppRouterState['prefetchCache']\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n  allowAliasing: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  tree: FlightRouterState | undefined\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  navigatedAt: number\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\nexport interface PrefetchAction {\n  type: typeof ACTION_PREFETCH\n  url: URL\n  kind: PrefetchKind\n}\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\nexport type PrefetchCacheEntry = {\n  treeAtTimeOfPrefetch: FlightRouterState\n  data: Promise<FetchServerResponseResult>\n  kind: PrefetchKind\n  prefetchTime: number\n  staleTime: number\n  lastUsedTime: number | null\n  key: string\n  status: PrefetchCacheEntryStatus\n  url: URL\n}\n\nexport enum PrefetchCacheEntryStatus {\n  fresh = 'fresh',\n  reusable = 'reusable',\n  expired = 'expired',\n  stale = 'stale',\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   * Prefetched data is stored separately in `prefetchCache`, that is applied during ACTION_NAVIGATE.\n   */\n  cache: CacheNode\n  /**\n   * Cache that holds prefetched Flight responses keyed by url.\n   */\n  prefetchCache: Map<string, PrefetchCacheEntry>\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState = Promise<AppRouterState> | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | PrefetchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n", "import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n", "import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n", "const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AmpContext\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ImageConfigContext\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].RouterContext\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n"], "names": ["ACTION_HMR_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_ACTION", "ACTION_SERVER_PATCH", "PrefetchCacheEntryStatus", "PrefetchKind", "isThenable", "promise", "then", "dispatchAppRouterAction", "useActionQueue", "dispatch", "action", "Error", "actionQueue", "state", "setState", "React", "useState", "process", "env", "NODE_ENV", "use", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "startTransition", "type", "findSourceMapURL", "basePath", "__NEXT_ROUTER_BASEPATH", "pathname", "filename", "undefined", "warnOnce", "_", "SideEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "module", "exports", "require", "vendored", "AmpContext", "isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "map", "c", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "cloneElement", "Head", "ampState", "useContext", "AmpStateContext", "HeadManagerContext", "Effect", "getImageBlurSvg", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "blurDataURL", "objectFit", "std", "svgWidth", "svgHeight", "viewBox", "preserveAspectRatio", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "remotePatterns", "qualities", "unoptimized", "getImgProps", "VALID_LOADING_VALUES", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "smallestRatio", "Math", "min", "widths", "s", "kind", "w", "find", "p", "generateImgAttrs", "config", "quality", "srcSet", "last", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "fetchPriority", "decoding", "layout", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "sort", "a", "b", "isDefaultLoader", "customImageLoader", "obj", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "split", "endsWith", "qualityInt", "imgStyle", "Object", "assign", "position", "left", "top", "right", "bottom", "color", "backgroundImage", "backgroundSize", "includes", "placeholder<PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "imgAttributes", "ImageConfigContext", "RouterContext", "DEFAULT_Q", "q", "prev", "cur", "abs", "encodeURIComponent", "NEXT_DEPLOYMENT_ID", "__next_img_default", "Image", "configEnv", "__NEXT_IMAGE_OPTS", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "catch", "parentElement", "isConnected", "current", "event", "Event", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "getDynamicProps", "fetchpriority", "ImageElement", "forwardRef", "forwardedRef", "setShowAltText", "onError", "ownRef", "useCallback", "complete", "ref", "useMergedRef", "data-nimg", "ImagePreload", "isAppRouter", "as", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "link", "rel", "href", "pagesRouter", "configContext", "useMemo", "useRef", "imgMeta"], "mappings": "qIAmJYQ,EAgEAD,KAvMCP,KAuIDQ,aAvImB,CAuMnBD,AAvMmB,kBAAlBP,GAJAC,eAAe,CAAA,kBAAfA,GAGAC,eAAe,CAAA,kBAAfA,GAJAC,cAAc,CAAA,kBAAdA,GAEAC,cAAc,CAAA,kBAAdA,GAIAC,oBAAoB,CAAA,kBAApBA,GAHAC,mBAAmB,CAAA,kBAAnBA,GAyMDC,wBAAwB,CAAA,kBAAxBA,GAhEAC,YAAY,CAAA,kBAAZA,uEA5IL,IAAML,EAAiB,UACjBF,EAAkB,WAClBG,EAAiB,UACjBE,EAAsB,eACtBJ,EAAkB,WAClBF,EAAqB,cACrBK,EAAuB,gBAsI7B,IAAKG,eAAAA,WAAAA,GAAAA,+BAAAA,GAgEAD,2BAAAA,WAAAA,GAAAA,yCAAAA,8QC9MX,aACM,SAASE,EACdC,CAAuB,EAEvB,OACc,OAAZA,GACmB,UAAnB,OAAOA,GACP,SAAUA,GACc,YAAxB,OAAOA,EAAQC,IAAI,AAEvB,0EATgBF,aAAAA,qCAAAA,8HCSAG,uBAAuB,CAAA,kBAAvBA,GASAC,cAAc,CAAA,kBAAdA,6FAvBW,CAAA,CAAA,IAAA,SACA,CAAA,CAAA,IAAA,IAWvBC,EAA4C,KAEzC,SAASF,EAAwBG,CAAsB,EAC5D,GAAiB,AAAbD,MAAmB,GACrB,MAAM,OAAA,cAEL,CAFK,AAAIE,MACR,2EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEFF,EAASC,EACX,CAEO,SAASF,EACdI,CAAiC,EAEjC,GAAM,CAACC,EAAOC,EAAS,CAAGC,EAAAA,OAAK,CAACC,QAAQ,CAAeJ,EAAYC,KAAK,EA0BxE,OAJEJ,EAAW,AAACC,GACVE,EAAYH,QAAQ,CAACC,EAAQI,GAG1BV,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACS,GAASO,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACP,GAASA,CAC1C,mWClDsBQ,aAAAA,qCAAAA,aAJU,CAAA,CAAA,IAAA,QACK,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,IAEjC,eAAeA,EAAWC,CAAgB,CAAEC,CAAiB,EAClE,OAAO,IAAIC,QAAQ,CAACC,EAASC,KAC3BC,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACdpB,CAAAA,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBqB,KAAM5B,EAAAA,oBAAoB,UAC1BsB,EACAC,qBACAE,SACAC,CACF,EACF,EACF,EACF,mWCbaG,mBAAAA,qCAAAA,KAAN,IAAMA,OA0BPK,YAzBJjB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,SAASU,iBAAiBI,QAAgB,6QCMvCE,WAAAA,qCAAAA,KAXT,IAAIA,EAAYC,AAAD,IAAgB,6ICoB/B,UAAA,qCAAwBC,aAnBuC,CAAA,CAAA,IAAA,IAezDC,EAA6B,aAAlB,OAAOC,OAClBC,EAA4BF,EAAW,KAAO,EAAIG,EAAAA,eAAe,CACjEC,EAAsBJ,EAAW,KAAO,EAAIK,EAAAA,SAAS,CAE5C,SAASN,EAAWO,CAAsB,EACvD,GAAM,aAAEC,CAAW,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,CAEA,GAAIN,EAAU,KACZO,CAAAA,OAAAA,GAAAA,AAA6B,EAA7BA,KAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EACjDX,GACF,CAsCA,OApCAP,EAA0B,SACxBK,EACA,OADAA,MAAAA,CAAAA,EAAAA,AAA6B,GAA7BA,IAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAA,AAAhBA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAAAA,AAA6B,EAA7BA,KAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOAlB,EAA0B,KACpBK,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAGFL,EAAoB,KACdG,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,MAExB,KACDf,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,KAEjC,IAGK,IACT,kEC5EAC,GAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACC,UAAU,iECFL,SAASC,EAAY,CAAA,EAAA,GAAA,CAC1BC,WAAW,EAAK,CAChBC,UAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,KAAA,IAAA,EAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,0EANgBH,cAAAA,qCAAAA,8HCuMhB,OAAmB,CAAA,kBAAnB,GA1LgBI,WAAW,CAAA,kBAAXA,6HAX4B,CAAA,CAAA,IAAA,aACzB,CAAA,CAAA,IAAA,SACa,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACP,CAAA,CAAA,IAAA,IAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,GAAAA,IAAY,CAAA,EACtC,IAAMC,EAAO,CAAC,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,AAACH,GACHC,EAAKG,IAAI,CAAA,AACP,CAAA,AAFY,EAEZ,EAAA,GAAA,EAACF,CADM,MACNA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,AAAqB,UAAjB,OAAOA,GAAsB,AAAiB,UAAU,OAApBA,EAC/BD,EAGLC,EAAMpD,IAAI,GAAKb,EAAAA,OAAK,CAACkE,QAAQ,CACxBF,CAD0B,CACrBG,MAAM,CAChB,EACAnE,OAAK,CAACmC,QAAQ,CAACC,OAAO,CAAC6B,EAAMpC,KAAK,CAACc,QAAQ,EAAEyB,MAAM,CAEjD,CACEC,EACAC,IAEA,AAC2B,UAAzB,OAAOA,EARsF,CASpE,UAAzB,AACA,OADOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDN,EAAKG,MAAM,CAACF,EACrB,GA/CyB,CAAA,CAAA,IAAA,EA6BkF,EAoB3G,IAAMM,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASqB,EACPC,CAAoD,CACpDhE,CAAQ,EAER,GAAM,WAAE2B,CAAS,CAAE,CAAG3B,EACtB,OAAOgE,EACJzB,MAAM,CAACL,EAAkB,EAAE,EAC3B+B,OAAO,GACP3B,MAAM,CAACZ,EAAYC,GAAWsC,OAAO,IACrCvD,MAAM,CAxEX,AAwEYiC,SAxEHA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAO,AAACC,IACN,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIF,EAAEG,GAAG,EAAqB,UAAjB,OAAOH,EAAEG,GAAG,EAAiBH,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEF,GAAS,EACT,IAAMC,EAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCT,EAAKW,GAAG,CAACH,GACXF,GADiB,AACN,EAEXN,EAAK/B,GAAG,CAACuC,EAEb,CAGA,OAAQH,EAAEjE,IAAI,EACZ,IAAK,QACL,IAAK,OACC8D,EAAKS,GAAG,CAACN,EAAEjE,IAAI,EACjBkE,CADoB,EACT,EAEXJ,EAAKjC,GAAG,CAACoC,EAAEjE,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAIwE,EAAI,EAAGC,EAAMf,EAAUgB,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWjB,CAAS,CAACc,EAAE,CAC7B,GAAKP,CAAD,CAAGjD,KAAK,CAAC4D,cAAc,CAACD,GAE5B,GAAiB,KAFsB,MAEX,CAAxBA,EACEZ,EAAUQ,GAAG,CAACI,GAChBT,GAAW,EAEXH,EAAUlC,CAHiB,EAGd,CAAC8C,OAEX,CACL,IAAME,EAAWZ,EAAEjD,KAAK,CAAC2D,EAAS,CAC5BG,EAAad,CAAc,CAACW,EAAS,EAAI,IAAId,GAC9Cc,EAAa,SAAbA,GAAuB,CAACR,CAAAA,CAAK,EAAMW,EAAWP,GAAG,CAACM,GACrDX,GAAW,GAEXY,EAHgE,AAGrDjD,GAAG,CAACgD,GACfb,CAAc,CAACW,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOZ,CACT,CACF,KAgBKe,OAAO,GACPC,GAAG,CAAC,CAACC,EAA4BX,KAChC,IAAMJ,EAAMe,EAAEf,GAAG,EAAII,EACrB,GACEnF,AACAA,QADQC,AACAA,GADG,AACA,CADCC,AACA6F,QADQ,aACa,EACjC,CAACzC,GAGCwC,AAAW,QAFb,GAEInF,CALqB,GAKjB,EACNmF,EAAEnE,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAACqE,IAAI,CACnE,AAACC,GAAQH,EAAEnE,KAAK,CAAC,IAAO,CAACuE,OAF+D,GAErD,CAACD,IAEtC,CACA,IAAME,EAAW,CAAE,GAAIL,EAAEnE,KAAK,EAAI,CAAC,CAAC,AAAE,EAOtC,OANAwE,AAMA,CANQ,CAAC,SAMT,GANqB,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAWlF,EAGnBkF,CAAQ,CAAC,uBAAuB,CAAG,GAE5BrG,EAAAA,OAAK,CAACsG,YAAY,CAACN,EAAGK,EAC/B,CAiBF,OAAA,AAAOrG,EAAAA,OAAK,CAACsG,CAAb,WAAyB,CAACN,EAAG,KAAEf,CAAI,EACrC,EACJ,KAoBA,EAdA,SAASsB,AAAK,AAcCA,CAd0C,EAA3C,GAAA,UAAE5D,CAAQ,CAAiC,CAA3C,EACN6D,EAAWC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,eAAe,EACrC5E,EAAc2E,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACE,EAAAA,kBAAkB,EACjD,MACE,CAAA,AADF,EACE,EAAA,GAAA,EAACC,EAAAA,AADH,OACS,CAAA,CACL7E,wBAAyB6D,EACzB9D,YAAaA,EACb0B,UAAWL,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACqD,YAEtB7D,GAGP,2QCnMC,cACM,SAASkE,EAAgB,CAc/B,EAd+B,GAAA,UAC9BC,CAAQ,WACRC,CAAS,WACTC,CAAS,YACTC,CAAU,aACVC,CAAW,CACXC,WAAS,CAQV,CAd+B,EAgBxBE,EAAWL,EAAwB,GAAZA,EAAiBF,EACxCQ,EAAYL,EAA0B,GAAbA,EAAkBF,EAE3CQ,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,MAAQ,6CAA4CC,UAAQ,8FAA2FH,MAAI,oQAAiQA,MAAI,qEARpYG,EACxB,OACAJ,AAAc,cACZ,IAKufK,OAJze,UAAdL,EACE,iBACA,MAAA,EAEygB,sCAAqCD,EAAY,iBACpkB,0EA9BgBL,kBAAAA,qCAAAA,8HCHHY,aAAa,CAAA,kBAAbA,GAiIAC,kBAAkB,CAAA,kBAAlBA,uEAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CC,KAAM,eACNC,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,qBAAqB,EACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAepH,EACfqH,eAAgB,EAAE,CAClBC,eAAWtH,EACXuH,aAAa,CACf,6IC+GgBC,cAAAA,qCAAAA,OAjQS,CAAA,CAAA,IAAA,YACO,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,IAkF7BE,EAAiC,CACrC,eACA,OACA,OACA,kBACA1H,EACD,CA4BD,SAAS2H,EACPC,CAAoC,EAEpC,OAAQA,KAAkC5H,MAAZ6H,OAAO,AACvC,CAuBA,SAASM,EAAOC,CAAU,SACxB,AAAI,KAAa,IAANA,EACFA,EAEL,AAAa,KAHa,KAGH,OAAhBA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAASf,EACd,CAyBa,CACbyC,CAKC,MAkBmBpF,IAjDpB,IA0CI+E,EAqEA/D,EACAC,EAhHJ,KACE8B,CAAG,OACHgB,CAAK,aACLrB,GAAc,CAAK,UACnB2C,GAAW,CAAK,SAChBC,CAAO,WACPC,CAAS,SACTP,CAAO,CACPlB,OAAK,QACL0B,CAAM,MACNC,GAAO,CAAK,OACZC,CAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,CACrB5E,aAAW,eACX6E,CAAa,UACbC,EAAW,OAAO,QAClBC,CAAM,WACN9E,CAAS,gBACT+E,CAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGC,EACQ,CAzBb,EAyCM,SAAEC,CAAO,aAAEC,CAAW,cAAEC,CAAY,eAAEC,CAAa,CAAE,CAAGrB,EAE1DpF,EAAIsG,GAAW5E,EAAAA,kBAAkB,CACrC,GAAI,aAAc1B,EAChB+E,CADmB,CACV/E,MACJ,CACL,IAAMgE,EAAW,IAAIhE,EAAE2B,WAAW,IAAK3B,EAAE4B,UAAU,CAAC,CAAC8E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEjF,EAAc3B,EAAE2B,WAAW,CAAC+E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CnE,EAAAA,AAAuB,OAAXzC,EAAAA,EAAEyC,SAAS,AAATA,EAAS,KAAA,EAAXzC,EAAa0G,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD7B,EAAS,CAAE,GAAG/E,CAAC,UAAEgE,cAAUrC,YAAac,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBgE,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,AAAI7M,MACR,yIADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,IAAIkI,EAAgCuE,EAAKvE,MAAM,EAAI2E,CAGnD,QAAOJ,EAAKvE,MAAM,CAClB,OAAQuE,EAAapB,MAAM,CAI3B,IAAM4B,EAAkB,uBAAwB/E,EAEhD,GAAI+E,GACF,GAAsB,UAAU,CAA5B9B,AADe,EACRjD,MAAM,CACf,MAAM,OAAA,cAGL,CAHK,AAAIlI,MACP,mBAAkBmJ,MAAI,gCACpB,kEAFC,oBAAA,OAAA,mBAAA,gBAAA,CAGN,EACF,KACK,CAIL,IAAM+D,EAAoBhF,EAC1BA,EAAS,AAACiF,IACR,GAAM,CAAEhC,OAAQ1J,CAAC,CAAE,GAAG2L,EAAM,CAAGD,EAC/B,OAAOD,EAAkBE,EAC3B,CACF,CAEA,GAAIf,EAAQ,CACK,QAAQ,CAAnBA,IACFR,GAAO,CAAA,EAUT,IAAM6B,EAAcL,AARsD,CACxEC,UAAW,CAAEC,SAAU,OAAQ3B,OAAQ,MAAO,EAC9C4B,WAAY,CAAEtD,MAAO,OAAQ0B,OAAQ,MAAO,CAC9C,CAKiC,CAACS,EAAO,CACrCqB,IACF5B,EAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQ4B,CAAW,CAAC,EAErC,IAAMC,EARoD,AAQtCF,CAPlBD,WAAY,QACZ3B,KAAM,OACR,CAKiC,CAACQ,EAAO,AACrCsB,IAAe,CAACxD,IAClBA,EAAQwD,CADiB,AACjBA,CAEZ,CAEA,IAAIC,EAAY,GACZ1G,EAAWwC,EAAOQ,GAClB/C,EAAYuC,EAAOkC,GAGvB,GA/OE,CAAC,AAFmBzC,AAiPlBG,CA/OAH,CA+OeA,CAjP6B,GAG/B,UAAf,EACCD,KADMC,IACND,EAAgBC,QACfE,CARoC9H,IAQlB4H,AARdA,EAAwBA,GAAG,AAQbA,CAAmB,CA4OhB,CACvB,IAAM0E,EAAkB3E,EAAgBC,GAAOA,EAAIC,OAAO,CAAGD,EAE7D,GAAI,CAAC0E,EAAgB1E,GAAG,CACtB,CADwB,KAClB,OAAA,cAIL,CAJK,AAAInJ,MACP,8IAA6I8N,KAAKC,SAAS,CAC1JF,IAFE,oBAAA,OAAA,mBAAA,gBAAA,CAIN,GAEF,GAAI,CAACA,EAAgBjC,MAAM,EAAI,CAACiC,EAAgB3D,KAAK,CACnD,CADqD,KAC/C,OAAA,cAIL,CAJK,AAAIlK,MACP,2JAA0J8N,KAAKC,SAAS,CACvKF,IAFE,oBAAA,OAAA,kBAAA,gBAAA,CAIN,GAQF,GALAzG,EAAYyG,EAAgBzG,SAAS,CACrCC,EAAawG,EAAgBxG,UAAU,CACvCC,EAAcA,GAAeuG,EAAgBvG,WAAW,CACxDsG,EAAYC,EAAgB1E,GAAG,CAE3B,CAAC0C,EACH,GAAI,AAAC3E,CADI,EACSC,GAGX,GAAID,GAAY,AAHN,CAGOC,CAHK,CAGM,CACjC,IAAM6G,EAAQ9G,EAAW2G,EAAgB3D,KAAK,CAC9C/C,EAAYuD,KAAKuD,KAAK,CAACJ,EAAgBjC,MAAM,CAAGoC,EAClD,MAAO,GAAI,CAAC9G,GAAYC,EAAW,CACjC,IAAM6G,EAAQ7G,EAAY0G,EAAgBjC,MAAM,CAChD1E,EAAWwD,KAAKuD,KAAK,CAACJ,EAAgB3D,KAAK,CAAG8D,EAChD,OARE9G,EAAW2G,EAAgB3D,KAAK,CAChC/C,EAAY0G,EAAgBjC,MASlC,AATwC,CAYxC,IAAIsC,EACF,CAACzC,IAAyB,QAAZC,CAAAA,GAAsB,KAAmB,IAAZA,CAAY,CAAU,EAC/D,CAACvC,CAJLA,EAAqB,UAAf,OAAOA,EAAmBA,EAAMyE,CAAAA,GAI1BzE,EAAI3C,UAAU,CAAC,UAAY2C,EAAI3C,UAAU,CAAC,QAAA,GAAU,CAE9DsC,GAAc,EACdoF,GAAS,GAEP/C,EAAOrC,WAAW,EAAE,CACtBA,GAAc,CAAA,EAGdmE,GACA,CAAC9B,EAAO3C,mBAAmB,EAC3BW,EAAIgF,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,AAGAtF,IAAc,CAAA,EAGhB,IAAMuF,EAAa3E,EAAO0B,GAyMpBkD,EAAWC,OAAOC,MAAM,CAC5B3C,EACI,CACE4C,SAAU,WACV7C,OAAQ,OACR1B,MAAO,OACPwE,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACRtH,iBACA+E,CACF,EACA,CAAC,EACLK,EAAc,CAAC,EAAI,CAAEmC,MAAO,aAAc,EAC1ChD,GAGIiD,EACJ,AAACnC,GAAgC,UAAhBV,EAWb,KAVAA,AAAgB,WACb,yCAAwCjF,CAAAA,EAAAA,EAAAA,eAAe,AAAfA,EAAgB,UACvDC,YACAC,YACAC,aACAC,EACAC,YAAaA,GAAe,GAC5BC,UAAW+G,EAAS/G,SAAS,AAC/B,GAAG,KACF,QAAO2E,EAAY,KAAI,AAG1B8C,EAAiB,AAAC/F,EAA+BgG,QAAQ,CAC7DX,EAAS/G,QAJ4C,CAInC,EAGO,SAAvB+G,EAAS/G,SAAS,CAChB,YAAY,AACZ,QAHF+G,EAAS/G,SAAS,CAKlB2H,EAAqCH,EACrC,gBACEC,EACAG,CANuD,kBAMnCb,EAAShC,cAAc,EAAI,UAC/C8C,iBAAkB,YAClBL,iBACF,EACA,CAAC,EAeCM,EA3dR,AA2dwBnE,SA3dfA,AAAiB,CAQR,EARQ,GAAA,QACxBC,CAAM,KACNhC,CAAG,aACHL,CAAW,OACXoB,CAAK,SACLkB,CAAO,OACPjB,CAAK,CACLjC,QAAM,CACU,CARQ,EASxB,GAAIY,EACF,MAAO,CAAEK,IADM,EACDkC,YAAQ9J,EAAW4I,WAAO5I,CAAU,EAGpD,GAAM,QAAEqJ,CAAM,CAAEE,MAAI,CAAE,CAxExB,AAwE2Bb,SAxElBA,AACP,CAAsC,CACtCC,CAAyB,CACzBC,CAAyB,EAFzB,GAAA,CAAEpC,aAAW,UAAEqC,CAAQ,CAAe,CAAtC,EAIA,GAAID,EAAO,CAET,IAAME,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAACL,IAC5CG,EAAatG,EADwCuG,EACpC,CAACP,GAD0C,MACjCO,CAAK,CAAC,EAAE,GAErC,GAAID,EAAa3E,MAAM,CAAE,CACvB,IAAM8E,EAA4C,IAA5BC,KAAKC,GAAG,IAAIL,GAClC,MAAO,CACLM,OAAQR,EAASzH,MAAM,CAAC,AAACkI,GAAMA,GAAK9C,CAAW,CAAC,EAAE,CAAG0C,GACrDK,KAAM,GACR,CACF,CACA,MAAO,CAAEF,OAAQR,EAAUU,KAAM,GAAI,CACvC,OACA,AAAqB,UAAjB,AAA2B,OAApBZ,EACF,CAAEU,OAAQ7C,EAAa+C,KAAM,GAAI,EAkBnC,CAAEF,OAfM,IACV,IAAI9F,IACL,AACA,AAOA,CAACoF,EAAe,EAARA,AAAU,EAAgB,CAAC/D,GAAG,CACpC,AAAC4E,GAAMX,EAASY,CADa,GACT,CAAC,AAACC,GAAMA,GAAKF,IAAMX,CAAQ,CAACA,EAASzE,MAAM,CAAG,EAAE,GAGzE,CACgBmF,KAAM,GAAI,CAC7B,EA+BqCK,EAAQjB,EAAOC,GAC5CmB,EA7CmE,AA6C5DV,EA9C8D,AA8CvDjF,MAAM,CAAG,EAE7B,MAAO,CACLwE,MAAQA,AAAD,GAAmB,MAATW,EAAyBX,EAAV,QAChCkB,OAAQT,EACLzE,GAAG,CACF,CAAC4E,EAAGtF,IACCyC,EAAO,QAAEiD,MAAQhC,UAAKiC,EAASlB,MAAOa,CAAE,GAAG,KACnC,CAATD,KAAAA,EAAeC,EAAItF,GAAI,CAAA,CACtBqF,GAENS,IAAI,CAAC,MAQRpC,IAAKjB,EAAO,QAAEiD,MAAQhC,UAAKiC,EAASlB,MAAOU,CAAM,CAACU,EAAM,AAAD,EACzD,CACF,EAwbyC,QACrCH,MACAhC,cACAL,EACAoB,MAAOhD,EACPkE,QAASiD,QACTlE,SACAjC,CACF,GA4BA,MAAO,CAAEjG,MAde,CACtB,GAAGwK,CAAI,CACPf,QAASwC,EAAS,OAASxC,gBAC3BS,EACAjC,MAAOhD,EACP0E,OAAQzE,WACRiF,YACAT,EACAG,MAAO,CAAE,GAAGwC,CAAQ,CAAE,GAAGY,CAAgB,AAAC,EAC1C/E,MAAOkF,EAAclF,KAAK,CAC1BkB,OAAQgE,EAAchE,MAAM,CAC5BlC,IAAK4C,GAAesD,EAAclG,GAAG,AACvC,EAEgBrF,KADH,aAAEgF,WAAa2C,cAAUS,OAAaL,CAAK,CACnC,CACvB,mECztBA3I,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACiM,kBAAkB,gECFpBpM,GAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACkM,aAAa,kECEf,SAAS1C,EAAc,CAKM,MA8EzB1B,EAnFmB,GAAA,QACrBA,CAAM,KACNhC,CAAG,OACHe,CAAK,SACLkB,CAAO,CACoB,CALN,EAiFfqE,EACJrE,IAAAA,AACgB,OADhBA,AACAD,EAAAA,EAAOtC,SAAAA,AAAS,EAAA,KAAA,EAAhBsC,EAAkB3G,MAAM,CAAC,CAACkL,EAAMC,IAC9BjF,KAAKkF,GAAG,CAACD,MAAMH,AAAa9E,KAAKkF,GAAG,CAACF,MAAoBC,CAAbH,CAAmBE,EAAAA,CAAAA,EAtFnD,GAwFdF,AAEF,OAAUrE,EAAOlD,IAAI,CAAC,QAAO4H,mBAAmB1G,GAAK,MAAKe,EAAM,MAAKuF,GACnEtG,CAAAA,CAAI3C,UAAU,CAAC,wBAEX,EAAC,CAFqClG,AAI9C,QAJsDC,GAAG,CAACuP,kBAAkB,GACpE,AAAC,UAAOxP,QAAQC,GAAG,CAACuP,kBAAkB,CAS9C,UAAA,qCAAA,KAFAjD,EAAckD,kBAAkB,CAAG,OAEnC,EAAelD,6ICmQFmD,QAAAA,qCAAAA,2DA/VN,CAAA,CAAA,IAAA,aACc,CAAA,CAAA,IAAA,aACJ,CAAA,CAAA,IAAA,SACW,CAAA,CAAA,IAAA,QAYO,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,MACV,CAAA,CAAA,IAAA,YACK,CAAA,CAAA,IAAA,YAGJ,CAAA,CAAA,IAAA,SACG,CAAA,CAAA,IAAA,IAGvBC,EAAyC,KAAA,KAA7B3P,AAA6B,CAAA,OAArBC,GAAG,CAAC2P,iBAAiB,iKAyB/C,SAASG,EACPC,CAA2B,CAC3BpE,CAA6B,CAC7BqE,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrC3H,CAAoB,CACpB4H,CAA8B,EAE9B,IAAMvH,EAAMmH,MAAAA,EAAAA,KAAAA,EAAAA,EAAKnH,GAAG,CACfmH,GAAOA,CAAG,CAAC,kBAAkB,GAAKnH,IAGvCmH,CAAG,AAHyC,CAGxC,kBAAkB,CAAGnH,EAEzB8B,CADU,WAAYqF,EAAMA,EAAIK,MAAM,GAAK9P,QAAQC,OAAO,EAAA,EACxD8P,KAAK,CAAC,KAAO,GAAGjR,IAAI,CAAC,KACrB,GAAI,AAAC2Q,EAAIO,aAAa,EAAKP,EAAD,AAAKQ,WAAW,EAAE,AAW5C,GAHoB,SAAS,CAAzB5E,GACFuE,GAAgB,GAEdF,MAAAA,EAAAA,KAAAA,EAAAA,EAAWQ,OAAO,CAAE,CAItB,IAAMC,EAAQ,IAAIC,MAAM,QACxB1C,OAAO2C,cAAc,CAACF,EAAO,SAAU,CAAEG,SAAU,GAAOC,MAAOd,CAAI,GACrE,IAAIe,GAAY,EACZC,GAAU,EACdf,EAAUQ,OAAO,CAAC,CAChB,GAAGC,CAAK,CACRO,YAAaP,EACbQ,cAAelB,EACfmB,OAAQnB,EACRoB,mBAAoB,IAAML,EAC1BM,qBAAsB,IAAML,EAC5BM,QAAS,KAAO,EAChBC,eAAgB,KACdR,GAAY,EACZL,EAAMa,cAAc,EACtB,EACAC,gBAAiB,KACfR,GAAU,EACVN,EAAMc,eAAe,EACvB,CACF,EACF,EACItB,MAAAA,EAAAA,KAAAA,EAAAA,EAAsBO,OAAAA,AAAO,EAAE,CACjCP,EAAqBO,OAAO,CAACT,GAkDjC,GACF,CAEA,SAASyB,EACP5F,CAAsB,SAEtB,AAAY1L,EAAAA,EAARmC,CAAW,CAIN,EAJS,aAIPuJ,CAAc,EAIlB,CAAE6F,cAAe7F,CAAc,CACxC,CA9IsB,aAAlB,AAA+B,OAAxBvK,SACPuO,WAAmBC,qBAAqB,EAAG,CAAA,EA+I/C,IAAM6B,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,AAAU,EAC7B,CAAA,EAwBEC,IAzBEF,IAEF,KACE9I,CAAG,QACHkC,CAAM,OACNlB,CAAK,QACLyB,CAAM,OACN1B,CAAK,UACLkC,CAAQ,WACRT,CAAS,OACTG,CAAK,eACLK,CAAa,aACbD,CAAW,CACXR,SAAO,aACP5C,CAAW,MACX+C,CAAI,WACJ0E,CAAS,sBACTC,CAAoB,iBACpBC,CAAe,gBACf2B,CAAc,YACd1B,CAAU,CACV1E,QAAM,CACNqG,SAAO,CACP,GAAG5F,EACJ,CAAA,EAGK6F,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EACxB,AAACjC,IACMA,IAGD+B,CAHM,GAQR/B,EAAInH,GALO,AAKJ,CAAGmH,EAAInH,GAAAA,AAAG,EAYfmH,EAAIkC,QAAQ,EAAE,AAChBnC,EACEC,EACApE,EACAqE,EACAC,EACAC,EACA3H,EACA4H,GAGN,EACA,CACEvH,EACA+C,EACAqE,EACAC,EACAC,EACA4B,EACAvJ,EACA4H,EACD,EAGG+B,EAAMC,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACP,EAAcG,GAEvC,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAAChC,EADH,IACGA,CACE,GAAG7D,CAAI,CACP,GAAGsF,EAAgB5F,EAAc,CAIlCT,QAASA,EACTxB,MAAOA,EACP0B,OAAQA,EACRQ,SAAUA,EACVuG,YAAW9G,EAAO,OAAS,IAC3BF,UAAWA,EACXG,MAAOA,EAOP3B,MAAOA,EACPkB,OAAQA,EACRlC,IAAKA,EACLsJ,IAAKA,EACLzG,OAAQ,AAACgF,IAEPX,EADYW,EAAMQ,UAEhBlB,GAF6B,CAG7BpE,EACAqE,EACAC,EACAC,EACA3H,EACA4H,EAEJ,EACA2B,QAAUrB,AAAD,IAEPoB,GAAe,GACK,SAAS,CAAzBlG,GAEFuE,EAAgB,IAEd4B,GACFA,EAAQrB,EAEZ,EAHe,CAMrB,GAGF,SAAS4B,EAAa,CAMrB,EANqB,GAAA,aACpBC,CAAW,eACXxD,CAAa,CAId,CANqB,EAOdjC,EAAO,CACX0F,GAAI,QACJC,YAAa1D,EAAchE,MAAM,CACjCrD,WAAYqH,EAAclF,KAAK,CAC/B6I,YAAa3D,EAAc2D,WAAW,CACtCC,eAAgB5D,EAAc4D,cAAc,CAC5C,GAAGlB,EAAgB1C,EAAclD,aAAa,CAAC,AACjD,SAEA,AAAI0G,GAAeK,EAAAA,OAAQ,CAACC,OAAO,EAEjCD,AAFmC,EAEnCA,OAAQ,CAACC,OAAO,CACd9D,EAAclG,GAAG,CAEjBiE,GAEK,MAIP,CAAA,EAAA,EAAA,GAAA,EAACzG,EAAAA,OAAI,CAAA,UACH,CAAA,EAAA,EAAA,GAAA,EAACyM,EAAD,KAACA,CAOCC,IAAI,CAfwD,SAqB5DC,KAAMjE,EAAchE,MAAM,CAAG9J,OAAY8N,EAAclG,GAAG,CACzD,GAAGiE,CAAI,EAZN,UACAiC,EAAclG,GAAG,CACjBkG,EAAchE,MAAM,CACpBgE,EAAclF,KAAK,GAa7B,CAOO,IAAM6F,EAAQkC,CAAAA,EAAAA,EAAAA,CAARlC,SAAQkC,AAAU,EAC7B,AADWlC,CACV/N,EAAOkQ,KACN,IAAMoB,EAAc1M,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC0I,EAAAA,aAAa,EAItCiE,EAAgB3M,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACyI,EAAAA,kBAAkB,EAC7CnE,EAASsI,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,SAIHrN,EAHlB,IAAMA,EAAI6J,GAAauD,GAAiB1L,EAAAA,kBAAkB,CACpDsC,EAAW,IAAIhE,EAAE2B,WAAW,IAAK3B,EAAE4B,UAAU,CAAC,CAAC8E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEjF,EAAc3B,EAAE2B,WAAW,CAAC+E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CnE,EAAAA,AAAuB,OAAXzC,EAAAA,EAAEyC,SAAAA,AAAS,EAAA,KAAA,EAAXzC,EAAa0G,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD,MAAO,CAAE,GAAG5G,CAAC,UAAEgE,cAAUrC,YAAac,CAAU,CAClD,EAAG,CAAC2K,EAAc,EAEZ,QAAExH,CAAM,mBAAEC,CAAiB,CAAE,CAAGhK,EAChCsO,EAAYmD,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAC1H,GAEzBhK,GAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRuO,EAAUQ,OAAO,CAAG/E,CACtB,EAAG,CAACA,EAAO,EAEX,IAAMwE,EAAuBkD,GAAAA,EAAAA,MAAAA,AAAM,EAACzH,GAEpCjK,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRwO,EAAqBO,OAAO,CAAG9E,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACW,EAAc6D,EAAgB,CAAGpQ,CAAAA,EAAAA,EAAAA,QAAAA,AAAQ,GAAC,GAC3C,CAACsM,EAAayF,EAAe,CAAG/R,CAAAA,EAAAA,EAAAA,QAAAA,AAAQ,GAAC,GAEzC,CAAE4B,MAAOoN,CAAa,CAAEvL,KAAM6P,CAAO,CAAE,CAAG5K,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC9G,EAAO,CACjE4K,cAAAA,EAAAA,OAAa,CACbH,QAASvB,eACTyB,EACAD,aACF,GAEA,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAAA,CADF,CACE,QAAA,CAAA,WAEI,CAAA,EAAA,EAAA,GAAA,EAACsF,EAAAA,CACE,GAAG5C,CAAa,CACjBvG,YAAa6K,EAAQ7K,WAAW,CAChCoD,YAAayH,EAAQzH,WAAW,CAChCL,KAAM8H,EAAQ9H,IAAI,CAClB0E,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjB2B,eAAgBA,EAChB1B,WAAYzO,EAAMkI,KAAK,CACvBsI,IAAKN,IAGRwB,EAAQlI,QAAQ,CACf,CAAA,CADe,CACf,EAAA,GAAA,EAACmH,EAAAA,CADc,AAEbC,YApDY,CAACU,AAoDAV,EACbxD,cAAeA,IAEf,OAGV", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}