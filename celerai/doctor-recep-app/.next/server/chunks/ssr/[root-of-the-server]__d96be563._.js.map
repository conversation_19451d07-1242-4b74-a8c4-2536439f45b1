{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client-edge.ts", "turbopack:///[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs", "turbopack:///[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/head-manager-context.ts", "turbopack:///[project]/src/lib/analytics.ts", "turbopack:///[project]/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].ReactDOM\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AppRouterContext\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HooksClientContext\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedHtml\n", "if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = require('../../module.compiled').vendored['react-ssr'].React\n", "module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxRuntime\n", "module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactServerDOMTurbopackClientEdge\n", "\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n", "\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HeadManagerContext\n", "'use client'\n\n/**\n * Analytics Abstraction Layer - Celer AI\n * \n * SECURITY FRAMEWORK:\n * - Two-Zone Analytics: Public (full tracking) vs Authenticated (PII-free only)\n * - Zero PII Policy: Never send user_id, email, patient_name, or PHI to GA4\n * - Allowlist Approach: Only predefined safe events pass through\n * \n * CTO APPROVED EVENTS FOR AUTHENTICATED ZONE:\n * - consultation_generated: Track consultation creation (no patient data)\n * - summary_approved: Track summary approval (no content)\n * - quota_warning: Track quota threshold alerts (no user data)\n * - dashboard_viewed: Track dashboard access (no personal data)\n */\n\n// Type definitions for safe analytics events\ntype PublicPageEvent = \n  | 'page_view'\n  | 'signup_started'\n  | 'signup_completed'\n  | 'login_attempted'\n  | 'login_successful'\n  | 'blog_post_viewed'\n  | 'guide_viewed'\n\ntype AuthenticatedAppEvent = \n  | 'consultation_generated'\n  | 'summary_approved'\n  | 'quota_warning'\n  | 'dashboard_viewed'\n  | 'settings_accessed'\n  | 'template_updated'\n\ntype AnalyticsEvent = PublicPageEvent | AuthenticatedAppEvent\n\n// Safe event parameters (no PII allowed)\ninterface SafeEventParams {\n  // Only non-PII metadata allowed\n  page_title?: string\n  consultation_type?: string\n  quota_percentage?: number\n  feature_used?: string\n  error_type?: string\n  [key: string]: string | number | boolean | undefined\n}\n\n// Check if user is in authenticated app\nfunction isAuthenticatedRoute(): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const authenticatedPaths = ['/dashboard', '/info', '/settings', '/templates', '/admin']\n  return authenticatedPaths.some(path => window.location.pathname.startsWith(path))\n}\n\n// Allowlist of safe events for authenticated zone\nconst SAFE_AUTHENTICATED_EVENTS: AuthenticatedAppEvent[] = [\n  'consultation_generated',\n  'summary_approved', \n  'quota_warning',\n  'dashboard_viewed',\n  'settings_accessed',\n  'template_updated'\n]\n\n// Scrub any potentially sensitive parameters\nfunction scrubParameters(params: SafeEventParams): SafeEventParams {\n  const scrubbed: SafeEventParams = {}\n  \n  // Only allow specific safe parameters\n  const allowedParams = [\n    'page_title',\n    'consultation_type', \n    'quota_percentage',\n    'feature_used',\n    'error_type'\n  ]\n  \n  for (const [key, value] of Object.entries(params)) {\n    if (allowedParams.includes(key) && value !== undefined) {\n      // Additional validation for specific params\n      if (key === 'quota_percentage' && typeof value === 'number') {\n        scrubbed[key] = Math.round(value) // Round to avoid precision-based identification\n      } else if (typeof value === 'string' && value.length < 100) {\n        scrubbed[key] = value\n      } else if (typeof value === 'boolean') {\n        scrubbed[key] = value\n      }\n    }\n  }\n  \n  return scrubbed\n}\n\n/**\n * Main analytics tracking function\n * Enforces two-zone security model\n */\nexport function trackEvent(\n  event: AnalyticsEvent, \n  parameters: SafeEventParams = {}\n): void {\n  // Only track in browser environment\n  if (typeof window === 'undefined') return\n  \n  // Check if GA4 is loaded\n  if (typeof window.gtag !== 'function') {\n    console.warn('GA4 not loaded, skipping analytics event:', event)\n    return\n  }\n  \n  try {\n    const isAuthenticated = isAuthenticatedRoute()\n    \n    if (isAuthenticated) {\n      // AUTHENTICATED ZONE: Strict allowlist enforcement\n      if (!SAFE_AUTHENTICATED_EVENTS.includes(event as AuthenticatedAppEvent)) {\n        console.warn(`Analytics: Blocked unauthorized event in authenticated zone: ${event}`)\n        return\n      }\n      \n      // Scrub all parameters for safety\n      const scrubbedParams = scrubParameters(parameters)\n      \n      console.log(`Analytics: Tracking safe authenticated event: ${event}`, scrubbedParams)\n      window.gtag('event', event, scrubbedParams)\n      \n    } else {\n      // PUBLIC ZONE: Full tracking allowed (but still scrub for safety)\n      const scrubbedParams = scrubParameters(parameters)\n      \n      console.log(`Analytics: Tracking public event: ${event}`, scrubbedParams)\n      window.gtag('event', event, scrubbedParams)\n    }\n    \n  } catch (error) {\n    console.error('Analytics tracking error:', error)\n  }\n}\n\n/**\n * Track page views with automatic zone detection\n */\nexport function trackPageView(pageTitle?: string): void {\n  const isAuthenticated = isAuthenticatedRoute()\n  \n  if (isAuthenticated) {\n    // In authenticated zone, only track generic dashboard view\n    trackEvent('dashboard_viewed', { page_title: 'Dashboard' })\n  } else {\n    // In public zone, track full page view\n    trackEvent('page_view', { page_title: pageTitle })\n  }\n}\n\n/**\n * Track consultation events (authenticated zone only)\n */\nexport function trackConsultation(type: 'generated' | 'approved', consultationType?: string): void {\n  if (!isAuthenticatedRoute()) return\n  \n  if (type === 'generated') {\n    trackEvent('consultation_generated', { \n      consultation_type: consultationType \n    })\n  } else if (type === 'approved') {\n    trackEvent('summary_approved', { \n      consultation_type: consultationType \n    })\n  }\n}\n\n/**\n * Track quota warnings (authenticated zone only)\n */\nexport function trackQuotaWarning(percentage: number): void {\n  if (!isAuthenticatedRoute()) return\n  \n  trackEvent('quota_warning', { \n    quota_percentage: percentage \n  })\n}\n\n/**\n * Track authentication events (public zone only)\n */\nexport function trackAuth(type: 'signup_started' | 'signup_completed' | 'login_attempted' | 'login_successful'): void {\n  // Only track in public zone\n  if (isAuthenticatedRoute()) return\n\n  trackEvent(type)\n}\n\n/**\n * Initialize analytics with environment detection\n */\nexport function initializeAnalytics(): void {\n  if (typeof window === 'undefined') return\n\n  // Log analytics initialization\n  console.log('Analytics initialized with two-zone security model')\n\n  // Track initial page view\n  trackPageView(document.title)\n}\n\n// Global gtag function type declaration\ndeclare global {\n  interface Window {\n    gtag: (\n      command: 'config' | 'event' | 'js',\n      targetId: string | AnalyticsEvent,\n      config?: any\n    ) => void\n  }\n}\n", "module.exports = require('./dist/client/components/navigation')\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM", "AppRouterContext", "HooksClientContext", "ServerInsertedHtml", "process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "React", "ReactJsxRuntime", "ReactServerDOMTurbopackClientEdge", "HeadManagerContext"], "mappings": "2lCAAAA,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CAAC,YAAY,CAACC,QAAQ,iECAhFJ,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACE,gBAAgB,gECFlBL,GAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACG,kBAAkB,iECFpBN,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACI,kBAAkB,iECwBZP,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,oEC1BjCF,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CAAC,YAAY,CAACW,KAAK,iECA7Ed,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,YACD,CAACY,eAAe,gECFjBf,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,YACD,CAACa,iCAAiC,oDCFnC,aAEA,SAAS,EAAyB,CAAW,EACzC,GAAuB,YAAnB,OAAO,QAAwB,OAAO,KAE1C,IAAI,EAAoB,IAAI,QACxB,EAAmB,IAAI,QAE3B,MAAO,CAAC,EAA2B,SAAS,CAAW,EACnD,OAAO,EAAc,EAAmB,EAC5C,CAAC,CAAE,EACP,CA0BA,EAAQ,CAAC,CAzBT,EAyBY,OAzBH,AAA0B,CAAG,CAAE,CAAW,EAC/C,GAAI,CAAC,GAAe,GAAO,EAAI,UAAU,CAAE,OAAO,EAClD,GAAY,AAAR,UAA+B,UAAf,OAAO,GAAmC,YAAf,OAAO,EAAoB,MAAO,CAAE,QAAS,CAAI,EAEhG,IAAI,EAAQ,EAAyB,GAErC,GAAI,GAAS,EAAM,GAAG,CAAC,GAAM,OAAO,EAAM,GAAG,CAAC,GAE9C,IAAI,EAAS,CAAE,UAAW,IAAK,EAC3B,EAAwB,OAAO,cAAc,EAAI,OAAO,wBAAwB,CAEpF,IAAK,IAAI,KAAO,EACZ,EADiB,CACL,YAAR,GAAqB,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAK,GAAM,CACrE,IAAI,EAAO,EAAwB,OAAO,wBAAwB,CAAC,EAAK,GAAO,KAC3E,IAAS,EAAK,EAAN,CAAS,EAAI,EAAK,GAAA,AAAG,EAAG,OAAO,cAAc,CAAC,EAAQ,EAAK,GAClE,CAAM,CAAC,EAAI,CAAG,CAAG,CAAC,EAAI,AAC/B,CAOJ,OAJA,EAAO,OAAO,CAAG,EAEb,GAAO,EAAM,GAAG,CAAC,EAAK,GAEnB,CACX,qDCpCA,YAKA,GAAQ,CAAC,CAHT,EAGY,OAHH,AAAyB,CAAG,EACjC,OAAO,GAAO,EAAI,UAAU,CAAG,EAAM,CAAE,QAAS,CAAI,CACxD,kECJAhB,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,IAAyBC,QAAQ,CACxD,QACD,CAACc,kBAAkB,iDCiGb,SAAS,EACd,CAAqB,CACrB,EAA8B,CAAC,CAAC,EAsClC,CAKO,SAAS,EAAc,CAAkB,EAQ5C,EAAW,YAAa,CAAE,WAAY,CAAU,EAEpD,CAKO,SAAS,EAAkB,CAA8B,CAAE,CAAyB,EAY3F,CAKO,SAAS,EAAkB,CAAkB,EAMpD,CAKO,SAAS,EAAU,CAAoF,EAI5G,EAAW,EACb,CAKO,SAAS,IAQhB,0LC7MA,EAAO,OAAO,CAAA,EAAA,CAAA,CAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12]}