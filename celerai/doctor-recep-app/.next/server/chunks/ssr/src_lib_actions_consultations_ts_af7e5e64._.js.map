{"version": 3, "sources": ["turbopack:///[project]/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": "0iBAEA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAGA,SAAS,EAAqB,CAAc,EAC1C,GAAI,CAAC,EAAO,MAAO,EAAE,CACrB,GAAI,MAAM,OAAO,CAAC,GAAQ,OAAO,EACjC,GAAqB,UAAjB,AAA2B,OAApB,EACT,GAAI,CACF,OAAO,KAAK,KAAK,CAAC,EACpB,CAAE,KAAM,CAER,CAEF,MAAO,EAAE,AACX,CAGO,eAAe,EACpB,CAAe,CACf,CAAkB,CAClB,CAA4B,CAC5B,CAAsC,CACtC,CAAoB,CACpB,EAAyI,YAAY,CACrJ,CAAoB,EAEpB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAIjD,IAAM,EAAW,CAAC,KAAc,KAAe,EAAqB,CAC9D,EAAsB,CAAA,EAAA,EAAA,iBAAA,AAAgB,EAAE,GAC9C,GAAI,CAAC,EAAoB,KAAK,CAC5B,CAD8B,EADJ,GAEnB,CAAE,SAAS,EAAO,MAAO,EAAoB,KAAK,AAAE,EAI7D,IAAM,EAAiB,OAAO,UAAU,GAGlC,EAAoB,MAAM,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAW,EAAQ,MAAM,CAAE,EAAgB,SACtF,GADgC,AAC5B,CAAC,EAAkB,OAAO,CAC5B,CAD8B,KACvB,CAAE,SAAS,EAAO,MAAO,CAAC,qBAAqB,EAAE,EAAkB,KAAK,CAAA,CAAE,AAAC,EAIpF,IAAI,EAAgC,EAAE,CACtC,GAAI,EAAqB,MAAM,CAAG,EAAG,CACnC,IAAM,EAAwB,MAAM,CAAA,EAAA,EAAA,mBAAA,AAAkB,EAAE,EAAsB,EAAQ,MAAM,CAAE,EAAgB,GAA1E,MACpC,GAAI,CAAC,EAAsB,OAAO,CAChC,CADkC,KAC3B,CAAE,SAAS,EAAO,MAAO,CAAC,gCAAgC,EAAE,EAAsB,MAAM,EAAE,KAAK,MAAA,CAAO,AAAC,EAEhH,EAAsB,EAAsB,IAAI,EAAI,EAAE,AACxD,CAGA,IAAI,EAAsB,EAAE,CAC5B,GAAI,EAAW,MAAM,CAAG,EAAG,CACzB,IAAM,EAAoB,MAAM,CAAA,EAAA,EAAA,mBAAA,AAAkB,EAAE,EAAY,EAAQ,MAAM,CAAE,EAAgB,GAAhE,MAChC,GAAI,CAAC,EAAkB,OAAO,CAC5B,CAD8B,KACvB,CAAE,SAAS,EAAO,MAAO,CAAC,qBAAqB,EAAE,EAAkB,MAAM,EAAE,KAAK,MAAA,CAAO,AAAC,EAEjG,EAAY,EAAkB,IAAI,EAAI,EAAE,AAC1C,CAEA,IAAM,EAAgB,CAAA,EAAA,EAAA,sBAAA,AAAqB,EAAE,GAEvC,EAAW,MAAM,CAAA,CAFD,CAEC,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,OAAE,CAAK,CAAE,CAAG,IAHf,EAGqB,EACzC,IAAI,CAAC,iBACL,MAAM,CAAC,CACN,GAAI,EACJ,UAAW,EAAQ,MAAM,CACzB,kBAAmB,EAAkB,GAAG,CACxC,sBAAuB,EACvB,WAAY,EACZ,aAAc,EACd,OAAQ,UACR,sBAAuB,EACvB,aAAc,GAAe,KAC7B,kBAAmB,EACnB,aAAc,GAAe,IAC/B,GACC,MAAM,GACN,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,CAAE,SAAS,EAAO,MAAO,+BAAgC,EAMlE,MAHA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACf,CAAA,EAAA,EAAA,EADA,YACa,AAAb,EAAe,WAER,CAAE,SAAS,AAFlB,EAEwB,KAAM,CAA6B,CAC7D,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,wCAAyC,GAChD,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAqB,CAAsB,CAAE,CAAe,EAChF,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,EAEF,GAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,kBAAmB,EACnB,OAAQ,WACV,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAAE,AAEnC,GAAI,EAEF,OADA,IADe,IACP,KAAK,CAAC,gBAAiB,GACxB,CAAE,QAJ8E,CAIrE,EAAO,MAAO,kCAAmC,EAKrE,MAFA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cAER,CAAE,MAFT,GAEkB,EAAM,KAAM,CAAQ,CACxC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAmB,CAAkB,EACzD,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAY,AAAZ,IACtB,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,GAEF,EAAO,MAAO,cAAe,EAIjD,IAAM,EAAkB,EAAS,GAAG,CAAC,qBAC/B,EAA4B,EAAS,GAAG,CAAC,yBACzC,EAAkB,EAAS,GAAG,CAAC,cAC/B,EAAsB,EAAS,GAAG,CAAC,yBAErC,EAAgC,EAAE,CAClC,EAAsB,EAAE,CACxB,EAAgB,EAGpB,GAAI,EACF,GAAI,CACF,EAAsB,KAAK,KAAK,CAAC,EACnC,CAAE,KAH2B,CAGpB,EAAO,CACd,QAAQ,KAAK,CAAC,uCAAwC,EACxD,CAIF,GAAI,EACF,GAAI,CACF,EAAY,KAAK,IAFA,CAEK,CAAC,EACzB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,EAC7C,CAIE,IACF,EAAgB,SAAS,EAAqB,IADvB,CAC8B,GAIvD,IAAM,EAAkB,EAAA,wBAAwB,CAAC,SAAS,CAAC,CACzD,CADsB,iBACH,EACnB,sBAAuB,EACvB,WAAY,EACZ,aAAc,EAAS,GAAG,CAAC,gBAC3B,sBAAuB,CACzB,GAEA,GAAI,CAAC,EAAgB,OAAO,CAC1B,CAD4B,KACrB,CACL,SAAS,EACT,MAAO,sBAAwB,KAAK,SAAS,CAAC,EAAgB,KAAK,CAAC,OAAO,GAAG,WAAW,CAC3F,EAGF,GAAM,mBAAE,CAAiB,CAAE,uBAAqB,YAAE,CAAU,cAAE,CAAY,CAAE,uBAAqB,CAAE,CAAG,EAAgB,IAAI,CAEpH,EAAW,MAAM,CAAA,EAAA,EAAA,YAAW,AAAX,IAGjB,CAAE,KAAM,CAAY,OAAE,CAAK,CAAE,CAAG,IAHf,EAGqB,EACzC,IAAI,CAAC,iBACL,MAAM,CAAC,CACN,UAAW,EAAQ,MAAM,mBACzB,EACA,sBAAuB,GAAyB,EAAE,CAClD,WAAY,GAAc,EAAE,cAC5B,EACA,OAAQ,UACR,sBAAuB,GAAyB,CAClD,GACC,MAAM,GACN,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,CAAE,SAAS,EAAO,MAAO,+BAAgC,EAMlE,MAHA,GAAA,EAAA,cAAA,AAAa,EAAE,cACf,CAAA,EAAA,EAAA,EADA,YACa,AAAb,EAAe,WAER,CAAE,SAAS,AAFlB,EAEwB,KAAM,CAA6B,CAC7D,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,CAAE,QAAS,GAAO,MAAO,8BAA+B,CACjE,CACF,CAGO,eAAe,EAAiB,MACrC,EAAO,CAAC,UACR,EAAW,EAAE,QACb,CAAM,YACN,CAAU,CAMX,CAAG,CAAC,CAAC,EAKJ,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAC5B,EAAO,CAAC,GAAO,CAAC,CAAI,EACpB,EAAK,EAAO,EAAW,EAEzB,EAAQ,CAJW,CAKpB,IAAI,CAAC,iBACL,MAAM,CAAC,kRAAmR,CAAE,MAAO,OAAQ,GAC3S,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,KAAK,CAAC,aAAc,CAAE,WAAW,CAAM,GACvC,KAAK,CAAC,EAAM,GAQf,GALI,IACF,EAAQ,EADE,AACI,EAAE,CAAC,SAAU,EAAA,EAIzB,GAAc,EAAW,IAAI,GAAI,CAEnC,IAAM,EAAW,EAAW,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,OACrD,EAAQ,EAAM,UAAU,CAAC,MAAO,EAClC,CAEA,GAAM,CAAE,KAAM,CAAa,OAAE,CAAK,OAAE,CAAK,CAAE,CAAG,MAAM,EAEpD,GAAI,EAAO,CAIT,GAHA,QAAQ,KAAK,CAAC,kBAAmB,GAGd,YAAY,CAA3B,EAAM,IAAI,CACZ,MAAO,CACL,SAAS,EACT,KAAM,CACJ,cAAe,EAAE,CACjB,SAAS,EACT,WAAY,GAAS,CACvB,CACF,EAGF,MAAO,CAAE,SAAS,EAAO,MAAO,+BAAgC,CAClE,CAGA,IAAM,EAAqB,CAAC,GAAiB,EAAA,AAAE,EAAE,GAAG,CAAC,AAAC,IAC7C,CACL,GAAI,EAAI,EAAE,CACV,UAAW,EAAI,SAAS,CACxB,aAAc,EAAI,YAAY,CAC9B,kBAAmB,EAAI,iBAAiB,CACxC,sBAAuB,EAAqB,EAAI,qBAAqB,EACrE,WAAY,EAAqB,EAAI,UAAU,EAC/C,kBAAmB,EAAI,iBAAiB,OAAI,EAC5C,YAAa,EAAI,WAAW,OAAI,EAChC,OAAQ,EAAI,MAAM,CAClB,eAAgB,EAAI,cAAc,EAAI,OACtC,aAAc,EAAI,YAAY,OAAI,EAClC,sBAAuB,EAAI,qBAAqB,EAAI,EACpD,qBAAsB,EAAI,oBAAoB,CAC9C,WAAY,EAAI,UAAU,CAC1B,WAAY,EAAI,UAAU,CAC1B,kBAAmB,EAAI,iBAAiB,EAAI,aAC5C,aAAc,EAAI,YAAY,OAAI,EAClC,iBAAkB,EAAI,gBAAgB,OAAI,EAC5C,GAGI,EAAU,CAAC,IAAS,CAAC,CAAI,EAAK,EAEpC,MAAO,CACL,SAAS,EACT,KAAM,CACJ,cAAe,UACf,EACA,WAAY,GAAS,CACvB,CACF,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,2BAA4B,GACnC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAIO,eAAe,EACpB,CAAsB,CACtB,CAA2B,CAC3B,CAAiC,CACjC,CAAsC,CACtC,CAAiC,CACjC,CAAyB,CACzB,CAAoB,CACpB,CAAwB,EAExB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EAEH,OAFY,AACZ,IAAU,EAFU,cAGb,CAAE,SAAS,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,MAAO,CAAiB,CAAE,CAAG,IAHlC,EAGwC,EAC5D,IAAI,CAAC,iBACL,MAAM,CAAC,mOACP,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,MAAM,GAET,GAAI,GAAqB,CAAC,EAExB,OADA,IAAU,CAD4B,yBAE/B,CAAE,SAAS,EAAO,MAAO,wBAAyB,EAQ3D,IAAM,EAAsB,EAHF,AAGuB,EAAkB,qBAAqB,EAElF,EAAe,IADH,EAAqB,EAAkB,UAAU,KAC3B,GAAoB,EAAE,CAAE,CAG1D,EAAW,MAAM,MAAM,oDAAkE,CAC7F,OAAQ,CADsD,MAE9D,QAAS,CACP,aAHwF,CAAC,CAGzE,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,kBAAmB,EAAkB,iBAAiB,CACtD,sBAAuB,MAAM,OAAO,CAAC,GAAuB,EAAsB,EAAE,CACpF,WAAY,MAAM,OAAO,CAAC,GAAgB,EAAe,EAAE,CAC3D,aAAc,EAAkB,YAAY,EAAI,SAChD,kBAAmB,GAAoB,EAAkB,iBAAiB,EAAI,aAC9E,aAAc,GAAe,EAAkB,YAAY,EAAI,OAC/D,iBAAkB,QAAmB,EACrC,aAAc,EAAkB,YAAY,EAAI,OAChD,YAAa,EAAkB,OAAO,CAAC,IAAI,OAAI,EAC/C,WAAY,EAAkB,UAAU,EAAI,MAC9C,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAW,CAAC,KAAK,EAAE,EAAS,MAAM,CAAC,EAAE,EAAE,EAAS,UAAU,CAAA,CAAE,CAElE,OADA,IAAU,GACH,CAAE,SAAS,EAAO,MAAO,CAAS,CAC3C,CAEA,IAAM,EAAS,EAAS,IAAI,EAAE,YAC9B,GAAI,CAAC,EAEH,MAFW,CACX,IAAU,uBACH,CAAE,SAAS,EAAO,MAAO,qBAAsB,EAGxD,IAAM,EAAU,IAAI,YAChB,EAAc,GAElB,GAAI,CACF,MAAO,CAAM,CACX,GAAM,MAAE,CAAI,CAAE,OAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,EAAM,MAKV,IAAK,IAAM,KAHG,AACA,EADQ,CAGH,KAHS,CAAC,AAGH,EAHU,CAAE,QAAQ,CAAK,GAC/B,KAAK,CAAC,MAGxB,GAAI,EAAK,UAAU,CAAC,UAClB,CAD6B,EACzB,CACF,IAAM,EAAO,KAAK,KAAK,CAAC,EAAK,KAAK,CAAC,IACnC,GAAkB,UAAd,EAAK,IAAI,EAAgB,EAAK,IAAI,CAAE,CAEtC,IAAM,EAAgB,EAAK,IAAI,CAAC,OAAO,CAAC,OAAQ,MAAM,OAAO,CAAC,SAAU,QACxE,GAAe,EAEf,IAAU,EACZ,CACF,CAAE,MAAO,EAAI,CAEb,CAGN,CAGA,IAAa,GAGb,GAAI,CACF,IAAM,EAAa,MAAM,EAAqB,EAAgB,GAC9D,GAAI,CAAC,EAAW,OAAO,CAGrB,CAHuB,MACvB,QAAQ,KAAK,CAAC,oCAAqC,EAAW,KAAK,EACnE,IAAU,CAAC,sCAAsC,EAAE,EAAW,KAAK,CAAA,CAAE,EAC9D,CAAE,SAAS,EAAO,MAAO,CAAC,sCAAsC,EAAE,EAAW,KAAK,CAAA,CAAE,AAAC,CAEhG,CAAE,MAAO,EAAW,CAGlB,OAFA,QAAQ,KAAK,CAAC,kCAAmC,GACjD,IAAU,gEACH,CAAE,SAAS,EAAO,MAAO,8DAA+D,CACjG,CAGA,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,EACkB,GAAM,KAAM,CAAY,CAE5C,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oBAAqB,GACnC,IAAM,EAAW,CAAC,4BAA4B,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,gBAAA,CAAiB,CAE1G,OADA,IAAU,GACH,CAAE,QAAS,GAAO,MAAO,CAAS,CAC3C,CAEF,CAAE,MAAO,EAAO,CAGd,OAFA,QAAQ,KAAK,CAAC,oCAAqC,GACnD,IAAU,gCACH,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EACpB,CAAsB,CACtB,CAAkB,EAElB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAQjD,GAAI,CAJoB,AAInB,EAJmB,wBAAwB,CAAC,SAAS,CAAC,CACzD,CADsB,WACT,CACf,GAEqB,OAAO,CAC1B,CAD4B,KACrB,CAAE,SAAS,EAAO,MAAO,sBAAuB,EAGzD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAW,AAAX,IAGjB,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,YAAa,EACb,OAAQ,UACV,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,gBAAiB,GACxB,CAAE,SAAS,EAAO,MAAO,gCAAiC,EAKnE,MAFA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cAER,CAAE,MAFT,GAEkB,EAAM,KAAM,EAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAyB,CAAsB,CAAE,CAAmB,EACxF,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,OAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,WAAY,CACd,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,CAAE,SAAS,EAAO,MAAO,sCAAuC,EAIzE,MADA,CAAA,EAAA,EAAA,cAAa,AAAb,EAAe,cACR,CAAE,MADT,GACkB,EAAM,KAAM,EAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAmB,CAAsB,CAAE,CAAe,EAC9E,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,EAEF,GAAO,MAAO,cAAe,EAEjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,MAAO,CAAU,CAAE,CAAG,IAH3B,EAGiC,EACrD,IAAI,CAAC,iBACL,MAAM,CAAC,iCACP,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,MAAM,GACT,GAAI,GAAc,CAAC,EACjB,MAAO,CAAE,KADsB,IACb,EAAO,MAAO,wBAAyB,EAE3D,GAA4B,YAAY,CAApC,EAAa,MAAM,CACrB,MAAO,CAAE,SAAS,EAAO,MAAO,CAAC,4DAA4D,EAAE,EAAa,MAAM,CAAA,CAAE,AAAC,EAIvH,IAAM,EAAe,MAAM,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAW,EAAQ,MAAM,CAAE,EAAgB,SACjF,GAD2B,AACvB,CAAC,EAAa,OAAO,CACvB,CADyB,KAClB,CAAE,SAAS,EAAO,MAAO,CAAC,qBAAqB,EAAE,EAAa,KAAK,CAAA,CAAE,AAAC,EAG/E,IAAM,EAAsB,EAAqB,EAAa,qBAAqB,EACnF,EAAoB,IAAI,CAAC,EAAa,GAAG,EAGzC,GAAM,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,IAAI,CAAC,iBACL,MAAM,CAAC,CAAE,sBAAuB,CAAoB,GACpD,EAAE,CAAC,KAAM,GACZ,GAAI,EACF,MAAO,CAAE,IADM,KACG,EAAO,MAAO,gCAAiC,EAInE,MAFA,GAAA,EAAA,cAAA,AAAa,EAAE,cACf,CAAA,EAAA,EAAA,EADA,YACA,AAAa,EAAE,WACR,CAAE,SADT,AACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,CAAE,QAAS,GAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAoB,CAAsB,CAAE,CAAkB,EAClF,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,EAEF,GAAO,MAAO,cAAe,EAEjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,MAAO,CAAU,CAAE,CAAG,IAH3B,EAGiC,EACrD,IAAI,CAAC,iBACL,MAAM,CAAC,sBACP,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,MAAM,GACT,GAAI,GAAc,CAAC,EACjB,MAAO,CAAE,KADsB,IACb,EAAO,MAAO,wBAAyB,EAE3D,GAA4B,YAAY,CAApC,EAAa,MAAM,CACrB,MAAO,CAAE,SAAS,EAAO,MAAO,CAAC,6DAA6D,EAAE,EAAa,MAAM,CAAA,CAAE,AAAC,EAIxH,IAAM,EAAgB,MAAM,QAAQ,GAAG,CACrC,EAAW,GAAG,CAAC,GAAQ,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAM,EAAQ,MAAM,CAAE,EAAgB,WAIpE,CAJmB,CAIH,EAAc,MAAM,CAAC,GAAU,CAAC,EAAO,OAAO,EACpE,GAAI,EAAc,MAAM,CAAG,EAAG,CAC5B,IAAM,EAAS,EAAc,GAAG,CAAC,GAAK,EAAE,KAAK,EAAE,IAAI,CAAC,MACpD,MAAO,CAAE,SAAS,EAAO,MAAO,CAAC,qBAAqB,EAAE,EAAA,CAAQ,AAAC,CACnE,CAGA,IAAM,EAAoB,EAAqB,EAAa,UAAU,EAChE,EAAe,EAAc,GAAG,CAAC,GAAU,EAAO,GAAG,EAAG,MAAM,CAAC,GAAO,GACtE,EAAe,IAAI,KAAsB,EAAa,CAGtD,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,IAAI,CAAC,iBACL,MAAM,CAAC,CAAE,WAAY,CAAa,GAClC,EAAE,CAAC,KAAM,GACZ,GAAI,EACF,MAAO,CAAE,IADM,KACG,EAAO,MAAO,iCAAkC,EAKpE,MAFA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACf,CAAA,EAAA,EAAA,EADA,YACA,AAAa,EAAE,WACR,CAAE,QAAS,CADlB,EACwB,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,+BAAgC,GACvC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAuB,CAAsB,CAAE,CAAsI,EACzM,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,GAAA,EAAA,YAAA,AAAW,IAG5B,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,kBAAmB,CACrB,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,CAAE,SAAS,EAAO,MAAO,oCAAqC,EAIvE,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAe,CAAsB,CAAE,CAAkB,EAC7E,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,YAAa,CACf,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,0BAA2B,GAClC,CAAE,SAAS,EAAO,MAAO,4BAA6B,EAI/D,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAsB,CAAsB,CAAE,CAAuB,EACzF,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,EAEF,GAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAW,AAAX,IAGjB,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,iBAAkB,GAAmB,IACvC,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,iCAAkC,GACzC,CAAE,SAAS,EAAO,MAAO,mCAAoC,EAItE,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,iCAAkC,GACzC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAkB,CAAsB,CAAE,CAAmB,EACjF,GAAI,CACF,IAAM,EAAU,MAAM,GAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAW,AAAX,IAGjB,CAAE,OAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,aAAc,GAAe,IAC/B,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,6BAA8B,GACrC,CAAE,SAAS,EAAO,MAAO,+BAAgC,EAIlE,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAgB,CAAsB,EAC1D,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,EAEF,GAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,OAAE,CAAK,CAAE,CAAG,MAAM,EACrB,GAJoB,CAIhB,CAAC,iBACL,MAAM,CAAC,CACN,YAAa,IACf,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,2BAA4B,GACnC,CAAE,QAAS,GAAO,MAAO,6BAA8B,EAIhE,MADA,GAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,2BAA4B,GACnC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAGO,eAAe,EACpB,CAAsB,CACtB,CAAgB,EAEhB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CAAE,AADG,MADQ,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,MAAO,CAAU,CAAE,CAAG,IAH3B,EAGiC,EACrD,IAAI,CAAC,iBACL,MAAM,CAAC,iCACP,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,MAAM,GAET,GAAI,GAAc,CAAC,EACjB,MAAO,CAAE,KADsB,IACb,EAAO,MAAO,wBAAyB,EAI3D,GAA4B,AAAxB,YAAoC,GAAvB,MAAM,CACrB,MAAO,CAAE,SAAS,EAAO,MAAO,iDAAkD,EAKpF,IAAM,EADsB,AACH,EADwB,EAAa,qBAAqB,EACtC,MAAM,CAAC,GAAO,IAAQ,GAG7D,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,IAAI,CAAC,iBACL,MAAM,CAAC,CACN,sBAAuB,CACzB,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,OADA,IADe,IACP,KAAK,CAAC,iCAAkC,GACzC,CAAE,SAAS,EAAO,MAAO,mCAAoC,EAItE,GAAI,CACF,GAAM,YAAE,CAAU,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACjB,EAAW,EAAS,KAAK,CAAC,KAAK,GAAG,IAAM,EAC9C,OAAM,EAAW,EAAU,QAC7B,CAAE,MAAO,EAAc,CACrB,QAAQ,KAAK,CAAC,0BAA2B,EAE3C,CAGA,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,iCAAkC,GACzC,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAGO,eAAe,EACpB,CAAsB,CACtB,CAAgB,EAEhB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,MAAO,CAAU,CAAE,CAAG,IAH3B,EAGiC,EACrD,IAAI,CAAC,iBACL,MAAM,CAAC,sBACP,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAC9B,MAAM,GAET,GAAI,GAAc,CAAC,EACjB,MAAO,CAAE,KADsB,IACb,EAAO,MAAO,wBAAyB,EAI3D,GAA4B,YAAY,CAApC,EAAa,MAAM,CACrB,MAAO,CAAE,SAAS,EAAO,MAAO,iDAAkD,EAKpF,IAAM,EADY,AACO,EADc,EAAa,UAAU,EAC3B,MAAM,CAAC,GAAO,IAAQ,GAGnD,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,IAAI,CAAC,iBACL,MAAM,CAAC,CACN,WAAY,CACd,GACC,EAAE,CAAC,KAAM,GACT,EAAE,CAAC,YAAa,EAAQ,MAAM,EAEjC,GAAI,EAEF,OADA,IADe,IACP,KAAK,CAAC,mCAAoC,GAC3C,CAAE,QAAS,GAAO,MAAO,wBAAyB,EAI3D,GAAI,CACF,GAAM,YAAE,CAAU,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACjB,EAAW,EAAS,KAAK,CAAC,KAAK,GAAG,IAAM,EAC9C,OAAM,EAAW,EAAU,QAC7B,CAAE,MAAO,EAAc,CACrB,QAAQ,KAAK,CAAC,0BAA2B,EAE3C,CAGA,MADA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACR,CAAE,MADT,GACkB,EAAM,KAAM,EAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAGO,eAAe,IAMpB,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClC,GAAI,CAAC,EACH,MAAO,CADK,AACH,MAFW,GAEF,EAAO,MAAO,cAAe,EAGjD,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAE5B,CAAE,MAAI,OAAE,CAAK,CAAE,CAAG,IAFD,EAEO,EAAS,GAAG,CAAC,yBAA0B,CACnE,YAAa,EAAQ,MAAM,AAC7B,GAEA,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,CAAE,SAAS,EAAO,MAAO,oCAAqC,EAGvE,IAAM,EAAQ,GAAM,CAAC,EAAE,EAAI,CACzB,oBAAqB,EACrB,sBAAuB,EACvB,uBAAwB,EACxB,oBAAqB,CACvB,EAEA,MAAO,CACL,SAAS,EACT,KAAM,CACJ,oBAAqB,OAAO,EAAM,mBAAmB,EACrD,sBAAuB,OAAO,EAAM,qBAAqB,EACzD,uBAAwB,OAAO,EAAM,sBAAsB,EAC3D,oBAAqB,OAAO,EAAM,mBAAmB,CACvD,CACF,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,CAAE,QAAS,GAAO,MAAO,8BAA+B,CACjE,CACF,2CAv8BsB,EAyFA,EAiCA,EA2FA,EA0GA,EAwIA,EA6CA,EA+BA,EAgDA,EAyDA,EA+BA,EA+BA,EA+BA,EA+BA,EAgCA,EAkEA,EAkEA,IA55BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAyFA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAiCA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA2FA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA0GA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAwIA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA6CA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA+BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAgDA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAyDA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA+BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA+BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA+BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA+BA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAgCA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAkEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAkEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA"}