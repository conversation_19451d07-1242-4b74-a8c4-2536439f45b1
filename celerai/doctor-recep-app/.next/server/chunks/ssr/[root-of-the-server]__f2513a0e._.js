module.exports={1186:a=>{var{g:b,__dirname:c}=a;a.v({className:"inter_59dee874-module__9CtR0q__className"})},610309:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>c});var d=a.i(1186);let b={className:d.default.className,style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"}};null!=d.default.variable&&(b.variable=d.default.variable);let c=b}},913003:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Analytics:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/node_modules/@vercel/analytics/dist/next/index.mjs <module evaluation>","Analytics")}},960997:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Analytics:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/node_modules/@vercel/analytics/dist/next/index.mjs","Analytics")}},424523:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(913003);var d=a.i(960997);a.n(d)},72260:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SpeedInsights:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs <module evaluation>","SpeedInsights")}},879197:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SpeedInsights:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs","SpeedInsights")}},553902:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(72260);var d=a.i(879197);a.n(d)},362901:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/script.js <module evaluation>"))}},683934:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/script.js"))}},689297:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(362901);var d=a.i(683934);a.n(d)},784866:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(689297)},606211:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AnalyticsProvider:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AnalyticsProvider() from the server but AnalyticsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/analytics-provider.tsx <module evaluation>","AnalyticsProvider")}},39564:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AnalyticsProvider:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AnalyticsProvider() from the server but AnalyticsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/analytics-provider.tsx","AnalyticsProvider")}},597667:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(606211);var d=a.i(39564);a.n(d)},520884:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>j,metadata:()=>b,viewport:()=>c});var d=a.i(129629),e=a.i(610309),f=a.i(424523),g=a.i(553902),h=a.i(784866),i=a.i(597667);let b={title:"Celer AI",description:"AI-powered patient consultation summary system for Indian doctors",keywords:"doctor, patient, consultation, AI, summary, clinic, healthcare",authors:[{name:"Celer AI"}],manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"Doctor Reception"},icons:{icon:[{url:"/icons/favicon-16x16.png",sizes:"16x16",type:"image/png"},{url:"/icons/favicon-32x32.png",sizes:"32x32",type:"image/png"},{url:"/icons/favicon-48x48.png",sizes:"48x48",type:"image/png"}],apple:[{url:"/icons/apple-touch-icon-180x180.png",sizes:"180x180",type:"image/png"}]}},c={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,themeColor:"#2563eb"};function j({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"32x32"}),(0,d.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/icons/favicon-16x16.png"}),(0,d.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/icons/favicon-32x32.png"}),(0,d.jsx)("link",{rel:"icon",type:"image/png",sizes:"48x48",href:"/icons/favicon-48x48.png"}),(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/icons/apple-touch-icon-180x180.png"}),(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"152x152",href:"/icons/apple-touch-icon-152x152.png"}),(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"120x120",href:"/icons/apple-touch-icon-120x120.png"}),(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,d.jsx)("meta",{name:"theme-color",content:"#14b8a6"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Celer AI"}),(0,d.jsx)("meta",{name:"msapplication-TileColor",content:"#14b8a6"}),(0,d.jsx)("meta",{name:"msapplication-TileImage",content:"/icons/icon-144x144.png"}),(0,d.jsx)(h.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-66GG02C5H5",strategy:"lazyOnload"}),(0,d.jsx)(h.default,{id:"google-analytics",strategy:"lazyOnload",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-66GG02C5H5', {
              page_title: document.title,
              custom_map: {'custom_parameter_1': 'zone_type'}
            });
          `})]}),(0,d.jsxs)("body",{className:`${e.default.className} antialiased`,children:[(0,d.jsx)(i.AnalyticsProvider,{children:a}),(0,d.jsx)(f.Analytics,{}),(0,d.jsx)(g.SpeedInsights,{})]})]})}}}};

//# sourceMappingURL=%5Broot-of-the-server%5D__f2513a0e._.js.map