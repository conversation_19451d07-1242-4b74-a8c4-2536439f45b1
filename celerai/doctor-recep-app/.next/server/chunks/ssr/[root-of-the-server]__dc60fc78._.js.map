{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/noop-head.tsx", "turbopack:///[project]/src/app/sentry-example-page/page.tsx"], "sourcesContent": ["export default function NoopHead() {\n  return null\n}\n", "\"use client\";\n\nimport Head from \"next/head\";\nimport * as Sentry from \"@sentry/nextjs\";\nimport { useState, useEffect } from \"react\";\n\nclass SentryExampleFrontendError extends Error {\n  constructor(message: string | undefined) {\n    super(message);\n    this.name = \"SentryExampleFrontendError\";\n  }\n}\n\nexport default function Page() {\n  const [hasSentError, setHasSentError] = useState(false);\n  const [isConnected, setIsConnected] = useState(true);\n  \n  useEffect(() => {\n    async function checkConnectivity() {\n      const result = await Sentry.diagnoseSdkConnectivity();\n      setIsConnected(result !== 'sentry-unreachable');\n    }\n    checkConnectivity();\n  }, []);\n\n  return (\n    <div>\n      <Head>\n        <title>sentry-example-page</title>\n        <meta name=\"description\" content=\"Test Sentry for your Next.js app!\" />\n      </Head>\n\n      <main>\n        <div className=\"flex-spacer\" />\n        <svg height=\"40\" width=\"40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M21.85 2.995a3.698 3.698 0 0 1 1.353 1.354l16.303 28.278a3.703 3.703 0 0 1-1.354 5.053 3.694 3.694 0 0 1-1.848.496h-3.828a31.149 31.149 0 0 0 0-3.09h3.815a.61.61 0 0 0 .537-.917L20.523 5.893a.61.61 0 0 0-1.057 0l-3.739 6.494a28.948 28.948 0 0 1 9.63 10.453 28.988 28.988 0 0 1 3.499 13.78v1.542h-9.852v-1.544a19.106 19.106 0 0 0-2.182-8.85 19.08 19.08 0 0 0-6.032-6.829l-1.85 3.208a15.377 15.377 0 0 1 6.382 12.484v1.542H3.696A3.694 3.694 0 0 1 0 34.473c0-.648.17-1.286.494-1.849l2.33-4.074a8.562 8.562 0 0 1 2.689 1.536L3.158 34.17a.611.611 0 0 0 .538.917h8.448a12.481 12.481 0 0 0-6.037-9.09l-1.344-.772 4.908-8.545 1.344.77a22.16 22.16 0 0 1 7.705 7.444 22.193 22.193 0 0 1 3.316 10.193h3.699a25.892 25.892 0 0 0-3.811-12.033 25.856 25.856 0 0 0-9.046-8.796l-1.344-.772 5.269-9.136a3.698 3.698 0 0 1 3.2-1.849c.648 0 1.285.17 1.847.495Z\" fill=\"currentcolor\"/>\n        </svg>\n        <h1>\n          sentry-example-page\n        </h1>\n\n        <p className=\"description\">\n          Click the button below, and view the sample error on the Sentry <a target=\"_blank\" href=\"https://celer-ai.sentry.io/issues/?project=4509552950640720\">Issues Page</a>.\n          For more details about setting up Sentry, <a target=\"_blank\" href=\"https://docs.sentry.io/platforms/javascript/guides/nextjs/\">read our docs</a>.\n        </p>\n\n        <button\n          type=\"button\"\n          onClick={async () => {\n            await Sentry.startSpan({\n              name: 'Example Frontend Span',\n              op: 'test'\n            }, async () => {\n              const res = await fetch(\"/api/sentry-example-api\");\n              if (!res.ok) {\n                setHasSentError(true);\n                throw new SentryExampleFrontendError(\"This error is raised on the frontend of the example page.\");\n              }\n            });\n          }}\n        >\n          <span>\n            Throw Sample Error\n          </span>\n        </button>\n\n        {hasSentError ? (\n          <p className=\"success\">\n            Sample error was sent to Sentry.\n          </p>\n        ) : !isConnected ? (\n          <div className=\"connectivity-error\">\n            <p>The Sentry SDK is not able to reach Sentry right now - this may be due to an adblocker. For more information, see <a target=\"_blank\" href=\"https://docs.sentry.io/platforms/javascript/guides/nextjs/troubleshooting/#the-sdk-is-not-sending-any-data\">the troubleshooting guide</a>.</p>\n          </div>\n        ) : (\n          <div className=\"success_placeholder\" />\n        )}\n\n        <div className=\"flex-spacer\" />\n        \n        <p className=\"description\">\n          Adblockers will prevent errors from being sent to Sentry.\n        </p>\n      </main>\n\n      <style>{`\n        main {\n          display: flex;\n          min-height: 100vh;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          gap: 16px;\n          padding: 16px;\n          font-family: system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", sans-serif;\n        }\n\n        h1 {\n          padding: 0px 4px;\n          border-radius: 4px;\n          background-color: rgba(24, 20, 35, 0.03);\n          font-family: monospace;\n          font-size: 20px;\n          line-height: 1.2;\n        }\n\n        p {\n          margin: 0;\n          font-size: 20px;\n        }\n\n        a {\n          color: #6341F0;\n          text-decoration: underline;\n          cursor: pointer;\n\n          @media (prefers-color-scheme: dark) {\n            color: #B3A1FF;\n          }\n        }\n\n        button {\n          border-radius: 8px;\n          color: white;\n          cursor: pointer;\n          background-color: #553DB8;\n          border: none;\n          padding: 0;\n          margin-top: 4px;\n\n          & > span {\n            display: inline-block;\n            padding: 12px 16px;\n            border-radius: inherit;\n            font-size: 20px;\n            font-weight: bold;\n            line-height: 1;\n            background-color: #7553FF;\n            border: 1px solid #553DB8;\n            transform: translateY(-4px);\n          }\n\n          &:hover > span {\n            transform: translateY(-8px);\n          }\n\n          &:active > span {\n            transform: translateY(0);\n          }\n        }\n\n        .description {\n          text-align: center;\n          color: #6E6C75;\n          max-width: 500px;\n          line-height: 1.5;\n          font-size: 20px;\n\n          @media (prefers-color-scheme: dark) {\n            color: #A49FB5;\n          }\n        }\n\n        .flex-spacer {\n          flex: 1;\n        }\n\n        .success {\n          padding: 12px 16px;\n          border-radius: 8px;\n          font-size: 20px;\n          line-height: 1;\n          background-color: #00F261;\n          border: 1px solid #00BF4D;\n          color: #181423;\n        }\n\n        .success_placeholder {\n          height: 46px;\n        }\n\n        .connectivity-error {\n          padding: 12px 16px;\n          background-color: #E50045;\n          border-radius: 8px;\n          width: 500px;\n          color: #FFFFFF;\n          border: 1px solid #A80033;\n          text-align: center;\n          margin: 0;\n        }\n        \n        .connectivity-error a {\n          color: #FFFFFF;\n          text-decoration: underline;\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": ["NoopHead"], "mappings": "u+bAAe,SAASA,IACtB,OAAO,IACT,0EAFA,UAAA,qCAAwBA,oTCExB,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OAEA,OAAM,UAAmC,MACvC,YAAY,CAA2B,CAAE,CACvC,KAAK,CAAC,GACN,IAAI,CAAC,IAAI,CAAG,4BACd,CACF,CAEe,SAAS,IACtB,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,GAC3C,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAO,AAAP,GAAS,CADP,EAWxC,MARA,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,CAF4B,KAOpC,AAJA,eAAe,EAEb,EAAe,AAAW,CAH9B,sBAEmB,MAAM,GAAA,EAAA,uBAAA,AAA6B,IAEpD,GAEF,EAAG,EAAE,EAGH,CAPuB,AAOvB,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,AADF,UACQ,wBACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cAAc,QAAQ,yCAGnC,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,OAAO,KAAK,MAAM,KAAK,KAAK,OAAO,MAAM,sCAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,00BAA00B,KAAK,mBAEz1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,wBAIJ,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,wBAAc,mEACuC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,OAAO,SAAS,KAAK,uEAA8D,gBAAe,+CAC3H,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,OAAO,SAAS,KAAK,sEAA6D,kBAAiB,OAGlJ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,QAAS,UACP,MAAM,GAAA,EAAA,SAAA,AAAe,EAAE,CACrB,KAAM,oBADF,IAEJ,GAAI,MACN,EAAG,UAED,GAAI,CAAC,CADO,MAAM,MAAM,0BAAA,EACf,EAAE,CAET,CAFW,KACX,EAAgB,IACV,IAAI,EAA2B,4DAEzC,EACF,WAEA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,yBAKP,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,qCAGrB,AAAC,EAKH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAJf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,WAAE,qHAAkH,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,OAAO,SAAS,KAAK,sHAA6G,8BAA6B,SAM3R,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBAEf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uBAAc,iEAK7B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+GT,CAAC,KAGP", "ignoreList": [0]}