{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/build/webpack/loaders/next-flight-loader/server-reference.ts", "turbopack:///[project]/node_modules/next/src/server/lib/trace/constants.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/is-thenable.ts", "turbopack:///[project]/node_modules/next/src/server/lib/trace/tracer.ts", "turbopack:///[project]/node_modules/next/src/lib/detached-promise.ts", "turbopack:///[project]/node_modules/next/src/server/stream-utils/encodedTags.ts", "turbopack:///[project]/node_modules/next/src/server/stream-utils/uint8array-helpers.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/errors/constants.ts", "turbopack:///[project]/node_modules/next/src/server/stream-utils/node-web-streams-helper.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/invariant-error.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/segment.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/app-paths.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/encryption-utils.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/encryption.ts", "turbopack:///[project]/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-validate.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nexport { registerServerReference } from 'react-server-dom-webpack/server.edge'\n", "/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n", "import type { FetchEventResult } from '../../web/types'\nimport type { TextMapSetter } from '@opentelemetry/api'\nimport type { SpanTypes } from './constants'\nimport { LogSpanAllowList, NextVanillaSpanAllowlist } from './constants'\n\nimport type {\n  ContextAPI,\n  Span,\n  SpanOptions,\n  Tracer,\n  AttributeValue,\n  TextMapGetter,\n} from 'next/dist/compiled/@opentelemetry/api'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\nlet api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n  api = require('@opentelemetry/api')\n} else {\n  try {\n    api = require('@opentelemetry/api')\n  } catch (err) {\n    api = require('next/dist/compiled/@opentelemetry/api')\n  }\n}\n\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } =\n  api\n\nexport class BubbledError extends Error {\n  constructor(\n    public readonly bubble?: boolean,\n    public readonly result?: FetchEventResult\n  ) {\n    super()\n  }\n}\n\nexport function isBubbledError(error: unknown): error is BubbledError {\n  if (typeof error !== 'object' || error === null) return false\n  return error instanceof BubbledError\n}\n\nconst closeSpanWithError = (span: Span, error?: Error) => {\n  if (isBubbledError(error) && error.bubble) {\n    span.setAttribute('next.bubble', true)\n  } else {\n    if (error) {\n      span.recordException(error)\n    }\n    span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message })\n  }\n  span.end()\n}\n\ntype TracerSpanOptions = Omit<SpanOptions, 'attributes'> & {\n  parentSpan?: Span\n  spanName?: string\n  attributes?: Partial<Record<AttributeNames, AttributeValue | undefined>>\n  hideSpan?: boolean\n}\n\ninterface NextTracer {\n  getContext(): ContextAPI\n\n  /**\n   * Instruments a function by automatically creating a span activated on its\n   * scope.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its second parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   *\n   */\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n\n  /**\n   * Wrap a function to automatically create a span activated on its\n   * scope when it's called.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its last parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   */\n  wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n\n  /**\n   * Starts and returns a new Span representing a logical unit of work.\n   *\n   * This method do NOT modify the current Context by default. In result, any inner span will not\n   * automatically set its parent context to the span created by this method unless manually activate\n   * context via `tracer.getContext().with`. `trace`, or `wrap` is generally recommended as it gracefully\n   * handles context activation. (ref: https://github.com/open-telemetry/opentelemetry-js/issues/1923)\n   */\n  startSpan(type: SpanTypes): Span\n  startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n\n  /**\n   * Returns currently activated span if current context is in the scope of the span.\n   * Returns undefined otherwise.\n   */\n  getActiveScopeSpan(): Span | undefined\n\n  /**\n   * Returns trace propagation data for the currently active context. The format is equal to data provided\n   * through the OpenTelemetry propagator API.\n   */\n  getTracePropagationData(): ClientTraceDataEntry[]\n}\n\ntype NextAttributeNames =\n  | 'next.route'\n  | 'next.page'\n  | 'next.rsc'\n  | 'next.segment'\n  | 'next.span_name'\n  | 'next.span_type'\n  | 'next.clientComponentLoadCount'\ntype OTELAttributeNames = `http.${string}` | `net.${string}`\ntype AttributeNames = NextAttributeNames | OTELAttributeNames\n\n/** we use this map to propagate attributes from nested spans to the top span */\nconst rootSpanAttributesStore = new Map<\n  number,\n  Map<AttributeNames, AttributeValue | undefined>\n>()\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId')\nlet lastSpanId = 0\nconst getSpanId = () => lastSpanId++\n\nexport interface ClientTraceDataEntry {\n  key: string\n  value: string\n}\n\nconst clientTraceDataSetter: TextMapSetter<ClientTraceDataEntry[]> = {\n  set(carrier, key, value) {\n    carrier.push({\n      key,\n      value,\n    })\n  },\n}\n\nclass NextTracerImpl implements NextTracer {\n  /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */\n  private getTracerInstance(): Tracer {\n    return trace.getTracer('next.js', '0.0.1')\n  }\n\n  public getContext(): ContextAPI {\n    return context\n  }\n\n  public getTracePropagationData(): ClientTraceDataEntry[] {\n    const activeContext = context.active()\n    const entries: ClientTraceDataEntry[] = []\n    propagation.inject(activeContext, entries, clientTraceDataSetter)\n    return entries\n  }\n\n  public getActiveScopeSpan(): Span | undefined {\n    return trace.getSpan(context?.active())\n  }\n\n  public withPropagatedContext<T, C>(\n    carrier: C,\n    fn: () => T,\n    getter?: TextMapGetter<C>\n  ): T {\n    const activeContext = context.active()\n    if (trace.getSpanContext(activeContext)) {\n      // Active span is already set, too late to propagate.\n      return fn()\n    }\n    const remoteContext = propagation.extract(activeContext, carrier, getter)\n    return context.with(remoteContext, fn)\n  }\n\n  // Trace, wrap implementation is inspired by datadog trace implementation\n  // (https://datadoghq.dev/dd-trace-js/interfaces/tracer.html#trace).\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(...args: Array<any>) {\n    const [type, fnOrOptions, fnOrEmpty] = args\n\n    // coerce options form overload\n    const {\n      fn,\n      options,\n    }: {\n      fn: (span?: Span, done?: (error?: Error) => any) => T | Promise<T>\n      options: TracerSpanOptions\n    } =\n      typeof fnOrOptions === 'function'\n        ? {\n            fn: fnOrOptions,\n            options: {},\n          }\n        : {\n            fn: fnOrEmpty,\n            options: { ...fnOrOptions },\n          }\n\n    const spanName = options.spanName ?? type\n\n    if (\n      (!NextVanillaSpanAllowlist.includes(type) &&\n        process.env.NEXT_OTEL_VERBOSE !== '1') ||\n      options.hideSpan\n    ) {\n      return fn()\n    }\n\n    // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n    let spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    let isRootSpan = false\n\n    if (!spanContext) {\n      spanContext = context?.active() ?? ROOT_CONTEXT\n      isRootSpan = true\n    } else if (trace.getSpanContext(spanContext)?.isRemote) {\n      isRootSpan = true\n    }\n\n    const spanId = getSpanId()\n\n    options.attributes = {\n      'next.span_name': spanName,\n      'next.span_type': type,\n      ...options.attributes,\n    }\n\n    return context.with(spanContext.setValue(rootSpanIdKey, spanId), () =>\n      this.getTracerInstance().startActiveSpan(\n        spanName,\n        options,\n        (span: Span) => {\n          const startTime =\n            'performance' in globalThis && 'measure' in performance\n              ? globalThis.performance.now()\n              : undefined\n\n          const onCleanup = () => {\n            rootSpanAttributesStore.delete(spanId)\n            if (\n              startTime &&\n              process.env.NEXT_OTEL_PERFORMANCE_PREFIX &&\n              LogSpanAllowList.includes(type || ('' as any))\n            ) {\n              performance.measure(\n                `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(\n                  type.split('.').pop() || ''\n                ).replace(\n                  /[A-Z]/g,\n                  (match: string) => '-' + match.toLowerCase()\n                )}`,\n                {\n                  start: startTime,\n                  end: performance.now(),\n                }\n              )\n            }\n          }\n\n          if (isRootSpan) {\n            rootSpanAttributesStore.set(\n              spanId,\n              new Map(\n                Object.entries(options.attributes ?? {}) as [\n                  AttributeNames,\n                  AttributeValue | undefined,\n                ][]\n              )\n            )\n          }\n          try {\n            if (fn.length > 1) {\n              return fn(span, (err) => closeSpanWithError(span, err))\n            }\n\n            const result = fn(span)\n            if (isThenable(result)) {\n              // If there's error make sure it throws\n              return result\n                .then((res) => {\n                  span.end()\n                  // Need to pass down the promise result,\n                  // it could be react stream response with error { error, stream }\n                  return res\n                })\n                .catch((err) => {\n                  closeSpanWithError(span, err)\n                  throw err\n                })\n                .finally(onCleanup)\n            } else {\n              span.end()\n              onCleanup()\n            }\n\n            return result\n          } catch (err: any) {\n            closeSpanWithError(span, err)\n            onCleanup()\n            throw err\n          }\n        }\n      )\n    )\n  }\n\n  public wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap(...args: Array<any>) {\n    const tracer = this\n    const [name, options, fn] =\n      args.length === 3 ? args : [args[0], {}, args[1]]\n\n    if (\n      !NextVanillaSpanAllowlist.includes(name) &&\n      process.env.NEXT_OTEL_VERBOSE !== '1'\n    ) {\n      return fn\n    }\n\n    return function (this: any) {\n      let optionsObj = options\n      if (typeof optionsObj === 'function' && typeof fn === 'function') {\n        optionsObj = optionsObj.apply(this, arguments)\n      }\n\n      const lastArgId = arguments.length - 1\n      const cb = arguments[lastArgId]\n\n      if (typeof cb === 'function') {\n        const scopeBoundCb = tracer.getContext().bind(context.active(), cb)\n        return tracer.trace(name, optionsObj, (_span, done) => {\n          arguments[lastArgId] = function (err: any) {\n            done?.(err)\n            return scopeBoundCb.apply(this, arguments)\n          }\n\n          return fn.apply(this, arguments)\n        })\n      } else {\n        return tracer.trace(name, optionsObj, () => fn.apply(this, arguments))\n      }\n    }\n  }\n\n  public startSpan(type: SpanTypes): Span\n  public startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n  public startSpan(...args: Array<any>): Span {\n    const [type, options]: [string, TracerSpanOptions | undefined] = args as any\n\n    const spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    return this.getTracerInstance().startSpan(type, options, spanContext)\n  }\n\n  private getSpanContext(parentSpan?: Span) {\n    const spanContext = parentSpan\n      ? trace.setSpan(context.active(), parentSpan)\n      : undefined\n\n    return spanContext\n  }\n\n  public getRootSpanAttributes() {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    return rootSpanAttributesStore.get(spanId)\n  }\n\n  public setRootSpanAttribute(key: AttributeNames, value: AttributeValue) {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    const attributes = rootSpanAttributesStore.get(spanId)\n    if (attributes) {\n      attributes.set(key, value)\n    }\n  }\n}\n\nconst getTracer = (() => {\n  const tracer = new NextTracerImpl()\n\n  return () => tracer\n})()\n\nexport { getTracer, SpanStatusCode, SpanKind }\nexport type { NextTracer, Span, SpanOptions, ContextAPI, TracerSpanOptions }\n", "/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n", "export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n} as const\n", "/**\n * Find the starting index of Uint8Array `b` within Uint8Array `a`.\n */\nexport function indexOfUint8Array(a: Uint8Array, b: Uint8Array) {\n  if (b.length === 0) return 0\n  if (a.length === 0 || b.length > a.length) return -1\n\n  // start iterating through `a`\n  for (let i = 0; i <= a.length - b.length; i++) {\n    let completeMatch = true\n    // from index `i`, iterate through `b` and check for mismatch\n    for (let j = 0; j < b.length; j++) {\n      // if the values do not match, then this isn't a complete match, exit `b` iteration early and iterate to next index of `a`.\n      if (a[i + j] !== b[j]) {\n        completeMatch = false\n        break\n      }\n    }\n\n    if (completeMatch) {\n      return i\n    }\n  }\n\n  return -1\n}\n\n/**\n * Check if two Uint8Arrays are strictly equivalent.\n */\nexport function isEquivalentUint8Arrays(a: Uint8Array, b: Uint8Array) {\n  if (a.length !== b.length) return false\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Remove Uint8Array `b` from Uint8Array `a`.\n *\n * If `b` is not in `a`, `a` is returned unchanged.\n *\n * Otherwise, the function returns a new Uint8Array instance with size `a.length - b.length`\n */\nexport function removeFromUint8Array(a: Uint8Array, b: Uint8Array) {\n  const tagIndex = indexOfUint8Array(a, b)\n  if (tagIndex === 0) return a.subarray(b.length)\n  if (tagIndex > -1) {\n    const removed = new Uint8Array(a.length - b.length)\n    removed.set(a.slice(0, tagIndex))\n    removed.set(a.slice(tagIndex + b.length), tagIndex)\n    return removed\n  } else {\n    return a\n  }\n}\n", "export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n", "import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n", "/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client.edge'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    const hangingInputAbortSignal =\n      workUnitStore?.type === 'prerender'\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      throw error\n    }\n\n    if (!workUnitStore) {\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    cacheSignal?.beginRead()\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    cacheSignal?.endRead()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        if (workUnitStore?.type === 'prerender') {\n          // Explicitly don't close the stream here (until prerendering is\n          // complete) so that hanging promises are not rejected.\n          if (workUnitStore.renderSignal.aborted) {\n            controller.close()\n          } else {\n            workUnitStore.renderSignal.addEventListener(\n              'abort',\n              () => controller.close(),\n              { once: true }\n            )\n          }\n        } else {\n          controller.close()\n        }\n      },\n    }),\n    {\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n", "// This function ensures that all the exported values are valid server actions,\n// during the runtime. By definition all actions are required to be async\n// functions, but here we can only check that they are functions.\nexport function ensureServerEntryExports(actions: any[]) {\n  for (let i = 0; i < actions.length; i++) {\n    const action = actions[i]\n    if (typeof action !== 'function') {\n      throw new Error(\n        `A \"use server\" file can only export async functions, found ${typeof action}.\\nRead more: https://nextjs.org/docs/messages/invalid-use-server-value`\n      )\n    }\n  }\n}\n"], "names": ["registerServerReference", "AppRenderSpan", "AppRouteRouteHandlersSpan", "BaseServerSpan", "LoadComponentsSpan", "LogSpanAllowList", "MiddlewareSpan", "NextNodeServerSpan", "NextServerSpan", "NextVanillaSpanAllowlist", "NodeSpan", "RenderSpan", "ResolveMetadataSpan", "RouterSpan", "StartServerSpan", "isThenable", "promise", "then", "BubbledError", "SpanKind", "SpanStatusCode", "getTracer", "isBubbledError", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "Error", "constructor", "bubble", "result", "error", "closeSpanWithError", "span", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "clientTraceDataSetter", "set", "carrier", "key", "value", "push", "NextTracerImpl", "getTracerInstance", "getContext", "getTracePropagationData", "activeContext", "active", "entries", "inject", "getActiveScopeSpan", "getSpan", "withPropagatedContext", "fn", "getter", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "Object", "length", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get", "setRootSpanAttribute", "Detached<PERSON>romise", "resolve", "reject", "Promise", "rej", "ENCODED_TAGS", "OPENING", "HTML", "Uint8Array", "BODY", "CLOSED", "HEAD", "BODY_AND_HTML", "indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "a", "b", "i", "completeMatch", "j", "tagIndex", "subarray", "removed", "slice", "MISSING_ROOT_TAGS_ERROR", "chainStreams", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createDocumentClosingStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "readable", "writable", "TransformStream", "pipeTo", "preventClose", "nextStream", "lastStream", "str", "ReadableStream", "controller", "enqueue", "encode", "close", "chunk", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "read", "<PERSON><PERSON><PERSON>", "concat", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "scheduleImmediate", "copiedBytes", "bufferedChunk", "byteLength", "transform", "ReactDOMServer", "element", "streamOptions", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "hasBytes", "insertion", "encodedInsertion", "index", "insertedHeadContent", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "atLeastOneTask", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "before", "after", "createStripDocumentClosingTagsTransform", "foundHtml", "foundBody", "missingTags", "map", "c", "join", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "allReady", "prerenderStream", "InvariantError", "endsWith", "ensureLeadingSlash", "path", "startsWith", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "searchParams", "isPageSegment", "stringified<PERSON><PERSON>y", "JSON", "stringify", "normalizeAppPath", "normalizeRscURL", "route", "reduce", "pathname", "segments", "url", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "__next_loaded_action_key", "buffer", "bytes", "len", "String", "fromCharCode", "binary", "arr", "charCodeAt", "iv", "data", "crypto", "subtle", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "clientReferenceManifestsPerPage", "serverActionsManifestSingleton", "workStore", "workAsyncStorage", "getStore", "mergeClientReferenceManifests", "<PERSON><PERSON><PERSON>", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "importKey", "atob", "clientReferenceManifests", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping", "decryptActionBoundArgs", "encryptActionBoundArgs", "isEdgeRuntime", "textEncoder", "textDecoder", "decodeActionBoundArg", "actionId", "arg", "originalPayload", "ivValue", "payload", "decrypted", "encodeActionBoundArg", "randomBytes", "workUnitAsyncStorage", "exit", "getRandomValues", "encrypted", "btoa", "React", "cache", "captureStackTrace", "didCatchError", "workUnitStore", "hangingInputAbortSignal", "createHangingInputAbortSignal", "serialized", "onError", "NODE_ENV", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "cacheSignal", "beginRead", "endRead", "encryptedPromise", "decryptedBoundArgs", "deserialized", "createFromReadableStream", "renderSignal", "addEventListener", "once", "serverConsumerManifest", "moduleLoading", "moduleMap", "ensureServerEntryExports", "actions", "action"], "mappings": "mEAAoD,sFAC3CA,0BAAAA,qCAAAA,EAAAA,uBAAuB,YAAQ,CAAA,CAAA,IAAA,wDCOZ,kEAEvBG,IAoBAK,IA0CAM,IAYAb,EA1EAE,AAiFAU,EAIAH,EAjEAF,EAyEAI,EAKAN,CApCAQ,CAmBAD,AAIAH,CAXAT,EA8EHA,SAtDGK,GALAM,CA2DU,CAAA,kBAAbX,GAEAC,yBAAyB,CAAA,kBAAzBA,GATAC,cAAc,CAAA,kBAAdA,GACAC,kBAAkB,CAAA,kBAAlBA,GARWC,gBAAgB,CAAA,kBAAhBA,GAkBXC,cAAc,CAAA,kBAAdA,GARAC,kBAAkB,CAAA,kBAAlBA,GADAC,cAAc,CAAA,kBAAdA,GA9BWC,wBAAwB,CAAA,kBAAxBA,GAoCXC,QAAQ,CAAA,kBAARA,GAHAC,UAAU,CAAA,kBAAVA,GAKAC,mBAAmB,CAAA,kBAAnBA,GAJAC,UAAU,CAAA,kBAAVA,GAFAC,eAAe,CAAA,kBAAfA,uEArJF,IAAKX,MAAAA,GAAAA,CAAAA,OAAAA,WAAAA,GAAAA,8eAAAA,GAeAC,IAAAA,EAAAA,GAAAA,CAAAA,WAAAA,CAAAA,UAAAA,GAAAA,kGAAAA,GAKAI,MAAAA,GAAAA,CAAAA,OAAAA,WAAAA,GAAAA,4KAAAA,GAOAD,IAAAA,EAAAA,GAAAA,CAAAA,WAAAA,CAAAA,UAAAA,GAAAA,04CAAAA,GAmCAO,MAAAA,GAAAA,CAAAA,QAAAA,WAAAA,GAAAA,mBAAAA,GAIAH,IAAAA,EAAAA,GAAAA,CAAAA,GAAAA,CAAAA,UAAAA,GAAAA,yMAAAA,GAQAV,MAAAA,GAAAA,CAAAA,MAAAA,WAAAA,GAAAA,yJAAAA,GAOAY,MAAAA,GAAAA,CAAAA,GAAAA,WAAAA,GAAAA,qBAAAA,GAIAH,MAAAA,GAAAA,CAAAA,CAAAA,WAAAA,GAAAA,iBAAAA,GAIAR,IAAAA,EAAAA,GAAAA,CAAAA,kBAAAA,CAAAA,UAAAA,GAAAA,iBAAAA,GAIAU,MAAAA,GAAAA,CAAAA,YAAAA,WAAAA,GAAAA,mFAAAA,GAKAN,MAAAA,GAAAA,CAAAA,OAAAA,WAAAA,GAAAA,WAAAA,GAmBE,IAAMG,EAA2B,2dAiBvC,CAIYJ,EAAmB,kHAI/B,qDCnJA,aACM,SAASU,EACdC,CAAuB,EAEvB,OACc,OAAZA,GACmB,UAAnB,OAAOA,GACP,SAAUA,GACV,AAAwB,mBAAjBA,EAAQC,IAAI,AAEvB,0EATgBF,aAAAA,qCAAAA,0ECSZQ,0DAsBSL,YAAY,CAAA,kBAAZA,GA+auBC,QAAQ,CAAA,kBAARA,GAAhBC,cAAc,CAAA,kBAAdA,GAAXC,SAAS,CAAA,kBAATA,GAtaOC,cAAc,CAAA,kBAAdA,+EA3C2C,CAAA,CAAA,IAAA,QAUhC,CAAA,CAAA,IAAA,IAczB,GAAI,CACFC,EAAMI,EAAQ,CAAA,CAAA,IAAA,GAChB,CAAE,MAAOC,EAAK,CACZL,EAAMI,EAAQ,CAAA,CAAA,IAAA,GAChB,CAGF,GAAM,SAAEE,CAAO,CAAEC,aAAW,OAAEC,CAAK,CAAEX,gBAAc,CAAED,UAAQ,cAAEa,CAAY,CAAE,CAC3ET,CAEK,OAAML,UAAqBe,MAChCC,YACkBC,CAAgB,CAChBC,CAAyB,CACzC,CACA,KAAK,GAAA,IAAA,CAHWD,MAAAA,CAAAA,EAAAA,IAAAA,CACAC,MAAAA,CAAAA,CAGlB,CACF,CAEO,SAASd,EAAee,CAAc,QAC3C,AAAqB,UAAjB,OAAOA,GAAgC,MAAM,CAAhBA,GAC1BA,GADiD,UAChCnB,CAC1B,CAEA,IAAMoB,EAAqB,CAACC,EAAYF,KAClCf,EAAee,IAAUA,EAAMF,MAAM,CACvCI,CADyC,CACpCC,YAAY,CAAC,eAAe,IAE7BH,GACFE,EAAKE,EADI,aACW,CAACJ,GAEvBE,EAAKG,SAAS,CAAC,CAAEC,KAAMvB,EAAewB,KAAK,CAAEC,OAAO,CAAER,MAAAA,EAAAA,KAAAA,EAAAA,EAAOQ,OAAO,AAAC,IAEvEN,EAAKO,GAAG,EACV,EA4GMC,EAA0B,IAAIC,IAI9BC,EAAgB1B,EAAI2B,gBAAgB,CAAC,mBACvCC,EAAa,EACXC,EAAY,IAAMD,IAOlBE,EAA+D,CACnEC,IAAIC,CAAO,CAAEC,CAAG,CAAEC,CAAK,EACrBF,EAAQG,IAAI,CAAC,KACXF,EACAC,OACF,EACF,CACF,CAEA,OAAME,EAMIC,mBAA4B,CAClC,OAAO7B,EAAMV,SAAS,CAAC,UAAW,QACpC,CAEOwC,YAAyB,CAC9B,OAAOhC,CACT,CAEOiC,yBAAkD,CACvD,IAAMC,EAAgBlC,EAAQmC,MAAM,GAC9BC,EAAkC,EAAE,CAE1C,OADAnC,EAAYoC,MAAM,CAACH,EAAeE,EAASZ,GACpCY,CACT,CAEOE,oBAAuC,CAC5C,OAAOpC,EAAMqC,OAAO,CAACvC,MAAAA,EAAAA,KAAAA,EAAAA,EAASmC,MAAM,GACtC,CAEOK,sBACLd,CAAU,CACVe,CAAW,CACXC,CAAyB,CACtB,CACH,IAAMR,EAAgBlC,EAAQmC,MAAM,GACpC,GAAIjC,EAAMyC,cAAc,CAACT,GAEvB,OAAOO,IAET,EAJyC,EAInCG,EAAgB3C,EAAY4C,OAAO,CAACX,EAAeR,EAASgB,GAClE,OAAO1C,EAAQ8C,IAAI,CAACF,EAAeH,EACrC,CAsBOvC,MAAS,GAAG6C,CAAgB,CAAE,KAwCxB7C,EAvCX,GAAM,CAAC8C,EAAMC,EAAaC,EAAU,CAAGH,EAGjC,IACJN,CAAE,SACFU,CAAO,CACR,CAIC,AAAuB,mBAAhBF,EACH,CACER,GAAIQ,EACJE,QAAS,CAAC,CACZ,EACA,CACEV,GAAIS,EACJC,QAAS,CAAE,GAAGF,CAAW,AAAC,CAC5B,EAEAG,EAAWD,EAAQC,QAAQ,EAAIJ,EAErC,GACG,CAACpE,EAAAA,wBAAwB,CAACyE,QAAQ,CAACL,IACA,MAAlCrD,QAAQC,GAAG,CAAC0D,iBAAiB,EAC/BH,EAAQI,QAAQ,CAEhB,CADA,MACOd,IAIT,IAAIe,EAAc,IAAI,CAACb,cAAc,CACnCQ,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASM,UAAAA,AAAU,GAAI,IAAI,CAACnB,kBAAkB,IAE5CoB,GAAa,EAEZF,EAGE,CAAyBA,OAArBtD,EAAAA,CAHO,CAGDyC,cAAc,CAACa,EAAAA,CAAAA,CAAAA,KAAAA,EAArBtD,EAAmCyD,QAAAA,AAAQ,EAAE,CACtDD,GAAa,EAAA,GAHbF,EAAcxD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASmC,MAAM,EAAA,CAAA,EAAMhC,EACnCuD,GAAa,GAKf,IAAME,EAASrC,IAQf,OANA4B,EAAQU,UAAU,CAAG,CACnB,iBAAkBT,EAClB,iBAAkBJ,EAClB,GAAGG,EAAQU,UAAU,AACvB,EAEO7D,EAAQ8C,IAAI,CAACU,EAAYM,QAAQ,CAAC1C,EAAewC,GAAS,IAC/D,IAAI,CAAC7B,iBAAiB,GAAGgC,eAAe,CACtCX,EACAD,EACCzC,AAAD,IACE,IAAMsD,EACJ,gBAAiBC,YAAc,YAAaC,YACxCD,WAAWC,WAAW,CAACC,GAAG,QAC1BC,EAEAC,EAAY,KAChBnD,EAAwBoD,MAAM,CAACV,GAE7BI,GACArE,QAAQC,GAAG,CAAC2E,4BAA4B,EACxC/F,EAAAA,gBAAgB,CAAC6E,QAAQ,CAACL,GAAS,KACnC,AACAkB,YAAYM,OAAO,CACjB,CAAA,EAAG7E,QAAQC,GAAG,CAAC2E,4BAA4B,CAAC,MAAM,EAChDvB,CAAAA,EAAKyB,KAAK,CAAC,KAAKC,GAAG,IAAM,EAAA,CAAC,CAC1BC,OAAO,CACP,SACA,AAACC,GAAkB,IAAMA,EAAMC,WAAW,IAAA,CACzC,CACH,CACEC,MAAOd,EACP/C,IAAKiD,YAAYC,GAAG,EACtB,EAGN,EAEIT,GACFxC,EAAwBO,GAAG,CACzBmC,EACA,CAHY,GAGRzC,IACF4D,OAAO3C,OAAO,CAACe,EAAQU,UAAU,EAAI,CAAC,KAO5C,GAAI,CACF,GAAIpB,EAAGuC,MAAM,CAAG,EACd,CADiB,MACVvC,EAAG/B,EAAM,AAACX,GAAQU,EAAmBC,EAAMX,IAGpD,IAAMQ,EAASkC,EAAG/B,GAClB,GAAIxB,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACqB,GAEb,MAFsB,CAEfA,EACJnB,IAAI,CAAC,AAAC6F,IACLvE,EAAKO,GAAG,GAGDgE,IAERC,KAAK,CAAC,AAACnF,IAEN,MADAU,EAAmBC,EAAMX,GACnBA,CACR,GACCoF,OAAO,CAACd,GAMb,OAJE3D,EAAKO,GAAG,GACRoD,IAGK9D,CACT,CAAE,MAAOR,EAAU,CAGjB,MAFAU,EAAmBC,EAAMX,GACzBsE,IACMtE,CACR,CACF,GAGN,CAaOqF,KAAK,GAAGrC,CAAgB,CAAE,CAC/B,IAAMsC,EAAS,IAAI,CACb,CAACC,EAAMnC,EAASV,EAAG,CACP,IAAhBM,EAAKiC,MAAM,CAASjC,EAAO,CAACA,CAAI,CAAC,EAAE,CAAE,CAAC,EAAGA,CAAI,CAAC,EAAE,CAAC,QAEnD,AACE,AAACnE,EAAAA,wBAAwB,CAACyE,QAAQ,CAACiC,IACD,KAClC,CADA3F,QAAQC,GAAG,CAAC0D,iBAAiB,CAKxB,WACL,IAAIiC,EAAapC,EACS,YAAtB,OAAOoC,GAA2C,YAAd,AAA0B,OAAnB9C,IAC7C8C,EAAaA,EAAWC,KAAK,CAAC,IAAI,CAAEC,UAAAA,EAGtC,IAAMC,EAAYD,UAAUT,MAAM,CAAG,EAC/BW,EAAKF,SAAS,CAACC,EAAU,CAE/B,GAAkB,YAAd,OAAOC,EAWT,OAAON,EAAOnF,KAAK,CAACoF,EAAMC,EAAY,IAAM9C,EAAG+C,KAAK,CAAC,IAAI,CAAEC,WAX/B,EAC5B,IAAMG,EAAeP,EAAOrD,UAAU,GAAG6D,IAAI,CAAC7F,EAAQmC,MAAM,GAAIwD,GAChE,OAAON,EAAOnF,KAAK,CAACoF,EAAMC,EAAY,CAACO,EAAOC,KAC5CN,SAAS,CAACC,EAAU,CAAG,SAAU3F,CAAQ,EAEvC,OADAgG,MAAAA,CAAAA,EAAAA,EAAOhG,CAAPgG,EACOH,EAAaJ,KAAK,CAAC,IAAI,CAAEC,UAClC,EAEOhD,EAAG+C,KAAK,CAAC,IAAI,CAAEC,YAE1B,CAGF,EAzBShD,CA0BX,CAIOuD,EARI,QAQM,GAAGjD,CAAgB,CAAQ,CAC1C,GAAM,CAACC,EAAMG,EAAQ,CAA4CJ,EAE3DS,EAAc,IAAI,CAACb,cAAc,CACrCQ,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASM,UAAU,AAAVA,GAAc,IAAI,CAACnB,kBAAkB,IAEhD,OAAO,IAAI,CAACP,iBAAiB,GAAGiE,SAAS,CAAChD,EAAMG,EAASK,EAC3D,CAEQb,eAAec,CAAiB,CAAE,CAKxC,OAJoBA,AAIbD,EAHHtD,EAAM+F,OAAO,CAACjG,EAAQmC,MAAM,GAAIsB,QAChCW,CAGN,CAEO8B,uBAAwB,CAC7B,IAAMtC,EAAS5D,EAAQmC,MAAM,GAAGgE,QAAQ,CAAC/E,GACzC,OAAOF,EAAwBkF,GAAG,CAACxC,EACrC,CAEOyC,qBAAqB1E,CAAmB,CAAEC,CAAqB,CAAE,CACtE,IAAMgC,EAAS5D,EAAQmC,MAAM,GAAGgE,QAAQ,CAAC/E,GACnCyC,EAAa3C,EAAwBkF,GAAG,CAACxC,GAC3CC,GACFA,EAAWpC,GAAG,CAACE,EAAKC,CADN,CAGlB,CACF,CAEA,IAAMpC,EAAa,MACjB,IAAM6F,EAAS,IAAIvD,EAEnB,MAAO,IAAMuD,EACf,CAAA,wDC7cC,sFACYiB,kBAAAA,qCAAAA,IAAN,OAAMA,EAKXjG,aAAc,CACZ,IAAIkG,EACAC,EAGJ,IAAI,CAACrH,OAAO,CAAG,IAAIsH,QAAW,CAACxB,EAAKyB,KAClCH,EAAUtB,EACVuB,EAASE,CACX,GAIA,IAAI,CAACH,OAAO,CAAGA,EACf,IAAI,CAACC,MAAM,CAAGA,CAChB,CACF,6IC1BaG,eAAAA,qCAAAA,KAAN,IAAMA,EAAe,CAE1BC,QAAS,CAEPC,KAAM,IAAIC,WAAW,CAAC,GAAI,IAAK,IAAK,IAAK,IAAI,EAE7CC,KAAM,IAAID,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAI,CAC9C,EACAE,OAAQ,CAENC,KAAM,IAAIH,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,GAAI,IAAK,GAAG,EAEpDC,KAAM,IAAID,WAAW,CAAC,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAG,EAEpDD,KAAM,IAAIC,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAAG,EAErDI,cAAe,IAAIJ,WAAW,CAC5B,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAC5D,CACH,CACF,qDClBC,qEACeK,iBAAiB,CAAA,kBAAjBA,GA2BAC,uBAAuB,CAAA,kBAAvBA,GAiBAC,oBAAoB,CAAA,kBAApBA,uEA5CT,SAASF,EAAkBG,CAAa,CAAEC,CAAa,EAC5D,GAAiB,IAAbA,EAAEvC,MAAM,CAAQ,OAAO,EAC3B,GAAiB,IAAbsC,EAAEtC,MAAM,EAAUuC,EAAEvC,MAAM,CAAGsC,EAAEtC,MAAM,CAAE,OAAO,CAAC,EAGnD,IAAK,IAAIwC,EAAI,EAAGA,GAAKF,EAAEtC,MAAM,CAAGuC,EAAEvC,MAAM,CAAEwC,IAAK,CAC7C,IAAIC,GAAgB,EAEpB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAEvC,MAAM,CAAE0C,IAAK,AAEjC,GAAIJ,CAAC,CAACE,EAAIE,EAAE,GAAKH,CAAC,CAACG,EAAE,CAAE,CACrBD,GAAgB,EAChB,KACF,CAGF,GAAIA,EACF,OAAOD,CAEX,CAEA,IALqB,GAKd,CAAC,CACV,CAKO,SAASJ,EAAwBE,CAAa,CAAEC,CAAa,EAClE,GAAID,EAAEtC,MAAM,GAAKuC,EAAEvC,MAAM,CAAE,OAAO,EAElC,IAAK,IAAIwC,EAAI,EAAGA,EAAIF,EAAEtC,MAAM,CAAEwC,IAAK,AACjC,GAAIF,CAAC,CAACE,EAAE,GAAKD,CAAC,CAACC,EAAE,CAAE,OAAO,EAG5B,OAAO,CACT,CASO,SAASH,EAAqBC,CAAa,CAAEC,CAAa,EAC/D,IAAMI,EAAWR,EAAkBG,EAAGC,GACtC,GAAiB,IAAbI,EAAgB,OAAOL,EAAEM,QAAQ,CAACL,EAAEvC,MAAM,EAC9C,KAAI2C,EAAW,EAAC,EAMd,OAAOL,CANU,EACjB,IAAMO,EAAU,IAAIf,WAAWQ,EAAEtC,MAAM,CAAGuC,EAAEvC,MAAM,EAGlD,OAFA6C,EAAQpG,GAAG,CAAC6F,EAAEQ,KAAK,CAAC,EAAGH,IACvBE,EAAQpG,GAAG,CAAC6F,EAAEQ,KAAK,CAACH,EAAWJ,EAAEvC,MAAM,EAAG2C,GACnCE,CACT,CAGF,MAHS,sICvDIE,0BAAAA,qCAAAA,KAAN,IAAMA,EAA0B,0WC2BvBC,YAAY,CAAA,kBAAZA,GAknBMC,yBAAyB,CAAA,kBAAzBA,GA1DAC,wBAAwB,CAAA,kBAAxBA,GArDAC,kBAAkB,CAAA,kBAAlBA,GAgFAC,uBAAuB,CAAA,kBAAvBA,GAnfNC,6BAA6B,CAAA,kBAA7BA,GA2iBAC,2BAA2B,CAAA,kBAA3BA,GApNAC,+BAA+B,CAAA,kBAA/BA,GA7RAC,yBAAyB,CAAA,kBAAzBA,GAzGAC,gBAAgB,CAAA,kBAAhBA,GATAC,gBAAgB,CAAA,kBAAhBA,GAkBMC,cAAc,CAAA,kBAAdA,GAkBAC,cAAc,CAAA,kBAAdA,+EAvGI,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,QACkB,CAAA,CAAA,IAAA,QACrB,CAAA,CAAA,IAAA,QAKtB,CAAA,CAAA,IAAA,OACiC,CAAA,CAAA,IAAA,IAExC,SAASC,IAIT,CASA,IAAMC,EAAU,IAAIC,YAEb,SAASf,EACd,GAAGgB,CAA4B,EAI/B,GAAuB,AAAnBA,GAAsB,GAAdhE,MAAM,CAChB,MAAM,OAAA,cAAiE,CAAjE,AAAI5E,MAAM,wDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgE,GAIxE,GAAI4I,AAAmB,GAAG,GAAdhE,MAAM,CAChB,OAAOgE,CAAO,CAAC,EAAE,CAGnB,GAAM,UAAEC,CAAQ,CAAEC,UAAQ,CAAE,CAAG,IAAIC,gBAI/BhK,EAAU6J,CAAO,CAAC,EAAE,CAACI,MAAM,CAACF,EAAU,CAAEG,cAAc,CAAK,GAE3D7B,EAAI,EACR,KAAOA,EAAIwB,EAAQhE,MAAM,CAAG,EAAGwC,IAAK,CAClC,IAAM8B,EAAaN,CAAO,CAACxB,EAAE,CAC7BrI,EAAUA,EAAQC,IAAI,CAAC,IACrBkK,EAAWF,MAAM,CAACF,EAAU,CAAEG,cAAc,CAAK,GAErD,CAIA,IAAME,EAAaP,CAAO,CAACxB,EAAE,CAO7B,MAFArI,CAJAA,EAAUA,EAAQC,IAAI,CAAC,IAAMmK,EAAWH,MAAM,CAACF,GAAAA,EAIvChE,KAAK,CAAC2D,GAEPI,CACT,CAEO,SAASP,EAAiBc,CAAW,EAC1C,OAAO,IAAIC,eAAe,CACxB3E,MAAM4E,CAAU,EACdA,EAAWC,OAAO,CAACb,EAAQc,MAAM,CAACJ,IAClCE,EAAWG,KAAK,EAClB,CACF,EACF,CAEO,SAASpB,EAAiBqB,CAAa,EAC5C,OAAO,IAAIL,eAAe,CACxB3E,MAAM4E,CAAU,EACdA,EAAWC,OAAO,CAACG,GACnBJ,EAAWG,KAAK,EAClB,CACF,EACF,CAEO,eAAelB,EACpBoB,CAAkC,EAElC,IAAMC,EAASD,EAAOE,SAAS,GACzBC,EAAuB,EAAE,CAE/B,MAAO,CAAM,CACX,GAAM,CAAEnE,MAAI,OAAEnE,CAAK,CAAE,CAAG,MAAMoI,EAAOG,IAAI,GACzC,GAAIpE,EACF,IADQ,EAIVmE,EAAOrI,IAAI,CAACD,EACd,CAEA,OAAOwI,OAAOC,MAAM,CAACH,EACvB,CAEO,eAAetB,EACpBmB,CAAkC,CAClCO,CAAoB,EAEpB,IAAMC,EAAU,IAAIC,YAAY,QAAS,CAAEC,OAAO,CAAK,GACnDC,EAAS,GAEb,UAAW,IAAMZ,KAASC,EAAQ,CAChC,GAAIO,QAAAA,KAAAA,EAAAA,EAAQK,OAAO,CACjB,CADmB,MACZD,EAGTA,GAAUH,EAAQK,MAAM,CAACd,EAAO,CAAEC,QAAQ,CAAK,EACjD,CAIA,OAAOW,AAFPA,EAAUH,EAAQK,MAAM,EAG1B,CAEO,SAASvC,IAId,IAEI0C,EAFAF,EAAoC,EAAE,CACtCC,EAA2B,EAGzBE,EAAQ,AAACtB,IAEb,GAAIqB,EAAS,OAEb,IAAME,EAAW,IAAI3E,EAAAA,eAAe,CACpCyE,EAAUE,EAEVC,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC,KAChB,GAAI,CACF,IAAMpB,EAAQ,IAAIhD,WAAWgE,GACzBK,EAAc,EAElB,IAAK,IAAI3D,EAAI,EAAGA,EAAIqD,EAAe7F,MAAM,CAAEwC,IAAK,CAC9C,IAAM4D,EAAgBP,CAAc,CAACrD,EAAE,CACvCsC,EAAMrI,GAAG,CAAC2J,EAAeD,GACzBA,GAAeC,EAAcC,UAAU,AACzC,CAGAR,EAAe7F,MAAM,CAAG,EACxB8F,EAAmB,EACnBpB,EAAWC,OAAO,CAACG,EACrB,CAAE,KAAM,CAIR,QAAU,CACRiB,OAAU3G,EACV6G,EAAS1E,OAAO,EAClB,CACF,EACF,EAEA,OAAO,IAAI4C,gBAAgB,CACzBmC,UAAUxB,CAAK,CAAEJ,CAAU,EAEzBmB,EAAehJ,IAAI,CAACiI,GACpBgB,GAAoBhB,EAAMuB,UAAU,CAGpCL,EAAMtB,EACR,EACAsB,QACE,GAAKD,CAAD,CAEJ,OAFc,AAEPA,EAAQ5L,OAAO,AACxB,CACF,EACF,CAEO,SAASqJ,EAA0B,gBACxC+C,CAAc,SACdC,CAAO,eACPC,CAAa,CAKd,EACC,MAAOjM,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,IAAGU,KAAK,CAAC9B,EAAAA,aAAa,CAACsN,sBAAsB,CAAE,SAC7DH,EAAeG,sBAAsB,CAACF,EAASC,GAEnD,CAEA,SAASE,EACPC,CAA6B,EAE7B,IAAIC,EAAW,GAIXC,EAAW,GAEf,OAAO,IAAI3C,gBAAgB,CACzB,MAAMmC,UAAUxB,CAAK,CAAEJ,CAAU,EAC/BoC,GAAW,EAEX,IAAMC,EAAY,MAAMH,IACxB,GAAIC,EAAU,CACZ,GAAIE,EAAW,CACb,IAAMC,EAAmBlD,EAAQc,MAAM,CAACmC,GACxCrC,EAAWC,OAAO,CAACqC,EACrB,CACAtC,EAAWC,OAAO,CAACG,EACrB,KAAO,CAEL,IAAMmC,EAAQ9E,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC2C,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACC,IAAI,EAG/D,GAAc,CAAC,IAAXgF,EAAc,CAChB,GAAIF,EAAW,CACb,IAAMC,EAAmBlD,EAAQc,MAAM,CAACmC,GAMlCG,EAAsB,IAAIpF,WAC9BgD,EAAM9E,MAAM,CAAGgH,EAAiBhH,MAAM,EAGxCkH,EAAoBzK,GAAG,CAACqI,EAAMhC,KAAK,CAAC,EAAGmE,IAEvCC,EAAoBzK,GAAG,CAACuK,EAAkBC,GAE1CC,EAAoBzK,GAAG,CACrBqI,EAAMhC,KAAK,CAACmE,GACZA,EAAQD,EAAiBhH,MAAM,EAEjC0E,EAAWC,OAAO,CAACuC,EACrB,MACExC,CADK,CACMC,OAAO,CAACG,GAErB+B,GAAW,CACb,MAOME,CAPC,EAQHrC,EAAWC,MADE,CACK,CAACb,EAAQc,MAAM,CAACmC,IAEpCrC,EAAWC,OAAO,CAACG,GACnB+B,GAAW,CAEf,CACF,EACA,MAAMb,MAAMtB,CAAU,EAEpB,GAAIoC,EAAU,CACZ,IAAMC,EAAY,MAAMH,IACpBG,GACFrC,EAAWC,MADE,CACK,CAACb,EAAQc,MAAM,CAACmC,GAEtC,CACF,CACF,EACF,CAmDA,SAASO,EACPvC,CAAkC,EAElC,IAAIwC,EAA6B,KAC7BC,GAAc,EAElB,eAAeC,EAAa/C,CAA4C,EACtE,GAAI6C,EACF,IADQ,GAIV,IAAMvC,EAASD,EAAOE,SAAS,EAY/B,OAAMyC,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,IAEpB,GAAI,CACF,MAAO,CAAM,CACX,GAAM,MAAE3G,CAAI,CAAEnE,OAAK,CAAE,CAAG,MAAMoI,EAAOG,IAAI,GACzC,GAAIpE,EAAM,CACRyG,GAAc,EACd,MACF,CAEA9C,EAAWC,OAAO,CAAC/H,EACrB,CACF,CAAE,MAAO7B,EAAK,CACZ2J,EAAWlJ,KAAK,CAACT,EACnB,CACF,CAEA,OAAO,IAAIoJ,gBAAgB,CACzBmC,UAAUxB,CAAK,CAAEJ,CAAU,EACzBA,EAAWC,OAAO,CAACG,GAGf,AAACyC,IACHA,EADS,AACFE,EAAa/C,EAAAA,CAExB,EACAsB,MAAMtB,CAAU,EACd,IAAI8C,EAGJ,OAAOD,GAAQE,CAHE,CAGW/C,EAC9B,CACF,EACF,CAEA,IAAMiD,EAAY,iBAOlB,SAASC,IACP,IAAIC,GAAc,EAElB,OAAO,IAAI1D,gBAAgB,CACzBmC,UAAUxB,CAAK,CAAEJ,CAAU,EACzB,GAAImD,EACF,OAAOnD,EAAWC,EADH,KACU,CAACG,GAG5B,IAAMmC,EAAQ9E,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC2C,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACE,aAAa,EACxE,GAAI+E,EAAQ,CAAC,EAAG,CAKd,GAJAY,GAAc,EAIV/C,EAAM9E,MAAM,GAAK2B,EAAAA,YAAY,CAACK,MAAM,CAACE,aAAa,CAAClC,MAAM,CAC3D,CAD6D,MAK/D,IAAM8H,EAAShD,EAAMhC,KAAK,CAAC,EAAGmE,GAK9B,GAJAvC,EAAWC,OAAO,CAACmD,GAIfhD,EAAM9E,MAAM,CAAG2B,EAAAA,YAAY,CAACK,MAAM,CAACE,aAAa,CAAClC,MAAM,CAAGiH,EAAO,CAEnE,IAAMc,EAAQjD,EAAMhC,KAAK,CACvBmE,EAAQtF,EAAAA,YAAY,CAACK,MAAM,CAACE,aAAa,CAAClC,MAAM,EAElD0E,EAAWC,OAAO,CAACoD,EACrB,CACF,MACErD,CADK,CACMC,OAAO,CAACG,EAEvB,EACAkB,MAAMtB,CAAU,EAGdA,EAAWC,OAAO,CAAChD,EAAAA,YAAY,CAACK,MAAM,CAACE,aAAa,CACtD,CACF,EACF,CAsCO,SAASqB,IAId,IAAI0E,GAAY,EACZC,GAAY,EAChB,OAAO,IAAI/D,gBAAgB,CACzB,MAAMmC,UAAUxB,CAAK,CAAEJ,CAAU,EAG7B,CAACuD,GACD9F,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC2C,EAAOnD,EAAAA,YAAY,CAACC,OAAO,CAACC,IAAI,EAAI,CAAC,GACvD,CACAoG,GAAY,CAAA,EAIZ,CAACC,GACD/F,CAAAA,EAAAA,EAAAA,iBAAiB,AAAjBA,EAAkB2C,EAAOnD,EAAAA,YAAY,CAACC,OAAO,CAACG,IAAI,EAAI,CAAC,GACvD,AACAmG,GAAY,EAAA,EAGdxD,EAAWC,OAAO,CAACG,EACrB,EACAkB,MAAMtB,CAAU,EACd,IAAMyD,EAAmC,EAAE,AACvC,CAACF,GAAWE,EAAYtL,IAAI,CAAC,QAC7B,AAACqL,GAAWC,EAAYtL,IAAI,CAAC,QAE5BsL,EAAYnI,MAAM,EAAE,AAEzB0E,EAAWC,OAAO,CAChBb,EAAQc,MAAM,CACZ,CAAC;;+CAEoC,EAAEuD,EAChCC,GAAG,CAAC,AAACC,GAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,EAAYnI,MAAM,CAAG,EAAI,QAAU,IACnC;AAAA;sCACoB,EAAE+C,EAAAA,uBAAuB,CAAC;;;UAGtD,CAAC,EAGP,CACF,EACF,CA2BO,eAAeI,EACpBwF,CAAiC,CACjC,QACEvB,CAAM,mBACNwB,CAAiB,CACjBC,oBAAkB,uBAClBC,CAAqB,2BACrBC,CAAyB,oBACzBC,CAAkB,CACI,EAGxB,IAAMC,EAAiB7B,EAASA,EAAO3H,KAAK,CAACkI,EAAW,EAAE,CAAC,EAAE,CAAG,KAI5DkB,GAAsB,aAAcF,GACtC,MAAMA,EAAaO,GADiC,KACzB,KAxC7BV,EA2CuC,CAErCnF,IAGAsD,EAAmCoC,GAhDoB,AAmDvDE,AAAkB,SAAQA,EAAejJ,MAAM,CAAG,EAC9CmH,AAzSR,SAASA,AACPC,CAAc,EAEd,IACIrB,EADAsB,GAAU,EAGRrB,EAAQ,AAACtB,IACb,IAAMuB,EAAW,IAAI3E,EAAAA,eAAe,CACpCyE,EAAUE,EAEVC,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC,KAChB,GAAI,CACFxB,EAAWC,OAAO,CAACb,EAAQc,MAAM,CAACwC,GACpC,CAAE,KAAM,CAIR,QAAU,CACRrB,OAAU3G,EACV6G,EAAS1E,OAAO,EAClB,CACF,EACF,EAEA,OAAO,IAAI4C,gBAAgB,CACzBmC,UAAUxB,CAAK,CAAEJ,CAAU,EACzBA,EAAWC,OAAO,CAACG,GAGfuC,IAGJA,GAAU,EAHG,AAIbrB,EAAMtB,GACR,EACAsB,MAAMtB,CAAU,EACd,GAAIqB,EAAS,OAAOA,EAAQ5L,OAAO,CAC/BkN,GAGJ3C,EAAWC,IAHE,GAGK,CAACb,EAAQc,MAAM,CAACwC,GACpC,CACF,EACF,EA8PmC6B,GAC3B,KAGJL,EAAoBtB,EAA4BsB,GAAqB,KAGrEI,EAAqBzF,IAAoC,KAGzDqE,IAKAjB,EAAmCmC,GACpC,CAlED,IAAI/D,EAyCqB4D,EAxCzB,IAAK,CADQ1E,GACFwE,KAAeD,EACnBC,IAEL1D,EAASA,EAAO2D,GAHsB,EACpB,MAES,CAACD,EAAAA,EAE9B,OAAO1D,CA6DT,CAOO,eAAe7B,EACpBiG,CAA2C,CAC3C,CACEL,uBAAqB,2BACrBC,CAAyB,CACO,EAElC,OACEI,EAEGT,WAAW,CAACrF,EADb,GAECqF,WAAW,CAACV,AA5KV,IAAI7D,gBAAgB,CACzBmC,UAAUxB,CAAK,CAAEJ,CAAU,EAOvBtC,EAkKmD,CAlKnDA,EAAAA,EAqKF,qBArKyB,AAAvBA,EAAwB0C,EAAOnD,EAAAA,KAqKD,OArKa,CAACK,MAAM,CAACE,aAAa,GAChEE,CAAAA,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC0C,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACD,IAAI,GACvDK,CAAAA,EAAAA,EAAAA,uBAAuB,AAAvBA,EAAwB0C,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACH,IAAI,GACvD,CAQFiD,EAAQzC,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACyC,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACD,IAAI,EAC5D+C,EAAQzC,CAAAA,EAAAA,EAAAA,oBAAoB,AAApBA,EAAqByC,EAAOnD,EAAAA,YAAY,CAACK,MAAM,CAACH,IAAI,EAE5D6C,EAAWC,OAAO,CAACG,GACrB,CACF,IAsJK4D,WAAW,CAAC/B,EAAmCmC,IAE/CJ,WAAW,CACV/B,EAAmCoC,GAG3C,CAQO,EAbD,aAagB3F,EACpB+F,CAA2C,CAC3C,WAfgC,QAgB9BP,CAAiB,uBACjBE,CAAqB,2BACrBC,CAAyB,CACM,EAEjC,OACEI,EAEGT,WAAW,CAACrF,EADb,GAGCqF,WAAW,CAAC/B,EAAmCmC,IAE/CJ,UAHD,CAGY,CACV/B,EAAmCoC,IAGpCL,EALD,SAKY,CAACpB,EAA4BsB,AATY,IAWpDF,MAHD,AANgC,KASpB,CAACd,IADb,AAGN,CAQO,GAjBmC,YAiBpB3E,EACpB0F,CAAwC,CACxC,mBACEC,CAAiB,UAdmC,aAepDE,CAAqB,KAjB4D,sBAkBjFC,CAAyB,CACH,EAExB,OACEJ,EAEGD,WADD,AACY,CAACrF,KAEZqF,WAAW,CAAC/B,EAAmCmC,IAE/CJ,UAHD,CAGY,CACV/B,EAAmCoC,IAGpCL,EALD,SAKY,AATyC,CASxCpB,EAA4BsB,IAExCF,MAHD,AANgC,KASpB,CAACd,IADb,AAGN,CAEO,GAXmC,MAW1BtE,IACd,OAAOI,EAAiBiE,EAC1B,yBAPwD,mBAF6B,iGC/pBxEyB,iBAAAA,qCAAAA,IAAN,OAAMA,UAAuBhO,MAClCC,YAAYW,CAAe,CAAEmC,CAAsB,CAAE,CACnD,KAAK,CACF,eAAanC,CAAAA,CAAQqN,QAAQ,CAAC,KAAOrN,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DmC,GAEF,IAAI,CAACmC,IAAI,CAAG,gBACd,CACF,sDCLC,aACM,SAASgJ,EAAmBC,CAAY,EAC7C,OAAOA,EAAKC,UAAU,CAAC,KAAOD,EAAQ,IAAGA,CAC3C,0EAFgBD,qBAAAA,qCAAAA,8HCwBHG,mBAAmB,CAAA,kBAAnBA,GADAC,gBAAgB,CAAA,kBAAhBA,GAhBGC,4BAA4B,CAAA,kBAA5BA,GATAC,cAAc,CAAA,kBAAdA,GAKAC,sBAAsB,CAAA,kBAAtBA,uEALT,SAASD,EAAeE,CAAe,EAE5C,MAAOA,AAAe,OAAR,CAAC,EAAE,EAAYA,EAAQT,QAAQ,CAAC,IAChD,CAEO,SAASQ,EAAuBC,CAAe,EACpD,OAAOA,EAAQN,UAAU,CAAC,MAAQM,AAAY,eAChD,CAEO,SAASH,EACdG,CAAgB,CAChBC,CAA2D,EAI3D,GAFsBD,CAElBE,CAF0B3L,QAAQ,CAACqL,GAEpB,CACjB,IAAMO,EAAmBC,KAAKC,SAAS,CAACJ,GACxC,MAA4B,OAArBE,EACHP,EAAmB,IAAMO,EACzBP,CACN,CAEA,OAAOI,CACT,CAEO,IAAMJ,EAAmB,WACnBD,EAAsB,wICNnBW,gBAAgB,CAAA,kBAAhBA,GAmCAC,eAAe,CAAA,kBAAfA,+EAzDmB,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,IAqBxB,SAASD,EAAiBE,CAAa,EAC5C,MAAOhB,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBgB,EAAM7K,KAAK,CAAC,KAAK8K,MAAM,CAAC,CAACC,EAAUV,EAAS7C,EAAOwD,IAEjD,AAAI,CAACX,GAKDF,CAAAA,EAAAA,EAAAA,CALU,aAKI,AAAdA,EAAeE,IAKA,KAAK,CALK,AAKzBA,CAAO,CAAC,EAAE,EAMXA,CAAY,YAAsB,UAAZA,CAAY,CAAM,EACzC7C,IAAUwD,EAASzK,MAAM,CAAG,EAhBrBwK,CAiBP,CAIQA,EAAS,IAAGV,EACrB,IAEP,CAMO,SAASO,EAAgBK,CAAW,EACzC,OAAOA,EAAI/K,OAAO,CAChB,cAEA,KAEJ,yBAHkC,+CClD9BwL,0DAEYR,mBAAmB,CAAA,kBAAnBA,GA0CAC,OAAO,CAAA,kBAAPA,GAXAC,OAAO,CAAA,kBAAPA,GA6HMC,sBAAsB,CAAA,kBAAtBA,GAxCNC,gCAAgC,CAAA,kBAAhCA,GApBAC,kBAAkB,CAAA,kBAAlBA,GAnCAC,8BAA8B,CAAA,kBAA9BA,GAzCAC,kBAAkB,CAAA,kBAAlBA,+EA1Be,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,GAI1B,SAASP,EACdS,CAAiD,EAEjD,IAAMC,EAAQ,IAAIvJ,WAAWsJ,GACvBE,EAAMD,EAAMhF,UAAU,CAK5B,GAAIiF,EAAM,MACR,CADe,MACRC,OAAOC,YAAY,CAAChL,KAAK,CAAC,KAAM6K,GAGzC,IAAII,EAAS,GACb,IAAK,IAAIjJ,EAAI,EAAGA,EAAI8I,EAAK9I,IAAK,AAC5BiJ,GAAUF,OAAOC,YAAY,CAACH,CAAK,CAAC7I,EAAE,EAExC,OAAOiJ,CACT,CAEO,SAASP,EAAmBO,CAAc,EAC/C,IAAMH,EAAMG,EAAOzL,MAAM,CACnB0L,EAAM,IAAI5J,WAAWwJ,GAE3B,IAAK,IAAI9I,EAAI,EAAGA,EAAI8I,EAAK9I,IAAK,AAC5BkJ,CAAG,CAAClJ,EAAE,CAAGiJ,EAAOE,UAAU,CAACnJ,GAG7B,OAAOkJ,CACT,CAEO,SAASb,EAAQlO,CAAc,CAAEiP,CAAc,CAAEC,CAAgB,EACtE,OAAOC,OAAOC,MAAM,CAAClB,OAAO,CAC1B,CACEvK,KAAM,aACNsL,CACF,EACAjP,EACAkP,EAEJ,CAEO,SAASjB,EAAQjO,CAAc,CAAEiP,CAAc,CAAEC,CAAgB,EACtE,OAAOC,OAAOC,MAAM,CAACnB,OAAO,CAC1B,CACEtK,KAAM,UACNsL,IACF,EACAjP,EACAkP,EAEJ,CAMA,IAAMG,EAAoCC,OAAOC,GAAG,CAClD,gCAGK,SAASjB,EAA+B,MAC7CkB,CAAI,yBACJC,CAAuB,CACvBC,uBAAqB,iBACrBC,CAAe,CAYhB,MAEyCrN,EAAxC,IAAMsN,EAAAA,AAEL,OAFuCtN,EAAAA,UAAU,CAChD+M,EAAAA,AACD,EAAA,KAAA,EAFuC/M,EAErCsN,+BAA+B,CAKlCtN,UAAU,CAAC+M,EAAkC,CAAG,CAC9CO,gCAAiC,CAC/B,GAAGA,CAA+B,CAClC,CAACnC,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAAC+B,GAAM,CAAEC,CAC5B,wBACAC,kBACAC,CACF,CACF,CAEO,SAAStB,IACd,IAAMwB,EAAkCvN,UAAkB,CACxD+M,EACD,CAUD,GAAI,CAACQ,EACH,MAAM,OAAA,cAA0D,CAA1D,EAD6B,EACzBpD,EAAAA,cAAc,CAAC,wCAAnB,oBAAA,OAAA,mBAAA,gBAAA,CAAyD,GAGjE,OAAOoD,EAA+BF,eAAe,AACvD,CAEO,SAASvB,IACd,IAAMyB,EAAkCvN,UAAkB,CACxD+M,EACD,CAMD,GAAI,CAACQ,EACH,MAAM,OAAA,cAA0D,CAA1D,EAD6B,EACzBpD,EAAAA,cAAc,CAAC,wCAAnB,oBAAA,OAAA,mBAAA,gBAAA,CAAyD,GAGjE,GAAM,iCAAEmD,CAA+B,CAAE,CAAGC,EACtCC,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GAE3C,GAAI,CAACF,EASIG,KAkDTL,EAlDuCA,EATvB,AA+DhB,IAAMW,EAA2BnN,OAAOoN,MAAM,CAC5CZ,GAGIa,EAA+D,CACnEC,CAPD,aAOgB,CAAC,EAChBC,qBAAsB,CAAC,EACvBC,iBAAkB,CAAC,CACrB,EAEA,IAAK,IAAMnB,KAA2Bc,EACpCE,EAA8BC,aAAa,CAAG,CAC5C,GAAGD,EAA8BC,CAF2B,YAEd,CAC9C,GAAGjB,EAAwBiB,aAAa,AAC1C,EACAD,EAA8BE,oBAAoB,CAAG,CACnD,GAAGF,EAA8BE,oBAAoB,CACrD,GAAGlB,EAAwBkB,oBAAoB,AACjD,EACAF,EAA8BG,gBAAgB,CAAG,CAC/C,GAAGH,EAA8BG,gBAAgB,CACjD,GAAGnB,EAAwBmB,gBAAgB,AAC7C,EAGF,OAAOH,CA/EgCb,CAGvC,IAAMH,EACJG,CAA+B,CAACE,EAAUnC,KAAK,CAAC,CAElD,GAAI,CAAC8B,EACH,MAAM,OAAA,UADsB,IAG3B,CAFK,IAAIhD,EAAAA,cAAc,CACtB,CAAC,sCAAsC,EAAEqD,EAAUnC,KAAK,CAAC,CAAC,CAAC,EADvD,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,OAAO8B,CACT,CAEO,eAAetB,IACpB,GAAIK,EACF,OAAOA,EAGT,IAAMqB,EAAkCvN,SAJV,CAI4B,CACxD+M,EACD,CAID,GAAI,CAACQ,EACH,MAAM,OAAA,cAA0D,CAA1D,EAD6B,EACzBpD,EAAAA,cAAc,CAAC,wCAAnB,oBAAA,OAAA,mBAAA,gBAAA,CAAyD,GAGjE,IAAMyD,EACJlS,QAAQC,GAAG,CAACkS,kCAAkC,EAC9CN,EAA+BH,qBAAqB,CAACU,aAAa,CAEpE,QAAe3N,IAAXyN,EACF,KADwB,CAClB,OAAA,cAA+D,CAA/D,IAAIzD,EAAAA,cAAc,CAAC,6CAAnB,oBAAA,OAAA,mBAAA,gBAAA,CAA8D,GAWtE,OAAO+B,AARPA,EAA2B,MAAMW,OAAOC,MAAM,CAACiB,SAAS,CACtD,MACA9B,EAAmB+B,KAAKJ,IACxB,UACA,GACA,CAAC,UAAW,UAAU,CAI1B,uDCxMoD,uEA+K9BW,sBAAsB,CAAA,kBAAtBA,GAvFTC,sBAAsB,CAAA,kBAAtBA,2FApF0B,CAAA,CAAA,IAAA,OAEE,CAAA,CAAA,IAAA,OAEV,CAAA,CAAA,IAAA,QASxB,CAAA,CAAA,IAAA,QAKA,CAAA,CAAA,IAAA,QACuC,CAAA,CAAA,IAAA,WAC5B,CAAA,CAAA,IAAA,iCAIZE,EAAc,IAAI5J,YAClB6J,EAAc,IAAIpI,YAKxB,eAAeqI,EAAqBC,CAAgB,CAAEC,CAAW,EAC/D,IAAMpR,EAAM,MAAMmO,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,IACxC,GAAI,KAAe,IAARnO,EACT,MAAM,CADwB,MACxB,cAEL,CAFK,AAAIvB,MACR,CAAC,kEAAkE,CAAC,EADhE,oBAAA,OAAA,kBAAA,gBAAA,CAEN,GAIF,IAAM4S,EAAkBf,KAAKc,GACvBE,EAAUD,EAAgBlL,KAAK,CAAC,EAAG,IACnCoL,EAAUF,EAAgBlL,KAAK,CAAC,IAEhCqL,EAAYP,EAAYhI,MAAM,CAClC,MAAMgF,GAAAA,EAAAA,OAAAA,AAAO,EAACjO,EAAKuO,CAAAA,EAAAA,EAAAA,kBAAkB,AAAlBA,EAAmB+C,GAAU/C,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACgD,KAGrE,GAAI,CAACC,EAAU3E,UAAU,CAACsE,GACxB,MAAM,EAD6B,KAC7B,cAA8D,CAA9D,AAAI1S,MAAM,qDAAV,oBAAA,OAAA,mBAAA,gBAAA,CAA6D,GAGrE,OAAO+S,EAAUrL,KAAK,CAACgL,EAAS9N,MAAM,CACxC,CAMA,eAAeoO,EAAqBN,CAAgB,CAAEC,CAAW,EAC/D,IAAMpR,EAAM,MAAMmO,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,IACxC,GAAInO,KAAQyC,MACV,KADqB,CACf,OAAA,cAEL,CAFK,AAAIhE,MACR,CAAC,kEAAkE,CAAC,EADhE,oBAAA,OAAA,kBAAA,gBAAA,CAEN,GAIF,IAAMiT,EAAc,IAAIvM,WAAW,IACnCwM,EAAAA,oBAAoB,CAACC,IAAI,CAAC,IAAMzC,OAAO0C,eAAe,CAACH,IACvD,IAAMJ,EAAUtD,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC0D,EAAYjD,MAAM,EAEhDqD,EAAY,MAAM5D,CAAAA,EAAAA,EAAAA,OAAO,AAAPA,EACtBlO,EACA0R,EACAV,EAAY/I,MAAM,CAACkJ,EAAWC,IAGhC,OAAOW,KAAKT,EAAUtD,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC8D,GAC5C,CAMO,IAAMhB,EAAyBkB,EAAAA,OAAK,CAACC,KAAK,CAC/C,eAAenB,EAAuBK,CAAgB,CAAE,GAAG/P,CAAW,EACpE,GAAM,eAAEsP,CAAa,CAAE,CAAGtC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,IAIpDvP,EAAQ,AAAIJ,QAClBA,MAAMyT,iBAAiB,CAACrT,EAAOiS,GAE/B,IAAIqB,GAAgB,EAEdC,EAAgBT,EAAAA,oBAAoB,CAAC3B,QAAQ,GAE7CqC,EACJD,CAAAA,QAAAA,KAAAA,EAAAA,EAAe/Q,IAAAA,AAAI,IAAK,YACpBiR,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,EAACF,QAC9B3P,EAGA8P,EAAa,MAAMtL,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EACrC8C,CAAAA,EAAAA,EAAAA,sBAAsB,AAAtBA,EAAuB3I,EAAMsP,EAAe,CAC1C/H,OAAQ0J,EACRG,QAAQpU,CAAG,GACLiU,MAAAA,CAAAA,GAAAA,EAAAA,AAAyBrJ,OAAAA,AAAO,EAAE,EAKlCmJ,IAIJA,EAAgB,GAIhBtT,EAAMQ,IARa,GAQN,CAAGjB,aAAeK,MAAQL,EAAIiB,OAAO,CAAGuP,OAAOxQ,IAC9D,CACF,GAIAiU,CAHA,AACA,EAKF,GAAIF,EAQF,MAAMtT,EAGR,GAAI,CAACuT,CAXc,CAYjB,OAAOX,EAAqBN,EAAUoB,EADpB,CAIpB,IAAMG,EAA2BC,CAAAA,EAAAA,EAAAA,uBArBsC,EACE,EAoBxCA,AAA2B,EAACP,GACvDQ,EAAwBC,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACT,GACjDU,EAAW3B,EAAWoB,EAEtBQ,EACJL,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAA0BM,kBAAkB,CAACvO,GAAG,CAACqO,EAAAA,CAAAA,EACjDF,CAAAA,EADiDE,MACjDF,KAAAA,EAAAA,EAAuBI,kBAAkB,CAACvO,GAAG,CAACqO,EAAAA,CAAAA,CAEhD,GAAIC,EACF,OAAOA,EAGT,IAAME,EAJe,AAKI,cAAvBb,EAAc/Q,IAAI,CAAmB+Q,EAAca,WAAW,MAAGxQ,CAEnEwQ,OAAAA,GAAAA,EAAAA,AAAaC,SAAS,GAEtB,IAAMpB,EAAY,MAAML,EAAqBN,EAAUoB,GAKvD,OAHAU,MAAAA,CAAAA,EAAAA,EAAaE,CAAbF,MAAoB,GACpBP,MAAAA,CAAAA,EAAAA,EAA0BM,CAA1BN,iBAA4C,CAAC5S,GAAG,CAACgT,EAAUhB,GAEpDA,CACT,GAIK,eAAejB,EACpBM,CAAgB,CAChBiC,CAAiC,EAEjC,IAGI5B,EAHEM,EAAY,MAAMsB,EAClBhB,EAAgBT,EAAAA,oBAAoB,CAAC3B,QAAQ,GAInD,GAAIoC,EAAe,CACjB,IAAMa,EACmB,cAAvBb,EAAc/Q,IAAI,CAAmB+Q,EAAca,WAAW,MAAGxQ,EAE7DiQ,EAA2BC,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACP,GACvDQ,EAAwBC,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACT,IAEvDZ,EACEkB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAA0BW,kBAAkB,CAAC5O,GAAG,CAACqN,EAAAA,CAAAA,GACjDc,EADiDd,MACjDc,KAAAA,EAAAA,EAAuBS,kBAAkB,CAAC5O,GAAG,CAACqN,EAAAA,CAAAA,IAG9CmB,MAAAA,CAAAA,EAAAA,EAAaC,CAAbD,QAAsB,GACtBzB,EAAY,MAAMN,EAAqBC,EAAUW,GACjDmB,MAAAA,CAAAA,EAAAA,EAAaE,CAAbF,MAAoB,GACpBP,MAAAA,CAAAA,EAAAA,EAA0BW,CAA1BX,iBAA4C,CAAC5S,GAAG,CAACgS,EAAWN,GAEhE,MACEA,CADK,CACO,MAAMN,EAAqBC,EAAUW,GAGnD,GAAM,CAAEnB,sBAAoB,kBAAEC,CAAgB,CAAE,CAC9CxC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,IAqClC,OAlCqB,AAkCdkF,MAlCoBC,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EACjD,IAAIzL,eAAe,CACjB3E,MAAM4E,CAAU,EACdA,EAAWC,OAAO,CAACgJ,EAAY/I,MAAM,CAACuJ,IAElCY,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAe/Q,IAAAA,AAAI,IAAK,YAGtB+Q,CAHmC,CAGrBoB,YAAY,CAACxK,OAAO,CACpCjB,CADsC,CAC3BG,KAAK,GAEhBkK,EAAcoB,YAAY,CAACC,gBAAgB,CACzC,QACA,IAAM1L,EAAWG,KAAK,GACtB,CAAEwL,MAAM,CAAK,GAIjB3L,EAAWG,KAAK,EAEpB,CACF,GACA,CACEyL,uBAAwB,CAItBC,cAAe,KACfC,UAAkDjD,CAAvCG,CACXpB,eAD2BgB,CACVtC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,GACrC,CACF,EAIJ,mECjPO,SAASyF,EAAyBC,CAAc,EACrD,IAAK,IAAIlO,EAAI,EAAGA,EAAIkO,EAAQ1Q,MAAM,CAAEwC,IAAK,CACvC,IAAMmO,EAASD,CAAO,CAAClO,EAAE,CACzB,GAAsB,YAAlB,AAA8B,OAAvBmO,EACT,MAAM,OAAA,cAEL,CAFK,AAAIvV,MACR,CAAC,2DAA2D,EAAE,OAAOuV,EAAO;AAAA,oEAAuE,CAAC,EADhJ,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEJ,CACF,0EATgBF,2BAAAA,qCAAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}