module.exports={769257:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({encodeIntoResult:()=>h,stegaEncodeSourceMap:()=>l,stegaEncodeSourceMap$1:()=>m});var d=a.i(249431);let b=/_key\s*==\s*['"](.*)['"]/;function e(a){if(!Array.isArray(a))throw Error("Path is not an array");return a.reduce((a,c,d)=>{let e=typeof c;if("number"===e)return`${a}[${c}]`;if("string"===e)return`${a}${0===d?"":"."}${c}`;if(("string"==typeof c?b.test(c.trim()):"object"==typeof c&&"_key"in c)&&c._key)return`${a}[_key=="${c._key}"]`;if(Array.isArray(c)){let[b,d]=c;return`${a}[${b}:${d}]`}throw Error(`Unsupported path segment \`${JSON.stringify(c)}\``)},"")}let c={"\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","'":"\\'","\\":"\\\\"},n={"\\f":"\f","\\n":`
`,"\\r":"\r","\\t":"	","\\'":"'","\\\\":"\\"};function f(a){let b,c=[],d=/\['(.*?)'\]|\[(\d+)\]|\[\?\(@\._key=='(.*?)'\)\]/g;for(;null!==(b=d.exec(a));){if(void 0!==b[1]){let a=b[1].replace(/\\(\\|f|n|r|t|')/g,a=>n[a]);c.push(a);continue}if(void 0!==b[2]){c.push(parseInt(b[2],10));continue}if(void 0!==b[3]){let a=b[3].replace(/\\(\\')/g,a=>n[a]);c.push({_key:a,_index:-1});continue}}return c}function g(a){return a.map(a=>{if("string"==typeof a||"number"==typeof a)return a;if(""!==a._key)return{_key:a._key};if(-1!==a._index)return a._index;throw Error(`invalid segment:${JSON.stringify(a)}`)})}function h(a,b,e){return function a(b,c,e=[]){if(null!==b&&Array.isArray(b))return b.map((b,f)=>{if((0,d.isRecord)(b)){let d=b._key;if("string"==typeof d)return a(b,c,e.concat({_key:d,_index:f}))}return a(b,c,e.concat(f))});if((0,d.isRecord)(b)){if("block"===b._type||"span"===b._type){let d={...b};return"block"===b._type?d.children=a(b.children,c,e.concat("children")):"span"===b._type&&(d.text=a(b.text,c,e.concat("text"))),d}return Object.fromEntries(Object.entries(b).map(([b,d])=>[b,a(d,c,e.concat(b))]))}return c(b,e)}(a,(a,d)=>{if("string"!=typeof a)return a;let g=function(a,b){var d;if(!b?.mappings)return;let e=(d=a.map(a=>{if("string"==typeof a||"number"==typeof a)return a;if(-1!==a._index)return a._index;throw Error(`invalid segment:${JSON.stringify(a)}`)}),`$${d.map(a=>"string"==typeof a?`['${a.replace(/[\f\n\r\t'\\]/g,a=>c[a])}']`:"number"==typeof a?`[${a}]`:""!==a._key?`[?(@._key=='${a._key.replace(/['\\]/g,a=>c[a])}')]`:`[${a._index}]`).join("")}`);if(void 0!==b.mappings[e])return{mapping:b.mappings[e],matchedPath:e,pathSuffix:""};let f=Object.entries(b.mappings).filter(([a])=>e.startsWith(a)).sort(([a],[b])=>b.length-a.length);if(0==f.length)return;let[g,h]=f[0],i=e.substring(g.length);return{mapping:h,matchedPath:g,pathSuffix:i}}(d,b);if(!g)return a;let{mapping:h,matchedPath:i}=g;if("value"!==h.type||"documentValue"!==h.source.type)return a;let j=b.documents[h.source.document],k=b.paths[h.source.path],l=f(i);return e({sourcePath:f(k).concat(d.slice(l.length)),sourceDocument:j,resultPath:d,value:a})})}let o="drafts.";function i(a){return a.startsWith(o)}function j(a){return a.startsWith("versions.")}let p=({sourcePath:a,resultPath:b,value:c})=>{if(function(a){return!!/^\d{4}-\d{2}-\d{2}/.test(a)&&!!Date.parse(a)}(c)||function(a){try{new URL(a,a.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(c))return!1;let d=a.at(-1);return!("slug"===a.at(-2)&&"current"===d||"string"==typeof d&&(d.startsWith("_")||d.endsWith("Id"))||a.some(a=>"meta"===a||"metadata"===a||"openGraph"===a||"seo"===a)||k(a)||k(b)||"string"==typeof d&&q.has(d))},q=new Set(["color","colour","currency","email","format","gid","hex","href","hsl","hsla","icon","id","index","key","language","layout","link","linkAction","locale","lqip","page","path","ref","rgb","rgba","route","secret","slug","status","tag","template","theme","type","textTheme","unit","url","username","variant","website"]);function k(a){return a.some(a=>"string"==typeof a&&null!==a.match(/type/i))}function l(a,c,f){let{filter:k,logger:l,enabled:m}=f;if(!m){let b="config.enabled must be true, don't call this function otherwise";throw l?.error?.(`[@sanity/client]: ${b}`,{result:a,resultSourceMap:c,config:f}),TypeError(b)}if(!c)return l?.error?.("[@sanity/client]: Missing Content Source Map from response body",{result:a,resultSourceMap:c,config:f}),a;if(!f.studioUrl){let b="config.studioUrl must be defined";throw l?.error?.(`[@sanity/client]: ${b}`,{result:a,resultSourceMap:c,config:f}),TypeError(b)}let n={encoded:[],skipped:[]},q=h(a,c,({sourcePath:a,sourceDocument:b,resultPath:c,value:h})=>{var m;let q;if(("function"==typeof k?k({sourcePath:a,resultPath:c,filterDefault:p,sourceDocument:b,value:h}):p({sourcePath:a,resultPath:c,value:h}))===!1)return l&&n.skipped.push({path:e(g(a)),value:`${h.slice(0,20)}${h.length>20?"...":""}`,length:h.length}),h;l&&n.encoded.push({path:e(g(a)),value:`${h.slice(0,20)}${h.length>20?"...":""}`,length:h.length});let{baseUrl:r,workspace:s,tool:t}=("/"!==(q="string"==typeof(m="function"==typeof f.studioUrl?f.studioUrl(b):f.studioUrl)?m:m.baseUrl)&&(q=q.replace(/\/$/,"")),"string"==typeof m?{baseUrl:q}:{...m,baseUrl:q});if(!r)return h;let{_id:u,_type:v,_projectId:w,_dataset:x}=b;return(0,d.C)(h,{origin:"sanity.io",href:function(a){let{baseUrl:b,workspace:c="default",tool:d="default",id:f,type:h,path:k,projectId:l,dataset:m}=a;if(!b)throw Error("baseUrl is required");if(!k)throw Error("path is required");if(!f)throw Error("id is required");if("/"!==b&&b.endsWith("/"))throw Error("baseUrl must not end with a slash");let n="default"===c?void 0:c,p="default"===d?void 0:d,q=j(f)?f.split(".").slice(2).join("."):i(f)?f.slice(o.length):f,r=Array.isArray(k)?e(g(k)):k,s=new URLSearchParams({baseUrl:b,id:q,type:h,path:r});if(n&&s.set("workspace",n),p&&s.set("tool",p),l&&s.set("projectId",l),m&&s.set("dataset",m),i(f)||j(f)){if(j(f)){let a=function(a){if(!j(a))return;let[b,c,...d]=a.split(".");return c}(f);s.set("perspective",a)}}else s.set("perspective","published");let t=["/"===b?"":b];n&&t.push(n);let u=["mode=presentation",`id=${q}`,`type=${h}`,`path=${encodeURIComponent(r)}`];return p&&u.push(`tool=${p}`),t.push("intent","edit",`${u.join(";")}?${s}`),t.join("/")}({baseUrl:r,workspace:s,tool:t,id:u,type:v,path:a,...!f.omitCrossDatasetReferenceData&&{dataset:x,projectId:w}})},!1)});if(l){let a=n.skipped.length,c=n.encoded.length;if((a||c)&&((l?.groupCollapsed||l.log)?.("[@sanity/client]: Encoding source map into result"),l.log?.(`[@sanity/client]: Paths encoded: ${n.encoded.length}, skipped: ${n.skipped.length}`)),n.encoded.length>0&&(l?.log?.("[@sanity/client]: Table of encoded paths"),(l?.table||l.log)?.(n.encoded)),n.skipped.length>0){let a=new Set;for(let{path:c}of n.skipped)a.add(c.replace(b,"0").replace(/\[\d+\]/g,"[]"));l?.log?.("[@sanity/client]: List of skipped paths",[...a.values()])}(a||c)&&l?.groupEnd?.()}return q}var m=Object.freeze({__proto__:null,stegaEncodeSourceMap:l})}}};

//# sourceMappingURL=node_modules_%40sanity_client_dist__chunks-es_stegaEncodeSourceMap_4241083d.js.map