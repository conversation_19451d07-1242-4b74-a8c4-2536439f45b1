module.exports={329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},249431:a=>{"use strict";var{g:b,__dirname:c}=a;{function d(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}a.s({C:()=>h,isRecord:()=>d,stegaClean:()=>k,vercelStegaCleanAll:()=>b});var e={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},f={0:8203,1:8204,2:8205,3:65279},g=[,,,,].fill(String.fromCodePoint(f[0])).join("");function h(a,b,c="auto"){let d;return!0===c||"auto"===c&&(!(!Number.isNaN(Number(a))||/[a-z]/i.test(a)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(a))&&Date.parse(a)||function(a){try{new URL(a,a.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(a))?a:`${a}${d=JSON.stringify(b),`${g}${Array.from(d).map(a=>{let b=a.charCodeAt(0);if(b>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${d} on character ${a} (${b})`);return Array.from(b.toString(4).padStart(4,"0")).map(a=>String.fromCodePoint(f[a])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(f).map(a=>a.reverse())),Object.fromEntries(Object.entries(e).map(a=>a.reverse()));var i=`${Object.values(e).map(a=>`\\u{${a.toString(16)}}`).join("")}`,j=RegExp(`[${i}]{4,}`,"gu");function k(a){var b,c;return a&&JSON.parse({cleaned:(b=JSON.stringify(a)).replace(j,""),encoded:(null==(c=b.match(j))?void 0:c[0])||""}.cleaned)}let b=k}},520304:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasA11yProp:()=>f,mergeClasses:()=>e,toCamelCase:()=>c,toKebabCase:()=>b,toPascalCase:()=>d});let b=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase()),d=a=>{let b=c(a);return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim(),f=a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0}}},91640:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>d});var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}},252747:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(91640),f=a.i(520304);let b=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e.default,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:(0,f.mergeClasses)("lucide",h),...!i&&!(0,f.hasA11yProp)(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]]))}},274453:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(520304),f=a.i(252747);let b=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},h)=>(0,d.createElement)(f.default,{ref:h,iconNode:b,className:(0,e.mergeClasses)(`lucide-${(0,e.toKebabCase)((0,e.toPascalCase)(a))}`,`lucide-${a}`,c),...g}));return c.displayName=(0,e.toPascalCase)(a),c}}},161982:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))}},502549:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js"))}},331055:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(161982);var d=a.i(502549);a.n(d)},164938:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"warnOnce",{enumerable:!0,get:function(){return a}});let a=a=>{}}},655439:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return f}})},140333:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={VALID_LOADERS:function(){return a},imageConfigDefault:function(){return b}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a=["default","imgix","cloudinary","akamai","custom"],b={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}}},918817:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return h}}),a.r(164938);let b=a.r(655439),c=a.r(140333),d=["-moz-initial","fill","none","scale-down",void 0];function f(a){return void 0!==a.default}function g(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function h(a,e){var h,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=e,O=K||c.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),c=null==(h=O.qualities)?void 0:h.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:c}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=g(t),T=g(u);if((i=m)&&"object"==typeof i&&(f(i)||void 0!==i.src)){let a=f(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=g(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,b.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=d.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}}},806914:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/image-component.js <module evaluation>"))}},999722:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/image-component.js"))}},256617:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(806914);var d=a.i(999722);a.n(d)},784739:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";function f(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return a}}),f.__next_img_default=!0;let a=f}},882474:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={default:function(){return j},getImageProps:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(445237),c=a.r(918817),d=a.r(256617),i=b._(a.r(784739));function h(a){let{props:b}=(0,c.getImgProps)(a,{defaultLoader:i.default,imgConf:JSON.parse('{"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false}')});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let j=d.Image}},99492:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(882474)},616296:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],c=(0,d.default)("sparkles",b)}},954124:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Sparkles:()=>d.default});var d=a.i(616296)},515564:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],c=(0,d.default)("arrow-right",b)}},391745:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ArrowRight:()=>d.default});var d=a.i(515564)},607566:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],c=(0,d.default)("calendar",b)}},640836:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Calendar:()=>d.default});var d=a.i(607566)},98702:function(a){var{g:b,__dirname:c,m:d,e:e}=a},859175:a=>{var{g:b,__dirname:c}=a;a.n(a.i(929987))},729969:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(859175),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["blog",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/blog/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/blog/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},904824:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(859175),a.i(660874),a.i(715847),a.i(781045),a.i(729969)},331680:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(729969)},279452:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(904824);var d=a.i(331680)},929987:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>k,metadata:()=>b,revalidate:()=>c});var d=a.i(129629),e=a.i(331055),f=a.i(99492),g=a.i(934041),h=a.i(640836),i=a.i(391745),j=a.i(954124);let b={title:"Blog - Celer AI",description:"Latest insights, tips, and updates about AI-powered healthcare documentation",keywords:"healthcare, AI, medical documentation, blog, insights"},c=3600;async function k(){let a=await (0,g.getBlogPosts)();return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden",children:[(0,d.jsx)("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(f.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold",children:"Celer AI"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(e.default,{href:"/",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Home"}),(0,d.jsx)(e.default,{href:"/blog",className:"text-slate-900 text-sm font-medium",children:"Blog"}),(0,d.jsx)(e.default,{href:"/guide",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Guides"}),(0,d.jsx)(e.default,{href:"/login",className:"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200",children:"Get Started"})]})]})}),(0,d.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"}),(0,d.jsx)("div",{className:"absolute bottom-40 left-1/4 w-20 h-20 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000"})]}),(0,d.jsx)("main",{className:"relative",children:(0,d.jsx)("div",{className:"relative max-w-4xl mx-auto px-6 pt-32 pb-12",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4",children:(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse relative z-10",children:"Insights"})}),(0,d.jsx)("p",{className:"text-lg text-slate-600 mb-6",children:"Effortless insights that flow naturally"})]})})}),(0,d.jsx)("section",{className:"relative pb-16 px-6",children:(0,d.jsx)("div",{className:"max-w-6xl mx-auto",children:0===a.length?(0,d.jsxs)("div",{className:"text-center py-16 animate-fade-in",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse",children:(0,d.jsx)(h.Calendar,{className:"w-16 h-16 text-indigo-500"})}),(0,d.jsx)("div",{className:"absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-cyan-500/20 rounded-full blur-xl animate-pulse"})]}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-slate-900 mb-4",children:"Magical insights coming soon"}),(0,d.jsxs)("p",{className:"text-lg text-slate-600 max-w-md mx-auto",children:["We're crafting effortless insights that will make AI healthcare feel like magic.",(0,d.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 font-medium",children:"Stay tuned!"})]})]}):(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-slide-up delay-400",children:a.map((a,b)=>(0,d.jsx)("div",{className:"animate-fade-in",style:{animationDelay:`${100*b}ms`},children:(0,d.jsx)(l,{post:a})},a._id))})})})]})}function l({post:a}){let b=new Date(a.publishedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,d.jsxs)("article",{className:"relative group",children:[(0,d.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-0 group-hover:opacity-30 transition-all duration-500"}),(0,d.jsxs)("div",{className:"relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden border border-white/20",children:[a.mainImage&&(0,d.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[(0,d.jsx)(f.default,{src:(0,g.urlFor)(a.mainImage).width(400).height(300).fit("crop").auto("format").url(),alt:a.mainImage.alt||a.title,fill:!0,className:"object-cover group-hover:scale-110 transition-transform duration-500",loading:"lazy"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"}),(0,d.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)(j.Sparkles,{className:"w-5 h-5 text-white animate-pulse"})})]}),(0,d.jsxs)("div",{className:"p-6",children:[a.categories&&a.categories.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:a.categories.slice(0,2).map(a=>(0,d.jsx)("span",{className:"px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 text-xs font-medium rounded-full border border-indigo-200",children:a.title},a.slug.current))}),(0,d.jsx)("h2",{className:"text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-purple-600 transition-all duration-300",children:a.title}),a.excerpt&&(0,d.jsx)("p",{className:"text-slate-600 mb-6 line-clamp-3 leading-relaxed",children:a.excerpt}),(0,d.jsx)("div",{className:"flex items-center justify-between text-sm text-slate-500 mb-6",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(h.Calendar,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:b})]}),a.author&&(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[a.author.image&&(0,d.jsx)(f.default,{src:(0,g.urlFor)(a.author.image).width(24).height(24).fit("crop").auto("format").url(),alt:a.author.name,width:24,height:24,className:"rounded-full border border-white/50"}),(0,d.jsx)("span",{children:a.author.name})]})]})}),(0,d.jsxs)(e.default,{href:`/blog/${a.slug.current}`,onClick:()=>{},className:"group/link inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:[(0,d.jsx)("span",{children:"Read Insight"}),(0,d.jsx)(i.ArrowRight,{className:"w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300"})]})]})]})]})}}},557265:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_4241083d.js"].map(b=>a.l(b))).then(()=>b(769257)))},75728:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__ae5705a9._.js"].map(b=>a.l(b))).then(()=>b(947382)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__63f4a7f4._.js.map