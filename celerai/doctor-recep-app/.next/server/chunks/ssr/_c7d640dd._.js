module.exports={283620:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={callServer:function(){return b.callServer},createServerReference:function(){return d},findSourceMapURL:function(){return c.findSourceMapURL}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(740515),c=a.r(249754),d=a.r(97477).createServerReference}},696627:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({signup:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d",d.callServer,void 0,d.findSourceMapURL,"signup")},389312:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SignupForm:()=>i});var d=a.i(674420),e=a.i(722851),f=a.i(918656),g=a.i(696627),h=a.i(844183);let b={success:!1,message:""};function i({referralCode:a}){let[c,i,j]=(0,e.useActionState)(g.signup,b),k=(0,f.useRouter)();return(0,e.useEffect)(()=>{if(c?.success&&c?.message?.includes("Account created successfully")){(0,h.trackAuth)("signup_completed");let a=setTimeout(()=>{k.push("/login")},3e3);return()=>clearTimeout(a)}},[c?.success,c?.message,k]),(0,d.jsxs)("form",{action:a=>{(0,h.trackAuth)("signup_started"),i(a)},className:"mt-8 space-y-6",children:[a&&(0,d.jsx)("input",{type:"hidden",name:"referral_code",value:a}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-slate-700 mb-2",children:"Full Name"}),(0,d.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"Dr. John Doe"}),c?.fieldErrors?.name&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.name[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-slate-700 mb-2",children:"Email Address"}),(0,d.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"<EMAIL>"}),c?.fieldErrors?.email&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.email[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-700 mb-2",children:"Password"}),(0,d.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"Create a strong password"}),c?.fieldErrors?.password&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.password[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"clinic_name",className:"block text-sm font-medium text-slate-700 mb-2",children:"Hospital Name"}),(0,d.jsx)("input",{id:"clinic_name",name:"clinic_name",type:"text",className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"ABC Medical Center"}),c?.fieldErrors?.clinic_name&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.clinic_name[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-slate-700 mb-2",children:"Phone Number"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-4",children:(0,d.jsx)("span",{className:"text-slate-500 text-sm font-medium",children:"+91"})}),(0,d.jsx)("input",{id:"phone",name:"phone",type:"tel",autoComplete:"tel",className:"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"**********"})]}),c?.fieldErrors?.phone&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.phone[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"referral_code",className:"block text-sm font-medium text-slate-700 mb-2",children:"Referral ID (Optional)"}),(0,d.jsx)("input",{id:"referral_id",name:"referral_code",type:"text",className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"Enter referral ID if you have one (optional)",defaultValue:a||""}),c?.fieldErrors?.referral_code&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:c.fieldErrors.referral_id[0]})]})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2"}),(0,d.jsxs)("label",{htmlFor:"terms",className:"text-sm text-slate-600 leading-relaxed",children:["I accept the"," ",(0,d.jsx)("a",{href:"/terms",target:"_blank",className:"text-indigo-600 hover:text-purple-600 font-medium transition-colors",children:"Terms of Service"})," ","and"," ",(0,d.jsx)("a",{href:"/privacy",target:"_blank",className:"text-indigo-600 hover:text-purple-600 font-medium transition-colors",children:"Privacy Policy"})]})]})]}),c?.message&&(0,d.jsx)("div",{className:`rounded-xl p-4 border ${c.success?"bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200":"bg-gradient-to-r from-red-50 to-pink-50 border-red-200"}`,children:(0,d.jsxs)("p",{className:`text-sm font-medium flex items-center space-x-2 ${c.success?"text-emerald-800":"text-red-800"}`,children:[(0,d.jsx)("span",{className:`w-2 h-2 rounded-full ${c.success?"bg-emerald-400":"bg-red-400"}`}),(0,d.jsx)("span",{children:c.message})]})}),(0,d.jsx)("div",{children:(0,d.jsx)("button",{type:"submit",disabled:j,className:"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2",children:j?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,d.jsx)("span",{children:"Creating your magical account..."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{children:"Start Creating Magic"}),(0,d.jsx)("svg",{className:"w-4 h-4 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})})})]})}}}};

//# sourceMappingURL=_c7d640dd._.js.map