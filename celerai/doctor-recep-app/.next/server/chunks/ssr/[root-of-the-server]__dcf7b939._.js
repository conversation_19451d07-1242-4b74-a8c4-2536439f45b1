module.exports={81725:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},654129:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},826542:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createConsultationWithFiles:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("7fc832fd06db7232f37f9b1b44631bd2956c9b940c",d.callServer,void 0,d.findSourceMapURL,"createConsultationWithFiles")},452987:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f,g,h={ACTION_HMR_REFRESH:function(){return n},ACTION_NAVIGATE:function(){return b},ACTION_PREFETCH:function(){return m},ACTION_REFRESH:function(){return a},ACTION_RESTORE:function(){return c},ACTION_SERVER_ACTION:function(){return o},ACTION_SERVER_PATCH:function(){return l},PrefetchCacheEntryStatus:function(){return k},PrefetchKind:function(){return j}};for(var i in h)Object.defineProperty(e,i,{enumerable:!0,get:h[i]});let a="refresh",b="navigate",c="restore",l="server-patch",m="prefetch",n="hmr-refresh",o="server-action";var j=((f={}).AUTO="auto",f.FULL="full",f.TEMPORARY="temporary",f),k=((g={}).fresh="fresh",g.reusable="reusable",g.expired="expired",g.stale="stale",g);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},375265:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isThenable",{enumerable:!0,get:function(){return f}})},341977:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={dispatchAppRouterAction:function(){return h},useActionQueue:function(){return i}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(465578)._(a.r(722851)),c=a.r(375265),j=null;function h(a){if(null===j)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});j(a)}function i(a){let[d,e]=b.default.useState(a.state);return j=b=>a.dispatch(b,e),(0,c.isThenable)(d)?(0,b.use)(d):d}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},740515:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"callServer",{enumerable:!0,get:function(){return f}});let b=a.r(722851),c=a.r(452987),g=a.r(341977);async function f(a,d){return new Promise((e,f)=>{(0,b.startTransition)(()=>{(0,g.dispatchAppRouterAction)({type:c.ACTION_SERVER_ACTION,actionId:a,actionArgs:d,resolve:e,reject:f})})})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},249754:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"findSourceMapURL",{enumerable:!0,get:function(){return a}});let a=void 0;("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},283620:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={callServer:function(){return b.callServer},createServerReference:function(){return d},findSourceMapURL:function(){return c.findSourceMapURL}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(740515),c=a.r(249754),d=a.r(97477).createServerReference}},442638:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasA11yProp:()=>f,mergeClasses:()=>e,toCamelCase:()=>c,toKebabCase:()=>b,toPascalCase:()=>d});let b=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase()),d=a=>{let b=c(a);return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim(),f=a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0}}},30875:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>d});var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}},847252:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(722851),e=a.i(30875),f=a.i(442638);let b=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e.default,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:(0,f.mergeClasses)("lucide",h),...!i&&!(0,f.hasA11yProp)(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]]))}},528442:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(722851),e=a.i(442638),f=a.i(847252);let b=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},h)=>(0,d.createElement)(f.default,{ref:h,iconNode:b,className:(0,e.mergeClasses)(`lucide-${(0,e.toKebabCase)((0,e.toPascalCase)(a))}`,`lucide-${a}`,c),...g}));return c.displayName=(0,e.toPascalCase)(a),c}}},807365:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],c=(0,d.default)("x",b)}},767332:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({X:()=>d.default});var d=a.i(807365)},686341:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],c=(0,d.default)("circle-check-big",b)}},72732:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({CheckCircle:()=>d.default});var d=a.i(686341)},579046:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],c=(0,d.default)("circle-alert",b)}},676261:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AlertCircle:()=>d.default});var d=a.i(579046)},948572:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],c=(0,d.default)("mic",b)}},629551:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Mic:()=>d.default});var d=a.i(948572)},602363:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]],c=(0,d.default)("square",b)}},954780:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Square:()=>d.default});var d=a.i(602363)},537860:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],c=(0,d.default)("upload",b)}},525128:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Upload:()=>d.default});var d=a.i(537860)},13081:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]],c=(0,d.default)("camera",b)}},388457:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Camera:()=>d.default});var d=a.i(13081)},711656:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({STORAGE_CONFIG:()=>b,calculateTotalFileSize:()=>l,deleteFile:()=>i,downloadFile:()=>k,extractFilePathFromUrl:()=>j,generateStoragePath:()=>f,uploadFile:()=>g,uploadMultipleFiles:()=>h,validateFile:()=>e,validateTotalSize:()=>m}),a.i(81725);var d=a.i(654129);let b={BUCKET_NAME:process.env.R2_BUCKET_NAME||"celerai-storage",PUBLIC_URL:process.env.R2_PUBLIC_URL||"https://celerai.tallyup.pro",AUDIO_PREFIX:"consultation-audio",IMAGE_PREFIX:"consultation-images",MAX_FILE_SIZE:0x6400000,MAX_TOTAL_SIZE:0xc800000,ALLOWED_AUDIO_TYPES:["audio/webm","audio/mp3","audio/wav","audio/m4a","audio/mpeg","audio/mp4","audio/ogg"],ALLOWED_IMAGE_TYPES:["image/jpeg","image/jpg","image/png","image/webp","image/heic"],RETENTION_DAYS:30},c=()=>new d.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID||"57014886c6cd87ebacf23a94e56a6e0c"}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID||"4dff08f96bf2f040b48bf3973813f7f0",secretAccessKey:process.env.R2_SECRET_ACCESS_KEY||"****************************************************************"}});function e(a,c){return a.size>b.MAX_FILE_SIZE?{valid:!1,error:`File size exceeds ${b.MAX_FILE_SIZE/1024/1024}MB limit`}:("audio"===c?b.ALLOWED_AUDIO_TYPES:b.ALLOWED_IMAGE_TYPES).includes(a.type)?{valid:!0}:{valid:!1,error:`File type ${a.type} is not allowed`}}function f(a,c,d,e){let f=d.replace(/[^a-zA-Z0-9.-]/g,"_"),g="audio"===e?b.AUDIO_PREFIX:b.IMAGE_PREFIX;return`${g}/${a}/${c}/${f}`}async function g(a,g,h,i){try{let j=e(a,i);if(!j.valid)return{success:!1,error:j.error};let k=c(),l=f(g,h,a.name,i),m=await a.arrayBuffer(),n=new d.PutObjectCommand({Bucket:b.BUCKET_NAME,Key:l,Body:new Uint8Array(m),ContentType:a.type,CacheControl:"public, max-age=3600"});await k.send(n);let o=`${b.PUBLIC_URL}/${l}`;return{success:!0,url:o}}catch(a){return console.error("R2 upload error:",a),{success:!1,error:`Upload failed: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(a,b,c,d){let e=await Promise.all(a.map(a=>g(a,b,c,d))),f=e.filter(a=>a.success),h=e.filter(a=>!a.success);return h.length>0?{success:!1,errors:h.map(a=>a.error||"Unknown error")}:{success:!0,urls:f.map(a=>a.url).filter(Boolean)}}async function i(a,e){try{let e=c(),f=new d.DeleteObjectCommand({Bucket:b.BUCKET_NAME,Key:a});return await e.send(f),{success:!0}}catch(a){return console.error("R2 delete error:",a),{success:!1,error:a instanceof Error?a.message:"Delete failed"}}}function j(a,c){try{let d="audio"===c?b.AUDIO_PREFIX:b.IMAGE_PREFIX,e=`/${d}/`,f=a.indexOf(e);if(-1===f)return null;return a.substring(a.indexOf(d))}catch{return null}}async function k(a,c){try{let c=`${b.PUBLIC_URL}/${a}`,d=await fetch(c);if(!d.ok)throw Error(`HTTP ${d.status}: ${d.statusText}`);let e=await d.blob();return{success:!0,data:e}}catch(a){return console.error("R2 download error:",a),{success:!1,error:a instanceof Error?a.message:"Download failed"}}}function l(a){return a.reduce((a,b)=>a+b.size,0)}function m(a){return l(a)>b.MAX_TOTAL_SIZE?{valid:!1,error:`Total file size exceeds ${b.MAX_TOTAL_SIZE/1024/1024}MB limit`}:{valid:!0}}}}};

//# sourceMappingURL=%5Broot-of-the-server%5D__dcf7b939._.js.map