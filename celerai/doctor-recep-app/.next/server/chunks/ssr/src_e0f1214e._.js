module.exports = {

"[project]/src/lib/actions/data:1ebaea [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d":"signup"},"src/lib/actions/auth.ts",""] */ __turbopack_context__.s({
    "signup": (()=>signup)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var signup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "signup"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/components/auth/signup-form.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SignupForm": (()=>SignupForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$1ebaea__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:1ebaea [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analytics.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
// CORRECTED initialState to conform to FormState interface
const initialState = {
    success: false,
    message: ''
};
function SignupForm({ referralCode }) {
    const [state, formAction, isPending] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useActionState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$1ebaea__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["signup"], initialState);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Auto-redirect to login page after 3 seconds for successful account creation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (state?.success && state?.message?.includes('Account created successfully')) {
            // Track successful signup completion
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trackAuth"])('signup_completed');
            const timer = setTimeout(()=>{
                router.push('/login');
            }, 3000);
            return ()=>clearTimeout(timer);
        }
    }, [
        state?.success,
        state?.message,
        router
    ]);
    // Handle form submission to track signup started
    const handleSubmit = (formData)=>{
        // Track signup attempt
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trackAuth"])('signup_started');
        // Call the original form action
        formAction(formData);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        action: handleSubmit,
        className: "mt-8 space-y-6",
        children: [
            referralCode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                type: "hidden",
                name: "referral_code",
                value: referralCode
            }, void 0, false, {
                fileName: "[project]/src/components/auth/signup-form.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "name",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Full Name"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 61,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "name",
                                name: "name",
                                type: "text",
                                autoComplete: "name",
                                required: true,
                                className: "w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                placeholder: "Dr. John Doe"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 64,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 75,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.name[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 76,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "email",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Email Address"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 82,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "email",
                                name: "email",
                                type: "email",
                                autoComplete: "email",
                                required: true,
                                className: "w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                placeholder: "<EMAIL>"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 96,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.email[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "password",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Password"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 103,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "password",
                                name: "password",
                                type: "password",
                                autoComplete: "new-password",
                                required: true,
                                className: "w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                placeholder: "Create a strong password"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.password && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.password[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 116,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 102,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "clinic_name",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Hospital Name"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "clinic_name",
                                name: "clinic_name",
                                type: "text",
                                className: "w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                placeholder: "ABC Medical Center"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 127,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.clinic_name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 136,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.clinic_name[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "phone",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Phone Number"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-y-0 left-0 flex items-center pl-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-slate-500 text-sm font-medium",
                                            children: "+91"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/auth/signup-form.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        id: "phone",
                                        name: "phone",
                                        type: "tel",
                                        autoComplete: "tel",
                                        className: "w-full pl-12 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                        placeholder: "9876543210"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 150,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.phone && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 161,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.phone[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 162,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 160,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "referral_code",
                                className: "block text-sm font-medium text-slate-700 mb-2",
                                children: "Referral ID (Optional)"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "referral_id",
                                name: "referral_code",
                                type: "text",
                                className: "w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",
                                placeholder: "Enter referral ID if you have one (optional)",
                                defaultValue: referralCode || ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this),
                            state?.fieldErrors?.referral_code && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-2 text-sm text-red-600 flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "w-1 h-1 bg-red-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 181,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: state.fieldErrors.referral_id[0]
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 182,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 180,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "terms",
                                name: "terms",
                                type: "checkbox",
                                required: true,
                                className: "mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 189,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "terms",
                                className: "text-sm text-slate-600 leading-relaxed",
                                children: [
                                    "I accept the",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "/terms",
                                        target: "_blank",
                                        className: "text-indigo-600 hover:text-purple-600 font-medium transition-colors",
                                        children: "Terms of Service"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 198,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    "and",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "/privacy",
                                        target: "_blank",
                                        className: "text-indigo-600 hover:text-purple-600 font-medium transition-colors",
                                        children: "Privacy Policy"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/auth/signup-form.tsx",
                                        lineNumber: 202,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 196,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/signup-form.tsx",
                        lineNumber: 188,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/signup-form.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            state?.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `rounded-xl p-4 border ${state.success ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `text-sm font-medium flex items-center space-x-2 ${state.success ? 'text-emerald-800' : 'text-red-800'}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: `w-2 h-2 rounded-full ${state.success ? 'bg-emerald-400' : 'bg-red-400'}`
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/signup-form.tsx",
                            lineNumber: 218,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: state.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/signup-form.tsx",
                            lineNumber: 221,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/auth/signup-form.tsx",
                    lineNumber: 215,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/signup-form.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "submit",
                    disabled: isPending,
                    className: "group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2",
                    children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 234,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Creating your magical account..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 235,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Start Creating Magic"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 239,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-4 h-4 group-hover:translate-x-1 transition-transform",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 7l5 5m0 0l-5 5m5-5H6"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/auth/signup-form.tsx",
                                    lineNumber: 241,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/signup-form.tsx",
                                lineNumber: 240,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/auth/signup-form.tsx",
                    lineNumber: 227,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/auth/signup-form.tsx",
                lineNumber: 226,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/auth/signup-form.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_e0f1214e._.js.map