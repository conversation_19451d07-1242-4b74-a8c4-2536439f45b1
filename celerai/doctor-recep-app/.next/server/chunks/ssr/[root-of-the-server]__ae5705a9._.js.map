{"version": 3, "sources": ["turbopack:///[project]/node_modules/eventsource/lib/eventsource.js", "turbopack:///[project]/node_modules/@sanity/eventsource/node.js"], "sourcesContent": ["var parse = require('url').parse\nvar events = require('events')\nvar https = require('https')\nvar http = require('http')\nvar util = require('util')\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n", "module.exports = require('eventsource')\n"], "names": [], "mappings": "shBAAI,EAAQ,EAAA,CAAA,CAAA,QAAe,KAAK,CAC5B,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAe,CACjB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAC1C,qBAAsB,iBAAkB,aAAc,sBACvD,CAEG,EAAM,CAAC,IAAK,IAAK,IAAI,CAQrB,EAAiB,4BAerB,SAAS,EAAa,CAAG,CAAE,CAAmB,EAC5C,IAwCI,EAxCA,EAAa,EAAY,UAAU,CACnC,EAAU,GAAuB,EAAoB,OAAO,CAC5D,GAAe,EACnB,OAAO,cAAc,CAAC,IAAI,CAAE,aAAc,CACxC,IAAK,WACH,OAAO,CACT,CACF,GAEA,OAAO,cAAc,CAAC,IAAI,CAAE,MAAO,CACjC,IAAK,WACH,OAAO,CACT,CACF,GAEA,IAAI,EAAO,IAAI,CAIf,SAAS,EAAoB,CAAO,EAC9B,IAAe,EAAY,MAAM,EAAE,CACvC,EAAa,EAAY,UAAU,CACnC,EAAM,QAAS,IAAI,EAAM,QAAS,CAAC,QAAS,CAAO,IAI/C,IACF,EAAM,EACN,EAAe,IAFC,CAGhB,GAAe,GAEjB,WAAW,WACL,IAAe,EAAY,UAAU,EAAI,EAAK,oBAAoB,EAAE,CAGxE,EAAK,oBAAoB,EAAG,EAC5B,IACF,EAAG,EAAK,iBAAiB,EAC3B,CAtBA,EAAK,iBAAiB,CAAG,IACzB,EAAK,oBAAoB,EAAG,EAwB5B,IAAI,EAAc,GACd,GAAW,CAAO,CAAC,gBAAgB,EAAE,CACvC,EAAc,CAAO,CAAC,gBAAgB,CACtC,OAAO,CAAO,CAAC,gBAAgB,EAGjC,IAAI,GAAyB,EACzB,EAAO,GACP,EAAY,GAEZ,EAAe,KAEnB,SAAS,IACP,IAAI,EAAU,EAAM,GAChB,EAAgC,WAArB,EAAQ,QAAQ,CAG/B,GAFA,EAAQ,OAAO,CAAG,CAAE,gBAAiB,WAAY,OAAU,mBAAoB,EAC3E,IAAa,EAAQ,OAAO,CAAC,gBAAgB,CAAG,CAAA,EAChD,EAAS,CACX,IAAI,EAAa,EAqYvB,AArYsC,SAqY7B,AAAqB,CAAO,EACnC,IAAI,EAAO,CAAC,EACZ,IAAK,IAAI,KAAO,EACV,EAAe,IADI,AACA,CAAC,IAIxB,EAJ8B,AAI1B,CAAC,EAAI,CAAG,CAAO,CAAC,EAAA,AAAI,EAG1B,OAAO,CACT,EAhZ0D,GAAW,EAC/D,IAAK,IAAI,KAAK,EAAY,CACxB,IAAI,EAAS,CAAU,CAAC,EAAE,CACtB,IACF,EAAQ,EADE,KACK,CAAC,EAAE,CAAG,CAAA,CAEzB,CACF,CAaA,GATA,CASI,CATI,kBAAkB,CAAG,CAAC,CAAC,GAAuB,CAAC,EAAoB,kBAAA,AAAkB,EAEzF,QAAgE,IAAzC,EAAoB,KAAgC,WAAhB,GAC7D,EAAQ,gBAAgB,CAAG,EAAoB,gBAAA,AAAgB,EAKlD,GAAuB,EAAoB,KAAK,CACjD,CACZ,IAAI,EAAQ,EAAM,EAAoB,KAAK,EAG3C,EAAQ,QAAQ,CAAG,CAFnB,EAA8B,WAAnB,EAAM,QAAQ,AAAK,EAEA,SAAW,QACzC,EAAQ,IAAI,CAAG,EACf,EAAQ,OAAO,CAAC,IAAI,CAAG,EAAQ,IAAI,CACnC,EAAQ,QAAQ,CAAG,EAAM,QAAQ,CACjC,EAAQ,IAAI,CAAG,EAAM,IAAI,CACzB,EAAQ,IAAI,CAAG,EAAM,IAAI,AAC3B,CAGA,GAAI,GAAuB,EAAoB,KAAK,EAAE,AACpD,IAAK,IAAI,KAAW,EAAoB,KAAK,CAAE,AAC7C,GAAsC,CAAC,GAAG,CAAtC,EAAa,OAAO,CAAC,IAIzB,IAAI,EAAS,EAAoB,KAAK,CAAC,EAAQ,MAChC,IAAX,IACF,CAAO,CAAC,CADgB,CACR,CAAG,CAAA,EAEvB,CAIE,QAA+D,IAAxC,EAAoB,KAA+B,UAAhB,GAC5D,EAAQ,eAAe,CAAG,EAAoB,eAAA,AAAe,EAmI/D,CAhIA,EAAO,AAAD,GAAY,EAAQ,CAAA,CAAI,CAAE,OAAO,CAAC,EAAS,SAAU,CAAG,EAG5D,GAFA,EAAK,oBAAoB,EAAG,EAEL,MAAnB,EAAI,UAAU,EAA+B,MAAnB,EAAI,UAAU,EAAY,AAAmB,QAAf,UAAU,EAA+B,MAAnB,EAAI,UAAU,CAAU,CACxG,EAAM,QAAS,IAAI,EAAM,QAAS,CAAC,OAAQ,EAAI,UAAU,CAAE,QAAS,EAAI,aAAa,IACrF,IACA,MACF,CAGA,GAAuB,MAAnB,EAAI,UAAU,EAA+B,MAAnB,EAAI,UAAU,EAA+B,MAAnB,EAAI,UAAU,CAAU,CAC9E,IAoCE,EACA,EArCE,EAAW,EAAI,OAAO,CAAC,QAAQ,QAC9B,AAAL,GAOA,CAPI,CAKa,AAEF,IAFM,CALN,GAKU,GAAK,IAEA,EAFM,GACnB,IAAI,IAAI,GAAU,MAAM,CAElB,MAAnB,EAAI,UAAU,GAAU,EAAe,CAAA,EAC3C,EAAM,OACN,QAAQ,QAAQ,CAAC,SARf,EAAM,QAAS,IAAI,EAAM,QAAS,CAAC,OAAQ,EAAI,UAAU,CAAE,QAAS,EAAI,aAAa,GAUzF,CAEA,GAAuB,KAAK,CAAxB,EAAI,UAAU,CAEhB,OADA,EAAM,QAAS,IAAI,EAAM,QAAS,CAAC,OAAQ,EAAI,UAAU,CAAE,QAAS,EAAI,aAAa,IAC9E,EAAK,KAAK,GAGnB,EAAa,EAAY,IAAI,CAC7B,EAAI,EAAE,CAAC,QAAS,WACd,EAAI,kBAAkB,CAAC,SACvB,EAAI,kBAAkB,CAAC,OACvB,GACF,GAEA,EAAI,EAAE,CAAC,MAAO,WACZ,EAAI,kBAAkB,CAAC,SACvB,EAAI,kBAAkB,CAAC,OACvB,GACF,GACA,EAAM,OAAQ,IAAI,EAAM,SAMxB,IAAI,EAAc,EACd,EAAsB,CAAC,EACvB,EAAgB,EAChB,EAAY,EAEhB,EAAI,EAAE,CAAC,OAAQ,SAAU,CAAK,EAC5B,GAAK,CAAD,CAOE,EAAM,MAAM,CAAG,EAAI,MAAM,CAAG,IAE1B,CADJ,EAA8B,EAAb,EADwB,AACpB,MAAM,CAAQ,EAAM,AACrB,MADqB,AAAM,WAE7C,EAAgB,EAAI,KADwB,CAClB,CAAG,EAAM,MAAM,CAjMxB,EAiM2B,IAAA,CAjMpB,CAmM1B,EAAY,OAAO,KAAK,CAAC,GACzB,EAAI,IAAI,CAAC,EAAW,EAAG,EAAG,GAC1B,EAAM,GAER,EAAM,IAAI,CAAC,EAAK,GAChB,GAAa,EAAM,MAAM,KAjBjB,OAnLD,EAoLP,CApLU,CAoLJ,EAnLP,EAAI,KAAK,CAAC,SAAU,CAAQ,CAAE,CAAK,EACxC,OAAO,CAAG,CAAC,EAAM,GAAK,CACxB,KAmLU,EAAM,EAAI,KAAK,CAAC,EAAI,OAAM,EAE5B,EAAY,EAAI,MAAM,AACxB,CAiBA,IAHA,EAdO,EAcH,EAAM,EACN,EAAS,EAEN,EAAM,GAAQ,CACf,IACE,MAAG,CAAC,EAAI,EACV,EAAE,CADa,CAGjB,GAAyB,EAJC,CAW5B,GAV6B,CAUxB,IAFD,EAFA,EAAa,CAAC,EACd,EAAc,EAGT,EAAI,EAAa,EAAa,GAAK,EAAI,EAAQ,EAAE,EAAG,AA/N3D,AAiOI,MAAM,AADV,EAAI,CAAG,CAAC,EAAA,AAAE,CACO,CACX,EAAc,GAAG,CACnB,EAAc,EAAI,CAAA,EAhOb,KAkOE,GACT,EAAyB,CADV,EAEf,EAAa,EAAI,GArOhB,KAsOQ,EAHsB,EAI/B,EADe,AACF,EAAI,CAAA,EAIrB,GAAI,EAAa,AALY,EAKT,CAClB,EAAc,EAAS,EACvB,EAAsB,EACtB,KACF,CACE,EAAc,EACd,EAAsB,AAFjB,CAEkB,EA0CjC,AAvCQ,SAuCC,AAAsB,CAAG,CAAE,CAAG,CAAE,CAAW,CAAE,CAAU,EAC9D,GAAmB,IAAf,EAAkB,CACpB,GAAI,EAAK,MAAM,CAAG,EAAG,CACnB,IAAI,EAAO,GAAa,UACxB,EAAM,EAAM,IAAI,EAAa,EAAM,CACjC,KAAM,EAAK,KAAK,CAAC,EAAG,CAAC,GACrB,YAAa,EACb,OAAQ,IAAI,IAAI,GAAK,MAAM,AAC7B,IACA,EAAO,EACT,CACA,EAAY,KAAK,CACnB,MAAO,GAAI,EAAc,EAAG,CAC1B,IAAI,EAAU,EAAc,EACxB,EAAO,EACP,EAAQ,EAAI,KAAK,CAAC,EAAK,GAAO,EAAU,CAAX,CAAwB,CAAA,CAAW,EAAG,QAAQ,GAG7E,EADE,EACK,EA9SH,KA6SO,AAEF,CAAG,CAAC,EAAM,EAAc,EAAE,CAC5B,EAAc,EADmB,AAGjC,EAAc,EAEvB,GALiD,AAK1C,EAEP,IAAI,EAAc,EAAa,EAC3B,EAAQ,EAAI,KAAK,CAAC,EAAK,EAAM,GAAa,QAAQ,GAEtD,GAAc,QAAQ,CAAlB,EACF,GAAQ,EAAQ,UACX,GAAc,SAAS,CAAnB,EACT,EAAY,OACP,GAAI,AAAU,MAAM,GACzB,EAAc,OACT,GAAc,UAAV,EAAmB,CAC5B,IAAI,EAAQ,SAAS,EAAO,GACxB,CAAC,OAAO,KAAK,CAAC,KAChB,EAAK,CADmB,gBACF,CAAG,CAAA,CAE7B,CACF,CACF,EAjF6B,EAAK,EAAK,EAAa,GAE5C,GAAO,EAAa,CACtB,CAEI,IAAQ,GACV,EAAM,GADY,EACP,EACX,EAAY,GACH,EAAM,GAAG,CAElB,EAAY,CADZ,EAAM,EAAI,KAAK,CAAC,EAAK,EAAA,EACL,MAAA,AAAM,CAE1B,EACF,EAAA,EAEI,EAAE,CAAC,QAAS,SAAU,CAAG,EAC3B,EAAK,oBAAoB,EAAG,EAC5B,EAAmB,EAAI,OAAO,CAChC,GAEI,EAAI,UAAU,EAAE,EAAI,UAAU,EAAC,GACnC,EAAI,GAAG,EACT,CAIA,SAAS,IACH,EAAK,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAG,GAAG,AAC3C,EAAK,IAAI,CAAC,KAAK,CAAC,EAAM,UAE1B,CANA,IAQA,IAAI,CAAC,MAAM,CAAG,WACR,IAAe,EAAY,MAAM,EAAE,CACvC,EAAa,EAAY,MAAM,CAC3B,EAAI,KAAK,EAAE,EAAI,KAAK,GACpB,EAAI,GAAG,EAAI,EAAI,GAAG,CAAC,KAAK,EAAE,EAAI,GAAG,CAAC,KAAK,GAC7C,CA6CF,CA8GA,SAAS,EAAO,CAAI,CAAE,CAAkB,EAEtC,GADA,OAAO,cAAc,CAAC,IAAI,CAAE,OAAQ,CAAE,UAAU,EAAO,MAAO,EAAM,YAAY,CAAK,GACjF,EACF,IAAK,IAAI,KAAK,EACR,EAAmB,CAFH,aAEiB,CADL,AACM,IAAI,AACxC,OAAO,cAAc,CAAC,IAAI,CAAE,EAAG,CAAE,UAAU,EAAO,MAAO,CAAkB,CAAC,EAAE,CAAE,YAAY,CAAK,EAIzG,CAQA,SAAS,EAAc,CAAI,CAAE,CAAa,EAExC,IAAK,IAAI,KADT,OAAO,cAAc,CAAC,IAAI,CAAE,OAAQ,CAAE,UAAU,EAAO,MAAO,EAAM,YAAY,CAAK,GACvE,EACR,EAAc,UADS,IACK,CAAC,IAAI,AACnC,OAAO,cAAc,CAAC,IAAI,CAAE,EAAG,CAAE,UAAU,EAAO,MAAO,CAAa,CAAC,EAAE,CAAE,YAAY,CAAK,EAGlG,CApIA,EAAO,OAAO,CAAG,EAEjB,EAAK,QAAQ,CAAC,EAAa,EAAO,YAAY,EAC9C,EAAY,SAAS,CAAC,WAAW,CAAG,EAEpC,CAAC,OAAQ,GAFwC,KAE/B,UAAU,CAAC,OAAO,CAAC,IAFwC,KAE9B,CAAM,EACnD,OAAO,cAAc,CAAC,EAAY,SAAS,CAAE,KAAO,EAAQ,CAO1D,IAAK,SAAS,EACZ,IAAI,EAAW,IAAI,CAAC,SAAS,CAAC,EAAO,CAAC,EAAE,CACxC,OAAO,EAAY,EAAS,SAAS,CAAG,EAAS,SAAS,CAAG,OAAY,CAC3E,EASA,IAAK,SAAc,AAAL,CAAa,EACzB,IAAI,CAAC,kBAAkB,CAAC,GACxB,IAAI,CAAC,gBAAgB,CAAC,EAAQ,EAChC,CACF,EACF,GAKA,OAAO,cAAc,CAAC,EAAa,aAAc,CAAC,YAAY,EAAM,MAAO,CAAC,GAC5E,OAAO,cAAc,CAAC,EAAa,OAAQ,CAAC,YAAY,EAAM,MAAO,CAAC,GACtE,OAAO,cAAc,CAAC,EAAa,SAAU,CAAC,YAAY,EAAM,MAAO,CAAC,GAExE,EAAY,SAAS,CAAC,UAAU,CAAG,EACnC,EAAY,SAAS,CAAC,IAAI,CAAG,EAC7B,EAAY,SAAS,CAAC,MAAM,CAAG,EAQ/B,EAAY,SAAS,CAAC,KAAK,CAAG,WAC5B,IAAI,CAAC,MAAM,EACb,EAWA,EAAY,SAAS,CAAC,gBAAgB,CAAG,SAAS,AAAkB,CAAI,CAAE,CAAQ,EACxD,YAAY,AAAhC,OAAO,IAET,EAAS,SAAS,CAAG,EACrB,IAAI,CAAC,EAAE,CAAC,EAAM,GAElB,EASA,EAAY,SAAS,CAAC,aAAa,CAAG,SAAS,AAAe,CAAK,EACjE,GAAI,CAAC,EAAM,IAAI,CACb,CADe,KACT,AAAI,MAAM,8BAIlB,IAAI,CAAC,IAAI,CAAC,EAAM,IAAI,CAAE,EAAM,MAAM,CACpC,EAWA,EAAY,SAAS,CAAC,mBAAmB,CAAG,SAAS,AAAqB,CAAI,CAAE,CAAQ,EAC9D,YAApB,AAAgC,OAAzB,IACT,EAAS,SAAS,MAAG,EACrB,IAAI,CAAC,cAAc,CAAC,EAAM,GAE9B,qDC1bA,EAAO,OAAO,CAAA,EAAA,CAAA,CAAA", "ignoreList": [0, 1]}