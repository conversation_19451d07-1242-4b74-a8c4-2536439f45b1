module.exports={329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},137496:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSession:()=>i,decrypt:()=>h,deleteSession:()=>l,encrypt:()=>g,refreshSession:()=>k,updateSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify session"),null}}async function i(a){let b=new Date(Date.now()+6048e5),c=await g({userId:a,expiresAt:b}),d=await (0,f.cookies)();console.log("DEBUG: Creating session for user:",a),console.log("DEBUG: Session expires at:",b),d.set("session",c,{httpOnly:!0,secure:!1,expires:b,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("session")?.value,c=await h(b);if(console.log("DEBUG: Updating session - session exists:",!!b),console.log("DEBUG: Updating session - payload valid:",!!c),!b||!c)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let d=new Date(Date.now()+6048e5);a.set("session",b,{httpOnly:!0,secure:!1,expires:d,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function k(a){console.log("DEBUG: Refreshing session for user:",a),await l(),await i(a),console.log("DEBUG: Session refresh completed")}async function l(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting session cookie"),a.delete("session"),console.log("DEBUG: Session cookie deleted")}}},76803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkSession:()=>c,getDoctorQuota:()=>k,getUser:()=>i,getUserById:()=>j,verifySession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(137496),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId||(0,f.redirect)("/login"),{isAuth:!0,userId:c.userId}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId?{isAuth:!0,userId:c.userId}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("*").eq("id",a.userId).single();if(d)return console.error("Failed to fetch user:",d.message||d),null;if(!c)return null;return{...c,password_hash:c.password_hash}}catch(a){return console.error("Failed to fetch user:",a instanceof Error?a.message:a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",a).single();if(d)return console.error("Failed to fetch user by ID:",d.message||d),null;if(!c)return null;return{...c}}catch(a){return console.error("Failed to fetch user by ID:",a instanceof Error?a.message:a),null}}),k=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",a).single();if(d)return console.error("Failed to fetch quota:",d.message||d),null;let e=c.monthly_quota-c.quota_used,f=Math.round(c.quota_used/c.monthly_quota*100),g=new Date(c.quota_reset_at),i=Math.ceil((g.getTime()-Date.now())/864e5);return{monthly_quota:c.monthly_quota,quota_used:c.quota_used,quota_remaining:e,quota_percentage:f,quota_reset_at:c.quota_reset_at,days_until_reset:Math.max(0,i)}}catch(a){return console.error("Failed to fetch quota:",a instanceof Error?a.message:a),null}})}},520304:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasA11yProp:()=>f,mergeClasses:()=>e,toCamelCase:()=>c,toKebabCase:()=>b,toPascalCase:()=>d});let b=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase()),d=a=>{let b=c(a);return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim(),f=a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0}}},91640:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>d});var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}},252747:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(91640),f=a.i(520304);let b=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e.default,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:(0,f.mergeClasses)("lucide",h),...!i&&!(0,f.hasA11yProp)(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]]))}},274453:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(520304),f=a.i(252747);let b=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},h)=>(0,d.createElement)(f.default,{ref:h,iconNode:b,className:(0,e.mergeClasses)(`lucide-${(0,e.toKebabCase)((0,e.toPascalCase)(a))}`,`lucide-${a}`,c),...g}));return c.displayName=(0,e.toPascalCase)(a),c}}},161982:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))}},502549:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js"))}},331055:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(161982);var d=a.i(502549);a.n(d)},164938:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"warnOnce",{enumerable:!0,get:function(){return a}});let a=a=>{}}},655439:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return f}})},140333:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={VALID_LOADERS:function(){return a},imageConfigDefault:function(){return b}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a=["default","imgix","cloudinary","akamai","custom"],b={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}}},918817:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return h}}),a.r(164938);let b=a.r(655439),c=a.r(140333),d=["-moz-initial","fill","none","scale-down",void 0];function f(a){return void 0!==a.default}function g(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function h(a,e){var h,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=e,O=K||c.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),c=null==(h=O.qualities)?void 0:h.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:c}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=g(t),T=g(u);if((i=m)&&"object"==typeof i&&(f(i)||void 0!==i.src)){let a=f(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=g(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,b.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=d.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}}},806914:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/image-component.js <module evaluation>"))}},999722:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/image-component.js"))}},256617:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(806914);var d=a.i(999722);a.n(d)},784739:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";function f(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return a}}),f.__next_img_default=!0;let a=f}},882474:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={default:function(){return j},getImageProps:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(445237),c=a.r(918817),d=a.r(256617),i=b._(a.r(784739));function h(a){let{props:b}=(0,c.getImgProps)(a,{defaultLoader:i.default,imgConf:JSON.parse('{"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false}')});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let j=d.Image}},99492:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(882474)},616296:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],c=(0,d.default)("sparkles",b)}},954124:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Sparkles:()=>d.default});var d=a.i(616296)},974827:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(230122)},19198:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(230122),a.i(974827)},543654:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"608ed2498189cfd7c9da6c6017df4df3d3e1da70c2":()=>d.changePassword});var d=a.i(230122);a.i(974827)},655425:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"608ed2498189cfd7c9da6c6017df4df3d3e1da70c2":()=>d["608ed2498189cfd7c9da6c6017df4df3d3e1da70c2"]}),a.i(19198);var d=a.i(543654)},11814:a=>{var{g:b,__dirname:c}=a;a.n(a.i(156787))},57545:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(11814),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["settings",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/settings/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/settings/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},233787:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(11814),a.i(660874),a.i(715847),a.i(781045),a.i(57545)},51936:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(57545)},670946:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(233787);var d=a.i(51936)},910569:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SettingsForm:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call SettingsForm() from the server but SettingsForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/settings/settings-form.tsx <module evaluation>","SettingsForm")}},991835:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SettingsForm:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call SettingsForm() from the server but SettingsForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/settings/settings-form.tsx","SettingsForm")}},903825:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(910569);var d=a.i(991835);a.n(d)},573744:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],c=(0,d.default)("settings",b)}},392641:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Settings:()=>d.default});var d=a.i(573744)},156787:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>m});var d=a.i(129629),e=a.i(76803),f=a.i(903825),g=a.i(729149);a.i(622427);var h=a.i(766719),i=a.i(331055),j=a.i(99492),k=a.i(954124),l=a.i(392641);async function m(){let a=await (0,e.verifySession)();a||(0,h.redirect)("/login");let b=await (0,g.createClient)(),{data:c,error:m}=await b.from("doctors").select("name, email").eq("id",a.userId).single();return(m||!c)&&(0,h.redirect)("/dashboard"),(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden",children:[(0,d.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"}),(0,d.jsx)("div",{className:"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"})]}),(0,d.jsx)("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(j.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold",children:"Celer AI"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.default,{href:"/dashboard",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Dashboard"}),(0,d.jsx)(i.default,{href:"/info",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Analytics"})]})]})}),(0,d.jsxs)("div",{className:"relative max-w-4xl mx-auto py-32 px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4",children:(0,d.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse",children:"Settings"})}),(0,d.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12",children:[(0,d.jsx)(l.Settings,{className:"w-4 h-4 text-indigo-600 animate-pulse"}),(0,d.jsx)("span",{className:"text-indigo-700 text-sm font-medium",children:"Customize Your Experience"}),(0,d.jsx)(k.Sparkles,{className:"w-4 h-4 text-purple-600"})]})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"}),(0,d.jsx)("div",{className:"relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20",children:(0,d.jsx)(f.SettingsForm,{doctorId:a.userId,doctorName:c.name,doctorEmail:c.email})})]})]})]})}},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__65230092._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__1e050229._.js.map