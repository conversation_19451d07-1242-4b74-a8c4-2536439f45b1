module.exports={824544:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(974023)},820188:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({RouteKind:()=>d});var d=function(a){return a.PAGES="PAGES",a.PAGES_API="PAGES_API",a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.IMAGE="IMAGE",a}({})},836087:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({hoist:()=>function a(b,c){return c in b?b[c]:"then"in b&&"function"==typeof b.then?b.then(b=>a(b,c)):"function"==typeof b&&"default"===c?b:void 0}})},551556:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(f=function(a){return a?c:b})(a)}e._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=f(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=e?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}},616038:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return f}});let b=a.r(338005),c="undefined"==typeof window,d=c?()=>{}:b.useLayoutEffect,g=c?()=>{}:b.useEffect;function f(a){let{headManager:e,reduceComponentsToState:f}=a;function h(){if(e&&e.mountedInstances){let c=b.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(f(c,a))}}if(c){var i;null==e||null==(i=e.mountedInstances)||i.add(a.children),h()}return d(()=>{var b;return null==e||null==(b=e.mountedInstances)||b.add(a.children),()=>{var b;null==e||null==(b=e.mountedInstances)||b.delete(a.children)}}),d(()=>(e&&(e._pendingUpdate=h),()=>{e&&(e._pendingUpdate=h)})),g(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}}},589874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";d.exports=a.r(15583).vendored.contexts.AmpContext},936911:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";d.exports=a.r(15583).vendored.contexts.HeadManagerContext},8016:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";function f(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return f}})},254299:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"warnOnce",{enumerable:!0,get:function(){return a}});let a=a=>{}}},114284:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={default:function(){return r},defaultHead:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(666662),c=a.r(551556),k=a.r(157739),l=c._(a.r(338005)),m=b._(a.r(616038)),n=a.r(589874),o=a.r(936911),p=a.r(8016);function h(a){void 0===a&&(a=!1);let b=[(0,k.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,k.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function i(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===l.default.Fragment?a.concat(l.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}a.r(254299);let q=["name","httpEquiv","charSet","itemProp"];function j(a,b){let{inAmpMode:c}=b;return a.reduce(i,[]).reverse().concat(h(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=q.length;a<b;a++){let b=q[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let d=a.key||b;if(process.env.__NEXT_OPTIMIZE_FONTS&&!c&&"link"===a.type&&a.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(b=>a.props.href.startsWith(b))){let b={...a.props||{}};return b["data-href"]=b.href,b.href=void 0,b["data-optimized-fonts"]=!0,l.default.cloneElement(a,b)}return l.default.cloneElement(a,{key:d})})}let r=function(a){let{children:b}=a,c=(0,l.useContext)(n.AmpStateContext),d=(0,l.useContext)(o.HeadManagerContext);return(0,k.jsx)(m.default,{reduceComponentsToState:j,headManager:d,inAmpMode:(0,p.isInAmpMode)(c),children:b})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},797969:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={NEXT_REQUEST_META:function(){return a},addRequestMeta:function(){return j},getRequestMeta:function(){return h},removeRequestMeta:function(){return k},setRequestMeta:function(){return i}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a=Symbol.for("NextInternalRequestMeta");function h(b,c){let d=b[a]||{};return"string"==typeof c?d[c]:d}function i(b,c){return b[a]=c,c}function j(a,b,c){let d=h(a);return d[b]=c,i(a,d)}function k(a,b){let c=h(a);return delete c[b],i(a,c)}}},104210:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return k}});let b=a.r(666662),c=a.r(157739),g=b._(a.r(338005)),h=b._(a.r(114284)),i={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function f(b){let c,{req:d,res:e,err:f}=b,g=e&&e.statusCode?e.statusCode:f?f.statusCode:404;if("undefined"!=typeof window)c=window.location.hostname;else if(d){let{getRequestMeta:b}=a.r(797969),e=b(d,"initURL");e&&(c=new URL(e).hostname)}return{statusCode:g,hostname:c}}let j={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class k extends g.default.Component{render(){let{statusCode:a,withDarkMode:b=!0}=this.props,d=this.props.title||i[a]||"An unexpected error has occurred";return(0,c.jsxs)("div",{style:j.error,children:[(0,c.jsx)(h.default,{children:(0,c.jsx)("title",{children:a?a+": "+d:"Application error: a client-side exception has occurred"})}),(0,c.jsxs)("div",{style:j.desc,children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(b?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),a?(0,c.jsx)("h1",{className:"next-error-h1",style:j.h1,children:a}):null,(0,c.jsx)("div",{style:j.wrap,children:(0,c.jsxs)("h2",{style:j.h2,children:[this.props.title||a?d:(0,c.jsxs)(c.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,c.jsxs)(c.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}k.displayName="ErrorPage",k.getInitialProps=f,k.origGetInitialProps=f,("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},385088:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(104210)},710516:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({config:()=>l,default:()=>b,getServerSideProps:()=>k,getStaticPaths:()=>j,getStaticProps:()=>c,reportWebVitals:()=>m,routeModule:()=>s,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>r,unstable_getStaticParams:()=>p,unstable_getStaticPaths:()=>o,unstable_getStaticProps:()=>n});var d=a.i(824544),e=a.i(820188),f=a.i(836087),g=a.i(945253),h=a.i(552731),i=a.i(385088);let b=(0,f.hoist)(i,"default"),c=(0,f.hoist)(i,"getStaticProps"),j=(0,f.hoist)(i,"getStaticPaths"),k=(0,f.hoist)(i,"getServerSideProps"),l=(0,f.hoist)(i,"config"),m=(0,f.hoist)(i,"reportWebVitals"),n=(0,f.hoist)(i,"unstable_getStaticProps"),o=(0,f.hoist)(i,"unstable_getStaticPaths"),p=(0,f.hoist)(i,"unstable_getStaticParams"),q=(0,f.hoist)(i,"unstable_getServerProps"),r=(0,f.hoist)(i,"unstable_getServerSideProps"),s=new d.PagesRouteModule({definition:{kind:e.RouteKind.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:h.default,Document:g.default},userland:i})}}};

//# sourceMappingURL=node_modules_ad3e0e0e._.js.map