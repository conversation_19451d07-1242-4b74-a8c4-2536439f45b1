{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/querystring.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/format-url.ts", "turbopack:///[project]/node_modules/next/src/client/use-merged-ref.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/parse-path.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts", "turbopack:///[project]/node_modules/next/src/client/normalize-trailing-slash.ts", "turbopack:///[project]/node_modules/next/src/client/add-base-path.ts", "turbopack:///[project]/node_modules/next/src/client/components/app-router-headers.ts", "turbopack:///[project]/node_modules/next/src/client/flight-data-helpers.ts", "turbopack:///[project]/node_modules/next/src/client/app-build-id.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/hash.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/set-cache-busting-search-param.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/fetch-server-response.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/create-href-from-url.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/create-router-cache-key.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts", "turbopack:///[project]/node_modules/next/src/client/components/match-segments.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/invalidate-cache-by-router-state.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/apply-flight-data.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/refetch-inactive-parallel-segments.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/apply-router-state-patch-to-tree.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/should-hard-navigate.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/is-navigating-to-new-root-layout.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/app-paths.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/interception-routes.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/compute-changed-path.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/handle-mutable.ts", "turbopack:///[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_base.cjs", "turbopack:///[project]/node_modules/@swc/helpers/cjs/_class_private_field_loose_key.cjs", "turbopack:///[project]/node_modules/next/src/client/components/promise-queue.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/prefetch-cache-utils.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/prefetch-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation-untracked.ts", "turbopack:///[project]/node_modules/next/src/client/components/nav-failure-handler.ts", "turbopack:///[project]/node_modules/next/src/client/components/error-boundary.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/html-bots.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/is-bot.ts", "turbopack:///[project]/node_modules/next/src/client/components/app-router-announcer.tsx", "turbopack:///[project]/node_modules/next/src/client/components/redirect-boundary.tsx", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/find-head-in-cache.ts", "turbopack:///[project]/node_modules/next/src/client/components/unresolved-thenable.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts", "turbopack:///[project]/node_modules/next/src/client/has-base-path.ts", "turbopack:///[project]/node_modules/next/src/client/remove-base-path.ts", "turbopack:///[project]/node_modules/next/src/client/components/app-router.tsx", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/ppr-navigations.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/aliased-prefetch-navigations.ts", "turbopack:///[project]/node_modules/next/src/client/components/segment-cache.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/navigate-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/server-patch-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/restore-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/handle-segment-mismatch.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/refresh-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/assign-location.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/server-reference-info.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/server-action-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/router-reducer.ts", "turbopack:///[project]/node_modules/next/src/client/components/app-router-instance.ts", "turbopack:///[project]/node_modules/next/src/client/components/links.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/router/utils/is-local-url.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils/error-once.ts", "turbopack:///[project]/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n", "// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n", "import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n", "import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n", "export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n", "import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map(getFlightDataPartsFromPath)\n}\n", "// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n", "// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n", "'use client'\nimport { hexHash } from '../../../shared/lib/hash'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = hexHash(\n    [\n      headers[NEXT_ROUTER_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_STATE_TREE_HEADER],\n      headers[NEXT_URL],\n    ].join(',')\n  )\n\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n  const pairs = rawQuery.split('&').filter(Boolean)\n  pairs.push(`${NEXT_RSC_UNION_QUERY}=${uniqueCacheKey}`)\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n", "'use client'\n\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nexport function urlToUrlWithoutFlightMarker(url: string): URL {\n  const urlWithoutFlightParameters = new URL(url, location.origin)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(url).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n      JSON.stringify(flightRouterState)\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(res.url)\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeader = res.headers.get(NEXT_ROUTER_STALE_TIME_HEADER)\n    const staleTime =\n      staleTimeHeader !== null ? parseInt(staleTimeHeader, 10) : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await require('../react-dev-overlay/app/hot-reloader-client').waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\nexport function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n) {\n  const fetchUrl = new URL(url)\n\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n  setCacheBustingSearchParam(fetchUrl, headers)\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  return fetch(fetchUrl, {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  })\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n", "export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n", "import type { Segment } from '../../../server/app-render/types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightSegmentPath } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\n\n/**\n * Fill cache up to the end of the flightSegmentPath, invalidating anything below it.\n */\nexport function invalidateCacheBelowFlightSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n  const [parallelRouteKey, segment] = flightSegmentPath\n\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!existingChildSegmentMap) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  // In case of last entry don't copy further down.\n  if (isLastEntry) {\n    childSegmentMap.delete(cacheKey)\n    return\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  invalidateCacheBelowFlightSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  CacheNodeSeedData,\n} from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n} from './router-reducer-types'\n\nexport function fillLazyItemsTillLeafWithHead(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode | undefined,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null,\n  head: React.ReactNode,\n  prefetchEntry: PrefetchCacheEntry | undefined\n): void {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    newCache.head = head\n    return\n  }\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    // TODO: We should traverse the cacheNodeSeedData tree instead of the router\n    // state tree. Ideally, they would always be the same shape, but because of\n    // the loading.js pattern, cacheNodeSeedData sometimes only represents a\n    // partial tree. That's why this node is sometimes null. Once PPR lands,\n    // loading.js will no longer have special behavior and we can traverse the\n    // data tree instead.\n    //\n    // We should also consider merging the router state tree and the data tree\n    // in the response format, so that we don't have to send the keys twice.\n    // Then the client can convert them into separate representations.\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n    if (existingCache) {\n      const existingParallelRoutesCacheNode =\n        existingCache.parallelRoutes.get(key)\n      if (existingParallelRoutesCacheNode) {\n        const hasReusablePrefetch =\n          prefetchEntry?.kind === 'auto' &&\n          prefetchEntry.status === PrefetchCacheEntryStatus.reusable\n\n        let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n        const existingCacheNode = parallelRouteCacheNode.get(cacheKey)\n        let newCacheNode: CacheNode\n        if (parallelSeedData !== null) {\n          // New data was sent from the server.\n          const seedNode = parallelSeedData[1]\n          const loading = parallelSeedData[3]\n          newCacheNode = {\n            lazyData: null,\n            rsc: seedNode,\n            // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n            // this path during a navigation, but until PPR is fully implemented\n            // yet it's possible the existing node does have a non-null\n            // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n            // old behavior — no PPR value.\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            loading,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            navigatedAt,\n          }\n        } else if (hasReusablePrefetch && existingCacheNode) {\n          // No new data was sent from the server, but the existing cache node\n          // was prefetched, so we should reuse that.\n          newCacheNode = {\n            lazyData: existingCacheNode.lazyData,\n            rsc: existingCacheNode.rsc,\n            // This is a PPR-only field. Unlike the previous branch, since we're\n            // just cloning the existing cache node, we might as well keep the\n            // PPR value, if it exists.\n            prefetchRsc: existingCacheNode.prefetchRsc,\n            head: existingCacheNode.head,\n            prefetchHead: existingCacheNode.prefetchHead,\n            parallelRoutes: new Map(existingCacheNode.parallelRoutes),\n            loading: existingCacheNode.loading,\n          } as CacheNode\n        } else {\n          // No data available for this node. This will trigger a lazy fetch\n          // during render.\n          newCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            loading: null,\n            navigatedAt,\n          }\n        }\n\n        // Overrides the cache key with the new cache node.\n        parallelRouteCacheNode.set(cacheKey, newCacheNode)\n        // Traverse deeper to apply the head / fill lazy items till the head.\n        fillLazyItemsTillLeafWithHead(\n          navigatedAt,\n          newCacheNode,\n          existingCacheNode,\n          parallelRouteState,\n          parallelSeedData ? parallelSeedData : null,\n          head,\n          prefetchEntry\n        )\n\n        newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n        continue\n      }\n    }\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const seedNode = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        rsc: seedNode,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      newCacheNode,\n      undefined,\n      parallelRouteState,\n      parallelSeedData,\n      head,\n      prefetchEntry\n    )\n  }\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightRouterState } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * Invalidate cache one level down from the router state.\n */\nexport function invalidateCacheByRouterState(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState\n): void {\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const segmentForParallelRoute = routerState[1][key][0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n    const existingParallelRoutesCacheNode =\n      existingCache.parallelRoutes.get(key)\n    if (existingParallelRoutesCacheNode) {\n      let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n      parallelRouteCacheNode.delete(cacheKey)\n      newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n    }\n  }\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { Segment } from '../../../server/app-render/types'\nimport { invalidateCacheByRouterState } from './invalidate-cache-by-router-state'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\n/**\n * Common logic for filling cache with new sub tree data.\n */\nfunction fillCacheHelper(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry: PrefetchCacheEntry | undefined,\n  fillLazyItems: boolean\n): void {\n  const {\n    segmentPath,\n    seedData: cacheNodeSeedData,\n    tree: treePatch,\n    head,\n  } = flightData\n  let newCacheNode = newCache\n  let existingCacheNode = existingCache\n\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n\n    // segmentPath is a repeating tuple of parallelRouteKey and segment\n    // we know we've hit the last entry we've reached our final pair\n    const isLastEntry = i === segmentPath.length - 2\n    const cacheKey = createRouterCacheKey(segment)\n\n    const existingChildSegmentMap =\n      existingCacheNode.parallelRoutes.get(parallelRouteKey)\n\n    if (!existingChildSegmentMap) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    let childSegmentMap = newCacheNode.parallelRoutes.get(parallelRouteKey)\n    if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n      childSegmentMap = new Map(existingChildSegmentMap)\n      newCacheNode.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n    }\n\n    const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n    let childCacheNode = childSegmentMap.get(cacheKey)\n\n    if (isLastEntry) {\n      if (\n        cacheNodeSeedData &&\n        (!childCacheNode ||\n          !childCacheNode.lazyData ||\n          childCacheNode === existingChildCacheNode)\n      ) {\n        const incomingSegment = cacheNodeSeedData[0]\n        const rsc = cacheNodeSeedData[1]\n        const loading = cacheNodeSeedData[3]\n\n        childCacheNode = {\n          lazyData: null,\n          // When `fillLazyItems` is false, we only want to fill the RSC data for the layout,\n          // not the page segment.\n          rsc:\n            fillLazyItems || incomingSegment !== PAGE_SEGMENT_KEY ? rsc : null,\n          prefetchRsc: null,\n          head: null,\n          prefetchHead: null,\n          loading,\n          parallelRoutes:\n            fillLazyItems && existingChildCacheNode\n              ? new Map(existingChildCacheNode.parallelRoutes)\n              : new Map(),\n          navigatedAt,\n        }\n\n        if (existingChildCacheNode && fillLazyItems) {\n          invalidateCacheByRouterState(\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch\n          )\n        }\n        if (fillLazyItems) {\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            prefetchEntry\n          )\n        }\n\n        childSegmentMap.set(cacheKey, childCacheNode)\n      }\n      continue\n    }\n\n    if (!childCacheNode || !existingChildCacheNode) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    if (childCacheNode === existingChildCacheNode) {\n      childCacheNode = {\n        lazyData: childCacheNode.lazyData,\n        rsc: childCacheNode.rsc,\n        prefetchRsc: childCacheNode.prefetchRsc,\n        head: childCacheNode.head,\n        prefetchHead: childCacheNode.prefetchHead,\n        parallelRoutes: new Map(childCacheNode.parallelRoutes),\n        loading: childCacheNode.loading,\n      } as CacheNode\n      childSegmentMap.set(cacheKey, childCacheNode)\n    }\n\n    // Move deeper into the cache nodes\n    newCacheNode = childCacheNode\n    existingCacheNode = existingChildCacheNode\n  }\n}\n\n/**\n * Fill cache with rsc based on flightDataPath\n */\nexport function fillCacheWithNewSubTreeData(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    true\n  )\n}\n\nexport function fillCacheWithNewSubTreeDataButOnlyLoading(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    false\n  )\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { fillCacheWithNewSubTreeData } from './fill-cache-with-new-subtree-data'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\nexport function applyFlightData(\n  navigatedAt: number,\n  existingCache: CacheNode,\n  cache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): boolean {\n  // The one before last item is the router state tree patch\n  const { tree: treePatch, seedData, head, isRootRender } = flightData\n\n  // Handles case where prefetch only returns the router tree patch without rendered components.\n  if (seedData === null) {\n    return false\n  }\n\n  if (isRootRender) {\n    const rsc = seedData[1]\n    const loading = seedData[3]\n    cache.loading = loading\n    cache.rsc = rsc\n    // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n    // this path during a navigation, but until PPR is fully implemented\n    // yet it's possible the existing node does have a non-null\n    // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n    // old behavior — no PPR value.\n    cache.prefetchRsc = null\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      existingCache,\n      treePatch,\n      seedData,\n      head,\n      prefetchEntry\n    )\n  } else {\n    // Copy rsc for the root node of the cache.\n    cache.rsc = existingCache.rsc\n    // This is a PPR-only field. Unlike the previous branch, since we're\n    // just cloning the existing cache node, we might as well keep the\n    // PPR value, if it exists.\n    cache.prefetchRsc = existingCache.prefetchRsc\n    cache.parallelRoutes = new Map(existingCache.parallelRoutes)\n    cache.loading = existingCache.loading\n    // Create a copy of the existing cache with the rsc applied.\n    fillCacheWithNewSubTreeData(\n      navigatedAt,\n      cache,\n      existingCache,\n      flightData,\n      prefetchEntry\n    )\n  }\n\n  return true\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterState } from './router-reducer-types'\nimport { applyFlightData } from './apply-flight-data'\nimport { fetchServerResponse } from './fetch-server-response'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\ninterface RefreshInactiveParallelSegments {\n  navigatedAt: number\n  state: AppRouterState\n  updatedTree: FlightRouterState\n  updatedCache: CacheNode\n  includeNextUrl: boolean\n  canonicalUrl: string\n}\n\n/**\n * Refreshes inactive segments that are still in the current FlightRouterState.\n * A segment is considered \"inactive\" when the server response indicates it didn't match to a page component.\n * This happens during a soft-navigation, where the server will want to patch in the segment\n * with the \"default\" component, but we explicitly ignore the server in this case\n * and keep the existing state for that segment. New data for inactive segments are inherently\n * not part of the server response when we patch the tree, because they were associated with a response\n * from an earlier navigation/request. For each segment, once it becomes \"active\", we encode the URL that provided\n * the data for it. This function traverses parallel routes looking for these markers so that it can re-fetch\n * and patch the new data into the tree.\n */\nexport async function refreshInactiveParallelSegments(\n  options: RefreshInactiveParallelSegments\n) {\n  const fetchedSegments = new Set<string>()\n  await refreshInactiveParallelSegmentsImpl({\n    ...options,\n    rootTree: options.updatedTree,\n    fetchedSegments,\n  })\n}\n\nasync function refreshInactiveParallelSegmentsImpl({\n  navigatedAt,\n  state,\n  updatedTree,\n  updatedCache,\n  includeNextUrl,\n  fetchedSegments,\n  rootTree = updatedTree,\n  canonicalUrl,\n}: RefreshInactiveParallelSegments & {\n  fetchedSegments: Set<string>\n  rootTree: FlightRouterState\n}) {\n  const [, parallelRoutes, refetchPath, refetchMarker] = updatedTree\n  const fetchPromises = []\n\n  if (\n    refetchPath &&\n    refetchPath !== canonicalUrl &&\n    refetchMarker === 'refresh' &&\n    // it's possible for the tree to contain multiple segments that contain data at the same URL\n    // we keep track of them so we can dedupe the requests\n    !fetchedSegments.has(refetchPath)\n  ) {\n    fetchedSegments.add(refetchPath) // Mark this URL as fetched\n\n    // Eagerly kick off the fetch for the refetch path & the parallel routes. This should be fine to do as they each operate\n    // independently on their own cache nodes, and `applyFlightData` will copy anything it doesn't care about from the existing cache.\n    const fetchPromise = fetchServerResponse(\n      new URL(refetchPath, location.origin),\n      {\n        // refetch from the root of the updated tree, otherwise it will be scoped to the current segment\n        // and might not contain the data we need to patch in interception route data (such as dynamic params from a previous segment)\n        flightRouterState: [rootTree[0], rootTree[1], rootTree[2], 'refetch'],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n      }\n    ).then(({ flightData }) => {\n      if (typeof flightData !== 'string') {\n        for (const flightDataPath of flightData) {\n          // we only pass the new cache as this function is called after clearing the router cache\n          // and filling in the new page data from the server. Meaning the existing cache is actually the cache that's\n          // just been created & has been written to, but hasn't been \"committed\" yet.\n          applyFlightData(\n            navigatedAt,\n            updatedCache,\n            updatedCache,\n            flightDataPath\n          )\n        }\n      } else {\n        // When flightData is a string, it suggests that the server response should have triggered an MPA navigation\n        // I'm not 100% sure of this decision, but it seems unlikely that we'd want to introduce a redirect side effect\n        // when refreshing on-screen data, so handling this has been ommitted.\n      }\n    })\n\n    fetchPromises.push(fetchPromise)\n  }\n\n  for (const key in parallelRoutes) {\n    const parallelFetchPromise = refreshInactiveParallelSegmentsImpl({\n      navigatedAt,\n      state,\n      updatedTree: parallelRoutes[key],\n      updatedCache,\n      includeNextUrl,\n      fetchedSegments,\n      rootTree,\n      canonicalUrl,\n    })\n\n    fetchPromises.push(parallelFetchPromise)\n  }\n\n  await Promise.all(fetchPromises)\n}\n\n/**\n * Walks the current parallel segments to determine if they are \"active\".\n * An active parallel route will have a `__PAGE__` segment in the FlightRouterState.\n * As opposed to a `__DEFAULT__` segment, which means there was no match for that parallel route.\n * We add a special marker here so that we know how to refresh its data when the router is revalidated.\n */\nexport function addRefreshMarkerToActiveParallelSegments(\n  tree: FlightRouterState,\n  path: string\n) {\n  const [segment, parallelRoutes, , refetchMarker] = tree\n  // a page segment might also contain concatenated search params, so we do a partial match on the key\n  if (segment.includes(PAGE_SEGMENT_KEY) && refetchMarker !== 'refresh') {\n    tree[2] = path\n    tree[3] = 'refresh'\n  }\n\n  for (const key in parallelRoutes) {\n    addRefreshMarkerToActiveParallelSegments(parallelRoutes[key], path)\n  }\n}\n", "import type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\n\n/**\n * Deep merge of the two router states. Parallel route keys are preserved if the patch doesn't have them.\n */\nfunction applyPatch(\n  initialTree: FlightRouterState,\n  patchTree: FlightRouterState\n): FlightRouterState {\n  const [initialSegment, initialParallelRoutes] = initialTree\n  const [patchSegment, patchParallelRoutes] = patchTree\n\n  // if the applied patch segment is __DEFAULT__ then it can be ignored in favor of the initial tree\n  // this is because the __DEFAULT__ segment is used as a placeholder on navigation\n  if (\n    patchSegment === DEFAULT_SEGMENT_KEY &&\n    initialSegment !== DEFAULT_SEGMENT_KEY\n  ) {\n    return initialTree\n  }\n\n  if (matchSegment(initialSegment, patchSegment)) {\n    const newParallelRoutes: FlightRouterState[1] = {}\n    for (const key in initialParallelRoutes) {\n      const isInPatchTreeParallelRoutes =\n        typeof patchParallelRoutes[key] !== 'undefined'\n      if (isInPatchTreeParallelRoutes) {\n        newParallelRoutes[key] = applyPatch(\n          initialParallelRoutes[key],\n          patchParallelRoutes[key]\n        )\n      } else {\n        newParallelRoutes[key] = initialParallelRoutes[key]\n      }\n    }\n\n    for (const key in patchParallelRoutes) {\n      if (newParallelRoutes[key]) {\n        continue\n      }\n\n      newParallelRoutes[key] = patchParallelRoutes[key]\n    }\n\n    const tree: FlightRouterState = [initialSegment, newParallelRoutes]\n\n    // Copy over the existing tree\n    if (initialTree[2]) {\n      tree[2] = initialTree[2]\n    }\n\n    if (initialTree[3]) {\n      tree[3] = initialTree[3]\n    }\n\n    if (initialTree[4]) {\n      tree[4] = initialTree[4]\n    }\n\n    return tree\n  }\n\n  return patchTree\n}\n\n/**\n * Apply the router state from the Flight response, but skip patching default segments.\n * Useful for patching the router cache when navigating, where we persist the existing default segment if there isn't a new one.\n * Creates a new router state tree.\n */\nexport function applyRouterStatePatchToTree(\n  flightSegmentPath: FlightSegmentPath,\n  flightRouterState: FlightRouterState,\n  treePatch: FlightRouterState,\n  path: string\n): FlightRouterState | null {\n  const [segment, parallelRoutes, url, refetch, isRootLayout] =\n    flightRouterState\n\n  // Root refresh\n  if (flightSegmentPath.length === 1) {\n    const tree: FlightRouterState = applyPatch(flightRouterState, treePatch)\n\n    addRefreshMarkerToActiveParallelSegments(tree, path)\n\n    return tree\n  }\n\n  const [currentSegment, parallelRouteKey] = flightSegmentPath\n\n  // Tree path returned from the server should always match up with the current tree in the browser\n  if (!matchSegment(currentSegment, segment)) {\n    return null\n  }\n\n  const lastSegment = flightSegmentPath.length === 2\n\n  let parallelRoutePatch\n  if (lastSegment) {\n    parallelRoutePatch = applyPatch(parallelRoutes[parallelRouteKey], treePatch)\n  } else {\n    parallelRoutePatch = applyRouterStatePatchToTree(\n      getNextFlightSegmentPath(flightSegmentPath),\n      parallelRoutes[parallelRouteKey],\n      treePatch,\n      path\n    )\n\n    if (parallelRoutePatch === null) {\n      return null\n    }\n  }\n\n  const tree: FlightRouterState = [\n    flightSegmentPath[0],\n    {\n      ...parallelRoutes,\n      [parallelRouteKey]: parallelRoutePatch,\n    },\n    url,\n    refetch,\n  ]\n\n  // Current segment is the root layout\n  if (isRootLayout) {\n    tree[4] = true\n  }\n\n  addRefreshMarkerToActiveParallelSegments(tree, path)\n\n  return tree\n}\n", "import type {\n  FlightRouterState,\n  FlightDataPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\n\n// TODO-APP: flightSegmentPath will be empty in case of static response, needs to be handled.\nexport function shouldHardNavigate(\n  flightSegmentPath: FlightDataPath,\n  flightRouterState: FlightRouterState\n): boolean {\n  const [segment, parallelRoutes] = flightRouterState\n  // TODO-APP: Check if `as` can be replaced.\n  const [currentSegment, parallelRouteKey] = flightSegmentPath as [\n    Segment,\n    string,\n  ]\n\n  // Check if current segment matches the existing segment.\n  if (!matchSegment(currentSegment, segment)) {\n    // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n    if (Array.isArray(currentSegment)) {\n      return true\n    }\n\n    // If the existing segment did not match soft navigation is triggered.\n    return false\n  }\n  const lastSegment = flightSegmentPath.length <= 2\n\n  if (lastSegment) {\n    return false\n  }\n\n  return shouldHardNavigate(\n    getNextFlightSegmentPath(flightSegmentPath),\n    parallelRoutes[parallelRouteKey]\n  )\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\n\nexport function isNavigatingToNewRootLayout(\n  currentTree: FlightRouterState,\n  nextTree: FlightRouterState\n): boolean {\n  // Compare segments\n  const currentTreeSegment = currentTree[0]\n  const nextTreeSegment = nextTree[0]\n\n  // If any segment is different before we find the root layout, the root layout has changed.\n  // E.g. /same/(group1)/layout.js -> /same/(group2)/layout.js\n  // First segment is 'same' for both, keep looking. (group1) changed to (group2) before the root layout was found, it must have changed.\n  if (Array.isArray(currentTreeSegment) && Array.isArray(nextTreeSegment)) {\n    // Compare dynamic param name and type but ignore the value, different values would not affect the current root layout\n    // /[name] - /slug1 and /slug2, both values (slug1 & slug2) still has the same layout /[name]/layout.js\n    if (\n      currentTreeSegment[0] !== nextTreeSegment[0] ||\n      currentTreeSegment[2] !== nextTreeSegment[2]\n    ) {\n      return true\n    }\n  } else if (currentTreeSegment !== nextTreeSegment) {\n    return true\n  }\n\n  // Current tree root layout found\n  if (currentTree[4]) {\n    // If the next tree doesn't have the root layout flag, it must have changed.\n    return !nextTree[4]\n  }\n  // Current tree didn't have its root layout here, must have changed.\n  if (nextTree[4]) {\n    return true\n  }\n  // We can't assume it's `parallelRoutes.children` here in case the root layout is `app/@something/layout.js`\n  // But it's not possible to be more than one parallelRoutes before the root layout is found\n  // TODO-APP: change to traverse all parallel routes\n  const currentTreeChild = Object.values(currentTree[1])[0]\n  const nextTreeChild = Object.values(nextTree[1])[0]\n  if (!currentTreeChild || !nextTreeChild) return true\n  return isNavigatingToNewRootLayout(currentTreeChild, nextTreeChild)\n}\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n", "import { computeChangedPath } from './compute-changed-path'\nimport type {\n  Mu<PERSON>,\n  ReadonlyReducerState,\n  ReducerState,\n} from './router-reducer-types'\n\nfunction isNotUndefined<T>(value: T): value is Exclude<T, undefined> {\n  return typeof value !== 'undefined'\n}\n\nexport function handleMutable(\n  state: ReadonlyReducerState,\n  mutable: Mutable\n): ReducerState {\n  // shouldScroll is true by default, can override to false.\n  const shouldScroll = mutable.shouldScroll ?? true\n\n  let nextUrl = state.nextUrl\n\n  if (isNotUndefined(mutable.patchedTree)) {\n    // If we received a patched tree, we need to compute the changed path.\n    const changedPath = computeChangedPath(state.tree, mutable.patchedTree)\n    if (changedPath) {\n      // If the tree changed, we need to update the nextUrl\n      nextUrl = changedPath\n    } else if (!nextUrl) {\n      // if the tree ends up being the same (ie, no changed path), and we don't have a nextUrl, then we should use the canonicalUrl\n      nextUrl = state.canonicalUrl\n    }\n    // otherwise this will be a no-op and continue to use the existing nextUrl\n  }\n\n  return {\n    // Set href.\n    canonicalUrl: isNotUndefined(mutable.canonicalUrl)\n      ? mutable.canonicalUrl === state.canonicalUrl\n        ? state.canonicalUrl\n        : mutable.canonicalUrl\n      : state.canonicalUrl,\n    pushRef: {\n      pendingPush: isNotUndefined(mutable.pendingPush)\n        ? mutable.pendingPush\n        : state.pushRef.pendingPush,\n      mpaNavigation: isNotUndefined(mutable.mpaNavigation)\n        ? mutable.mpaNavigation\n        : state.pushRef.mpaNavigation,\n      preserveCustomHistoryState: isNotUndefined(\n        mutable.preserveCustomHistoryState\n      )\n        ? mutable.preserveCustomHistoryState\n        : state.pushRef.preserveCustomHistoryState,\n    },\n    // All navigation requires scroll and focus management to trigger.\n    focusAndScrollRef: {\n      apply: shouldScroll\n        ? isNotUndefined(mutable?.scrollableSegments)\n          ? true\n          : state.focusAndScrollRef.apply\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          false,\n      onlyHashChange: mutable.onlyHashChange || false,\n      hashFragment: shouldScroll\n        ? // Empty hash should trigger default behavior of scrolling layout into view.\n          // #top is handled in layout-router.\n          mutable.hashFragment && mutable.hashFragment !== ''\n          ? // Remove leading # and decode hash to make non-latin hashes work.\n            decodeURIComponent(mutable.hashFragment.slice(1))\n          : state.focusAndScrollRef.hashFragment\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          null,\n      segmentPaths: shouldScroll\n        ? mutable?.scrollableSegments ?? state.focusAndScrollRef.segmentPaths\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          [],\n    },\n    // Apply cache.\n    cache: mutable.cache ? mutable.cache : state.cache,\n    prefetchCache: mutable.prefetchCache\n      ? mutable.prefetchCache\n      : state.prefetchCache,\n    // Apply patched router state.\n    tree: isNotUndefined(mutable.patchedTree)\n      ? mutable.patchedTree\n      : state.tree,\n    nextUrl,\n  }\n}\n", "\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n", "\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n", "/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n", "import {\n  fetchServerResponse,\n  type FetchServerResponseResult,\n} from './fetch-server-response'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n  PrefetchKind,\n  type ReadonlyReducerState,\n} from './router-reducer-types'\nimport { prefetchQueue } from './reducers/prefetch-reducer'\n\nconst INTERCEPTION_CACHE_KEY_MARKER = '%'\n\nexport type AliasedPrefetchCacheEntry = PrefetchCacheEntry & {\n  /** This is a special property that indicates a prefetch entry associated with a different URL\n   * was returned rather than the requested URL. This signals to the router that it should only\n   * apply the part that doesn't depend on searchParams (specifically the loading state).\n   */\n  aliased?: boolean\n}\n\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */\nfunction createPrefetchCacheKeyImpl(\n  url: URL,\n  includeSearchParams: boolean,\n  prefix?: string | null\n) {\n  // Initially we only use the pathname as the cache key. We don't want to include\n  // search params so that multiple URLs with the same search parameter can re-use\n  // loading states.\n  let pathnameFromUrl = url.pathname\n\n  // RSC responses can differ based on search params, specifically in the case where we aren't\n  // returning a partial response (ie with `PrefetchKind.AUTO`).\n  // In the auto case, since loading.js & layout.js won't have access to search params,\n  // we can safely re-use that cache entry. But for full prefetches, we should not\n  // re-use the cache entry as the response may differ.\n  if (includeSearchParams) {\n    // if we have a full prefetch, we can include the search param in the key,\n    // as we'll be getting back a full response. The server might have read the search\n    // params when generating the full response.\n    pathnameFromUrl += url.search\n  }\n\n  if (prefix) {\n    return `${prefix}${INTERCEPTION_CACHE_KEY_MARKER}${pathnameFromUrl}`\n  }\n\n  return pathnameFromUrl\n}\n\nfunction createPrefetchCacheKey(\n  url: URL,\n  kind: PrefetchKind | undefined,\n  nextUrl?: string | null\n) {\n  return createPrefetchCacheKeyImpl(url, kind === PrefetchKind.FULL, nextUrl)\n}\n\nfunction getExistingCacheEntry(\n  url: URL,\n  kind: PrefetchKind = PrefetchKind.TEMPORARY,\n  nextUrl: string | null,\n  prefetchCache: Map<string, PrefetchCacheEntry>,\n  allowAliasing: boolean\n): AliasedPrefetchCacheEntry | undefined {\n  // We first check if there's a more specific interception route prefetch entry\n  // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n  // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n  for (const maybeNextUrl of [nextUrl, null]) {\n    const cacheKeyWithParams = createPrefetchCacheKeyImpl(\n      url,\n      true,\n      maybeNextUrl\n    )\n    const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(\n      url,\n      false,\n      maybeNextUrl\n    )\n\n    // First, we check if we have a cache entry that exactly matches the URL\n    const cacheKeyToUse = url.search\n      ? cacheKeyWithParams\n      : cacheKeyWithoutParams\n\n    const existingEntry = prefetchCache.get(cacheKeyToUse)\n    if (existingEntry && allowAliasing) {\n      // We know we're returning an aliased entry when the pathname matches but the search params don't,\n      const isAliased =\n        existingEntry.url.pathname === url.pathname &&\n        existingEntry.url.search !== url.search\n\n      if (isAliased) {\n        return {\n          ...existingEntry,\n          aliased: true,\n        }\n      }\n\n      return existingEntry\n    }\n\n    // If the request contains search params, and we're not doing a full prefetch, we can return the\n    // param-less entry if it exists.\n    // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n    // but lets us arrive there quicker in the param-full case.\n    const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams)\n    if (\n      process.env.NODE_ENV !== 'development' &&\n      allowAliasing &&\n      url.search &&\n      kind !== PrefetchKind.FULL &&\n      entryWithoutParams &&\n      // We shouldn't return the aliased entry if it was relocated to a new cache key.\n      // Since it's rewritten, it could respond with a completely different loading state.\n      !entryWithoutParams.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n    ) {\n      return { ...entryWithoutParams, aliased: true }\n    }\n  }\n\n  // If we've gotten to this point, we didn't find a specific cache entry that matched\n  // the request URL.\n  // We attempt a partial match by checking if there's a cache entry with the same pathname.\n  // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n  // This will signal to the router that it should only apply the loading state on the prefetched data.\n  if (\n    process.env.NODE_ENV !== 'development' &&\n    kind !== PrefetchKind.FULL &&\n    allowAliasing\n  ) {\n    for (const cacheEntry of prefetchCache.values()) {\n      if (\n        cacheEntry.url.pathname === url.pathname &&\n        // We shouldn't return the aliased entry if it was relocated to a new cache key.\n        // Since it's rewritten, it could respond with a completely different loading state.\n        !cacheEntry.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n      ) {\n        return { ...cacheEntry, aliased: true }\n      }\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns a prefetch cache entry if one exists. Otherwise creates a new one and enqueues a fetch request\n * to retrieve the prefetch data from the server.\n */\nexport function getOrCreatePrefetchCacheEntry({\n  url,\n  nextUrl,\n  tree,\n  prefetchCache,\n  kind,\n  allowAliasing = true,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache' | 'tree'> & {\n  url: URL\n  kind?: PrefetchKind\n  allowAliasing: boolean\n}): AliasedPrefetchCacheEntry {\n  const existingCacheEntry = getExistingCacheEntry(\n    url,\n    kind,\n    nextUrl,\n    prefetchCache,\n    allowAliasing\n  )\n\n  if (existingCacheEntry) {\n    // Grab the latest status of the cache entry and update it\n    existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry)\n\n    // when `kind` is provided, an explicit prefetch was requested.\n    // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n    const switchedToFullPrefetch =\n      existingCacheEntry.kind !== PrefetchKind.FULL &&\n      kind === PrefetchKind.FULL\n\n    if (switchedToFullPrefetch) {\n      // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n      // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n      // are seeded but without a prefetch intent)\n      existingCacheEntry.data.then((prefetchResponse) => {\n        const isFullPrefetch =\n          Array.isArray(prefetchResponse.flightData) &&\n          prefetchResponse.flightData.some((flightData) => {\n            // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n            return flightData.isRootRender && flightData.seedData !== null\n          })\n\n        if (!isFullPrefetch) {\n          return createLazyPrefetchEntry({\n            tree,\n            url,\n            nextUrl,\n            prefetchCache,\n            // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n            // rather than assuming the same intent as the previous entry, to be consistent with how we\n            // lazily create prefetch entries when intent is left unspecified.\n            kind: kind ?? PrefetchKind.TEMPORARY,\n          })\n        }\n      })\n    }\n\n    // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n    // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n    if (kind && existingCacheEntry.kind === PrefetchKind.TEMPORARY) {\n      existingCacheEntry.kind = kind\n    }\n\n    // We've determined that the existing entry we found is still valid, so we return it.\n    return existingCacheEntry\n  }\n\n  // If we didn't return an entry, create a new one.\n  return createLazyPrefetchEntry({\n    tree,\n    url,\n    nextUrl,\n    prefetchCache,\n    kind: kind || PrefetchKind.TEMPORARY,\n  })\n}\n\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */\nfunction prefixExistingPrefetchCacheEntry({\n  url,\n  nextUrl,\n  prefetchCache,\n  existingCacheKey,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache'> & {\n  url: URL\n  existingCacheKey: string\n}) {\n  const existingCacheEntry = prefetchCache.get(existingCacheKey)\n  if (!existingCacheEntry) {\n    // no-op -- there wasn't an entry to move\n    return\n  }\n\n  const newCacheKey = createPrefetchCacheKey(\n    url,\n    existingCacheEntry.kind,\n    nextUrl\n  )\n  prefetchCache.set(newCacheKey, { ...existingCacheEntry, key: newCacheKey })\n  prefetchCache.delete(existingCacheKey)\n\n  return newCacheKey\n}\n\n/**\n * Use to seed the prefetch cache with data that has already been fetched.\n */\nexport function createSeededPrefetchCacheEntry({\n  nextUrl,\n  tree,\n  prefetchCache,\n  url,\n  data,\n  kind,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  data: FetchServerResponseResult\n  kind: PrefetchKind\n}) {\n  // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n  // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n  // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n  const prefetchCacheKey = data.couldBeIntercepted\n    ? createPrefetchCacheKey(url, kind, nextUrl)\n    : createPrefetchCacheKey(url, kind)\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data: Promise.resolve(data),\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: Date.now(),\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  } satisfies PrefetchCacheEntry\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */\nfunction createLazyPrefetchEntry({\n  url,\n  kind,\n  tree,\n  nextUrl,\n  prefetchCache,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  kind: PrefetchKind\n}): PrefetchCacheEntry {\n  const prefetchCacheKey = createPrefetchCacheKey(url, kind)\n\n  // initiates the fetch request for the prefetch and attaches a listener\n  // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n  const data = prefetchQueue.enqueue(() =>\n    fetchServerResponse(url, {\n      flightRouterState: tree,\n      nextUrl,\n      prefetchKind: kind,\n    }).then((prefetchResponse) => {\n      // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n      // to avoid drift between this cache key prefixing logic\n      // (which is currently directly influenced by the server response)\n      let newCacheKey\n\n      if (prefetchResponse.couldBeIntercepted) {\n        // Determine if we need to prefix the cache key with the nextUrl\n        newCacheKey = prefixExistingPrefetchCacheEntry({\n          url,\n          existingCacheKey: prefetchCacheKey,\n          nextUrl,\n          prefetchCache,\n        })\n      }\n\n      // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n      // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n      // staleTime.\n      if (prefetchResponse.prerendered) {\n        const existingCacheEntry = prefetchCache.get(\n          // if we prefixed the cache key due to route interception, we want to use the new key. Otherwise we use the original key\n          newCacheKey ?? prefetchCacheKey\n        )\n        if (existingCacheEntry) {\n          existingCacheEntry.kind = PrefetchKind.FULL\n          if (prefetchResponse.staleTime !== -1) {\n            // This is the stale time that was collected by the server during\n            // static generation. Use this in place of the default stale time.\n            existingCacheEntry.staleTime = prefetchResponse.staleTime\n          }\n        }\n      }\n\n      return prefetchResponse\n    })\n  )\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data,\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: null,\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  }\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\nexport function prunePrefetchCache(\n  prefetchCache: ReadonlyReducerState['prefetchCache']\n) {\n  for (const [href, prefetchCacheEntry] of prefetchCache) {\n    if (\n      getPrefetchEntryCacheStatus(prefetchCacheEntry) ===\n      PrefetchCacheEntryStatus.expired\n    ) {\n      prefetchCache.delete(href)\n    }\n  }\n}\n\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nexport const DYNAMIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME) * 1000\n\nexport const STATIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME) * 1000\n\nfunction getPrefetchEntryCacheStatus({\n  kind,\n  prefetchTime,\n  lastUsedTime,\n  staleTime,\n}: PrefetchCacheEntry): PrefetchCacheEntryStatus {\n  if (staleTime !== -1) {\n    // `staleTime` is the value sent by the server during static generation.\n    // When this is available, it takes precedence over any of the heuristics\n    // that follow.\n    //\n    // TODO: When PPR is enabled, the server will *always* return a stale time\n    // when prefetching. We should never use a prefetch entry that hasn't yet\n    // received data from the server. So the only two cases should be 1) we use\n    // the server-generated stale time 2) the unresolved entry is discarded.\n    return Date.now() < prefetchTime + staleTime\n      ? PrefetchCacheEntryStatus.fresh\n      : PrefetchCacheEntryStatus.stale\n  }\n\n  // We will re-use the cache entry data for up to the `dynamic` staletime window.\n  if (Date.now() < (lastUsedTime ?? prefetchTime) + DYNAMIC_STALETIME_MS) {\n    return lastUsedTime\n      ? PrefetchCacheEntryStatus.reusable\n      : PrefetchCacheEntryStatus.fresh\n  }\n\n  // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n  // A stale entry will only re-use the `loading` boundary, not the full data.\n  // This will trigger a \"lazy fetch\" for the full data.\n  if (kind === PrefetchKind.AUTO) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.stale\n    }\n  }\n\n  // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n  if (kind === PrefetchKind.FULL) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.reusable\n    }\n  }\n\n  return PrefetchCacheEntryStatus.expired\n}\n", "import type {\n  PrefetchAction,\n  ReducerState,\n  ReadonlyReducerState,\n} from '../router-reducer-types'\nimport { PromiseQueue } from '../../promise-queue'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nexport const prefetchQueue = new PromiseQueue(5)\n\nexport const prefetchReducer = process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ? identityReducerWhenSegmentCacheIsEnabled\n  : prefetchReducerImpl\n\nfunction identityReducerWhenSegmentCacheIsEnabled<T>(state: T): T {\n  // Unlike the old implementation, the Segment Cache doesn't store its data in\n  // the router reducer state.\n  //\n  // This shouldn't be reachable because we wrap the prefetch API in a check,\n  // too, which prevents the action from being dispatched. But it's here for\n  // clarity + code elimination.\n  return state\n}\n\nfunction prefetchReducerImpl(\n  state: ReadonlyReducerState,\n  action: PrefetchAction\n): ReducerState {\n  // let's prune the prefetch cache before we do anything else\n  prunePrefetchCache(state.prefetchCache)\n\n  const { url } = action\n\n  getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    prefetchCache: state.prefetchCache,\n    kind: action.kind,\n    tree: state.tree,\n    allowAliasing: true,\n  })\n\n  return state\n}\n", "import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n", "import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n", "'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n", "// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n", "import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS\nconst HEADLESS_BROWSER_BOT_UA_RE =\n  /Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n", "import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n", "'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n", "import type { FlightRouterState } from '../../../../server/app-render/types'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createRouterCacheKey } from '../create-router-cache-key'\n\nexport function findHeadInCache(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1]\n): [CacheNode, string] | null {\n  return findHeadInCacheImpl(cache, parallelRoutes, '')\n}\n\nfunction findHeadInCacheImpl(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1],\n  keyPrefix: string\n): [CacheNode, string] | null {\n  const isLastItem = Object.keys(parallelRoutes).length === 0\n  if (isLastItem) {\n    // Returns the entire Cache Node of the segment whose head we will render.\n    return [cache, keyPrefix]\n  }\n\n  // First try the 'children' parallel route if it exists\n  // when starting from the \"root\", this corresponds with the main page component\n  if (parallelRoutes.children) {\n    const [segment, childParallelRoutes] = parallelRoutes.children\n    const childSegmentMap = cache.parallelRoutes.get('children')\n    if (childSegmentMap) {\n      const cacheKey = createRouterCacheKey(segment)\n      const cacheNode = childSegmentMap.get(cacheKey)\n      if (cacheNode) {\n        const item = findHeadInCacheImpl(\n          cacheNode,\n          childParallelRoutes,\n          keyPrefix + '/' + cacheKey\n        )\n        if (item) return item\n      }\n    }\n  }\n\n  // if we didn't find metadata in the page slot, check the other parallel routes\n  for (const key in parallelRoutes) {\n    if (key === 'children') continue // already checked above\n\n    const [segment, childParallelRoutes] = parallelRoutes[key]\n    const childSegmentMap = cache.parallelRoutes.get(key)\n    if (!childSegmentMap) {\n      continue\n    }\n\n    const cacheKey = createRouterCacheKey(segment)\n\n    const cacheNode = childSegmentMap.get(cacheKey)\n    if (!cacheNode) {\n      continue\n    }\n\n    const item = findHeadInCacheImpl(\n      cacheNode,\n      childParallelRoutes,\n      keyPrefix + '/' + cacheKey\n    )\n    if (item) {\n      return item\n    }\n  }\n\n  return null\n}\n", "/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n", "import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n", "'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n", "import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  ChildSegmentMap,\n  HeadData,\n  LoadingModuleData,\n  ReadyCacheNode,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { FetchServerResponseResult } from './fetch-server-response'\nimport { isNavigatingToNewRootLayout } from './is-navigating-to-new-root-layout'\nimport { DYNAMIC_STALETIME_MS } from './prefetch-cache-utils'\n\n// This is yet another tree type that is used to track pending promises that\n// need to be fulfilled once the dynamic data is received. The terminal nodes of\n// this tree represent the new Cache Node trees that were created during this\n// request. We can't use the Cache Node tree or Route State tree directly\n// because those include reused nodes, too. This tree is discarded as soon as\n// the navigation response is received.\ntype SPANavigationTask = {\n  // The router state that corresponds to the tree that this Task represents.\n  route: FlightRouterState\n  // The CacheNode that corresponds to the tree that this Task represents. If\n  // `children` is null (i.e. if this is a terminal task node), then `node`\n  // represents a brand new Cache Node tree, which way or may not need to be\n  // filled with dynamic data from the server.\n  node: CacheNode | null\n  // The tree sent to the server during the dynamic request. This is the\n  // same as `route`, except with the `refetch` marker set on dynamic segments.\n  // If all the segments are static, then this will be null, and no server\n  // request is required.\n  dynamicRequestTree: FlightRouterState | null\n  children: Map<string, SPANavigationTask> | null\n}\n\n// A special type used to bail out and trigger a full-page navigation.\ntype MPANavigationTask = {\n  // MPA tasks are distinguised from SPA tasks by having a null `route`.\n  route: null\n  node: null\n  dynamicRequestTree: null\n  children: null\n}\n\nconst MPA_NAVIGATION_TASK: MPANavigationTask = {\n  route: null,\n  node: null,\n  dynamicRequestTree: null,\n  children: null,\n}\n\nexport type Task = SPANavigationTask | MPANavigationTask\n\n// Creates a new Cache Node tree (i.e. copy-on-write) that represents the\n// optimistic result of a navigation, using both the current Cache Node tree and\n// data that was prefetched prior to navigation.\n//\n// At the moment we call this function, we haven't yet received the navigation\n// response from the server. It could send back something completely different\n// from the tree that was prefetched — due to rewrites, default routes, parallel\n// routes, etc.\n//\n// But in most cases, it will return the same tree that we prefetched, just with\n// the dynamic holes filled in. So we optimistically assume this will happen,\n// and accept that the real result could be arbitrarily different.\n//\n// We'll reuse anything that was already in the previous tree, since that's what\n// the server does.\n//\n// New segments (ones that don't appear in the old tree) are assigned an\n// unresolved promise. The data for these promises will be fulfilled later, when\n// the navigation response is received.\n//\n// The tree can be rendered immediately after it is created (that's why this is\n// a synchronous function). Any new trees that do not have prefetch data will\n// suspend during rendering, until the dynamic data streams in.\n//\n// Returns a Task object, which contains both the updated Cache Node and a path\n// to the pending subtrees that need to be resolved by the navigation response.\n//\n// A return value of `null` means there were no changes, and the previous tree\n// can be reused without initiating a server request.\nexport function startPPRNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  const segmentPath: Array<FlightSegmentPath> = []\n  return updateCacheNodeOnNavigation(\n    navigatedAt,\n    oldCacheNode,\n    oldRouterState,\n    newRouterState,\n    false,\n    prefetchData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction updateCacheNodeOnNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  // Diff the old and new trees to reuse the shared layouts.\n  const oldRouterStateChildren = oldRouterState[1]\n  const newRouterStateChildren = newRouterState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  if (!didFindRootLayout) {\n    // We're currently traversing the part of the tree that was also part of\n    // the previous route. If we discover a root layout, then we don't need to\n    // trigger an MPA navigation. See beginRenderingNewRouteTree for context.\n    const isRootLayout = newRouterState[4] === true\n    if (isRootLayout) {\n      // Found a matching root layout.\n      didFindRootLayout = true\n    }\n  }\n\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n\n  // Clone the current set of segment children, even if they aren't active in\n  // the new tree.\n  // TODO: We currently retain all the inactive segments indefinitely, until\n  // there's an explicit refresh, or a parent layout is lazily refreshed. We\n  // rely on this for popstate navigations, which update the Router State Tree\n  // but do not eagerly perform a data fetch, because they expect the segment\n  // data to already be in the Cache Node tree. For highly static sites that\n  // are mostly read-only, this may happen only rarely, causing memory to\n  // leak. We should figure out a better model for the lifetime of inactive\n  // segments, so we can maintain instant back/forward navigations without\n  // leaking memory indefinitely.\n  const prefetchParallelRoutes = new Map(oldParallelRoutes)\n\n  // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n  // the Route Tree that was returned by the server — for example, in the case\n  // of default parallel routes, we preserve the currently active segment. To\n  // avoid mutating the original tree, we clone the router state children along\n  // the return path.\n  let patchedRouterStateChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let taskChildren = null\n\n  // Most navigations require a request to fetch additional data from the\n  // server, either because the data was not already prefetched, or because the\n  // target route contains dynamic data that cannot be prefetched.\n  //\n  // However, if the target route is fully static, and it's already completely\n  // loaded into the segment cache, then we can skip the server request.\n  //\n  // This starts off as `false`, and is set to `true` if any of the child\n  // routes requires a dynamic request.\n  let needsDynamicRequest = false\n  // As we traverse the children, we'll construct a FlightRouterState that can\n  // be sent to the server to request the dynamic data. If it turns out that\n  // nothing in the subtree is dynamic (i.e. needsDynamicRequest is false at the\n  // end), then this will be discarded.\n  // TODO: We can probably optimize the format of this data structure to only\n  // include paths that are dynamic. Instead of reusing the\n  // FlightRouterState type.\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n\n  for (let parallelRouteKey in newRouterStateChildren) {\n    const newRouterStateChild: FlightRouterState =\n      newRouterStateChildren[parallelRouteKey]\n    const oldRouterStateChild: FlightRouterState | void =\n      oldRouterStateChildren[parallelRouteKey]\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const newSegmentChild = newRouterStateChild[0]\n    const newSegmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      newSegmentChild,\n    ])\n    const newSegmentKeyChild = createRouterCacheKey(newSegmentChild)\n\n    const oldSegmentChild =\n      oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined\n\n    const oldCacheNodeChild =\n      oldSegmentMapChild !== undefined\n        ? oldSegmentMapChild.get(newSegmentKeyChild)\n        : undefined\n\n    let taskChild: Task | null\n    if (newSegmentChild === DEFAULT_SEGMENT_KEY) {\n      // This is another kind of leaf segment — a default route.\n      //\n      // Default routes have special behavior. When there's no matching segment\n      // for a parallel route, Next.js preserves the currently active segment\n      // during a client navigation — but not for initial render. The server\n      // leaves it to the client to account for this. So we need to handle\n      // it here.\n      if (oldRouterStateChild !== undefined) {\n        // Reuse the existing Router State for this segment. We spawn a \"task\"\n        // just to keep track of the updated router state; unlike most, it's\n        // already fulfilled and won't be affected by the dynamic response.\n        taskChild = spawnReusedTask(oldRouterStateChild)\n      } else {\n        // There's no currently active segment. Switch to the \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else if (\n      isSamePageNavigation &&\n      // Check if this is a page segment.\n      // TODO: We're not consistent about how we do this check. Some places\n      // check if the segment starts with PAGE_SEGMENT_KEY, but most seem to\n      // check if there any any children, which is why I'm doing it here. We\n      // should probably encode an empty children set as `null` though. Either\n      // way, we should update all the checks to be consistent.\n      Object.keys(newRouterStateChild[1]).length === 0\n    ) {\n      // We special case navigations to the exact same URL as the current\n      // location. It's a common UI pattern for apps to refresh when you click a\n      // link to the current page. So when this happens, we refresh the dynamic\n      // data in the page segments.\n      //\n      // Note that this does not apply if the any part of the hash or search\n      // query has changed. This might feel a bit weird but it makes more sense\n      // when you consider that the way to trigger this behavior is to click\n      // the same link multiple times.\n      //\n      // TODO: We should probably refresh the *entire* route when this case\n      // occurs, not just the page segments. Essentially treating it the same as\n      // a refresh() triggered by an action, which is the more explicit way of\n      // modeling the UI pattern described above.\n      //\n      // Also note that this only refreshes the dynamic data, not static/\n      // cached data. If the page segment is fully static and prefetched, the\n      // request is skipped. (This is also how refresh() works.)\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    } else if (\n      oldRouterStateChild !== undefined &&\n      oldSegmentChild !== undefined &&\n      matchSegment(newSegmentChild, oldSegmentChild)\n    ) {\n      if (\n        oldCacheNodeChild !== undefined &&\n        oldRouterStateChild !== undefined\n      ) {\n        // This segment exists in both the old and new trees. Recursively update\n        // the children.\n        taskChild = updateCacheNodeOnNavigation(\n          navigatedAt,\n          oldCacheNodeChild,\n          oldRouterStateChild,\n          newRouterStateChild,\n          didFindRootLayout,\n          prefetchDataChild,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          isSamePageNavigation,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      } else {\n        // There's no existing Cache Node for this segment. Switch to the\n        // \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else {\n      // This is a new tree. Switch to the \"create\" path.\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    }\n\n    if (taskChild !== null) {\n      // Recursively propagate up the child tasks.\n\n      if (taskChild.route === null) {\n        // One of the child tasks discovered a change to the root layout.\n        // Immediately unwind from this recursive traversal.\n        return MPA_NAVIGATION_TASK\n      }\n\n      if (taskChildren === null) {\n        taskChildren = new Map()\n      }\n      taskChildren.set(parallelRouteKey, taskChild)\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild)\n        prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n\n      // The child tree's route state may be different from the prefetched\n      // route sent by the server. We need to clone it as we traverse back up\n      // the tree.\n      const taskChildRoute = taskChild.route\n      patchedRouterStateChildren[parallelRouteKey] = taskChildRoute\n\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = taskChildRoute\n      }\n    } else {\n      // The child didn't change. We can use the prefetched router state.\n      patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild\n      dynamicRequestTreeChildren[parallelRouteKey] = newRouterStateChild\n    }\n  }\n\n  if (taskChildren === null) {\n    // No new tasks were spawned.\n    return null\n  }\n\n  const newCacheNode: ReadyCacheNode = {\n    lazyData: null,\n    rsc: oldCacheNode.rsc,\n    // We intentionally aren't updating the prefetchRsc field, since this node\n    // is already part of the current tree, because it would be weird for\n    // prefetch data to be newer than the final data. It probably won't ever be\n    // observable anyway, but it could happen if the segment is unmounted then\n    // mounted again, because LayoutRouter will momentarily switch to rendering\n    // prefetchRsc, via useDeferredValue.\n    prefetchRsc: oldCacheNode.prefetchRsc,\n    head: oldCacheNode.head,\n    prefetchHead: oldCacheNode.prefetchHead,\n    loading: oldCacheNode.loading,\n\n    // Everything is cloned except for the children, which we computed above.\n    parallelRoutes: prefetchParallelRoutes,\n\n    navigatedAt,\n  }\n\n  return {\n    // Return a cloned copy of the router state with updated children.\n    route: patchRouterStateWithNewChildren(\n      newRouterState,\n      patchedRouterStateChildren\n    ),\n    node: newCacheNode,\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(\n          newRouterState,\n          dynamicRequestTreeChildren\n        )\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction beginRenderingNewRouteTree(\n  navigatedAt: number,\n  oldRouterState: FlightRouterState | void,\n  newRouterState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task {\n  if (!didFindRootLayout) {\n    // The route tree changed before we reached a layout. (The highest-level\n    // layout in a route tree is referred to as the \"root\" layout.) This could\n    // mean that we're navigating between two different root layouts. When this\n    // happens, we perform a full-page (MPA-style) navigation.\n    //\n    // However, the algorithm for deciding where to start rendering a route\n    // (i.e. the one performed in order to reach this function) is stricter\n    // than the one used to detect a change in the root layout. So just because\n    // we're re-rendering a segment outside of the root layout does not mean we\n    // should trigger a full-page navigation.\n    //\n    // Specifically, we handle dynamic parameters differently: two segments are\n    // considered the same even if their parameter values are different.\n    //\n    // Refer to isNavigatingToNewRootLayout for details.\n    //\n    // Note that we only have to perform this extra traversal if we didn't\n    // already discover a root layout in the part of the tree that is unchanged.\n    // In the common case, this branch is skipped completely.\n    if (\n      oldRouterState === undefined ||\n      isNavigatingToNewRootLayout(oldRouterState, newRouterState)\n    ) {\n      // The root layout changed. Perform a full-page navigation.\n      return MPA_NAVIGATION_TASK\n    }\n  }\n  return createCacheNodeOnNavigation(\n    navigatedAt,\n    newRouterState,\n    existingCacheNode,\n    prefetchData,\n    possiblyPartialPrefetchHead,\n    isPrefetchHeadPartial,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction createCacheNodeOnNavigation(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Same traversal as updateCacheNodeNavigation, but we switch to this path\n  // once we reach the part of the tree that was not in the previous route. We\n  // don't need to diff against the old tree, we just need to create a new one.\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const routerStateChildren = routerState[1]\n  const isLeafSegment = Object.keys(routerStateChildren).length === 0\n\n  // Even we're rendering inside the \"new\" part of the target tree, we may have\n  // a locally cached segment that we can reuse. This may come from either 1)\n  // the CacheNode tree, which lives in React state and is populated by previous\n  // navigations; or 2) the prefetch cache, which is a separate cache that is\n  // populated by prefetches.\n  let rsc: React.ReactNode\n  let loading: LoadingModuleData | Promise<LoadingModuleData>\n  let head: HeadData | null\n  let cacheNodeNavigatedAt: number\n  if (\n    existingCacheNode !== undefined &&\n    // DYNAMIC_STALETIME_MS defaults to 0, but it can be increased using\n    // the experimental.staleTimes.dynamic config. When set, we'll avoid\n    // refetching dynamic data if it was fetched within the given threshold.\n    existingCacheNode.navigatedAt + DYNAMIC_STALETIME_MS > navigatedAt\n  ) {\n    // We have an existing CacheNode for this segment, and it's not stale. We\n    // should reuse it rather than request a new one.\n    rsc = existingCacheNode.rsc\n    loading = existingCacheNode.loading\n    head = existingCacheNode.head\n\n    // Don't update the navigatedAt timestamp, since we're reusing stale data.\n    cacheNodeNavigatedAt = existingCacheNode.navigatedAt\n  } else if (prefetchData !== null) {\n    // There's no existing CacheNode for this segment, but we do have prefetch\n    // data. If the prefetch data is fully static (i.e. does not contain any\n    // dynamic holes), we don't need to request it from the server.\n    rsc = prefetchData[1]\n    loading = prefetchData[3]\n    head = isLeafSegment ? possiblyPartialPrefetchHead : null\n    // Even though we're accessing the data from the prefetch cache, this is\n    // conceptually a new segment, not a reused one. So we should update the\n    // navigatedAt timestamp.\n    cacheNodeNavigatedAt = navigatedAt\n    const isPrefetchRscPartial = prefetchData[4]\n    if (\n      // Check if the segment data is partial\n      isPrefetchRscPartial ||\n      // Check if the head is partial (only relevant if this is a leaf segment)\n      (isPrefetchHeadPartial && isLeafSegment)\n    ) {\n      // We only have partial data from this segment. Like missing segments, we\n      // must request the full data from the server.\n      return spawnPendingTask(\n        navigatedAt,\n        routerState,\n        prefetchData,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPath,\n        scrollableSegmentsResult\n      )\n    } else {\n      // The prefetch data is fully static, so we can omit it from the\n      // navigation request.\n    }\n  } else {\n    // There's no prefetch for this segment. Everything from this point will be\n    // requested from the server, even if there are static children below it.\n    // Create a terminal task node that will later be fulfilled by\n    // server response.\n    return spawnPendingTask(\n      navigatedAt,\n      routerState,\n      null,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    )\n  }\n\n  // We already have a full segment we can render, so we don't need to request a\n  // new one from the server. Keep traversing down the tree until we reach\n  // something that requires a dynamic request.\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n  const taskChildren = new Map()\n  const existingCacheNodeChildren =\n    existingCacheNode !== undefined ? existingCacheNode.parallelRoutes : null\n  const cacheNodeChildren = new Map(existingCacheNodeChildren)\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let needsDynamicRequest = false\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  } else {\n    for (let parallelRouteKey in routerStateChildren) {\n      const routerStateChild: FlightRouterState =\n        routerStateChildren[parallelRouteKey]\n      const prefetchDataChild: CacheNodeSeedData | void | null =\n        prefetchDataChildren !== null\n          ? prefetchDataChildren[parallelRouteKey]\n          : null\n      const existingSegmentMapChild =\n        existingCacheNodeChildren !== null\n          ? existingCacheNodeChildren.get(parallelRouteKey)\n          : undefined\n      const segmentChild = routerStateChild[0]\n      const segmentPathChild = segmentPath.concat([\n        parallelRouteKey,\n        segmentChild,\n      ])\n      const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n      const existingCacheNodeChild =\n        existingSegmentMapChild !== undefined\n          ? existingSegmentMapChild.get(segmentKeyChild)\n          : undefined\n\n      const taskChild = createCacheNodeOnNavigation(\n        navigatedAt,\n        routerStateChild,\n        existingCacheNodeChild,\n        prefetchDataChild,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPathChild,\n        scrollableSegmentsResult\n      )\n      taskChildren.set(parallelRouteKey, taskChild)\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = routerStateChild\n      }\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map()\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  return {\n    // Since we're inside a new route tree, unlike the\n    // `updateCacheNodeOnNavigation` path, the router state on the children\n    // tasks is always the same as the router state we pass in. So we don't need\n    // to clone/modify it.\n    route: routerState,\n    node: {\n      lazyData: null,\n      // Since this segment is already full, we don't need to use the\n      // `prefetchRsc` field.\n      rsc,\n      prefetchRsc: null,\n      head,\n      prefetchHead: null,\n      loading,\n      parallelRoutes: cacheNodeChildren,\n      navigatedAt: cacheNodeNavigatedAt,\n    },\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(routerState, dynamicRequestTreeChildren)\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction patchRouterStateWithNewChildren(\n  baseRouterState: FlightRouterState,\n  newChildren: { [parallelRouteKey: string]: FlightRouterState }\n): FlightRouterState {\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n\nfunction spawnPendingTask(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Create a task that will later be fulfilled by data from the server.\n\n  // Clone the prefetched route tree and the `refetch` marker to it. We'll send\n  // this to the server so it knows where to start rendering.\n  const dynamicRequestTree = patchRouterStateWithNewChildren(\n    routerState,\n    routerState[1]\n  )\n  dynamicRequestTree[3] = 'refetch'\n\n  const newTask: Task = {\n    route: routerState,\n\n    // Corresponds to the part of the route that will be rendered on the server.\n    node: createPendingCacheNode(\n      navigatedAt,\n      routerState,\n      prefetchData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    ),\n    // Because this is non-null, and it gets propagated up through the parent\n    // tasks, the root task will know that it needs to perform a server request.\n    dynamicRequestTree,\n    children: null,\n  }\n  return newTask\n}\n\nfunction spawnReusedTask(reusedRouterState: FlightRouterState): Task {\n  // Create a task that reuses an existing segment, e.g. when reusing\n  // the current active segment in place of a default route.\n  return {\n    route: reusedRouterState,\n    node: null,\n    dynamicRequestTree: null,\n    children: null,\n  }\n}\n\n// Writes a dynamic server response into the tree created by\n// updateCacheNodeOnNavigation. All pending promises that were spawned by the\n// navigation will be resolved, either with dynamic data from the server, or\n// `null` to indicate that the data is missing.\n//\n// A `null` value will trigger a lazy fetch during render, which will then patch\n// up the tree using the same mechanism as the non-PPR implementation\n// (serverPatchReducer).\n//\n// Usually, the server will respond with exactly the subset of data that we're\n// waiting for — everything below the nearest shared layout. But technically,\n// the server can return anything it wants.\n//\n// This does _not_ create a new tree; it modifies the existing one in place.\n// Which means it must follow the Suspense rules of cache safety.\nexport function listenForDynamicRequest(\n  task: SPANavigationTask,\n  responsePromise: Promise<FetchServerResponseResult>\n) {\n  responsePromise.then(\n    ({ flightData }: FetchServerResponseResult) => {\n      if (typeof flightData === 'string') {\n        // Happens when navigating to page in `pages` from `app`. We shouldn't\n        // get here because should have already handled this during\n        // the prefetch.\n        return\n      }\n      for (const normalizedFlightData of flightData) {\n        const {\n          segmentPath,\n          tree: serverRouterState,\n          seedData: dynamicData,\n          head: dynamicHead,\n        } = normalizedFlightData\n\n        if (!dynamicData) {\n          // This shouldn't happen. PPR should always send back a response.\n          // However, `FlightDataPath` is a shared type and the pre-PPR handling of\n          // this might return null.\n          continue\n        }\n\n        writeDynamicDataIntoPendingTask(\n          task,\n          segmentPath,\n          serverRouterState,\n          dynamicData,\n          dynamicHead\n        )\n      }\n\n      // Now that we've exhausted all the data we received from the server, if\n      // there are any remaining pending tasks in the tree, abort them now.\n      // If there's any missing data, it will trigger a lazy fetch.\n      abortTask(task, null)\n    },\n    (error: any) => {\n      // This will trigger an error during render\n      abortTask(task, error)\n    }\n  )\n}\n\nfunction writeDynamicDataIntoPendingTask(\n  rootTask: SPANavigationTask,\n  segmentPath: FlightSegmentPath,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  // The data sent by the server represents only a subtree of the app. We need\n  // to find the part of the task tree that matches the server response, and\n  // fulfill it using the dynamic data.\n  //\n  // segmentPath represents the parent path of subtree. It's a repeating pattern\n  // of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // Iterate through the path and finish any tasks that match this payload.\n  let task = rootTask\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n    const taskChildren = task.children\n    if (taskChildren !== null) {\n      const taskChild = taskChildren.get(parallelRouteKey)\n      if (taskChild !== undefined) {\n        const taskSegment = taskChild.route[0]\n        if (matchSegment(segment, taskSegment)) {\n          // Found a match for this task. Keep traversing down the task tree.\n          task = taskChild\n          continue\n        }\n      }\n    }\n    // We didn't find a child task that matches the server data. Exit. We won't\n    // abort the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    return\n  }\n\n  finishTaskUsingDynamicDataPayload(\n    task,\n    serverRouterState,\n    dynamicData,\n    dynamicHead\n  )\n}\n\nfunction finishTaskUsingDynamicDataPayload(\n  task: SPANavigationTask,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  if (task.dynamicRequestTree === null) {\n    // Everything in this subtree is already complete. Bail out.\n    return\n  }\n\n  // dynamicData may represent a larger subtree than the task. Before we can\n  // finish the task, we need to line them up.\n  const taskChildren = task.children\n  const taskNode = task.node\n  if (taskChildren === null) {\n    // We've reached the leaf node of the pending task. The server data tree\n    // lines up the pending Cache Node tree. We can now switch to the\n    // normal algorithm.\n    if (taskNode !== null) {\n      finishPendingCacheNode(\n        taskNode,\n        task.route,\n        serverRouterState,\n        dynamicData,\n        dynamicHead\n      )\n      // Set this to null to indicate that this task is now complete.\n      task.dynamicRequestTree = null\n    }\n    return\n  }\n  // The server returned more data than we need to finish the task. Skip over\n  // the extra segments until we reach the leaf task node.\n  const serverChildren = serverRouterState[1]\n  const dynamicDataChildren = dynamicData[2]\n\n  for (const parallelRouteKey in serverRouterState) {\n    const serverRouterStateChild: FlightRouterState =\n      serverChildren[parallelRouteKey]\n    const dynamicDataChild: CacheNodeSeedData | null | void =\n      dynamicDataChildren[parallelRouteKey]\n\n    const taskChild = taskChildren.get(parallelRouteKey)\n    if (taskChild !== undefined) {\n      const taskSegment = taskChild.route[0]\n      if (\n        matchSegment(serverRouterStateChild[0], taskSegment) &&\n        dynamicDataChild !== null &&\n        dynamicDataChild !== undefined\n      ) {\n        // Found a match for this task. Keep traversing down the task tree.\n        return finishTaskUsingDynamicDataPayload(\n          taskChild,\n          serverRouterStateChild,\n          dynamicDataChild,\n          dynamicHead\n        )\n      }\n    }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n  }\n}\n\nfunction createPendingCacheNode(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): ReadyCacheNode {\n  const routerStateChildren = routerState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const parallelRoutes = new Map()\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | null | void =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const segmentChild = routerStateChild[0]\n    const segmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      segmentChild,\n    ])\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n    const newCacheNodeChild = createPendingCacheNode(\n      navigatedAt,\n      routerStateChild,\n      prefetchDataChild === undefined ? null : prefetchDataChild,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPathChild,\n      scrollableSegmentsResult\n    )\n\n    const newSegmentMapChild: ChildSegmentMap = new Map()\n    newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n    parallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n  }\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = parallelRoutes.size === 0\n\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  }\n\n  const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null\n  const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null\n  return {\n    lazyData: null,\n    parallelRoutes: parallelRoutes,\n\n    prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n    prefetchHead: isLeafSegment ? prefetchHead : [null, null],\n\n    // TODO: Technically, a loading boundary could contain dynamic data. We must\n    // have separate `loading` and `prefetchLoading` fields to handle this, like\n    // we do for the segment data and head.\n    loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n\n    // Create a deferred promise. This will be fulfilled once the dynamic\n    // response is received from the server.\n    rsc: createDeferredRsc() as React.ReactNode,\n    head: isLeafSegment ? (createDeferredRsc() as React.ReactNode) : null,\n\n    navigatedAt,\n  }\n}\n\nfunction finishPendingCacheNode(\n  cacheNode: CacheNode,\n  taskState: FlightRouterState,\n  serverState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n): void {\n  // Writes a dynamic response into an existing Cache Node tree. This does _not_\n  // create a new tree, it updates the existing tree in-place. So it must follow\n  // the Suspense rules of cache safety — it can resolve pending promises, but\n  // it cannot overwrite existing data. It can add segments to the tree (because\n  // a missing segment will cause the layout router to suspend).\n  // but it cannot delete them.\n  //\n  // We must resolve every promise in the tree, or else it will suspend\n  // indefinitely. If we did not receive data for a segment, we will resolve its\n  // data promise to `null` to trigger a lazy fetch during render.\n  const taskStateChildren = taskState[1]\n  const serverStateChildren = serverState[1]\n  const dataChildren = dynamicData[2]\n\n  // The router state that we traverse the tree with (taskState) is the same one\n  // that we used to construct the pending Cache Node tree. That way we're sure\n  // to resolve all the pending promises.\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in taskStateChildren) {\n    const taskStateChild: FlightRouterState =\n      taskStateChildren[parallelRouteKey]\n    const serverStateChild: FlightRouterState | void =\n      serverStateChildren[parallelRouteKey]\n    const dataChild: CacheNodeSeedData | null | void =\n      dataChildren[parallelRouteKey]\n\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    const taskSegmentChild = taskStateChild[0]\n    const taskSegmentKeyChild = createRouterCacheKey(taskSegmentChild)\n\n    const cacheNodeChild =\n      segmentMapChild !== undefined\n        ? segmentMapChild.get(taskSegmentKeyChild)\n        : undefined\n\n    if (cacheNodeChild !== undefined) {\n      if (\n        serverStateChild !== undefined &&\n        matchSegment(taskSegmentChild, serverStateChild[0])\n      ) {\n        if (dataChild !== undefined && dataChild !== null) {\n          // This is the happy path. Recursively update all the children.\n          finishPendingCacheNode(\n            cacheNodeChild,\n            taskStateChild,\n            serverStateChild,\n            dataChild,\n            dynamicHead\n          )\n        } else {\n          // The server never returned data for this segment. Trigger a lazy\n          // fetch during render. This shouldn't happen because the Route Tree\n          // and the Seed Data tree sent by the server should always be the same\n          // shape when part of the same server response.\n          abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n        }\n      } else {\n        // The server never returned data for this segment. Trigger a lazy\n        // fetch during render.\n        abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n      }\n    } else {\n      // The server response matches what was expected to receive, but there's\n      // no matching Cache Node in the task tree. This is a bug in the\n      // implementation because we should have created a node for every\n      // segment in the tree that's associated with this task.\n    }\n  }\n\n  // Use the dynamic data from the server to fulfill the deferred RSC promise\n  // on the Cache Node.\n  const rsc = cacheNode.rsc\n  const dynamicSegmentData = dynamicData[1]\n  if (rsc === null) {\n    // This is a lazy cache node. We can overwrite it. This is only safe\n    // because we know that the LayoutRouter suspends if `rsc` is `null`.\n    cacheNode.rsc = dynamicSegmentData\n  } else if (isDeferredRsc(rsc)) {\n    // This is a deferred RSC promise. We can fulfill it with the data we just\n    // received from the server. If it was already resolved by a different\n    // navigation, then this does nothing because we can't overwrite data.\n    rsc.resolve(dynamicSegmentData)\n  } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved with the dynamic head from\n  // the server.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(dynamicHead)\n  }\n}\n\nexport function abortTask(task: SPANavigationTask, error: any): void {\n  const cacheNode = task.node\n  if (cacheNode === null) {\n    // This indicates the task is already complete.\n    return\n  }\n\n  const taskChildren = task.children\n  if (taskChildren === null) {\n    // Reached the leaf task node. This is the root of a pending cache\n    // node tree.\n    abortPendingCacheNode(task.route, cacheNode, error)\n  } else {\n    // This is an intermediate task node. Keep traversing until we reach a\n    // task node with no children. That will be the root of the cache node tree\n    // that needs to be resolved.\n    for (const taskChild of taskChildren.values()) {\n      abortTask(taskChild, error)\n    }\n  }\n\n  // Set this to null to indicate that this task is now complete.\n  task.dynamicRequestTree = null\n}\n\nfunction abortPendingCacheNode(\n  routerState: FlightRouterState,\n  cacheNode: CacheNode,\n  error: any\n): void {\n  // For every pending segment in the tree, resolve its `rsc` promise to `null`\n  // to trigger a lazy fetch during render.\n  //\n  // Or, if an error object is provided, it will error instead.\n  const routerStateChildren = routerState[1]\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    if (segmentMapChild === undefined) {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n      continue\n    }\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const cacheNodeChild = segmentMapChild.get(segmentKeyChild)\n    if (cacheNodeChild !== undefined) {\n      abortPendingCacheNode(routerStateChild, cacheNodeChild, error)\n    } else {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n    }\n  }\n  const rsc = cacheNode.rsc\n  if (isDeferredRsc(rsc)) {\n    if (error === null) {\n      // This will trigger a lazy fetch during render.\n      rsc.resolve(null)\n    } else {\n      // This will trigger an error during rendering.\n      rsc.reject(error)\n    }\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved. If an error was provided, we\n  // will not resolve it with an error, since this is rendered at the root of\n  // the app. We want the segment to error, not the entire app.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(null)\n  }\n}\n\nexport function updateCacheNodeOnPopstateRestoration(\n  oldCacheNode: CacheNode,\n  routerState: FlightRouterState\n): ReadyCacheNode {\n  // A popstate navigation reads data from the local cache. It does not issue\n  // new network requests (unless the cache entries have been evicted). So, we\n  // update the cache to drop the prefetch data for any segment whose dynamic\n  // data was already received. This prevents an unnecessary flash back to PPR\n  // state during a back/forward navigation.\n  //\n  // This function clones the entire cache node tree and sets the `prefetchRsc`\n  // field to `null` to prevent it from being rendered. We can't mutate the node\n  // in place because this is a concurrent data structure.\n\n  const routerStateChildren = routerState[1]\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n  const newParallelRoutes = new Map(oldParallelRoutes)\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    if (oldSegmentMapChild !== undefined) {\n      const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild)\n      if (oldCacheNodeChild !== undefined) {\n        const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(\n          oldCacheNodeChild,\n          routerStateChild\n        )\n        const newSegmentMapChild = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        newParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  // Only show prefetched data if the dynamic data is still pending.\n  //\n  // Tehnically, what we're actually checking is whether the dynamic network\n  // response was received. But since it's a streaming response, this does not\n  // mean that all the dynamic data has fully streamed in. It just means that\n  // _some_ of the dynamic data was received. But as a heuristic, we assume that\n  // the rest dynamic data will stream in quickly, so it's still better to skip\n  // the prefetch state.\n  const rsc = oldCacheNode.rsc\n  const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending'\n\n  return {\n    lazyData: null,\n    rsc,\n    head: oldCacheNode.head,\n\n    prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : [null, null],\n    prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n    loading: oldCacheNode.loading,\n\n    // These are the cloned children we computed above\n    parallelRoutes: newParallelRoutes,\n\n    navigatedAt: oldCacheNode.navigatedAt,\n  }\n}\n\nconst DEFERRED = Symbol()\n\ntype PendingDeferredRsc = Promise<React.ReactNode> & {\n  status: 'pending'\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype FulfilledDeferredRsc = Promise<React.ReactNode> & {\n  status: 'fulfilled'\n  value: React.ReactNode\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype RejectedDeferredRsc = Promise<React.ReactNode> & {\n  status: 'rejected'\n  reason: any\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype DeferredRsc =\n  | PendingDeferredRsc\n  | FulfilledDeferredRsc\n  | RejectedDeferredRsc\n\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value: any): value is DeferredRsc {\n  return value && value.tag === DEFERRED\n}\n\nfunction createDeferredRsc(): PendingDeferredRsc {\n  let resolve: any\n  let reject: any\n  const pendingRsc = new Promise<React.ReactNode>((res, rej) => {\n    resolve = res\n    reject = rej\n  }) as PendingDeferredRsc\n  pendingRsc.status = 'pending'\n  pendingRsc.resolve = (value: React.ReactNode) => {\n    if (pendingRsc.status === 'pending') {\n      const fulfilledRsc: FulfilledDeferredRsc = pendingRsc as any\n      fulfilledRsc.status = 'fulfilled'\n      fulfilledRsc.value = value\n      resolve(value)\n    }\n  }\n  pendingRsc.reject = (error: any) => {\n    if (pendingRsc.status === 'pending') {\n      const rejectedRsc: RejectedDeferredRsc = pendingRsc as any\n      rejectedRsc.status = 'rejected'\n      rejectedRsc.reason = error\n      reject(error)\n    }\n  }\n  pendingRsc.tag = DEFERRED\n  return pendingRsc\n}\n", "import type { FlightSegmentPath } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * This will clear the CacheNode data for a particular segment path. This will cause a lazy-fetch in layout router to fill in new data.\n */\nexport function clearCacheNodeDataForSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n\n  const [parallelRouteKey, segment] = flightSegmentPath\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap?.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  // In case of last segment start off the fetch at this level and don't copy further down.\n  if (isLastEntry) {\n    if (\n      !childCacheNode ||\n      !childCacheNode.lazyData ||\n      childCacheNode === existingChildCacheNode\n    ) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Start fetch in the place where the existing cache doesn't have the data yet.\n    if (!childCacheNode) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n      loading: childCacheNode.loading,\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  return clearCacheNodeDataForSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n} from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { createEmptyCacheNode } from '../app-router'\nimport { applyRouterStatePatchToTree } from './apply-router-state-patch-to-tree'\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { fillCacheWithNewSubTreeDataButOnlyLoading } from './fill-cache-with-new-subtree-data'\nimport { handleMutable } from './handle-mutable'\nimport type { Mutable, ReadonlyReducerState } from './router-reducer-types'\n\n/**\n * This is a stop-gap until per-segment caching is implemented. It leverages the `aliased` flag that is added\n * to prefetch entries when it's determined that the loading state from that entry should be used for this navigation.\n * This function takes the aliased entry and only applies the loading state to the updated cache node.\n * We should remove this once per-segment fetching is implemented as ideally the prefetch cache will contain a\n * more granular segment map and so the router will be able to simply re-use the loading segment for the new navigation.\n */\nexport function handleAliasedPrefetchEntry(\n  navigatedAt: number,\n  state: ReadonlyReducerState,\n  flightData: string | NormalizedFlightData[],\n  url: URL,\n  mutable: Mutable\n) {\n  let currentTree = state.tree\n  let currentCache = state.cache\n  const href = createHrefFromUrl(url)\n  let applied\n\n  if (typeof flightData === 'string') {\n    return false\n  }\n\n  for (const normalizedFlightData of flightData) {\n    // If the segment doesn't have a loading component, we don't need to do anything.\n    if (!hasLoadingComponentInSeedData(normalizedFlightData.seedData)) {\n      continue\n    }\n\n    let treePatch = normalizedFlightData.tree\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    const { seedData, isRootRender, pathToSegment } = normalizedFlightData\n    // TODO-APP: remove ''\n    const flightSegmentPathWithLeadingEmpty = ['', ...pathToSegment]\n\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    let newTree = applyRouterStatePatchToTree(\n      flightSegmentPathWithLeadingEmpty,\n      currentTree,\n      treePatch,\n      href\n    )\n\n    const newCache = createEmptyCacheNode()\n\n    // The prefetch cache entry was aliased -- this signals that we only fill in the cache with the\n    // loading state and not the actual parallel route seed data.\n    if (isRootRender && seedData) {\n      // Fill in the cache with the new loading / rsc data\n      const rsc = seedData[1]\n      const loading = seedData[3]\n      newCache.loading = loading\n      newCache.rsc = rsc\n\n      // Construct a new tree and apply the aliased loading state for each parallel route\n      fillNewTreeWithOnlyLoadingSegments(\n        navigatedAt,\n        newCache,\n        currentCache,\n        treePatch,\n        seedData\n      )\n    } else {\n      // Copy rsc for the root node of the cache.\n      newCache.rsc = currentCache.rsc\n      newCache.prefetchRsc = currentCache.prefetchRsc\n      newCache.loading = currentCache.loading\n      newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n      // copy the loading state only into the leaf node (the part that changed)\n      fillCacheWithNewSubTreeDataButOnlyLoading(\n        navigatedAt,\n        newCache,\n        currentCache,\n        normalizedFlightData\n      )\n    }\n\n    // If we don't have an updated tree, there's no reason to update the cache, as the tree\n    // dictates what cache nodes to render.\n    if (newTree) {\n      currentTree = newTree\n      currentCache = newCache\n      applied = true\n    }\n  }\n\n  if (!applied) {\n    return false\n  }\n\n  mutable.patchedTree = currentTree\n  mutable.cache = currentCache\n  mutable.canonicalUrl = href\n  mutable.hashFragment = url.hash\n\n  return handleMutable(state, mutable)\n}\n\nfunction hasLoadingComponentInSeedData(seedData: CacheNodeSeedData | null) {\n  if (!seedData) return false\n\n  const parallelRoutes = seedData[2]\n  const loading = seedData[3]\n\n  if (loading) {\n    return true\n  }\n\n  for (const key in parallelRoutes) {\n    if (hasLoadingComponentInSeedData(parallelRoutes[key])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction fillNewTreeWithOnlyLoadingSegments(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null\n) {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    return\n  }\n\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const rsc = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        // copy the layout but null the page segment as that's not meant to be used\n        rsc: segmentForParallelRoute.includes(PAGE_SEGMENT_KEY) ? null : rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillNewTreeWithOnlyLoadingSegments(\n      navigatedAt,\n      newCacheNode,\n      existingCache,\n      parallelRouteState,\n      parallelSeedData\n    )\n  }\n}\n\n/**\n * Add search params to the page segments in the flight router state\n * Page segments that are associated with search params have a page segment key\n * followed by a query string. This function will add those params to the page segment.\n * This is useful if we return an aliased prefetch entry (ie, won't have search params)\n * but the canonical router URL has search params.\n */\nexport function addSearchParamsToPageSegments(\n  flightRouterState: FlightRouterState,\n  searchParams: Record<string, string | string[] | undefined>\n): FlightRouterState {\n  const [segment, parallelRoutes, ...rest] = flightRouterState\n\n  // If it's a page segment, modify the segment by adding search params\n  if (segment.includes(PAGE_SEGMENT_KEY)) {\n    const newSegment = addSearchParamsIfPageSegment(segment, searchParams)\n    return [newSegment, parallelRoutes, ...rest]\n  }\n\n  // Otherwise, recurse through the parallel routes and return a new tree\n  const updatedParallelRoutes: { [key: string]: FlightRouterState } = {}\n\n  for (const [key, parallelRoute] of Object.entries(parallelRoutes)) {\n    updatedParallelRoutes[key] = addSearchParamsToPageSegments(\n      parallelRoute,\n      searchParams\n    )\n  }\n\n  return [segment, updatedParallelRoutes, ...rest]\n}\n", "/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').reschedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n", "import type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../../server/app-render/types'\nimport { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { invalidateCacheBelowFlightSegmentPath } from '../invalidate-cache-below-flight-segmentpath'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { shouldHardNavigate } from '../should-hard-navigate'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport {\n  PrefetchCacheEntryStatus,\n  type Mutable,\n  type NavigateAction,\n  type ReadonlyReducerState,\n  type ReducerState,\n} from '../router-reducer-types'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport { prefetchQueue } from './prefetch-reducer'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { DEFAULT_SEGMENT_KEY } from '../../../../shared/lib/segment'\nimport { listenForDynamicRequest, startPPRNavigation } from '../ppr-navigations'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nimport { clearCacheNodeDataForSegmentPath } from '../clear-cache-node-data-for-segment-path'\nimport { handleAliasedPrefetchEntry } from '../aliased-prefetch-navigations'\nimport {\n  navigate as navigateUsingSegmentCache,\n  NavigationResultTag,\n  type NavigationResult,\n} from '../../segment-cache'\n\nexport function handleExternalUrl(\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  url: string,\n  pendingPush: boolean\n) {\n  mutable.mpaNavigation = true\n  mutable.canonicalUrl = url\n  mutable.pendingPush = pendingPush\n  mutable.scrollableSegments = undefined\n\n  return handleMutable(state, mutable)\n}\n\nfunction generateSegmentsFromPatch(\n  flightRouterPatch: FlightRouterState\n): FlightSegmentPath[] {\n  const segments: FlightSegmentPath[] = []\n  const [segment, parallelRoutes] = flightRouterPatch\n\n  if (Object.keys(parallelRoutes).length === 0) {\n    return [[segment]]\n  }\n\n  for (const [parallelRouteKey, parallelRoute] of Object.entries(\n    parallelRoutes\n  )) {\n    for (const childSegment of generateSegmentsFromPatch(parallelRoute)) {\n      // If the segment is empty, it means we are at the root of the tree\n      if (segment === '') {\n        segments.push([parallelRouteKey, ...childSegment])\n      } else {\n        segments.push([segment, parallelRouteKey, ...childSegment])\n      }\n    }\n  }\n\n  return segments\n}\n\nfunction triggerLazyFetchForLeafSegments(\n  newCache: CacheNode,\n  currentCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath,\n  treePatch: FlightRouterState\n) {\n  let appliedPatch = false\n\n  newCache.rsc = currentCache.rsc\n  newCache.prefetchRsc = currentCache.prefetchRsc\n  newCache.loading = currentCache.loading\n  newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n  const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map(\n    (segment) => [...flightSegmentPath, ...segment]\n  )\n\n  for (const segmentPaths of segmentPathsToFill) {\n    clearCacheNodeDataForSegmentPath(newCache, currentCache, segmentPaths)\n\n    appliedPatch = true\n  }\n\n  return appliedPatch\n}\n\nfunction handleNavigationResult(\n  url: URL,\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  pendingPush: boolean,\n  result: NavigationResult\n): ReducerState {\n  switch (result.tag) {\n    case NavigationResultTag.MPA: {\n      // Perform an MPA navigation.\n      const newUrl = result.data\n      return handleExternalUrl(state, mutable, newUrl, pendingPush)\n    }\n    case NavigationResultTag.NoOp: {\n      // The server responded with no change to the current page. However, if\n      // the URL changed, we still need to update that.\n      const newCanonicalUrl = result.data.canonicalUrl\n      mutable.canonicalUrl = newCanonicalUrl\n\n      // Check if the only thing that changed was the hash fragment.\n      const oldUrl = new URL(state.canonicalUrl, url)\n      const onlyHashChange =\n        // We don't need to compare the origins, because client-driven\n        // navigations are always same-origin.\n        url.pathname === oldUrl.pathname &&\n        url.search === oldUrl.search &&\n        url.hash !== oldUrl.hash\n      if (onlyHashChange) {\n        // The only updated part of the URL is the hash.\n        mutable.onlyHashChange = true\n        mutable.shouldScroll = result.data.shouldScroll\n        mutable.hashFragment = url.hash\n        // Setting this to an empty array triggers a scroll for all new and\n        // updated segments. See `ScrollAndFocusHandler` for more details.\n        mutable.scrollableSegments = []\n      }\n\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Success: {\n      // Received a new result.\n      mutable.cache = result.data.cacheNode\n      mutable.patchedTree = result.data.flightRouterState\n      mutable.canonicalUrl = result.data.canonicalUrl\n      mutable.scrollableSegments = result.data.scrollableSegments\n      mutable.shouldScroll = result.data.shouldScroll\n      mutable.hashFragment = result.data.hash\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Async: {\n      return result.data.then(\n        (asyncResult) =>\n          handleNavigationResult(url, state, mutable, pendingPush, asyncResult),\n        // If the navigation failed, return the current state.\n        // TODO: This matches the current behavior but we need to do something\n        // better here if the network fails.\n        () => {\n          return state\n        }\n      )\n    }\n    default: {\n      result satisfies never\n      return state\n    }\n  }\n}\n\nexport function navigateReducer(\n  state: ReadonlyReducerState,\n  action: NavigateAction\n): ReducerState {\n  const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } =\n    action\n  const mutable: Mutable = {}\n  const { hash } = url\n  const href = createHrefFromUrl(url)\n  const pendingPush = navigateType === 'push'\n  // we want to prune the prefetch cache on every navigation to avoid it growing too large\n  prunePrefetchCache(state.prefetchCache)\n\n  mutable.preserveCustomHistoryState = false\n  mutable.pendingPush = pendingPush\n\n  if (isExternalUrl) {\n    return handleExternalUrl(state, mutable, url.toString(), pendingPush)\n  }\n\n  // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n  // which will trigger an MPA navigation.\n  if (document.getElementById('__next-page-redirect')) {\n    return handleExternalUrl(state, mutable, href, pendingPush)\n  }\n\n  if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // (Very Early Experimental Feature) Segment Cache\n    //\n    // Bypass the normal prefetch cache and use the new per-segment cache\n    // implementation instead. This is only supported if PPR is enabled, too.\n    //\n    // Temporary glue code between the router reducer and the new navigation\n    // implementation. Eventually we'll rewrite the router reducer to a\n    // state machine.\n    const result = navigateUsingSegmentCache(\n      url,\n      state.cache,\n      state.tree,\n      state.nextUrl,\n      shouldScroll\n    )\n    return handleNavigationResult(url, state, mutable, pendingPush, result)\n  }\n\n  const prefetchValues = getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    tree: state.tree,\n    prefetchCache: state.prefetchCache,\n    allowAliasing,\n  })\n  const { treeAtTimeOfPrefetch, data } = prefetchValues\n\n  prefetchQueue.bump(data)\n\n  return data.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride, postponed }) => {\n      const navigatedAt = Date.now()\n\n      let isFirstRead = false\n      // we only want to mark this once\n      if (!prefetchValues.lastUsedTime) {\n        // important: we should only mark the cache node as dirty after we unsuspend from the call above\n        prefetchValues.lastUsedTime = navigatedAt\n        isFirstRead = true\n      }\n\n      if (prefetchValues.aliased) {\n        const result = handleAliasedPrefetchEntry(\n          navigatedAt,\n          state,\n          flightData,\n          url,\n          mutable\n        )\n\n        // We didn't return new router state because we didn't apply the aliased entry for some reason.\n        // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n        // will create an on-demand prefetch entry.\n        if (result === false) {\n          return navigateReducer(state, { ...action, allowAliasing: false })\n        }\n\n        return result\n      }\n\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(state, mutable, flightData, pendingPush)\n      }\n\n      const updatedCanonicalUrl = canonicalUrlOverride\n        ? createHrefFromUrl(canonicalUrlOverride)\n        : href\n\n      const onlyHashChange =\n        !!hash &&\n        state.canonicalUrl.split('#', 1)[0] ===\n          updatedCanonicalUrl.split('#', 1)[0]\n\n      // If only the hash has changed, the server hasn't sent us any new data. We can just update\n      // the mutable properties responsible for URL and scroll handling and return early.\n      if (onlyHashChange) {\n        mutable.onlyHashChange = true\n        mutable.canonicalUrl = updatedCanonicalUrl\n        mutable.shouldScroll = shouldScroll\n        mutable.hashFragment = hash\n        mutable.scrollableSegments = []\n        return handleMutable(state, mutable)\n      }\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n      let scrollableSegments: FlightSegmentPath[] = []\n      for (const normalizedFlightData of flightData) {\n        const {\n          pathToSegment: flightSegmentPath,\n          seedData,\n          head,\n          isHeadPartial,\n          isRootRender,\n        } = normalizedFlightData\n        let treePatch = normalizedFlightData.tree\n\n        // TODO-APP: remove ''\n        const flightSegmentPathWithLeadingEmpty = ['', ...flightSegmentPath]\n\n        // Create new tree based on the flightSegmentPath and router state patch\n        let newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          flightSegmentPathWithLeadingEmpty,\n          currentTree,\n          treePatch,\n          href\n        )\n\n        // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n        // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n        if (newTree === null) {\n          newTree = applyRouterStatePatchToTree(\n            // TODO-APP: remove ''\n            flightSegmentPathWithLeadingEmpty,\n            treeAtTimeOfPrefetch,\n            treePatch,\n            href\n          )\n        }\n\n        if (newTree !== null) {\n          if (\n            // This is just a paranoid check. When a route is PPRed, the server\n            // will send back a static response that's rendered from\n            // the root. If for some reason it doesn't, we fall back to the\n            // non-PPR implementation.\n            // TODO: We should get rid of the else branch and do all navigations\n            // via startPPRNavigation. The current structure is just\n            // an incremental step.\n            seedData &&\n            isRootRender &&\n            postponed\n          ) {\n            const task = startPPRNavigation(\n              navigatedAt,\n              currentCache,\n              currentTree,\n              treePatch,\n              seedData,\n              head,\n              isHeadPartial,\n              false,\n              scrollableSegments\n            )\n\n            if (task !== null) {\n              if (task.route === null) {\n                // Detected a change to the root layout. Perform an full-\n                // page navigation.\n                return handleExternalUrl(state, mutable, href, pendingPush)\n              }\n              // Use the tree computed by startPPRNavigation instead\n              // of the one computed by applyRouterStatePatchToTree.\n              // TODO: We should remove applyRouterStatePatchToTree\n              // from the PPR path entirely.\n              const patchedRouterState: FlightRouterState = task.route\n              newTree = patchedRouterState\n\n              const newCache = task.node\n              if (newCache !== null) {\n                // We've created a new Cache Node tree that contains a prefetched\n                // version of the next page. This can be rendered instantly.\n                mutable.cache = newCache\n              }\n              const dynamicRequestTree = task.dynamicRequestTree\n              if (dynamicRequestTree !== null) {\n                // The prefetched tree has dynamic holes in it. We initiate a\n                // dynamic request to fill them in.\n                //\n                // Do not block on the result. We'll immediately render the Cache\n                // Node tree and suspend on the dynamic parts. When the request\n                // comes in, we'll fill in missing data and ping React to\n                // re-render. Unlike the lazy fetching model in the non-PPR\n                // implementation, this is modeled as a single React update +\n                // streaming, rather than multiple top-level updates. (However,\n                // even in the new model, we'll still need to sometimes update the\n                // root multiple times per navigation, like if the server sends us\n                // a different response than we expected. For now, we revert back\n                // to the lazy fetching mechanism in that case.)\n                const dynamicRequest = fetchServerResponse(url, {\n                  flightRouterState: dynamicRequestTree,\n                  nextUrl: state.nextUrl,\n                })\n\n                listenForDynamicRequest(task, dynamicRequest)\n                // We store the dynamic request on the `lazyData` property of the CacheNode\n                // because we're not going to await the dynamic request here. Since we're not blocking\n                // on the dynamic request, `layout-router` will\n                // task.node.lazyData = dynamicRequest\n              } else {\n                // The prefetched tree does not contain dynamic holes — it's\n                // fully static. We can skip the dynamic request.\n              }\n            } else {\n              // Nothing changed, so reuse the old cache.\n              // TODO: What if the head changed but not any of the segment data?\n              // Is that possible? If so, we should clone the whole tree and\n              // update the head.\n              newTree = treePatch\n            }\n          } else {\n            // The static response does not include any dynamic holes, so\n            // there's no need to do a second request.\n            // TODO: As an incremental step this just reverts back to the\n            // non-PPR implementation. We can simplify this branch further,\n            // given that PPR prefetches are always static and return the whole\n            // tree. Or in the meantime we could factor it out into a\n            // separate function.\n\n            if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n              return handleExternalUrl(state, mutable, href, pendingPush)\n            }\n\n            const cache: CacheNode = createEmptyCacheNode()\n            let applied = false\n\n            if (\n              prefetchValues.status === PrefetchCacheEntryStatus.stale &&\n              !isFirstRead\n            ) {\n              // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n              // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n              // while copying over the `loading` for the segment that contains the page data.\n              // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n\n              // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n              applied = triggerLazyFetchForLeafSegments(\n                cache,\n                currentCache,\n                flightSegmentPath,\n                treePatch\n              )\n              // since we re-used the stale cache's loading state & refreshed the data,\n              // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n              prefetchValues.lastUsedTime = navigatedAt\n            } else {\n              applied = applyFlightData(\n                navigatedAt,\n                currentCache,\n                cache,\n                normalizedFlightData,\n                prefetchValues\n              )\n            }\n\n            const hardNavigate = shouldHardNavigate(\n              // TODO-APP: remove ''\n              flightSegmentPathWithLeadingEmpty,\n              currentTree\n            )\n\n            if (hardNavigate) {\n              // Copy rsc for the root node of the cache.\n              cache.rsc = currentCache.rsc\n              cache.prefetchRsc = currentCache.prefetchRsc\n\n              invalidateCacheBelowFlightSegmentPath(\n                cache,\n                currentCache,\n                flightSegmentPath\n              )\n              // Ensure the existing cache value is used when the cache was not invalidated.\n              mutable.cache = cache\n            } else if (applied) {\n              mutable.cache = cache\n              // If we applied the cache, we update the \"current cache\" value so any other\n              // segments in the FlightDataPath will be able to reference the updated cache.\n              currentCache = cache\n            }\n\n            for (const subSegment of generateSegmentsFromPatch(treePatch)) {\n              const scrollableSegmentPath = [\n                ...flightSegmentPath,\n                ...subSegment,\n              ]\n              // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n              if (\n                scrollableSegmentPath[scrollableSegmentPath.length - 1] !==\n                DEFAULT_SEGMENT_KEY\n              ) {\n                scrollableSegments.push(scrollableSegmentPath)\n              }\n            }\n          }\n\n          currentTree = newTree\n        }\n      }\n\n      mutable.patchedTree = currentTree\n      mutable.canonicalUrl = updatedCanonicalUrl\n      mutable.scrollableSegments = scrollableSegments\n      mutable.hashFragment = hash\n      mutable.shouldScroll = shouldScroll\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ServerPatchAction,\n  ReducerState,\n  ReadonlyReducerState,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyFlightData } from '../apply-flight-data'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\n\nexport function serverPatchReducer(\n  state: ReadonlyReducerState,\n  action: ServerPatchAction\n): ReducerState {\n  const {\n    serverResponse: { flightData, canonicalUrl: canonicalUrlOverride },\n    navigatedAt,\n  } = action\n\n  const mutable: Mutable = {}\n\n  mutable.preserveCustomHistoryState = false\n\n  // Handle case when navigating to page in `pages` from `app`\n  if (typeof flightData === 'string') {\n    return handleExternalUrl(\n      state,\n      mutable,\n      flightData,\n      state.pushRef.pendingPush\n    )\n  }\n\n  let currentTree = state.tree\n  let currentCache = state.cache\n\n  for (const normalizedFlightData of flightData) {\n    const { segmentPath: flightSegmentPath, tree: treePatch } =\n      normalizedFlightData\n\n    const newTree = applyRouterStatePatchToTree(\n      // TODO-APP: remove ''\n      ['', ...flightSegmentPath],\n      currentTree,\n      treePatch,\n      state.canonicalUrl\n    )\n\n    // `applyRouterStatePatchToTree` returns `null` when it determined that the server response is not applicable to the current tree.\n    // In other words, the server responded with a tree that doesn't match what the client is currently rendering.\n    // This can happen if the server patch action took longer to resolve than a subsequent navigation which would have changed the tree.\n    // Previously this case triggered an MPA navigation but it should be safe to simply discard the server response rather than forcing\n    // the entire page to reload.\n    if (newTree === null) {\n      return state\n    }\n\n    if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n      return handleExternalUrl(\n        state,\n        mutable,\n        state.canonicalUrl,\n        state.pushRef.pendingPush\n      )\n    }\n\n    const canonicalUrlOverrideHref = canonicalUrlOverride\n      ? createHrefFromUrl(canonicalUrlOverride)\n      : undefined\n\n    if (canonicalUrlOverrideHref) {\n      mutable.canonicalUrl = canonicalUrlOverrideHref\n    }\n\n    const cache: CacheNode = createEmptyCacheNode()\n    applyFlightData(navigatedAt, currentCache, cache, normalizedFlightData)\n\n    mutable.patchedTree = newTree\n    mutable.cache = cache\n\n    currentCache = cache\n    currentTree = newTree\n  }\n\n  return handleMutable(state, mutable)\n}\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  RestoreAction,\n} from '../router-reducer-types'\nimport { extractPathFromFlightRouterState } from '../compute-changed-path'\nimport { updateCacheNodeOnPopstateRestoration } from '../ppr-navigations'\n\nexport function restoreReducer(\n  state: ReadonlyReducerState,\n  action: RestoreAction\n): ReducerState {\n  const { url, tree } = action\n  const href = createHrefFromUrl(url)\n  // This action is used to restore the router state from the history state.\n  // However, it's possible that the history state no longer contains the `FlightRouterState`.\n  // We will copy over the internal state on pushState/replaceState events, but if a history entry\n  // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n  // the history state will not contain the `FlightRouterState`.\n  // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n  const treeToRestore = tree || state.tree\n\n  const oldCache = state.cache\n  const newCache = process.env.__NEXT_PPR\n    ? // When PPR is enabled, we update the cache to drop the prefetch\n      // data for any segment whose dynamic data was already received. This\n      // prevents an unnecessary flash back to PPR state during a\n      // back/forward navigation.\n      updateCacheNodeOnPopstateRestoration(oldCache, treeToRestore)\n    : oldCache\n\n  return {\n    // Set canonical url\n    canonicalUrl: href,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // Ensures that the custom history state that was set is preserved when applying this update.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: state.focusAndScrollRef,\n    cache: newCache,\n    prefetchCache: state.prefetchCache,\n    // Restore provided tree\n    tree: treeToRestore,\n    nextUrl: extractPathFromFlightRouterState(treeToRestore) ?? url.pathname,\n  }\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport { handleExternalUrl } from './reducers/navigate-reducer'\nimport type {\n  ReadonlyReducerState,\n  ReducerActions,\n} from './router-reducer-types'\n\n/**\n * Handles the case where the client router attempted to patch the tree but, due to a mismatch, the patch failed.\n * This will perform an MPA navigation to return the router to a valid state.\n */\nexport function handleSegmentMismatch(\n  state: ReadonlyReducerState,\n  action: ReducerActions,\n  treePatch: FlightRouterState\n) {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      'Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' +\n        'Reason: Segment mismatch\\n' +\n        `Last Action: ${action.type}\\n\\n` +\n        `Current Tree: ${JSON.stringify(state.tree)}\\n\\n` +\n        `Tree Patch Payload: ${JSON.stringify(treePatch)}`\n    )\n  }\n\n  return handleExternalUrl(state, {}, state.canonicalUrl, true)\n}\n", "import type { FlightRouterState } from '../../../../server/app-render/types'\nimport { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes'\n\nexport function hasInterceptionRouteInCurrentTree([\n  segment,\n  parallelRoutes,\n]: FlightRouterState): boolean {\n  // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n  if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n    return true\n  }\n\n  // If segment is not an array, apply the existing string-based check\n  if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n    return true\n  }\n\n  // Iterate through parallelRoutes if they exist\n  if (parallelRoutes) {\n    for (const key in parallelRoutes) {\n      if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  const navigatedAt = Date.now()\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          navigatedAt,\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  const navigatedAt = Date.now()\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          navigatedAt,\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n", "import { addBasePath } from './add-base-path'\n\n/**\n * Function to correctly assign location to URL\n *\n * The method will add basePath, and will also correctly add location (including if it is a relative path)\n * @param location Location that should be added to the url\n * @param url Base URL to which the location should be assigned\n */\nexport function assignLocation(location: string, url: URL): URL {\n  if (location.startsWith('.')) {\n    const urlBase = url.origin + url.pathname\n    return new URL(\n      // In order for a relative path to be added to the current url correctly, the current url must end with a slash\n      // new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n      // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n      (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location\n    )\n  }\n\n  return new URL(addBasePath(location), url.href)\n}\n", "export interface ServerReferenceInfo {\n  type: 'server-action' | 'use-cache'\n  usedArgs: [boolean, boolean, boolean, boolean, boolean, boolean]\n  hasRestArgs: boolean\n}\n\n/**\n * Extracts info about the server reference for the given server reference ID by\n * parsing the first byte of the hex-encoded ID.\n *\n * ```\n * Bit positions: [7]      [6] [5] [4] [3] [2] [1]  [0]\n * Bits:          typeBit  argMask                  restArgs\n * ```\n *\n * If the `typeBit` is `1` the server reference represents a `\"use cache\"`\n * function, otherwise a server action.\n *\n * The `argMask` encodes whether the function uses the argument at the\n * respective position.\n *\n * The `restArgs` bit indicates whether the function uses a rest parameter. It's\n * also set to 1 if the function has more than 6 args.\n *\n * @param id hex-encoded server reference ID\n */\nexport function extractInfoFromServerReferenceId(\n  id: string\n): ServerReferenceInfo {\n  const infoByte = parseInt(id.slice(0, 2), 16)\n  const typeBit = (infoByte >> 7) & 0x1\n  const argMask = (infoByte >> 1) & 0x3f\n  const restArgs = infoByte & 0x1\n  const usedArgs = Array(6)\n\n  for (let index = 0; index < 6; index++) {\n    const bitPosition = 5 - index\n    const bit = (argMask >> bitPosition) & 0x1\n    usedArgs[index] = bit === 1\n  }\n\n  return {\n    type: typeBit === 1 ? 'use-cache' : 'server-action',\n    usedArgs: usedArgs as [\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n    ],\n    hasRestArgs: restArgs === 1,\n  }\n}\n\n/**\n * Creates a sparse array containing only the used arguments based on the\n * provided action info.\n */\nexport function omitUnusedArgs(\n  args: unknown[],\n  info: ServerReferenceInfo\n): unknown[] {\n  const filteredArgs = new Array(args.length)\n\n  for (let index = 0; index < args.length; index++) {\n    if (\n      (index < 6 && info.usedArgs[index]) ||\n      // This assumes that the server reference info byte has the restArgs bit\n      // set to 1 if there are more than 6 args.\n      (index >= 6 && info.hasRestArgs)\n    ) {\n      filteredArgs[index] = args[index]\n    }\n  }\n\n  return filteredArgs\n}\n", "import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\n\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult?: ActionResult\n  actionFlightData?: NormalizedFlightData[] | string\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch('', {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n        JSON.stringify(state.tree)\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = {\n      paths: [],\n      tag: false,\n      cookie: false,\n    }\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n\n  if (contentType?.startsWith(RSC_CONTENT_TYPE_HEADER)) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    if (location) {\n      // if it was a redirection, then result is just a regular RSC payload\n      return {\n        actionFlightData: normalizeFlightData(response.f),\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender,\n      }\n    }\n\n    return {\n      actionResult: response.a,\n      actionFlightData: normalizeFlightData(response.f),\n      redirectLocation,\n      redirectType,\n      revalidatedParts,\n      isPrerender,\n    }\n  }\n\n  // Handle invalid server action responses\n  if (res.status >= 400) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const error =\n      contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(error)\n  }\n\n  return {\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n", "import {\n  ACTION_NAVIGATE,\n  ACTION_SERVER_PATCH,\n  ACTION_RESTORE,\n  ACTION_REFRESH,\n  ACTION_PREFETCH,\n  ACTION_HMR_REFRESH,\n  ACTION_SERVER_ACTION,\n} from './router-reducer-types'\nimport type {\n  ReducerActions,\n  ReducerState,\n  ReadonlyReducerState,\n} from './router-reducer-types'\nimport { navigateReducer } from './reducers/navigate-reducer'\nimport { serverPatchReducer } from './reducers/server-patch-reducer'\nimport { restoreReducer } from './reducers/restore-reducer'\nimport { refreshReducer } from './reducers/refresh-reducer'\nimport { prefetchReducer } from './reducers/prefetch-reducer'\nimport { hmrRefreshReducer } from './reducers/hmr-refresh-reducer'\nimport { serverActionReducer } from './reducers/server-action-reducer'\n\n/**\n * Reducer that handles the app-router state updates.\n */\nfunction clientReducer(\n  state: ReadonlyReducerState,\n  action: ReducerActions\n): ReducerState {\n  switch (action.type) {\n    case ACTION_NAVIGATE: {\n      return navigateReducer(state, action)\n    }\n    case ACTION_SERVER_PATCH: {\n      return serverPatchReducer(state, action)\n    }\n    case ACTION_RESTORE: {\n      return restoreReducer(state, action)\n    }\n    case ACTION_REFRESH: {\n      return refreshReducer(state, action)\n    }\n    case ACTION_HMR_REFRESH: {\n      return hmrRefreshReducer(state, action)\n    }\n    case ACTION_PREFETCH: {\n      return prefetchReducer(state, action)\n    }\n    case ACTION_SERVER_ACTION: {\n      return serverActionReducer(state, action)\n    }\n    // This case should never be hit as dispatch is strongly typed.\n    default:\n      throw new Error('Unknown action')\n  }\n}\n\nfunction serverReducer(\n  state: ReadonlyReducerState,\n  _action: ReducerActions\n): ReducerState {\n  return state\n}\n\n// we don't run the client reducer on the server, so we use a noop function for better tree shaking\nexport const reducer =\n  typeof window === 'undefined' ? serverReducer : clientReducer\n", "import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n", "import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n", "import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n", "let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete", "formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "host", "encodeURIComponent", "replace", "indexOf", "port", "querystring", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "join", "App", "ctx", "getInitialProps", "pageProps", "props", "message", "Error", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack", "parsePath", "path", "hashIndex", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "addPathPrefix", "prefix", "startsWith", "removeTrailingSlash", "route", "normalizePathTrailingSlash", "__NEXT_MANUAL_TRAILING_SLASH", "__NEXT_TRAILING_SLASH", "addBasePath", "basePath", "__NEXT_ROUTER_BASEPATH", "required", "__NEXT_MANUAL_CLIENT_BASE_PATH", "ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER", "getFlightDataPartsFromPath", "getNextFlightSegmentPath", "normalizeFlightData", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "segmentPath", "pathToSegment", "segment", "isRootRender", "flightSegmentPath", "flightData", "map", "getAppBuildId", "setAppBuildId", "globalBuildId", "buildId", "djb2Hash", "hexHash", "str", "i", "char", "charCodeAt", "toString", "setCacheBustingSearchParam", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "existingSearch", "<PERSON><PERSON><PERSON><PERSON>", "pairs", "filter", "Boolean", "createFetch", "createFromNextReadableStream", "fetchServerResponse", "urlToUrlWithoutFlightMarker", "createFromReadableStream", "NEXT_RUNTIME", "require", "urlWithoutFlightParameters", "URL", "__NEXT_CONFIG_OUTPUT", "doMpaNavigation", "canonicalUrl", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "addEventListener", "abort", "options", "flightRouterState", "nextUrl", "prefetchKind", "PrefetchKind", "AUTO", "isHmrRefresh", "fetchPriority", "TEMPORARY", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "staleTimeHeader", "parseInt", "isFlightResponse", "ok", "body", "TURBOPACK", "flightStream", "createUnclosingPrefetchStream", "response", "b", "f", "S", "err", "aborted", "console", "fetchUrl", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetch", "credentials", "priority", "callServer", "findSourceMapURL", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "read", "enqueue", "createHrefFromUrl", "includeHash", "createRouterCache<PERSON>ey", "withoutSearchParameters", "PAGE_SEGMENT_KEY", "invalidateCacheBelowFlightSegmentPath", "newCache", "existingCache", "isLastEntry", "parallelRouteKey", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "childSegmentMap", "Map", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "prefetchHead", "matchSegment", "existingSegment", "fillLazyItemsTillLeafWithHead", "navigatedAt", "routerState", "cacheNodeSeedData", "prefetchEntry", "isLastSegment", "parallelRouteState", "segmentForParallelRoute", "parallelSeedData", "existingParallelRoutesCacheNode", "hasReusablePrefetch", "kind", "status", "PrefetchCacheEntryStatus", "reusable", "parallelRouteCacheNode", "existingCacheNode", "newCacheNode", "seedNode", "loading", "existingParallelRoutes", "invalidateCacheByRouterState", "fillCacheWithNewSubTreeData", "fillCacheWithNewSubTreeDataButOnlyLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillLazyItems", "treePatch", "incomingSegment", "applyFlightData", "cache", "addRefreshMarkerToActiveParallelSegments", "refreshInactiveParallelSegments", "fetchedSegments", "Set", "refreshInactiveParallelSegmentsImpl", "rootTree", "updatedTree", "state", "updatedCache", "includeNextUrl", "refetch<PERSON>ath", "refetch<PERSON><PERSON><PERSON>", "fetchPromises", "has", "add", "fetchPromise", "then", "parallelFetchPromise", "Promise", "all", "applyRouterStatePatchToTree", "applyPatch", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "DEFAULT_SEGMENT_KEY", "newParallelRoutes", "isInPatchTreeParallelRoutes", "refetch", "isRootLayout", "currentSegment", "lastSegment", "parallelRoutePatch", "shouldHardNavigate", "isNavigatingToNewRootLayout", "currentTree", "nextTree", "currentTreeSegment", "nextTreeSegment", "currentTreeChild", "values", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ensureLeadingSlash", "normalizeAppPath", "normalizeRscURL", "reduce", "index", "segments", "isGroupSegment", "INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "find", "m", "interceptingRoute", "marker", "interceptedRoute", "concat", "splitInterceptingRoute", "computeChangedPath", "extractPathFromFlightRouterState", "getSelectedParams", "removeLeadingSlash", "segmentToPathname", "normalizeSegments", "acc", "some", "<PERSON><PERSON><PERSON>", "children", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "params", "parallelRoute", "isDynamicParameter", "segmentValue", "isCatchAll", "handleMutable", "isNotUndefined", "mutable", "shouldScroll", "patchedTree", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "decodeURIComponent", "segmentPaths", "prefetchCache", "PromiseQueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "resolve", "reject", "task", "enqueueResult", "bump", "findIndex", "bumpedItem", "splice", "unshift", "maxConcurrency", "forced", "shift", "DYNAMIC_STALETIME_MS", "STATIC_STALETIME_MS", "createSeededPrefetchCacheEntry", "getOrCreatePrefetchCacheEntry", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "INTERCEPTION_CACHE_KEY_MARKER", "createPrefetchCacheKeyImpl", "includeSearchParams", "pathnameFromUrl", "createPrefetchCacheKey", "FULL", "getExistingCacheEntry", "allowAliasing", "maybeNextUrl", "cacheKeyWithParams", "cacheKeyWithoutParams", "cacheKeyToUse", "existingEntry", "isAliased", "aliased", "entryWithoutParams", "cacheEntry", "existingCacheEntry", "getPrefetchEntryCacheStatus", "switchedToFullPrefetch", "data", "prefetchResponse", "isFullPrefetch", "createLazyPrefetchEntry", "prefixExistingPrefetchCacheEntry", "existingCacheKey", "newCache<PERSON>ey", "prefetchCache<PERSON>ey", "treeAtTimeOfPrefetch", "prefetchTime", "Date", "now", "lastUsedTime", "fresh", "prefetchQueue", "prefetchCacheEntry", "expired", "Number", "__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME", "__NEXT_CLIENT_ROUTER_STATIC_STALETIME", "stale", "prefetchReducer", "__NEXT_CLIENT_SEGMENT_CACHE", "identityReducerWhenSegmentCacheIsEnabled", "prefetchReducerImpl", "action", "useUntrackedPathname", "hasFallbackRouteParams", "workAsyncStorage", "workStore", "getStore", "fallbackRouteParams", "size", "useContext", "PathnameContext", "handleHardNavError", "useNavFailureHandler", "next", "__pendingUrl", "__NEXT_APP_NAV_FAIL_HANDLING", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalError", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "isRevalidate", "isStaticGeneration", "React", "getDerivedStateFromError", "isNextRouterError", "getDerivedStateFromProps", "previousPathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "setState", "digest", "html", "id", "div", "style", "h2", "p", "HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isDomBotUA", "userAgent", "isHtmlLimitedBotUA", "AppRouterAnnouncer", "ANNOUNCER_TYPE", "ANNOUNCER_ID", "getAnnouncerNode", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "cssText", "announcer", "ariaLive", "role", "shadow", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "portalNode", "setPortalNode", "useState", "useEffect", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "createPortal", "RedirectBoundary", "RedirectErrorBoundary", "HandleRedirect", "redirect", "redirectType", "router", "useRouter", "startTransition", "RedirectType", "isRedirectError", "getURLFromRedirectError", "getRedirectTypeFromError", "findHeadInCache", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "cacheNode", "unresolvedThenable", "pathHasPrefix", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "createEmptyCacheNode", "createPrefetchURL", "AppRouter", "isExternalURL", "globalMutable", "navigator", "_", "HistoryUpdater", "appRouterState", "useInsertionEffect", "historyState", "history", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pushState", "replaceState", "copyNextJsInternalHistoryState", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "useDeferredValue", "Router", "actionQueue", "assetPrefix", "globalError", "useActionQueue", "useMemo", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchAppRouterAction", "type", "ACTION_RESTORE", "removeEventListener", "handleUnhandledRedirect", "reason", "preventDefault", "publicAppRouterInstance", "use", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "dispatchTraverseAction", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "RuntimeStyles", "PathParamsContext", "Provider", "SearchParamsContext", "GlobalLayoutRouterContext", "AppRouterContext", "LayoutRouterContext", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "DefaultGlobalError", "runtimeStyles", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "for<PERSON>ach", "cb", "forceUpdate", "renderedStylesSize", "changed", "c", "dplId", "link", "rel", "precedence", "abortTask", "listenForDynamicRequest", "startPPRNavigation", "updateCacheNodeOnPopstateRestoration", "MPA_NAVIGATION_TASK", "node", "dynamicRequestTree", "oldCacheNode", "oldRouterState", "newRouterState", "prefetchData", "isPrefetchHeadPartial", "isSamePageNavigation", "scrollableSegmentsResult", "updateCacheNodeOnNavigation", "didFindRootLayout", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "oldParallelRoutes", "prefetchParallelRoutes", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "needsDynamicRequest", "dynamicRequestTreeChildren", "newRouterStateChild", "oldRouterStateChild", "oldSegmentMapChild", "prefetchDataChild", "newSegmentChild", "newSegment<PERSON>ath<PERSON><PERSON><PERSON>", "newSegmentKeyChild", "oldSegment<PERSON>hild", "oldCacheNodeChild", "task<PERSON><PERSON><PERSON>", "spawnReusedTask", "beginRenderingNewRouteTree", "newCacheNodeChild", "newSegmentMapChild", "taskChildRoute", "dynamicRequestTreeChild", "patchRouterStateWithNewChildren", "possiblyPartialPrefetchHead", "createCacheNodeOnNavigation", "routerStateChildren", "isLeafSegment", "cacheNodeNavigatedAt", "isPrefetchRscPartial", "spawnPendingTask", "existingCacheNodeChildren", "cacheNodeChildren", "routerStateChild", "existingSegmentMapChild", "segmentChild", "segmentPathChild", "segmentKeyChild", "existingCacheNodeChild", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "newTask", "createPendingCacheNode", "reusedRouterState", "responsePromise", "normalizedFlightData", "serverRouterState", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "rootTask", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "isDeferredRsc", "shouldUsePrefetch", "DEFERRED", "Symbol", "tag", "pendingRsc", "rej", "fulfilledRsc", "rejectedRsc", "clearCacheNodeDataForSegmentPath", "addSearchParamsToPageSegments", "handleAliasedPrefetchEntry", "currentCache", "applied", "hasLoadingComponentInSeedData", "fromEntries", "flightSegmentPathWithLeadingEmpty", "newTree", "fillNewTreeWithOnlyLoadingSegments", "rest", "newSegment", "addSearchParamsIfPageSegment", "updatedParallelRoutes", "NavigationResultTag", "PrefetchPriority", "cancelPrefetchTask", "createCacheKey", "getCurrentCacheVersion", "navigate", "prefetch", "reschedulePrefetchTask", "revalidateEntireCache", "schedulePrefetchTask", "notEnabled", "handleExternalUrl", "navigateReducer", "generateSegmentsFromPatch", "flightRouterPatch", "childSegment", "triggerLazyFetchForLeafSegments", "appliedPatch", "segmentPathsToFill", "handleNavigationResult", "MPA", "newUrl", "NoOp", "newCanonicalUrl", "oldUrl", "Success", "Async", "asyncResult", "isExternalUrl", "navigateType", "getElementById", "prefetchValues", "canonicalUrlOverride", "isFirstRead", "updatedCanonicalUrl", "patchedRouterState", "dynamicRequest", "hardNavigate", "subSegment", "scrollableSegmentPath", "serverPatchReducer", "serverResponse", "canonicalUrlOverrideHref", "restoreReducer", "treeToRestore", "<PERSON><PERSON><PERSON>", "__NEXT_PPR", "handleSegmentMismatch", "hasInterceptionRouteInCurrentTree", "refreshReducer", "log", "hmrRefreshReducer", "hmrRefreshReducerImpl", "hmrRefreshReducerNoop", "_action", "assignLocation", "urlBase", "extractInfoFromServerReferenceId", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infoByte", "typeBit", "argMask", "restArgs", "usedArgs", "bitPosition", "bit", "hasRestArgs", "info", "filteredArgs", "serverActionReducer", "createFromFetch", "createTemporaryReferenceSet", "encodeReply", "fetchServerAction", "actionId", "actionArgs", "temporaryReferences", "Accept", "redirectHeader", "_redirectType", "<PERSON><PERSON><PERSON><PERSON>", "revalidatedParts", "revalidatedHeader", "parse", "paths", "cookie", "e", "redirectLocation", "actionFlightData", "actionResult", "a", "redirectHref", "actionRevalidated", "getRedirectError", "reducer", "clientReducer", "ACTION_NAVIGATE", "ACTION_SERVER_PATCH", "ACTION_REFRESH", "ACTION_HMR_REFRESH", "ACTION_PREFETCH", "ACTION_SERVER_ACTION", "serverReducer", "createMutableActionQueue", "dispatchNavigateAction", "getCurrentAppRouterState", "runRemainingActions", "pending", "runAction", "needsRefresh", "dispatch", "prevState", "payload", "handleResult", "nextState", "discarded", "isThenable", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "newAction", "last", "globalActionQueue", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "onRouterTransitionStart", "getAppRouterActionQueue", "getProfilingHookForOnNavigationStart", "linkInstanceRef", "setLinkForCurrentNavigation", "locationSearch", "back", "forward", "scroll", "refresh", "hmrRefresh", "IDLE_LINK_STATUS", "PENDING_LINK_STATUS", "mountFormInstance", "mountLinkInstance", "onLinkVisibilityChanged", "onNavigationIntent", "pingVisibleLinks", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "linkForMostRecentNavigation", "setOptimisticLinkStatus", "prefetchable", "WeakMap", "prefetchableAndVisible", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "observeVisibility", "element", "instance", "existingInstance", "observe", "coercePrefetchableUrl", "reportErrorFn", "reportError", "prefetchEnabled", "prefetchURL", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "unobserve", "entry", "intersectionRatio", "rescheduleLinkPrefetch", "unstable_upgradeToDynamicPrefetch", "__NEXT_DYNAMIC_ON_HOVER", "existingPrefetchTask", "prefetchWithOldCacheImplementation", "Intent", "<PERSON><PERSON><PERSON>", "currentCacheVersion", "scheduleSegmentPrefetchTask", "doPrefetch", "catch", "isLocalURL", "locationOrigin", "resolved", "errorOnce", "LinkComponent", "useLinkStatus", "isModifiedEvent", "eventTarget", "currentTarget", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "as", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isDefaultPrevented", "formatStringOrUrl", "urlObjOrString", "linkStatus", "useOptimistic", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "appPrefetchKind", "createPropError", "expected", "actual", "resolvedHref", "child", "Children", "only", "childRef", "observeLinkVisibilityOnMount", "mergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "__NEXT_LINK_NO_TOUCH_START", "cloneElement", "LinkStatusContext", "createContext"], "mappings": "uIAgDgBA,MAAM,CAAA,kBAANA,GA9CAC,sBAAsB,CAAA,kBAAtBA,GAgCAC,sBAAsB,CAAA,kBAAtBA,uEAhCT,SAASD,EACdE,CAA6B,EAE7B,IAAMC,EAAwB,CAAC,EAC/B,IAAK,GAAM,CAACC,EAAKC,EAAM,GAAIH,EAAaI,OAAO,GAAI,CACjD,IAAMC,EAAWJ,CAAK,CAACC,EAAI,AACvB,MAAoB,IAAbG,EACTJ,CAAK,CAACC,EAAI,CAAGC,EADsB,AAE1BG,MAAMC,OAAO,CAACF,GACvBA,EAASG,IAAI,CAACL,CADoB,EAGlCF,CAAK,CAACC,EAAI,CAAG,CAACG,EAAUF,EAAM,AAElC,CACA,OAAOF,CACT,CAEA,SAASQ,EAAuBC,CAAc,QAC5C,AAAqB,UAAjB,AAA2B,OAApBA,EACFA,GAIW,UAAjB,EAA6B,KAAtBA,GAAuBC,MAAMD,EAAAA,GACpB,WAAjB,AACA,OADOA,EAIA,GAFAE,OAAOF,EAIlB,CAEO,SAASX,EAAuBE,CAAqB,EAC1D,IAAMD,EAAe,IAAIa,gBACzB,IAAK,GAAM,CAACX,EAAKC,EAAM,GAAIW,OAAOV,OAAO,CAACH,GACxC,GAAIK,CAD4C,KACtCC,OAAO,CAACJ,GAChB,IAAK,CADmB,GACbY,KAAQZ,EACjBH,EAAagB,EADW,IACL,CAACd,EAAKO,EAAuBM,SAGlDf,EAAaiB,GAAG,CAACf,EAAKO,EAAuBN,IAGjD,OAAOH,CACT,CAEO,SAASH,EACdqB,CAAuB,EACvB,IAAA,IAAA,EAAA,UAAA,MAAA,CAAGC,EAAH,MAAA,EAAA,EAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA,EAAA,IAAGA,CAAAA,CAAH,CAAA,CAAA,EAAA,CAAA,SAAA,CAAA,EAAsC,CAEtC,IAAK,IAAMnB,KAAgBmB,EAAkB,CAC3C,IAAK,IAAMjB,KAAOF,EAAaoB,IAAI,GAAI,AACrCF,EAAOG,MAAM,CAACnB,GAGhB,IAAK,GAAM,CAACA,EAAKC,EAAM,GAAIH,EAAaI,OAAO,GAC7Cc,AADiD,EAC1CF,MAAM,CAACd,EAAKC,EAEvB,CAEA,OAAOe,CACT,2HCnCgBI,SAAS,CAAA,kBAATA,GA6DAC,oBAAoB,CAAA,kBAApBA,GAfHC,aAAa,CAAA,kBAAbA,6FAlDgB,CAAA,CAAA,IAAA,KAEvBC,EAAmB,yBAElB,SAASH,EAAUI,CAAiB,EACzC,GAAI,MAAEC,CAAI,UAAEC,CAAQ,CAAE,CAAGF,EACrBG,EAAWH,EAAOG,QAAQ,EAAI,GAC9BC,EAAWJ,EAAOI,QAAQ,EAAI,GAC9BC,EAAOL,EAAOK,IAAI,EAAI,GACtB9B,EAAQyB,EAAOzB,KAAK,EAAI,GACxB+B,GAAuB,EAE3BL,EAAOA,EAAOM,mBAAmBN,GAAMO,OAAO,CAAC,OAAQ,KAAO,IAAM,GAEhER,EAAOM,IAAI,CACbA,CADe,CACRL,EAAOD,EAAOM,IAAI,CAChBJ,IACTI,EAAOL,GAAQ,CADI,AACHC,EAASO,CAAV,MAAiB,CAAC,KAAQ,IAAGP,EAAS,IAAKA,CAAAA,CAAO,CAC7DF,EAAOU,IAAI,EAAE,CACfJ,GAAQ,IAAMN,EAAOU,IAAI,AAAJA,GAIrBnC,GAA0B,UAAjB,AAA2B,OAApBA,GAClBA,GAAQW,OAAOyB,EAAYtC,sBAAsB,CAACE,GAAAA,EAGpD,IAAIqC,EAASZ,EAAOY,MAAM,EAAKrC,GAAU,IAAGA,GAAY,GAoBxD,OAlBI4B,GAAY,CAACA,EAASU,QAAQ,CAAC,OAAMV,GAAY,GAAA,EAGnDH,EAAOc,OAAO,EACZ,CAAA,CAACX,GAAYJ,EAAiBgB,IAAI,CAACZ,EAAAA,CAAQ,EAAe,KAATG,EACnD,CACAA,EAAO,KAAQA,EAAAA,EAAQ,EAAA,CAAC,CACpBF,GAA4B,AAAhBA,OAAQ,CAAC,EAAE,GAAUA,EAAW,IAAMA,CAAAA,GAC7C,AAACE,IACVA,EADgB,AACT,EAAA,EAGLD,GAAoB,MAAZA,CAAI,CAAC,EAAE,GAAUA,EAAO,IAAMA,CAAAA,EACtCO,GAAwB,MAAdA,CAAM,CAAC,EAAE,GAAUA,EAAS,IAAMA,CAAAA,EAKxC,GAAET,EAAWG,GAHrBF,EAAWA,EAGiBA,AAHRI,OAAO,CAAC,GAGWI,KAHFL,mBAAAA,GACrCK,EAASA,EAAOJ,OAAO,CAAC,IAAK,MAAA,EAEmBH,CAClD,CAEO,IAAMP,EAAgB,CAC3B,OACA,OACA,OACA,WACA,OACA,OACA,WACA,OACA,WACA,QACA,SACA,UACD,CAEM,SAASD,EAAqBmB,CAAc,EAajD,OAAOpB,EAAUoB,EACnB,6IC9FgBI,eAAAA,qCAAAA,aAT8B,CAAA,CAAA,IAAA,IASvC,SAASA,EACdC,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MACvCC,EAAWD,CAAAA,EAAAA,EAAAA,MAAM,AAANA,EAA4B,MAS7C,MAAOE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAChB,AAACC,IACC,GAAgB,OAAZA,EAAkB,CACpB,IAAMC,EAAaL,EAASI,OAAO,CAC/BC,IACFL,EAASI,MADK,CACE,CAAG,KACnBC,KAEF,IAAMC,EAAaJ,EAASE,OAAO,CAC/BE,IACFJ,EAASE,MADK,CACE,CAAG,KACnBE,IAEJ,MACMR,CADC,EAEHE,GADQ,AACCI,OAAO,CAAGG,EAAST,EAAMM,EAAAA,EAEhCL,GACFG,GAASE,AADD,OACQ,CAAGG,EAASR,EAAMK,EAAAA,CAGxC,EACA,CAACN,EAAMC,EAAK,CAEhB,CAEA,SAASQ,EACPT,CAAgC,CAChCM,CAAiB,EAEjB,GAAoB,YAAhB,OAAON,EAST,OADAA,EAAKM,OAAO,CAAGA,EACR,KACLN,EAAKM,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAMI,EAAUV,EAAKM,SACrB,AAAuB,YAAnB,AAA+B,OAAxBI,EACFA,EAEA,IAAMV,EAAK,KAEtB,CAMF,MANS,4UCwWIW,WAAW,CAAA,kBAAXA,GAoBAC,uBAAuB,CAAA,kBAAvBA,GAPAC,iBAAiB,CAAA,kBAAjBA,GAZAC,cAAc,CAAA,kBAAdA,GACAC,iBAAiB,CAAA,kBAAjBA,GATAC,EAAE,CAAA,kBAAFA,GACAC,EAAE,CAAA,kBAAFA,GAlXAC,UAAU,CAAA,kBAAVA,GAsQGC,QAAQ,CAAA,kBAARA,GA+BAC,cAAc,CAAA,kBAAdA,GAXAC,iBAAiB,CAAA,kBAAjBA,GAKAC,MAAM,CAAA,kBAANA,GAPHC,aAAa,CAAA,kBAAbA,GAmBGC,SAAS,CAAA,kBAATA,GAkBMC,mBAAmB,CAAA,kBAAnBA,GAdNC,wBAAwB,CAAA,kBAAxBA,GA+GAC,cAAc,CAAA,kBAAdA,uEA9ZT,IAAMT,EAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAAO,CAsQ9D,SAASC,EACdS,CAAK,EAEL,IACIE,EADAD,GAAO,EAGX,OAAQ,sCAAIE,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAKV,OAJKF,IACHA,EADS,CACF,EACPC,EAASF,KAAMG,IAEVD,CACT,CACF,CAIA,IAAME,EAAqB,6BACdT,EAAgB,AAAC5B,GAAgBqC,EAAmBtC,IAAI,CAACC,GAE/D,SAAS0B,IACd,GAAM,UAAEvC,CAAQ,UAAED,CAAQ,MAAEQ,CAAI,CAAE,CAAG4C,OAAOC,QAAQ,CACpD,OAAUpD,EAAS,KAAID,GAAWQ,EAAO,IAAMA,EAAbA,AAAoB,EAAA,CAAC,AACzD,CAEO,SAASiC,IACd,GAAM,CAAEa,MAAI,CAAE,CAAGF,OAAOC,QAAQ,CAC1BE,EAASf,IACf,OAAOc,EAAKE,SAAS,CAACD,EAAOE,MAAM,CACrC,CAEO,SAASlB,EAAkBmB,CAA2B,EAC3D,MAA4B,UAArB,OAAOA,EACVA,EACAA,EAAUC,WAAW,EAAID,EAAUE,IAAI,EAAI,SACjD,CAEO,SAASjB,EAAUkB,CAAmB,EAC3C,OAAOA,EAAIC,QAAQ,EAAID,EAAIE,WAAW,AACxC,CAEO,SAASlB,EAAyB/B,CAAW,EAClD,IAAMkD,EAAWlD,EAAImD,KAAK,CAAC,KAG3B,OACEC,AAHiBF,CAAQ,CAAC,EAAE,CAMzB1D,MAFD,CAEQ,CAAC,MAAO,KACfA,OAAO,CAAC,SAAU,MACpB0D,CAAAA,AAAQ,CAAC,EAAE,CAAI,IAAGA,EAASG,KAAK,CAAC,GAAGC,IAAI,AAJqB,CAIpB,KAAS,EAAA,CAAC,AAExD,CAEO,eAAexB,EAIpByB,CAAgC,CAAEC,CAAM,EAUxC,IAAMT,EAAMS,EAAIT,GAAG,EAAKS,EAAIA,GAAG,EAAIA,EAAIA,GAAG,CAACT,GAAG,CAE9C,GAAI,CAACQ,EAAIE,eAAe,EAAE,MACxB,AAAID,EAAIA,GAAG,EAAIA,EAAIZ,SAAS,CAEnB,CAFqB,AAG1Bc,UAAW,MAAM5B,EAAoB0B,EAAIZ,SAAS,CAAEY,EAAIA,GAAG,CAC7D,EAEK,CAAC,EAGV,IAAMG,EAAQ,MAAMJ,EAAIE,eAAe,CAACD,GAExC,GAAIT,GAAOlB,EAAUkB,GACnB,GADyB,IAClBY,EAGT,GAAI,CAACA,EAIH,KAJU,CAIJ,OAAA,cAAkB,CAAlB,AAAIE,MAHO,AAGDD,IAHInC,EAClB8B,GACA,+DAA8DI,EAAM,cAChE,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAazB,OAAOA,CACT,CAEO,IAAMtC,EAAK,AAAuB,oBAAhByC,YACZxC,EACXD,GACC,CAAC,OAAQ,UAAW,mBAAmB,CAAW0C,KAAK,CACtD,AAACC,GAA0C,YAA/B,OAAOF,WAAW,CAACE,EAAO,CAGnC,OAAMhD,UAAoB6C,MAAO,CACjC,MAAM1C,UAAuB0C,MAAO,CACpC,MAAMzC,UAA0ByC,MAGrCI,YAAYC,CAAY,CAAE,CACxB,KAAK,GACL,IAAI,CAACC,IAAI,CAAG,SACZ,IAAI,CAACrB,IAAI,CAAG,oBACZ,IAAI,CAACc,OAAO,CAAI,gCAA+BM,CACjD,CACF,CAEO,MAAMhD,UAA0B2C,MACrCI,YAAYC,CAAY,CAAEN,CAAe,CAAE,CACzC,KAAK,GACL,IAAI,CAACA,OAAO,CAAI,wCAAuCM,EAAK,IAAGN,CACjE,CACF,CAEO,MAAM3C,UAAgC4C,MAE3CI,aAAc,CACZ,KAAK,GACL,IAAI,CAACE,IAAI,CAAG,SACZ,IAAI,CAACP,OAAO,CAAI,mCAClB,CACF,CAWO,SAAS5B,EAAeoC,CAAY,EACzC,OAAOC,KAAKC,SAAS,CAAC,CAAEV,QAASQ,EAAMR,OAAO,CAAEW,MAAOH,EAAMG,KAAK,AAAC,EACrE,sDCxcC,aACM,SAASC,EAAUC,CAAY,EACpC,IAAMC,EAAYD,EAAKhF,OAAO,CAAC,KACzBkF,EAAaF,EAAKhF,OAAO,CAAC,KAC1BmF,EAAWD,EAAa,CAAC,IAAMD,CAAAA,CAAY,GAAKC,EAAaD,CAAAA,CAAQ,QAEvEE,AAAJ,GAAgBF,EAAY,CAAC,EACpB,CADuB,AAE5BtF,SAAUqF,EAAK/B,SAAS,CAAC,EAAGkC,EAAWD,EAAaD,GACpDnH,MAAOqH,EACHH,EAAK/B,SAAS,CAACiC,EAAYD,EAAY,CAAC,EAAIA,OAAYG,GACxD,GACJxF,KAAMqF,EAAY,CAAC,EAAID,EAAKpB,KAAK,CAACqB,GAAa,EACjD,EAGK,CAAEtF,SAAUqF,EAAMlH,MAAO,GAAI8B,KAAM,EAAG,CAC/C,0EAhBgBmF,YAAAA,qCAAAA,+ICCAM,gBAAAA,qCAAAA,aANU,CAAA,CAAA,IAAA,IAMnB,SAASA,EAAcL,CAAY,CAAEM,CAAe,EACzD,GAAI,CAACN,EAAKO,UAAU,CAAC,MAAQ,CAACD,EAC5B,MADoC,CAC7BN,EAGT,GAAM,UAAErF,CAAQ,OAAE7B,CAAK,MAAE8B,CAAI,CAAE,CAAGmF,GAAAA,EAAAA,SAAAA,AAAS,EAACC,GAC5C,MAAQ,GAAEM,EAAS3F,EAAW7B,EAAQ8B,CACxC,sDCPC,aACM,SAAS4F,EAAoBC,CAAa,EAC/C,OAAOA,EAAM1F,OAAO,CAAC,MAAO,KAAO,GACrC,0EAFgByF,sBAAAA,qCAAAA,+ICAHE,6BAAAA,qCAAAA,aAPuB,CAAA,CAAA,IAAA,QACV,CAAA,CAAA,IAAA,IAMbA,EAA6B,AAACV,IACzC,GAAI,CAACA,EAAKO,UAAU,CAAC,MAAQ/E,QAAQC,GAAG,CAACkF,4BAA4B,CACnE,CADqE,MAC9DX,EAGT,GAAM,UAAErF,CAAQ,OAAE7B,CAAK,CAAE8B,MAAI,CAAE,CAAGmF,CAAAA,EAAAA,EAAAA,SAAS,AAATA,EAAUC,GAW5C,MAAQ,GAAEQ,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC7F,GAAY7B,EAAQ8B,CACpD,oWCnBgBiG,cAAAA,qCAAAA,aALc,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,IAIpC,SAASA,EAAYb,CAAY,CAAEgB,CAAkB,EAC1D,MAAON,GAAAA,EAAAA,0BAAAA,AAA0B,EAC/BlF,AAEI6E,CAAAA,EAAAA,EAAAA,GAFI5E,GAAG,CAACwF,MAERZ,AAAa,EAACL,EAN6C,IAMvCc,AAE5B,gBAJ8C,IAAI,CAACE,WAC3ChB,kTCPKkB,aAAa,CAAA,kBAAbA,GAiBAC,cAAc,CAAA,kBAAdA,GAWAC,wBAAwB,CAAA,kBAAxBA,GAfAC,4BAA4B,CAAA,kBAA5BA,GADAC,uBAAuB,CAAA,kBAAvBA,GAmBAC,wBAAwB,CAAA,kBAAxBA,GAFAC,0BAA0B,CAAA,kBAA1BA,GACAC,2BAA2B,CAAA,kBAA3BA,GAzBAC,2BAA2B,CAAA,kBAA3BA,GAKAC,mCAAmC,CAAA,kBAAnCA,GAiBAC,6BAA6B,CAAA,kBAA7BA,GAvBAC,6BAA6B,CAAA,kBAA7BA,GAqBAC,oBAAoB,CAAA,kBAApBA,GAXAC,QAAQ,CAAA,kBAARA,GACAC,uBAAuB,CAAA,kBAAvBA,GAhBAC,UAAU,CAAA,kBAAVA,uEAAN,IAAMA,EAAa,MACbf,EAAgB,cAIhBW,EAAgC,yBAChCH,EAA8B,uBAK9BC,EACX,+BACWL,EAA0B,mBAC1BD,EAA+B,4BAC/BU,EAAW,WACXC,EAA0B,mBAE1Bb,EAAiB,CAC5Bc,EACAJ,EACAH,EACAJ,EACAK,EACD,CAEYG,EAAuB,OAEvBF,EAAgC,sBAChCR,EAA2B,qBAC3BI,EAA6B,0BAC7BC,EAA8B,2BAC9BF,EAA2B,qWCDxBW,0BAA0B,CAAA,kBAA1BA,GA4BAC,wBAAwB,CAAA,kBAAxBA,GAQAC,mBAAmB,CAAA,kBAAnBA,uEApCT,SAASF,EACdG,CAA8B,MAkBnBM,EAbX,GAAM,CAACJ,EAAMC,EAAUC,EAAMC,EAAc,CACzCL,EAAezD,KAAK,CAAC,CAAC0D,GAElBK,EAAcN,EAAezD,KAAK,CAAC,EAAG,CAAC0D,GAE7C,MAAO,CAILM,cAAeD,EAAY/D,KAAK,CAAC,EAAG,CAAC,eACrC+D,EAGAE,QAASF,AAAmC,MAAnCA,GAAAA,CAAW,CAACA,EAAYzE,MAAM,CAAG,EAAA,AAAE,EAAnCyE,EAAuC,QAChDJ,WACAC,EACAC,qBACAC,EACAI,aApB2B,IAoBbT,EAAenE,MAAM,AACrC,CACF,CAEO,GAJqCoE,MAI5BH,EACdY,CAAoC,EAIpC,OAAOA,EAAkBnE,KAAK,CAAC,EACjC,CAEO,SAASwD,EACdY,CAAsB,QAItB,AAA0B,UAAtB,AAAgC,OAAzBA,EACFA,EAGFA,EAAWC,GAAG,CAACf,EACxB,iVC1DgBgB,aAAa,CAAA,kBAAbA,GAJAC,aAAa,CAAA,kBAAbA,uEAFhB,IAAIC,EAAwB,GAErB,SAASD,EAAcE,CAAe,EAC3CD,EAAgBC,CAClB,CAEO,SAASH,IACd,OAAOE,CACT,iVCdgBE,QAAQ,CAAA,kBAARA,GASAC,OAAO,CAAA,kBAAPA,uEATT,SAASD,EAASE,CAAW,EAClC,IAAI5I,EAAO,KACX,IAAK,IAAI6I,EAAI,EAAGA,EAAID,EAAItF,MAAM,CAAEuF,IAAK,AAEnC7I,EAASA,IAAQ,CAAA,EAAKA,EADT4I,EAAIG,GACYD,OADF,CAACD,GACS,EAEvC,OAAO7I,IAAS,CAClB,CAEO,SAAS2I,EAAQC,CAAW,EACjC,OAAOF,EAASE,GAAKI,QAAQ,CAAC,IAAIhF,KAAK,CAAC,EAAG,EAC7C,4ICSaiF,6BAAAA,qCAAAA,aA1BW,CAAA,CAAA,IAAA,QAOjB,CAAA,CAAA,IAAA,IAmBMA,EAA6B,CACxCtI,EACAuI,KAEA,IAAMC,EAAiBR,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAC5B,CACEO,CAAO,CAACpC,EAAAA,2BAA2B,CAAC,EAAI,IACxCoC,CAAO,CAACnC,EAAAA,mCAAmC,CAAC,EAAI,IAChDmC,CAAO,CAACjC,EAAAA,6BAA6B,CAAC,CACtCiC,CAAO,CAAC/B,EAAAA,QAAQ,CAAC,CAClB,CAAClD,IAAI,CAAC,MAcHmF,EAAiBzI,EAAIJ,MAAM,CAI3B+I,EAAQD,CAHGD,EAAezD,UAAU,CAAC,KACvCyD,EAAepF,KAAK,CAAC,GACrBoF,CAAAA,EACmBtF,KAAK,CAAC,KAAKyF,MAAM,CAACC,SACzCF,EAAM7K,IAAI,CAAIyI,EAAAA,oBAAoB,CAAC,IAAGiC,GACtCxI,EAAIJ,MAAM,CAAG+I,EAAMhG,MAAM,CAAI,IAAGgG,EAAMrF,IAAI,CAAC,KAAS,EACtD,mVC6MgBwF,WAAW,CAAA,kBAAXA,GA8BAC,4BAA4B,CAAA,kBAA5BA,GA/KMC,mBAAmB,CAAA,kBAAnBA,GAlDNC,2BAA2B,CAAA,kBAA3BA,+EAvCT,CAAA,CAAA,IAAA,QACoB,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QAItB,CAAA,CAAA,IAAA,QACuB,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,IAjCrC,0BAAEC,CAAwB,CAAE,CAGpB,EAFZ,AAEY,CAFX,AAEW,CAFVjJ,AAEU,OA4DP,CA9DKC,GAAG,CAACiJ,IA8DAF,AA1DVG,EA0DsCpJ,CAAW,EACrD,GA/D0B,AAId,CA2DNqJ,EAA6B,AA7D/BD,IA6DmCE,IAAItJ,AA7D/B,EA6DoCuC,SAASE,MAAM,EAE/D,GADA4G,CACIpJ,CADuB3C,OACf4C,GAAG,CAACC,CADuB,CAACxB,MAAM,AACtB,CADuB4H,EAAAA,EAClB,kBADsC,EAG1B,AAFZ,GAAc,QAEvCtG,QAAQC,GAAG,CAACqJ,oBAAoB,EAChCF,EAA2BjK,QAAQ,CAACS,QAAQ,CAAC,QAC7C,CACA,GAAM,UAAET,CAAQ,CAAE,CAAGiK,EACf1G,EAASvD,EAASS,QAAQ,CAAC,cAAgB,GAAK,EAEtDwJ,EAA2BjK,QAAQ,CAAGA,EAASiE,KAAK,CAAC,EAAG,CAACV,EAC3D,CAEF,OAAO0G,CACT,CAEA,SAASG,EAAgBxJ,CAAW,EAClC,MAAO,CACLyH,WAAYwB,EAA4BjJ,GAAKqI,QAAQ,GACrDoB,aAAc5E,OACd6E,oBAAoB,EACpBC,aAAa,EACbC,WAAW,EACXC,UAAW,CAAC,CACd,CACF,CAEA,IAAIC,EAAkB,IAAIC,gBAsBnB,eAAef,EACpBhJ,CAAQ,CACRkK,CAAmC,EAEnC,GAAM,mBAAEC,CAAiB,SAAEC,CAAO,cAAEC,CAAY,CAAE,CAAGH,EAE/C3B,EAA0B,CAE9B,CAAC7B,EAAAA,UAAU,CAAC,CAAE,IAEd,CAACJ,EAAAA,6BAA6B,CAAC,CAAE/G,mBAC/B8E,KAAKC,SAAS,CAAC6F,GAEnB,EAQIE,IAAiBC,EAAAA,YAAY,CAACC,IAAI,EAAE,CACtChC,CAAO,CAACpC,EAAAA,2BAA2B,CAAC,CAAG,GAAA,EAOrCiE,IACF7B,CAAO,CAAC/B,EAAAA,CADG,OACK,CAAC,CAAG4D,CAAAA,EAGtB,GAAI,KAoCqBrH,EA/BvB,IAAM0H,EAAgBJ,EAClBA,IAAiBC,EAAAA,YAAY,CAACI,SAAS,CACrC,OACA,MACF,OAGuC,UAAU,CAA/CzK,QAAQC,GAAG,CAACqJ,oBAAoB,GAK9BvJ,CADJA,EAAM,IAAIsJ,IAAItJ,EAAAA,EACNZ,QAAQ,CAACS,QAAQ,CAAC,KACxBG,CAD8B,CAC1BZ,QAAQ,EAAI,YAEhBY,EAAIZ,QAAQ,EAAI,QAKtB,IAAM2D,EAAM,MAAM+F,EAChB9I,EACAuI,EACAkC,EACAX,EAAgBa,MAAM,EAGlBC,EAAc3B,EAA4BlG,EAAI/C,GAAG,EACjDyJ,EAAe1G,EAAI8H,UAAU,CAAGD,OAAc/F,EAE9CiG,EAAc/H,EAAIwF,OAAO,CAACwC,GAAG,CAAC,iBAAmB,GACjDC,EAAe,CAAC,CAAA,CAAA,AAAiB,OAAhBjI,EAAAA,EAAIwF,OAAO,CAACwC,GAAG,CAAC,OAAA,CAAA,CAAA,KAAA,EAAhBhI,EAAyBkI,QAAQ,CAACzE,EAAAA,SAAQ,CAAA,CAC3DoD,EAAY,CAAC,CAAC7G,EAAIwF,OAAO,CAACwC,GAAG,CAAClF,EAAAA,wBAAwB,EACtDqF,EAAkBnI,EAAIwF,OAAO,CAACwC,GAAG,CAAC1E,EAAAA,6BAA6B,EAC/DwD,EACgB,OAApBqB,EAA2BC,SAASD,EAAiB,IAAM,CAAC,EAC1DE,EAAmBN,EAAY9F,UAAU,CAACyB,EAAAA,uBAAuB,EAYrE,GAV6B,AACc,GADA,OACU,CAA/CxG,QAAQC,GAAG,CAACqJ,oBAAoB,EAC7B6B,IACHA,EAAmBN,EAAY9F,UADV,AACoB,CAAC,aAAA,EAO5C,CAACoG,GAAoB,CAACrI,EAAIsI,EAAE,EAAI,CAACtI,EAAIuI,IAAI,CAM3C,CAN6C,MAEzCtL,EAAIX,IAAI,EAAE,CACZuL,EAAYvL,IAAI,CAAGW,EAAIX,IAAAA,AAAI,EAGtBmK,EAAgBoB,EAAYvC,QAAQ,IAY7C,IAAMmD,EAAe5B,EACjB6B,AA+ER,SAASA,AACPgB,CAAgD,EAahD,IAAMC,EAASD,EAAqBE,SAAS,GAC7C,OAAO,IAAIC,eAAe,CACxB,MAAMC,KAAKC,CAAU,EACnB,MAAO,CAAM,CACX,GAAM,CAAEC,MAAI,OAAEtP,CAAK,CAAE,CAAG,MAAMiP,EAAOM,IAAI,GACzC,GAAI,CAACD,EAAM,CAGTD,EAAWG,OAAO,CAACxP,GACnB,QACF,CAGA,MACF,CACF,CACF,EACF,EA9GsCsF,EAAIuI,IAAI,EACtCvI,EAAIuI,IAAI,CACNI,EAAW,MAAO3C,EACtByC,GAGF,GAAI7D,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,MAAO+D,EAASC,CAAC,CAChC,CADkC,MAC3BnC,EAAgBzG,EAAI/C,GAAG,EAGhC,MAAO,CACLyH,WAAYZ,GAAAA,EAAAA,mBAAAA,AAAmB,EAAC6E,EAASE,CAAC,EAC1CnC,aAAcA,EACdC,mBAAoBsB,EACpBrB,YAAa+B,EAASG,CAAC,WACvBjC,YACAC,CACF,CACF,CAAE,MAAOiC,EAAK,CAWZ,OAVI,AAAChC,EAAgBa,MAAM,CAACoB,OAAO,EAAE,AACnCC,QAAQ5H,KAAK,CACV,mCAAkCpE,EAAI,wCACvC8L,GAOG,CACLrE,WAAYzH,EAAIqI,QAAQ,GACxBoB,kBAAc5E,EACd6E,oBAAoB,EACpBC,aAAa,EACbC,WAAW,EACXC,UAAW,CAAC,CACd,CACF,CACF,CAEO,SAASf,EACd9I,CAAQ,CACRuI,CAAuB,CACvBkC,CAA6C,CAC7CE,CAAoB,EAEpB,IAAMsB,EAAW,IAAI3C,IAAItJ,SAKzBsI,CAAAA,EAAAA,EAAAA,0BAAAA,AAA0B,EAAC2D,EAAU1D,GAU9B6D,MAAMH,EAAU,CAErBI,YAAa,sBACb9D,EACA+D,SAAU7B,GAAiB5F,cAC3B8F,CACF,EACF,CAEO,SAAS5B,EACdyC,CAAwC,EAExC,OAAOtC,EAAyBsC,EAAc,CAC5Ce,WAAAA,EAAAA,UAAU,CACVC,iBAAAA,EAAAA,gBAAgB,AAClB,EACF,CA1MsB,aAAa,AAA/B,OAAOlK,SAKTA,OAAO0H,gBAAgB,CAAC,WAAY,KAClCF,EAAgBG,KAAK,EACvB,GAIA3H,OAAO0H,gBAAgB,CAAC,WAAY,KAClCF,EAAkB,IAAIC,eACxB,0RC/GK,SAASmD,EACdlN,CAA8C,CAC9CmN,CAA2B,EAE3B,OAFAA,KAAAA,IAAAA,IAAAA,GAAuB,CAAA,EAEhBnN,EAAIZ,QAAQ,CAAGY,EAAIJ,MAAM,EAAIuN,CAAAA,CAAcnN,EAAIX,IAAI,CAAG,EAAA,CAAC,AAChE,0EALgB6N,oBAAAA,qCAAAA,sWCGAE,uBAAAA,qCAAAA,aAFiB,CAAA,CAAA,IAAA,IAE1B,SAASA,EACd9F,CAAgB,CAChB+F,CAAwC,QAIxC,CAJAA,KAAAA,QAAAA,GAAmC,CAAA,EAI/BzP,MAAMC,OAAO,CAACyJ,IACNA,CAAO,CAAC,EAAE,CAAC,CADK,GACFA,CAAO,CAAC,EAAE,CAAC,IAAGA,CAAO,CAAC,EAAE,CAK9C+F,GAA2B/F,EAAQtC,UAAU,CAACsI,EAAAA,gBAAgB,EACzDA,CAD4D,CAC5DA,gBAAgB,CAGlBhG,CACT,mWCZgBiG,wCAAAA,qCAAAA,AAAT,SAASA,EACdC,CAAmB,CACnBC,CAAwB,CACxBjG,CAAoC,EAEpC,IAAMkG,EAAclG,EAAkB7E,MAAM,EAAI,EAC1C,CAACgL,EAAkBrG,EAAQ,CAAGE,EAE9BoG,EAAWR,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC9F,GAEhCuG,EACJJ,EAAcK,cAAc,CAAC/C,GAAG,CAAC4C,GAEnC,GAAI,CAACE,EAGH,OAGF,IAAIE,EAAkBP,EAASM,QAND,MAMe,CAAC/C,GAAG,CAAC4C,GAOlD,GANKI,GAAmBA,IAAoBF,IAC1CE,EAAkB,IAAIC,IAAIH,GAC1BL,EAASM,MAF0D,QAE5C,CAACvP,GAAG,CAACoP,EAAkBI,IAI5CL,EAAa,YACfK,EAAgBpP,MAAM,CAACiP,GAIzB,IAAMK,EAAyBJ,EAAwB9C,GAAG,CAAC6C,GACvDM,EAAiBH,EAAgBhD,GAAG,CAAC6C,GAEpCM,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CAPI,AAQnBC,SAR4C,AAQlCD,EAAeC,MAFkB,EAEV,CACjCC,IAAKF,EAAeE,GAAG,CACvBC,YAAaH,EAAeG,WAAW,CACvCnH,KAAMgH,EAAehH,IAAI,CACzBoH,aAAcJ,EAAeI,YAAY,CACzCR,eAAgB,IAAIE,IAAIE,EAAeJ,cAAc,CACvD,EACAC,EAAgBxP,GAAG,CAACqP,EAAUM,IAGhCX,EACEW,EACAD,EACArH,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACY,IAE7B,aA/DqC,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,sWCD5B+G,eAAAA,qCAAAA,KAAN,IAAMA,EAAe,CAC1BC,EACAlH,IAGA,AAA+B,UAA3B,AAAqC,OAA9BkH,EACT,AAAuB,UAAnB,AAA6B,OAAtBlH,GAEFkH,IAAoBlH,EAK/B,AAAuB,UAAnB,AAA6B,OAAtBA,GAGJkH,CAAe,CAAC,EAAE,GAAKlH,CAAO,CAAC,EAAE,EAAIkH,CAAe,CAAC,EAAE,GAAKlH,CAAO,CAAC,EAAE,mWCP/DmH,gCAAAA,qCAAAA,AAAT,SAASA,EACdC,CAAmB,CACnBlB,CAAmB,CACnBC,CAAoC,CACpCkB,CAA8B,CAC9BC,CAA2C,CAC3C1H,CAAqB,CACrB2H,CAA6C,EAG7C,GAD6D,CACzDC,GADkB1Q,OAAOM,IAAI,CAACiQ,CAAW,CAAC,EAAE,EAAEhM,MAAM,CACrC,CACjB6K,EAAStG,IAAI,CAAGA,EAChB,MACF,CAEA,IAAK,IAAM1J,KAAOmR,CAAW,CAAC,EAAE,CAAE,CAChC,IAgGIe,EAhGEX,EAAqBJ,CAAW,CAAC,EAAE,CAACnR,EAAI,CACxCwR,EAA0BD,CAAkB,CAAC,EAAE,CAC/CnB,EAAWR,GAAAA,EAAAA,oBAAAA,AAAoB,EAAC4B,GAYhCC,EACkB,OAAtBL,QAA4D/J,IAA9B+J,CAAiB,CAAC,EAAE,CAACpR,EAAI,CACnDoR,CAAiB,CAAC,EAAE,CAACpR,EAAI,CACzB,KACN,GAAIiQ,EAAe,CACjB,IAAMyB,EACJzB,EAAcK,cAAc,CAAC/C,GAAG,CAACvN,GACnC,GAAI0R,EAAiC,CACnC,IAMIQ,EANEP,EACJN,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAeO,IAAAA,AAAI,IAAK,QACxBP,EAAcQ,MAAM,GAAKC,EAAAA,wBAAwB,CAACC,QAAQ,CAExDC,EAAyB,IAAIxB,IAAIkB,GAC/BO,EAAoBD,EAAuBzE,GAAG,CAAC6C,GAMnD8B,EAJuB,MAAM,CAA3BT,EAIa,CACbd,SAAU,KACVC,IAJea,CAIVU,AAJ0B,CAAC,EAAE,CAUlCtB,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdsB,QAZcX,CAAgB,CAAC,EAAE,CAajCnB,eAAgB,IAAIE,IAAIyB,MAAAA,EAAAA,KAAAA,EAAAA,EAAmB3B,cAAc,cACzDY,CACF,EACSS,GAAuBM,EAGjB,CACbtB,SAAUsB,EAAkBtB,KAJqB,GAIb,CACpCC,IAAKqB,EAAkBrB,GAAG,CAI1BC,YAAaoB,EAAkBpB,WAAW,CAC1CnH,KAAMuI,EAAkBvI,IAAI,CAC5BoH,aAAcmB,EAAkBnB,YAAY,CAC5CR,eAAgB,IAAIE,IAAIyB,EAAkB3B,cAAc,EACxD8B,QAASH,EAAkBG,OAAO,AACpC,EAIe,CACbzB,SAAU,KACVC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IAAIyB,MAAAA,EAAAA,KAAAA,EAAAA,EAAmB3B,cAAc,EACzD8B,QAAS,iBACTlB,CACF,EAIFc,EAAuBjR,GAAG,CAACqP,EAAU8B,GAErCjB,EACEC,EACAgB,EACAD,EACAV,EACAE,GAAsC,KACtC/H,EACA2H,GAGFrB,EAASM,IALYmB,UAKE,CAAC1Q,GAAG,CAACf,EAAKgS,GACjC,QACF,CACF,CAGA,GAAyB,OAArBP,EAA2B,CAE7B,IAAMU,EAAWV,CAAgB,CAAC,EAAE,CAC9BW,EAAUX,CAAgB,CAAC,EAAE,CACnCS,EAAe,CACbvB,SAAU,KACVC,IAAKuB,EACLtB,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,YACpB4B,cACAlB,CACF,CACF,MAGEgB,CAHK,CAGU,CACbvB,SAAU,KACVC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IACpB4B,QAAS,iBACTlB,CACF,EAGF,IAAMmB,EAAyBrC,EAASM,cAAc,CAAC/C,GAAG,CAACvN,GACvDqS,EACFA,EAAuBtR,GAAG,CAACqP,EAAU8B,GAErClC,EAASM,SAHiB,KAGH,CAACvP,GAAG,CAACf,EAAK,IAAIwQ,IAAI,CAAC,CAACJ,EAAU8B,EAAa,CAAC,GAGrEjB,EACEC,EACAgB,OACA7K,EACAkK,EACAE,EACA/H,EACA2H,EAEJ,CACF,aArKqC,CAAA,CAAA,IAAA,QAI9B,CAAA,CAAA,IAAA,sWCFSiB,+BAAAA,qCAAAA,aALqB,CAAA,CAAA,IAAA,IAK9B,SAASA,EACdtC,CAAmB,CACnBC,CAAwB,CACxBkB,CAA8B,EAG9B,IAAK,IAAMnR,KAAOmR,CAAW,CAAC,EAAE,CAAE,CAChC,IAAMK,EAA0BL,CAAW,CAAC,EAAE,CAACnR,EAAI,CAAC,EAAE,CAChDoQ,EAAWR,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC4B,GAChCE,EACJzB,EAAcK,cAAc,CAAC/C,GAAG,CAACvN,GACnC,GAAI0R,EAAiC,CACnC,IAAIM,EAAyB,IAAIxB,IAAIkB,GACrCM,EAAuB7Q,MAAM,CAACiP,GAC9BJ,EAASM,cAAc,CAACvP,GAAG,CAACf,EAAKgS,EACnC,CACF,CACF,kVCgHgBO,2BAA2B,CAAA,kBAA3BA,GAiBAC,yCAAyC,CAAA,kBAAzCA,+EAvJ6B,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACT,CAAA,CAAA,IAAA,QAEJ,CAAA,CAAA,IAAA,IAMjC,SAASC,EACPvB,CAAmB,CACnBlB,CAAmB,CACnBC,CAAwB,CACxBhG,CAAgC,CAChCoH,CAA6C,CAC7CqB,CAAsB,EAEtB,GAAM,aACJ9I,CAAW,CACXH,SAAU2H,CAAiB,CAC3B5H,KAAMmJ,CAAS,MACfjJ,CAAI,CACL,CAAGO,EACAiI,EAAelC,EACfiC,EAAoBhC,EAExB,IAAK,IAAIvF,EAAI,EAAGA,EAAId,EAAYzE,MAAM,CAAEuF,GAAK,EAAG,CAC9C,IAAMyF,EAA2BvG,CAAW,CAACc,EAAE,CACzCZ,EAAmBF,CAAW,CAACc,EAAI,EAAE,CAIrCwF,EAAcxF,IAAMd,EAAYzE,MAAM,CAAG,EACzCiL,EAAWR,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC9F,GAEhCuG,EACJ4B,EAAkB3B,cAAc,CAAC/C,GAAG,CAAC4C,GAEvC,GAAI,CAACE,EAGH,SAGF,IAAIE,EAAkB2B,EAAa5B,MANL,QAMmB,CAAC/C,GAAG,CAAC4C,GACjDI,GAAmBA,IAAoBF,IAC1CE,EAAkB,IAAIC,IAAIH,GAC1B6B,EAAa5B,MAFsD,QAExC,CAACvP,GAAG,CAACoP,EAAkBI,IAGpD,IAAME,EAAyBJ,EAAwB9C,GAAG,CAAC6C,GACvDM,EAAiBH,EAAgBhD,GAAG,CAAC6C,GAEzC,GAAIF,EAAa,CACf,GACEkB,IACC,CAACV,GACA,CAACA,EAAeC,QAAQ,EACxBD,AAFD,IAEoBD,CAAAA,CAAqB,CAC1C,CACA,IAAMmC,EAAkBxB,CAAiB,CAAC,EAAE,CACtCR,EAAMQ,CAAiB,CAAC,EAAE,CAC1BgB,EAAUhB,CAAiB,CAAC,EAAE,CAEpCV,EAAiB,CACfC,SAAU,KAGVC,IACE8B,GAAiBE,IAAoB9C,EAAAA,gBAAgB,CAAGc,EAAM,KAChEC,YAAa,KACbnH,KAAM,KACNoH,aAAc,aACdsB,EACA9B,eACEoC,GAAiBjC,EACb,IAAID,IAAIC,EAAuBH,cAAc,EAC7C,IAAIE,gBACVU,CACF,EAEIT,GAA0BiC,GAC5BJ,CAAAA,EAAAA,EAAAA,OAD2C,qBAC3CA,AAA4B,EAC1B5B,EACAD,EACAkC,GAGAD,GACFzB,CAAAA,EAAAA,EAAAA,OADiB,sBACjBA,AAA6B,EAC3BC,EACAR,EACAD,EACAkC,EACAvB,EACA1H,EACA2H,GAIJd,EAAgBxP,GAAG,CAACqP,EAAUM,EAChC,CACA,QACF,CAEKA,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CACfC,AARmB,SAAyB,AAQlCD,EAAeC,MAFkB,EAEV,CACjCC,IAAKF,EAAeE,GAAG,CACvBC,YAAaH,EAAeG,WAAW,CACvCnH,KAAMgH,EAAehH,IAAI,CACzBoH,aAAcJ,EAAeI,YAAY,CACzCR,eAAgB,IAAIE,IAAIE,EAAeJ,cAAc,EACrD8B,QAAS1B,EAAe0B,OAAO,AACjC,EACA7B,EAAgBxP,GAAG,CAACqP,EAAUM,IAIhCwB,EAAexB,EACfuB,EAAoBxB,EACtB,CACF,CAKO,SAAS8B,EACdrB,CAAmB,CACnBlB,CAAmB,CACnBC,CAAwB,CACxBhG,CAAgC,CAChCoH,CAAkC,EAElCoB,EACEvB,EACAlB,EACAC,EACAhG,EACAoH,GACA,EAEJ,CAEO,SAASmB,EACdtB,CAAmB,CACnBlB,CAAmB,CACnBC,CAAwB,CACxBhG,CAAgC,CAChCoH,CAAkC,EAElCoB,EACEvB,EACAlB,EACAC,EACAhG,EACAoH,GACA,EAEJ,mWClKgBwB,kBAAAA,qCAAAA,aAL8B,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,IAIrC,SAASA,EACd3B,CAAmB,CACnBjB,CAAwB,CACxB6C,CAAgB,CAChB7I,CAAgC,CAChCoH,CAAkC,EAGlC,GAAM,CAAE7H,KAAMmJ,CAAS,UAAElJ,CAAQ,CAAEC,MAAI,cAAEK,CAAY,CAAE,CAAGE,EAG1D,GAAiB,MAAM,CAAnBR,EACF,OAAO,EAGT,GAAIM,EAAc,CAChB,IAAM6G,EAAMnH,CAAQ,CAAC,EAAE,AAEvBqJ,GAAMV,OAAO,CADG3I,CAAQ,CAAC,AACT2I,EADW,CAE3BU,EAAMlC,GAAG,CAAGA,EAMZkC,EAAMjC,WAAW,CAAG,KACpBI,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,EAC3BC,EACA4B,EACA7C,EACA0C,EACAlJ,EACAC,EACA2H,EAEJ,MAEEyB,CAFK,CAEClC,GAAG,CAAGX,EAAcW,GAAG,CAI7BkC,EAAMjC,WAAW,CAAGZ,EAAcY,WAAW,CAC7CiC,EAAMxC,cAAc,CAAG,IAAIE,IAAIP,EAAcK,cAAc,EAC3DwC,EAAMV,OAAO,CAAGnC,EAAcmC,OAAO,CAErCG,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EACzBrB,EACA4B,EACA7C,EACAhG,EACAoH,GAIJ,OAAO,CACT,kVC4DgB0B,wCAAwC,CAAA,kBAAxCA,AAAT,SAASA,EACdvJ,CAAuB,CACvBvC,CAAY,EAEZ,GAAM,CAAC6C,EAASwG,GAAkBoD,EAAc,CAAGlK,EAOnD,IAAK,IAAMxJ,KALP8J,EAAQ2D,QAAQ,CAACqC,EAAAA,gBAAgB,GAAuB,WAAW,CAA7B4D,IACxClK,CAAI,CAAC,EAAE,CAAGvC,EACVuC,CAAI,CAAC,EAAE,CAAG,WAGM8G,EAChByC,EAAyCzC,CAAc,CAACtQ,EAAI,CAAEiH,EAElE,GA5GsB+L,CAyGc,8BAzGiB,CAAA,kBAA/BA,+EAxBU,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,QACH,CAAA,CAAA,IAAA,IAsB1B,eAAeA,EACpBtG,CAAwC,EAExC,IAAMuG,EAAkB,IAAIC,GAC5B,OAAMC,EAAoC,CACxC,GAAGzG,CAAO,CACV0G,SAAU1G,EAAQ2G,WAAW,iBAC7BJ,CACF,EACF,CAEA,eAAeE,EAAoC,CAYlD,EAZkD,GAAA,aACjDjC,CAAW,OACXoC,CAAK,aACLD,CAAW,cACXE,CAAY,gBACZC,CAAc,CACdP,iBAAe,UACfG,EAAWC,CAAW,cACtBpH,CAAY,CAIb,CAZkD,EAa3C,EAAGqE,EAAgBmD,EAAaC,EAAc,CAAGL,EACjDM,EAAgB,EAAE,CAExB,GACEF,GACAA,IAAgBxH,GACE,YAAlByH,CACA,EAEA,CAACT,EAAgBW,GAAG,CAACH,GACrB,CACAR,EAAgBY,GAAG,CAACJ,GAIpB,IAAMK,EAAetI,CAAAA,EAAAA,CAJY,CAIZA,mBAAAA,AAAmB,EACtC,IAAIM,CALsD,GAKlD2H,EAAa1O,SAASE,MAAM,EACpC,CAGE0H,UAbwF,QAarE,CAACyG,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAE,UAAU,CACrExG,QAAS4G,EAAiBF,EAAM1G,OAAO,CAAG,IAC5C,GACAmH,IAAI,CAAC,OAAC,YAAE9J,CAAU,CAAE,CAAA,EACpB,GAA0B,UAAtB,AAAgC,OAAzBA,EACT,IAAK,IAAMX,KAAkBW,EAI3B4I,CAAAA,EAAAA,EAAAA,IAJuC,WAIvCA,AAAe,EACb3B,EACAqC,EACAA,EACAjK,EAQR,GAEAqK,EAAcrT,IAAI,CAACwT,EACrB,CAEA,IAAK,IAAM9T,KAAOsQ,EAAgB,CAChC,IAAM0D,EAAuBb,EAAoC,CAC/DjC,oBACAoC,EACAD,YAAa/C,CAAc,CAACtQ,EAAI,CAChCuT,8BACAC,kBACAP,WACAG,eACAnH,CACF,GAEA0H,EAAcrT,IAAI,CAAC0T,EACrB,CAEA,MAAMC,QAAQC,GAAG,CAACP,EACpB,mWCpCgBQ,8BAAAA,qCAAT,AAASA,SAAAA,EACdnK,CAAoC,CACpC2C,CAAoC,CACpCgG,CAA4B,CAC5B1L,CAAY,EAEZ,IAqBIiO,EArBE,CAACpL,EAASwG,EAAgB9N,EAAKsS,EAASC,EAAa,CACzDpI,EAGF,GAAiC,IAA7B3C,EAAkB7E,MAAM,CAAQ,CAClC,IAAMqE,EAA0B4K,EAAWzH,EAAmBgG,GAI9D,MAFAI,CAAAA,EAAAA,EAAAA,wCAAAA,AAAwC,EAACvJ,EAAMvC,GAExCuC,CACT,CAEA,GAAM,CAACwL,EAAgB7E,EAAiB,CAAGnG,EAG3C,GAAI,CAAC+G,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACiE,EAAgBlL,GAChC,OAD0C,AACnC,KAMT,GAHiD,CAG7CmL,GAHgBjL,EAAkB7E,MAAM,CAI1C+P,CADe,CACMd,EAAW9D,CAAc,CAACH,EAAiB,CAAEwC,QASlE,GAAIuC,AAAuB,MAAM,CAPjCA,GAAqBf,EACnB/K,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACY,GACzBsG,CAAc,CAACH,EAAiB,CAChCwC,EACA1L,EAAAA,EAIA,OAAO,KAIX,IAAMuC,EAA0B,CAC9BQ,CAAiB,CAAC,EAAE,CACpB,CACE,GAAGsG,CAAc,CACjB,CAACH,EAAiB,CAAE+E,CACtB,EACA1S,EACAsS,EACD,CASD,OANIC,GACFvL,EAAI,CAAC,EAAE,EAAG,CAAA,EAGZuJ,CAJkB,AAIlBA,EAAAA,EAAAA,wCAAAA,AAAwC,EAACvJ,EAAMvC,GAExCuC,CACT,aAtIoC,CAAA,CAAA,IAAA,QACK,CAAA,CAAA,IAAA,QACZ,CAAA,CAAA,IAAA,QAC4B,CAAA,CAAA,IAAA,IAKzD,SAAS4K,EACPC,CAA8B,CAC9BC,CAA4B,EAE5B,GAAM,CAACC,EAAgBC,EAAsB,CAAGH,EAC1C,CAACI,EAAcC,EAAoB,CAAGJ,EAI5C,GACEG,IAAiBE,EAAAA,mBAAmB,EACpCJ,IAAmBI,EAAAA,mBAAmB,CAEtC,CADA,MACON,EAGT,GAAItD,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACwD,EAAgBE,GAAe,CAC9C,IAAMG,EAA0C,CAAC,EACjD,IAAK,IAAM5U,KAAOwU,EAEd,KAAoC,IAA7BE,CAAmB,CAAC1U,EAAI,CAE/B4U,CAAiB,CAAC5U,EAAI,CAAGoU,CAJY,CAKnCI,CAAqB,CAACxU,EAAI,CAC1B0U,CAAmB,CAAC1U,EAAI,EAG1B4U,CAAiB,CAAC5U,EAAI,CAAGwU,CAAqB,CAACxU,EAAI,CAIvD,IAAK,IAAMA,KAAO0U,EACZE,CAAiB,CAAC5U,EAAI,EAAE,CAI5B4U,CAAiB,CAAC5U,EAAI,CAAG0U,CAAmB,CAAC1U,EAAI,AAAJA,EAG/C,AARuC,IAQjCwJ,EAA0B,CAAC+K,EAAgBK,EAAkB,CAenE,OAZIP,CAAW,CAAC,EAAE,EAAE,CAClB7K,CAAI,CAAC,EAAE,CAAG6K,CAAW,CAAC,EAAA,AAAE,EAGtBA,CAAW,CAAC,EAAE,EAAE,CAClB7K,CAAI,CAAC,EAAE,CAAG6K,CAAW,CAAC,EAAA,AAAE,EAGtBA,CAAW,CAAC,EAAE,EAAE,CAClB7K,CAAI,CAAC,EAAE,CAAG6K,CAAW,CAAC,EAAA,AAAE,EAGnB7K,CACT,CAEA,OAAO8K,CACT,mWC7DgBa,qBAAAA,qCAAAA,AAAT,SAASA,EACdnL,CAAiC,CACjC2C,CAAoC,EAEpC,GAAM,CAAC7C,EAASwG,EAAe,CAAG3D,EAE5B,CAACqI,EAAgB7E,EAAiB,CAAGnG,QAM3C,AAAK+G,CAAAA,EAAAA,CAAD,CAACA,YAAAA,AAAY,EAACiE,EAAgBlL,GAWlC,EAFoBE,EAAkB7E,AAElC8P,GAXwC,GASA,GAAI,GAMzCE,CAJU,CAKf/L,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACY,GACzBsG,CAAc,CAACH,EAAiB,IAf5B/P,MAAMC,OAAO,CAAC2U,EAiBtB,aAnCyC,CAAA,CAkBF,AAlBE,IAAA,QACZ,CAAA,CAAA,IAAA,qWCJbI,8BAAAA,qCAAT,AAASA,SAAAA,EACdC,CAA8B,CAC9BC,CAA2B,EAG3B,IAAMC,EAAqBF,CAAW,CAAC,EAAE,CACnCG,EAAkBF,CAAQ,CAAC,EAAE,CAKnC,GAAIlV,MAAMC,OAAO,CAACkV,IAAuBnV,MAAMC,OAAO,CAACmV,IAGrD,GACED,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAJ2B,AAIzB,EAC5CD,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAAE,CAE5C,CADA,MACO,CACT,MACK,GAAID,IAAuBC,EAChC,OAAO,EAIT,GAAIH,CAAW,CAAC,CALmC,CAKjC,CAEhB,CAFkB,KAEX,CAACC,CAAQ,CAAC,EAAE,CAGrB,GAAIA,CAAQ,CAAC,EAAE,CACb,CADe,MACR,EAKT,IAAMG,EAAmB7U,OAAO8U,MAAM,CAACL,CAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CACnDM,EAAgB/U,OAAO8U,MAAM,CAACJ,CAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,OACnD,CAAKG,GAAD,CAAsBE,GACnBP,EAA4BK,EAAkBE,EACvD,MAF2C,CAAhB,MAAuB,iQCrCjD,cACM,SAASC,EAAmB3O,CAAY,EAC7C,OAAOA,EAAKO,UAAU,CAAC,KAAOP,EAAQ,IAAGA,CAC3C,0EAFgB2O,qBAAAA,qCAAAA,8HCkBAC,gBAAgB,CAAA,kBAAhBA,GAmCAC,eAAe,CAAA,kBAAfA,+EAzDmB,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,IAqBxB,SAASD,EAAiBnO,CAAa,EAC5C,MAAOkO,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EACvBlO,EAAM/B,KAAK,CAAC,KAAKoQ,MAAM,CAAC,CAACnU,EAAUkI,EAASkM,EAAOC,IAEjD,AAAI,CAACnM,GAKDoM,CAAAA,EAAAA,EAAAA,CALU,aAKI,AAAdA,EAAepM,IAKA,KAAK,CALK,AAKzBA,CAAO,CAAC,EAAE,EAMXA,CAAAA,AAAY,YAAUA,AAAY,WAAA,CAAM,EACzCkM,IAAUC,EAAS9Q,MAAM,CAAG,EAhBrBvD,CAiBP,CAIQA,EAAS,IAAGkI,EACrB,IAEP,CAMO,SAASgM,EAAgBtT,CAAW,EACzC,OAAOA,EAAIR,OAAO,CAChB,cAEA,KAEJ,yBAHkC,mGCzDrBmU,0BAA0B,CAAA,kBAA1BA,GAkBGC,mCAAmC,CAAA,kBAAnCA,GAXAC,0BAA0B,CAAA,kBAA1BA,+EAViB,CAAA,CAAA,IAAA,IAGpBF,EAA6B,CACxC,WACA,MACA,OACA,QACD,CAEM,SAASE,EAA2BpP,CAAY,EAErD,OAKUI,SAJRJ,EACGtB,KAAK,CAAC,KACN2Q,IAAI,CAAExM,AAAD,GACJqM,EAA2BG,IAAI,CAAC,AAACC,GAAMzM,EAAQtC,UAAU,CAAC+O,IAGlE,CAEO,SAASH,EAAoCnP,CAAY,EAC9D,IAAIuP,EACFC,EACAC,EAEF,IAAK,IAAM5M,KAAW7C,EAAKtB,KAAK,CAAC,KAE/B,AAFqC,GACrC8Q,CACIA,CADKN,EAA2BG,IAAI,CAAC,AAACC,GAAMzM,EAAQtC,UAAU,CAAC+O,IACvD,CACT,CAACC,EAAmBE,EAAiB,CAAGzP,EAAKtB,KAAK,CAAC8Q,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACC,EACpC,MAAM,OAAA,GADgD,WAGrD,CAFSrQ,AAAJ,MACH,+BAA8BY,EAAK,qFADhC,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAKF,OAFAuP,EAAoBX,CAAAA,EAAAA,EAAAA,gBAAgB,AAAhBA,EAAiBW,GAE7BC,GACN,IAAK,MAGDC,EADwB,CAL0B,IAKrB,CAA3BF,EACkB,IAAGE,EAEJF,EAAoB,IAAME,EAE/C,KACF,KAAK,OAEH,GAA0B,KAAK,CAA3BF,EACF,AAdmG,MAc7F,OAAA,cAEL,CAFK,AAAInQ,MACP,+BAA8BY,EAAK,gEADhC,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAEFyP,EAAmBF,EAChB7Q,KAAK,CAAC,KACNE,KAAK,CAAC,EAAG,CAAC,GACV8Q,MAAM,CAACD,GACP5Q,IAAI,CAAC,KACR,KACF,KAAK,QAEH4Q,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAME,EAAyBJ,EAAkB7Q,KAAK,CAAC,KACvD,GAAIiR,EAAuBzR,MAAM,EAAI,EACnC,CADsC,KAChC,OAAA,cAEL,CAFK,AAAIkB,MACP,+BAA8BY,EAAK,mEADhC,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGFyP,EAAmBE,EAChB/Q,KAAK,CAAC,EAAG,CAAC,GACV8Q,MAAM,CAACD,GACP5Q,IAAI,CAAC,KACR,KACF,SACE,MAAM,OAAA,cAAyC,CAAzC,AAAIO,MAAM,gCAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAwC,EAClD,CAEA,MAAO,mBAAEmQ,mBAAmBE,CAAiB,CAC/C,4HCgCgBG,kBAAkB,CAAA,kBAAlBA,GA9EAC,gCAAgC,CAAA,kBAAhCA,GA+FAC,iBAAiB,CAAA,kBAAjBA,AAAT,SAASA,EACd1B,CAA8B,CAC9B8C,CAAmB,EAInB,IAAK,IAAMC,KAJXD,KAAAA,IAAAA,IAAAA,EAAiB,EAAC,EAIUvX,OAAO8U,MAAM,CAACpF,AAFnB+E,CAAW,CAAC,EAAE,GAEsB,CACzD,IAAMvL,EAAUsO,CAAa,CAAC,EAAE,CAC1BC,EAAqBjY,MAAMC,OAAO,CAACyJ,GACnCwO,EAAeD,EAAqBvO,CAAO,CAAC,EAAE,CAAGA,CACnD,EAACwO,GAAgBA,EAAa9Q,UAAU,CAACsI,EAAAA,gBAAgB,GAAG,CAI9DuI,GAAuBvO,CAAe,OAAR,CAAC,EAAE,EAAYA,AAAe,MAArCA,EAA6B,CAAC,EAAE,AAAK,CAAG,CAG/DqO,CAAM,CAACrO,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,EAAE,CAACnE,KAAK,CAAC,KAC7B0S,IACTF,CAAM,CAACrO,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,EAAA,AAAE,EAGjCqO,EAJ+B,AAItBpB,EAAkBqB,EAAeD,GAC5C,CAEA,OAAOA,CACT,+EA/J2C,CAAA,CAAA,IAAA,QAMpC,CAAA,CAAA,IAAA,QACsB,CAAA,CAAA,IAAA,IAEvBnB,EAAqB,AAAClN,GACJ,MAAfA,CAAO,CAAC,EAAE,CAAWA,EAAQjE,KAAK,CAAC,GAAKiE,EAG3CmN,EAAoB,AAACnN,GACzB,AAAuB,UAAnB,AAA6B,OAAtBA,EAGT,AAAgB,YAAY,CAAxBA,EAA+B,GAE5BA,EAGFA,CAAO,CAAC,EAAE,CAGnB,SAASoN,EAAkBjB,CAAkB,EAC3C,OACEA,EAASF,MAAM,CAAC,CAACoB,EAAKrN,IAEpB,AAAIA,AAAY,MADhBA,EAAUkN,EAAmBlN,EAAAA,GACPoM,GAAAA,EAAAA,cAAAA,AAAc,EAACpM,GAC5BqN,EAGCA,EAAI,GAJiC,CAI9BrN,EAChB,KAAO,GAEd,CAEO,SAASgN,EACdnK,CAAoC,MAebA,EAbvB,IAAM7C,EAAU1J,MAAMC,OAAO,CAACsM,CAAiB,CAAC,EAAE,EAC9CA,CAAiB,CAAC,EAAE,CAAC,EAAE,CACvBA,CAAiB,CAAC,EAAE,CAExB,GACE7C,IAAY6K,EAAAA,mBAAmB,EAC/BwB,EAAAA,0BAA0B,CAACiB,IAAI,CAAC,AAACb,GAAMzM,EAAQtC,UAAU,CAAC+O,IAE1D,OAAOlP,AAET,GAAIyC,EAAQtC,UAAU,CAACsI,EAAAA,gBAAgB,EAAG,MAAO,GAEjD,IAAMmG,EAAW,CAACgB,EAAkBnN,GAAS,CACvCwG,EAAiB3D,AAAoB,MAApBA,GAAAA,CAAiB,CAAC,EAAA,AAAE,EAApBA,EAAwB,CAAC,EAE1C0K,EAAe/G,EAAegH,QAAQ,CACxCR,EAAiCxG,EAAegH,QAAQ,OACxDjQ,EAEJ,QAAqBA,IAAjBgQ,EACFpB,EAAS3V,GADqB,CACjB,CAAC+W,QAEd,IAAK,GAAM,CAACrX,EAAKC,EAAM,GAAIW,OAAOV,OAAO,CAACoQ,GAAiB,CACzD,GAAY,aAARtQ,EAAoB,SAExB,IAAMuX,EAAYT,EAAiC7W,QAEjCoH,IAAdkQ,GACFtB,EAAS3V,EADkB,EACd,CAACiX,EAElB,CAGF,OAAOL,EAAkBjB,EAC3B,CAyCO,SAASY,EACdY,CAAwB,CACxBC,CAAwB,EAExB,IAAMQ,EA3CR,AA2CsBV,SA3CbA,EACPC,CAAwB,CACxBC,CAAwB,EAExB,GAAM,CAACC,EAAUC,EAAgB,CAAGH,EAC9B,CAACI,EAAUC,EAAgB,CAAGJ,EAE9BK,EAAqBd,EAAkBU,GACvCK,EAAqBf,EAAkBY,GAE7C,GACE1B,EAAAA,0BAA0B,CAACiB,IAAI,CAC7B,AAACb,GACCwB,EAAmBvQ,UAAU,CAAC+O,IAAMyB,EAAmBxQ,UAAU,CAAC+O,IAGtE,CADA,KACO,GAGT,GAAI,CAACxF,CAAAA,EAAAA,EAAAA,YAAY,AAAZA,EAAa4G,EAAUE,GAAW,KAE9Bf,EAAP,OAAOA,AAAiCY,MAAjCZ,GAAAA,EAAiCY,EAAAA,CAAAA,CAAjCZ,EAA2C,EACpD,CAEA,IAAK,IAAMmB,KAAqBL,EAC9B,GAAIE,CAAe,CAACG,EAAkB,CAAE,CACtC,IAAMC,CAFuC,CAEzBV,EAClBI,CAAe,CAACK,EAAkB,CAClCH,CAAe,CAACG,EAAkB,EAEpC,GAAIC,AAAgB,MAAM,GACxB,OAAUjB,EAAkBY,GAAU,IAAGK,CAE7C,CAGF,OAAO,IACT,EAM6CT,EAAOC,UAElD,AAAmB,AAAfQ,SAAuC,KAAK,CAArBA,EAClBA,EAIFhB,EAAkBgB,EAAYvS,KAAK,CAAC,KAC7C,mWCzHgB6S,gBAAAA,qCAAAA,aAXmB,CAAA,CAAA,IAAA,IAOnC,SAASC,EAAkBxY,CAAQ,EACjC,OAAO,KAAiB,IAAVA,CAChB,CAEO,SAASuY,EACdlF,CAA2B,CAC3BoF,CAAgB,MAGKA,EAwDbA,EAxDR,IAAMC,EAAeD,AAAoB,OAApBA,EAAAA,EAAQC,YAAAA,AAAY,GAApBD,EAEjB9L,EAAU0G,EAAM1G,OAAO,CAE3B,GAAI6L,EAAeC,EAAQE,GAJkB,QAIP,EAAG,CAEvC,IAAMV,EAAcrB,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACvD,EAAM9J,IAAI,CAAEkP,EAAQE,WAAW,EAClEV,EAEFtL,EAAUsL,EACAtL,AAAD,IAETA,EAAU0G,CALK,CAKCrH,CAFG,WAEHA,AAAY,CAGhC,CAEA,MAAO,CAELA,aAAcwM,EAAeC,EAAQzM,YAAY,EAC7CyM,EAAQzM,YAAY,GAAKqH,EAAMrH,YAAY,CACzCqH,EAAMrH,YAAY,CAClByM,EAAQzM,YAAY,CACtBqH,EAAMrH,YAAY,CACtB4M,QAAS,CACPC,YAAaL,EAAeC,EAAQI,WAAW,EAC3CJ,EAAQI,WAAW,CACnBxF,EAAMuF,OAAO,CAACC,WAAW,CAC7BC,cAAeN,EAAeC,EAAQK,aAAa,EAC/CL,EAAQK,aAAa,CACrBzF,EAAMuF,OAAO,CAACE,aAAa,CAC/BC,2BAA4BP,EAC1BC,EAAQM,0BAA0B,EAEhCN,EAAQM,0BAA0B,CAClC1F,EAAMuF,OAAO,CAACG,0BACpB,AAD8C,EAG9CC,kBAAmB,CACjBC,QAAOP,MACHF,EAAeC,MAAAA,EAAAA,KAAAA,EAAAA,EAASS,kBAAkB,GAExC7F,CADA,CACM2F,iBAAiB,CAACC,KAAAA,AAAK,EAGnCE,CADI,cACYV,EAAQU,cAAc,GAAI,EAC1CC,aAAcV,EAEV,EACQU,YAAY,EAA6B,KAAzBX,EAAQW,IADI,QACQ,CAE1CC,mBAAmBZ,EAAQW,YAAY,CAACxT,KAAK,CAAC,IAC9CyN,EAAM2F,iBAAiB,CAACI,YAAY,CAEtC,KACJE,aAAcZ,EACVD,AAA2B,OAA3BA,EAAAA,QAAAA,KAAAA,EAAAA,EAASS,kBAAkB,AAAlBA,EAATT,EAA+BpF,EAAM2F,iBAAiB,CAACM,YAAY,CAEnE,EAAE,AACR,EAEAzG,MAAO4F,EAAQ5F,KAAK,CAAG4F,EAAQ5F,KAAK,CAAGQ,EAAMR,KAAK,CAClD0G,cAAed,EAAQc,aAAa,CAChCd,EAAQc,aAAa,CACrBlG,EAAMkG,aAAa,CAEvBhQ,KAAMiP,EAAeC,EAAQE,WAAW,EACpCF,EAAQE,WAAW,CACnBtF,EAAM9J,IAAI,SACdoD,CACF,CACF,4QCvFA,aASA,EAAQ,CAAC,CAPT,EAOY,OAPH,AAAgC,CAAQ,CAAE,CAAU,EACzD,GAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAU,GAChD,MAAU,AAAJ,IADuD,MACzC,kDAGxB,OAAO,CACX,qDCRA,aAEA,IAAI,EAAK,EAKT,EAAQ,CAAC,CAHT,EAGY,OAHH,AAA+B,CAAI,EACxC,MAAO,aAAe,IAAO,IAAM,CACvC,sDCDA,sFACa6M,eAAAA,qCAAAA,yCACX,EAAA,EAAA,CAAA,CAAA,YAAA,OACA,EAAA,EAAA,AADA,CACA,CAAA,UAAA,OACA,EAAA,EADA,AACA,CAAA,CAAA,GAAA,OAmDA,EAAA,EAAA,AAnDA,CAmDA,CAAA,SAAA,MAtDK,KAsDL,EAtDWA,EAcXhK,QAAWiK,CAA2B,CAAc,CAIlD,IAHIC,EACAC,EAEEC,EAAc,IAAI5F,QAAQ,CAAC6F,EAASC,KACxCJ,EAAcG,EACdF,EAAaG,CACf,GAEMC,EAAO,UACX,GAAI,CACF,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,GACL,IAAMrV,EAAS,MAAM+U,IACrBC,EAAYhV,EACd,CAAE,MAAOiC,EAAO,CACdgT,EAAWhT,EACb,QAAU,CACR,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,GACL,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,EACP,CACF,EAOA,OAHA,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOtG,IAAI,CAAC2Z,AAFK,CAAEP,UAAWG,OAAaG,CAAK,GAGrD,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,GAEEH,CACT,CAEAK,KAAKR,CAAuB,CAAE,CAC5B,IAAM1D,EAAQ,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOmE,SAAS,CAAEtZ,AAAD,GAAUA,EAAK6Y,SAAS,GAAKA,GAEjE,GAAI1D,EAAQ,CAAC,EAAG,CACd,IAAMoE,EAAa,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOC,MAAM,CAACrE,EAAO,EAAE,CAAC,EAAE,CAClD,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOsE,OAAO,CAACF,GACpB,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,EAAa,EACpB,CACF,CA5CA3T,YAAY8T,EAAiB,CAAC,CAAE,CA8ChC,OAAA,cAAA,CAAA,IAAA,CAAA,EAAA,OAAA,IArDA,OAAA,cAAA,CAAA,IAAA,CAAA,EAAA,mBAAA,KAAA,IACA,OAAA,cAAA,CAAA,IAAA,CAAA,EAAA,mBAAA,KAAA,IACA,OAAA,cAAA,CAAA,IAAA,CAAA,EAAA,mBAAA,KAAA,IAME,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAkBA,EACvB,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAgB,EACrB,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAS,EAAE,AAClB,CAkDF,CARE,SAAA,EAAaC,CAAc,EACzB,GADWA,KAAAA,QAAAA,GAAS,CAAA,EAEjB,CAAA,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAgB,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,EAAmBA,CAAAA,CAAK,EACnD,EAAA,CAAA,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOrV,MAAM,CAAG,EACrB,KACA,CAAiB,OAAA,CAAjB,EAAA,EAAA,AAAiB,CAAjB,CAAA,IAAI,CAAC,EAAA,CAAA,EAAA,CAAOsV,KAAK,EAAA,CAAA,EAAjB,EAAqBT,IAAI,EAC3B,CACF,kVCyUWU,oBAAoB,CAAA,kBAApBA,GAGAC,mBAAmB,CAAA,kBAAnBA,GAnIGC,8BAA8B,CAAA,kBAA9BA,GA9GAC,6BAA6B,CAAA,kBAA7BA,GA+NAC,kBAAkB,CAAA,kBAAlBA,+EA1XT,CAAA,CAAA,IAAA,QAMA,CAAA,CAAA,IAAA,QACuB,CAAA,CAAA,IAAA,IAmB9B,SAASE,EACPxY,CAAQ,CACRyY,CAA4B,CAC5B1T,CAAsB,EAKtB,IAAI2T,EAAkB1Y,EAAIZ,QAAQ,OAclC,CAPIqZ,IAIFC,GAAmB1Y,EAAIJ,MAAAA,AAAM,EAG3BmF,GACM,CARe,EAQbA,EADA,IACyC2T,EAG9CA,CAHcH,AAIvB,CAEA,SAASI,EACP3Y,CAAQ,CACRoP,CAA8B,CAC9BhF,CAAuB,EAEvB,OAAOoO,EAA2BxY,EAAKoP,IAAS9E,EAAAA,YAAY,CAACsO,IAAI,CAAExO,EACrE,CA8FO,SAASiO,EAA8B,CAW7C,EAX6C,GAAA,KAC5CrY,CAAG,SACHoK,CAAO,MACPpD,CAAI,CACJgQ,eAAa,MACb5H,CAAI,eACJ0J,GAAgB,CAAI,CAKrB,CAX6C,EAYtCU,EAxGR,AAwG6BX,SAxGpBA,AACP7Y,CAAQ,CACRoP,CAA2C,CAC3ChF,CAAsB,CACtB4M,CAA8C,CAC9C8B,CAAsB,EAKtB,IAAK,IAAMC,KARX3J,KAAAA,QAAAA,EAAqB9E,EAAAA,YAAY,CAACI,SAAS,AAATA,EAQP,CAACN,EAAS,KAAK,EAAE,CAC1C,IAAM4O,EAAqBR,EACzBxY,GACA,EACA+Y,GAEIE,EAAwBT,EAC5BxY,GACA,EACA+Y,GAIIG,EAAgBlZ,EAAIJ,MAAM,CAC5BoZ,EACAC,EAEEE,EAAgBnC,EAAcjM,GAAG,CAACmO,GACxC,GAAIC,GAAiBL,EAAe,CAMlC,GAHEK,CAGEC,CAHYpZ,GAAG,CAACZ,MAGL,EAHa,GAAKY,EAAIZ,QAAQ,EAC3C+Z,EAAcnZ,GAAG,CAACJ,MAAM,GAAKI,EAAIJ,MAAM,CAGvC,MAAO,CACL,GAAGuZ,CAAa,CAChBE,SAAS,CACX,EAGF,OAAOF,CACT,CAMA,IAAMG,EAAqBtC,EAAcjM,GAAG,CAACkO,GAC7C,GACEhZ,AACA6Y,GACA9Y,EAAIJ,GAFIM,GAAG,AAED,CAFEC,CAGZiP,IAAS9E,EAAAA,CAHW,WAGC,CAACsO,IAAI,EAC1BU,GAGA,CAACA,EAAmB9b,GAAG,CAACyN,GAPC,KAOO,CAACsN,GAFjC,EAIA,MAAO,CAAE,GAAGe,CAAkB,CAAED,QAAS,EAAK,CAElD,CAOA,GAEEjK,AAZE,AAWFnP,IACSqK,EAAAA,EADDpK,GAAG,CAACC,MACS,CAACyY,CADF,GACM,EAC1BE,GAEA,IAAK,IAAMS,IADX,CACyBvC,EAAc9D,MAAM,EAJpB,AAdyD,CAkBjC,AAC/C,GACEqG,EAAWvZ,GAAG,CAACZ,QAAQ,GAAKY,EAAIZ,QAAQ,EAGxC,CAACma,CAFD,CAEY/b,GAAG,CAACyN,QAAQ,CApIM,AAoILsN,KAEzB,MAAO,CAAE,GAAGgB,CAAU,CAAEF,SAAS,CAAK,CAE1C,CAIJ,EAmBIrZ,CA1BI,CA2BJoP,EACAhF,EACA4M,EACA8B,UAGF,AAAIU,GAEFA,EAAmBnK,MAAM,CAAGoK,EAA4BD,GAKtDA,AAGEE,AA9CgF,EA2C/DtK,CAPC,GAOG,GAAK9E,EAAAA,YAAY,CAGd,AAHesO,IAAI,EAC7CxJ,IAAS9E,EAAAA,YAAY,CAACsO,IAAI,EAM1BY,EAAmBG,IAAI,CAACpI,IAAI,CAAC,AAACqI,IAQ5B,GAAI,CAACC,CANHjc,MAAMC,OAAO,CAAC+b,CAMK,CANYnS,UAAU,GACzCmS,EAAiBnS,UAAU,CAACmN,IAAI,CAAC,AAACnN,GAEzBA,EAAWF,YAAY,EAA4B,OAAxBE,EAAWR,QAAQ,CACvD,EAGA,OAAO6S,EAAwB,MAC7B9S,EACAhH,cACAoK,gBACA4M,EAIA5H,KAAMA,MAAAA,EAAAA,EAAQ9E,EAAAA,YAAY,CAACI,SAAS,AACtC,EAEJ,GAKE0E,GAAQoK,EAAmBpK,IAAI,GAAK9E,EAAAA,YAAY,CAACI,SAAS,EAAE,CAC9D8O,EAAmBpK,IAAI,CAAGA,CAAAA,EAIrBoK,GAIFM,EAAwB,MAC7B9S,MACAhH,UACAoK,EACA4M,gBACA5H,KAAMA,GAAQ9E,EAAAA,YAAY,CAACI,SAC7B,AADsC,EAExC,CAmCO,SAAS0N,EAA+B,CAW9C,EAX8C,GAAA,SAC7ChO,CAAO,MACPpD,CAAI,eACJgQ,CAAa,KACbhX,CAAG,MACH2Z,CAAI,MACJvK,CAAI,CAKL,CAX8C,EAevC8K,EAAmBP,EAAKjQ,kBAAkB,CAC5CiP,EAAuB3Y,EAAKoP,EAAMhF,GAClCuO,EAAuB3Y,EAAKoP,GAE1BP,EAAgB,CACpBsL,qBAAsBnT,EACtB2S,KAAMlI,QAAQ6F,OAAO,CAACqC,GACtBvK,OACAgL,aAAcC,KAAKC,GAAG,GACtBC,aAAcF,KAAKC,GAAG,GACtBzQ,UAAW,CAAC,EACZrM,IAAK0c,EACL7K,OAAQC,EAAAA,wBAAwB,CAACkL,KAAK,KACtCxa,CACF,EAIA,OAFAgX,EAAczY,GAAG,CAAC2b,EAAkBrL,GAE7BA,CACT,CAKA,SAASiL,EAAwB,CAShC,EATgC,GAAA,KAC/B9Z,CAAG,CACHoP,MAAI,MACJpI,CAAI,SACJoD,CAAO,eACP4M,CAAa,CAId,CATgC,EAUzBkD,EAAmBvB,EAAuB3Y,EAAKoP,GAI/CuK,EAAOc,EAAAA,aAAa,CAACxN,OAAO,CAAC,IACjCjE,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAChJ,EAAK,CACvBmK,kBAAmBnD,UACnBoD,EACAC,aAAc+E,CAChB,GAAGmC,IAAI,CAAC,AAACqI,IAIP,IAAIK,EAeJ,GAbIL,EAAiBlQ,kBAAkB,EAAE,CAEvCuQ,EA/FR,AA+FsBF,SA/FbA,AAAiC,CAQzC,EARyC,GAAA,KACxC/Z,CAAG,SACHoK,CAAO,eACP4M,CAAa,CACbgD,kBAAgB,CAIjB,CARyC,EASlCR,EAAqBxC,EAAcjM,GAAG,CAACiP,GAC7C,GAAI,CAACR,EAEH,OAGF,IAAMS,EAActB,EAClB3Y,EACAwZ,CAPuB,CAOJpK,IAAI,CACvBhF,GAKF,OAHA4M,EAAczY,GAAG,CAAC0b,EAAa,CAAE,GAAGT,CAAkB,CAAEhc,IAAKyc,CAAY,GACzEjD,EAAcrY,MAAM,CAACqb,GAEdC,CACT,EAuEuD,KAC7Cja,EACAga,iBAAkBE,UAClB9P,gBACA4M,CACF,EAAA,EAME4C,EAAiBjQ,WAAW,CAAE,CAChC,IAAM6P,EAAqBxC,EAAcjM,GAAG,CAE1CkP,MAAAA,EAAAA,EAAeC,GAEbV,IACFA,EAAmBpK,IAAI,CAAG9E,EAAAA,OADJ,KACgB,CAACsO,IAAI,CACR,CAAC,GAAG,CAAnCgB,EAAiB/P,SAAS,EAG5B2P,GAAmB3P,SAAS,CAAG+P,EAAiB/P,SAAS,AAATA,EAGtD,CAEA,KAb4H,EAarH+P,CACT,IAGI/K,EAAgB,CACpBsL,qBAAsBnT,OACtB2S,OACAvK,EACAgL,aAAcC,KAAKC,GAAG,GACtBC,aAAc,KACd1Q,UAAW,CAAC,EACZrM,IAAK0c,EACL7K,OAAQC,EAAAA,wBAAwB,CAACkL,KAAK,KACtCxa,CACF,EAIA,OAFAgX,EAAczY,GAAG,CAAC2b,EAAkBrL,GAE7BA,CACT,CAEO,SAASyJ,EACdtB,CAAoD,EAEpD,IAAK,GAAM,CAACxU,EAAMkY,EAAmB,GAAI1D,EAErCyC,EAA4BiB,KAC5BpL,EAAAA,GAHoD,qBAG5B,CAACqL,OAAO,EAChC,AACA3D,EAAcrY,MAAM,CAAC6D,EAG3B,CAIO,IAAM0V,EACkD,IAA7D0C,KAAyD,EAAtCC,AAAZ5a,KAEIkY,EACiD,CAH7CjY,GAAG,AAGlB0a,CAHmBC,CAGqC,KAArCC,AAAZ7a,OAET,CAFiBC,GAAG,CAAC4a,IAEZrB,EAA4B,CAKhB,EALgB,GAAA,MACnCrK,CAAI,cACJgL,CAAY,cACZG,CAAY,WACZ1Q,CAAS,CACU,CALgB,SAMnC,AAAkB,CAAC,GAAG,CAAlBA,EASKwQ,KAAKC,GAAG,GAAKF,EAAevQ,EAC/ByF,EAAAA,wBAAwB,CAACkL,KAAK,CAC9BlL,EAAAA,wBAAwB,CAACyL,KAAK,CAIhCV,KAAKC,GAAG,GAAMC,CAAAA,MAAAA,EAAAA,EAAgBH,CAAAA,CAAW,CAAKlC,EACzCqC,EACHjL,EAAAA,gBAFkE,QAE1C,CAACC,QAAQ,CACjCD,EAAAA,wBAAwB,CAACkL,KAAK,CAMhCpL,IAAS9E,EAAAA,YAAY,CAACC,IAAI,EACxB8P,AAD0B,KACrBC,GAAG,GAAKF,EAAejC,EACvB7I,EAAAA,iBAD4C,OACpB,CAACyL,KAAK,CAKrC3L,IAAS9E,EAAAA,YAAY,CAACsO,IAAI,EAAE,AAC1ByB,KAAKC,GAAG,GAAKF,EAAejC,EACvB7I,EAAAA,iBAD4C,OACpB,CAACC,QAAQ,CAIrCD,EAAAA,wBAAwB,CAACqL,OAAO,AACzC,kVCpbaF,aAAa,CAAA,kBAAbA,GAEAO,eAAe,CAAA,kBAAfA,+EAPgB,CAAA,CAAA,IAAA,QAItB,CAAA,CAAA,IAAA,IACMP,EAAgB,IAAIxD,EAAAA,YAAY,CAAC,GAEjC+D,EAcb,SAASG,AACPrK,CAA2B,CAC3BsK,CAAsB,EAGtB9C,CAAAA,CAnB6BrY,CAmB7BqY,EAAAA,KAnBqCpY,GAAG,CAAC+a,SAmBzC3C,AAAkB,EAACxH,EAAMkG,aAAa,CAnB8B,CAqBpE,EApBEkE,CAoBI,KAAElb,CAAG,CAAE,CAAGob,EAWhB,MATA/C,CAAAA,EAAAA,EAAAA,aArBE8C,gBAqBF9C,AAA6B,EAAC,KAC5BrY,EACAoK,QAAS0G,EAAM1G,OAAO,CACtB4M,cAAelG,EAAMkG,aAAa,CAClC5H,KAAMgM,EAAOhM,IAAI,CACjBpI,KAAM8J,EAAM9J,IAAI,CAChB8R,eAAe,CACjB,GAEOhI,CACT,oWCPgBuK,uBAAAA,qCAAAA,aAtCW,CAAA,CAAA,IAAA,QACK,CAAA,CAAA,IAAA,IAqCzB,SAASA,UAKd,CAAIC,AAlCN,SAASA,EACP,GAAsB,YAiCQ,CAjC1B,OAAOhZ,OAAwB,CAEjC,GAAM,kBAAEiZ,CAAgB,CAAE,CACxBnS,EAAQ,CAAA,CAAA,IAAA,GAEJoS,EAAYD,EAAiBE,QAAQ,GAC3C,GAAI,CAACD,EAAW,OAAO,EAEvB,GAAM,qBAAEE,CAAmB,CAAE,CAAGF,SAC5B,CAACE,GAAoD,GAAG,CAAhCA,EAAoBC,IAGlD,AAHsD,AAAe,CAKrE,OAAO,CACT,IAyBSC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,eAAe,EANxB,IAOX,kVChDgBC,kBAAkB,CAAA,kBAAlBA,GAkBAC,oBAAoB,CAAA,kBAApBA,yEArBU,CAAA,CAAA,IAAA,YACQ,CAAA,CAAA,IAAA,EAE3B,SAASD,EAAmB1X,CAAc,QAC/C,EACEA,GACkB,aAAlB,OAAO9B,UACPA,OAAO0Z,IAAI,CAACC,YAAY,EACxB/O,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC,IAAI5D,IAAIhH,OAAOC,QAAQ,CAACC,IAAI,KAC5C0K,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC5K,OAAO0Z,IAAI,CAACC,YAAY,GAC5C,CACAjQ,QAAQ5H,KAAK,CACV,oEACDA,GAEF9B,OAAOC,QAAQ,CAACC,IAAI,CAAGF,OAAO0Z,IAAI,CAACC,YAAY,CAAC5T,QAAQ,IACjD,EAGX,CAEO,SAAS0T,IAyBhB,kVCmJgBI,aAAa,CAAA,kBAAbA,GAxHHC,oBAAoB,CAAA,kBAApBA,GAoFGC,WAAW,CAAA,kBAAXA,GAyBhB,OAA0B,CAAA,kBAA1B,+GApLgC,CAAA,CAAA,IAAA,SACK,CAAA,CAAA,IAAA,QACH,CAAA,CAAA,IAAA,KACC,CAAA,CAAA,IAAA,IAEnC,IAAMd,EACc,aAAlB,OAAOjZ,OAED8G,EAAQ,CAAA,CAAA,IAAA,GACRmS,gBAAgB,MAClB1W,EAEAyX,EAAS,CACblY,MAAO,CAELmY,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA6BA,SAASC,EAAe,CAAyB,EAAzB,GAAA,OAAE/Y,CAAK,CAAkB,CAAzB,EACtB,GAAImX,EAAkB,CACpB,IAAM6B,EAAQ7B,EAAiBE,QAAQ,GACvC,GAAI2B,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOC,YAAY,AAAZA,IAAgBD,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOE,kBAAAA,AAAkB,EAElD,CAFoD,KACpDtR,QAAQ5H,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,CAEO,MAAMgY,UAA6BmB,EAAAA,OAAK,CAAC3a,SAAS,CASvD,OAAO4a,yBAAyBpZ,CAAY,CAAE,CAC5C,GAAIqZ,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACrZ,GAGpB,KAH4B,CAGtBA,EAGR,MAAO,OAAEA,CAAM,CACjB,CAEA,OAAOsZ,yBACL/Z,CAAgC,CAChCmN,CAAgC,CACE,CAClC,GAAM,OAAE1M,CAAK,CAAE,CAAG0M,SAsBlB,AAAInN,EAAMvE,QAAQ,GAAK0R,EAAM6M,gBAAgB,EAAI7M,EAAM1M,KAAK,CACnD,CADqD,AAE1DA,MAAO,KACPuZ,iBAAkBha,EAAMvE,QAAQ,AAClC,EAEK,CACLgF,MAAO0M,EAAM1M,KAAK,CAClBuZ,iBAAkBha,EAAMvE,QAAQ,AAClC,CACF,CAOAwe,QAA0B,QACxB,AAAI,IAAI,CAAC9M,KAAK,CAAC1M,KAAK,CAEhB,CAAA,AAFkB,EAElB,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,GAAA,EAAA,GAAA,EAAC+Y,EAAAA,CAAe/Y,MAAO,IAAI,CAAC0M,KAAK,CAAC1M,KAAK,GACtC,IAAI,CAACT,KAAK,CAACka,WAAW,CACtB,IAAI,CAACla,KAAK,CAACma,YAAY,CACxB,CAAA,EAAA,EAAA,GAAA,EAACC,IAAI,CAACpa,KAAK,CAACqa,cAAc,CAAA,CACxB5Z,MAAO,IAAI,CAAC0M,KAAK,CAAC1M,KAAK,CACvB6Z,MAAO,IAAI,CAACA,KAAK,MAMlB,IAAI,CAACta,KAAK,CAACmR,QAAQ,AAC5B,CA1EA7Q,YAAYN,CAAgC,CAAE,CAC5C,KAAK,CAACA,GAAAA,IAAAA,CAoDRsa,KAAAA,CAAQ,KACN,IAAI,CAACC,QAAQ,CAAC,CAAE9Z,MAAO,IAAK,EAC9B,EArDE,IAAI,CAAC0M,KAAK,CAAG,CAAE1M,MAAO,KAAMuZ,iBAAkB,IAAI,CAACha,KAAK,CAACvE,QAAQ,AAAC,CACpE,CAwEF,CAKO,SAASid,EAAY,CAAyB,EAAzB,GAAA,OAAEjY,CAAK,CAAkB,CAAzB,EACpB+Z,EAA6B/Z,MAAAA,EAAAA,KAAAA,EAAAA,EAAO+Z,MAAM,CAChD,MACE,CAAA,AADF,EACE,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACnX,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACoE,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC6R,EAAAA,CAAe/Y,MAAOA,IACvB,GAAA,EAAA,GAAA,EAACka,MAAAA,CAAIC,MAAOjC,EAAOlY,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACka,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOjC,EAAOQ,IAAI,WAAE,wBACAqB,EAAS,SAAW,SAAS,8CACvB7b,OAAOC,QAAQ,CAACrD,QAAQ,CAAC,YAAU,IAC9Dif,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACM,IAAAA,CAAEF,GAAZJ,GAAmB7B,EAAOQ,IAAI,UAAI,WAAUqB,IAAgB,eAMzE,KAIA,EAAe9B,EAWR,SAASF,EAAc,CAO7B,EAP6B,GAAA,gBAC5B6B,CAAc,CACdH,aAAW,cACXC,CAAY,UACZhJ,CAAQ,CAGT,CAP6B,EAYtB1V,EAAWic,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,WACrC,AAAI2C,EAEA,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,CACChd,CAHc,QAGJA,EACV4e,eAAgBA,EAChBH,YAAaA,EACbC,aAAcA,WAEbhJ,IAKA,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,QAAA,CAAA,UAAGA,GACZ,mWC1Na4J,yBAAAA,qCAAAA,KAAN,IAAMA,EACX,wYCKOA,sBAAsB,CAAA,kBAAtBA,EAAAA,sBAAsB,EAFlBC,6BAA6B,CAAA,kBAA7BA,GAgBGC,UAAU,CAAA,kBAAVA,GAJAC,KAAK,CAAA,kBAALA,+EAlBuB,CAAA,CAAA,IAAA,IAGjCC,EACJ,8EAEWH,EAAgCD,EAAAA,sBAAsB,CAACK,MAAM,CAQ1E,SAASG,EAAmBD,CAAiB,EAC3C,OAAOP,EAAAA,sBAAsB,CAAC3e,IAAI,CAACkf,EACrC,CAEO,SAASJ,EAAMI,CAAiB,EACrC,OAAOD,OAAWC,IAAcC,EAAmBD,EACrD,CAEO,SAASL,EAAWK,CAAiB,SAC1C,AAZOH,EAA2B/e,EAY9Bif,EAZkC,CAYvBC,AAZwBA,GAa9B,MAELC,EAAmBD,CAHI,EAIlB,SAD0B,IAIrC,6ICDgBE,qBAAAA,qCAAAA,aA7B4B,CAAA,CAAA,IAAA,QACf,CAAA,CAAA,IAAA,IAGvBC,EAAiB,uBAyBhB,SAASD,EAAmB,CAAqC,EAArC,GAAA,MAAEnY,CAAI,CAA+B,CAArC,EAC3B,CAACsZ,EAAYC,EAAc,CAAGC,GAAAA,EAAAA,QAAAA,AAAQ,EAAqB,MAEjEC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KAERF,EA3BJ,AA0BsBjB,SA1BbA,GA2BSS,GAzBZR,EADJ,IAAMA,EAAoBC,SAASC,iBAAiB,CAACL,EAAe,CAAC,EAAE,CACvE,GAAIG,MAAAA,CAAAA,EAAAA,AAA6B,GAA7BA,IAAAA,EAAAA,EAAmBG,UAAAA,AAAU,EAAA,KAAA,EAA7BH,EAA+BI,UAAU,CAAC,EAAE,CAC9C,CADgD,MACzCJ,EAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE,AAC5C,EACL,IAAMC,EAAYJ,SAASK,aAAa,CAACT,GACzCQ,EAAUrB,KAAK,CAACuB,OAAO,CAAG,oBAC1B,IAAMC,EAAYP,SAASK,aAAa,CAAC,OAWzC,OAVAE,EAAUC,QAAQ,CAAG,YACrBD,EAAU1B,EAAE,CAXK,EAWFgB,yBACfU,EAAUE,IAAI,CAAG,QACjBF,EAAUxB,KAAK,CAACuB,OAAO,CACrB,+IAGaF,AACfM,EADyBC,YAAY,CAAC,CAAEC,KAAM,MAAO,GAC9CC,WAAW,CAACN,GACnBP,SAASlU,IAAI,CAAC+U,WAAW,CAACT,GACnBG,CACT,CACF,KAQW,KACL,IAAMH,EAAYJ,SAASkB,oBAAoB,CAACtB,EAAe,CAAC,EAAE,EAC9DQ,MAAAA,EAAAA,KAAAA,EAAAA,EAAWe,WAAAA,AAAW,EAAE,CAC1BnB,SAASlU,IAAI,CAACsV,WAAW,CAAChB,EAE9B,GACC,EAAE,EAEL,GAAM,CAACiB,EAAmBC,EAAqB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,AAAQ,EAAC,IACrDO,EAAgBvgB,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAqBqE,QAwBjD,MAtBA4b,CAAAA,EAAAA,EAAAA,SAAS,AAATA,EAAU,KACR,IAAIO,EAAe,GACnB,GAAIxB,SAASyB,KAAK,CAChBD,CADkB,CACHxB,SAASyB,KAAK,KACxB,CACL,IAAMC,EAAa1B,SAAS2B,aAAa,CAAC,MACtCD,IACFF,EAAeE,EAAWE,IADZ,KACqB,EAAIF,EAAWG,WAAW,EAAI,EAAA,CAErE,CAKEN,KAA0Blc,MAAZlE,OAAO,EACrBogB,EAAcpgB,OAAO,GAAKqgB,GAE1BF,EAAqBE,GAEvBD,EAAcpgB,IAHZ,GAGmB,CAAGqgB,CAC1B,EAAG,CAACha,EAAK,EAEFsZ,EAAagB,CAAAA,EAAAA,EAAAA,MAAbhB,MAAyB,AAAZgB,EAAaT,EAAmBP,CAA7CA,EAA2D,IACpE,kVCIgBiB,gBAAgB,CAAA,kBAAhBA,GApCHC,qBAAqB,CAAA,kBAArBA,+GApCoB,CAAA,CAAA,IAAA,SAEP,CAAA,CAAA,IAAA,QACwC,CAAA,CAAA,IAAA,QACpB,CAAA,CAAA,IAAA,IAO9C,SAASC,EAAe,CAQvB,EARuB,GAAA,UACtBC,CAAQ,OACRzD,CAAK,cACL0D,CAAY,CAKb,CARuB,EAShBC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,IAaxB,MAXApB,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRlD,EAAAA,OAAK,CAACuE,eAAe,CAAC,KAChBH,IAAiBI,EAAAA,YAAY,CAACjkB,IAAI,CACpC8jB,CADsC,CAC/B9jB,IAAI,CAAC4jB,EAAU,CAAC,GAEvBE,EAAOpiB,OAAO,CAACkiB,EAAU,CAAC,GAE5BzD,GACF,EACF,EAAG,CAACyD,EAAUC,EAAc1D,EAAO2D,EAAO,EAEnC,IACT,CAEO,MAAMJ,UAA8BjE,EAAAA,OAAK,CAAC3a,SAAS,CASxD,OAAO4a,yBAAyBpZ,CAAU,CAAE,CAC1C,GAAI4d,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC5d,GAGlB,KAH0B,CAGnB,CAAEsd,SAFGO,CAEOjiB,AAFPiiB,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC7d,GAEZud,aADHO,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAAC9d,EACT,CAGvC,OAAMA,CACR,CAGAwZ,QAA0B,CACxB,GAAM,UAAE8D,CAAQ,cAAEC,CAAY,CAAE,CAAG,IAAI,CAAC7Q,KAAK,QAC7C,AAAiB,OAAb4Q,GAAsC,MAAM,CAAvBC,EAErB,CAAA,EAAA,EAAA,GAAA,EAACF,EAAAA,CACCC,SAAUA,EACVC,aAAcA,EACd1D,MAAO,IAAM,IAAI,CAACC,QAAQ,CAAC,CAAEwD,SAAU,IAAK,KAK3C,IAAI,CAAC/d,KAAK,CAACmR,QAAQ,AAC5B,CA7BA7Q,YAAYN,CAA4B,CAAE,CACxC,KAAK,CAACA,GACN,IAAI,CAACmN,KAAK,CAAG,CAAE4Q,SAAU,KAAMC,aAAc,IAAK,CACpD,CA2BF,CAEO,SAASJ,EAAiB,CAA2C,EAA3C,GAAA,UAAEzM,CAAQ,CAAiC,CAA3C,EACzB8M,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,IACxB,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACL,EADH,AACGA,CAAsBI,OAAQA,WAAS9M,GAE5C,kWC1EgBqN,kBAAAA,qCAAAA,aAFqB,CAAA,CAAA,IAAA,IAE9B,SAASA,EACd7R,CAAgB,CAChBxC,CAAoC,EAEpC,OAAOsU,AAGT,SAASA,EACP9R,CAAgB,CAChBxC,CAAoC,CACpCuU,CAAiB,EAGjB,GADmBjkB,AAAuC,CACtDkkB,UADsB5jB,EACV,EADc,CAACoP,GAAgBnL,MAAM,CAGnD,MAAO,CAAC2N,EAAO+R,EAAU,CAK3B,GAAIvU,EAAegH,QAAQ,CAAE,CAC3B,GAAM,CAACxN,EAASib,EAAoB,CAAGzU,EAAegH,QAAQ,CACxD/G,EAAkBuC,EAAMxC,cAAc,CAAC/C,GAAG,CAAC,YACjD,GAAIgD,EAAiB,CACnB,IAAMH,EAAWR,GAAAA,EAAAA,oBAAAA,AAAoB,EAAC9F,GAChCkb,EAAYzU,EAAgBhD,GAAG,CAAC6C,GACtC,GAAI4U,EAAW,CACb,IAAMnkB,EAAO+jB,EACXI,EACAD,EACAF,EAAY,IAAMzU,GAEpB,GAAIvP,EAAM,OAAOA,CACnB,CACF,CACF,CAGA,IAAK,IAAMb,KAAOsQ,EAAgB,CAChC,GAAY,aAARtQ,EAAoB,SAExB,CAFiC,EAE3B,CAAC8J,EAASib,EAAoB,CAAGzU,CAAc,CAACtQ,EAAI,CACpDuQ,EAAkBuC,EAAMxC,OAH2B,OAGb,CAAC/C,GAAG,CAACvN,GACjD,GAAI,CAACuQ,EACH,SAGF,IAAMH,EAAWR,AAJK,CAILA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC9F,GAEhCkb,EAAYzU,EAAgBhD,GAAG,CAAC6C,GACtC,GAAI,CAAC4U,EACH,SAGF,AAJgB,IAIVnkB,EAAO+jB,EACXI,EACAD,EACAF,EAAY,IAAMzU,GAEpB,GAAIvP,EACF,IADQ,GACDA,CAEX,CAEA,OAAO,IACT,EA7D6BiS,EAAOxC,EAAgB,GACpD,6QCPC,sFACY2U,qBAAAA,qCAAAA,KAAN,IAAMA,EAAqB,CAChClR,KAAM,KAAO,CACf,oWCIgBmR,gBAAAA,qCAAAA,aATU,CAAA,CAAA,IAAA,IASnB,SAASA,EAAcje,CAAY,CAAEM,CAAc,EACxD,GAAI,AAAgB,UAAU,OAAnBN,EACT,OAAO,EAGT,GAAM,UAAErF,CAAQ,CAAE,CAAGoF,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAACC,GAC/B,OAAOrF,IAAa2F,GAAU3F,EAAS4F,UAAU,CAACD,EAAS,IAC7D,6ICZgB4d,cAAAA,qCAAAA,aAJc,CAAA,CAAA,IAAA,IAIvB,SAASA,EAAYle,CAAY,EACtC,MAAOie,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACje,EAH4C,GAInE,CAD6Bc,uRCDtB,SAASqd,EAAene,CAAY,SAQPA,CAKpC,0EAbgBme,iBAAAA,qCAAAA,OAJY,CAAA,CAAA,IAAA,oVC+IZC,oBAAoB,CAAA,kBAApBA,GA9EAC,iBAAiB,CAAA,kBAAjBA,GAwehB,OAwBC,CAAA,kBAxBuBC,GAnfRC,aAAa,CAAA,kBAAbA,+GA7CT,CAAA,CAAA,IAAA,SAKA,CAAA,CAAA,IAAA,QAEwB,CAAA,CAAA,IAAA,QAEG,CAAA,CAAA,IAAA,MAK3B,CAAA,CAAA,IAAA,QACiD,CAAA,CAAA,IAAA,YAKjD,CAAA,CAAA,IAAA,SACe,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,OACH,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,QAEG,CAAA,CAAA,IAAA,QAK9B,CAAA,CAAA,IAAA,QAC2D,CAAA,CAAA,IAAA,QACpB,CAAA,CAAA,IAAA,MACb,CAAA,CAAA,IAAA,IAEjC,IAAMC,EAEF,CAAC,EAEE,SAASD,EAAchjB,CAAQ,EACpC,OAAOA,EAAIyC,MAAM,GAAKH,OAAOC,QAAQ,CAACE,MAAM,AAC9C,CASO,SAASqgB,EAAkBtgB,CAAY,MAMxCxC,EAJJ,GAAI6e,CAAAA,EAAAA,EAAAA,KAAAA,AAAK,EAACvc,OAAO4gB,SAAS,CAACjE,SAAS,EAClC,CADqC,MAC9B,KAIT,GAAI,CACFjf,EAAM,IAAIsJ,IAAIhE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC9C,GAAOF,OAAOC,QAAQ,CAACC,IAAI,CACvD,CAAE,MAAO2gB,EAAG,CAGV,MAAM,OAAA,cAEL,CAFK,AAAItf,MACP,oBAAmBrB,EAAK,8CADrB,oBAAA,OAAA,kBAAA,iBAAA,CAEN,EACF,QAQA,AAAIwgB,EAAchjB,GACT,GADe,EAIjBA,CACT,CAEA,SAASojB,EAAe,CAIvB,EAJuB,GAAA,gBACtBC,CAAc,CAGf,CAJuB,EA6CtB,MAxCAC,CAAAA,EAAAA,EAAAA,kBAAkB,AAAlBA,EAAmB,KAOjB,GAAM,MAAEtc,CAAI,SAAEqP,CAAO,cAAE5M,CAAY,CAAE,CAAG4Z,EAClCE,EAAe,CACnB,GAAIlN,EAAQG,0BAA0B,CAAGlU,OAAOkhB,OAAO,CAAC1S,KAAK,CAAG,CAAC,CAAC,CAIlE2S,MAAM,EACNC,gCAAiC1c,CACnC,EAEEqP,EAAQC,WAAW,EAGnBpJ,CAAAA,CAFA,CAEAA,EAAAA,iBAAAA,AAAiB,EAAC,IAAI5D,IAAIhH,OAAOC,QAAQ,CAACC,IAAI,KAAOiH,GAGrD4M,EAAQC,SAFR,EAEmB,EAAG,EACtBhU,OAAOkhB,OAAO,CAACG,KANgF,IAMvE,CAACJ,EAAc,GAAI9Z,IAE3CnH,OAAOkhB,OAAO,CAACI,YAAY,CAACL,EAAc,GAAI9Z,EAElD,EAAG,CAAC4Z,EAAe,EAEnB5C,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KAQV,EAAG,CAAC4C,EAAejZ,OAAO,CAAEiZ,EAAerc,IAAI,CAAC,EAEzC,IACT,CAEO,SAAS6b,IACd,MAAO,CACL1U,SAAU,KACVC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IACpB4B,QAAS,KACTlB,YAAa,CAAC,CAChB,CACF,CAEA,SAASmV,EAA+BlK,CAAS,EACnC,MAARA,IAAcA,EAAO,EAAC,EAC1B,IAAMmK,EAAexhB,OAAOkhB,OAAO,CAAC1S,KAAK,CACnC2S,EAAOK,MAAAA,EAAAA,KAAAA,EAAAA,EAAcL,IAAI,CAC3BA,IACF9J,EADQ,AACH8J,IAAI,CAAGA,CAAAA,EAEd,IAAMC,EACJI,MAAAA,EAAAA,KAAAA,EAAAA,EAAcJ,+BAA+B,CAK/C,OAJIA,IACF/J,EAAK+J,2BAD8B,IACC,CAAGA,CAAAA,EAGlC/J,CACT,CAEA,SAASoK,EAAK,CAIb,EAJa,GAAA,CACZC,eAAa,CAGd,CAJa,EAQN9c,EAAyB,OAAlB8c,EAAyBA,EAAc9c,IAAI,CAAG,KACrDoH,EACJ0V,AAAkB,SAAOA,EAAc1V,YAAY,CAAG,KAGlD2V,EAAuC,OAAjB3V,EAAwBA,EAAepH,EAKnE,MAAOgd,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAAChd,EAAM+c,EAChC,CAKA,SAASE,EAAO,CAQf,EARe,IA+QVjd,EA/QU,aACdkd,CAAW,aACXC,CAAW,aACXC,CAAW,CAKZ,CARe,EASRxT,EAAQyT,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACH,GACvB,cAAE3a,CAAY,CAAE,CAAGqH,EAEnB,CAAExT,cAAY,UAAE8B,CAAQ,CAAE,CAAGolB,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,KACzC,IAAMxkB,EAAM,IAAIsJ,IACdG,EACA,AAAkB,oBAAXnH,OAAyB,WAAaA,OAAOC,QAAQ,CAACC,IAAI,EAGnE,MAAO,CAELlF,aAAc0C,EAAI1C,YAAY,CAC9B8B,SAAUujB,GAAAA,EAAAA,WAAAA,AAAW,EAAC3iB,EAAIZ,QAAQ,EAC9BwjB,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAAC5iB,EAAIZ,QAAQ,EAC3BY,EAAIZ,QACV,AADkB,CAEpB,EAAG,CAACqK,EAAa,EAqBjBgX,CAAAA,EAAAA,EAAAA,SAAS,AAATA,EAAU,KAKR,SAASgE,EAAeC,CAA0B,MAG7CpiB,EADAoiB,EAAMC,SAAS,GAChB,AAAqB,CAArB,CAAA,IAACriB,GAAAA,OAAOkhB,OAAO,CAAC1S,KAAAA,AAAK,EAAA,KAAA,EAApBxO,EAAsBohB,+BAAAA,AAA+B,GACtD,CAOFT,EAAc2B,cAAc,MAAG/f,EAE/BggB,GAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBC,KAAMC,EAAAA,cAAc,CACpB/kB,IAAK,IAAIsJ,IAAIhH,OAAOC,QAAQ,CAACC,IAAI,EACjCwE,KAAM1E,OAAOkhB,OAAO,CAAC1S,KAAK,CAAC4S,+BAA+B,AAC5D,GACF,CAIA,OAFAphB,OAAO0H,gBAAgB,CAAC,WAAYya,GAE7B,KACLniB,OAAO0iB,mBAAmB,CAAC,WAAYP,EACzC,CACF,EAAG,EAAE,EAELhE,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KAGR,SAASwE,EACPP,CAAyC,EAEzC,IAAMtgB,EAAQ,WAAYsgB,EAAQA,EAAMQ,MAAM,CAAGR,EAAMtgB,KAAK,CAC5D,GAAI4d,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC5d,GAAQ,CAC1BsgB,EAAMS,cAAc,GACpB,IAAMnlB,EAAMiiB,GAAAA,EAAAA,uBAAAA,AAAuB,EAAC7d,EAIhCud,CAHiBO,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAAC9d,KAGzB2d,EAAAA,YAAY,CAACjkB,IAAI,CACpCsnB,CADsC,CACtCA,uBAAuB,CAACtnB,IAAI,CAACkC,EAAK,CAAC,GAEnColB,EAAAA,uBAAuB,CAAC5lB,OAAO,CAACQ,EAAK,CAAC,EAE1C,CACF,CAIA,OAHAsC,OAAO0H,gBAAgB,CAAC,QAASib,GACjC3iB,OAAO0H,gBAAgB,CAAC,qBAAsBib,GAEvC,KACL3iB,OAAO0iB,mBAAmB,CAAC,QAASC,GACpC3iB,OAAO0iB,mBAAmB,CAAC,qBAAsBC,EACnD,CACF,EAAG,EAAE,EAYL,GAAM,SAAE5O,CAAO,CAAE,CAAGvF,EACpB,GAAIuF,EAAQE,aAAa,CAAE,CAEzB,GAAI0M,EAAc2B,cAAc,GAAKnb,EAAc,CACjD,IAAMlH,EAAWD,OAAOC,QAAQ,CAC5B8T,EAAQC,WAAW,CACrB/T,CADuB,CACdpF,MAAM,CAACsM,GAEhBlH,EAAS/C,OAAO,CAACiK,GAGnBwZ,EAAc2B,cAAc,CAAGnb,CACjC,CAIA4b,GAAAA,EAAAA,GAAAA,AAAG,EAAC5C,EAAAA,kBAAkB,CACxB,CAEAhC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACR,IAAM6E,EAAoBhjB,OAAOkhB,OAAO,CAACG,SAAS,CAAC4B,IAAI,CAACjjB,OAAOkhB,OAAO,EAChEgC,EAAuBljB,OAAOkhB,OAAO,CAACI,YAAY,CAAC2B,IAAI,CAC3DjjB,OAAOkhB,OAAO,EAIViC,EAAiC,AACrCzlB,QAIEsC,EAFF,IAAME,EAAOF,OAAOC,QAAQ,CAACC,IAAI,CAC3BwE,EACgB,AADhBA,OACJ1E,EAAAA,OAAOkhB,OAAO,CAAC1S,KAAAA,AAAK,EAAA,KAAA,EAApBxO,EAAsBohB,+BAA+B,CAEvD5B,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACd+C,GAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBC,KAAMC,EAAAA,cAAc,CACpB/kB,IAAK,IAAIsJ,IAAItJ,MAAAA,EAAAA,EAAOwC,EAAMA,QAC1BwE,CACF,EACF,EACF,EAOA1E,OAAOkhB,OAAO,CAACG,SAAS,CAAG,SAASA,AAClChK,CAAS,CACT+L,CAAe,CACf1lB,CAAyB,SAGrB2Z,MAAAA,EAAAA,KAAAA,EAAAA,EAAM8J,IAAI,AAAJA,IAAQ9J,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAMgM,EAAAA,AAAE,GAAE,CAI5BhM,EAAOkK,EAA+BlK,GAElC3Z,GACFylB,EADO,AACwBzlB,IANxBslB,EAAkB3L,EAAM+L,EAAS1lB,EAU5C,EAOAsC,OAAOkhB,OAAO,CAACI,YAAY,CAAG,SAASA,AACrCjK,CAAS,CACT+L,CAAe,CACf1lB,CAAyB,SAGrB2Z,MAAAA,EAAAA,KAAAA,EAAAA,EAAM8J,IAAAA,AAAI,GAAI9J,CAAAA,CAAJ,OAAIA,KAAAA,EAAAA,EAAMgM,EAAE,AAAFA,GAAI,CAG5BhM,EAAOkK,EAA+BlK,GAElC3Z,GACFylB,EADO,AACwBzlB,IALxBwlB,EAAqB7L,EAAM+L,EAAS1lB,EAQ/C,EAOA,IAAM4lB,EAAa,AAAClB,IAClB,GAAKA,CAAD,CAAO5T,KAAK,EAAE,AAMlB,GAAI,CAAC4T,EAAM5T,KAAK,CAAC2S,IAAI,CAAE,YACrBnhB,OAAOC,QAAQ,CAACsjB,MAAM,GAMxB/D,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACdgE,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EACpBxjB,OAAOC,QAAQ,CAACC,IAAI,CACpBkiB,EAAM5T,KAAK,CAAC4S,+BAA+B,CAE/C,GACF,EAIA,OADAphB,OAAO0H,gBAAgB,CAAC,WAAY4b,GAC7B,KACLtjB,OAAOkhB,OAAO,CAACG,SAAS,CAAG2B,EAC3BhjB,OAAOkhB,OAAO,CAACI,YAAY,CAAG4B,EAC9BljB,OAAO0iB,mBAAmB,CAAC,WAAYY,EACzC,CACF,EAAG,EAAE,EAEL,GAAM,CAAEtV,OAAK,CAAEtJ,MAAI,SAAEoD,CAAO,mBAAEqM,CAAiB,CAAE,CAAG3F,EAE9CiV,EAAevB,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,IACpBrC,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC7R,EAAOtJ,CAAI,CAAC,EAAE,EACpC,CAACsJ,EAAOtJ,EAAK,EAGVgf,EAAaxB,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,IAClBjQ,GAAAA,EAAAA,iBAAAA,AAAiB,EAACvN,GACxB,CAACA,EAAK,EAEHif,EAAsBzB,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,KAC3B,CACL0B,WAAYlf,EACZmf,gBAAiB7V,EACjB8V,kBAAmB,KAGnBpmB,IAAKyJ,CACP,GACC,CAACzC,EAAMsJ,EAAO7G,EAAa,EAExB4c,EAA4B7B,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,KACjC,MACLxd,oBACAyP,UACArM,EACF,EACC,CAACpD,EAAMyP,EAAmBrM,EAAQ,EAGrC,GAAqB,OAAjB2b,EAAuB,CAOzB,GAAM,CAAC/B,EAAesC,EAAQ,CAAGP,EACjC7e,EAAO,CAAA,EAAA,EAAA,AAAPA,GAAO,EAAC6c,EAAAA,CAAmBC,GAA3B9c,WAA0C8c,GAAxBsC,EACpB,MACEpf,CADK,CACE,KAGT,IAAIqf,EACF,CAAA,EAAA,EAAA,GADEA,CACF,EAAChF,EAAAA,MADCgF,UACe,CAAA,WACdrf,EACAoJ,EAAMlC,GAAG,CACV,CAAA,EAAA,EAAA,GAAA,EAAC+Q,EAAAA,kBAAkB,CAAA,CAACnY,KAAMA,OAwC9B,OAVEuf,AAUF,EATI,CAAA,EAAA,EAAA,GADFA,AACE,CASJ,CATKpK,EAAAA,OADHoK,MACgB,CAAA,CACZvI,eAAgBsG,CAAW,CAAC,EAAE,CAC9BzG,YAAayG,CAAW,CAAC,EAAE,UAE1BiC,IAML,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAACnD,EAAAA,CAAeC,eAAgBvS,IAChC,CAAA,EAAA,EAAA,GAAA,EAAC0V,EAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAACC,EAAAA,iBAAiB,CAACC,QAAQ,CAAA,CAACjpB,MAAOuoB,WACjC,CAAA,EAAA,EAAA,GAAA,EAACnK,EAAD,AAACA,eAAe,CAAC6K,QAAQ,CAAA,CAACjpB,MAAO2B,WAC/B,CAAA,EAAA,EAAA,GAAA,EAACunB,EAAD,AAACA,mBAAmB,CAACD,QAAQ,CAAA,CAACjpB,MAAOH,WACnC,CAAA,EAAA,EAAA,GAAA,EAACspB,EAAD,AAACA,yBAAyB,CAACF,QAAQ,CAAA,CACjCjpB,MAAO4oB,WAOP,CAAA,EAAA,EAAA,GAAA,EAACQ,EAAD,AAACA,gBAAgB,CAACH,QAAQ,CAAA,CAACjpB,MAAO2nB,EAAAA,uBAAuB,UACvD,CAAA,EAAA,EAAA,GAAA,EAAC0B,EAAD,AAACA,mBAAmB,CAACJ,QAAQ,CAAA,CAACjpB,MAAOwoB,WAClCM,gBASnB,CAEe,SAASxD,EAAU,CAQjC,EARiC,GAAA,aAChCqB,CAAW,CACX2C,8BAA+B,CAACC,EAAsBC,EAAkB,aACxE5C,CAAW,CAKZ,CARiC,EAWhC,MAFAtI,CAAAA,AAEA,EAFAA,EAAAA,OAEA,aAFoB,AAApBA,IAGE,CAAA,EAAA,EAAA,GAAA,EAACI,EAAAA,aAAa,CAAA,CAGZ6B,eAAgBkJ,EAAAA,OAAkB,UAElC,CAAA,EAAA,EAAA,GAAA,EAAC/C,EAAAA,AAAD,CACEC,YAAaA,EACbC,YAAaA,EACbC,YAAa,CAAC0C,EAAsBC,EAAkB,IAI9D,CAEA,IAAME,EAAgB,IAAIzW,IACtB0W,EAAsB,IAAI1W,IAa9B,SAAS8V,IACP,GAAM,EAAGkB,EAAY,CAAGnK,EAAAA,OAAK,CAACiD,QAAQ,CAAC,GACjCmH,EAAqBR,EAAcxL,IAAI,OAC7C8E,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACR,IAAMmH,EAAU,IAAMF,EAAY,AAACG,GAAMA,EAAI,GAK7C,OAJAT,EAAoB/V,GAAG,CAACuW,GACpBD,IAAuBR,EAAcxL,IAAI,EAC3CiM,AAD6C,IAGxC,KACLR,EAAoBzoB,MAAM,CAACipB,EAC7B,CACF,EAAG,CAACD,EAAoBD,EAAY,EAK7B,IAAIP,EAAc,CAACzf,GAAG,CAAC,CAAClF,EAAM0F,IACnC,AADmCA,CACnC,EAAA,EAAA,GAAA,EAAC6f,CADkC7f,MAClC6f,CAECC,IAAI,aACJxlB,KAAO,GAAEA,EAETylB,KAFgBH,MAEL,QAJN5f,GAUX,CAxCAmf,WAAWC,eAAe,CAAG,SAAU9kB,CAAY,EACjD,IAAI+kB,EAAMJ,EAAcxL,IAAI,CAO5B,OANAwL,EAAc9V,GAAG,CAAC7O,GACd2kB,EAAcxL,IAAI,GAAK4L,GACzBH,EAAoBI,AADU,OACH,CAAC,AAACC,GAAOA,KAI/BhW,QAAQ6F,OAAO,EACxB,mVC2fgB4Q,SAAS,CAAA,kBAATA,GA1VAC,uBAAuB,CAAA,kBAAvBA,GAvpBAC,kBAAkB,CAAA,kBAAlBA,GA6jCAC,oCAAoC,CAAA,kBAApCA,AAAT,SAASA,EACdI,CAAuB,CACvB9Z,CAA8B,EAY9B,IAAMmc,EAAsBnc,CAAW,CAAC,EAAE,CACpC0a,EAAoBZ,EAAa3a,cAAc,CAC/CsE,EAAoB,IAAIpE,IAAIqb,GAClC,IAAK,IAAI1b,KAAoBmd,EAAqB,CAChD,IAAMO,EACJP,CAAmB,CAACnd,EAAiB,CACjC4d,EAAeF,CAAgB,CAAC,EAAE,CAClCI,EAAkBre,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACme,GACvC1B,EAAqBR,EAAkBte,GAAG,CAAC4C,GACjD,QAA2B9I,IAAvBglB,EAAkC,CACpC,IAAMM,EAAoBN,EAAmB9e,GAAG,CAAC0gB,GACjD,GAAItB,KAAsBtlB,MAAW,CACnC,IAAM0lB,EAAoBlC,EACxB8B,EACAkB,GAEIb,EAAqB,IAAIxc,IAAI6b,GACnCW,EAAmBjsB,GAAG,CAACktB,EAAiBlB,GACxCnY,EAAkB7T,GAAG,CAACoP,EAAkB6c,EAC1C,CACF,CACF,CAUA,IAAMpc,EAAMqa,EAAara,GAAG,CACtB8f,EAAoBD,EAAc7f,IAAuB,YAAfA,EAAIiB,MAAM,CAE1D,MAAO,CACLlB,SAAU,KACVC,MACAlH,KAAMuhB,EAAavhB,IAAI,CAEvBoH,aAAc4f,EAAoBzF,EAAana,YAAY,CAAG,CAAC,KAAM,KAAK,CAC1ED,YAAa6f,EAAoBzF,EAAapa,WAAW,CAAG,KAC5DuB,QAAS6Y,EAAa7Y,OAAO,CAG7B9B,eAAgBsE,EAEhB1D,YAAa+Z,EAAa/Z,WAC5B,AADuC,CAEzC,+EAvsCoC,CAAA,CAAA,IAAA,QACP,CAAA,CAAA,IAAA,QACQ,CAAA,CAAA,IAAA,QAEO,CAAA,CAAA,IAAA,QACP,CAAA,CAAA,IAAA,IAiC/B4Z,EAAyC,CAC7CpjB,MAAO,KACPqjB,KAAM,KACNC,mBAAoB,KACpB1T,SAAU,IACZ,EAiCO,SAASsT,EACd1Z,CAAmB,CACnB+Z,CAAuB,CACvBC,CAAiC,CACjCC,CAAiC,CACjCC,CAAsC,CACtCta,CAA6B,CAC7Bua,CAA8B,CAC9BC,CAA6B,CAC7BC,CAAkD,EAGlD,OAAOC,AAeT,SAASA,EACPta,CAAmB,CACnB+Z,CAAuB,CACvBC,CAAiC,CACjCC,CAAiC,CACjCM,CAA0B,CAC1BL,CAAsC,CACtCta,CAA6B,CAC7Bua,CAA8B,CAC9BC,CAA6B,CAC7B1hB,CAA8B,CAC9B2hB,CAAkD,EAGlD,IAAMG,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAElEK,GAKC1W,CADuC,IAAtBoW,CAAc,CAAC,EAAE,GAGpCM,EAFgB,CAEI,CAAA,AAPA,EAWxB,IAAMI,EAAoBZ,EAAa3a,cAAc,CAa/Cwb,EAAyB,IAAItb,IAAIqb,GAOnCE,EAEA,CAAC,EACDC,EAAe,KAWfC,GAAsB,EAQtBC,EAEA,CAAC,EAEL,IAAK,IAAI/b,KAAoBwb,EAAwB,CACnD,IAyBIiB,EAzBET,EACJR,CAAsB,CAACxb,EAAiB,CACpCic,EACJV,CAAsB,CAACvb,EAAiB,CACpCkc,EAAqBR,EAAkBte,GAAG,CAAC4C,GAC3Cmc,EACqB,OAAzBV,EACIA,CAAoB,CAACzb,EAAiB,CACtC,KAEAoc,EAAkBJ,CAAmB,CAAC,EAAE,CACxCK,EAAsB5iB,EAAY+M,MAAM,CAAC,CAC7CxG,EACAoc,EACD,EACKE,EAAqB7c,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC2c,GAE1CG,OACoBrlB,IAAxB+kB,EAAoCA,CAAmB,CAAC,EAAE,CAAG/kB,OAEzDslB,OACmBtlB,IAAvBglB,EACIA,EAAmB9e,GAAG,CAACkf,QACvBplB,EA+HN,GAAIulB,AAAc,QAhHdA,EAZAL,IAAoB5X,EAAAA,IAYRkY,eAZ2B,CAQrCT,AAAwB/kB,CARe,UA+fxC,AAvfoC,CAwfzCK,MApfgC0kB,CAofzBoC,CACPzD,KAAM,KACNC,mBAAoB,KACpB1T,SAAU,IACZ,EArfkBwV,EACV5b,EACAkb,EACAD,EACAQ,EACAlB,OACsBpkB,IAAtBilB,EAAkCA,EAAoB,KACtDxb,EACAua,EACAmB,EACAjB,GAIJD,GAO+C,AAA/C1qB,GACA,QADOM,IAAI,CAACirB,CAAmB,CAAC,EAAE,CANlC,CAMoChnB,MAAM,CAoB9B2nB,EACV5b,EACAkb,EACAD,EACAQ,EACAlB,OACsBpkB,IAAtBilB,EAAkCA,EAAoB,EAhCrB,GAiCjCxb,EACAua,EACAmB,EACAjB,GAGsBlkB,SAAxB+kB,QACoB/kB,IAApBqlB,GACA3b,CAAAA,EAAAA,EAAAA,YAAY,AAAZA,EAAawb,EAAiBG,IAGNrlB,SAAtBslB,KAFF,GAG0BtlB,IAAxB+kB,EAIYZ,EACVta,EACAyb,CALF,CAMEP,EACAD,EACAV,EACAa,EACAxb,EACAua,EACAC,EACAkB,EACAjB,GAKUuB,EACV5b,EACAkb,EACAD,EACAQ,EACAlB,OACsBpkB,IAAtBilB,EAAkCA,EAAoB,KACtDxb,EACAua,EACAmB,EACAjB,IAmBkB,CAGtB,GAAwB,MAAM,CAA1BqB,EAAUllB,KAAK,CAGjB,OAAOojB,CAGY,MAAM,EAAvBkB,IACFA,EAAe,IAAIxb,GAAAA,EAErBwb,EAAajrB,GAAG,CAACoP,EAAkByc,GACnC,IAAMG,EAAoBH,EAAU7B,IAAI,CACxC,GAA0B,OAAtBgC,EAA4B,CAC9B,IAAMC,EAAsC,IAAIxc,IAAI6b,GACpDW,EAAmBjsB,GAAG,CAAC0rB,EAAoBM,GAC3CjB,EAAuB/qB,GAAG,CAACoP,EAAkB6c,EAC/C,CAKA,IAAMC,EAAiBL,EAAUllB,KAAK,CACtCqkB,CAA0B,CAAC5b,EAAiB,CAAG8c,EAE/C,IAAMC,EAA0BN,EAAU5B,kBAAkB,AAC5B,MAAM,EAAlCkC,GAEFjB,EAAsB,GACtBC,CAA0B,CAAC/b,EAAiB,CAAG+c,GAE/ChB,CAA0B,CAAC/b,EAAiB,CAAG8c,CAEnD,MAEElB,CAFK,AAEqB,CAAC5b,EAAiB,CAAGgc,EAC/CD,CAA0B,CAAC/b,EAAiB,CAAGgc,CAEnD,CAEA,GAAqB,MAAM,CAAvBH,EAEF,OAAO,KAGT,IAAM9Z,EAA+B,CACnCvB,SAAU,KACVC,IAAKqa,EAAara,GAAG,CAOrBC,YAAaoa,EAAapa,WAAW,CACrCnH,KAAMuhB,EAAavhB,IAAI,CACvBoH,aAAcma,EAAana,YAAY,CACvCsB,QAAS6Y,EAAa7Y,OAAO,CAG7B9B,eAAgBwb,cAEhB5a,CACF,EAEA,MAAO,CAELxJ,MAAOylB,EACLhC,EACAY,GAEFhB,KAAM7Y,EACN8Y,mBAAoBiB,EAChBkB,EACEhC,EACAe,GAEF,KACJ5U,SAAU0U,CACZ,CACF,EAjUI9a,EACA+Z,EACAC,EACAC,GACA,EACAC,EACAta,EACAua,EACAC,EAV4C,EAAE,CAY9CC,EAEJ,CAuTA,SAASuB,EACP5b,CAAmB,CACnBga,CA5TEthB,AA4TsC,CACxCuhB,CAAiC,CACjClZ,CAAmC,CACnCwZ,CAA0B,CAC1BL,CAAsC,CACtCgC,CAA4C,CAC5C/B,CAA8B,CAC9BzhB,CAA8B,CAC9B2hB,CAAkD,QAElD,AAAI,CAACE,GAqBDP,MAAmB7jB,OACnB+N,GAAAA,AAtBoB,EAsBpBA,2BAAAA,AAA2B,EAAC8V,EAAgBC,EAAAA,EAGrCL,CAFP,CAKGuC,AAYT,SAASA,EACPnc,CAAmB,CACnBC,CAA8B,CAC9Bc,CAAmC,CACnCmZ,CAAsC,CACtCgC,CAA4C,CAC5C/B,CAA8B,CAC9BzhB,CAA8B,CAC9B2hB,CAAkD,EAQlD,IAQI3a,EACAwB,EACA1I,EACA8jB,EAXEF,EAAsBnc,CAAW,CAAC,EAAE,CACpCoc,EAAgB3sB,AAA4C,WAArCM,IAAI,CAACosB,GAAqBnoB,MAAM,CAW7D,GACE8M,AAAsB5K,YAItB4K,CAHA,CAGkBf,WAAW,CAAGwJ,EAAAA,oBAAoB,CAAGxJ,EAIvDN,EAAMqB,EAAkBrB,GAAG,CAC3BwB,EAAUH,CAJV,CAI4BG,OAAO,CACnC1I,EAAOuI,EAAkBvI,IAAI,CAG7B8jB,CAZoE,CAY7Cvb,EAAkBf,WAAW,MAC/C,GAAqB,MAAM,CAAvBka,EAsCT,OAAOsC,EACLxc,EACAC,EACA,KACAic,EACA/B,EACAzhB,EACA2hB,QAjCF,GARA3a,EAAMwa,CAAY,CAAC,EAAE,CACrBhZ,EAAUgZ,CAAY,CAAC,EAAE,CACzB1hB,EAAO6jB,EAAgBH,EAA8B,KAIrDI,EAAuBtc,EAIrBuc,AAH2BrC,CAAY,CAAC,EAAE,EAKzCC,GAAyBkC,EAI1B,OAAOG,EACLxc,EACAC,EAPF,AAEA,AAMEia,EACAgC,EACA/B,EACAzhB,EACA2hB,GAyBN,IAvC2C,AAuCrCK,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KACjEY,EAAe,IAAIxb,IACnBmd,OACkBtmB,IAAtB4K,EAAkCA,EAAkB3B,YAxCuB,EAwCT,CAAG,KACjEsd,EAAoB,IAAIpd,IAAImd,GAC9BzB,EAEA,CAAC,EACDD,GAAsB,EAC1B,GAAIsB,EAOFhC,EAAyBjrB,IAAI,CAACsJ,MAPb,EASjB,IAAK,IAAIuG,KAAoBmd,EAAqB,CAChD,IAAMO,EACJP,CAAmB,CAACnd,EAAiB,CACjCmc,EACqB,AAAzBV,SACIA,CAAoB,CAACzb,EAAiB,CACtC,KACA2d,EAC0B,OAA9BH,EACIA,EAA0BpgB,GAAG,CAAC4C,QAC9B9I,EACA0mB,EAAeF,CAAgB,CAAC,EAAE,CAClCG,EAAmBpkB,EAAY+M,MAAM,CAAC,CAC1CxG,EACA4d,EACD,EACKE,EAAkBre,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACme,GAOvCnB,EAAYS,EAChBnc,EACA2c,OAN4BxmB,IAA5BymB,EACIA,EAAwBvgB,GAAG,AAM/B2gB,CANgCD,QAC5B5mB,EAMJilB,EACAc,EACA/B,EACA2C,EACAzC,GAEFS,EAAajrB,GAAG,CAACoP,EAAkByc,GACnC,IAAMM,EAA0BN,EAAU5B,kBAAkB,AAC5B,MAAM,EAAlCkC,GAEFjB,GAAsB,EACtBC,CAA0B,CAAC/b,EAAiB,CAAG+c,GAE/ChB,CAA0B,CAAC/b,EAAiB,CAAG0d,EAEjD,IAAMd,EAAoBH,EAAU7B,IAAI,CACxC,GAAIgC,AAAsB,SAAM,CAC9B,IAAMC,EAAsC,IAAIxc,IAChDwc,EAAmBjsB,GAAG,CAACktB,EAAiBlB,GACxCa,EAAkB7sB,GAAG,CAACoP,EAAkB6c,EAC1C,CACF,CAGF,MAAO,CAKLtlB,MAAOyJ,EACP4Z,KAAM,CACJpa,SAAU,SAGVC,EACAC,YAAa,UACbnH,EACAoH,aAAc,aACdsB,EACA9B,eAAgBsd,EAChB1c,YAAasc,CACf,EACAxC,mBAAoBiB,EAChBkB,EAAgChc,EAAa+a,GAC7C,KACJ5U,SAAU0U,CACZ,CACF,EAtMI9a,EACAia,EACAlZ,EACAmZ,EACAgC,EACA/B,EACAzhB,EACA2hB,EAEJ,CA+LA,SAAS4B,EACPgB,CAAkC,CAClCC,CAA8D,EAE9D,IAAMC,EAA2B,CAACF,CAAe,CAAC,EAAE,CAAEC,EAAY,CAalE,OATI,KAAKD,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,EAAA,AAAE,EAE3B,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,EAAA,AAAE,EAE3B,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,EAAA,AAAE,EAExBE,CACT,CAJ4B,AAM5B,SAASX,EACPxc,CAAmB,CACnBC,CAA8B,CAC9Bia,CAAsC,CACtCta,CAA6B,CAC7Bua,CAA8B,CAC9BzhB,CAA8B,CAC9B2hB,CAAkD,EAMlD,IAAMP,EAAqBmC,EACzBhc,EACAA,CAAW,CAAC,EAAE,EAsBhB,OApBA6Z,AAoBOsD,CApBW,CAAC,EAAE,CAAG,UAEF,CACpB5mB,MAAOyJ,EAGP4Z,KAAMwD,AA8MV,SAASA,EACPrd,CAAmB,CACnBC,CAA8B,CAC9Bia,CAAsC,CACtCta,CAA6B,CAC7Bua,CAA8B,CAC9BzhB,CAA8B,CAC9B2hB,CAAkD,EAElD,IAAM+B,EAAsBnc,CAAW,CAAC,EAAE,CACpCya,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAEjE9a,EAAiB,IAAIE,IAC3B,IAAK,IAAIL,KAAoBmd,EAAqB,CAChD,IAAMO,EACJP,CAAmB,CAACnd,EAAiB,CACjCmc,EACJV,AAAyB,SACrBA,CAAoB,CAACzb,EAAiB,CACtC,KAEA4d,EAAeF,CAAgB,CAAC,EAAE,CAClCG,EAAmBpkB,EAAY+M,MAAM,CAAC,CAC1CxG,EACA4d,EACD,EACKE,EAAkBre,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACme,GAEvChB,EAAoBwB,EACxBrd,EACA2c,EACsBxmB,AAAtBilB,WAAkC,KAAOA,EACzCxb,EACAua,EACA2C,EACAzC,GAGIyB,EAAsC,IAAIxc,IAChDwc,EAAmBjsB,GAAG,CAACktB,EAAiBlB,GACxCzc,EAAevP,GAAG,CAACoP,EAAkB6c,EACvC,CAIA,IAAMO,EAAwC,IAAxBjd,EAAe6N,IAAI,CAErCoP,GAOFhC,EAAyBjrB,IAAI,CAACsJ,GAGhC,EAVmB,EAUb4lB,EAAoC,OAAjBpE,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAC7DqE,EAAuBrE,AAAiB,SAAOA,CAAY,CAAC,EAAE,CAAG,KACvE,MAAO,CACLza,SAAU,KACVL,eAAgBA,EAEhBO,YAAa2e,AAAqBnoB,WAAYmoB,EAAmB,KACjE1e,aAAcyc,EAAgBzc,EAAe,CAAC,KAAM,KAAK,CAKzDsB,QAASqd,KAAyBpoB,MAAYooB,EAAuB,KAIrE7e,IAAK8e,IACLhmB,KAAM6jB,EAAiBmC,IAA0C,KAEjExe,aACF,CACF,EA3RMA,EACAC,EACAia,EACAta,EACAua,EACAzhB,EACA2hB,GAIFP,qBACA1T,SAAU,IACZ,CAEF,CA4BO,SAASqT,EACd3Q,CAAuB,CACvByU,CAAmD,EAEnDA,EAAgB1a,IAAI,CAClB,OAAC,YAAE9J,CAAU,CAA6B,CAAA,EACxC,GAA0B,UAAtB,AAAgC,OAAzBA,GAMX,IAAK,IAAMykB,KAAwBzkB,EAAY,CAC7C,GAAM,aACJL,CAAW,CACXJ,KAAMmlB,CAAiB,CACvBllB,SAAUmlB,CAAW,CACrBllB,KAAMmlB,CAAW,CAClB,CAAGH,EAECE,GAOLE,AAqBR,SACEC,AADOD,CACoB,AA7BH,CA8BxBllB,CAA8B,CAC9B+kB,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAYrB,IAAI7U,EAAO+U,EACX,IAAK,IAAIrkB,EAAI,EAAGA,EAAId,EAAYzE,MAAM,CAAEuF,GAAK,EAAG,CAC9C,IAAMyF,EAA2BvG,CAAW,CAACc,EAAE,CACzCZ,EAAmBF,CAAW,CAACc,EAAI,EAAE,CACrCshB,EAAehS,EAAK1C,QAAQ,CAClC,GAAqB,OAAjB0U,EAAuB,CACzB,IAAMY,EAAYZ,EAAaze,GAAG,CAAC4C,GACnC,QAAkB9I,IAAdulB,EAAyB,CAC3B,IAAMoC,EAAcpC,EAAUllB,KAAK,CAAC,EAAE,CACtC,GAAIqJ,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACjH,EAASklB,GAAc,CAEtChV,EAAO4S,EACP,QACF,CACF,CACF,CAKA,MACF,EAEAqC,AAQF,SAASA,EACPjV,CAAuB,CACvB2U,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAErB,GAAgC,MAAM,CAAlC7U,EAAKgR,kBAAkB,CAEzB,OAKF,IAAMgB,EAAehS,EAAK1C,QAAQ,CAC5B4X,EAAWlV,EAAK+Q,IAAI,CAC1B,GAAqB,OAAjBiB,EAAuB,CAIR,MAAM,CAAnBkD,IAgIR,AA/HMC,SA+HGA,EACPnK,CAAoB,CACpB2K,CAA4B,CAC5BC,CAA8B,CAC9BhB,CAA8B,CAC9BC,CAAqB,EAYrB,IAAMgB,EAAoBF,CAAS,CAAC,EAAE,CAChCG,EAAsBF,CAAW,CAAC,EAAE,CACpCG,EAAenB,CAAW,CAAC,EAAE,CAK7Bte,EAAiB0U,EAAU1U,cAAc,CAC/C,IAAK,IAAIH,KAAoB0f,EAAmB,CAC9C,IAAMG,EACJH,CAAiB,CAAC1f,EAAiB,CAC/B8f,EACJH,CAAmB,CAAC3f,EAAiB,CACjC+f,EACJH,CAAY,CAAC5f,EAAiB,CAE1BggB,EAAkB7f,EAAe/C,GAAG,CAAC4C,GACrCigB,EAAmBJ,CAAc,CAAC,EAAE,CACpCK,EAAsBzgB,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACwgB,GAE3CE,OACgBjpB,IAApB8oB,EACIA,EAAgB5iB,GAAG,CAAC8iB,QACpBhpB,OAEiBA,IAAnBipB,OAA8B,EAETjpB,IAArB4oB,GACAlf,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACqf,EAAkBH,CAAgB,CAAC,EAAE,GAClD,MACIC,EAEFf,EACEmB,EACAN,EACAC,EACAC,EACArB,EAPcxnB,CAchBkpB,EAAsBP,EAAgBM,EAAgB,MAdzBJ,AA2BrC,CAIA,IAAMtf,EAAMoU,EAAUpU,GAAG,CACnB4f,CAhC6C,CAgCxB5B,CAAW,CAAC,EAAE,AAC7B,CAjC6C,KAiCvC,EAAdhe,EAGFoU,EAAUpU,GAAG,CAAG4f,EACPC,EAAc7f,IAIvBA,EAJ6B,AAIzBkJ,OAAO,CAAC0W,GASd,IAAM9mB,EAAOsb,EAAUtb,IAAI,CACvB+mB,EAAc/mB,IAChBA,EAAKoQ,CADkB,MACX,CAAC+U,EAEjB,EAnOQK,EACAlV,EAAKtS,KAAK,CACVinB,EACAC,EACAC,GAGF7U,EAAKgR,kBAAkB,CAAG,MAE5B,MACF,CAGA,IAAMoE,EAAiBT,CAAiB,CAAC,EAAE,CACrCU,EAAsBT,CAAW,CAAC,EAAE,CAE1C,IAAK,IAAMze,KAAoBwe,EAAmB,CAChD,IAAMW,EACJF,CAAc,CAACjf,EAAiB,CAC5Bof,EACJF,CAAmB,CAAClf,EAAiB,CAEjCyc,EAAYZ,EAAaze,GAAG,CAAC4C,GACnC,QAAkB9I,IAAdulB,EAAyB,CAC3B,IAAMoC,EAAcpC,EAAUllB,KAAK,CAAC,EAAE,CACtC,GACEqJ,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACue,CAAsB,CAAC,EAAE,CAAEN,IAExCO,MADAA,EAIA,OAAON,EACLrC,EACA0C,EALmBjoB,AAMnBkoB,EACAV,EAGN,CAKF,CAhB2B,AAiB7B,EAxEI7U,EACA2U,CAwDI,CAvDJC,EACAC,EAEJ,EAlEU7U,EACApQ,EACA+kB,EACAC,EACAC,EAEJ,CAKAnE,EAAU1Q,EAAM,MAClB,EACA,AAACpT,IAEC8jB,EAAU1Q,EAAMpT,EAClB,EAEJ,CA4SO,SAAS8jB,EAAU1Q,CAAuB,CAAEpT,CAAU,EAC3D,IAAMoe,EAAYhL,EAAK+Q,IAAI,CAC3B,GAAkB,MAAM,CAApB/F,EAEF,OAGF,IAAMgH,EAAehS,EAAK1C,QAAQ,CAClC,GAAqB,MAAM,CAAvB0U,EAGFuE,EAAsBvW,EAAKtS,KAAK,CAAEsd,EAAWpe,QAK7C,IAAK,IAAMgmB,KAAaZ,EAAatW,MAAM,GAAI,AAC7CgV,EAAUkC,EAAWhmB,GAKzBoT,EAAKgR,kBAAkB,CAAG,IAC5B,CAEA,SAASuF,EACPpf,CAA8B,CAC9B6T,CAAoB,CACpBpe,CAAU,EAMV,IAAM0mB,EAAsBnc,CAAW,CAAC,EAAE,CACpCb,EAAiB0U,EAAU1U,cAAc,CAC/C,IAAK,IAAIH,KAAoBmd,EAAqB,CAChD,IAAMO,EACJP,CAAmB,CAACnd,EAAiB,CACjCggB,EAAkB7f,EAAe/C,GAAG,CAAC4C,GAC3C,QAAwB9I,IAApB8oB,EAGF,KAHiC,IAKnC,IAAMpC,EAAeF,CAAgB,CAAC,EAAE,CAClCI,EAAkBre,GAAAA,EAAAA,oBAAAA,AAAoB,EAACme,GACvCuC,EAAiBH,EAAgB5iB,GAAG,CAAC0gB,QACpB5mB,IAAnBipB,GACFC,EAAsB1C,EAAkByC,AADR,EACwB1pB,EAK5D,CACA,IAAMgK,EAAMoU,EAAUpU,GAAG,CACrB6f,EAAc7f,KACF,CADQ,KACF,CAAhBhK,EAEFgK,EAAIkJ,OAAO,CAAC,MAGZlJ,EAAImJ,MAAM,CAACnT,IAQf,IAAM8C,EAAOsb,EAAUtb,IAAI,CACvB+mB,EAAc/mB,IAChBA,EAAKoQ,CADkB,MACX,CAAC,KAEjB,CAkEA,IAAM6W,EAAWC,SAkCjB,SAASH,EAAcxwB,CAAU,EAC/B,OAAOA,GAASA,EAAM4wB,GAAG,GAAKF,CAChC,CAEA,SAASjB,IAGP,IAFI5V,EACAC,EACE+W,EAAa,IAAI7c,QAAyB,CAAC1O,EAAKwrB,KACpDjX,EAAUvU,EACVwU,EAASgX,CACX,GAmBA,OAlBAD,EAAWjf,MAAM,CAAG,UACpBif,EAAWhX,OAAO,CAAG,AAAC7Z,IACM,WAAW,CAAjC6wB,EAAWjf,MAAM,GACwBif,AAC3CE,EAAanf,MAAM,CAAG,YACtBmf,EAAa/wB,KAAK,CAAGA,EACrB6Z,EAAQ7Z,GAEZ,EACA6wB,EAAW/W,MAAM,CAAG,AAACnT,IACO,WAAW,CAAjCkqB,EAAWjf,MAAM,GAEnBof,EAAYpf,MAAM,CAAG,WADoBif,AAEzCG,EAAYvJ,MAAM,CAAG9gB,EACrBmT,EAAOnT,GAEX,EACAkqB,EAAWD,GAAG,CAAGF,EACVG,CACT,mWC9wCgBI,mCAAAA,qCAAAA,AAAT,SAASA,EACdlhB,CAAmB,CACnBC,CAAwB,CACxBjG,CAAoC,EAEpC,IAAMkG,EAAclG,EAAkB7E,MAAM,EAAI,EAE1C,CAACgL,EAAkBrG,EAAQ,CAAGE,EAC9BoG,EAAWR,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC9F,GAEhCuG,EACJJ,EAAcK,cAAc,CAAC/C,GAAG,CAAC4C,GAE/BI,EAAkBP,EAASM,cAAc,CAAC/C,GAAG,CAAC4C,GAE7CI,GAAmBA,IAAoBF,IAC1CE,EAAkB,IAAIC,IAAIH,GAC1BL,EAASM,MAF0D,QAE5C,CAACvP,GAAG,CAACoP,EAAkBI,IAGhD,IAAME,EAAyBJ,MAAAA,EAAAA,KAAAA,EAAAA,EAAyB9C,GAAG,CAAC6C,GACxDM,EAAiBH,EAAgBhD,GAAG,CAAC6C,GAGzC,GAAIF,EAAa,CAEb,AAACQ,GACAA,EAAeC,QAAQ,EACxBD,GADA,CACmBD,GAEnBF,EAAgBxP,GAAG,CAACqP,EAAU,CAC5BO,SAAU,GAFZ,EAGEC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IACpB4B,QAAS,KACTlB,YAAa,CAAC,CAChB,GAEF,MACF,CAEA,GAAI,CAACR,GAAkB,CAACD,EAAwB,CAE1C,AAACC,GACHH,EAAgBxP,GAAG,CAACqP,EAAU,CAC5BO,IAFiB,KAEP,KACVC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IACpB4B,QAAS,KACTlB,YAAa,CAAC,CAChB,GAEF,MACF,CAeA,OAbIR,IAAmBD,IACrBC,EAAiB,CACfC,SAAUD,EAAeC,MAFkB,EAEV,CACjCC,IAAKF,EAAeE,GAAG,CACvBC,YAAaH,EAAeG,WAAW,CACvCnH,KAAMgH,EAAehH,IAAI,CACzBoH,aAAcJ,EAAeI,YAAY,CACzCR,eAAgB,IAAIE,IAAIE,EAAeJ,cAAc,EACrD8B,QAAS1B,EAAe0B,OAAO,AACjC,EACA7B,EAAgBxP,GAAG,CAACqP,EAAUM,IAGzBwgB,EACLxgB,EACAD,EACArH,CAAAA,EAAAA,EAAAA,wBAAAA,AAAwB,EAACY,GAE7B,aArFyC,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,oVCgOrBmnB,6BAA6B,CAAA,kBAA7BA,GA1MAC,0BAA0B,CAAA,kBAA1BA,+EAjBT,CAAA,CAAA,IAAA,QAE8B,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,QACV,CAAA,CAAA,IAAA,MACG,CAAA,CAAA,IAAA,QACqB,CAAA,CAAA,IAAA,QAC5B,CAAA,CAAA,IAAA,IAUvB,SAASA,EACdlgB,CAAmB,CACnBoC,CAA2B,CAC3BrJ,CAA2C,CAC3CzH,CAAQ,CACRkW,CAAgB,EAEhB,IAGI4Y,EAHAjc,EAAc/B,EAAM9J,IAAI,CACxB6nB,EAAe/d,EAAMR,KAAK,CACxB9N,EAAO0K,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAClN,GAG/B,GAA0B,UAAtB,AAAgC,OAAzByH,EACT,OAAO,EAGT,IAAK,IAAMykB,KAAwBzkB,EAAY,CAE7C,GAAI,CAACsnB,AAwFT,SAASA,EAA8B9nB,CAAkC,EACvE,GAAI,CAACA,EAAU,OAAO,EAEtB,IAAM6G,EAAiB7G,CAAQ,CAAC,EAAE,CAGlC,GAFgBA,CAAQ,AAEpB2I,CAFqB,EAAE,CAGzB,KADW,CACJ,GAGT,IAAK,IAAMpS,KAAOsQ,EAChB,GAAIihB,EAA8BjhB,CAAc,CAACtQ,EAAI,EACnD,CADsD,CADxB,KAEvB,EAIX,OAAO,CACT,EAzGuC0uB,EAAqBjlB,QAAQ,EAC9D,CADiE,QAInE,IAAIkJ,EAAY+b,EAAqBllB,IAAI,CAIzCmJ,EAAYwe,EACVxe,EACA/R,OAAO4wB,WAAW,CAAChvB,EAAI1C,YAAY,GAGrC,GAAM,UAAE2J,CAAQ,cAAEM,CAAY,eAAEF,CAAa,CAAE,CAAG6kB,EAE5C+C,EAAoC,CAAC,MAAO5nB,EAAc,CAKhE8I,EAAYwe,EACVxe,EACA/R,OAAO4wB,WAAW,CAAChvB,EAAI1C,YAAY,GAGrC,IAAI4xB,EAAUvd,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EACvCsd,EACApc,EACA1C,EACA3N,GAGIgL,EAAWqV,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,IAIrC,GAAItb,GAAgBN,EAAU,CAE5B,IAAMmH,EAAMnH,CAAQ,CAAC,EAAE,CAEvBuG,EAASoC,OAAO,CADA3I,CAAQ,CAAC,AACN2I,EADQ,CAE3BpC,EAASY,GAAG,CAAGA,EAkErB,AA/DM+gB,SA+DGA,EACPzgB,CAAmB,CACnBlB,CAAmB,CACnBC,CAAwB,CACxBkB,CAA8B,CAC9BC,CAA2C,EAG3C,GAD6D,CACzDE,GADkB1Q,OAAOM,IAAI,CAACiQ,AACf,CAD0B,CAAC,EAAE,EAAEhM,MAAM,CAKxD,IAAK,IAAMnF,KAAOmR,CAAW,CAAC,EAAE,CAAE,CAChC,IASIe,EATEX,EAAqBJ,CAAW,CAAC,EAAE,CAACnR,EAAI,CACxCwR,EAA0BD,CAAkB,CAAC,EAAE,CAC/CnB,EAAWR,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC4B,GAEhCC,EACkB,OAAtBL,QAA4D/J,IAA9B+J,CAAiB,CAAC,EAAE,CAACpR,EAAI,CACnDoR,CAAiB,CAAC,EAAE,CAACpR,EAAI,CACzB,KAGN,GAAyB,OAArByR,EAA2B,CAE7B,IAAMb,EAAMa,CAAgB,CAAC,EAAE,CACzBW,EAAUX,CAAgB,CAAC,EAAE,CACnCS,EAAe,CACbvB,SAAU,KAEVC,IAAKY,EAAwB/D,QAAQ,CAACqC,EAAAA,gBAAgB,EAAI,KAAOc,EACjEC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,YACpB4B,cACAlB,CACF,CACF,MAGEgB,CAHK,CAGU,CACbvB,SAAU,KACVC,IAAK,KACLC,YAAa,KACbnH,KAAM,KACNoH,aAAc,KACdR,eAAgB,IAAIE,IACpB4B,QAAS,KACTlB,YAAa,CAAC,CAChB,EAGF,IAAMmB,EAAyBrC,EAASM,cAAc,CAAC/C,GAAG,CAACvN,GACvDqS,EACFA,EAAuBtR,GAAG,CAACqP,EAAU8B,GAErClC,EAASM,SAHiB,KAGH,CAACvP,GAAG,CAACf,EAAK,IAAIwQ,IAAI,CAAC,CAACJ,EAAU8B,EAAa,CAAC,GAGrEyf,EACEzgB,EACAgB,EACAjC,EACAsB,EACAE,EAEJ,CACF,EAlIQP,EACAlB,EACAqhB,EACA1e,EACAlJ,EAEJ,MAEEuG,CAFK,CAEIY,GAAG,CAAGygB,EAAazgB,GAAG,CAC/BZ,EAASa,WAAW,CAAGwgB,EAAaxgB,WAAW,CAC/Cb,EAASoC,OAAO,CAAGif,EAAajf,OAAO,CACvCpC,EAASM,cAAc,CAAG,IAAIE,IAAI6gB,EAAa/gB,cAAc,EAG7DkC,CAAAA,EAAAA,EAAAA,yCAAyC,AAAzCA,EACEtB,EACAlB,EACAqhB,EACA3C,GAMAgD,IACFrc,EAAcqc,EACdL,CAFW,CAEIrhB,EACfshB,GAAU,EAEd,OAEA,CAAI,CAACA,IAIL5Y,EAAQE,GAJM,QAIK,CAAGvD,EACtBqD,EAAQ5F,KAAK,CAAGue,EAChB3Y,EAAQzM,YAAY,CAAGjH,EACvB0T,EAAQW,YAAY,CAAG7W,EAAIX,IAAI,CAExB2W,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,GAC9B,CAkGO,SAASyY,EACdxkB,CAAoC,CACpC7M,CAA2D,EAE3D,GAAM,CAACgK,EAASwG,EAAgB,GAAGshB,EAAK,CAAGjlB,EAG3C,GAAI7C,EAAQ2D,QAAQ,CAACqC,EAAAA,gBAAgB,EAEnC,CAFsC,KAE/B,CADYgiB,CAAAA,EAAAA,EAAAA,4BAA4B,AAA5BA,EAA6BhoB,EAAShK,GACrCwQ,KAAmBshB,EAAK,CAI9C,IAAMG,EAA8D,CAAC,EAErE,IAAK,GAAM,CAAC/xB,EAAKoY,EAAc,GAAIxX,OAAOV,OAAO,CAACoQ,GAChDyhB,CAAqB,CAAC/xB,EAAI,CAAGmxB,EAC3B/Y,EACAtY,GAIJ,CAPmE,KAO5D,CAACgK,EAASioB,KAA0BH,EAAK,AAClD,6QCzOC,kEAkFiBI,EAUAC,KAVAD,WAUAC,CAVAD,OAAmB,CAAA,kBAAnBA,GAUAC,gBAAgB,CAAA,kBAAhBA,GAxCLC,kBAAkB,CAAA,kBAAlBA,GAkBAC,cAAc,CAAA,kBAAdA,GApCAC,sBAAsB,CAAA,kBAAtBA,GAhBAC,QAAQ,CAAA,kBAARA,GAPAC,QAAQ,CAAA,kBAARA,GAkDAC,sBAAsB,CAAA,kBAAtBA,GApCAC,qBAAqB,CAAA,kBAArBA,GAkBAC,oBAAoB,CAAA,kBAApBA,uEAtCb,IAAMC,EAAkB,KACtB,MAAM,OAAA,cAEL,CAFK,AAAIrsB,MACR,sEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,EAEaisB,EAKPI,EAEOL,EAKPK,EAEOF,EAOPE,CApBJjwB,CAsBW2vB,EAOPM,CAtBJjwB,CAwBWgwB,EAOPC,CAtCIhwB,CAwCGwvB,EAxCA,AA+CPQ,CAxCIhwB,AAPI+a,CAiDD8U,EA1CA,AAiDPG,CAjDQjV,CAmDD0U,EAKPO,EAOC,AAxDLjwB,IAwDgBuvB,CA/ChBvvB,EASAA,CAlBQC,CA2BRD,EA3BW,CAACgb,CA6CZhb,AApCQC,EASAA,CATG,CAkBHA,AASRD,AA3BYgb,CASD,AAhC4B,CAgC3BA,CASD,CAACA,AAkBJ/a,AA1DJ,CAMmC,EAoD5B,CATHA,AASI+a,AAnDR,CA8DYuU,EApBL,CAACvU,QAoBIuU,EAxDuB,CAwDvBA,EAvDZ,EAQmC,EASA,CARnC,CAiBmC,CARnC,EASA,CAiBmC,GACnC,CAVmC,GACnC,mCAmBYA,GAUAC,GAKf,MAAA,CAAA,EAAA,MAAA,CALeA,AAKf,EAAA,CAAA,IAAA,IALeA,CASf,CAAA,CATeA,AASf,EAAA,OAAA,CAAA,EAAA,CAAA,GAAA,OAKA,CAAA,CAAA,EAAA,UAAA,CAAA,EAAA,CAAA,aAdeA,oVCzEFU,iBAAiB,CAAA,kBAAjBA,GAsIAC,eAAe,CAAA,kBAAfA,AAAT,SAASA,EACdtf,CAA2B,CAC3BsK,CAAsB,EAEtB,GAAM,KAAEpb,CAAG,eAAEoxB,CAAa,cAAEC,CAAY,CAAElb,cAAY,eAAE2C,CAAa,CAAE,CACrEsC,EACIlF,EAAmB,CAAC,EACpB,MAAE7W,CAAI,CAAE,CAAGW,EACXwC,EAAO0K,GAAAA,EAAAA,iBAAAA,AAAiB,EAAClN,GACzBsW,EAA+B,SAAjB+a,EAOpB,GALA/Y,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACxH,EAAMkG,aAAa,EAEtCd,EAAQM,0BAA0B,EAAG,EACrCN,EAAQI,WAAW,CAAGA,EAElB8a,EACF,OAAOjB,EAAkBrf,EAAOoF,EADf,AACwBlW,EAAIqI,QAAQ,GAAIiO,GAK3D,GAAIkJ,SAAS8R,cAAc,CAAC,wBAC1B,CADmD,MAC5CnB,EAAkBrf,EAAOoF,EAAS1T,EAAM8T,GAsBjD,IAAMib,EAAiBlZ,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,EAAC,KACnDrY,EACAoK,QAAS0G,EAAM1G,OAAO,CACtBpD,KAAM8J,EAAM9J,IAAI,CAChBgQ,cAAelG,EAAMkG,aAAa,eAClC8B,CACF,GACM,sBAAEqB,CAAoB,MAAER,CAAI,CAAE,CAAG4X,EAIvC,OAFA9W,EAAAA,aAAa,CAAC/C,IAAI,CAACiC,GAEZA,EAAKpI,IAAI,CACd,OAAC,YAAE9J,CAAU,CAAEgC,aAAc+nB,CAAoB,CAAE5nB,WAAS,CAAE,CAAA,EACtD8E,EAAc2L,KAAKC,GAAG,GAExBmX,GAAc,EAQlB,GANKF,EAAehX,YAAY,EAAE,CAEhCgX,EAAehX,YAAY,CAAG7L,EAC9B+iB,GAAc,GAGZF,EAAelY,OAAO,CAAE,CAC1B,IAAMlX,EAASysB,GAAAA,EAAAA,0BAAAA,AAA0B,EACvClgB,EACAoC,EACArJ,EACAzH,EACAkW,SAMF,CAAe,IAAX/T,EACKiuB,CADa,CACGtf,EAAO,CAAE,GAAGsK,CAAM,CAAEtC,eAAe,CAAM,GAG3D3W,CACT,CAGA,GAA0B,UAAtB,AAAgC,OAAzBsF,EACT,OAAO0oB,EAAkBrf,EAAOoF,EAASzO,EAAY6O,GAGvD,IAAMob,EAAsBF,EACxBtkB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACskB,GAClBhvB,EASJ,GANE,AAAEnD,CAAD,AAMCuX,EALF9F,EAAMrH,YAAY,AAKA,CALCtG,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,GACjCuuB,EAAoBvuB,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAUtC,OALA+S,EAAQU,cAAc,EAAG,EACzBV,EAAQzM,YAAY,CAAGioB,EACvBxb,EAAQC,YAAY,CAAGA,EACvBD,EAAQW,YAAY,CAAGxX,EACvB6W,EAAQS,kBAAkB,CAAG,EAAE,CACxBX,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,GAG9B,IAAIrD,EAAc/B,EAAM9J,IAAI,CACxB6nB,EAAe/d,EAAMR,KAAK,CAC1BqG,EAA0C,EAAE,CAChD,IAAK,IAAMuV,KAAwBzkB,EAAY,CAC7C,GAAM,CACJJ,cAAeG,CAAiB,UAChCP,CAAQ,MACRC,CAAI,eACJC,CAAa,CACbI,cAAY,CACb,CAAG2kB,EACA/b,EAAY+b,EAAqBllB,IAAI,CAGnCioB,EAAoC,CAAC,MAAOznB,EAAkB,CAGhE0nB,EAAUvd,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAEvCsd,EACApc,EACA1C,EACA3N,GAeF,GAVgB,MAAM,CAAlB0sB,GAToB,CAUtBA,EAAUvd,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAEnCsd,EACA9U,EACAhK,EACA3N,EAAAA,EAIY,OAAZ0sB,EAAkB,CACpB,EATwB,CAiBtBjoB,CANA,AADA,EAQAM,GACAqC,EACA,CACA,IAAM4N,EAAO4Q,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAC7B1Z,EACAmgB,EACAhc,EACA1C,EACAlJ,EACAC,EACAC,GACA,EAlBsD,AAmBtDwP,GAGF,GAAa,KAvBsD,EAuB/Da,EAAe,CACjB,GAAmB,MAAM,CAArBA,EAAKtS,KAAK,CAGZ,OAAOirB,EAAkBrf,EAAOoF,EAAS1T,EAAM8T,GAOjD4Y,EAD8C1X,EAAKtS,KAAK,CAC9CysB,AAEV,IAAMnkB,EAAWgK,EAAK+Q,IAAI,AACT,MAAM,EAAnB/a,IAGF0I,EAAQ5F,KAAK,CAAG9C,CAAAA,EAElB,IAAMgb,EAAqBhR,EAAKgR,kBAAkB,CAClD,GAA2B,OAAvBA,EAA6B,CAc/B,IAAMoJ,EAAiB5oB,CAAAA,EAAAA,EAAAA,mBAAmB,AAAnBA,EAAoBhJ,EAAK,CAC9CmK,kBAAmBqe,EACnBpe,QAAS0G,EAAM1G,OAAO,AACxB,GAEA+d,CAAAA,EAAAA,EAAAA,uBAAuB,AAAvBA,EAAwB3Q,EAAMoa,EAKhC,CAIF,MAJS,AASP1C,CALK,CAKK/e,CAEd,KAAO,CASL,GAAIyC,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACC,EAAaqc,GAC3C,OADqD,AAC9CiB,EAAkBrf,EAAOoF,EAAS1T,EAAM8T,GAGjD,IAAMhG,EAAmBuS,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,IACzCiM,GAAU,EAwDd,IAAK,IAAMgD,KArDTP,EAAeliB,MAAM,GAAKC,EAAAA,wBAAwB,CAACyL,KAAK,EACvD0W,EAAD,AAkBA3C,EAAUze,CAAAA,EAAAA,EAAAA,IAjBV,WAiBUA,AAAe,EACvB3B,EACAmgB,EACAve,EACA4b,EACAqF,IAfFzC,EA7Vd,AA6VwB0B,SA5VtBhjB,AADOgjB,CACY,CACnB3B,CAAuB,CACvBrnB,CAAoC,CACpC2I,CAA4B,EAE5B,IAAIsgB,EAAe,GAWnB,IAAK,IAAM1Z,KATXvJ,EAASY,GAAG,CAAGygB,EAAazgB,GASDsiB,AATI,CAC/BljB,EAASa,WAAW,CAAGwgB,EAAaxgB,EAQW,SARA,CAC/Cb,EAASoC,OAAO,CAAGif,EAAajf,OAAO,CACvCpC,EAASM,cAAc,CAAG,IAAIE,IAAI6gB,EAAa/gB,cAAc,EAElCuiB,EAA0BlgB,GAAWzI,GAAG,CAChEJ,AAAD,GAAa,IAAIE,KAAsBF,EAAQ,GAI/ConB,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAAClhB,EAAUqhB,EAAc9X,GAEzD0Z,GAAe,EAGjB,OAAOA,CACT,EAsUgBngB,EACAue,EACArnB,EACA2I,GAIFohB,EAAehX,YAAY,CAAG7L,GAWXiE,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAErCsc,EACApc,IAKAvC,EAAMlC,GAAG,CAAGygB,EAAazgB,GAAG,CAC5BkC,EAAMjC,EARgB,SAQL,CAAGwgB,EAAaxgB,WAAW,CAE5Cd,CAAAA,EAAAA,EAAAA,qCAAAA,AAAqC,EACnC+C,EACAue,EACArnB,GAGF0O,EAAQ5F,KAAK,CAAGA,GACPwe,IACT5Y,EAAQ5F,GADU,EACL,CAAGA,EAGhBue,EAAeve,GAGQ+f,EAA0BlgB,IAAY,CAC7D,IAAM4hB,EAAwB,IACzBvqB,KACAsqB,EACJ,CAGCC,CAAqB,CAACA,EAAsBpvB,MAAM,CAAG,EAAE,GACvDwP,EAAAA,mBAAmB,EACnB,AACAwE,EAAmB7Y,IAAI,CAACi0B,EAE5B,CACF,CAEAlf,EAAcqc,CAChB,CACF,CAQA,OANAhZ,EAAQE,WAAW,CAAGvD,EACtBqD,EAAQzM,YAAY,CAAGioB,EACvBxb,EAAQS,kBAAkB,CAAGA,EAC7BT,EAAQW,YAAY,CAAGxX,EACvB6W,EAAQC,YAAY,CAAGA,EAEhBH,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,EAC9B,EACA,IAAMpF,EAEV,+EA7eoC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACoB,CAAA,CAAA,IAAA,QACV,CAAA,CAAA,IAAA,QACT,CAAA,CAAA,IAAA,QACS,CAAA,CAAA,IAAA,QAOrC,CAAA,CAAA,IAAA,QACuB,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,QAIrD,CAAA,CAAA,IAAA,QAC0C,CAAA,CAAA,IAAA,QACN,CAAA,CAAA,IAAA,GAOpC,SAASqf,EACdrf,CAA2B,CAC3BoF,CAAgB,CAChBlW,CAAW,CACXsW,CAAoB,EAOpB,OALAJ,EAAQK,aAAa,CAAG,GACxBL,EAAQzM,YAAY,CAAGzJ,EACvBkW,EAAQI,WAAW,CAAGA,EACtBJ,EAAQS,kBAAkB,MAAG9R,EAEtBmR,CAAAA,EAAAA,EAAAA,aAAa,AAAbA,EAAclF,EAAOoF,EAC9B,CAEA,SAASma,EACPC,CAAoC,EAEpC,IAAM7c,EAAgC,EAAE,CAClC,CAACnM,EAASwG,EAAe,CAAGwiB,EAElC,GAA2C,GAAG,CAA1ClyB,OAAOM,IAAI,CAACoP,GAAgBnL,MAAM,CACpC,MAAO,CAAC,CAAC2E,EAAQ,CAAC,CAGpB,IAAK,GAAM,CAACqG,EAAkBiI,EAAc,GAAIxX,OAAOV,OAAO,CAC5DoQ,GAEA,IAAK,IAAMyiB,KAAgBF,AAD1B,EACoDza,GAEnC,IAAI,CAAhBtO,EACFmM,EAAS3V,GAHwD,CAGpD,CAAC,CAAC6P,KAAqB4iB,EAAa,EAEjD9c,EAAS3V,IAAI,CAAC,CAACwJ,EAASqG,KAAqB4iB,EAAa,EAKhE,OAAO9c,CACT,GAxCO,CAAA,CAAA,IAAA,sWCnBSue,qBAAAA,qCAAAA,aAfkB,CAAA,CAAA,IAAA,MACU,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QAOV,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,QAEO,CAAA,CAAA,IAAA,IAE9B,SAASA,EACdlhB,CAA2B,CAC3BsK,CAAyB,EAEzB,GAAM,CACJ6W,eAAgB,YAAExqB,CAAU,CAAEgC,aAAc+nB,CAAoB,CAAE,aAClE9iB,CAAW,CACZ,CAAG0M,EAEElF,EAAmB,CAAC,EAK1B,GAHAA,EAAQM,0BAA0B,EAAG,EAGX,UAAtB,AAAgC,OAAzB/O,EACT,MAAO0oB,GAAAA,EAAAA,iBAAiB,AAAjBA,EACLrf,EACAoF,EACAzO,EACAqJ,EAAMuF,OAAO,CAACC,WAAW,EAI7B,IAAIzD,EAAc/B,EAAM9J,IAAI,CACxB6nB,EAAe/d,EAAMR,KAAK,CAE9B,IAAK,IAAM4b,KAAwBzkB,EAAY,CAC7C,GAAM,CAAEL,YAAaI,CAAiB,CAAER,KAAMmJ,CAAS,CAAE,CACvD+b,EAEIgD,EAAUvd,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAEzC,CAAC,MAAOnK,EAAkB,CAC1BqL,EACA1C,EACAW,EAAMrH,MAJgB,MAIJ,EAQpB,GAAgB,MAAM,CAAlBylB,EACF,OAAOpe,EAGT,GAAI8B,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACC,EAAaqc,GAC3C,MAAOiB,CAD8C,AAC9CA,EAAAA,EAAAA,iBAAAA,AAAiB,EACtBrf,EACAoF,EACApF,EAAMrH,YAAY,CAClBqH,EAAMuF,OAAO,CAACC,WAAW,EAI7B,IAAM4b,EAA2BV,EAC7BtkB,CAAAA,EAAAA,EAAAA,iBAAiB,AAAjBA,EAAkBskB,QAClB3sB,EAEAqtB,IACFhc,EAAQzM,YAAY,CAAGyoB,CAAAA,EAGzB,IAAM5hB,AAJwB,EAILuS,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,IAC7CxS,GAAAA,EAAAA,eAAAA,AAAe,EAAC3B,EAAamgB,EAAcve,EAAO4b,GAElDhW,EAAQE,WAAW,CAAG8Y,EACtBhZ,EAAQ5F,KAAK,CAAGA,EAEhBue,EAAeve,EACfuC,EAAcqc,CAChB,CAEA,MAAOlZ,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,EAC9B,mWCjFgBic,iBAAAA,qCAAAA,aATkB,CAAA,CAAA,IAAA,MAMe,CAAA,CAAA,IAAA,IAG1C,SAASA,EACdrhB,CAA2B,CAC3BsK,CAAqB,MAmCV9G,EAjCX,GAAM,KAAEtU,CAAG,MAAEgH,CAAI,CAAE,CAAGoU,EAChB5Y,EAAO0K,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAClN,GAOzBoyB,EAAgBprB,GAAQ8J,EAAM9J,IAAI,CAElCqrB,EAAWvhB,EAAMR,KAAK,CAS5B,MAAO,CAEL7G,aAAcjH,EACd6T,QAAS,CACPC,aAAa,EACbC,eAAe,EAEfC,4BAA4B,CAC9B,EACAC,kBAAmB3F,EAAM2F,iBAAiB,CAC1CnG,MAZE+hB,CAYK7kB,CACPwJ,cAAelG,CAjBwD,CAiBlDkG,aAAa,CAElChQ,KAAMorB,EACNhoB,QAASkK,AAAiC8d,OAAjC9d,EAAAA,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAAC8d,EAAAA,CAAAA,CAAjC9d,EAAmDtU,EAAIZ,QAAQ,AAC1E,CACF,GAzCqD,CAAA,CAAA,IAAA,sWCIrCmzB,wBAAAA,qCAAAA,aAVkB,CAAA,CAAA,IAAA,IAU3B,SAASA,EACdzhB,CAA2B,CAC3BsK,CAAsB,CACtBjL,CAA4B,EAY5B,MAAOggB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACrf,EAAO,CAAC,EAAGA,EAAMrH,YAAY,EAAE,EAC1D,mWCxBgB+oB,oCAAAA,qCAAT,AAASA,SAAAA,EAAkC,CAG9B,EAH8B,GAAA,CAChDlrB,EACAwG,EACkB,CAH8B,EAKhD,GAAIlQ,MAAMC,OAAO,CAACyJ,KAA4B,OAAfA,CAAO,CAAC,EAAE,EAA4B,OAAfA,CAAO,CAAC,EAAE,AAAK,CAAG,EAKpE,AAAmB,CALqD,gBAKjEA,GAAwBuM,CAAAA,EAAAA,EAAAA,0BAAAA,AAA0B,EAACvM,GAJ5D,OAIsE,AAJ/D,EAST,GAAIwG,GACF,IAAK,IAAMtQ,KADO,AACAsQ,EAChB,GAAI0kB,EAAkC1kB,CAAc,CAACtQ,EAAI,EACvD,CAD0D,CAD5B,IAEvB,EAEX,CAGF,OAAO,CACT,aA1B2C,CAAA,CAAA,IAAA,sWCmB3Bi1B,iBAAAA,qCAAAA,aApBoB,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACU,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QAOV,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QAEgB,CAAA,CAAA,IAAA,QACT,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACY,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,IAGzC,SAASA,EACd3hB,CAA2B,CAC3BsK,CAAqB,EAErB,GAAM,QAAE3Y,CAAM,CAAE,CAAG2Y,EACblF,EAAmB,CAAC,EACpB1T,EAAOsO,EAAMrH,YAAY,CAE3BoJ,EAAc/B,EAAM9J,IAAI,CAE5BkP,EAAQM,0BAA0B,EAAG,EAErC,IAAMlG,EAAmBuS,CAAAA,EAAAA,EAAAA,oBAAoB,AAApBA,IAInB7R,EAAiBwhB,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EAAC1hB,EAAM9J,IAAI,EAInEsJ,EAAMnC,QAAQ,CAAGnF,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC,IAAIM,IAAI9G,EAAMC,GAAS,CAC1D0H,kBAAmB,CACjB0I,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACd,UACD,CACDzI,QAAS4G,EAAiBF,EAAM1G,OAAO,CAAG,IAC5C,GAEA,IAAMsE,EAAc2L,KAAKC,GAAG,GAC5B,OAAOhK,EAAMnC,QAAQ,CAACoD,IAAI,CACxB,MAAA,OAAO,YAAE9J,CAAU,CAAEgC,aAAc+nB,CAAoB,CAAE,CAAA,EAEvD,GAA0B,UAAtB,AAAgC,OAAzB/pB,EACT,MAAO0oB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACtBrf,EACAoF,EACAzO,EACAqJ,EAAMuF,OAAO,CAACC,WAAW,EAO7B,IAAK,IAAM4V,KAFX5b,EAAMnC,QAAQ,CAAG,KAEkB1G,GAAY,CAC7C,GAAM,CACJT,KAAMmJ,CAAS,CACflJ,SAAU2H,CAAiB,MAC3B1H,CAAI,cACJK,CAAY,CACb,CAAG2kB,EAEJ,GAAI,CAAC3kB,EAGH,OADAyE,KAFiB,GAET0mB,GAAG,CAAC,kBACL5hB,EAGT,IAAMoe,EAAUvd,GAAAA,EAAAA,2BAAAA,AAA2B,EAEzC,CAAC,GAAG,CACJkB,EACA1C,EACAW,EAAMrH,WAJgB,CAIJ,EAGpB,GAAgB,MAAM,CAAlBylB,EACF,MAAOqD,CAAAA,EAAAA,EAAAA,qBAAAA,AAAqB,EAACzhB,EAAOsK,EAAQjL,GAG9C,GAAIyC,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACC,EAAaqc,GAC3C,MAAOiB,CAD8C,AAC9CA,EAAAA,EAAAA,iBAAAA,AAAiB,EACtBrf,EACAoF,EACA1T,EACAsO,EAAMuF,OAAO,CAACC,WAAW,EAI7B,IAAM4b,EAA2BV,EAC7BtkB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACskB,GAClB3sB,OAOJ,GALI2sB,IACFtb,EAAQzM,YAAY,CAAGyoB,CAAAA,EADC,AAKA,OAAtBtjB,EAA4B,CAC9B,IAAMR,EAAMQ,CAAiB,CAAC,EAAE,CAC1BgB,EAAUhB,CAAiB,CAAC,EAAE,AACpC0B,GAAMlC,GAAG,CAAGA,EACZkC,EAAMjC,WAAW,CAAG,KACpBiC,EAAMV,OAAO,CAAGA,EAChBnB,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,EAC3BC,EACA4B,EAEAzL,OACAsL,EACAvB,EACA1H,OACArC,GAKAqR,EAAQc,aAAa,CAAG,IAAIhJ,GAEhC,CAEA,MAAMwC,CAAAA,EAAAA,EAAAA,OAd0F,wBAc1FA,AAA+B,EAAC,aACpC9B,QACAoC,EACAD,YAAaqe,EACbne,aAAcT,iBACdU,EACAvH,aAAcyM,EAAQzM,YAAY,EAAIqH,EAAMrH,YAAY,AAC1D,GAEAyM,EAAQ5F,KAAK,CAAGA,EAChB4F,EAAQE,WAAW,CAAG8Y,EAEtBrc,EAAcqc,CAChB,CAEA,MAAOlZ,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,EAC9B,EACA,IAAMpF,EAEV,GAtIsC,CAAA,CAAA,IAAA,sWC6GzB6hB,oBAAAA,qCAAAA,OA/HuB,CAAA,CAAA,IAAA,MACF,CAAA,CAAA,IAAA,IACU,CAAA,CAAA,IAAA,MACA,CAAA,CAAA,IAAA,MAOV,CAAA,CAAA,IAAA,MACJ,CAAA,CAAA,IAAA,MACE,CAAA,CAAA,IAAA,MAEK,CAAA,CAAA,IAAA,MACC,CAAA,CAAA,IAAA,MACY,CAAA,CAAA,IAAA,IA+G3C,IAAMA,EAPb,SAASE,AACP/hB,CAA2B,CAC3BgiB,CAAyB,EAEzB,IAIA7yB,GAJO6Q,CACT,IAGU5Q,GAAG,CAACC,QAAQ,KAAK,cACrB0yB,wBACAD,ySCzHUG,iBAAAA,qCAAAA,aATY,CAAA,CAAA,IAAA,IASrB,SAASA,EAAexwB,CAAgB,CAAEvC,CAAQ,EACvD,GAAIuC,EAASyC,UAAU,CAAC,KAAM,CAC5B,IAAMguB,EAAUhzB,EAAIyC,MAAM,CAAGzC,EAAIZ,QAAQ,CACzC,OAAO,IAAIkK,IACT,AAGA,AAFA,AAEC0pB,CAAAA,EAAQnzB,QAAQ,CAAC,KAAOmzB,EAAUA,EAAU,GAAA,CAAE,CAAKzwB,EAExD,CAEA,OAAO,IAAI+G,IAAIhE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC/C,GAAWvC,EAAIwC,IAAI,CAChD,qBAPmG,kBADkB,0SCarGywB,gCAAgC,CAAA,kBAAhCA,GAiCAC,cAAc,CAAA,kBAAdA,uEAjCT,SAASD,EACd5U,CAAU,EAEV,IAAM8U,EAAWhoB,SAASkT,EAAGhb,KAAK,CAAC,EAAG,GAAI,IAEpCgwB,EAAWF,GAAY,EAAK,GAE5BI,EAAW31B,MAAM,GAEvB,IAAK,IAAI4V,EAAQ,EAAGA,EAAQ,EAAGA,IAAS,CAEtC,IAAMigB,EAAOJ,GADO,EAAI7f,EACe,EACvC+f,CAAQ,CADgBC,AACfhgB,EAAM,CAAW,IAARigB,CACpB,CAEA,MAAO,CACL3O,KAAMsO,AAAY,IAZHD,GAAY,EAAK,CAAA,EAYV,YAAc,gBACpCI,SAAUA,EAQVG,YAAaJ,AAAa,IAnBA,EAAXH,CAAW,CAoB5B,CACF,CAMO,SAASD,EACd9wB,CAAe,CACfuxB,CAAyB,EAEzB,IAAMC,EAAe,AAAIh2B,MAAMwE,EAAKO,MAAM,EAE1C,IAAK,IAAI6Q,EAAQ,EAAGA,EAAQpR,EAAKO,MAAM,CAAE6Q,KAEpCA,EAAQ,CAFqC,EAEhCmgB,EAAKJ,QAAQ,CAAC/f,EAAM,EAGjCA,EAFD,CAEU,GAAKmgB,EAAKD,WAAAA,AAAW,EAC/B,EACAE,CAAY,CAACpgB,EAAM,CAAGpR,CAAI,CAACoR,EAAM,AAANA,EAI/B,OAAOogB,CACT,gCAT8E,2GCwI9DC,sBAAAA,qCAAAA,aAxMW,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,QAO1B,CAAA,CAAA,IAAA,QAoBA,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,MACA,CAAA,CAAA,IAAA,QACU,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QAEd,CAAA,CAAA,IAAA,QACgB,CAAA,CAAA,IAAA,QACT,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,QACZ,CAAA,CAAA,IAAA,QACU,CAAA,CAAA,IAAA,QAIzC,CAAA,CAAA,IAAA,QAC0B,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACkB,CAAA,CAAA,IAAA,QAChB,CAAA,CAAA,IAAA,OACH,CAAA,CAAA,IAAA,QAIrB,CAAA,CAAA,IAAA,MAC+B,CAAA,CAAA,IAAA,IAxCtC,GAAM,iBAAEC,CAAe,6BAAEC,CAA2B,aAAEC,CAAW,CAAE,CAGrD,EAFZ,AAEY,CAFX,AAEW,CAFV/zB,AAEU,OAoDd,CAtDYC,GAAG,CAACiJ,IAIVC,MAkDS6qB,EAtDa,AAuD1BnjB,AAnDY,CAmDe,CAC3B1G,CAtDIhB,AAsDoC,CACxC,CAA4C,EAA5C,IAvDY,AA4FRuY,EAaA8S,EAlDJ,UAAEP,CAAQ,YAAEC,CAAU,CAAsB,CAA5C,EAEMC,EAAsBL,IACtBJ,EAAOV,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAACiB,GAKxCX,EACU,cAAdI,EAAK7O,IAAI,CAAmBoO,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACiB,EAAYR,GAAQQ,EAE3D7oB,EAAO,MAAM0oB,EAAYT,EAAU,qBAAEa,CAAoB,GAEzDrxB,EAAM,MAAMqJ,MAAM,GAAI,CAC1BpI,OAAQ,OACRuE,QAAS,CACP8rB,OAAQ5tB,EAAAA,uBAAuB,CAC/B,CAACd,EAAAA,aAAa,CAAC,CAAEuuB,EACjB,CAAC5tB,EAAAA,6BAA6B,CAAC,CAAE/G,mBAC/B8E,KAAKC,SAAS,CAACwM,EAAM9J,IAAI,GAE3B,GAAI/G,AAIA,CAAC,CAAC,CACN,GAAImK,EALQlK,AAMR,CACE,CAACsG,CAPQ,CAAC2F,AAOT3F,QAAQ,CAAC,CAAE4D,CACd,EACA,CAAC,CAAC,AACR,GAVoC,GAC9B,CAUNkB,CACF,GAEMgpB,EAAiBvxB,EAAIwF,OAAO,CAACwC,GAAG,CAAC,qBACjC,CAACxI,EAAUgyB,EAAc,CAAGD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBnxB,KAAK,CAAC,IAAA,CAAA,EAAQ,EAAE,CAElE,OAAQoxB,GACN,IAAK,OACH5S,EAAeI,EAAAA,YAAY,CAACjkB,IAAI,CAChC,KACF,KAAK,UACH6jB,EAAeI,EAAAA,YAAY,CAACviB,OAAO,CACnC,KACF,SACEmiB,OAAe9c,CACnB,CAEA,IAAM2vB,EAAc,CAAC,CAACzxB,EAAIwF,OAAO,CAACwC,GAAG,CAAC/E,EAAAA,wBAAwB,EAE9D,GAAI,CACF,IAAM0uB,EAAoBrwB,KAAKswB,KAAK,CAClC5xB,EAAIwF,OAAO,CAACwC,GAAG,CAAC,yBAA2B,YAE7C0pB,EAAmB,CACjBG,MAAOF,CAAiB,CAAC,EAAE,EAAI,EAAE,CACjCrG,IAAK,CAAC,CAACqG,CAAiB,CAAC,EAAE,CAC3BG,OAAQH,CAAiB,CAAC,EAAE,AAC9B,CACF,CAAE,MAAOI,EAAG,CACVL,EAAmB,CACjBG,MAAO,EAAE,CACTvG,IAAK,GACLwG,QAAQ,CACV,CACF,CAEA,IAAME,EAAmBxyB,EACrBwwB,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EACZxwB,EACA,IAAI+G,IAAIwH,EAAMrH,YAAY,CAAEnH,OAAOC,QAAQ,CAACC,IAAI,QAElDqC,EAEEiG,EAAc/H,EAAIwF,OAAO,CAACwC,GAAG,CAAC,gBAEpC,GAAID,QAAAA,KAAAA,EAAAA,EAAa9F,UAAU,CAACyB,EAAAA,uBAAuB,EAAG,CACpD,IAAMiF,EAAiC,MAAMooB,EAC3CriB,QAAQ6F,OAAO,CAACvU,GAChB,CAAEwJ,WAAAA,EAAAA,UAAU,CAAEC,iBAAAA,EAAAA,gBAAgB,qBAAE4nB,CAAoB,UAGtD,AAAI7xB,EAEK,CACLyyB,OAHU,UAGQnuB,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC6E,EAASE,CAAC,mBAChDmpB,EACApT,eACA8S,mBACAD,aACF,EAGK,CACLS,aAAcvpB,EAASwpB,CAAC,CACxBF,iBAAkBnuB,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC6E,EAASE,CAAC,mBAChDmpB,eACApT,mBACA8S,cACAD,CACF,CACF,CAGA,GAAIzxB,EAAIsM,MAAM,EAAI,IAQhB,CARqB,KAQf,OAAA,cAAgB,CAAhB,AAAIxL,MAJQ,AAIFO,eAJd0G,EACI,MAAM/H,EAAI+Z,IAAI,GACd,wDAEA,oBAAA,OAAA,mBAAA,gBAAA,CAAe,GAGvB,MAAO,CACLiY,gCACApT,mBACA8S,EACAD,aACF,CACF,CAMO,SAASX,EACd/iB,CAA2B,CAC3BsK,CAA0B,EAE1B,GAAM,SAAE9D,CAAO,QAAEC,CAAM,CAAE,CAAG6D,EACtBlF,EAA+B,CAAC,EAElCrD,EAAc/B,EAAM9J,IAAI,CAE5BkP,EAAQM,0BAA0B,EAAG,EAMrC,IAAMpM,EACJ0G,EAAM1G,OAAO,EAAIooB,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EAAC1hB,EAAM9J,IAAI,EACzD8J,EAAM1G,OAAO,CACb,KAEAsE,EAAc2L,KAAKC,GAAG,GAE5B,OAAO2Z,EAAkBnjB,EAAO1G,EAASgR,GAAQ7J,IAAI,CACnD,MAAA,QAQM4jB,EARC,cACLF,CAAY,CACZD,iBAAkBvtB,CAAU,kBAC5BstB,CAAgB,cAChBpT,CAAY,aACZ6S,CAAW,kBACXC,CAAgB,CACjB,CAAA,EAiBC,GAbIM,IACEpT,IAAiBI,EAAAA,QADD,IACa,CAACviB,OAAO,EAAE,AACzCsR,EAAMuF,OAAO,CAACC,WAAW,EAAG,EAC5BJ,EAAQI,WAAW,EAAG,IAEtBxF,EAAMuF,OAAO,CAACC,WAAW,EAAG,EAC5BJ,EAAQI,WAAW,EAAG,GAIxBJ,EAAQzM,YAAY,CADpB0rB,EAAejoB,AACQioB,CADRjoB,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC6nB,EAAkB,KAIjD,CAACttB,QAIH,CAHA6P,EAAQ2d,CADO,EAIXF,GACK5E,CAAAA,EAAAA,EAAAA,UADa,OACbA,AAAiB,EACtBrf,EACAoF,EACA6e,EAAiBvyB,IAAI,CACrBsO,EAAMuF,OAAO,CAACC,WAAW,EAGtBxF,EAGT,GAA0B,UAAtB,AAAgC,OAAzBrJ,EAIT,OAFA6P,EAAQ2d,GAED9E,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACtBrf,EACAoF,EACAzO,EACAqJ,EAAMuF,OAAO,CAACC,WAAW,EAI7B,IAAM8e,EACJX,EAAiBG,KAAK,CAACjyB,MAAM,CAAG,GAChC8xB,EAAiBpG,GAAG,EACpBoG,EAAiBI,MAAM,CAEzB,IAAK,IAAM3I,KAAwBzkB,EAAY,CAC7C,GAAM,CACJT,KAAMmJ,CAAS,CACflJ,SAAU2H,CAAiB,MAC3B1H,CAAI,cACJK,CAAY,CACb,CAAG2kB,EAEJ,GAAI,CAAC3kB,EAKH,OAHAyE,KAFiB,GAET0mB,GAAG,CAAC,8BACZpb,EAAQ2d,GAEDnkB,EAIT,IAAMoe,EAAUvd,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAEzC,CAAC,GAAG,CACJkB,EACA1C,EACAglB,GAA8BrkB,EAAMrH,QAJd,EAIP0rB,EAAiC,EAGlD,GAAgB,MAAM,CAAlBjG,EAGF,OAFA5X,EAAQ2d,GAED1C,GAAAA,EAAAA,qBAAAA,AAAqB,EAACzhB,EAAOsK,EAAQjL,GAG9C,GAAIyC,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACC,EAAaqc,GAG3C,OAHqD,AACrD5X,EAAQ2d,GAED9E,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACtBrf,EACAoF,EACAif,GAAgBrkB,EAAMrH,YAAY,CAClCqH,EAAMuF,OAAO,CAACC,WAAW,EAK7B,GAA0B,OAAtB1H,EAA4B,CAC9B,IAAMR,EAAMQ,CAAiB,CAAC,EAAE,CAC1B0B,EAAmBuS,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,IAC7CvS,EAAMlC,GAAG,CAAGA,EACZkC,EAAMjC,WAAW,CAAG,KACpBiC,EAAMV,OAAO,CAAGhB,CAAiB,CAAC,EAAE,CACpCH,CAAAA,EAAAA,EAAAA,6BAAAA,AAA6B,EAC3BC,EACA4B,OACA,EAEAH,EACAvB,EACA1H,OACArC,GAGFqR,EAAQ5F,KAAK,CAAGA,EAId4F,EAAQc,aAAa,CAAG,IAAIhJ,IAE1BonB,GACF,EAfyF,IAenF5kB,CAAAA,EAAAA,EAAAA,KADe,0BACgB,AAA/BA,EAAgC,aACpC9B,QACAoC,EACAD,YAAaqe,EACbne,aAAcT,EACdU,gBAAgBnI,CAAQuB,EACxBX,aAAcyM,EAAQzM,YAAY,EAAIqH,EAAMrH,YAAY,AAC1D,EAEJ,CAEAyM,EAAQE,WAAW,CAAG8Y,EACtBrc,EAAcqc,CAChB,CAoDA,OAlDI6F,GAAoBI,GAC2BC,IAW/Chd,CAAAA,EAAAA,EAAAA,EAZkC,QACgC,oBAWlEA,AAA8B,EAAC,CAC7BpY,IAAK+0B,EACLpb,KAAM,YACJlS,EACAgC,kBAAc5E,EACd6E,oBAAoB,EACpBC,YAAa,GACbC,WAAW,EAGXC,UAAW,CAAC,CACd,EACA7C,KAAM8J,EAAM9J,IAAI,CAChBgQ,cAAelG,EAAMkG,aAAa,CAClC5M,QAAS0G,EAAM1G,OAAO,CACtBgF,KAAMolB,EAAclqB,EAAAA,YAAY,CAACsO,IAAI,CAAGtO,EAAAA,YAAY,CAACC,IACvD,AAD2D,GAE3D2L,EAAQc,aAAa,CAAGlG,EAAMkG,aAAa,EAS7CO,EACE8d,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EACd1S,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACwS,GACRvS,GAAAA,EAAAA,cAAAA,AAAc,EAACuS,GACfA,EACJxT,GAAgBI,EAAAA,YAAY,CAACjkB,IAAI,IAIrCwZ,EAAQ2d,GAGHjf,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAClF,EAAOoF,EAC9B,EACA,AAAC4e,IAECvd,EAAOud,GAEAhkB,GAGb,kWCvWawkB,UAAAA,qCAAAA,aAzDN,CAAA,CAAA,IAAA,QAMyB,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,GA6CvBA,EACO,aAAlB,CAAgCQ,MAAzBxzB,OATT,GASkDizB,MATzCO,AACPhlB,CAA2B,CAC3BgiB,CAAuB,EAEvB,OAAOhiB,CACT,EArCA,SAASykB,AACPzkB,CAA2B,CAC3BsK,CAAsB,EAEtB,OAAQA,EAAO0J,IAAI,EACjB,KAAK0Q,EAAAA,eAAe,CAClB,MAAOpF,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACtf,EAAOsK,EAEhC,MAAKqa,EAAAA,mBAAmB,CACtB,MAAOzD,GAAAA,EAAAA,kBAAkB,AAAlBA,EAAmBlhB,EAAOsK,EAEnC,MAAK2J,EAAAA,cAAc,CACjB,MAAOoN,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACrhB,EAAOsK,EAE/B,MAAKsa,EAAAA,cAAc,CACjB,MAAOjD,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAAC3hB,EAAOsK,EAE/B,MAAKua,EAAAA,kBAAkB,CACrB,MAAOhD,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC7hB,EAAOsK,EAElC,MAAKwa,EAAAA,eAAe,CAClB,MAAO5a,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAClK,EAAOsK,EAEhC,MAAKya,EAAAA,oBAAoB,CACvB,MAAOhC,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC/iB,EAAOsK,EAGpC,SACE,MAAM,OAAA,cAA2B,CAAvBvX,AAAJ,MAAU,kBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAA0B,EACpC,CACF,mVCkJgBkyB,wBAAwB,CAAA,kBAAxBA,GA0DAC,sBAAsB,CAAA,kBAAtBA,GA+BAlQ,sBAAsB,CAAA,kBAAtBA,GAnDAmQ,wBAAwB,CAAA,kBAAxBA,GAuEH7Q,uBAAuB,CAAA,kBAAvBA,+EA1SN,CAAA,CAAA,IAAA,QACiB,CAAA,CAAA,IAAA,OACQ,CAAA,CAAA,IAAA,QACL,CAAA,CAAA,IAAA,MAC0B,CAAA,CAAA,IAAA,YACb,CAAA,CAAA,IAAA,QACZ,CAAA,CAAA,IAAA,QACqB,CAAA,CAAA,IAAA,QACjB,CAAA,CAAA,IAAA,QAM+B,CAAA,CAAA,IAAA,IA4B/D,SAAS8Q,EACP9R,CAAiC,CACjClG,CAA8B,EAEF,MAAM,CAA9BkG,EAAY+R,OAAO,GACrB/R,EAAY+R,OAAO,CAAG/R,EAAY+R,OAAO,CAACna,IAAI,CAClB,MAAM,CAA9BoI,EAAY+R,OAAO,CAErBC,EAAU,aACRhS,EACAhJ,OAAQgJ,EAAY+R,OAAO,UAC3BjY,CACF,GAGIkG,EAAYiS,YAAY,EAAE,CAC5BjS,EAAYiS,YAAY,EAAG,EAC3BjS,EAAYkS,QAAQ,CAClB,CACExR,KAAM4Q,EAAAA,cAAc,CACpBjzB,OAAQH,OAAOC,QAAQ,CAACE,MAAM,AAChC,EACAyb,IAKV,CAEA,eAAekY,EAAU,CAQxB,EARwB,GAAA,aACvBhS,CAAW,QACXhJ,CAAM,UACN8C,CAAQ,CAKT,CARwB,EASjBqY,EAAYnS,EAAYtT,KAAK,CAEnCsT,EAAY+R,OAAO,CAAG/a,EAEtB,IAAMob,EAAUpb,EAAOob,OAAO,CACxBvB,EAAe7Q,EAAYhJ,MAAM,CAACmb,EAAWC,GAEnD,SAASC,EAAaC,CAAyB,EAEzCtb,EAAOub,SAAS,EAAE,CAItBvS,EAAYtT,KAAK,CAAG4lB,EAEpBR,EAAoB9R,EAAalG,GACjC9C,EAAO9D,OAAO,CAACof,GACjB,CAGIE,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC3B,GACbA,EAAa1jB,IAAI,CAACklB,EAAc,AAAC3qB,GADL,CAE1BoqB,EAAoB9R,EAAalG,GACjC9C,EAAO7D,MAAM,CAACzL,EAChB,GAEA2qB,EAAaxB,EAEjB,CA+EA,IAAIiC,EAAiD,KAE9C,SAASnB,EACdoB,CAA4B,CAC5BC,CAAuD,EAEvD,IAAMhT,EAAoC,CACxCtT,MAAOqmB,EACPb,SAAU,CAACE,EAAyBtY,IAClC2Y,CAtFN,SAASA,AACPzS,CAAiC,CACjCoS,CAAuB,CACvBtY,CAA8B,EAE9B,IAAI4Y,EAGA,CAAExf,QAAS4G,EAAU3G,OAAQ,KAAO,CAAE,EAM1C,GAAIif,EAAQ1R,IAAI,GAAKC,EAAAA,cAAc,CAAE,CAEnC,IAAMgS,EAAkB,IAAItlB,QAAwB,CAAC6F,EAASC,KAC5Duf,EAAY,SAAExf,SAASC,CAAO,CAChC,GAEAuK,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KAGd5D,EAAS6Y,EACX,EACF,CAEA,IAAMC,EAA6B,SACjCR,EACAxa,KAAM,KACN1E,QAASwf,EAAUxf,OAAO,CAC1BC,OAAQuf,EAAUvf,MACpB,AAD0B,CAIE,MAAM,EAA9B6M,EAAY+R,OAAO,EAGrB/R,EAAY6S,IAAI,CAAGD,EAEnBZ,EAAU,aACRhS,EACAhJ,OAAQ4b,WACR9Y,CACF,IAEAsY,EAAQ1R,IAAI,GAAK0Q,EAAAA,eAAe,EAChCgB,EAAQ1R,IAAI,GAAKC,EAAAA,cAAc,EAC/B,AAGAX,EAAY+R,OAAO,CAACQ,SAAS,CAAG,GAIhCK,EAAUhb,IAAI,CAAGoI,EAAY+R,OAAO,CAACna,IAAI,CAGrCoI,EAAY+R,OAAO,CAACK,OAAO,CAAC1R,IAAI,GAAK+Q,EAAAA,oBAAoB,EAAE,AAC7DzR,GAAYiS,YAAY,EAAG,CAAA,EAG7BD,EAAU,aACRhS,EACAhJ,OAAQ4b,WACR9Y,CACF,KAIyB,MAAM,CAA3BkG,EAAY6S,IAAI,EAClB7S,GAAY6S,IAAI,CAACjb,IAAI,CAAGgb,CAAAA,EAE1B5S,EAAY6S,IAAI,CAAGD,GAEvB,EAWqB5S,EAAaoS,EAAStY,GACvC9C,OAAQ,MAAOtK,EAAuBsK,IACrBka,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAACxkB,EAAOsK,GAGhC+a,QAAS,KACTc,KAAM,KACNI,wBAC2B,OAAzBD,GACwD,YAAxD,OAAOA,EAAqBC,uBAAuB,CAE/CD,EAAqBC,uBAAuB,CAC5C,IACR,EAEA,GAAsB,aAAlB,OAAO/0B,OAAwB,CAIjC,GAA0B,MAAM,CAA5B40B,EACF,MAAM,OAAA,cAGL,CAHK,AAAIrzB,MACR,sEACE,QAFE,oBAAA,OAAA,mBAAA,gBAAA,CAGN,GAEFqzB,EAAoB9S,CACtB,CAEA,OAAOA,CACT,CAEO,SAAS6R,IACd,OAA6B,OAAtBiB,EAA6BA,EAAkBpmB,KAAK,CAAG,IAChE,CAWA,SAASymB,WACP,AAA0B,MAAM,CAA5BL,EACKA,EAAkBG,uBAAuB,CAE3C,IACT,CAEO,SAASrB,EACdxzB,CAAY,CACZ6uB,CAA4C,CAC5Clb,CAAqB,CACrBqhB,CAAoC,EAIpC,IAAMx3B,EAAM,IAAIsJ,IAAIhE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC9C,GAAOD,SAASC,IAAI,EAKpDi1B,GAAAA,EAAAA,2BAAAA,AAA2B,EAACD,GAE5B,IAAMH,EAA0BE,GACA,MAAM,EAAlCF,GACFA,EAAwB70B,EAAM6uB,GAGhCxM,CAAAA,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBC,KAAM0Q,EAAAA,eAAe,CACrBx1B,MACAoxB,cAAepO,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAChjB,GAC7B03B,eAAgBn1B,SAAS3C,MAAM,cAC/BuW,eACAkb,EACAvY,eAAe,CACjB,EACF,CAEO,SAASgN,EACdtjB,CAAY,CACZwE,CAAmC,EAEnC,IAAMqwB,EAA0BE,GACA,MAAM,EAAlCF,GACFA,EAAwB70B,EAAM,YAEhCqiB,CAAAA,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBC,KAAMC,EAAAA,cAAc,CACpB/kB,IAAK,IAAIsJ,IAAI9G,GACbwE,MACF,EACF,CAOO,IAAMoe,EAA6C,CACxDuS,KAAM,IAAMr1B,OAAOkhB,OAAO,CAACmU,IAAI,GAC/BC,QAAS,IAAMt1B,OAAOkhB,OAAO,CAACoU,OAAO,GACrC9H,SAaI,CAbM7vB,AAaLuC,EAAc0H,KAEb,CAfYhK,GAAG,AAeTkkB,CAfUnJ,CAtExB,AAqF4Bqc,SArFnBA,EACP,GAA0B,CAuE8C,KAvExC,CAA5BJ,EACF,GAoE+C,GAE7C,AAtEI,OAAA,cAEL,CAFSrzB,AAAJ,MACJ,2EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,OAAOqzB,CACT,IA+Ecl3B,EAAM8iB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACtgB,GAC9B,GAAIxC,AAAQ,SAAM,KAURkK,EAHR8Q,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACoJ,EAAYtT,KAAK,CAAE,CACjCgU,KAAM8Q,EAAAA,eAAe,KACrB51B,EACAoP,KAAMlF,AAAa,OAAbA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASkF,IAAAA,AAAI,EAAblF,EAAiBI,EAAAA,YAAY,CAACsO,IAAI,AAC1C,EACF,CACF,EACJpZ,QAAS,CAACgD,EAAc0H,KACtB4X,CAAAA,EAAAA,EAAAA,eAAe,AAAfA,EAAgB,SAC0B5X,EAAxC8rB,EAAuBxzB,EAAM,UAAW0H,AAAe,OAAfA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS2tB,MAAAA,AAAM,GAAf3tB,EAAyB,KACnE,EACF,EACApM,KAAM,CAAC0E,CAHwD,CAG1C0H,KACnB4X,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,SACuB5X,EAArC8rB,EAAuBxzB,EAAM,OAAQ0H,AAAe,OAAfA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS2tB,MAAM,AAANA,GAAT3tB,EAAyB,KAChE,EACF,EACA4tB,OAH4D,CAGnD,KACPhW,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACd+C,CAAAA,EAAAA,EAAAA,uBAAuB,AAAvBA,EAAwB,CACtBC,KAAM4Q,EAAAA,cAAc,CACpBjzB,OAAQH,OAAOC,QAAQ,CAACE,MAAM,AAChC,EACF,EACF,EACAs1B,WAAY,KAER,MAAM,OAAA,cAEL,CAFK,AAAIl0B,MACR,gFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EASJ,CACF,EAGsB,aAAlB,OAAOvB,QAA0BA,OAAO0Z,IAAI,EAAE,CAChD1Z,OAAO0Z,IAAI,CAAC4F,MAAM,CAAGwD,CAAAA,mVCxTV4S,gBAAgB,CAAA,kBAAhBA,GAHAC,mBAAmB,CAAA,kBAAnBA,GAwHGC,iBAAiB,CAAA,kBAAjBA,GA1CAC,iBAAiB,CAAA,kBAAjBA,GA8FAC,uBAAuB,CAAA,kBAAvBA,GAsBAC,kBAAkB,CAAA,kBAAlBA,GAoFAC,gBAAgB,CAAA,kBAAhBA,GA7QAb,2BAA2B,CAAA,kBAA3BA,GASAc,+BAA+B,CAAA,kBAA/BA,GAiIAC,2BAA2B,CAAA,kBAA3BA,yEAlNyB,CAAA,CAAA,IAAA,YACP,CAAA,CAAA,IAAA,QACL,CAAA,CAAA,IAAA,QACU,CAAA,CAAA,IAAA,QASP,CAAA,CAAA,IAAA,IAgD5BC,EAAmD,KAG1CR,EAAsB,CAAE9B,SAAS,CAAK,EAGtC6B,EAAmB,CAAE7B,SAAS,CAAM,EAM1C,SAASsB,EAA4B1P,CAAyB,EACnEjG,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACd2W,MAAAA,CAAAA,EAAAA,EAA6BC,CAA7BD,sBAAoD,CAACT,GACrDjQ,MAAAA,CAAAA,EAAAA,EAAM2Q,CAAN3Q,sBAA6B,CAACkQ,GAC9BQ,EAA8B1Q,CAChC,EACF,CAGO,SAASwQ,EAAgCxQ,CAAkB,EAC5D0Q,IAAgC1Q,IAClC0Q,EADwC,AACV,IAAA,CAElC,CAIA,IAAME,EAGe,YAAnB,OAAOC,QAAyB,IAAIA,QAAY,IAAI5qB,IAMhD6qB,EAAoD,IAAInoB,IAGxDooB,EAC4B,YAAhC,OAAOC,qBACH,IAAIA,qBAAqBC,AA0H/B,SAASA,AAAgBt7B,CAAyC,EAChE,IAAK,IAAMw8B,KAASx8B,EAAS,CAI3B,IAAMk8B,EAAYM,EAAMC,iBAAiB,CAAG,EAC5C/B,EAAwB8B,EAAM17B,MAAM,CAAuBo7B,EAC7D,CACF,EAlIgD,CACxCX,WAAY,OACd,GACA,KAEN,SAASC,EAAkBC,CAAgB,CAAEC,CAA8B,EAErEC,KAAqBx0B,IADA8zB,EAAa5tB,GAAG,CAACouB,CACN,GAIlCX,EAA4BW,GAG9BR,EAAap6B,GAAG,CAAC46B,EAASC,GACT,MAAM,CAAnBN,GACFA,EAASQ,OAAO,CAACH,EAErB,CAEA,SAASI,EAAsB/2B,CAAY,EACzC,GAAI,CACF,MAAOsgB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACtgB,EAC3B,CAAE,MAAA,EAAM,CAWN,MAHAg3B,CADyB,YAAvB,OAAOC,YAA6BA,YAAcztB,QAAQ5H,KAAAA,AAAK,EAE9D,oBAAmB5B,EAAK,8CAEpB,IACT,CACF,CAEO,SAAS21B,EACdgB,CAAoB,CACpB32B,CAAY,CACZof,CAAyB,CACzBxS,CAA2C,CAC3CsqB,CAAwB,CACxBhB,CAA+D,EAE/D,GAAIgB,EAAiB,CACnB,IAAMC,EAAcJ,EAAsB/2B,GAC1C,GAAoB,OAAhBm3B,EAAsB,CACxB,IAAMP,EAAqC,QACzCxX,OACAxS,EACAwqB,WAAW,EACXC,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcL,EAAYn3B,IAAI,yBAC9Bk2B,CACF,EAIA,OADAQ,EAAkBC,EAASC,GACpBA,CACT,CACF,CAaA,MAV8C,CAUvCA,AATLxX,cACAxS,EACAwqB,WAAW,EACXC,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAc,6BACdtB,CACF,CAEF,CAEO,SAASR,EACdiB,CAAwB,CACxB32B,CAAY,CACZof,CAAyB,CACzBxS,CAA2C,EAE3C,IAAMuqB,EAAcJ,EAAsB/2B,EACtB,MAAM,EAAtBm3B,GAiBJT,EAAkBC,EAVa,OAUJC,CATzBxX,OACAxS,EACAwqB,WAAW,EACXC,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcL,EAAYn3B,IAAI,CAC9Bk2B,wBAAyB,IAC3B,EAEF,CAEO,SAASF,EAA4BW,CAAgB,EAC1D,IAAMC,EAAWT,EAAa5tB,GAAG,CAACouB,GAClC,QAAiBt0B,IAAbu0B,EAAwB,CAC1BT,EAAah6B,MAAM,CAACw6B,GACpBN,EAAuBl6B,MAAM,CAACy6B,GAC9B,IAAMU,EAAeV,EAASU,YAAY,AACrB,MAAM,EAAvBA,GACFpK,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACoK,EAEvB,CACiB,MAAM,CAAnBhB,GACFA,EAASmB,SAAS,CAACd,EAEvB,CAYO,SAASf,EAAwBe,CAAgB,CAAES,CAAkB,EAQ1E,IAAMR,EAAWT,EAAa5tB,GAAG,CAACouB,QACjBt0B,IAAbu0B,IAIJA,EAASQ,CAJmB,QAIV,CAAGA,EACjBA,EACFf,EAAuBxnB,GAAG,CAAC+nB,GADd,AAGbP,EAAuBl6B,MAAM,CAACy6B,GAEhCgB,EAAuBhB,GACzB,CAEO,SAASf,EACdc,CAAwC,CACxCkB,CAA0C,EAE1C,IAAMjB,EAAWT,EAAa5tB,GAAG,CAACouB,QACjBt0B,IAAbu0B,OAAwB,CAIXv0B,IAAbu0B,IACFA,EAASS,CADiB,kBACE,EAAG,EAQ/BO,EAAuBhB,GAE3B,CAEA,SAASgB,EAAuBhB,CAA8B,MAyGlBA,EAxG1C,IAAMmB,EAAuBnB,AAwG2C,EAxGlCU,YAAY,CAElD,GAAI,CAACV,EAASQ,SAAS,CAAE,CAGM,MAAM,CAA/BW,GACF7K,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC6K,GAMrB,MACF,GAKqCnB,EAwFf,aAAlB,AAA+B,OAAxB92B,QAgBXu4B,CAZmB,SAGVzB,EAASxX,MAAM,CAACkO,QAAQ,CAACsJ,EAASY,YAAY,CAAE,CACrD5qB,KAAMgqB,EAAShqB,IAAI,AACrB,EACF,IAMa0rB,KAAK,CAAC,AAAChvB,IAKpB,EApEF,CAEO,SAASwsB,EACdluB,CAAsB,CACtBpD,CAAuB,EASvB,IAAM2zB,EAAsB/K,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,IAClD,IAAK,IAAMwJ,KAAYP,EAAwB,CAC7C,IAAMrhB,EAAO4hB,EAASU,YAAY,CAClC,GACW,OAATtiB,GACA4hB,EAASW,YAAY,GAAKY,GAC1BnjB,EAAKha,GAAG,CAAC4M,OAAO,GAAKA,GACrBoN,EAAK2C,oBAAoB,GAAKnT,EAI9B,IAHA,IAOW,AAATwQ,MAAe,KACjBkY,CAAAA,EAAAA,EAAAA,kBAAkB,AAAlBA,EAAmBlY,GAErB,IAAM5J,EAAW+hB,CAAAA,EAAAA,EAAAA,cAAAA,AAAc,EAACyJ,EAASY,YAAY,CAAE5vB,GACjDkC,EAAW8sB,EAASS,mBAAmB,CACzCpK,EAAAA,gBAAgB,CAACgL,MAAM,CACvBhL,EAAAA,gBAAgB,CAACiL,OAAO,CAC5BtB,EAASU,YAAY,CAAGc,CAAAA,EAAAA,EAAAA,oBAA2B,AAA3BA,EACtBhtB,EACA5G,EACAoyB,EAAShqB,IAAI,GAAK9E,EAAAA,YAAY,CAACsO,IAAI,CACnCtM,GAEF8sB,EAASW,YAAY,CAAGnK,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,GAChD,CACF,kWC1XgBmL,aAAAA,qCAAAA,aANiC,CAAA,CAAA,IAAA,QACrB,CAAA,CAAA,IAAA,IAKrB,SAASA,EAAW/6B,CAAW,EAEpC,GAAI,CAAC4B,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAC5B,GAAM,OAAO,EAChC,GAAI,CAEF,IAAMg7B,EAAiBt5B,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,IAClCu5B,EAAW,IAAI3xB,IAAItJ,EAAKg7B,GAC9B,OAAOC,EAASx4B,MAAM,GAAKu4B,GAAkBrY,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACsY,EAAS77B,QAAQ,CAC5E,CAAE,MAAO+jB,EAAG,CACV,OAAO,CACT,CACF,6ICNS+X,YAAAA,qCAAAA,KAXT,IAAIA,EAAY,AAAC/X,IAAe,4HC6ThC,OAyZC,CAAA,kBAzZuBgY,GA+ZXC,aAAa,CAAA,kBAAbA,+GA1tB2D,CAAA,CAAA,IAAA,SAE9C,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACH,CAAA,CAAA,IAAA,YASlB,CAAA,CAAA,IAAA,QACoB,CAAA,CAAA,IAAA,OACY,CAAA,CAAA,IAAA,IAsRvC,SAASmB,EAAkBC,CAAkC,QAC3D,AAA8B,UAA1B,AAAoC,OAA7BA,EACFA,EAGF59B,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC49B,EACnB,CAYe,SAASrB,EACtBx3B,CAGC,EAED,IAEImR,EA+LAkpB,EAyLAjW,EA1XE,CAAC0U,EAAY/D,EAAwB,CAAGgE,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAC1E,EAAAA,gBAAgB,EAItER,EAAkBh3B,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MAE9C,CACJgC,KAAMm6B,CAAQ,CACdX,GAAIY,CAAM,CACV9nB,SAAU+nB,CAAY,CACtB/M,SAAUgN,EAAe,IAAI,UAC7BC,CAAQ,SACRv9B,CAAO,SACPw9B,CAAO,QACPnF,CAAM,SACNoF,CAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,YACtBrB,CAAU,CACVsB,IAAKC,CAAY,yBACjBC,CAAuB,CACvB,GAAGC,EACJ,CAAG/5B,EAEJmR,EAAW+nB,EAGTS,IACqB,UAApB,IAAA,GAAOxoB,GAA6C,UAApB,OAAOA,CAAa,CAAO,GAE5DA,AADA,EACW,GAAA,EAAA,GAAA,CAAXA,CAAYogB,IAAAA,MAAZpgB,IAAeA,KAGjB,IAAM8M,EAASrE,EAAAA,OAAK,CAAC3B,UAAU,CAACiL,EAAAA,gBAAgB,EAE1C6S,GAAmC,IAAjBoD,EAQlBa,EACa,OAAjBb,EAAwBxyB,EAAAA,YAAY,CAACC,IAAI,CAAGD,EAAAA,YAAY,CAACsO,IAAI,CA2IzD,MAAEpW,CAAI,IAAEw5B,CAAE,CAAE,CAAGze,EAAAA,OAAK,CAACiH,OAAO,CAAC,KACjC,IAAMuZ,EAAexB,EAAkBI,GACvC,MAAO,CACLn6B,KAAMu7B,EACN/B,GAAIY,EAASL,EAAkBK,GAAUmB,CAC3C,CACF,EAAG,CAACpB,EAAUC,EAAO,EAIjBU,IA4BAU,EAAQzgB,EAAAA,OAAK,CA5BG,AA4BF0gB,QAAQ,CAACC,IAAI,CAACppB,EAAAA,EAYhC,IAAMqpB,EAAgBb,EAClBU,GAA0B,UAAjB,OAAOA,GAAsBA,EAAMT,GAAG,CAC/CC,EAMEY,EAA+B7gB,EAAAA,OAAK,CAAC7c,WAAW,CACpD,AAACy4B,IACgB,MAAM,CAAjBvX,IACF4V,EAAgB72B,OAAO,CAAGw3B,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACzCgB,EACA32B,EACAof,EACA+b,EACAjE,EACAhB,EAAAA,EAIG,KACDlB,EAAgB72B,OAAO,EAAE,CAC3B43B,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACf,EAAgB72B,OAAO,EACvD62B,EAAgB72B,OAAO,CAAG,MAE5B63B,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACW,EAC9B,GAEF,CAACO,EAAiBl3B,EAAMof,EAAQ+b,EAAiBjF,EAAwB,EAKrE4F,EAMF,CACFf,IATgBn9B,CAAAA,AASXi+B,EATWj+B,EAAAA,YAAAA,AAAY,EAACg+B,EAA8BD,GAU3DlB,QAAQnI,CAAC,EASH,AAACwI,GAAqC,YAAnB,AAA+B,OAAxBL,GAC5BA,EAAQnI,GAIRwI,GACAU,EAAMr6B,KAAK,EACoB,YAA/B,AACA,OADOq6B,EAAMr6B,KAAK,CAACs5B,OAAO,EAE1Be,EAAMr6B,KAAK,CAACs5B,OAAO,CAACnI,GAGjBlT,IAIDkT,EAAEyJ,EAJO,cAIS,EAItBxC,AAvYN,AAmY8B,SAnYrBA,AACPjH,CAAmB,CACnBtyB,CAAY,CACZw5B,CAAU,CACVxE,CAAqD,CACrDh4B,CAAiB,CACjBq4B,CAAgB,CAChBoE,CAAmC,EAEnC,GAAM,UAAEC,CAAQ,CAAE,CAAGpH,EAAEyG,aAAa,CAKpC,KAFoD,AAGjDY,MAHsBD,EAASE,WAAW,IAGtBf,AA5BzB,SAASA,AAAgB3W,CAAuB,EAE9C,IAAMlmB,EAAS88B,AADK5W,EAAM6W,aAAa,CACZC,YAAY,CAAC,UACxC,OACGh9B,GAAqB,UAAXA,GACXkmB,EAAM+W,OAAO,EACb/W,EAAMgX,OAAO,EACbhX,EAAMiX,QAAQ,EACdjX,EAAMkX,MAAM,EACXlX,EAAMmX,AADS,WACE,EAAgC,IAA5BnX,EAAMmX,UADiB,CACN,CAACC,KAAK,AAEjD,EAiByChH,IACrCA,EAAEyG,aAAa,CAACc,YAAY,CAAC,WAAA,GAC7B,AAKF,GAAI,CAACtB,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACv4B,GAAO,CACjBhD,IAGFs1B,EAAE3P,GAHS,WAGK,GAChB5iB,SAAS/C,OAAO,CAACgD,IAInB,MACF,CAEAsyB,EAAE3P,cAAc,GAyBhB5H,EAAAA,OAAK,CAACuE,eAAe,CAvBJ,AAuBK+N,KAtBpB,GAAIoM,EAAY,CACd,IAAIK,GAAqB,EAQzB,GANAL,EAAW,CACT9W,eAAgB,KACdmX,GAAqB,CACvB,CACF,GAEIA,EACF,MAEJ,CAEAtG,CAAAA,EAAAA,EAAAA,MAL0B,gBAK1BA,AAAsB,EACpBgG,GAAMx5B,EACNhD,EAAU,UAAY,OACtBq4B,MAAAA,GAAAA,EACAL,EAAgB72B,KADN,EACa,CAE3B,GAGF,EA2UkBm0B,EAAGtyB,EAAMw5B,EAAIxE,EAAiBh4B,EAASq4B,EAAQoE,GAC7D,EACAiB,aAAapI,CAAC,EACR,AAACwI,GAA8C,YAAY,AAAxC,OAAOH,GAC5BA,EAAiBrI,GAIjBwI,GACAU,EAAMr6B,KAAK,EACyB,YAApC,AACA,OADOq6B,EAAMr6B,KAAK,CAACu5B,YAAY,EAE/Bc,EAAMr6B,KAAK,CAACu5B,YAAY,CAACpI,GAGtBlT,GAIA8X,GAKLrB,CAAAA,CATa,CASbA,EAAAA,WALwBp4B,OAKxBo4B,AAAkB,CALcn4B,CAM9B40B,EAAEyG,AAN+B,CAACp7B,QAAQ,IAM3B,EAF4C,AAG3Dq+B,IAH+Bf,EAKnC,EACAL,aAEI,CAFUn9B,GAVqC,KAU7BC,AAETk9B,AAAatI,CAAC,EAFF,AAGf,AAACwI,CAHemB,EAG+B,IAbS,QAarC,AAAwC,OAAjCpB,GAC5BA,EAJ4C,AAI3BvI,GAIjBwI,AAPJz4B,GAQIm5B,EAAMr6B,KAAK,EACyB,YAApC,AACA,OADOq6B,EAAMr6B,KAAK,CAACy5B,YAAY,EAE/BY,EAAMr6B,KAAK,CAACy5B,YAAY,CAACtI,GAGtBlT,GAIA8X,GAKLrB,CAAAA,CATa,CASbA,EAAAA,SALsB,SAKtBA,AAAkB,EAChBvD,EAAEyG,aAAa,EAF4C,AAG3DiD,IAH+Bf,EAKnC,CACN,EAmCA,MA9BI77B,CAAAA,AA8BJ,EA9BIA,EAAAA,OA8BJ,MA9BIA,AAAa,EAACo6B,GAChBsC,EADqB,AACV97B,IAAI,CAAGw5B,EAElB,AAACsB,IACDP,IACgB,MAAfiB,CAAsB,CAAE,AAAlBlZ,IAAI,EAAc,SAAUkZ,EAAMr6B,KAAI,GAC7C,CACA26B,EAAW97B,IAAI,CAAG8C,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC02B,EAAAA,EAc9BjU,EATEuV,EASK/f,EAAAA,CAAPwK,MAAY,CAAC2W,IATK,AASlB3W,QAAyB,CAACiW,EAAOM,GAG/B,CAAA,EAAA,EAAA,GAAA,EAACpJ,IAAAA,CAAG,GAAGwI,CAAS,CAAG,GAAGY,CAAU,UAC7BxpB,IAML,CAAA,EAAA,EAAA,GAAA,EAAC6pB,EAAkBjY,QAAQ,CAAA,CAACjpB,MAAOg/B,WAChC1U,GAGP,GAhsB0B,CAAA,CAAA,IAAA,IAksB1B,IAAM4W,EAAoBC,CAAAA,EAAAA,EAAAA,aAApBD,AAAoBC,AAAa,EAErC5G,EAAAA,OAFI2G,SAEY,EAELvD,EAAgB,IACpBxf,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC+iB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]}