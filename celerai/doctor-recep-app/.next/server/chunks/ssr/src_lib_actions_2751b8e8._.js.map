{"version": 3, "sources": ["turbopack:///[project]/src/lib/actions/data:390535 <text/javascript>", "turbopack:///[project]/src/lib/actions/data:eee441 <text/javascript>", "turbopack:///[project]/src/lib/actions/data:1d9e42 <text/javascript>", "turbopack:///[project]/src/lib/actions/data:8158ad <text/javascript>", "turbopack:///[project]/src/lib/actions/data:64a5d0 <text/javascript>", "turbopack:///[project]/src/lib/actions/data:8e1e69 <text/javascript>", "turbopack:///[project]/src/lib/actions/data:8ec235 <text/javascript>"], "sourcesContent": ["/* __next_internal_action_entry_do_not_use__ [{\"608aaaf92844312537830e8c8d0c49debb89bda21b\":\"saveStreamingSummary\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var saveStreamingSummary=/*#__PURE__*/createServerReference(\"608aaaf92844312537830e8c8d0c49debb89bda21b\",callServer,void 0,findSourceMapURL,\"saveStreamingSummary\");", "/* __next_internal_action_entry_do_not_use__ [{\"40d596bef0460f198d588bbf8dede026bc85cca740\":\"createConsultation\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var createConsultation=/*#__PURE__*/createServerReference(\"40d596bef0460f198d588bbf8dede026bc85cca740\",callServer,void 0,findSourceMapURL,\"createConsultation\");", "/* __next_internal_action_entry_do_not_use__ [{\"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd\":\"generateSummaryStream\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var generateSummaryStream=/*#__PURE__*/createServerReference(\"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd\",callServer,void 0,findSourceMapURL,\"generateSummaryStream\");", "/* __next_internal_action_entry_do_not_use__ [{\"6016d1147dadc5ab75f7387f44ab799f7cd595708b\":\"updateConsultationImages\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var updateConsultationImages=/*#__PURE__*/createServerReference(\"6016d1147dadc5ab75f7387f44ab799f7cd595708b\",callServer,void 0,findSourceMapURL,\"updateConsultationImages\");", "/* __next_internal_action_entry_do_not_use__ [{\"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7\":\"updateAdditionalNotes\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var updateAdditionalNotes=/*#__PURE__*/createServerReference(\"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7\",callServer,void 0,findSourceMapURL,\"updateAdditionalNotes\");", "/* __next_internal_action_entry_do_not_use__ [{\"609aacf458083825c0e86748f13c86d5773450588b\":\"updatePatientName\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var updatePatientName=/*#__PURE__*/createServerReference(\"609aacf458083825c0e86748f13c86d5773450588b\",callServer,void 0,findSourceMapURL,\"updatePatientName\");", "/* __next_internal_action_entry_do_not_use__ [{\"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c\":\"getConsultationStats\"},\"src/lib/actions/consultations.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var getConsultationStats=/*#__PURE__*/createServerReference(\"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c\",callServer,void 0,findSourceMapURL,\"getConsultationStats\");"], "names": [], "mappings": "0LAA2J,EAAA,CAAA,CAAA,8BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAkC,CAAA,EAAA,EAAA,gBAAb,KAAiC,AAApB,EAAsB,IAAxB,UAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,oDCA5Q,EAAA,CAAA,CAAA,4BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAgC,GAAA,EAAA,cAAb,OAAa,AAAoB,EAAE,EAAxB,YAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,kDCArQ,EAAA,CAAA,CAAA,+BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAmC,GAAA,EAAA,iBAAb,IAAa,AAAoB,EAAE,KAAxB,SAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAC,AAAnC,oBAAkB,qDCAxQ,EAAA,CAAA,CAAA,kCAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAsC,GAAA,EAAA,oBAAb,CAAa,AAAoB,EAAE,QAAxB,MAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,wDCAjR,EAAA,CAAA,CAAA,+BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAmC,CAAA,EAAA,EAAA,iBAAb,IAAa,AAAoB,EAAE,KAAxB,SAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAC,AAAnC,oBAAkB,qDCA/Q,EAAA,CAAA,CAAA,2BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAA+B,GAAA,EAAA,aAAb,QAAa,AAAoB,EAAE,CAAxB,aAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,iDCApQ,EAAA,CAAA,CAAA,8BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAkC,CAAA,EAAA,EAAA,gBAAb,KAAa,AAAoB,EAAE,IAAxB,UAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB"}