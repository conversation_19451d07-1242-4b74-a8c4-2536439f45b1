module.exports={998934:a=>{"use strict";var{g:b,__dirname:c}=a;{function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function f(a){return"span"===a._type&&"text"in a&&"string"==typeof a.text&&(typeof a.marks>"u"||Array.isArray(a.marks)&&a.marks.every(a=>"string"==typeof a))}function g(a){return"string"==typeof a._type&&"@"!==a._type[0]&&(!("markDefs"in a)||!a.markDefs||Array.isArray(a.markDefs)&&a.markDefs.every(a=>"string"==typeof a._key))&&"children"in a&&Array.isArray(a.children)&&a.children.every(a=>"object"==typeof a&&"_type"in a)}function h(a){return g(a)&&"listItem"in a&&"string"==typeof a.listItem&&(typeof a.level>"u"||"number"==typeof a.level)}function i(a){return"@list"===a._type}function j(a){return"@span"===a._type}function k(a){return"@text"===a._type}a.s({LIST_NEST_MODE_DIRECT:()=>t,LIST_NEST_MODE_HTML:()=>s,buildMarksTree:()=>m,isPortableTextBlock:()=>g,isPortableTextListItemBlock:()=>h,isPortableTextSpan:()=>f,isPortableTextToolkitList:()=>i,isPortableTextToolkitSpan:()=>j,isPortableTextToolkitTextNode:()=>k,nestLists:()=>n,sortMarksByOccurences:()=>l,spanToPlainText:()=>function a(b){let c="";return b.children.forEach(b=>{k(b)?c+=b.text:j(b)&&(c+=a(b))}),c},toPlainText:()=>q});let b=["strong","em","code","underline","strike-through"];function l(a,c,d){if(!f(a)||!a.marks||!a.marks.length)return[];let e=a.marks.slice(),g={};return e.forEach(a=>{g[a]=1;for(let b=c+1;b<d.length;b++){let c=d[b];if(c&&f(c)&&Array.isArray(c.marks)&&-1!==c.marks.indexOf(a))g[a]++;else break}}),e.sort((a,c)=>(function(a,c,d){let e=a[c],f=a[d];if(e!==f)return f-e;let g=b.indexOf(c),h=b.indexOf(d);return g!==h?g-h:c.localeCompare(d)})(g,a,c))}function m(a){var b,c;let{children:d}=a,e=null!=(b=a.markDefs)?b:[];if(!d||!d.length)return[];let g=d.map(l),h={_type:"@span",children:[],markType:"<unknown>"},i=[h];for(let a=0;a<d.length;a++){let b=d[a];if(!b)continue;let h=g[a]||[],j=1;if(i.length>1)for(;j<i.length;j++){let a=(null==(c=i[j])?void 0:c.markKey)||"",b=h.indexOf(a);if(-1===b)break;h.splice(b,1)}let k=(i=i.slice(0,j))[i.length-1];if(k){for(let a of h){let c=null==e?void 0:e.find(b=>b._key===a),d=c?c._type:a,f={_type:"@span",_key:b._key,children:[],markDef:c,markType:d,markKey:a};k.children.push(f),i.push(f),k=f}if(f(b)){let a=b.text.split(`
`);for(let b=a.length;b-- >1;)a.splice(b,0,`
`);k.children=k.children.concat(a.map(a=>({_type:"@text",text:a})))}else k.children=k.children.concat(b)}}return h.children}function n(a,b){let c,d=[];for(let i=0;i<a.length;i++){let j=a[i];if(j){var f,g;if(!h(j)){d.push(j),c=void 0;continue}if(!c){c=o(j,i,b),d.push(c);continue}if(f=j,g=c,(f.level||1)===g.level&&f.listItem===g.listItem){c.children.push(j);continue}if((j.level||1)>c.level){let a=o(j,i,b);if("html"===b){let b=c.children[c.children.length-1],d=e(e({},b),{},{children:[...b.children,a]});c.children[c.children.length-1]=d}else c.children.push(a);c=a;continue}if((j.level||1)<c.level){let a=d[d.length-1],e=a&&p(a,j);if(e){(c=e).children.push(j);continue}c=o(j,i,b),d.push(c);continue}if(j.listItem!==c.listItem){let a=d[d.length-1],e=a&&p(a,{level:j.level||1});if(e&&e.listItem===j.listItem){(c=e).children.push(j);continue}c=o(j,i,b),d.push(c);continue}console.warn("Unknown state encountered for block",j),d.push(j)}}return d}function o(a,b,c){return{_type:"@list",_key:`${a._key||`${b}`}-parent`,mode:c,level:a.level||1,listItem:a.listItem,children:[a]}}function p(a,b){let c=b.level||1,d=b.listItem||"normal",e="string"==typeof b.listItem;if(i(a)&&(a.level||1)===c&&e&&(a.listItem||"normal")===d)return a;if(!("children"in a))return;let g=a.children[a.children.length-1];return g&&!f(g)?p(g,b):void 0}let c=/^\s/,r=/\s$/;function q(a){let b=Array.isArray(a)?a:[a],d="";return b.forEach((a,e)=>{if(!g(a))return;let h=!1;a.children.forEach(a=>{f(a)?(d+=h&&d&&!r.test(d)&&!c.test(a.text)?" ":"",d+=a.text,h=!1):h=!0}),e!==b.length-1&&(d+=`

`)}),d}let s="html",t="direct"}},117261:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PortableText:()=>m,defaultComponents:()=>y,mergeComponents:()=>k});var d=a.i(129629),e=a.i(998934),f=a.i(465421);let b=["block","list","listItem","marks","types"],c=["listItem"],p=["_key"];function g(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function h(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?g(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):g(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function i(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(b.includes(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],b.includes(c)||({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}let q={textDecoration:"underline"},r=(a,b)=>`[@portabletext/react] Unknown ${a}, specify a component for it in the \`components.${b}\` prop`,s=a=>r(`block type "${a}"`,"types"),t=a=>r(`mark type "${a}"`,"marks"),u=a=>r(`block style "${a}"`,"block"),v=a=>r(`list style "${a}"`,"list"),w=a=>r(`list item style "${a}"`,"listItem");function j(a){console.warn(a)}let x={display:"none"},y={types:{},block:{normal:({children:a})=>(0,d.jsx)("p",{children:a}),blockquote:({children:a})=>(0,d.jsx)("blockquote",{children:a}),h1:({children:a})=>(0,d.jsx)("h1",{children:a}),h2:({children:a})=>(0,d.jsx)("h2",{children:a}),h3:({children:a})=>(0,d.jsx)("h3",{children:a}),h4:({children:a})=>(0,d.jsx)("h4",{children:a}),h5:({children:a})=>(0,d.jsx)("h5",{children:a}),h6:({children:a})=>(0,d.jsx)("h6",{children:a})},marks:{em:({children:a})=>(0,d.jsx)("em",{children:a}),strong:({children:a})=>(0,d.jsx)("strong",{children:a}),code:({children:a})=>(0,d.jsx)("code",{children:a}),underline:({children:a})=>(0,d.jsx)("span",{style:q,children:a}),"strike-through":({children:a})=>(0,d.jsx)("del",{children:a}),link:({children:a,value:b})=>(0,d.jsx)("a",{href:b?.href,children:a})},list:{number:({children:a})=>(0,d.jsx)("ol",{children:a}),bullet:({children:a})=>(0,d.jsx)("ul",{children:a})},listItem:({children:a})=>(0,d.jsx)("li",{children:a}),hardBreak:()=>(0,d.jsx)("br",{}),unknownType:({value:a,isInline:b})=>{let c=s(a._type);return b?(0,d.jsx)("span",{style:x,children:c}):(0,d.jsx)("div",{style:x,children:c})},unknownMark:({markType:a,children:b})=>(0,d.jsx)("span",{className:`unknown__pt__mark__${a}`,children:b}),unknownList:({children:a})=>(0,d.jsx)("ul",{children:a}),unknownListItem:({children:a})=>(0,d.jsx)("li",{children:a}),unknownBlockStyle:({children:a})=>(0,d.jsx)("p",{children:a})};function k(a,c){let{block:d,list:e,listItem:f,marks:g,types:j}=c,k=i(c,b);return h(h({},a),{},{block:l(a,c,"block"),list:l(a,c,"list"),listItem:l(a,c,"listItem"),marks:l(a,c,"marks"),types:l(a,c,"types")},k)}function l(a,b,c){let d=b[c],e=a[c];return"function"==typeof d||d&&"function"==typeof e?d:d?h(h({},e),d):e}function m({value:a,components:b,listNestingMode:c,onMissingComponent:g=j}){let h=g||o,i=Array.isArray(a)?a:[a],l=(0,e.nestLists)(i,c||e.LIST_NEST_MODE_HTML),m=(0,f.useMemo)(()=>b?k(y,b):y,[b]),n=(0,f.useMemo)(()=>z(m,h),[m,h]),p=l.map((a,b)=>n({node:a,index:b,isInline:!1,renderNode:n}));return(0,d.jsx)(d.Fragment,{children:p})}let z=(a,b)=>function f(g){let{node:j,index:k,isInline:l}=g,m=j._key||`node-${k}`;return(0,e.isPortableTextToolkitList)(j)?function(c,e,g){let i=c.children.map((a,b)=>f({node:a._key?a:h(h({},a),{},{_key:`li-${e}-${b}`}),index:b,isInline:!1,renderNode:f})),j=a.list,k=("function"==typeof j?j:j[c.listItem])||a.unknownList;if(k===a.unknownList){let a=c.listItem||"bullet";b(v(a),{nodeType:"listStyle",type:a})}return(0,d.jsx)(k,{value:c,index:e,isInline:!1,renderNode:f,children:i},g)}(j,k,m):(0,e.isPortableTextListItemBlock)(j)?function(e,g,h){let j=n({node:e,index:g,isInline:!1,renderNode:f}),k=a.listItem,l=("function"==typeof k?k:k[e.listItem])||a.unknownListItem;if(l===a.unknownListItem){let a=e.listItem||"bullet";b(w(a),{type:a,nodeType:"listItemStyle"})}let m=j.children;if(e.style&&"normal"!==e.style){let{listItem:a}=e;m=f({node:i(e,c),index:g,isInline:!1,renderNode:f})}return(0,d.jsx)(l,{value:e,index:g,isInline:!1,renderNode:f,children:m},h)}(j,k,m):(0,e.isPortableTextToolkitSpan)(j)?function(c,g,h){let{markDef:i,markType:j,markKey:k}=c,l=a.marks[j]||a.unknownMark,m=c.children.map((a,b)=>f({node:a,index:b,isInline:!0,renderNode:f}));return l===a.unknownMark&&b(t(j),{nodeType:"mark",type:j}),(0,d.jsx)(l,{text:(0,e.spanToPlainText)(c),value:i,markType:j,markKey:k,renderNode:f,children:m},h)}(j,0,m):j._type in a.types?function(b,c,e,g){let i=a.types[b._type];return i?(0,d.jsx)(i,h({},{value:b,isInline:g,index:c,renderNode:f}),e):null}(j,k,m,l):(0,e.isPortableTextBlock)(j)?function(c,e,g,j){let k=n({node:c,index:e,isInline:j,renderNode:f}),{_key:l}=k,m=i(k,p),o=m.node.style||"normal",q=("function"==typeof a.block?a.block:a.block[o])||a.unknownBlockStyle;return q===a.unknownBlockStyle&&b(u(o),{nodeType:"blockStyle",type:o}),(0,d.jsx)(q,h(h({},m),{},{value:m.node,renderNode:f}),g)}(j,k,m,l):(0,e.isPortableTextToolkitTextNode)(j)?function(b,c){if(b.text===`
`){let b=a.hardBreak;return b?(0,d.jsx)(b,{},c):`
`}return b.text}(j,m):function(c,e,g,i){b(s(c._type),{nodeType:"block",type:c._type});let j=a.unknownType;return(0,d.jsx)(j,h({},{value:c,isInline:i,index:e,renderNode:f}),g)}(j,k,m,l)};function n(a){let{node:b,index:c,isInline:d,renderNode:f}=a,g=(0,e.buildMarksTree)(b).map((a,b)=>f({node:a,isInline:!0,index:b,renderNode:f}));return{_key:b._key||`block-${c}`,children:g,index:c,isInline:d,node:b}}function o(){}}},425099:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PortableTextRenderer:()=>i});var d=a.i(129629),e=a.i(117261),f=a.i(99492),g=a.i(331055),h=a.i(934041);let b={block:{h1:({children:a})=>(0,d.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-6 leading-tight",children:a}),h2:({children:a})=>(0,d.jsx)("h2",{className:"text-3xl font-bold text-slate-900 mb-5 mt-8 leading-tight",children:a}),h3:({children:a})=>(0,d.jsx)("h3",{className:"text-2xl font-semibold text-slate-900 mb-4 mt-6 leading-tight",children:a}),h4:({children:a})=>(0,d.jsx)("h4",{className:"text-xl font-semibold text-slate-900 mb-3 mt-5 leading-tight",children:a}),normal:({children:a})=>(0,d.jsx)("p",{className:"text-slate-700 mb-4 leading-relaxed text-base",children:a}),blockquote:({children:a})=>(0,d.jsx)("blockquote",{className:"border-l-4 border-indigo-500 pl-6 py-2 my-6 bg-indigo-50 rounded-r-lg",children:(0,d.jsx)("div",{className:"text-slate-700 italic text-lg leading-relaxed",children:a})})},list:{bullet:({children:a})=>(0,d.jsx)("ul",{className:"list-disc list-inside mb-4 space-y-2 text-slate-700 ml-4",children:a}),number:({children:a})=>(0,d.jsx)("ol",{className:"list-decimal list-inside mb-4 space-y-2 text-slate-700 ml-4",children:a})},listItem:{bullet:({children:a})=>(0,d.jsx)("li",{className:"leading-relaxed",children:a}),number:({children:a})=>(0,d.jsx)("li",{className:"leading-relaxed",children:a})},marks:{strong:({children:a})=>(0,d.jsx)("strong",{className:"font-semibold text-slate-900",children:a}),em:({children:a})=>(0,d.jsx)("em",{className:"italic text-slate-700",children:a}),code:({children:a})=>(0,d.jsx)("code",{className:"bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono",children:a}),link:({children:a,value:b})=>b?.href?.startsWith("http")?(0,d.jsx)("a",{href:b.href,target:"_blank",rel:"noopener noreferrer",className:"text-indigo-600 hover:text-indigo-800 underline transition-colors duration-200",children:a}):(0,d.jsx)(g.default,{href:b.href||"#",className:"text-indigo-600 hover:text-indigo-800 underline transition-colors duration-200",children:a})},types:{image:({value:a})=>a?.asset?(0,d.jsxs)("figure",{className:"my-8",children:[(0,d.jsx)("div",{className:"relative rounded-lg overflow-hidden shadow-lg",children:(0,d.jsx)(f.default,{src:(0,h.urlFor)(a).width(800).height(600).fit("max").auto("format").url(),alt:a.alt||a.caption||"Blog image",width:800,height:600,className:"w-full h-auto",loading:"lazy",placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="})}),a.caption&&(0,d.jsx)("figcaption",{className:"text-center text-sm text-slate-600 mt-3 italic",children:a.caption})]}):null,code:({value:a})=>(0,d.jsx)("div",{className:"my-6",children:(0,d.jsx)("pre",{className:"bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto",children:(0,d.jsx)("code",{className:"text-sm font-mono leading-relaxed",children:a.code})})}),callToAction:({value:a})=>(0,d.jsxs)("div",{className:"my-8 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold text-slate-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-slate-700 mb-4",children:a.description}),(0,d.jsx)(g.default,{href:a.buttonUrl||"#",className:"inline-block bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105",children:a.buttonText})]})}};function i({content:a,className:c=""}){return a&&Array.isArray(a)?(0,d.jsx)("div",{className:`prose prose-slate max-w-none ${c}`,children:(0,d.jsx)(e.PortableText,{value:a,components:b})}):null}}},397120:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],c=(0,d.default)("share-2",b)}},428436:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Share2:()=>d.default});var d=a.i(397120)}};

//# sourceMappingURL=_3d7bc49d._.js.map