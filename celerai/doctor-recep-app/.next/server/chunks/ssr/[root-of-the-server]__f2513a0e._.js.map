{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/inter_59dee874.js", "turbopack:///[project]/node_modules/@vercel/analytics/dist/next/index.mjs/proxy.mjs", "turbopack:///[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs/proxy.mjs", "turbopack:///[project]/node_modules/next/dist/client/script.js/proxy.cjs", "turbopack:///[project]/node_modules/next/script.js", "turbopack:///[project]/src/components/analytics/analytics-provider.tsx/proxy.mjs", "turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Analytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/analytics/dist/next/index.mjs <module evaluation>\",\n    \"Analytics\",\n);\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SpeedInsights = registerClientReference(\n    function() { throw new Error(\"Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs <module evaluation>\",\n    \"SpeedInsights\",\n);\n", "const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js <module evaluation>\"));\n", "module.exports = require('./dist/client/script')\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AnalyticsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnalyticsProvider() from the server but AnalyticsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/analytics-provider.tsx <module evaluation>\",\n    \"AnalyticsProvider\",\n);\n", "import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport { Analytics } from \"@vercel/analytics/next\";\nimport { SpeedInsights } from \"@vercel/speed-insights/next\";\nimport <PERSON>ript from \"next/script\";\nimport { AnalyticsProvider } from \"@/components/analytics/analytics-provider\";\nimport \"./globals.css\";\n\nconst inter = Inter({ subsets: [\"latin\"] });\n\nexport const metadata: Metadata = {\n  title: \"Celer AI\",\n  description: \"AI-powered patient consultation summary system for Indian doctors\",\n  keywords: \"doctor, patient, consultation, AI, summary, clinic, healthcare\",\n  authors: [{ name: \"Celer AI\" }],\n  manifest: \"/manifest.json\",\n  appleWebApp: {\n    capable: true,\n    statusBarStyle: \"default\",\n    title: \"Doctor Reception\",\n  },\n  icons: {\n    icon: [\n      { url: \"/icons/favicon-16x16.png\", sizes: \"16x16\", type: \"image/png\" },\n      { url: \"/icons/favicon-32x32.png\", sizes: \"32x32\", type: \"image/png\" },\n      { url: \"/icons/favicon-48x48.png\", sizes: \"48x48\", type: \"image/png\" },\n    ],\n    apple: [\n      { url: \"/icons/apple-touch-icon-180x180.png\", sizes: \"180x180\", type: \"image/png\" },\n    ],\n  },\n};\n\nexport const viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 1,\n  userScalable: false,\n  themeColor: '#2563eb',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        {/* Favicon */}\n        <link rel=\"icon\" href=\"/favicon.ico\" sizes=\"32x32\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/icons/favicon-16x16.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/icons/favicon-32x32.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"48x48\" href=\"/icons/favicon-48x48.png\" />\n\n        {/* Apple Touch Icons */}\n        <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/icons/apple-touch-icon-180x180.png\" />\n        <link rel=\"apple-touch-icon\" sizes=\"152x152\" href=\"/icons/apple-touch-icon-152x152.png\" />\n        <link rel=\"apple-touch-icon\" sizes=\"120x120\" href=\"/icons/apple-touch-icon-120x120.png\" />\n\n        {/* PWA Manifest */}\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n\n        {/* PWA Meta Tags */}\n        <meta name=\"theme-color\" content=\"#14b8a6\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"Celer AI\" />\n\n        {/* Microsoft Tiles */}\n        <meta name=\"msapplication-TileColor\" content=\"#14b8a6\" />\n        <meta name=\"msapplication-TileImage\" content=\"/icons/icon-144x144.png\" />\n\n        {/* GA4 Analytics - Lazy Loaded for Performance */}\n        <Script\n          src=\"https://www.googletagmanager.com/gtag/js?id=G-66GG02C5H5\"\n          strategy=\"lazyOnload\"\n        />\n        <Script id=\"google-analytics\" strategy=\"lazyOnload\">\n          {`\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', 'G-66GG02C5H5', {\n              page_title: document.title,\n              custom_map: {'custom_parameter_1': 'zone_type'}\n            });\n          `}\n        </Script>\n      </head>\n      <body className={`${inter.className} antialiased`}>\n        <AnalyticsProvider>\n          {children}\n        </AnalyticsProvider>\n        <Analytics />\n        <SpeedInsights />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "gDAAA,EAAA,CAAA,CAAA,CACA,UAAA,0CACA,yECFA,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,UAFO,CAEK,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,GADpB,IAC6B,CAAC,QAAQ,AAAR,QAGnB,WAHS,gECVjB,IAAM,EAAY,CAAA,EADzB,AACyB,EADzB,CAAA,CAAA,OACyB,uBAAsB,AAAtB,EACrB,EADqB,SACR,MAAM,AAAI,MAAM,gOAAkO,EAC/P,mFACA,sFAHG,IAAM,EAAY,CAAA,EADzB,AACyB,EADzB,CAAA,CAAA,OACyB,uBAAA,AAAsB,EAC3C,EADqB,SACR,MAAM,AAAI,MAAM,gOAAkO,EAC/P,+DACA,8KCHG,IAAM,EAAgB,CAAA,EAD7B,AAC6B,EAD7B,CAAA,CAAA,OAC6B,uBAAA,AAAsB,EAC/C,EADyB,SACZ,MAAM,AAAI,MAAM,wOAA0O,EACvQ,wFACA,8FAHG,IAAM,EAAgB,CAAA,EAAA,AAD7B,EAAA,CAAA,CAAA,OAC6B,uBAAA,AAAsB,EAC/C,EADyB,SACZ,MAAM,AAAI,MAAM,wOAA0O,EACvQ,oEACA,0JCJJ,GAAM,CAAE,yBAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,+HAFhD,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,+LCFhD,EAAO,OAAO,CAAA,EAAA,CAAA,CAAA,wFCCP,IAAM,EAAoB,CAAA,EAAA,AADjC,EAAA,CAAA,CAAA,OACiC,uBAAA,AAAsB,EACnD,EAD6B,SAChB,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,gFACA,qGAHG,IAAM,EAAoB,CAAA,EAAA,AADjC,EAAA,CAAA,CAAA,OACiC,uBAAA,AAAsB,EACnD,EAD6B,SAChB,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,4DACA,8OCFJ,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAKO,IAAM,EAAqB,CAChC,MAAO,WACP,YAAa,oEACb,SAAU,iEACV,QAAS,CAAC,CAAE,KAAM,UAAW,EAAE,CAC/B,SAAU,iBACV,YAAa,CACX,QAAS,GACT,eAAgB,UAChB,MAAO,kBACT,EACA,MAAO,CACL,KAAM,CACJ,CAAE,IAAK,2BAA4B,MAAO,QAAS,KAAM,WAAY,EACrE,CAAE,IAAK,2BAA4B,MAAO,QAAS,KAAM,WAAY,EACrE,CAAE,IAAK,2BAA4B,MAAO,QAAS,KAAM,WAAY,EACtE,CACD,MAAO,CACL,CAAE,IAAK,sCAAuC,MAAO,UAAW,KAAM,WAAY,EACnF,AACH,CACF,EAEa,EAAW,CACtB,MAAO,eACP,aAAc,EACd,aAAc,EACd,cAAc,EACd,WAAY,SACd,EAEe,SAAS,EAAW,CACjC,UAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,KAAK,eACT,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WAEC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,OAAO,KAAK,eAAe,MAAM,UAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,OAAO,KAAK,YAAY,MAAM,QAAQ,KAAK,6BACrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,OAAO,KAAK,YAAY,MAAM,QAAQ,KAAK,6BACrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,OAAO,KAAK,YAAY,MAAM,QAAQ,KAAK,6BAGrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,mBAAmB,MAAM,UAAU,KAAK,wCAClD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,mBAAmB,MAAM,UAAU,KAAK,wCAClD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,mBAAmB,MAAM,UAAU,KAAK,wCAGlD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,WAAW,KAAK,mBAG1B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cAAc,QAAQ,YACjC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,+BAA+B,QAAQ,QAClD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,wCAAwC,QAAQ,YAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,6BAA6B,QAAQ,aAGhD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,0BAA0B,QAAQ,YAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,0BAA0B,QAAQ,4BAG7C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CACL,IAAI,wBADL,mCAEC,SAAS,eAEX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAC,GAAG,mBAAmB,MAA7B,GAAsC,sBACpC,CAAC;;;;;;;;UAQF,CAAC,MAGL,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAW,CAAA,EAAG,EAAA,OAAK,CAAC,SAAS,CAAC,YAAY,CAAC,MAA7B,KAClB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,iBAAiB,CAAA,UACf,IAEH,CAAA,EAAA,EAHC,AAGD,GAAA,EAAC,EAAA,SAAS,CAAA,CAAA,GACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WADA,EACa,CAAA,CAAA,QAItB,cAJS", "ignoreList": [0, 1, 2, 3, 4, 5]}