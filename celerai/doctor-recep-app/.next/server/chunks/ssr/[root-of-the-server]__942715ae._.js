module.exports = {

"[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/client-s3", () => require("@aws-sdk/client-s3"));

module.exports = mod;
}}),
"[project]/src/lib/storage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STORAGE_CONFIG": (()=>STORAGE_CONFIG),
    "calculateTotalFileSize": (()=>calculateTotalFileSize),
    "deleteFile": (()=>deleteFile),
    "downloadFile": (()=>downloadFile),
    "extractFilePathFromUrl": (()=>extractFilePathFromUrl),
    "generateStoragePath": (()=>generateStoragePath),
    "uploadFile": (()=>uploadFile),
    "uploadMultipleFiles": (()=>uploadMultipleFiles),
    "validateFile": (()=>validateFile),
    "validateTotalSize": (()=>validateTotalSize)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
;
const STORAGE_CONFIG = {
    // R2 Configuration
    BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
    PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',
    // Folder prefixes (replaces separate buckets)
    AUDIO_PREFIX: 'consultation-audio',
    IMAGE_PREFIX: 'consultation-images',
    // File limits
    MAX_FILE_SIZE: 100 * 1024 * 1024,
    MAX_TOTAL_SIZE: 200 * 1024 * 1024,
    ALLOWED_AUDIO_TYPES: [
        'audio/webm',
        'audio/mp3',
        'audio/wav',
        'audio/m4a',
        'audio/mpeg',
        'audio/mp4',
        'audio/ogg'
    ],
    ALLOWED_IMAGE_TYPES: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/heic'
    ],
    RETENTION_DAYS: 30
};
// R2 Client configuration
const createR2Client = ()=>{
    return new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["S3Client"]({
        region: 'auto',
        endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,
        credentials: {
            accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',
            secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************'
        }
    });
};
function validateFile(file, type) {
    // Check file size
    if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
        return {
            valid: false,
            error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit`
        };
    }
    // Check file type
    const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES;
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            error: `File type ${file.type} is not allowed`
        };
    }
    return {
        valid: true
    };
}
function generateStoragePath(doctorId, consultationId, fileName, type) {
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX;
    return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`;
}
async function uploadFile(file, doctorId, consultationId, type) {
    try {
        // Validate file
        const validation = validateFile(file, type);
        if (!validation.valid) {
            return {
                success: false,
                error: validation.error
            };
        }
        const r2Client = createR2Client();
        const filePath = generateStoragePath(doctorId, consultationId, file.name, type);
        // Convert file to buffer
        const fileBuffer = await file.arrayBuffer();
        // Upload to R2 - preserve exact Content-Type from file
        const uploadCommand = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
            Bucket: STORAGE_CONFIG.BUCKET_NAME,
            Key: filePath,
            Body: new Uint8Array(fileBuffer),
            ContentType: file.type,
            CacheControl: 'public, max-age=3600'
        });
        await r2Client.send(uploadCommand);
        // Generate public URL
        const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`;
        return {
            success: true,
            url: publicUrl
        };
    } catch (error) {
        console.error('R2 upload error:', error);
        return {
            success: false,
            error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}
async function uploadMultipleFiles(files, doctorId, consultationId, type) {
    const results = await Promise.all(files.map((file)=>uploadFile(file, doctorId, consultationId, type)));
    const successful = results.filter((r)=>r.success);
    const failed = results.filter((r)=>!r.success);
    if (failed.length > 0) {
        return {
            success: false,
            errors: failed.map((f)=>f.error || 'Unknown error')
        };
    }
    return {
        success: true,
        urls: successful.map((s)=>s.url).filter(Boolean)
    };
}
async function deleteFile(filePath, _type) {
    try {
        const r2Client = createR2Client();
        const deleteCommand = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["DeleteObjectCommand"]({
            Bucket: STORAGE_CONFIG.BUCKET_NAME,
            Key: filePath
        });
        await r2Client.send(deleteCommand);
        return {
            success: true
        };
    } catch (error) {
        console.error('R2 delete error:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Delete failed'
        };
    }
}
function extractFilePathFromUrl(url, type) {
    try {
        const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX;
        const prefixPath = `/${prefix}/`;
        const index = url.indexOf(prefixPath);
        if (index === -1) return null;
        return url.substring(url.indexOf(prefix));
    } catch  {
        return null;
    }
}
async function downloadFile(filePath, _type) {
    try {
        // For public files, we can fetch directly from the custom domain
        const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`;
        const response = await fetch(publicUrl);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.blob();
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('R2 download error:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Download failed'
        };
    }
}
function calculateTotalFileSize(files) {
    return files.reduce((total, file)=>total + file.size, 0);
}
function validateTotalSize(files) {
    const totalSize = calculateTotalFileSize(files);
    if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {
        return {
            valid: false,
            error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`
        };
    }
    return {
        valid: true
    };
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__942715ae._.js.map