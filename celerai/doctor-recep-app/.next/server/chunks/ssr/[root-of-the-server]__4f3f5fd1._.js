module.exports={269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},137496:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSession:()=>i,decrypt:()=>h,deleteSession:()=>l,encrypt:()=>g,refreshSession:()=>k,updateSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify session"),null}}async function i(a){let b=new Date(Date.now()+6048e5),c=await g({userId:a,expiresAt:b}),d=await (0,f.cookies)();console.log("DEBUG: Creating session for user:",a),console.log("DEBUG: Session expires at:",b),d.set("session",c,{httpOnly:!0,secure:!1,expires:b,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("session")?.value,c=await h(b);if(console.log("DEBUG: Updating session - session exists:",!!b),console.log("DEBUG: Updating session - payload valid:",!!c),!b||!c)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let d=new Date(Date.now()+6048e5);a.set("session",b,{httpOnly:!0,secure:!1,expires:d,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function k(a){console.log("DEBUG: Refreshing session for user:",a),await l(),await i(a),console.log("DEBUG: Session refresh completed")}async function l(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting session cookie"),a.delete("session"),console.log("DEBUG: Session cookie deleted")}}},76803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkSession:()=>c,getDoctorQuota:()=>k,getUser:()=>i,getUserById:()=>j,verifySession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(137496),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId||(0,f.redirect)("/login"),{isAuth:!0,userId:c.userId}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId?{isAuth:!0,userId:c.userId}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("*").eq("id",a.userId).single();if(d)return console.error("Failed to fetch user:",d.message||d),null;if(!c)return null;return{...c,password_hash:c.password_hash}}catch(a){return console.error("Failed to fetch user:",a instanceof Error?a.message:a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",a).single();if(d)return console.error("Failed to fetch user by ID:",d.message||d),null;if(!c)return null;return{...c}}catch(a){return console.error("Failed to fetch user by ID:",a instanceof Error?a.message:a),null}}),k=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",a).single();if(d)return console.error("Failed to fetch quota:",d.message||d),null;let e=c.monthly_quota-c.quota_used,f=Math.round(c.quota_used/c.monthly_quota*100),g=new Date(c.quota_reset_at),i=Math.ceil((g.getTime()-Date.now())/864e5);return{monthly_quota:c.monthly_quota,quota_used:c.quota_used,quota_remaining:e,quota_percentage:f,quota_reset_at:c.quota_reset_at,days_until_reset:Math.max(0,i)}}catch(a){return console.error("Failed to fetch quota:",a instanceof Error?a.message:a),null}})}},81725:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},654129:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},870110:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({STORAGE_CONFIG:()=>b,calculateTotalFileSize:()=>l,deleteFile:()=>i,downloadFile:()=>k,extractFilePathFromUrl:()=>j,generateStoragePath:()=>f,uploadFile:()=>g,uploadMultipleFiles:()=>h,validateFile:()=>e,validateTotalSize:()=>m}),a.i(81725);var d=a.i(654129);let b={BUCKET_NAME:process.env.R2_BUCKET_NAME||"celerai-storage",PUBLIC_URL:process.env.R2_PUBLIC_URL||"https://celerai.tallyup.pro",AUDIO_PREFIX:"consultation-audio",IMAGE_PREFIX:"consultation-images",MAX_FILE_SIZE:0x6400000,MAX_TOTAL_SIZE:0xc800000,ALLOWED_AUDIO_TYPES:["audio/webm","audio/mp3","audio/wav","audio/m4a","audio/mpeg","audio/mp4","audio/ogg"],ALLOWED_IMAGE_TYPES:["image/jpeg","image/jpg","image/png","image/webp","image/heic"],RETENTION_DAYS:30},c=()=>new d.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID||"57014886c6cd87ebacf23a94e56a6e0c"}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID||"4dff08f96bf2f040b48bf3973813f7f0",secretAccessKey:process.env.R2_SECRET_ACCESS_KEY||"****************************************************************"}});function e(a,c){return a.size>b.MAX_FILE_SIZE?{valid:!1,error:`File size exceeds ${b.MAX_FILE_SIZE/1024/1024}MB limit`}:("audio"===c?b.ALLOWED_AUDIO_TYPES:b.ALLOWED_IMAGE_TYPES).includes(a.type)?{valid:!0}:{valid:!1,error:`File type ${a.type} is not allowed`}}function f(a,c,d,e){let f=d.replace(/[^a-zA-Z0-9.-]/g,"_"),g="audio"===e?b.AUDIO_PREFIX:b.IMAGE_PREFIX;return`${g}/${a}/${c}/${f}`}async function g(a,g,h,i){try{let j=e(a,i);if(!j.valid)return{success:!1,error:j.error};let k=c(),l=f(g,h,a.name,i),m=await a.arrayBuffer(),n=new d.PutObjectCommand({Bucket:b.BUCKET_NAME,Key:l,Body:new Uint8Array(m),ContentType:a.type,CacheControl:"public, max-age=3600"});await k.send(n);let o=`${b.PUBLIC_URL}/${l}`;return{success:!0,url:o}}catch(a){return console.error("R2 upload error:",a),{success:!1,error:`Upload failed: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(a,b,c,d){let e=await Promise.all(a.map(a=>g(a,b,c,d))),f=e.filter(a=>a.success),h=e.filter(a=>!a.success);return h.length>0?{success:!1,errors:h.map(a=>a.error||"Unknown error")}:{success:!0,urls:f.map(a=>a.url).filter(Boolean)}}async function i(a,e){try{let e=c(),f=new d.DeleteObjectCommand({Bucket:b.BUCKET_NAME,Key:a});return await e.send(f),{success:!0}}catch(a){return console.error("R2 delete error:",a),{success:!1,error:a instanceof Error?a.message:"Delete failed"}}}function j(a,c){try{let d="audio"===c?b.AUDIO_PREFIX:b.IMAGE_PREFIX,e=`/${d}/`,f=a.indexOf(e);if(-1===f)return null;return a.substring(a.indexOf(d))}catch{return null}}async function k(a,c){try{let c=`${b.PUBLIC_URL}/${a}`,d=await fetch(c);if(!d.ok)throw Error(`HTTP ${d.status}: ${d.statusText}`);let e=await d.blob();return{success:!0,data:e}}catch(a){return console.error("R2 download error:",a),{success:!1,error:a instanceof Error?a.message:"Download failed"}}}function l(a){return a.reduce((a,b)=>a+b.size,0)}function m(a){return l(a)>b.MAX_TOTAL_SIZE?{valid:!1,error:`Total file size exceeds ${b.MAX_TOTAL_SIZE/1024/1024}MB limit`}:{valid:!0}}}},179604:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(755284)},808945:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(755284),a.i(179604)},946955:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"7fc832fd06db7232f37f9b1b44631bd2956c9b940c":()=>d.createConsultationWithFiles});var d=a.i(755284);a.i(179604)},436340:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"7fc832fd06db7232f37f9b1b44631bd2956c9b940c":()=>d["7fc832fd06db7232f37f9b1b44631bd2956c9b940c"]}),a.i(808945);var d=a.i(946955)},249281:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MobileRecordingInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call MobileRecordingInterface() from the server but MobileRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/mobile/recording-interface.tsx <module evaluation>","MobileRecordingInterface")}},260978:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MobileRecordingInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call MobileRecordingInterface() from the server but MobileRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/mobile/recording-interface.tsx","MobileRecordingInterface")}},674671:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(249281);var d=a.i(260978);a.n(d)},226404:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>g,metadata:()=>b,viewport:()=>c});var d=a.i(129629),e=a.i(76803),f=a.i(674671);let b={title:"Doctor Mobile - Record Consultation",description:"Record patient consultations on mobile"},c={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1};async function g(){let a=await (0,e.getUser)();return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"px-4 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-lg font-semibold text-gray-900",children:["Dr. ",a?.name]}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a?.clinic_name?a.clinic_name:"Mobile Recording"})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Mobile Interface"})})]})})}),(0,d.jsx)("main",{className:"px-4 py-6",children:(0,d.jsx)(f.MobileRecordingInterface,{})})]})}}},382636:a=>{var{g:b,__dirname:c}=a;a.n(a.i(226404))},388333:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(382636),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["mobile",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/mobile/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/mobile/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/mobile/page",pathname:"/mobile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},842803:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(382636),a.i(660874),a.i(715847),a.i(781045),a.i(388333)},645826:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(388333)},590876:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(842803);var d=a.i(645826)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__76cf8bdc._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))},482970:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(870110)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__4f3f5fd1._.js.map