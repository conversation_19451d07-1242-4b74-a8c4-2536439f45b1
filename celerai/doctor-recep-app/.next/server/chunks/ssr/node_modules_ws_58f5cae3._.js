module.exports={837699:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let a=["nodebuffer","arraybuffer","fragments"],b="undefined"!=typeof Blob;b&&a.push("blob"),d.exports={BINARY_TYPES:a,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:b,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}}},981903:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{EMPTY_BUFFER:b}=a.r(837699),c=Buffer[Symbol.species];function f(a,b,c,d,e){for(let f=0;f<e;f++)c[d+f]=a[f]^b[3&f]}function g(a,b){for(let c=0;c<a.length;c++)a[c]^=b[3&c]}function h(a){let b;return(h.readOnly=!0,Buffer.isBuffer(a))?a:(a instanceof ArrayBuffer?b=new c(a):ArrayBuffer.isView(a)?b=new c(a.buffer,a.byteOffset,a.byteLength):(b=Buffer.from(a),h.readOnly=!1),b)}if(d.exports={concat:function(a,d){if(0===a.length)return b;if(1===a.length)return a[0];let e=Buffer.allocUnsafe(d),f=0;for(let b=0;b<a.length;b++){let c=a[b];e.set(c,f),f+=c.length}return f<d?new c(e.buffer,e.byteOffset,f):e},mask:f,toArrayBuffer:function(a){return a.length===a.buffer.byteLength?a.buffer:a.buffer.slice(a.byteOffset,a.byteOffset+a.length)},toBuffer:h,unmask:g},!process.env.WS_NO_BUFFER_UTIL)try{let a=(()=>{let a=Error("Cannot find module 'bufferutil'");throw a.code="MODULE_NOT_FOUND",a})();d.exports.mask=function(b,c,d,e,g){g<48?f(b,c,d,e,g):a.mask(b,c,d,e,g)},d.exports.unmask=function(b,c){b.length<32?g(b,c):a.unmask(b,c)}}catch(a){}}},933605:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let a=Symbol("kDone"),b=Symbol("kRun");d.exports=class{constructor(c){this[a]=()=>{this.pending--,this[b]()},this.concurrency=c||1/0,this.jobs=[],this.pending=0}add(a){this.jobs.push(a),this[b]()}[b](){if(this.pending!==this.concurrency&&this.jobs.length){let b=this.jobs.shift();this.pending++,b(this[a])}}}}},518751:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b,c=a.r(794045),e=a.r(981903),i=a.r(933605),{kStatusCode:j}=a.r(837699),k=Buffer[Symbol.species],l=Buffer.from([0,0,255,255]),m=Symbol("permessage-deflate"),n=Symbol("total-length"),o=Symbol("callback"),p=Symbol("buffers"),q=Symbol("error");function f(a){this[p].push(a),this[n]+=a.length}function g(a){if(this[n]+=a.length,this[m]._maxPayload<1||this[n]<=this[m]._maxPayload)return void this[p].push(a);this[q]=RangeError("Max payload size exceeded"),this[q].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[q][j]=1009,this.removeListener("data",g),this.reset()}function h(a){if(this[m]._inflate=null,this[q])return void this[o](this[q]);a[j]=1007,this[o](a)}d.exports=class{constructor(a,c,d){this._maxPayload=0|d,this._options=a||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!c,this._deflate=null,this._inflate=null,this.params=null,b||(b=new i(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let a={};return this._options.serverNoContextTakeover&&(a.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(a.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(a.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?a.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(a.client_max_window_bits=!0),a}accept(a){return a=this.normalizeParams(a),this.params=this._isServer?this.acceptAsServer(a):this.acceptAsClient(a),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let a=this._deflate[o];this._deflate.close(),this._deflate=null,a&&a(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(a){let b=this._options,c=a.find(a=>(!1!==b.serverNoContextTakeover||!a.server_no_context_takeover)&&(!a.server_max_window_bits||!1!==b.serverMaxWindowBits&&("number"!=typeof b.serverMaxWindowBits||!(b.serverMaxWindowBits>a.server_max_window_bits)))&&("number"!=typeof b.clientMaxWindowBits||!!a.client_max_window_bits));if(!c)throw Error("None of the extension offers can be accepted");return b.serverNoContextTakeover&&(c.server_no_context_takeover=!0),b.clientNoContextTakeover&&(c.client_no_context_takeover=!0),"number"==typeof b.serverMaxWindowBits&&(c.server_max_window_bits=b.serverMaxWindowBits),"number"==typeof b.clientMaxWindowBits?c.client_max_window_bits=b.clientMaxWindowBits:(!0===c.client_max_window_bits||!1===b.clientMaxWindowBits)&&delete c.client_max_window_bits,c}acceptAsClient(a){let b=a[0];if(!1===this._options.clientNoContextTakeover&&b.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(b.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&b.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(b.client_max_window_bits=this._options.clientMaxWindowBits);return b}normalizeParams(a){return a.forEach(a=>{Object.keys(a).forEach(b=>{let c=a[b];if(c.length>1)throw Error(`Parameter "${b}" must have only a single value`);if(c=c[0],"client_max_window_bits"===b){if(!0!==c){let a=+c;if(!Number.isInteger(a)||a<8||a>15)throw TypeError(`Invalid value for parameter "${b}": ${c}`);c=a}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${b}": ${c}`)}else if("server_max_window_bits"===b){let a=+c;if(!Number.isInteger(a)||a<8||a>15)throw TypeError(`Invalid value for parameter "${b}": ${c}`);c=a}else if("client_no_context_takeover"===b||"server_no_context_takeover"===b){if(!0!==c)throw TypeError(`Invalid value for parameter "${b}": ${c}`)}else throw Error(`Unknown parameter "${b}"`);a[b]=c})}),a}decompress(a,c,d){b.add(b=>{this._decompress(a,c,(a,c)=>{b(),d(a,c)})})}compress(a,c,d){b.add(b=>{this._compress(a,c,(a,c)=>{b(),d(a,c)})})}_decompress(a,b,d){let f=this._isServer?"client":"server";if(!this._inflate){let a=`${f}_max_window_bits`,b="number"!=typeof this.params[a]?c.Z_DEFAULT_WINDOWBITS:this.params[a];this._inflate=c.createInflateRaw({...this._options.zlibInflateOptions,windowBits:b}),this._inflate[m]=this,this._inflate[n]=0,this._inflate[p]=[],this._inflate.on("error",h),this._inflate.on("data",g)}this._inflate[o]=d,this._inflate.write(a),b&&this._inflate.write(l),this._inflate.flush(()=>{let a=this._inflate[q];if(a){this._inflate.close(),this._inflate=null,d(a);return}let c=e.concat(this._inflate[p],this._inflate[n]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[n]=0,this._inflate[p]=[],b&&this.params[`${f}_no_context_takeover`]&&this._inflate.reset()),d(null,c)})}_compress(a,b,d){let g=this._isServer?"server":"client";if(!this._deflate){let a=`${g}_max_window_bits`,b="number"!=typeof this.params[a]?c.Z_DEFAULT_WINDOWBITS:this.params[a];this._deflate=c.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:b}),this._deflate[n]=0,this._deflate[p]=[],this._deflate.on("data",f)}this._deflate[o]=d,this._deflate.write(a),this._deflate.flush(c.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let a=e.concat(this._deflate[p],this._deflate[n]);b&&(a=new k(a.buffer,a.byteOffset,a.length-4)),this._deflate[o]=null,this._deflate[n]=0,this._deflate[p]=[],b&&this.params[`${g}_no_context_takeover`]&&this._deflate.reset(),d(null,a)})}}}},475492:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{isUtf8:b}=a.r(963018),{hasBlob:c}=a.r(837699);function f(a){let b=a.length,c=0;for(;c<b;)if((128&a[c])==0)c++;else if((224&a[c])==192){if(c+1===b||(192&a[c+1])!=128||(254&a[c])==192)return!1;c+=2}else if((240&a[c])==224){if(c+2>=b||(192&a[c+1])!=128||(192&a[c+2])!=128||224===a[c]&&(224&a[c+1])==128||237===a[c]&&(224&a[c+1])==160)return!1;c+=3}else{if((248&a[c])!=240||c+3>=b||(192&a[c+1])!=128||(192&a[c+2])!=128||(192&a[c+3])!=128||240===a[c]&&(240&a[c+1])==128||244===a[c]&&a[c+1]>143||a[c]>244)return!1;c+=4}return!0}if(d.exports={isBlob:function(a){return c&&"object"==typeof a&&"function"==typeof a.arrayBuffer&&"string"==typeof a.type&&"function"==typeof a.stream&&("Blob"===a[Symbol.toStringTag]||"File"===a[Symbol.toStringTag])},isValidStatusCode:function(a){return a>=1e3&&a<=1014&&1004!==a&&1005!==a&&1006!==a||a>=3e3&&a<=4999},isValidUTF8:f,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},b)d.exports.isValidUTF8=function(a){return a.length<24?f(a):b(a)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let a=(()=>{let a=Error("Cannot find module 'utf-8-validate'");throw a.code="MODULE_NOT_FOUND",a})();d.exports.isValidUTF8=function(b){return b.length<32?f(b):a(b)}}catch(a){}}},981004:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{Writable:b}=a.r(109651),c=a.r(518751),{BINARY_TYPES:e,EMPTY_BUFFER:f,kStatusCode:g,kWebSocket:h}=a.r(837699),{concat:i,toArrayBuffer:j,unmask:k}=a.r(981903),{isValidStatusCode:l,isValidUTF8:m}=a.r(475492),n=Buffer[Symbol.species];d.exports=class extends b{constructor(a={}){super(),this._allowSynchronousEvents=void 0===a.allowSynchronousEvents||a.allowSynchronousEvents,this._binaryType=a.binaryType||e[0],this._extensions=a.extensions||{},this._isServer=!!a.isServer,this._maxPayload=0|a.maxPayload,this._skipUTF8Validation=!!a.skipUTF8Validation,this[h]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(a,b,c){if(8===this._opcode&&0==this._state)return c();this._bufferedBytes+=a.length,this._buffers.push(a),this.startLoop(c)}consume(a){if(this._bufferedBytes-=a,a===this._buffers[0].length)return this._buffers.shift();if(a<this._buffers[0].length){let b=this._buffers[0];return this._buffers[0]=new n(b.buffer,b.byteOffset+a,b.length-a),new n(b.buffer,b.byteOffset,a)}let b=Buffer.allocUnsafe(a);do{let c=this._buffers[0],d=b.length-a;a>=c.length?b.set(this._buffers.shift(),d):(b.set(new Uint8Array(c.buffer,c.byteOffset,a),d),this._buffers[0]=new n(c.buffer,c.byteOffset+a,c.length-a)),a-=c.length}while(a>0)return b}startLoop(a){this._loop=!0;do switch(this._state){case 0:this.getInfo(a);break;case 1:this.getPayloadLength16(a);break;case 2:this.getPayloadLength64(a);break;case 3:this.getMask();break;case 4:this.getData(a);break;case 5:case 6:this._loop=!1;return}while(this._loop)this._errored||a()}getInfo(a){if(this._bufferedBytes<2){this._loop=!1;return}let b=this.consume(2);if((48&b[0])!=0)return void a(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let d=(64&b[0])==64;if(d&&!this._extensions[c.extensionName])return void a(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&b[0])==128,this._opcode=15&b[0],this._payloadLength=127&b[1],0===this._opcode){if(d)return void a(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void a(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void a(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=d}else{if(!(this._opcode>7)||!(this._opcode<11))return void a(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void a(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(d)return void a(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void a(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&b[1])==128,this._isServer){if(!this._masked)return void a(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void a(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(a)}getPayloadLength16(a){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(a)}getPayloadLength64(a){if(this._bufferedBytes<8){this._loop=!1;return}let b=this.consume(8),c=b.readUInt32BE(0);if(c>2097151)return void a(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*c+b.readUInt32BE(4),this.haveLength(a)}haveLength(a){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void a(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(a){let b=f;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}b=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&k(b,this._mask)}if(this._opcode>7)return void this.controlMessage(b,a);if(this._compressed){this._state=5,this.decompress(b,a);return}b.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(b)),this.dataMessage(a)}decompress(a,b){this._extensions[c.extensionName].decompress(a,this._fin,(a,c)=>{if(a)return b(a);if(c.length){if(this._messageLength+=c.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void b(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(c)}this.dataMessage(b),0===this._state&&this.startLoop(b)})}dataMessage(a){if(!this._fin){this._state=0;return}let b=this._messageLength,c=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let d;d="nodebuffer"===this._binaryType?i(c,b):"arraybuffer"===this._binaryType?j(i(c,b)):"blob"===this._binaryType?new Blob(c):c,this._allowSynchronousEvents?(this.emit("message",d,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",d,!0),this._state=0,this.startLoop(a)}))}else{let d=i(c,b);if(!this._skipUTF8Validation&&!m(d))return void a(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",d,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",d,!1),this._state=0,this.startLoop(a)}))}}controlMessage(a,b){if(8===this._opcode){if(0===a.length)this._loop=!1,this.emit("conclude",1005,f),this.end();else{let c=a.readUInt16BE(0);if(!l(c))return void b(this.createError(RangeError,`invalid status code ${c}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let d=new n(a.buffer,a.byteOffset+2,a.length-2);if(!this._skipUTF8Validation&&!m(d))return void b(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",c,d),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",a),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",a),this._state=0,this.startLoop(b)}))}createError(a,b,c,d,e){this._loop=!1,this._errored=!0;let f=new a(c?`Invalid WebSocket frame: ${b}`:b);return Error.captureStackTrace(f,this.createError),f.code=e,f[g]=d,f}}}},231355:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b,{Duplex:c}=a.r(109651),{randomFillSync:e}=a.r(329295),h=a.r(518751),{EMPTY_BUFFER:i,kWebSocket:j,NOOP:k}=a.r(837699),{isBlob:l,isValidStatusCode:m}=a.r(475492),{mask:n,toBuffer:o}=a.r(981903),p=Symbol("kByteLength"),q=Buffer.alloc(4),r=8192;class s{constructor(a,b,c){this._extensions=b||{},c&&(this._generateMask=c,this._maskBuffer=Buffer.alloc(4)),this._socket=a,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=k,this[j]=void 0}static frame(a,c){let d,f,g=!1,h=2,i=!1;c.mask&&(d=c.maskBuffer||q,c.generateMask?c.generateMask(d):(8192===r&&(void 0===b&&(b=Buffer.alloc(8192)),e(b,0,8192),r=0),d[0]=b[r++],d[1]=b[r++],d[2]=b[r++],d[3]=b[r++]),i=(d[0]|d[1]|d[2]|d[3])==0,h=6),"string"==typeof a?f=(!c.mask||i)&&void 0!==c[p]?c[p]:(a=Buffer.from(a)).length:(f=a.length,g=c.mask&&c.readOnly&&!i);let j=f;f>=65536?(h+=8,j=127):f>125&&(h+=2,j=126);let k=Buffer.allocUnsafe(g?f+h:h);return(k[0]=c.fin?128|c.opcode:c.opcode,c.rsv1&&(k[0]|=64),k[1]=j,126===j?k.writeUInt16BE(f,2):127===j&&(k[2]=k[3]=0,k.writeUIntBE(f,4,6)),c.mask)?(k[1]|=128,k[h-4]=d[0],k[h-3]=d[1],k[h-2]=d[2],k[h-1]=d[3],i)?[k,a]:g?(n(a,d,k,h,f),[k]):(n(a,d,a,0,f),[k,a]):[k,a]}close(a,b,c,d){let e;if(void 0===a)e=i;else if("number"==typeof a&&m(a))if(void 0!==b&&b.length){let c=Buffer.byteLength(b);if(c>123)throw RangeError("The message must not be greater than 123 bytes");(e=Buffer.allocUnsafe(2+c)).writeUInt16BE(a,0),"string"==typeof b?e.write(b,2):e.set(b,2)}else(e=Buffer.allocUnsafe(2)).writeUInt16BE(a,0);else throw TypeError("First argument must be a valid error code number");let f={[p]:e.length,fin:!0,generateMask:this._generateMask,mask:c,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,e,!1,f,d]):this.sendFrame(s.frame(e,f),d)}ping(a,b,c){let d,e;if("string"==typeof a?(d=Buffer.byteLength(a),e=!1):l(a)?(d=a.size,e=!1):(d=(a=o(a)).length,e=o.readOnly),d>125)throw RangeError("The data size must not be greater than 125 bytes");let f={[p]:d,fin:!0,generateMask:this._generateMask,mask:b,maskBuffer:this._maskBuffer,opcode:9,readOnly:e,rsv1:!1};l(a)?0!==this._state?this.enqueue([this.getBlobData,a,!1,f,c]):this.getBlobData(a,!1,f,c):0!==this._state?this.enqueue([this.dispatch,a,!1,f,c]):this.sendFrame(s.frame(a,f),c)}pong(a,b,c){let d,e;if("string"==typeof a?(d=Buffer.byteLength(a),e=!1):l(a)?(d=a.size,e=!1):(d=(a=o(a)).length,e=o.readOnly),d>125)throw RangeError("The data size must not be greater than 125 bytes");let f={[p]:d,fin:!0,generateMask:this._generateMask,mask:b,maskBuffer:this._maskBuffer,opcode:10,readOnly:e,rsv1:!1};l(a)?0!==this._state?this.enqueue([this.getBlobData,a,!1,f,c]):this.getBlobData(a,!1,f,c):0!==this._state?this.enqueue([this.dispatch,a,!1,f,c]):this.sendFrame(s.frame(a,f),c)}send(a,b,c){let d,e,f=this._extensions[h.extensionName],g=b.binary?2:1,i=b.compress;"string"==typeof a?(d=Buffer.byteLength(a),e=!1):l(a)?(d=a.size,e=!1):(d=(a=o(a)).length,e=o.readOnly),this._firstFragment?(this._firstFragment=!1,i&&f&&f.params[f._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(i=d>=f._threshold),this._compress=i):(i=!1,g=0),b.fin&&(this._firstFragment=!0);let j={[p]:d,fin:b.fin,generateMask:this._generateMask,mask:b.mask,maskBuffer:this._maskBuffer,opcode:g,readOnly:e,rsv1:i};l(a)?0!==this._state?this.enqueue([this.getBlobData,a,this._compress,j,c]):this.getBlobData(a,this._compress,j,c):0!==this._state?this.enqueue([this.dispatch,a,this._compress,j,c]):this.dispatch(a,this._compress,j,c)}getBlobData(a,b,c,d){this._bufferedBytes+=c[p],this._state=2,a.arrayBuffer().then(a=>{if(this._socket.destroyed){let a=Error("The socket was closed while the blob was being read");process.nextTick(f,this,a,d);return}this._bufferedBytes-=c[p];let e=o(a);b?this.dispatch(e,b,c,d):(this._state=0,this.sendFrame(s.frame(e,c),d),this.dequeue())}).catch(a=>{process.nextTick(g,this,a,d)})}dispatch(a,b,c,d){if(!b)return void this.sendFrame(s.frame(a,c),d);let e=this._extensions[h.extensionName];this._bufferedBytes+=c[p],this._state=1,e.compress(a,c.fin,(a,b)=>{if(this._socket.destroyed)return void f(this,Error("The socket was closed while data was being compressed"),d);this._bufferedBytes-=c[p],this._state=0,c.readOnly=!1,this.sendFrame(s.frame(b,c),d),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let a=this._queue.shift();this._bufferedBytes-=a[3][p],Reflect.apply(a[0],this,a.slice(1))}}enqueue(a){this._bufferedBytes+=a[3][p],this._queue.push(a)}sendFrame(a,b){2===a.length?(this._socket.cork(),this._socket.write(a[0]),this._socket.write(a[1],b),this._socket.uncork()):this._socket.write(a[0],b)}}function f(a,b,c){"function"==typeof c&&c(b);for(let c=0;c<a._queue.length;c++){let d=a._queue[c],e=d[d.length-1];"function"==typeof e&&e(b)}}function g(a,b,c){f(a,b,c),a.onerror(b)}d.exports=s}},704519:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{kForOnEventAttribute:b,kListener:c}=a.r(837699),e=Symbol("kCode"),g=Symbol("kData"),h=Symbol("kError"),i=Symbol("kMessage"),j=Symbol("kReason"),k=Symbol("kTarget"),l=Symbol("kType"),m=Symbol("kWasClean");class n{constructor(a){this[k]=null,this[l]=a}get target(){return this[k]}get type(){return this[l]}}Object.defineProperty(n.prototype,"target",{enumerable:!0}),Object.defineProperty(n.prototype,"type",{enumerable:!0});class o extends n{constructor(a,b={}){super(a),this[e]=void 0===b.code?0:b.code,this[j]=void 0===b.reason?"":b.reason,this[m]=void 0!==b.wasClean&&b.wasClean}get code(){return this[e]}get reason(){return this[j]}get wasClean(){return this[m]}}Object.defineProperty(o.prototype,"code",{enumerable:!0}),Object.defineProperty(o.prototype,"reason",{enumerable:!0}),Object.defineProperty(o.prototype,"wasClean",{enumerable:!0});class p extends n{constructor(a,b={}){super(a),this[h]=void 0===b.error?null:b.error,this[i]=void 0===b.message?"":b.message}get error(){return this[h]}get message(){return this[i]}}Object.defineProperty(p.prototype,"error",{enumerable:!0}),Object.defineProperty(p.prototype,"message",{enumerable:!0});class q extends n{constructor(a,b={}){super(a),this[g]=void 0===b.data?null:b.data}get data(){return this[g]}}function f(a,b,c){"object"==typeof a&&a.handleEvent?a.handleEvent.call(a,c):a.call(b,c)}Object.defineProperty(q.prototype,"data",{enumerable:!0}),d.exports={CloseEvent:o,ErrorEvent:p,Event:n,EventTarget:{addEventListener(a,d,e={}){let g;for(let f of this.listeners(a))if(!e[b]&&f[c]===d&&!f[b])return;if("message"===a)g=function(a,b){let c=new q("message",{data:b?a:a.toString()});c[k]=this,f(d,this,c)};else if("close"===a)g=function(a,b){let c=new o("close",{code:a,reason:b.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});c[k]=this,f(d,this,c)};else if("error"===a)g=function(a){let b=new p("error",{error:a,message:a.message});b[k]=this,f(d,this,b)};else{if("open"!==a)return;g=function(){let a=new n("open");a[k]=this,f(d,this,a)}}g[b]=!!e[b],g[c]=d,e.once?this.once(a,g):this.on(a,g)},removeEventListener(a,d){for(let e of this.listeners(a))if(e[c]===d&&!e[b]){this.removeListener(a,e);break}}},MessageEvent:q}}},617867:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{tokenChars:b}=a.r(475492);function f(a,b,c){void 0===a[b]?a[b]=[c]:a[b].push(c)}d.exports={format:function(a){return Object.keys(a).map(b=>{let c=a[b];return Array.isArray(c)||(c=[c]),c.map(a=>[b].concat(Object.keys(a).map(b=>{let c=a[b];return Array.isArray(c)||(c=[c]),c.map(a=>!0===a?b:`${b}=${a}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(a){let c,d,e=Object.create(null),g=Object.create(null),h=!1,i=!1,j=!1,k=-1,l=-1,m=-1,n=0;for(;n<a.length;n++)if(l=a.charCodeAt(n),void 0===c)if(-1===m&&1===b[l])-1===k&&(k=n);else if(0!==n&&(32===l||9===l))-1===m&&-1!==k&&(m=n);else if(59===l||44===l){if(-1===k)throw SyntaxError(`Unexpected character at index ${n}`);-1===m&&(m=n);let b=a.slice(k,m);44===l?(f(e,b,g),g=Object.create(null)):c=b,k=m=-1}else throw SyntaxError(`Unexpected character at index ${n}`);else if(void 0===d)if(-1===m&&1===b[l])-1===k&&(k=n);else if(32===l||9===l)-1===m&&-1!==k&&(m=n);else if(59===l||44===l){if(-1===k)throw SyntaxError(`Unexpected character at index ${n}`);-1===m&&(m=n),f(g,a.slice(k,m),!0),44===l&&(f(e,c,g),g=Object.create(null),c=void 0),k=m=-1}else if(61===l&&-1!==k&&-1===m)d=a.slice(k,n),k=m=-1;else throw SyntaxError(`Unexpected character at index ${n}`);else if(i){if(1!==b[l])throw SyntaxError(`Unexpected character at index ${n}`);-1===k?k=n:h||(h=!0),i=!1}else if(j)if(1===b[l])-1===k&&(k=n);else if(34===l&&-1!==k)j=!1,m=n;else if(92===l)i=!0;else throw SyntaxError(`Unexpected character at index ${n}`);else if(34===l&&61===a.charCodeAt(n-1))j=!0;else if(-1===m&&1===b[l])-1===k&&(k=n);else if(-1!==k&&(32===l||9===l))-1===m&&(m=n);else if(59===l||44===l){if(-1===k)throw SyntaxError(`Unexpected character at index ${n}`);-1===m&&(m=n);let b=a.slice(k,m);h&&(b=b.replace(/\\/g,""),h=!1),f(g,d,b),44===l&&(f(e,c,g),g=Object.create(null),c=void 0),d=void 0,k=m=-1}else throw SyntaxError(`Unexpected character at index ${n}`);if(-1===k||j||32===l||9===l)throw SyntaxError("Unexpected end of input");-1===m&&(m=n);let o=a.slice(k,m);return void 0===c?f(e,o,g):(void 0===d?f(g,o,!0):h?f(g,d,o.replace(/\\/g,"")):f(g,d,o),f(e,c,g)),e}}}},300229:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b=a.r(420467),c=a.r(348388),e=a.r(62445),y=a.r(629918),z=a.r(655804),{randomBytes:A,createHash:B}=a.r(329295),{Duplex:C,Readable:D}=a.r(109651),{URL:E}=a.r(771485),F=a.r(518751),G=a.r(981004),H=a.r(231355),{isBlob:I}=a.r(475492),{BINARY_TYPES:J,EMPTY_BUFFER:K,GUID:L,kForOnEventAttribute:M,kListener:N,kStatusCode:O,kWebSocket:P,NOOP:Q}=a.r(837699),{EventTarget:{addEventListener:R,removeEventListener:S}}=a.r(704519),{format:T,parse:U}=a.r(617867),{toBuffer:V}=a.r(981903),W=Symbol("kAborted"),X=[8,13],Y=["CONNECTING","OPEN","CLOSING","CLOSED"],Z=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class $ extends b{constructor(a,b,d){super(),this._binaryType=J[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=K,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=$.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==a?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===b?b=[]:Array.isArray(b)||("object"==typeof b&&null!==b?(d=b,b=[]):b=[b]),function a(b,d,j,k){let l,m,n,o,p={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:X[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...k,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(b._autoPong=p.autoPong,!X.includes(p.protocolVersion))throw RangeError(`Unsupported protocol version: ${p.protocolVersion} (supported versions: ${X.join(", ")})`);if(d instanceof E)l=d;else try{l=new E(d)}catch(a){throw SyntaxError(`Invalid URL: ${d}`)}"http:"===l.protocol?l.protocol="ws:":"https:"===l.protocol&&(l.protocol="wss:"),b._url=l.href;let q="wss:"===l.protocol,r="ws+unix:"===l.protocol;if("ws:"===l.protocol||q||r?r&&!l.pathname?m="The URL's pathname is empty":l.hash&&(m="The URL contains a fragment identifier"):m='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',m){let a=SyntaxError(m);if(0!==b._redirects)return void f(b,a);throw a}let s=q?443:80,t=A(16).toString("base64"),u=q?c.request:e.request,v=new Set;if(p.createConnection=p.createConnection||(q?h:g),p.defaultPort=p.defaultPort||s,p.port=l.port||s,p.host=l.hostname.startsWith("[")?l.hostname.slice(1,-1):l.hostname,p.headers={...p.headers,"Sec-WebSocket-Version":p.protocolVersion,"Sec-WebSocket-Key":t,Connection:"Upgrade",Upgrade:"websocket"},p.path=l.pathname+l.search,p.timeout=p.handshakeTimeout,p.perMessageDeflate&&(n=new F(!0!==p.perMessageDeflate?p.perMessageDeflate:{},!1,p.maxPayload),p.headers["Sec-WebSocket-Extensions"]=T({[F.extensionName]:n.offer()})),j.length){for(let a of j){if("string"!=typeof a||!Z.test(a)||v.has(a))throw SyntaxError("An invalid or duplicated subprotocol was specified");v.add(a)}p.headers["Sec-WebSocket-Protocol"]=j.join(",")}if(p.origin&&(p.protocolVersion<13?p.headers["Sec-WebSocket-Origin"]=p.origin:p.headers.Origin=p.origin),(l.username||l.password)&&(p.auth=`${l.username}:${l.password}`),r){let a=p.path.split(":");p.socketPath=a[0],p.path=a[1]}if(p.followRedirects){if(0===b._redirects){b._originalIpc=r,b._originalSecure=q,b._originalHostOrSocketPath=r?p.socketPath:l.host;let a=k&&k.headers;if(k={...k,headers:{}},a)for(let[b,c]of Object.entries(a))k.headers[b.toLowerCase()]=c}else if(0===b.listenerCount("redirect")){let a=r?!!b._originalIpc&&p.socketPath===b._originalHostOrSocketPath:!b._originalIpc&&l.host===b._originalHostOrSocketPath;a&&(!b._originalSecure||q)||(delete p.headers.authorization,delete p.headers.cookie,a||delete p.headers.host,p.auth=void 0)}p.auth&&!k.headers.authorization&&(k.headers.authorization="Basic "+Buffer.from(p.auth).toString("base64")),o=b._req=u(p),b._redirects&&b.emit("redirect",b.url,o)}else o=b._req=u(p);p.timeout&&o.on("timeout",()=>{i(b,o,"Opening handshake has timed out")}),o.on("error",a=>{null===o||o[W]||(o=b._req=null,f(b,a))}),o.on("response",c=>{let e=c.headers.location,g=c.statusCode;if(e&&p.followRedirects&&g>=300&&g<400){let c;if(++b._redirects>p.maxRedirects)return void i(b,o,"Maximum redirects exceeded");o.abort();try{c=new E(e,d)}catch(a){f(b,SyntaxError(`Invalid URL: ${e}`));return}a(b,c,j,k)}else b.emit("unexpected-response",o,c)||i(b,o,`Unexpected server response: ${c.statusCode}`)}),o.on("upgrade",(a,c,d)=>{let e;if(b.emit("upgrade",a),b.readyState!==$.CONNECTING)return;o=b._req=null;let f=a.headers.upgrade;if(void 0===f||"websocket"!==f.toLowerCase())return void i(b,c,"Invalid Upgrade header");let g=B("sha1").update(t+L).digest("base64");if(a.headers["sec-websocket-accept"]!==g)return void i(b,c,"Invalid Sec-WebSocket-Accept header");let h=a.headers["sec-websocket-protocol"];if(void 0!==h?v.size?v.has(h)||(e="Server sent an invalid subprotocol"):e="Server sent a subprotocol but none was requested":v.size&&(e="Server sent no subprotocol"),e)return void i(b,c,e);h&&(b._protocol=h);let j=a.headers["sec-websocket-extensions"];if(void 0!==j){let a;if(!n)return void i(b,c,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{a=U(j)}catch(a){i(b,c,"Invalid Sec-WebSocket-Extensions header");return}let d=Object.keys(a);if(1!==d.length||d[0]!==F.extensionName)return void i(b,c,"Server indicated an extension that was not requested");try{n.accept(a[F.extensionName])}catch(a){i(b,c,"Invalid Sec-WebSocket-Extensions header");return}b._extensions[F.extensionName]=n}b.setSocket(c,d,{allowSynchronousEvents:p.allowSynchronousEvents,generateMask:p.generateMask,maxPayload:p.maxPayload,skipUTF8Validation:p.skipUTF8Validation})}),p.finishRequest?p.finishRequest(o,b):o.end()}(this,a,b,d)):(this._autoPong=d.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(a){J.includes(a)&&(this._binaryType=a,this._receiver&&(this._receiver._binaryType=a))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(a,b,c){let d=new G({allowSynchronousEvents:c.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:c.maxPayload,skipUTF8Validation:c.skipUTF8Validation}),e=new H(a,this._extensions,c.generateMask);this._receiver=d,this._sender=e,this._socket=a,d[P]=this,e[P]=this,a[P]=this,d.on("conclude",k),d.on("drain",l),d.on("error",m),d.on("message",o),d.on("ping",p),d.on("pong",q),e.onerror=s,a.setTimeout&&a.setTimeout(0),a.setNoDelay&&a.setNoDelay(),b.length>0&&a.unshift(b),a.on("close",u),a.on("data",v),a.on("end",w),a.on("error",x),this._readyState=$.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=$.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[F.extensionName]&&this._extensions[F.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=$.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(a,b){if(this.readyState!==$.CLOSED){if(this.readyState===$.CONNECTING)return void i(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===$.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=$.CLOSING,this._sender.close(a,b,!this._isServer,a=>{!a&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),t(this)}}pause(){this.readyState!==$.CONNECTING&&this.readyState!==$.CLOSED&&(this._paused=!0,this._socket.pause())}ping(a,b,c){if(this.readyState===$.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof a?(c=a,a=b=void 0):"function"==typeof b&&(c=b,b=void 0),"number"==typeof a&&(a=a.toString()),this.readyState!==$.OPEN)return void j(this,a,c);void 0===b&&(b=!this._isServer),this._sender.ping(a||K,b,c)}pong(a,b,c){if(this.readyState===$.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof a?(c=a,a=b=void 0):"function"==typeof b&&(c=b,b=void 0),"number"==typeof a&&(a=a.toString()),this.readyState!==$.OPEN)return void j(this,a,c);void 0===b&&(b=!this._isServer),this._sender.pong(a||K,b,c)}resume(){this.readyState!==$.CONNECTING&&this.readyState!==$.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(a,b,c){if(this.readyState===$.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof b&&(c=b,b={}),"number"==typeof a&&(a=a.toString()),this.readyState!==$.OPEN)return void j(this,a,c);let d={binary:"string"!=typeof a,mask:!this._isServer,compress:!0,fin:!0,...b};this._extensions[F.extensionName]||(d.compress=!1),this._sender.send(a||K,d,c)}terminate(){if(this.readyState!==$.CLOSED){if(this.readyState===$.CONNECTING)return void i(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=$.CLOSING,this._socket.destroy())}}}function f(a,b){a._readyState=$.CLOSING,a._errorEmitted=!0,a.emit("error",b),a.emitClose()}function g(a){return a.path=a.socketPath,y.connect(a)}function h(a){return a.path=void 0,a.servername||""===a.servername||(a.servername=y.isIP(a.host)?"":a.host),z.connect(a)}function i(a,b,c){a._readyState=$.CLOSING;let d=Error(c);Error.captureStackTrace(d,i),b.setHeader?(b[W]=!0,b.abort(),b.socket&&!b.socket.destroyed&&b.socket.destroy(),process.nextTick(f,a,d)):(b.destroy(d),b.once("error",a.emit.bind(a,"error")),b.once("close",a.emitClose.bind(a)))}function j(a,b,c){if(b){let c=I(b)?b.size:V(b).length;a._socket?a._sender._bufferedBytes+=c:a._bufferedAmount+=c}if(c){let b=Error(`WebSocket is not open: readyState ${a.readyState} (${Y[a.readyState]})`);process.nextTick(c,b)}}function k(a,b){let c=this[P];c._closeFrameReceived=!0,c._closeMessage=b,c._closeCode=a,void 0!==c._socket[P]&&(c._socket.removeListener("data",v),process.nextTick(r,c._socket),1005===a?c.close():c.close(a,b))}function l(){let a=this[P];a.isPaused||a._socket.resume()}function m(a){let b=this[P];void 0!==b._socket[P]&&(b._socket.removeListener("data",v),process.nextTick(r,b._socket),b.close(a[O])),b._errorEmitted||(b._errorEmitted=!0,b.emit("error",a))}function n(){this[P].emitClose()}function o(a,b){this[P].emit("message",a,b)}function p(a){let b=this[P];b._autoPong&&b.pong(a,!this._isServer,Q),b.emit("ping",a)}function q(a){this[P].emit("pong",a)}function r(a){a.resume()}function s(a){let b=this[P];b.readyState!==$.CLOSED&&(b.readyState===$.OPEN&&(b._readyState=$.CLOSING,t(b)),this._socket.end(),b._errorEmitted||(b._errorEmitted=!0,b.emit("error",a)))}function t(a){a._closeTimer=setTimeout(a._socket.destroy.bind(a._socket),3e4)}function u(){let a,b=this[P];this.removeListener("close",u),this.removeListener("data",v),this.removeListener("end",w),b._readyState=$.CLOSING,this._readableState.endEmitted||b._closeFrameReceived||b._receiver._writableState.errorEmitted||null===(a=b._socket.read())||b._receiver.write(a),b._receiver.end(),this[P]=void 0,clearTimeout(b._closeTimer),b._receiver._writableState.finished||b._receiver._writableState.errorEmitted?b.emitClose():(b._receiver.on("error",n),b._receiver.on("finish",n))}function v(a){this[P]._receiver.write(a)||this.pause()}function w(){let a=this[P];a._readyState=$.CLOSING,a._receiver.end(),this.end()}function x(){let a=this[P];this.removeListener("error",x),this.on("error",Q),a&&(a._readyState=$.CLOSING,this.destroy())}Object.defineProperty($,"CONNECTING",{enumerable:!0,value:Y.indexOf("CONNECTING")}),Object.defineProperty($.prototype,"CONNECTING",{enumerable:!0,value:Y.indexOf("CONNECTING")}),Object.defineProperty($,"OPEN",{enumerable:!0,value:Y.indexOf("OPEN")}),Object.defineProperty($.prototype,"OPEN",{enumerable:!0,value:Y.indexOf("OPEN")}),Object.defineProperty($,"CLOSING",{enumerable:!0,value:Y.indexOf("CLOSING")}),Object.defineProperty($.prototype,"CLOSING",{enumerable:!0,value:Y.indexOf("CLOSING")}),Object.defineProperty($,"CLOSED",{enumerable:!0,value:Y.indexOf("CLOSED")}),Object.defineProperty($.prototype,"CLOSED",{enumerable:!0,value:Y.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(a=>{Object.defineProperty($.prototype,a,{enumerable:!0})}),["open","error","close","message"].forEach(a=>{Object.defineProperty($.prototype,`on${a}`,{enumerable:!0,get(){for(let b of this.listeners(a))if(b[M])return b[N];return null},set(b){for(let b of this.listeners(a))if(b[M]){this.removeListener(a,b);break}"function"==typeof b&&this.addEventListener(a,b,{[M]:!0})}})}),$.prototype.addEventListener=R,$.prototype.removeEventListener=S,d.exports=$}},898384:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";a.r(300229);let{Duplex:b}=a.r(109651);function f(a){a.emit("close")}function g(){!this.destroyed&&this._writableState.finished&&this.destroy()}function h(a){this.removeListener("error",h),this.destroy(),0===this.listenerCount("error")&&this.emit("error",a)}d.exports=function(a,c){let d=!0,e=new b({...c,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return a.on("message",function(b,c){let d=!c&&e._readableState.objectMode?b.toString():b;e.push(d)||a.pause()}),a.once("error",function(a){e.destroyed||(d=!1,e.destroy(a))}),a.once("close",function(){e.destroyed||e.push(null)}),e._destroy=function(b,c){if(a.readyState===a.CLOSED){c(b),process.nextTick(f,e);return}let g=!1;a.once("error",function(a){g=!0,c(a)}),a.once("close",function(){g||c(b),process.nextTick(f,e)}),d&&a.terminate()},e._final=function(b){if(a.readyState===a.CONNECTING)return void a.once("open",function(){e._final(b)});null!==a._socket&&(a._socket._writableState.finished?(b(),e._readableState.endEmitted&&e.destroy()):(a._socket.once("finish",function(){b()}),a.close()))},e._read=function(){a.isPaused&&a.resume()},e._write=function(b,c,d){if(a.readyState===a.CONNECTING)return void a.once("open",function(){e._write(b,c,d)});a.send(b,d)},e.on("end",g),e.on("error",h),e}}},254408:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let{tokenChars:b}=a.r(475492);d.exports={parse:function(a){let c=new Set,d=-1,e=-1,f=0;for(;f<a.length;f++){let g=a.charCodeAt(f);if(-1===e&&1===b[g])-1===d&&(d=f);else if(0!==f&&(32===g||9===g))-1===e&&-1!==d&&(e=f);else if(44===g){if(-1===d)throw SyntaxError(`Unexpected character at index ${f}`);-1===e&&(e=f);let b=a.slice(d,e);if(c.has(b))throw SyntaxError(`The "${b}" subprotocol is duplicated`);c.add(b),d=e=-1}else throw SyntaxError(`Unexpected character at index ${f}`)}if(-1===d||-1!==e)throw SyntaxError("Unexpected end of input");let g=a.slice(d,f);if(c.has(g))throw SyntaxError(`The "${g}" subprotocol is duplicated`);return c.add(g),c}}}},74625:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b=a.r(420467),c=a.r(62445),{Duplex:e}=a.r(109651),{createHash:j}=a.r(329295),k=a.r(617867),l=a.r(518751),m=a.r(254408),n=a.r(300229),{GUID:o,kWebSocket:p}=a.r(837699),q=/^[+/0-9A-Za-z]{22}==$/;function f(a){a._state=2,a.emit("close")}function g(){this.destroy()}function h(a,b,d,e){d=d||c.STATUS_CODES[b],e={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(d),...e},a.once("finish",a.destroy),a.end(`HTTP/1.1 ${b} ${c.STATUS_CODES[b]}\r
`+Object.keys(e).map(a=>`${a}: ${e[a]}`).join("\r\n")+"\r\n\r\n"+d)}function i(a,b,c,d,e){if(a.listenerCount("wsClientError")){let d=Error(e);Error.captureStackTrace(d,i),a.emit("wsClientError",d,c,b)}else h(c,d,e)}d.exports=class extends b{constructor(a,b){if(super(),null==(a={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:n,...a}).port&&!a.server&&!a.noServer||null!=a.port&&(a.server||a.noServer)||a.server&&a.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=a.port?(this._server=c.createServer((a,b)=>{let d=c.STATUS_CODES[426];b.writeHead(426,{"Content-Length":d.length,"Content-Type":"text/plain"}),b.end(d)}),this._server.listen(a.port,a.host,a.backlog,b)):a.server&&(this._server=a.server),this._server){let a=this.emit.bind(this,"connection");this._removeListeners=function(a,b){for(let c of Object.keys(b))a.on(c,b[c]);return function(){for(let c of Object.keys(b))a.removeListener(c,b[c])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(b,c,d)=>{this.handleUpgrade(b,c,d,a)}})}!0===a.perMessageDeflate&&(a.perMessageDeflate={}),a.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=a,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(a){if(2===this._state){a&&this.once("close",()=>{a(Error("The server is not running"))}),process.nextTick(f,this);return}if(a&&this.once("close",a),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(f,this);else{let a=this._server;this._removeListeners(),this._removeListeners=this._server=null,a.close(()=>{f(this)})}}shouldHandle(a){if(this.options.path){let b=a.url.indexOf("?");if((-1!==b?a.url.slice(0,b):a.url)!==this.options.path)return!1}return!0}handleUpgrade(a,b,c,d){b.on("error",g);let e=a.headers["sec-websocket-key"],f=a.headers.upgrade,j=+a.headers["sec-websocket-version"];if("GET"!==a.method)return void i(this,a,b,405,"Invalid HTTP method");if(void 0===f||"websocket"!==f.toLowerCase())return void i(this,a,b,400,"Invalid Upgrade header");if(void 0===e||!q.test(e))return void i(this,a,b,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==j&&13!==j)return void i(this,a,b,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(a))return void h(b,400);let n=a.headers["sec-websocket-protocol"],o=new Set;if(void 0!==n)try{o=m.parse(n)}catch(c){i(this,a,b,400,"Invalid Sec-WebSocket-Protocol header");return}let p=a.headers["sec-websocket-extensions"],r={};if(this.options.perMessageDeflate&&void 0!==p){let c=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let a=k.parse(p);a[l.extensionName]&&(c.accept(a[l.extensionName]),r[l.extensionName]=c)}catch(c){i(this,a,b,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let f={origin:a.headers[`${8===j?"sec-websocket-origin":"origin"}`],secure:!!(a.socket.authorized||a.socket.encrypted),req:a};if(2===this.options.verifyClient.length)return void this.options.verifyClient(f,(f,g,i,j)=>{if(!f)return h(b,g||401,i,j);this.completeUpgrade(r,e,o,a,b,c,d)});if(!this.options.verifyClient(f))return h(b,401)}this.completeUpgrade(r,e,o,a,b,c,d)}completeUpgrade(a,b,c,d,e,i,m){if(!e.readable||!e.writable)return e.destroy();if(e[p])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return h(e,503);let n=j("sha1").update(b+o).digest("base64"),q=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${n}`],r=new this.options.WebSocket(null,void 0,this.options);if(c.size){let a=this.options.handleProtocols?this.options.handleProtocols(c,d):c.values().next().value;a&&(q.push(`Sec-WebSocket-Protocol: ${a}`),r._protocol=a)}if(a[l.extensionName]){let b=a[l.extensionName].params,c=k.format({[l.extensionName]:[b]});q.push(`Sec-WebSocket-Extensions: ${c}`),r._extensions=a}this.emit("headers",q,d),e.write(q.concat("\r\n").join("\r\n")),e.removeListener("error",g),r.setSocket(e,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(r),r.on("close",()=>{this.clients.delete(r),this._shouldEmitClose&&!this.clients.size&&process.nextTick(f,this)})),m(r,d)}}}},23766:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b}),a.i(898384),a.i(981004),a.i(231355);var d=a.i(300229);a.i(74625);let b=d.default}},817809:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(898384),a.i(981004),a.i(231355),a.i(300229),a.i(74625),a.i(23766)},985782:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Receiver:()=>e.default,Sender:()=>f.default,WebSocket:()=>g.default,WebSocketServer:()=>h.default,createWebSocketStream:()=>d.default,default:()=>i.default});var d=a.i(898384),e=a.i(981004),f=a.i(231355),g=a.i(300229),h=a.i(74625),i=a.i(23766)},628329:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Receiver:()=>d.Receiver,Sender:()=>d.Sender,WebSocket:()=>d.WebSocket,WebSocketServer:()=>d.WebSocketServer,createWebSocketStream:()=>d.createWebSocketStream,default:()=>d.default}),a.i(817809);var d=a.i(985782)}};

//# sourceMappingURL=node_modules_ws_58f5cae3._.js.map