module.exports = {

"[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@sanity_client_dist__chunks-es_stegaEncodeSourceMap_4241083d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@sanity/eventsource/node.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__eca8fedb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@sanity/eventsource/node.js [app-rsc] (ecmascript)");
    });
});
}}),

};