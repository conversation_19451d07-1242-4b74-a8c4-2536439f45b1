module.exports={906079:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return f}});let b=a.r(129629),c=a.r(641093);function f(){return(0,b.jsx)(c.HTTPAccessErrorFallback,{status:401,message:"You're not authorized to access this page."})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}}};

//# sourceMappingURL=node_modules_next_dist_client_components_unauthorized-error_c8949b27.js.map