module.exports={268395:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],c=(0,d.default)("mic",b)}},597586:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Mic:()=>d.default});var d=a.i(268395)},531842:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]],c=(0,d.default)("file-check",b)}},190684:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({FileCheck:()=>d.default});var d=a.i(531842)},781397:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],c=(0,d.default)("chevron-down",b)}},4947:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ChevronDown:()=>d.default});var d=a.i(781397)},242930:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]],c=(0,d.default)("timer",b)}},643415:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Timer:()=>d.default});var d=a.i(242930)},288332:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]],c=(0,d.default)("headphones",b)}},640840:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Headphones:()=>d.default});var d=a.i(288332)},610056:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>s});var d=a.i(129629),e=a.i(331055),f=a.i(99492),g=a.i(597586),h=a.i(391745),i=a.i(293499),j=a.i(954124),k=a.i(256975),l=a.i(190684),m=a.i(533023),n=a.i(4947),o=a.i(643415),p=a.i(640840),q=a.i(76803);a.i(622427);var r=a.i(766719);async function s(){return await (0,q.checkSession)()&&(0,r.redirect)("/dashboard"),(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden",children:[(0,d.jsx)("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(f.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold",children:"Celer AI"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(e.default,{href:"/blog",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Blog"}),(0,d.jsx)(e.default,{href:"/guide",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Guides"}),(0,d.jsx)(e.default,{href:"/login",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Sign In"}),(0,d.jsx)(e.default,{href:"/signup",className:"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200",children:"Start Free"})]})]})}),(0,d.jsxs)("main",{className:"relative",children:[(0,d.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000"})]}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-6 pt-32 pb-16",children:(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]",children:[(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2",children:[(0,d.jsx)(j.Sparkles,{className:"w-4 h-4 text-indigo-600 animate-pulse"}),(0,d.jsx)("span",{className:"text-indigo-700 text-sm font-medium",children:"AI Magic for Medical Docs"}),(0,d.jsx)(k.Wand2,{className:"w-4 h-4 text-purple-600"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h1",{className:"text-6xl md:text-7xl font-black text-slate-900 leading-none",children:["Speak.",(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse",children:"Generate."}),(0,d.jsx)("span",{className:"block text-slate-700",children:"Done."})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 text-lg text-slate-600",children:[(0,d.jsx)(o.Timer,{className:"w-5 h-5 text-emerald-500"}),(0,d.jsx)("span",{children:"Medical reports in 30 seconds"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-ping"})]})]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-slate-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(g.Mic,{className:"w-6 h-6 text-white animate-pulse"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-slate-800",children:"Try saying:"}),(0,d.jsx)("p",{className:"text-slate-600 italic",children:"“Patient has fever, cough, prescribed antibiotics...”"})]})]}),(0,d.jsx)(h.ArrowRight,{className:"w-6 h-6 text-slate-400"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)(e.default,{href:"/signup",className:"group relative bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2",children:[(0,d.jsx)(m.Zap,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:"Start Creating Magic"}),(0,d.jsx)(h.ArrowRight,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6 pt-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-emerald-600",children:"30s"}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:"Average Time"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"90%"}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:"Accuracy"})]})]})]}),(0,d.jsxs)("div",{className:"relative -mt-35",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute -inset-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-30 animate-pulse"}),(0,d.jsx)("div",{className:"relative bg-white rounded-2xl shadow-2xl overflow-hidden",children:(0,d.jsx)("div",{style:{position:"relative",paddingBottom:"52.708333333333336%",height:0},children:(0,d.jsx)("iframe",{src:"https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7",className:"absolute top-0 left-0 w-full h-full border-0",allowFullScreen:!0,title:"Celer AI Demo"})})})]}),(0,d.jsx)("div",{className:"absolute -top-6 -left-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(p.Headphones,{className:"w-4 h-4 text-indigo-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-slate-700",children:"Voice Input"})]})}),(0,d.jsx)("div",{className:"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(l.FileCheck,{className:"w-4 h-4 text-emerald-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-slate-700",children:"Perfect Report"})]})})]})]})}),(0,d.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",children:(0,d.jsx)(n.ChevronDown,{className:"w-6 h-6 text-slate-400"})})]}),(0,d.jsxs)("section",{className:"py-32 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-slate-900"}),(0,d.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-200/20 to-cyan-200/20 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-1000"}),(0,d.jsx)("div",{className:"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-cyan-200/10 to-blue-200/10 rounded-full blur-xl animate-pulse delay-2000"})]}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto px-6",children:(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]",children:[(0,d.jsxs)("div",{className:"space-y-8 text-white",children:[(0,d.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-50/10 to-cyan-50/10 border border-emerald-200/20 rounded-full px-4 py-2",children:[(0,d.jsx)(i.Clock,{className:"w-4 h-4 text-emerald-400 animate-spin"}),(0,d.jsx)("span",{className:"text-emerald-300 text-sm font-medium",children:"Reclaim Your Time"}),(0,d.jsx)(o.Timer,{className:"w-4 h-4 text-cyan-400"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h2",{className:"text-6xl md:text-7xl font-black text-white leading-none",children:["Get Your",(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 via-cyan-400 to-purple-400 animate-pulse",children:"Life Back"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 text-lg text-slate-300",children:[(0,d.jsx)(o.Timer,{className:"w-5 h-5 text-emerald-400"}),(0,d.jsx)("span",{children:"Save 2+ hours daily with voice documentation"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-ping"})]})]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-slate-50/5 to-emerald-50/5 rounded-2xl p-6 border border-white/10",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-red-400 text-4xl font-black mb-2",children:"15-20"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm",children:"minutes per patient"}),(0,d.jsx)("div",{className:"text-red-400 text-xs mt-2",children:"😰 Stress & paperwork"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-emerald-400 text-4xl font-black mb-2",children:"30"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm",children:"seconds total"}),(0,d.jsx)("div",{className:"text-emerald-400 text-xs mt-2",children:"😌 Perfect & effortless"})]})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)(e.default,{href:"/signup",className:"group relative bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2",children:[(0,d.jsx)(o.Timer,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:"Start Saving Time Now"}),(0,d.jsx)(h.ArrowRight,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6 pt-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-emerald-400",children:"2+ hrs"}),(0,d.jsx)("div",{className:"text-sm text-slate-300",children:"Daily Savings"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-cyan-400",children:"90%"}),(0,d.jsx)("div",{className:"text-sm text-slate-300",children:"Accuracy"})]})]})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute -inset-4 bg-gradient-to-r from-emerald-500 via-cyan-500 to-purple-500 rounded-3xl blur-lg opacity-30 animate-pulse"}),(0,d.jsx)("div",{className:"relative bg-white/10 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-white/20",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-white mb-8 text-center",children:"What will you do with 2 extra hours daily?"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors",children:[(0,d.jsx)("div",{className:"text-4xl mb-3",children:"👨‍👩‍👧‍👦"}),(0,d.jsx)("div",{className:"text-white font-medium",children:"Family Time"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm mt-1",children:"Quality moments"})]}),(0,d.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors",children:[(0,d.jsx)("div",{className:"text-4xl mb-3",children:"🏃‍♂️"}),(0,d.jsx)("div",{className:"text-white font-medium",children:"Exercise"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm mt-1",children:"Stay healthy"})]}),(0,d.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors",children:[(0,d.jsx)("div",{className:"text-4xl mb-3",children:"📚"}),(0,d.jsx)("div",{className:"text-white font-medium",children:"Learning"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm mt-1",children:"Grow skills"})]}),(0,d.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors",children:[(0,d.jsx)("div",{className:"text-4xl mb-3",children:"😴"}),(0,d.jsx)("div",{className:"text-white font-medium",children:"Rest"}),(0,d.jsx)("div",{className:"text-slate-300 text-sm mt-1",children:"Recharge"})]})]})]})})]}),(0,d.jsx)("div",{className:"absolute -top-6 -left-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.Clock,{className:"w-4 h-4 text-emerald-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-white",children:"2+ Hours Saved"})]})}),(0,d.jsx)("div",{className:"absolute -bottom-6 -right-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(o.Timer,{className:"w-4 h-4 text-cyan-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-white",children:"Every Day"})]})})]})]})})]}),(0,d.jsx)("footer",{className:"bg-slate-50 border-t border-slate-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4 md:mb-0",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(f.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),(0,d.jsx)("span",{className:"font-semibold text-slate-800",children:"Celer AI"}),(0,d.jsx)("span",{className:"text-slate-500 text-sm",children:"• Built for Indian doctors"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-slate-500",children:[(0,d.jsx)(e.default,{href:"/privacy",className:"hover:text-slate-700 transition-colors",children:"Privacy"}),(0,d.jsx)(e.default,{href:"/terms",className:"hover:text-slate-700 transition-colors",children:"Terms"}),(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-slate-700 transition-colors",children:"Support"})]})]})})})]})}}};

//# sourceMappingURL=_d9a19434._.js.map