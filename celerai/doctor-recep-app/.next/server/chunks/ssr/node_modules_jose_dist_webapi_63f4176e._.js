module.exports={880446:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({concat:()=>d,decoder:()=>c,encoder:()=>b,uint32be:()=>g,uint64be:()=>f});let b=new TextEncoder,c=new TextDecoder;function d(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;for(let d of a)b.set(d,c),c+=d.length;return b}function e(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function f(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return e(c,b,0),e(c,a%0x100000000,4),c}function g(a){let b=new Uint8Array(4);return e(b,a),b}}},255665:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){if(Uint8Array.prototype.toBase64)return a.toBase64();let b=[];for(let c=0;c<a.length;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join(""))}function e(a){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(a);let b=atob(a),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}a.s({decodeBase64:()=>e,encodeBase64:()=>d})},154658:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({decode:()=>f,encode:()=>g});var d=a.i(880446),e=a.i(255665);function f(a){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof a?a:d.decoder.decode(a),{alphabet:"base64url"});let b=a;b instanceof Uint8Array&&(b=d.decoder.decode(b)),b=b.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return(0,e.decodeBase64)(b)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function g(a){let b=a;return("string"==typeof b&&(b=d.encoder.encode(b)),Uint8Array.prototype.toBase64)?b.toBase64({alphabet:"base64url",omitPadding:!0}):(0,e.encodeBase64)(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}},716168:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({JOSEAlgNotAllowed:()=>e,JOSEError:()=>b,JOSENotSupported:()=>f,JWEDecryptionFailed:()=>g,JWEInvalid:()=>h,JWKInvalid:()=>k,JWKSInvalid:()=>l,JWKSMultipleMatchingKeys:()=>n,JWKSNoMatchingKey:()=>m,JWKSTimeout:()=>o,JWSInvalid:()=>i,JWSSignatureVerificationFailed:()=>p,JWTClaimValidationFailed:()=>c,JWTExpired:()=>d,JWTInvalid:()=>j});class b extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(a,b){super(a,b),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class c extends b{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class d extends b{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class e extends b{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class f extends b{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class g extends b{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(a="decryption operation failed",b){super(a,b)}}class h extends b{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class i extends b{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class j extends b{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class k extends b{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class l extends b{static code="ERR_JWKS_INVALID";code="ERR_JWKS_INVALID"}class m extends b{static code="ERR_JWKS_NO_MATCHING_KEY";code="ERR_JWKS_NO_MATCHING_KEY";constructor(a="no applicable key found in the JSON Web Key Set",b){super(a,b)}}class n extends b{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(a="multiple matching keys found in the JSON Web Key Set",b){super(a,b)}}class o extends b{static code="ERR_JWKS_TIMEOUT";code="ERR_JWKS_TIMEOUT";constructor(a="request timed out",b){super(a,b)}}class p extends b{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(a="signature verification failed",b){super(a,b)}}}},802874:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(716168);let b=(a,b)=>{let c=`SHA-${a.slice(-3)}`;switch(a){case"HS256":case"HS384":case"HS512":return{hash:c,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:c,name:"RSA-PSS",saltLength:parseInt(a.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:c,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:c,name:"ECDSA",namedCurve:b.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new d.JOSENotSupported(`alg ${a} is not supported either by JOSE or your javascript runtime`)}}}},28203:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=(a,b)=>{if(a.startsWith("RS")||a.startsWith("PS")){let{modulusLength:c}=b.algorithm;if("number"!=typeof c||c<2048)throw TypeError(`${a} requires key modulusLength to be 2048 bits or larger`)}}}},612561:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function e(a,b){return a.name===b}function f(a){return parseInt(a.name.slice(4),10)}function g(a,b){if(b&&!a.usages.includes(b))throw TypeError(`CryptoKey does not support this operation, its usages must include ${b}.`)}function h(a,b,c){switch(b){case"HS256":case"HS384":case"HS512":{if(!e(a.algorithm,"HMAC"))throw d("HMAC");let c=parseInt(b.slice(2),10);if(f(a.algorithm.hash)!==c)throw d(`SHA-${c}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!e(a.algorithm,"RSASSA-PKCS1-v1_5"))throw d("RSASSA-PKCS1-v1_5");let c=parseInt(b.slice(2),10);if(f(a.algorithm.hash)!==c)throw d(`SHA-${c}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!e(a.algorithm,"RSA-PSS"))throw d("RSA-PSS");let c=parseInt(b.slice(2),10);if(f(a.algorithm.hash)!==c)throw d(`SHA-${c}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!e(a.algorithm,"Ed25519"))throw d("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!e(a.algorithm,"ECDSA"))throw d("ECDSA");let c=function(a){switch(a){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(b);if(a.algorithm.namedCurve!==c)throw d(c,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}g(a,c)}function i(a,b,c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!e(a.algorithm,"AES-GCM"))throw d("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw d(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!e(a.algorithm,"AES-KW"))throw d("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw d(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":break;default:throw d("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!e(a.algorithm,"PBKDF2"))throw d("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!e(a.algorithm,"RSA-OAEP"))throw d("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(f(a.algorithm.hash)!==c)throw d(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}g(a,c)}a.s({checkEncCryptoKey:()=>i,checkSigCryptoKey:()=>h})},398580:a=>{"use strict";var{g:b,__dirname:c}=a;{function d(a,b,...c){if((c=c.filter(Boolean)).length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor?.name&&(a+=` Received an instance of ${b.constructor.name}`),a}a.s({default:()=>b,withAlg:()=>e});let b=(a,...b)=>d("Key must be ",a,...b);function e(a,b,...c){return d(`Key for the ${a} algorithm must be `,b,...c)}}},702314:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(612561),e=a.i(398580);let b=async(a,b,c)=>{if(b instanceof Uint8Array){if(!a.startsWith("HS"))throw TypeError((0,e.default)(b,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",b,{hash:`SHA-${a.slice(-3)}`,name:"HMAC"},!1,[c])}return(0,d.checkSigCryptoKey)(b,a,c),b}}},733548:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(802874),e=a.i(28203),f=a.i(702314);let b=async(a,b,c)=>{let g=await (0,f.default)(a,b,"sign");return(0,e.default)(a,g),new Uint8Array(await crypto.subtle.sign((0,d.default)(a,g.algorithm),g,c))}}},126210:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0}}},364778:a=>{"use strict";var{g:b,__dirname:c}=a;{function d(a){if(!e(a))throw Error("CryptoKey instance expected")}function e(a){return a?.[Symbol.toStringTag]==="CryptoKey"}function f(a){return a?.[Symbol.toStringTag]==="KeyObject"}a.s({assertCryptoKey:()=>d,default:()=>b,isCryptoKey:()=>e,isKeyObject:()=>f});let b=a=>e(a)||f(a)}},465186:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=a=>{if(!function(a){return"object"==typeof a&&null!==a}(a)||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}}},831667:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isJWK:()=>e,isPrivateJWK:()=>f,isPublicJWK:()=>g,isSecretJWK:()=>h});var d=a.i(465186);function e(a){return(0,d.default)(a)&&"string"==typeof a.kty}function f(a){return"oct"!==a.kty&&"string"==typeof a.d}function g(a){return"oct"!==a.kty&&void 0===a.d}function h(a){return"oct"===a.kty&&"string"==typeof a.k}},14138:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>i});var d=a.i(398580),e=a.i(364778),f=a.i(831667);let b=a=>a?.[Symbol.toStringTag],c=(a,b,c)=>{if(void 0!==b.use){let a;switch(c){case"sign":case"verify":a="sig";break;case"encrypt":case"decrypt":a="enc"}if(b.use!==a)throw TypeError(`Invalid key for this operation, its "use" must be "${a}" when present`)}if(void 0!==b.alg&&b.alg!==a)throw TypeError(`Invalid key for this operation, its "alg" must be "${a}" when present`);if(Array.isArray(b.key_ops)){let d;switch(!0){case"sign"===c||"verify"===c:case"dir"===a:case a.includes("CBC-HS"):d=c;break;case a.startsWith("PBES2"):d="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(a):d=!a.includes("GCM")&&a.endsWith("KW")?"encrypt"===c?"wrapKey":"unwrapKey":c;break;case"encrypt"===c&&a.startsWith("RSA"):d="wrapKey";break;case"decrypt"===c:d=a.startsWith("RSA")?"unwrapKey":"deriveBits"}if(d&&b.key_ops?.includes?.(d)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${d}" when present`)}return!0},g=(a,g,h)=>{if(!(g instanceof Uint8Array)){if((0,f.isJWK)(g)){if((0,f.isSecretJWK)(g)&&c(a,g,h))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!(0,e.default)(g))throw TypeError((0,d.withAlg)(a,g,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==g.type)throw TypeError(`${b(g)} instances for symmetric algorithms must be of type "secret"`)}},h=(a,g,h)=>{if((0,f.isJWK)(g))switch(h){case"decrypt":case"sign":if((0,f.isPrivateJWK)(g)&&c(a,g,h))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if((0,f.isPublicJWK)(g)&&c(a,g,h))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!(0,e.default)(g))throw TypeError((0,d.withAlg)(a,g,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===g.type)throw TypeError(`${b(g)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===g.type)switch(h){case"sign":throw TypeError(`${b(g)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${b(g)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===g.type)switch(h){case"verify":throw TypeError(`${b(g)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${b(g)} instances for asymmetric algorithm encryption must be of type "public"`)}},i=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(a)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(a)?g(a,b,c):h(a,b,c)}}},915861:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(716168);let b=(a,b,c,e,f)=>{let g;if(void 0!==f.crit&&e?.crit===void 0)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!e||void 0===e.crit)return new Set;if(!Array.isArray(e.crit)||0===e.crit.length||e.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let h of(g=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,e.crit)){if(!g.has(h))throw new d.JOSENotSupported(`Extension Header Parameter "${h}" is not recognized`);if(void 0===f[h])throw new a(`Extension Header Parameter "${h}" is missing`);if(g.get(h)&&void 0===e[h])throw new a(`Extension Header Parameter "${h}" MUST be integrity protected`)}return new Set(e.crit)}}},746933:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(716168);let b=async a=>{if(!a.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:b,keyUsages:c}=function(a){let b,c;switch(a.kty){case"RSA":switch(a.alg){case"PS256":case"PS384":case"PS512":b={name:"RSA-PSS",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":b={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":b={name:"RSA-OAEP",hash:`SHA-${parseInt(a.alg.slice(-3),10)||1}`},c=a.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new d.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(a.alg){case"ES256":b={name:"ECDSA",namedCurve:"P-256"},c=a.d?["sign"]:["verify"];break;case"ES384":b={name:"ECDSA",namedCurve:"P-384"},c=a.d?["sign"]:["verify"];break;case"ES512":b={name:"ECDSA",namedCurve:"P-521"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:"ECDH",namedCurve:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new d.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(a.alg){case"Ed25519":case"EdDSA":b={name:"Ed25519"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new d.JOSENotSupported('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new d.JOSENotSupported('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:b,keyUsages:c}}(a),e={...a};return delete e.alg,delete e.use,crypto.subtle.importKey("jwk",e,b,a.ext??!a.d,a.key_ops??c)}}},282356:a=>{"use strict";var{g:b,__dirname:c}=a;{let b;a.s({default:()=>i});var d=a.i(831667),e=a.i(154658),f=a.i(746933),g=a.i(364778);let c=async(a,c,d,e=!1)=>{let g=(b||=new WeakMap).get(a);if(g?.[d])return g[d];let h=await (0,f.default)({...c,alg:d});return e&&Object.freeze(a),g?g[d]=h:b.set(a,{[d]:h}),h},h=(a,c)=>{let d,e=(b||=new WeakMap).get(a);if(e?.[c])return e[c];let f="public"===a.type,g=!!f;if("x25519"===a.asymmetricKeyType){switch(c){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}d=a.toCryptoKey(a.asymmetricKeyType,g,f?[]:["deriveBits"])}if("ed25519"===a.asymmetricKeyType){if("EdDSA"!==c&&"Ed25519"!==c)throw TypeError("given KeyObject instance cannot be used for this algorithm");d=a.toCryptoKey(a.asymmetricKeyType,g,[f?"verify":"sign"])}if("rsa"===a.asymmetricKeyType){let b;switch(c){case"RSA-OAEP":b="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":b="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":b="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":b="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(c.startsWith("RSA-OAEP"))return a.toCryptoKey({name:"RSA-OAEP",hash:b},g,f?["encrypt"]:["decrypt"]);d=a.toCryptoKey({name:c.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:b},g,[f?"verify":"sign"])}if("ec"===a.asymmetricKeyType){let b=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(a.asymmetricKeyDetails?.namedCurve);if(!b)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===c&&"P-256"===b&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:b},g,[f?"verify":"sign"])),"ES384"===c&&"P-384"===b&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:b},g,[f?"verify":"sign"])),"ES512"===c&&"P-521"===b&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:b},g,[f?"verify":"sign"])),c.startsWith("ECDH-ES")&&(d=a.toCryptoKey({name:"ECDH",namedCurve:b},g,f?[]:["deriveBits"]))}if(!d)throw TypeError("given KeyObject instance cannot be used for this algorithm");return e?e[c]=d:b.set(a,{[c]:d}),d},i=async(a,b)=>{if(a instanceof Uint8Array||(0,g.isCryptoKey)(a))return a;if((0,g.isKeyObject)(a)){if("secret"===a.type)return a.export();if("toCryptoKey"in a&&"function"==typeof a.toCryptoKey)try{return h(a,b)}catch(a){if(a instanceof TypeError)throw a}let d=a.export({format:"jwk"});return c(a,d,b)}if((0,d.isJWK)(a))return a.k?(0,e.decode)(a.k):c(a,a,b,!0);throw Error("unreachable")}}},138799:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({FlattenedSign:()=>b});var d=a.i(154658),e=a.i(733548),f=a.i(126210),g=a.i(716168),h=a.i(880446),i=a.i(14138),j=a.i(915861),k=a.i(282356);class b{#a;#b;#c;constructor(a){if(!(a instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#a=a}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setUnprotectedHeader(a){if(this.#c)throw TypeError("setUnprotectedHeader can only be called once");return this.#c=a,this}async sign(a,b){let c;if(!this.#b&&!this.#c)throw new g.JWSInvalid("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,f.default)(this.#b,this.#c))throw new g.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let l={...this.#b,...this.#c},m=(0,j.default)(g.JWSInvalid,new Map([["b64",!0]]),b?.crit,this.#b,l),n=!0;if(m.has("b64")&&"boolean"!=typeof(n=this.#b.b64))throw new g.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:o}=l;if("string"!=typeof o||!o)throw new g.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,i.default)(o,a,"sign");let p=this.#a;n&&(p=h.encoder.encode((0,d.encode)(p))),c=this.#b?h.encoder.encode((0,d.encode)(JSON.stringify(this.#b))):h.encoder.encode("");let q=(0,h.concat)(c,h.encoder.encode("."),p),r=await (0,k.default)(a,o),s=await (0,e.default)(o,r,q),t={signature:(0,d.encode)(s),payload:""};return n&&(t.payload=h.decoder.decode(p)),this.#c&&(t.header=this.#c),this.#b&&(t.protected=h.decoder.decode(c)),t}}}},30355:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({CompactSign:()=>b});var d=a.i(138799);class b{#d;constructor(a){this.#d=new d.FlattenedSign(a)}setProtectedHeader(a){return this.#d.setProtectedHeader(a),this}async sign(a,b){let c=await this.#d.sign(a,b);if(void 0===c.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${c.protected}.${c.payload}.${c.signature}`}}}},598644:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=a=>Math.floor(a.getTime()/1e3)}},608444:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>c});let b=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,c=a=>{let c,d=b.exec(a);if(!d||d[4]&&d[1])throw TypeError("Invalid time period format");let e=parseFloat(d[2]);switch(d[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":c=Math.round(e);break;case"minute":case"minutes":case"min":case"mins":case"m":c=Math.round(60*e);break;case"hour":case"hours":case"hr":case"hrs":case"h":c=Math.round(3600*e);break;case"day":case"days":case"d":c=Math.round(86400*e);break;case"week":case"weeks":case"w":c=Math.round(604800*e);break;default:c=Math.round(0x1e187e0*e)}return"-"===d[1]||"ago"===d[4]?-c:c}}},872535:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({JWTClaimsBuilder:()=>k,validateClaimsSet:()=>j});var d=a.i(716168),e=a.i(880446),f=a.i(598644),g=a.i(608444),h=a.i(465186);function i(a,b){if(!Number.isFinite(b))throw TypeError(`Invalid ${a} input`);return b}let b=a=>a.includes("/")?a.toLowerCase():`application/${a.toLowerCase()}`,c=(a,b)=>"string"==typeof a?b.includes(a):!!Array.isArray(a)&&b.some(Set.prototype.has.bind(new Set(a)));function j(a,i,k={}){let l,m;try{l=JSON.parse(e.decoder.decode(i))}catch{}if(!(0,h.default)(l))throw new d.JWTInvalid("JWT Claims Set must be a top-level JSON object");let{typ:n}=k;if(n&&("string"!=typeof a.typ||b(a.typ)!==b(n)))throw new d.JWTClaimValidationFailed('unexpected "typ" JWT header value',l,"typ","check_failed");let{requiredClaims:o=[],issuer:p,subject:q,audience:r,maxTokenAge:s}=k,t=[...o];for(let a of(void 0!==s&&t.push("iat"),void 0!==r&&t.push("aud"),void 0!==q&&t.push("sub"),void 0!==p&&t.push("iss"),new Set(t.reverse())))if(!(a in l))throw new d.JWTClaimValidationFailed(`missing required "${a}" claim`,l,a,"missing");if(p&&!(Array.isArray(p)?p:[p]).includes(l.iss))throw new d.JWTClaimValidationFailed('unexpected "iss" claim value',l,"iss","check_failed");if(q&&l.sub!==q)throw new d.JWTClaimValidationFailed('unexpected "sub" claim value',l,"sub","check_failed");if(r&&!c(l.aud,"string"==typeof r?[r]:r))throw new d.JWTClaimValidationFailed('unexpected "aud" claim value',l,"aud","check_failed");switch(typeof k.clockTolerance){case"string":m=(0,g.default)(k.clockTolerance);break;case"number":m=k.clockTolerance;break;case"undefined":m=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:u}=k,v=(0,f.default)(u||new Date);if((void 0!==l.iat||s)&&"number"!=typeof l.iat)throw new d.JWTClaimValidationFailed('"iat" claim must be a number',l,"iat","invalid");if(void 0!==l.nbf){if("number"!=typeof l.nbf)throw new d.JWTClaimValidationFailed('"nbf" claim must be a number',l,"nbf","invalid");if(l.nbf>v+m)throw new d.JWTClaimValidationFailed('"nbf" claim timestamp check failed',l,"nbf","check_failed")}if(void 0!==l.exp){if("number"!=typeof l.exp)throw new d.JWTClaimValidationFailed('"exp" claim must be a number',l,"exp","invalid");if(l.exp<=v-m)throw new d.JWTExpired('"exp" claim timestamp check failed',l,"exp","check_failed")}if(s){let a=v-l.iat;if(a-m>("number"==typeof s?s:(0,g.default)(s)))throw new d.JWTExpired('"iat" claim timestamp check failed (too far in the past)',l,"iat","check_failed");if(a<0-m)throw new d.JWTClaimValidationFailed('"iat" claim timestamp check failed (it should be in the past)',l,"iat","check_failed")}return l}class k{#a;constructor(a){if(!(0,h.default)(a))throw TypeError("JWT Claims Set MUST be an object");this.#a=structuredClone(a)}data(){return e.encoder.encode(JSON.stringify(this.#a))}get iss(){return this.#a.iss}set iss(a){this.#a.iss=a}get sub(){return this.#a.sub}set sub(a){this.#a.sub=a}get aud(){return this.#a.aud}set aud(a){this.#a.aud=a}set jti(a){this.#a.jti=a}set nbf(a){"number"==typeof a?this.#a.nbf=i("setNotBefore",a):a instanceof Date?this.#a.nbf=i("setNotBefore",(0,f.default)(a)):this.#a.nbf=(0,f.default)(new Date)+(0,g.default)(a)}set exp(a){"number"==typeof a?this.#a.exp=i("setExpirationTime",a):a instanceof Date?this.#a.exp=i("setExpirationTime",(0,f.default)(a)):this.#a.exp=(0,f.default)(new Date)+(0,g.default)(a)}set iat(a){void 0===a?this.#a.iat=(0,f.default)(new Date):a instanceof Date?this.#a.iat=i("setIssuedAt",(0,f.default)(a)):"string"==typeof a?this.#a.iat=i("setIssuedAt",(0,f.default)(new Date)+(0,g.default)(a)):this.#a.iat=i("setIssuedAt",a)}}}},749724:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SignJWT:()=>b});var d=a.i(30355),e=a.i(716168),f=a.i(872535);class b{#b;#e;constructor(a={}){this.#e=new f.JWTClaimsBuilder(a)}setIssuer(a){return this.#e.iss=a,this}setSubject(a){return this.#e.sub=a,this}setAudience(a){return this.#e.aud=a,this}setJti(a){return this.#e.jti=a,this}setNotBefore(a){return this.#e.nbf=a,this}setExpirationTime(a){return this.#e.exp=a,this}setIssuedAt(a){return this.#e.iat=a,this}setProtectedHeader(a){return this.#b=a,this}async sign(a,b){let c=new d.CompactSign(this.#e.data());if(c.setProtectedHeader(this.#b),Array.isArray(this.#b?.crit)&&this.#b.crit.includes("b64")&&!1===this.#b.b64)throw new e.JWTInvalid("JWTs MUST NOT use unencoded payload");return c.sign(a,b)}}}},255113:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(802874),e=a.i(28203),f=a.i(702314);let b=async(a,b,c,g)=>{let h=await (0,f.default)(a,b,"verify");(0,e.default)(a,h);let i=(0,d.default)(a,h.algorithm);try{return await crypto.subtle.verify(i,h,c,g)}catch{return!1}}}},888287:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)}}},778715:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({flattenedVerify:()=>n});var d=a.i(154658),e=a.i(255113),f=a.i(716168),g=a.i(880446),h=a.i(126210),i=a.i(465186),j=a.i(14138),k=a.i(915861),l=a.i(888287),m=a.i(282356);async function n(a,b,c){let n,o;if(!(0,i.default)(a))throw new f.JWSInvalid("Flattened JWS must be an object");if(void 0===a.protected&&void 0===a.header)throw new f.JWSInvalid('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==a.protected&&"string"!=typeof a.protected)throw new f.JWSInvalid("JWS Protected Header incorrect type");if(void 0===a.payload)throw new f.JWSInvalid("JWS Payload missing");if("string"!=typeof a.signature)throw new f.JWSInvalid("JWS Signature missing or incorrect type");if(void 0!==a.header&&!(0,i.default)(a.header))throw new f.JWSInvalid("JWS Unprotected Header incorrect type");let p={};if(a.protected)try{let b=(0,d.decode)(a.protected);p=JSON.parse(g.decoder.decode(b))}catch{throw new f.JWSInvalid("JWS Protected Header is invalid")}if(!(0,h.default)(p,a.header))throw new f.JWSInvalid("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let q={...p,...a.header},r=(0,k.default)(f.JWSInvalid,new Map([["b64",!0]]),c?.crit,p,q),s=!0;if(r.has("b64")&&"boolean"!=typeof(s=p.b64))throw new f.JWSInvalid('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:t}=q;if("string"!=typeof t||!t)throw new f.JWSInvalid('JWS "alg" (Algorithm) Header Parameter missing or invalid');let u=c&&(0,l.default)("algorithms",c.algorithms);if(u&&!u.has(t))throw new f.JOSEAlgNotAllowed('"alg" (Algorithm) Header Parameter value not allowed');if(s){if("string"!=typeof a.payload)throw new f.JWSInvalid("JWS Payload must be a string")}else if("string"!=typeof a.payload&&!(a.payload instanceof Uint8Array))throw new f.JWSInvalid("JWS Payload must be a string or an Uint8Array instance");let v=!1;"function"==typeof b&&(b=await b(p,a),v=!0),(0,j.default)(t,b,"verify");let w=(0,g.concat)(g.encoder.encode(a.protected??""),g.encoder.encode("."),"string"==typeof a.payload?g.encoder.encode(a.payload):a.payload);try{n=(0,d.decode)(a.signature)}catch{throw new f.JWSInvalid("Failed to base64url decode the signature")}let x=await (0,m.default)(b,t);if(!await (0,e.default)(t,x,n,w))throw new f.JWSSignatureVerificationFailed;if(s)try{o=(0,d.decode)(a.payload)}catch{throw new f.JWSInvalid("Failed to base64url decode the payload")}else o="string"==typeof a.payload?g.encoder.encode(a.payload):a.payload;let y={payload:o};return(void 0!==a.protected&&(y.protectedHeader=p),void 0!==a.header&&(y.unprotectedHeader=a.header),v)?{...y,key:x}:y}},243326:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({compactVerify:()=>g});var d=a.i(778715),e=a.i(716168),f=a.i(880446);async function g(a,b,c){if(a instanceof Uint8Array&&(a=f.decoder.decode(a)),"string"!=typeof a)throw new e.JWSInvalid("Compact JWS must be a string or Uint8Array");let{0:g,1:h,2:i,length:j}=a.split(".");if(3!==j)throw new e.JWSInvalid("Invalid Compact JWS");let k=await (0,d.flattenedVerify)({payload:h,protected:g,signature:i},b,c),l={payload:k.payload,protectedHeader:k.protectedHeader};return"function"==typeof b?{...l,key:k.key}:l}},290753:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({jwtVerify:()=>g});var d=a.i(243326),e=a.i(872535),f=a.i(716168);async function g(a,b,c){let g=await (0,d.compactVerify)(a,b,c);if(g.protectedHeader.crit?.includes("b64")&&!1===g.protectedHeader.b64)throw new f.JWTInvalid("JWTs MUST NOT use unencoded payload");let h={payload:(0,e.validateClaimsSet)(g.protectedHeader,g.payload,c),protectedHeader:g.protectedHeader};return"function"==typeof b?{...h,key:g.key}:h}}};

//# sourceMappingURL=node_modules_jose_dist_webapi_63f4176e._.js.map