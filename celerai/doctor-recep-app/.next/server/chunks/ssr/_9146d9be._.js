module.exports={985661:a=>{"use strict";var{g:b,__dirname:c}=a;{function d(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}a.s({clsx:()=>d,default:()=>b});let b=d}},366294:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createTailwindMerge:()=>e,extendTailwindMerge:()=>ap,fromTheme:()=>t,getDefaultConfig:()=>aj,mergeConfigs:()=>ak,twJoin:()=>d,twMerge:()=>aq,validators:()=>ai});let b=a=>{let b=h(a),{conflictingClassGroups:d,conflictingClassGroupModifiers:e}=a;return{getClassGroupId:a=>{let d=a.split("-");return""===d[0]&&1!==d.length&&d.shift(),c(d,b)||g(a)},getConflictingClassGroupIds:(a,b)=>{let c=d[a]||[];return b&&e[a]?[...c,...e[a]]:c}}},c=(a,b)=>{if(0===a.length)return b.classGroupId;let d=a[0],e=b.nextPart.get(d),f=e?c(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},f=/^\[(.+)\]$/,g=a=>{if(f.test(a)){let b=f.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}},h=a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)i(c[a],d,a,b);return d},i=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:j(b,a)).classGroupId=c;return}if("function"==typeof a)return k(a)?void i(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{i(e,j(b,a),c,d)})})},j=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},k=a=>a.isThemeGetter,l=a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}},m=a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c=[],d=0,e=0,f=0;for(let g=0;g<a.length;g++){let h=a[g];if(0===d&&0===e){if(":"===h){c.push(a.slice(f,g)),f=g+1;continue}if("/"===h){b=g;continue}}"["===h?d++:"]"===h?d--:"("===h?e++:")"===h&&e--}let g=0===c.length?a:a.substring(f),h=n(g);return{modifiers:c,hasImportantModifier:h!==g,baseClassName:h,maybePostfixModifierPosition:b&&b>f?b-f:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d},n=a=>a.endsWith("!")?a.substring(0,a.length-1):a.startsWith("!")?a.substring(1):a,o=a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}},p=a=>({cache:l(a.cacheSize),parseClassName:m(a),sortModifiers:o(a),...b(a)}),q=/\s+/,r=(a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(q),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i};function d(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=s(a))&&(d&&(d+=" "),d+=b);return d}let s=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=s(a[d]))&&(c&&(c+=" "),c+=b);return c};function e(a,...b){let c,f,g,h=function(d){return f=(c=p(b.reduce((a,b)=>b(a),a()))).cache.get,g=c.cache.set,h=i,i(d)};function i(a){let b=f(a);if(b)return b;let d=r(a,c);return g(a,d),d}return function(){return h(d.apply(null,arguments))}}let t=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},u=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,v=/^\((?:(\w[\w-]*):)?(.+)\)$/i,w=/^\d+\/\d+$/,x=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,y=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,B=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=a=>w.test(a),D=a=>!!a&&!Number.isNaN(Number(a)),E=a=>!!a&&Number.isInteger(Number(a)),F=a=>a.endsWith("%")&&D(a.slice(0,-1)),G=a=>x.test(a),H=()=>!0,I=a=>y.test(a)&&!z.test(a),J=()=>!1,K=a=>A.test(a),L=a=>B.test(a),M=a=>!O(a)&&!U(a),N=a=>_(a,ad,J),O=a=>u.test(a),P=a=>_(a,ae,I),Q=a=>_(a,af,D),R=a=>_(a,ab,J),S=a=>_(a,ac,L),T=a=>_(a,ah,K),U=a=>v.test(a),V=a=>aa(a,ae),W=a=>aa(a,ag),X=a=>aa(a,ab),Y=a=>aa(a,ad),Z=a=>aa(a,ac),$=a=>aa(a,ah,!0),_=(a,b,c)=>{let d=u.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},aa=(a,b,c=!1)=>{let d=v.exec(a);return!!d&&(d[1]?b(d[1]):c)},ab=a=>"position"===a||"percentage"===a,ac=a=>"image"===a||"url"===a,ad=a=>"length"===a||"size"===a||"bg-size"===a,ae=a=>"length"===a,af=a=>"number"===a,ag=a=>"family-name"===a,ah=a=>"shadow"===a,ai=Object.defineProperty({__proto__:null,isAny:H,isAnyNonArbitrary:M,isArbitraryImage:S,isArbitraryLength:P,isArbitraryNumber:Q,isArbitraryPosition:R,isArbitraryShadow:T,isArbitrarySize:N,isArbitraryValue:O,isArbitraryVariable:U,isArbitraryVariableFamilyName:W,isArbitraryVariableImage:Z,isArbitraryVariableLength:V,isArbitraryVariablePosition:X,isArbitraryVariableShadow:$,isArbitraryVariableSize:Y,isFraction:C,isInteger:E,isNumber:D,isPercent:F,isTshirtSize:G},Symbol.toStringTag,{value:"Module"}),aj=()=>{let a=t("color"),b=t("font"),c=t("text"),d=t("font-weight"),e=t("tracking"),f=t("leading"),g=t("breakpoint"),h=t("container"),i=t("spacing"),j=t("radius"),k=t("shadow"),l=t("inset-shadow"),m=t("text-shadow"),n=t("drop-shadow"),o=t("blur"),p=t("perspective"),q=t("aspect"),r=t("ease"),s=t("animate"),u=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...v(),U,O],x=()=>["auto","hidden","clip","visible","scroll"],y=()=>["auto","contain","none"],z=()=>[U,O,i],A=()=>[C,"full","auto",...z()],B=()=>[E,"none","subgrid",U,O],I=()=>["auto",{span:["full",E,U,O]},E,U,O],J=()=>[E,"auto",U,O],K=()=>["auto","min","max","fr",U,O],L=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...z()],ab=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...z()],ac=()=>[a,U,O],ad=()=>[...v(),X,R,{position:[U,O]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",Y,N,{size:[U,O]}],ag=()=>[F,V,P],ah=()=>["","none","full",j,U,O],ai=()=>["",D,V,P],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[D,F,X,R],am=()=>["","none",o,U,O],an=()=>["none",D,U,O],ao=()=>["none",D,U,O],ap=()=>[D,U,O],aq=()=>[C,"full",...z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[G],breakpoint:[G],color:[H],container:[G],"drop-shadow":[G],ease:["in","out","in-out"],font:[M],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[G],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[G],shadow:[G],spacing:["px",D],text:[G],"text-shadow":[G],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,O,U,q]}],container:["container"],columns:[{columns:[D,O,U,h]}],"break-after":[{"break-after":u()}],"break-before":[{"break-before":u()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:x()}],"overflow-x":[{"overflow-x":x()}],"overflow-y":[{"overflow-y":x()}],overscroll:[{overscroll:y()}],"overscroll-x":[{"overscroll-x":y()}],"overscroll-y":[{"overscroll-y":y()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[E,"auto",U,O]}],basis:[{basis:[C,"full","auto",h,...z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[D,C,"auto","initial","none",O]}],grow:[{grow:["",D,U,O]}],shrink:[{shrink:["",D,U,O]}],order:[{order:[E,"first","last","none",U,O]}],"grid-cols":[{"grid-cols":B()}],"col-start-end":[{col:I()}],"col-start":[{"col-start":J()}],"col-end":[{"col-end":J()}],"grid-rows":[{"grid-rows":B()}],"row-start-end":[{row:I()}],"row-start":[{"row-start":J()}],"row-end":[{"row-end":J()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":K()}],"auto-rows":[{"auto-rows":K()}],gap:[{gap:z()}],"gap-x":[{"gap-x":z()}],"gap-y":[{"gap-y":z()}],"justify-content":[{justify:[...L(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...L()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":L()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:z()}],px:[{px:z()}],py:[{py:z()}],ps:[{ps:z()}],pe:[{pe:z()}],pt:[{pt:z()}],pr:[{pr:z()}],pb:[{pb:z()}],pl:[{pl:z()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":z()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,V,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,U,Q]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",F,O]}],"font-family":[{font:[W,O,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,U,O]}],"line-clamp":[{"line-clamp":[D,"none",U,Q]}],leading:[{leading:[f,...z()]}],"list-image":[{"list-image":["none",U,O]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",U,O]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[D,"from-font","auto",U,P]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[D,"auto",U,O]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",U,O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",U,O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},E,U,O],radial:["",U,O],conic:[E,U,O]},Z,S]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[D,U,O]}],"outline-w":[{outline:["",D,V,P]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,$,T]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",l,$,T]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[D,P]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",m,$,T]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[D,U,O]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[D]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[U,O]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":v()}],"mask-image-conic-pos":[{"mask-conic":[D]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",U,O]}],filter:[{filter:["","none",U,O]}],blur:[{blur:am()}],brightness:[{brightness:[D,U,O]}],contrast:[{contrast:[D,U,O]}],"drop-shadow":[{"drop-shadow":["","none",n,$,T]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",D,U,O]}],"hue-rotate":[{"hue-rotate":[D,U,O]}],invert:[{invert:["",D,U,O]}],saturate:[{saturate:[D,U,O]}],sepia:[{sepia:["",D,U,O]}],"backdrop-filter":[{"backdrop-filter":["","none",U,O]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[D,U,O]}],"backdrop-contrast":[{"backdrop-contrast":[D,U,O]}],"backdrop-grayscale":[{"backdrop-grayscale":["",D,U,O]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[D,U,O]}],"backdrop-invert":[{"backdrop-invert":["",D,U,O]}],"backdrop-opacity":[{"backdrop-opacity":[D,U,O]}],"backdrop-saturate":[{"backdrop-saturate":[D,U,O]}],"backdrop-sepia":[{"backdrop-sepia":["",D,U,O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":z()}],"border-spacing-x":[{"border-spacing-x":z()}],"border-spacing-y":[{"border-spacing-y":z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",U,O]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[D,"initial",U,O]}],ease:[{ease:["linear","initial",r,U,O]}],delay:[{delay:[D,U,O]}],animate:[{animate:["none",s,U,O]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,U,O]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[U,O,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",U,O]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",U,O]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[D,V,P,Q]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ak=(a,{cacheSize:b,prefix:c,experimentalParseClassName:d,extend:e={},override:f={}})=>(al(a,"cacheSize",b),al(a,"prefix",c),al(a,"experimentalParseClassName",d),am(a.theme,f.theme),am(a.classGroups,f.classGroups),am(a.conflictingClassGroups,f.conflictingClassGroups),am(a.conflictingClassGroupModifiers,f.conflictingClassGroupModifiers),al(a,"orderSensitiveModifiers",f.orderSensitiveModifiers),an(a.theme,e.theme),an(a.classGroups,e.classGroups),an(a.conflictingClassGroups,e.conflictingClassGroups),an(a.conflictingClassGroupModifiers,e.conflictingClassGroupModifiers),ao(a,e,"orderSensitiveModifiers"),a),al=(a,b,c)=>{void 0!==c&&(a[b]=c)},am=(a,b)=>{if(b)for(let c in b)al(a,c,b[c])},an=(a,b)=>{if(b)for(let c in b)ao(a,b,c)},ao=(a,b,c)=>{let d=b[c];void 0!==d&&(a[c]=a[c]?a[c].concat(d):d)},ap=(a,...b)=>"function"==typeof a?e(aj,a,...b):e(()=>ak(aj(),a),...b),aq=e(aj)}},148530:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({cn:()=>f,createFileFromBlob:()=>g,formatDate:()=>i,formatDuration:()=>h,formatRelativeTime:()=>j,generatePrompt:()=>m,isValidUrl:()=>l,retryWithBackoff:()=>k,supportsAudioRecording:()=>o,supportsCamera:()=>p,truncateText:()=>n});var d=a.i(985661),e=a.i(366294);function f(...a){return(0,e.twMerge)((0,d.clsx)(a))}function g(a,b){return new File([a],b,{type:a.type})}function h(a){let b=Math.floor(a/60),c=Math.floor(a%60);return`${b.toString().padStart(2,"0")}:${c.toString().padStart(2,"0")}`}function i(a){let b=new Date(a),c=b.getFullYear(),d=b.toLocaleDateString("en-US",{month:"short"}),e=b.getDate().toString().padStart(2,"0"),f=b.getMinutes().toString().padStart(2,"0"),g=b.getHours()>=12?"PM":"AM",h=b.getHours()%12||12;return`${d} ${e}, ${c} at ${h.toString().padStart(2,"0")}:${f} ${g}`}function j(a){let b=new Date(a),c=Math.floor((new Date().getTime()-b.getTime())/1e3);if(c<60)return"Just now";if(c<3600){let a=Math.floor(c/60);return`${a} minute${a>1?"s":""} ago`}if(c<86400){let a=Math.floor(c/3600);return`${a} hour${a>1?"s":""} ago`}{let a=Math.floor(c/86400);return`${a} day${a>1?"s":""} ago`}}async function k(a,b={maxAttempts:3,baseDelay:1e3,maxDelay:1e4}){let c;for(let d=1;d<=b.maxAttempts;d++)try{return await a()}catch(e){if(c=e,d===b.maxAttempts)throw c;let a=Math.min(b.baseDelay*Math.pow(2,d-1),b.maxDelay);console.warn(`Attempt ${d} failed, retrying in ${a}ms:`,e),await new Promise(b=>setTimeout(b,a))}throw c}function l(a){try{return new URL(a),!0}catch{return!1}}function m(a){return`
You are an AI assistant helping Indian doctors create patient consultation summaries.

Context: ${"doctor"===a?"This consultation was recorded by the doctor during patient visit.":"This consultation is being reviewed by the receptionist for final summary."}

Please analyze the provided audio recording and any handwritten notes (if image provided) to generate a comprehensive patient summary.

Requirements:
- Language: English
- Tone: Professional
- Format: Standard medical format
- Include sections: Chief Complaint, History, Examination, Diagnosis, Treatment Plan, Follow-up

Instructions:
1. Transcribe the audio accurately
2. Extract key medical information
3. If image provided, include any relevant handwritten notes
4. Structure the summary according to the specified sections
5. Use appropriate medical terminology
6. Ensure the summary is clear and professional

Please provide a well-structured patient consultation summary based on the audio and image inputs.
  `.trim()}function n(a,b){return a.length<=b?a:a.substring(0,b)+"..."}function o(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}function p(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}}};

//# sourceMappingURL=_9146d9be._.js.map