{"version": 3, "sources": ["turbopack:///[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/error-fallback.tsx", "turbopack:///[project]/node_modules/next/src/client/components/not-found-error.tsx"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "import React from 'react'\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  desc: {\n    display: 'inline-block',\n  },\n\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    padding: '0 23px 0 0',\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n    lineHeight: '49px',\n  },\n\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '49px',\n    margin: 0,\n  },\n}\n\nexport function HTTPAccessErrorFallback({\n  status,\n  message,\n}: {\n  status: number\n  message: string\n}) {\n  return (\n    <>\n      {/* <head> */}\n      <title>{`${status}: ${message}`}</title>\n      {/* </head> */}\n      <div style={styles.error}>\n        <div>\n          <style\n            dangerouslySetInnerHTML={{\n              /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,\n            }}\n          />\n          <h1 className=\"next-error-h1\" style={styles.h1}>\n            {status}\n          </h1>\n          <div style={styles.desc}>\n            <h2 style={styles.h2}>{message}</h2>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n", "import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function NotFound() {\n  return (\n    <HTTPAccessErrorFallback\n      status={404}\n      message=\"This page could not be found.\"\n    />\n  )\n}\n"], "names": ["HTTPAccessErrorFallback", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "status", "message", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className", "NotFound"], "mappings": "kEAAA,aAKA,EAAQ,CAAC,CAHT,EAGY,OAHsB,AAAzB,CAA4B,EACjC,OAAO,GAAO,EAAI,UAAU,CAAG,EAAM,CAAE,QAAS,CAAI,CACxD,4ICiCgBA,0BAAAA,qCAAAA,2CArCE,CAAA,CAAA,IAAA,KAElB,MAAMC,AACG,CAELE,QAHgD,GAI9C,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,IAEM,CACJH,QAAS,cACX,IAEI,CACFA,QAAS,eACTM,OAAQ,aACRC,QAAS,aACTC,SAAU,GACVC,WAAY,IACZC,cAAe,MACfC,WAAY,MACd,IAEI,CACFH,SAAU,GACVC,WAAY,IACZE,WAAY,OACZL,OAAQ,CACV,EAGK,SAASZ,EAAwB,CAMvC,EANuC,GAAA,QACtCmB,CAAM,SACNC,CAAO,CAIR,CANuC,EAOtC,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAAA,CADF,CACE,QAAA,CAAA,WAEE,CAAA,EAAA,EAAA,GAAA,EAACC,QAAAA,UAAUF,EAAO,KAAIC,IAEtB,CAAA,EAAA,EAAA,GAAA,EAACE,MAAAA,CAAIC,KAAAA,EAAOtB,OAAOC,GACjB,CAAA,CADsB,CACtB,EAAA,IAAA,EAACoB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,GAAA,EAACC,QAAAA,CACCC,wBAAyB,CAcvBC,OAAS,+NACX,IAEF,GAAA,EAAA,GAAA,EAACd,KAAAA,CAAGe,UAAU,gBAAgBH,KAAAA,EAAOtB,OAAOU,EAAE,CAC3CQ,IAEH,CAAA,EAAA,EAAA,GAAA,EAACG,MAAAA,CAAIC,KAAAA,EAAOtB,OAAOS,GACjB,CADqB,AACrB,EAAA,EAAA,GAAA,EAACQ,EAAD,GAACA,CAAGK,KAAAA,EAAOtB,OAAOiB,EAAE,CAAGE,aAMnC,mWC7EA,UAAA,qCAAwBO,2BAFgB,CAAA,CAAA,IAAA,IAEzB,SAASA,IACtB,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAAC3B,EADH,AACGA,uBAAuB,CAAA,CACtBmB,OAAQ,IACRC,QAAQ,iCAGd", "ignoreList": [0, 1, 2]}