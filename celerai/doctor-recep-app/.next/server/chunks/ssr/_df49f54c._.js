module.exports={994669:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],c=(0,d.default)("funnel",b)}},397428:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Filter:()=>d.default});var d=a.i(994669)},967662:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({CONSULTATION_TYPE_LABELS:()=>b,Constants:()=>c});let b={outpatient:"Outpatient Consultation",discharge:"Discharge Summary",surgery:"Operative Note",radiology:"Radiology Report",dermatology:"Dermatology Note",cardiology_echo:"Echocardiogram Report",ivf_cycle:"IVF Cycle Summary",pathology:"Histopathology Report"},c={public:{Enums:{}}}}},901825:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ConsultationModal:()=>A});var d=a.i(674420),e=a.i(722851),f=a.i(774440),g=a.i(767332),h=a.i(341239),i=a.i(246021),j=a.i(115285),k=a.i(12372),l=a.i(86111),m=a.i(388457),n=a.i(525128),o=a.i(629551),p=a.i(954780),q=a.i(491800),r=a.i(381085),s=a.i(967662),t=a.i(148530),u=a.i(762294),v=a.i(734293),w=a.i(895032),x=a.i(195646),y=a.i(844183),z=a.i(365171);function A({consultation:b,onClose:c,onConsultationUpdate:A,doctorName:B}){let[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(b.edited_note||b.ai_generated_note||""),[I,J]=(0,e.useState)(null),[K,L]=(0,e.useState)(!1),M=(0,e.useRef)(null),N=(0,e.useRef)(null),[O,P]=(0,e.useState)(""),[Q,R]=(0,e.useState)([]),[S,T]=(0,e.useState)(!1),[U,V]=(0,e.useState)(null),[W,X]=(0,e.useState)(null),[Y,Z]=(0,e.useState)(""),[$,_]=(0,e.useState)(!1),[aa,ab]=(0,e.useState)(b.consultation_type||"outpatient"),[ac,ad]=(0,e.useState)(b.additional_notes||""),[ae,af]=(0,e.useState)(null),[ag,ah]=(0,e.useState)(null),ai=(0,e.useRef)(null),aj=(0,e.useRef)(null),ak=(0,e.useRef)(null),al=(a,b=3e3)=>{ak.current&&clearTimeout(ak.current),P(a),a&&(ak.current=setTimeout(()=>{P(""),ak.current=null},b))};(0,e.useEffect)(()=>{_(!0)},[]),(0,e.useEffect)(()=>{P(""),ak.current&&(clearTimeout(ak.current),ak.current=null)},[b.id]),(0,e.useEffect)(()=>()=>{ae&&clearTimeout(ae)},[ae]),(0,e.useEffect)(()=>{H(b.edited_note||b.ai_generated_note||"")},[b.edited_note,b.ai_generated_note]),(0,e.useEffect)(()=>{ad(b.additional_notes||"")},[b.additional_notes]);let am=async c=>{try{P("Uploading images...");for(let d=0;d<c.length;d++){let e=c[d];if(!e.type.startsWith("image/")||e.size>0xa00000)continue;let{uploadFile:f}=await a.r(951737)(a.i),g=await f(e,b.doctor_id||"",b.id,"image");if(!g.success||!g.url)return void P(`Failed to upload ${e.name}: ${g.error}`);{let a=URL.createObjectURL(e);R(b=>[...b,{id:`${Date.now()}-${d}`,url:g.url,preview:a}])}}al("Images uploaded successfully!")}catch(a){P("Failed to upload images")}},an=a=>{R(b=>{let c=b.find(b=>b.id===a);return c&&c.preview&&"string"==typeof c.preview&&URL.revokeObjectURL(c.preview),b.filter(b=>b.id!==a)})},ao=async()=>{D(!0),P(""),Z("");try{let c=[...Array.isArray(b.image_urls)?b.image_urls:[],...Q.map(a=>a.url)].filter(a=>"string"==typeof a&&null!==a);if(Q.length>0){let{updateConsultationImages:d}=await a.r(881628)(a.i);await d(b.id,c)}try{let d=await fetch("/api/generate-summary-stream",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({primary_audio_url:b.primary_audio_url,additional_audio_urls:Array.isArray(b.additional_audio_urls)?b.additional_audio_urls:[],image_urls:[...Array.isArray(b.image_urls)?b.image_urls:[],...c],submitted_by:b.submitted_by||"doctor",consultation_type:aa,doctor_notes:b.doctor_notes||void 0,additional_notes:ac||void 0,patient_name:b.patient_name||void 0,doctor_name:B||void 0,created_at:b.created_at||void 0})});if(!d.ok)throw Error(`HTTP ${d.status}: ${d.statusText}`);let e=d.body?.getReader();if(!e)throw Error("No reader available");let f=new TextDecoder,g="";for(Z("");;){let{done:a,value:b}=await e.read();if(a)break;for(let a of f.decode(b,{stream:!0}).split("\n"))if(a.startsWith("data: "))try{let b=JSON.parse(a.slice(6));if("chunk"===b.type&&b.text){let a=b.text.replace(/\\n/g,"\n").replace(/\n\n+/g,"\n\n");g+=a,Z(g)}}catch(a){}}H(g);try{let{saveStreamingSummary:c}=await a.r(881628)(a.i),d=await c(b.id,g);d.success?(al("Streaming summary generated and saved successfully!"),(0,y.trackConsultation)("generated",aa),A&&A({...b,ai_generated_note:g,status:"generated"})):(console.error("Failed to save streaming summary:",d.error),P(`Summary generated but failed to save: ${d.error}`))}catch(a){console.error("Error saving streaming summary:",a),P("Summary generated but failed to save. Please try regenerate.")}setTimeout(()=>P(""),3e3)}catch(a){console.error("Streaming error:",a),P("Failed to generate streaming summary"),setTimeout(()=>P(""),5e3)}}catch{P("An unexpected error occurred")}finally{D(!1)}},ap=async()=>{if(!G.trim())return void P("Please provide a summary before approving");F(!0),P("");try{let a=await (0,u.approveConsultation)(b.id,G);a.success?(P("Consultation approved successfully!"),(0,y.trackConsultation)("approved",aa),setTimeout(()=>{P(""),c()},2e3)):P(a.error||"Failed to approve consultation")}catch{P("An unexpected error occurred")}finally{F(!1)}},aq=async()=>{try{await navigator.clipboard.writeText(G),al("Copied to clipboard!",2e3)}catch{P("Failed to copy to clipboard")}},ar=async()=>{if(N.current){N.current.stop(),N.current=null,J(null);return}L(!0),P("Loading audio...");try{M.current||(M.current=new(window.AudioContext||window.webkitAudioContext)),"suspended"===M.current.state&&await M.current.resume();let a=M.current,c=`/api/audio-proxy?url=${encodeURIComponent(b.primary_audio_url)}`,d=await fetch(c);if(!d.ok)throw Error(`Audio fetch failed: ${d.statusText}`);let e=await d.arrayBuffer(),f=await a.decodeAudioData(e),g=a.createBufferSource();g.buffer=f,g.connect(a.destination),g.start(0),g.onended=()=>{J(null),N.current=null},N.current=g,J({}),P("")}catch(b){console.error("Web Audio API Error:",b);let a=b instanceof Error?b.message:String(b);P(`Unable to play audio: ${a}`),J(null)}finally{L(!1)}},as=async()=>{if(/^((?!chrome|android).)*safari/i.test(navigator.userAgent))return void await ar();if(I){I.pause(),I.currentTime=0,J(null);return}try{let a=new Audio(b.primary_audio_url);a.onended=()=>J(null),a.onerror=()=>{J(null),P("Failed to play audio")},J(a),await a.play()}catch(a){console.error("Audio playback error:",a),J(null),P("Failed to play audio")}},at=()=>["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/mpeg"].find(a=>MediaRecorder.isTypeSupported(a))||"audio/webm",au=async()=>{try{let a=await navigator.mediaDevices.getUserMedia({audio:!0}),b=at(),c=new MediaRecorder(a,{mimeType:b}),d=[];c.ondataavailable=a=>d.push(a.data),c.onstop=()=>{let c=new Blob(d,{type:b});V(c),a.getTracks().forEach(a=>a.stop())},c.start(),X(c),T(!0)}catch(a){P("Failed to start recording. Please check microphone permissions.")}},av=async()=>{if(U)try{let c=new File([U],`additional_audio_${Date.now()}.wav`,{type:"audio/wav"}),d=await (0,v.addAdditionalAudio)(b.id,c);if(d.success){P("Additional audio uploaded successfully! Refreshing consultation data..."),V(null);let{getConsultations:c}=await a.r(881628)(a.i),d=await c();if(d.success&&d.data){let a=d.data.consultations.find(a=>a.id===b.id);a&&A&&(A(a),P("Additional audio uploaded successfully! Please regenerate summary to include this audio."))}al("")}else P(d.error||"Failed to upload additional audio")}catch(a){P("Failed to upload additional audio")}},aw=async c=>{try{P("Deleting audio...");let d=await (0,w.deleteAdditionalAudio)(b.id,c);if(d.success){al("Audio deleted successfully!");let{getConsultations:c}=await a.r(881628)(a.i),d=await c();if(d.success&&d.data){let a=d.data.consultations.find(a=>a.id===b.id);a&&A&&A(a)}}else P(d.error||"Failed to delete audio")}catch(a){P("Failed to delete audio")}finally{ah(null)}},ax=async c=>{try{P("Deleting image...");let d=await (0,x.deleteConsultationImage)(b.id,c);if(d.success){al("Image deleted successfully!");let{getConsultations:c}=await a.r(881628)(a.i),d=await c();if(d.success&&d.data){let a=d.data.consultations.find(a=>a.id===b.id);a&&A&&A(a)}}else P(d.error||"Failed to delete image")}catch(a){P("Failed to delete image")}finally{ah(null)}},ay=(a,b)=>{af(setTimeout(()=>{ah({type:a,url:b})},500))},az=()=>{ae&&(clearTimeout(ae),af(null))},aA=(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50",children:(0,d.jsxs)("div",{className:"absolute top-4 left-1/2 transform -translate-x-1/2 bg-white rounded-2xl max-w-5xl w-[95%] h-[90vh] shadow-2xl flex flex-col overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 px-3 sm:px-6 py-3 sm:py-4 border-b border-orange-200/50",children:(0,d.jsxs)("div",{className:"flex items-start sm:items-center justify-between gap-2",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h3",{className:"text-lg sm:text-xl font-bold text-slate-800 truncate",children:b.patient_name||`Patient #${b.patient_number||"N/A"}`}),(0,d.jsxs)("p",{className:"text-xs sm:text-sm text-slate-600 break-words",children:[(0,t.formatDate)(b.created_at)," • ",b.submitted_by," • ",b.status]}),b.doctor_notes&&(0,d.jsxs)("p",{className:"text-xs sm:text-sm text-slate-500 mt-1 italic line-clamp-2",children:["Doctor's Notes: ",b.doctor_notes]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-end sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-shrink-0",children:[(0,d.jsxs)("div",{className:"flex flex-col items-end",children:[(0,d.jsx)("label",{className:"text-xs text-slate-600 mb-1 whitespace-nowrap hidden sm:block",children:"Consultation Type"}),(0,d.jsx)("label",{className:"text-xs text-slate-600 mb-1 whitespace-nowrap sm:hidden",children:"Type"}),(0,d.jsx)("select",{value:aa,onChange:a=>ab(a.target.value),className:"px-2 py-1 text-xs border border-orange-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent w-24 sm:w-auto text-black",disabled:"approved"===b.status,children:Object.entries(s.CONSULTATION_TYPE_LABELS).map(([a,b])=>(0,d.jsx)("option",{value:a,className:"text-black",children:b},a))})]}),(0,d.jsx)("button",{onClick:c,className:"p-1.5 sm:p-2 hover:bg-orange-100 rounded-xl transition-colors flex-shrink-0",children:(0,d.jsx)(g.X,{className:"w-4 h-4 sm:w-5 sm:h-5 text-slate-600"})})]})]})}),(0,d.jsxs)("div",{className:"p-3 sm:p-6 space-y-4 sm:space-y-6 flex-1 overflow-y-auto",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-200",children:[(0,d.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,d.jsxs)("h4",{className:"text-lg font-semibold text-slate-800 flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full mr-3"}),"Primary Audio"]})}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("button",{onClick:as,disabled:K,className:"flex items-center space-x-3 px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:[K?(0,d.jsx)(r.Loader2,{className:"w-5 h-5 animate-spin"}):I?(0,d.jsx)(i.Pause,{className:"w-5 h-5"}):(0,d.jsx)(h.Play,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:K?"Loading...":I?"Pause Audio":"Play Audio"})]})})]}),"approved"!==b.status&&(0,d.jsxs)("div",{className:"bg-gradient-to-r from-teal-50 to-emerald-50 rounded-xl p-3 sm:p-5 border border-teal-200",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0",children:[(0,d.jsxs)("h4",{className:"text-base sm:text-lg font-semibold text-slate-800 flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-teal-500 rounded-full mr-2 sm:mr-3"}),"Additional Audio"]}),(0,d.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-full self-start",children:"Optional"})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 gap-2",children:[!S&&!U&&(0,d.jsxs)("button",{onClick:au,className:"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg text-sm font-medium transition-all duration-200 w-full sm:w-auto",children:[(0,d.jsx)(o.Mic,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Record"})]}),S&&(0,d.jsxs)("button",{onClick:()=>{W&&"recording"===W.state&&(W.stop(),T(!1),X(null))},className:"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg text-sm font-medium animate-pulse transition-all duration-200 w-full sm:w-auto",children:[(0,d.jsx)(p.Square,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Stop Recording"})]}),U&&(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto",children:[(0,d.jsxs)("button",{onClick:av,className:"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-all duration-200",children:[(0,d.jsx)(n.Upload,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Upload"})]}),(0,d.jsxs)("button",{onClick:()=>V(null),className:"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-all duration-200",children:[(0,d.jsx)(g.X,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Cancel"})]})]})]}),S&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 bg-red-100 px-3 py-2 rounded-lg",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,d.jsx)("span",{className:"text-sm text-red-700 font-medium",children:"Recording..."})]}),U&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 bg-green-100 px-3 py-2 rounded-lg",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm text-green-700 font-medium",children:"Ready to upload"})]}),b.additional_audio_urls&&Array.isArray(b.additional_audio_urls)&&b.additional_audio_urls.length>0&&(0,d.jsxs)("div",{className:"p-3 bg-white rounded-lg border",children:[(0,d.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Additional Audio Files (",b.additional_audio_urls.length,")"]}),(0,d.jsx)("div",{className:"space-y-2",children:b.additional_audio_urls.filter(a=>"string"==typeof a&&null!==a).map((a,c)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded border group",onMouseEnter:()=>"approved"!==b.status&&ah({type:"audio",url:a}),onMouseLeave:()=>ah(null),onTouchStart:()=>"approved"!==b.status&&ay("audio",a),onTouchEnd:az,onTouchCancel:az,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-teal-500 rounded-full"}),(0,d.jsxs)("span",{className:"text-sm text-gray-700",children:["Audio ",c+1]})]}),"approved"!==b.status&&ag?.type==="audio"&&ag?.url===a&&(0,d.jsxs)("button",{onClick:()=>aw(a),className:"flex items-center space-x-1 px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-all duration-200",title:"Delete audio",children:[(0,d.jsx)(q.Trash2,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"Delete"})]})]},c))})]})]})]})]}),b.image_urls&&Array.isArray(b.image_urls)&&b.image_urls.length>0&&(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:["Original Images (",b.image_urls.length," image",b.image_urls.length>1?"s":"",")"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:b.image_urls.filter(a=>"string"==typeof a&&null!==a).map((a,c)=>(0,d.jsxs)("div",{className:"relative group",onMouseEnter:()=>"approved"!==b.status&&ah({type:"image",url:a}),onMouseLeave:()=>ah(null),onTouchStart:()=>"approved"!==b.status&&ay("image",a),onTouchEnd:az,onTouchCancel:az,children:[(0,d.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded border overflow-hidden",children:(0,d.jsx)(z.default,{src:a,alt:`Original image ${c+1}`,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-200 cursor-pointer",width:96,height:96,priority:c<2,loading:c<2?"eager":"lazy",onError:b=>{console.error("Image failed to load:",a);let c=b.currentTarget;c.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5NiA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00OCA2NEw0MCA1Nkw0OCA0OEw1NiA1Nkw0OCA2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+",c.alt="Failed to load image"}})}),(0,d.jsx)("div",{className:"absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded",children:c+1}),"approved"!==b.status&&ag?.type==="image"&&ag?.url===a&&(0,d.jsx)("button",{onClick:()=>ax(a),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs shadow-lg transition-all duration-200 z-10",title:"Delete image",children:(0,d.jsx)(q.Trash2,{className:"w-3 h-3"})})]},c))})]}),(0,d.jsxs)("div",{className:"bg-orange-50 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-slate-800",children:"Additional Images"}),(0,d.jsx)("span",{className:"text-xs text-slate-500",children:"Optional"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,d.jsxs)("button",{onClick:()=>aj.current?.click(),className:"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm",children:[(0,d.jsx)(m.Camera,{className:"w-4 h-4 text-orange-500"}),(0,d.jsx)("span",{className:"text-orange-700",children:"Camera"})]}),(0,d.jsxs)("button",{onClick:()=>ai.current?.click(),className:"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm",children:[(0,d.jsx)(n.Upload,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"text-orange-700",children:"Upload"})]}),Q.length>0&&(0,d.jsxs)("span",{className:"text-xs text-orange-600 font-medium",children:["+",Q.length," image",Q.length>1?"s":""]})]}),Q.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:Q.map(a=>(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(z.default,{src:a.preview||"",alt:"Additional",className:"w-12 h-12 object-cover rounded border hover:w-24 hover:h-24 transition-all duration-200 cursor-pointer",width:48,height:48}),(0,d.jsx)("button",{onClick:()=>an(a.id),className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs",children:(0,d.jsx)(g.X,{className:"w-2.5 h-2.5"})})]},a.id))}),(0,d.jsx)("input",{ref:aj,type:"file",accept:"image/*",capture:"environment",onChange:a=>a.target.files&&am(a.target.files),className:"hidden"}),(0,d.jsx)("input",{ref:ai,type:"file",accept:"image/*",multiple:!0,onChange:a=>a.target.files&&am(a.target.files),className:"hidden"})]}),(0,d.jsxs)("div",{className:"bg-blue-50 rounded-xl p-3 sm:p-5 border border-blue-100",children:[(0,d.jsxs)("h4",{className:"text-base sm:text-lg font-semibold text-slate-800 mb-3 sm:mb-4 flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2 sm:mr-3"}),"Additional Notes (Optional)"]}),(0,d.jsx)("textarea",{value:ac,onChange:a=>ad(a.target.value),placeholder:"Add any additional context or instructions for the AI...",rows:3,className:"w-full px-3 sm:px-4 py-2 sm:py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm bg-white",disabled:"approved"===b.status}),(0,d.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"These notes will be included in the AI analysis along with the audio recordings."})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-3"}),"AI-Generated Patient Summary"]}),(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[("pending"===b.status||"generated"===b.status)&&(0,d.jsxs)("button",{onClick:ao,disabled:C,className:"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-purple-300 disabled:to-purple-400 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl",children:[(0,d.jsx)(j.Wand2,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:C?"Generating...":"generated"===b.status?"Regenerate Summary":"Generate Summary"}),(0,d.jsx)("span",{className:"sm:hidden",children:C?"Gen...":"Generate"})]}),G&&(0,d.jsxs)("button",{onClick:aq,className:"flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl",children:[(0,d.jsx)(l.Copy,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Copy"})]})]})]}),(0,d.jsx)("textarea",{value:C?Y:G,onChange:a=>H(a.target.value),placeholder:"pending"===b.status?'Click "Generate Summary" to create an AI-powered patient summary ...':"Edit the patient summary as needed...",className:"w-full min-h-[400px] max-h-[600px] p-4 border border-gray-200 rounded-lg resize-y focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white text-black text-sm font-medium flex-1",style:{height:(C?Y:G)?`${Math.max(400,Math.min(600,24*(C?Y:G).split("\n").length+80))}px`:"400px"}}),C&&(0,d.jsxs)("div",{className:"text-xs text-purple-600 mt-2 flex items-center",children:[(0,d.jsx)("span",{className:"inline-block w-2 h-2 bg-purple-500 animate-pulse mr-2 rounded-full"}),"Streaming response..."]})]}),O&&(0,d.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,d.jsx)("p",{className:"text-sm text-green-700 font-medium",children:O})]})})]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 px-4 sm:px-6 py-4 border-t border-orange-200/50 flex-shrink-0",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,d.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center w-fit ${"pending"===b.status?"bg-orange-100 text-orange-800 border border-orange-300":"generated"===b.status?"bg-emerald-100 text-emerald-800 border border-emerald-300":"bg-green-100 text-green-800 border border-green-300"}`,children:b.status.toUpperCase()}),(0,d.jsxs)("span",{className:"text-xs sm:text-sm text-slate-600",children:["Last updated: ",(0,t.formatDate)(b.updated_at)]})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3",children:[(0,d.jsx)("button",{onClick:c,className:"w-full sm:w-auto px-6 py-3 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-orange-50 transition-all duration-200 text-center",children:"Close"}),"approved"!==b.status&&G.trim()&&(0,d.jsxs)("button",{onClick:ap,disabled:E,className:"w-full sm:w-auto flex items-center justify-center space-x-2 px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 disabled:from-teal-300 disabled:to-emerald-400 transition-all duration-200 shadow-lg hover:shadow-xl",children:[(0,d.jsx)(k.Save,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:E?"Approving...":"Approve & Save"})]}),"approved"!==b.status&&!G.trim()&&(0,d.jsx)("div",{className:"w-full sm:w-auto px-6 py-3 text-sm text-center text-gray-500 bg-gray-100 rounded-xl border border-gray-200",children:"Generate or add summary to approve"})]})]})})]})});return $?(0,f.createPortal)(aA,document.body):null}},187627:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ConsultationsList:()=>o});var d=a.i(674420),e=a.i(722851),f=a.i(582126),g=a.i(314928),h=a.i(72732),i=a.i(614820),j=a.i(115285),k=a.i(397428),l=a.i(148530),m=a.i(901825),n=a.i(953676);function o({consultations:a,hasMore:b=!1,doctorName:c}){let[o,p]=(0,e.useState)(null),[q,r]=(0,e.useState)("all"),[s,t]=(0,e.useState)("all"),[u,v]=(0,e.useState)(""),[w,x]=(0,e.useState)(a),[y,z]=(0,e.useState)(b),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(1),E=(0,e.useRef)(null),F=(0,e.useRef)(null);(0,e.useEffect)(()=>{x(a),z(b),D(1)},[a,b]);let G=(0,e.useCallback)(async()=>{if(A||!y)return void console.log("Skipping load more - isLoading:",A,"hasMore:",y);console.log("Loading more consultations - page:",C+1),B(!0);try{let a=await (0,n.getConsultations)({page:C+1,pageSize:15,status:"all"===q?void 0:q});a.success&&a.data.consultations.length>0?(x(b=>[...b,...a.data.consultations]),z(a.data.hasMore),D(a=>a+1),console.log("Loaded",a.data.consultations.length,"more consultations")):(z(!1),console.log("No more consultations to load"))}catch(a){console.error("Failed to load more consultations:",a),z(!1)}finally{B(!1)}},[C,y,A,q]);(0,e.useEffect)(()=>{if(!E.current||!y||A){F.current&&(F.current.disconnect(),F.current=null);return}return F.current&&F.current.disconnect(),F.current=new IntersectionObserver(a=>{a[0].isIntersecting&&!A&&y&&w.length>0&&(console.log("Info page intersection observer triggered - loading more"),G())},{threshold:.1}),F.current.observe(E.current),()=>{F.current&&(F.current.disconnect(),F.current=null)}},[G,y,A,w.length]);let H=a=>{if("all"===s)return a;let b=new Date,c=new Date(b);return c.setDate(c.getDate()-1),a.filter(a=>{let d=new Date(a.created_at);switch(s){case"today":return d.toDateString()===b.toDateString();case"yesterday":return d.toDateString()===c.toDateString();case"custom":if(!u)return!0;let e=new Date(u);return d.toDateString()===e.toDateString();default:return!0}})},I=H(w).filter(a=>"all"===q||a.status===q),J=a=>{switch(a){case"pending":return(0,d.jsx)(f.Clock,{className:"w-4 h-4 text-orange-600"});case"generated":return(0,d.jsx)(g.FileText,{className:"w-4 h-4 text-emerald-600"});case"approved":return(0,d.jsx)(h.CheckCircle,{className:"w-4 h-4 text-green-600"});default:return(0,d.jsx)(f.Clock,{className:"w-4 h-4 text-slate-500"})}},K=a=>{switch(a){case"pending":return"Pending Review";case"generated":return"Summary Generated";case"approved":return"Approved";default:return"Unknown"}},L=a=>{switch(a){case"pending":return"bg-orange-100 text-orange-800 border border-orange-300";case"generated":return"bg-emerald-100 text-emerald-800 border border-emerald-300";case"approved":return"bg-green-100 text-green-800 border border-green-300";default:return"bg-slate-100 text-slate-800 border border-slate-300"}};return 0===w.length?(0,d.jsxs)("div",{className:"p-6 text-center",children:[(0,d.jsx)(g.FileText,{className:"mx-auto h-12 w-12 text-slate-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-800",children:"No consultations"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"Get started by recording a consultation on the mobile interface."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"border-b border-orange-200 mb-6",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,d.jsx)("nav",{className:"-mb-px flex space-x-2 sm:space-x-8 overflow-x-auto",children:["all","pending","generated","approved"].map(a=>(0,d.jsxs)("button",{onClick:()=>r(a),className:`whitespace-nowrap py-2 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-200 flex-shrink-0 ${q===a?"border-teal-500 text-teal-600":"border-transparent text-slate-800 hover:text-slate-900 hover:border-orange-300"}`,children:[a.charAt(0).toUpperCase()+a.slice(1),H(w).filter(b=>"all"===a||b.status===a).length>0&&(0,d.jsx)("span",{className:`ml-1 sm:ml-2 py-0.5 px-1 sm:px-2 rounded-full text-xs ${q===a?"bg-teal-100 text-teal-700":"bg-orange-100 text-slate-800"}`,children:H(w).filter(b=>"all"===a||b.status===a).length})]},a))}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,d.jsx)(k.Filter,{className:"w-4 h-4 text-slate-600"}),(0,d.jsxs)("select",{value:s,onChange:a=>{let b=a.target.value;t(b),"custom"!==b&&v("")},className:"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500",children:[(0,d.jsx)("option",{value:"all",children:"All Dates"}),(0,d.jsx)("option",{value:"today",children:"Today"}),(0,d.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,d.jsx)("option",{value:"custom",children:"Custom Date"})]}),"custom"===s&&(0,d.jsx)("input",{type:"date",value:u,onChange:a=>v(a.target.value),className:"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"})]})]})}),(0,d.jsx)("div",{className:"space-y-4",children:I.map(a=>(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm border border-orange-200/50 rounded-lg p-3 sm:p-4 hover:shadow-lg hover:bg-white/90 transition-all duration-200 hover:scale-[1.02]",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0",children:[(0,d.jsxs)("div",{className:"flex items-start sm:items-center space-x-3 sm:space-x-4 min-w-0 flex-1",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-0.5 sm:mt-0",children:J(a.status)}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-slate-800 truncate",children:a.patient_name||`Patient #${a.patient_number||"N/A"}`}),(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start ${L(a.status)}`,children:K(a.status)})]}),(0,d.jsxs)("div",{className:"mt-1 flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-slate-600",children:[(0,d.jsxs)("span",{className:"truncate",children:["Submitted by: ",a.submitted_by]}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,d.jsx)("span",{children:(0,l.formatRelativeTime)(a.created_at)}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:(0,l.formatDate)(a.created_at)})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,d.jsx)("button",{onClick:()=>p(a),className:`inline-flex items-center px-2 sm:px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-md hover:shadow-lg transition-all duration-200 ${"pending"===a.status?"border-transparent text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 focus:ring-teal-500":"generated"===a.status?"border-transparent text-white bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 focus:ring-blue-500":"border-transparent text-white bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 focus:ring-green-500"}`,children:"pending"===a.status?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.Wand2,{className:"w-3 h-3 mr-1"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Generate Summary"}),(0,d.jsx)("span",{className:"sm:hidden",children:"Generate"})]}):"generated"===a.status?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.Eye,{className:"w-3 h-3 mr-1"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Review Summary"}),(0,d.jsx)("span",{className:"sm:hidden",children:"Review"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.Eye,{className:"w-3 h-3 mr-1"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"View Approved"}),(0,d.jsx)("span",{className:"sm:hidden",children:"View"})]})}),(0,d.jsxs)("button",{onClick:()=>p(a),className:"inline-flex items-center px-2 sm:px-3 py-1.5 border border-orange-300 hover:border-teal-400 text-xs font-medium rounded text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200",children:[(0,d.jsx)(i.Eye,{className:"w-3 h-3 mr-1"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"View Details"}),(0,d.jsx)("span",{className:"sm:hidden",children:"Details"})]})]})]})},a.id))}),y&&(0,d.jsx)("div",{ref:E,className:"flex justify-center py-4",children:A?(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-slate-600",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"}),(0,d.jsx)("span",{children:"Loading more consultations..."})]}):(0,d.jsx)("div",{className:"text-sm text-slate-500",children:"Scroll to load more"})}),0===I.length&&"all"!==q&&(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsxs)("p",{className:"text-sm text-slate-600",children:['No consultations with status "',q,'".']})})]}),o&&(0,d.jsx)(m.ConsultationModal,{consultation:o,onClose:()=>p(null),onConsultationUpdate:a=>{p(a),x(b=>b.map(b=>b.id===a.id?a:b))},doctorName:c})]})}},648788:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],c=(0,d.default)("triangle-alert",b)}},380923:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AlertTriangle:()=>d.default});var d=a.i(648788)},50905:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({QuotaWarningModal:()=>k});var d=a.i(674420),e=a.i(767332),f=a.i(380923),g=a.i(584543),h=a.i(177286),i=a.i(582126),j=a.i(72732);function k({isOpen:a,onClose:b,quotaInfo:c,onContactFounder:k,isRequesting:l=!1,hasRequested:m=!1}){if(!a)return null;let n=c.quota_percentage>=95;return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,d.jsxs)("div",{className:`p-6 text-white relative ${n?"bg-gradient-to-r from-red-500 to-red-600":"bg-gradient-to-r from-orange-500 to-amber-600"}`,children:[(0,d.jsx)("button",{onClick:b,className:"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors",children:(0,d.jsx)(e.X,{className:"w-5 h-5"})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(f.AlertTriangle,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h2",{className:"text-2xl font-bold mb-2",children:n?"Quota Limit Reached!":"Quota Warning"}),(0,d.jsx)("p",{className:"text-orange-100",children:n?"You have reached your consultation limit":"You are approaching your consultation limit"})]})]}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-gray-50 to-slate-50 p-4 rounded-lg border border-gray-200 mb-6",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-3xl font-bold text-gray-900 mb-1",children:[c.quota_used," / ",c.monthly_quota]}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mb-3",children:"Consultations Used"}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,d.jsx)("div",{className:`h-3 rounded-full transition-all duration-300 ${n?"bg-red-500":"bg-orange-500"}`,style:{width:`${Math.min(c.quota_percentage,100)}%`}})}),(0,d.jsxs)("div",{className:`text-sm font-medium ${n?"text-red-600":"text-orange-600"}`,children:[c.quota_percentage.toFixed(0),"% Used"]})]})}),(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:n?"To continue using AI consultations, please contact our founder to upgrade your plan or increase your quota.":"You have only a few consultations remaining. Consider contacting our founder to discuss upgrading your plan."}),(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,d.jsx)(g.Phone,{className:"w-4 h-4 text-blue-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Contact Founder"})]}),(0,d.jsx)("div",{className:"text-lg font-bold text-blue-900 mb-1",children:"+91 8921628177"}),(0,d.jsx)("p",{className:"text-xs text-blue-700",children:"Available Mon-Sat, 9 AM - 8 PM IST"})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[m?(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,d.jsx)(j.CheckCircle,{className:"w-5 h-5 text-green-600"}),(0,d.jsx)("span",{className:"text-green-800 font-medium",children:"Request Sent Successfully!"})]}):(0,d.jsx)("button",{onClick:k,disabled:l,className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2",children:l?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,d.jsx)("span",{children:"Sending Request..."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.MessageCircle,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Request Callback"})]})}),(0,d.jsxs)("a",{href:"tel:+918921628177",className:"w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2",children:[(0,d.jsx)(g.Phone,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Call Now"})]}),!n&&(0,d.jsx)("button",{onClick:b,className:"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-200",children:"Continue with Trial"})]}),(0,d.jsx)("div",{className:"mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)(i.Clock,{className:"w-4 h-4"}),(0,d.jsxs)("span",{children:["Quota resets in ",c.days_until_reset," day",1!==c.days_until_reset?"s":""]})]})})]})]})})}},695906:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({hasActiveContactRequest:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("405d7138432cf8f813249d6a1a52fcfca61c62a7ed",d.callServer,void 0,d.findSourceMapURL,"hasActiveContactRequest")},501285:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({QuotaCard:()=>n});var d=a.i(674420),e=a.i(722851),f=a.i(953731),g=a.i(70763),h=a.i(380923),i=a.i(72732),j=a.i(425181),k=a.i(50905),l=a.i(293957),m=a.i(695906);function n({quota:a,doctorId:b}){let[c,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!1);(0,e.useEffect)(()=>{a.quota_percentage>=80&&n(!0),(async()=>{let a=await (0,m.hasActiveContactRequest)(b);a.success&&a.data&&r(!0)})()},[a.quota_percentage,b]);let s=async()=>{p(!0);try{(await (0,l.createContactRequest)(b,`Quota upgrade request - Currently at ${a.quota_percentage}% (${a.quota_used}/${a.monthly_quota} consultations used)`)).success&&r(!0)}catch(a){console.error("Error creating contact request:",a)}finally{p(!1)}},t=()=>a.quota_percentage>=90?"text-red-600":a.quota_percentage>=70?"text-orange-600":"text-emerald-600",u=a.quota_percentage>=90?h.AlertTriangle:a.quota_percentage>=70?f.Zap:i.CheckCircle;return(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-slate-800",children:"Consultations"}),(0,d.jsx)("div",{className:`w-6 h-6 rounded-lg ${a.quota_percentage>=90?"bg-red-100":a.quota_percentage>=70?"bg-orange-100":"bg-emerald-100"} flex items-center justify-center shadow-md`,children:(0,d.jsx)(u,{className:`w-4 h-4 ${t()}`})})]}),(0,d.jsxs)("div",{className:"mb-4 flex-1 space-y-3",children:[(0,d.jsxs)("div",{className:"text-center bg-gradient-to-br from-slate-50 to-orange-50 rounded-lg p-3 sm:p-4",children:[(0,d.jsx)("div",{className:"text-xs sm:text-sm text-slate-600 mb-1",children:"Monthly Usage"}),(0,d.jsxs)("div",{className:"text-base sm:text-lg font-bold text-slate-800",children:[a.quota_used," / ",a.monthly_quota]}),(0,d.jsx)("div",{className:"w-full bg-orange-100 rounded-full h-2 sm:h-2.5 mt-2",children:(0,d.jsx)("div",{className:`h-2 sm:h-2.5 rounded-full transition-all duration-300 ${a.quota_percentage>=90?"bg-red-500":a.quota_percentage>=70?"bg-orange-500":"bg-emerald-500"}`,style:{width:`${Math.min(a.quota_percentage,100)}%`}})}),(0,d.jsx)("div",{className:"mt-1",children:(0,d.jsxs)("span",{className:`text-sm sm:text-base font-bold ${t()}`,children:[a.quota_percentage,"%"]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-2 sm:p-3 text-center",children:[(0,d.jsx)(j.TrendingUp,{className:"w-4 h-4 sm:w-5 sm:h-5 text-teal-600 mx-auto mb-1"}),(0,d.jsx)("div",{className:"text-xs sm:text-sm text-slate-600",children:"Remaining"}),(0,d.jsx)("div",{className:"text-sm sm:text-base font-bold text-slate-800",children:a.quota_remaining})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-2 sm:p-3 text-center",children:[(0,d.jsx)(g.Calendar,{className:"w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mx-auto mb-1"}),(0,d.jsx)("div",{className:"text-xs sm:text-sm text-slate-600",children:"Resets in"}),(0,d.jsxs)("div",{className:"text-sm sm:text-base font-bold text-slate-800",children:[a.days_until_reset," day",1!==a.days_until_reset?"s":""]})]})]})]}),a.quota_percentage>=90&&(0,d.jsx)("div",{className:"p-2 bg-red-50 border border-red-200 rounded",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(h.AlertTriangle,{className:"w-3 h-3 text-red-500 mt-0.5 mr-1 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-xs text-red-700",children:[(0,d.jsx)("p",{className:"font-medium",children:"Quota almost exhausted!"}),(0,d.jsx)("p",{children:"Contact admin to increase limit."})]})]})}),a.quota_percentage>=70&&a.quota_percentage<90&&(0,d.jsx)("div",{className:"p-2 bg-orange-50 border border-orange-200 rounded",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(f.Zap,{className:"w-3 h-3 text-orange-500 mt-0.5 mr-1 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-xs text-orange-700",children:[(0,d.jsx)("p",{className:"font-medium",children:"High usage detected"}),(0,d.jsx)("p",{children:"Monitor AI generation usage."})]})]})}),(0,d.jsx)(k.QuotaWarningModal,{isOpen:c,onClose:()=>n(!1),quotaInfo:a,onContactFounder:s,isRequesting:o,hasRequested:q})]})}},139524:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({generateReferralLink:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("400d27483b0ad440768404c34690724d71663b6083",d.callServer,void 0,d.findSourceMapURL,"generateReferralLink")},844186:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ReferralStats:()=>m});var d=a.i(674420),e=a.i(722851),f=a.i(485878),g=a.i(605754),h=a.i(533784),i=a.i(86111),j=a.i(24353),k=a.i(321167),l=a.i(139524);function m({doctorId:a}){let[b,c]=(0,e.useState)(null),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!0);(0,e.useEffect)(()=>{(async()=>{let[b,d]=await Promise.all([(0,k.getReferralInfo)(a),(0,l.generateReferralLink)(a)]);b.success&&c(b.data),d.success&&n(d.data),r(!1)})()},[a]);let s=async()=>{m&&(await navigator.clipboard.writeText(m),p(!0),setTimeout(()=>p(!1),2e3))};if(q)return(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm shadow border border-orange-200/50 rounded-lg p-4 h-full",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3"})]})});if(!b)return null;let t=()=>b.successful_referrals>=3?(0,d.jsx)(h.Crown,{className:"w-5 h-5 text-amber-500"}):(0,d.jsx)(g.Gift,{className:"w-5 h-5 text-emerald-500"});return(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[t(),(0,d.jsx)("h3",{className:"text-sm font-medium text-slate-800",children:"Referral Program"})]}),(0,d.jsx)("div",{className:"w-6 h-6 rounded-lg bg-emerald-100 flex items-center justify-center shadow-md",children:t()})]}),(0,d.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-emerald-50 to-green-50 p-3 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-xs text-emerald-700 font-medium mb-1",children:"Success"}),(0,d.jsx)("div",{className:"text-lg font-bold text-emerald-800",children:b.successful_referrals})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-violet-50 p-3 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-xs text-purple-700 font-medium mb-1",children:"Earned"}),(0,d.jsxs)("div",{className:"text-sm font-bold text-purple-800",children:["₹",b.discount_earned.toFixed(0)]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-cyan-50 p-3 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-xs text-blue-700 font-medium mb-1",children:"Total"}),(0,d.jsx)("div",{className:"text-lg font-bold text-blue-800",children:b.total_referrals})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-yellow-50 p-3 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-xs text-amber-700 font-medium mb-1",children:"Pending"}),(0,d.jsx)("div",{className:"text-lg font-bold text-amber-800",children:b.pending_referrals})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-slate-50 to-gray-50 p-3 rounded-lg",children:[(0,d.jsx)("div",{className:"text-xs text-slate-600 mb-2 text-center",children:"Your Referral Link"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"text",value:m,readOnly:!0,className:"flex-1 px-2 py-1 rounded text-xs bg-white/70 text-slate-800 focus:outline-none"}),(0,d.jsx)("button",{onClick:s,className:"px-3 py-1 bg-teal-600 hover:bg-teal-700 text-white rounded text-xs transition-colors font-medium",children:o?(0,d.jsx)(j.Check,{className:"w-3 h-3"}):(0,d.jsx)(i.Copy,{className:"w-3 h-3"})})]})]}),b.referred_by&&(0,d.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsx)(f.Users,{className:"w-3 h-3 text-blue-600"}),(0,d.jsxs)("span",{className:"text-xs text-blue-700",children:["Referred by ",(0,d.jsx)("span",{className:"font-medium",children:b.referred_by.name})]})]})})]})]})}}};

//# sourceMappingURL=_df49f54c._.js.map