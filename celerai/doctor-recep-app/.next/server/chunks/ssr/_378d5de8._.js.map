{"version": 3, "sources": ["turbopack:///[project]/src/components/mobile/audio-recorder.tsx", "turbopack:///[project]/src/components/mobile/image-capture.tsx", "turbopack:///[project]/node_modules/lucide-react/src/icons/send.ts", "turbopack:///[project]/src/components/mobile/submission-form.tsx", "turbopack:///[project]/src/components/mobile/recording-interface.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Mic, Square } from 'lucide-react'\nimport { AudioRecordingState } from '@/lib/types'\nimport { formatDuration, supportsAudioRecording } from '@/lib/utils'\nimport { validateFile } from '@/lib/storage'\n\ninterface AudioRecorderProps {\n  audioState: AudioRecordingState\n  onStateChange: (newState: Partial<AudioRecordingState>) => void\n}\n\nexport function AudioRecorder({ audioState, onStateChange }: AudioRecorderProps) {\n  // OPTIMISTIC: Removed isPlaying state and audio playback refs since no local playback\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null)\n  const streamRef = useRef<MediaStream | null>(null)\n  const intervalRef = useRef<NodeJS.Timeout | null>(null)\n  const startTimeRef = useRef<number>(0)\n\n  useEffect(() => {\n    // Check if audio recording is supported\n    if (!supportsAudioRecording()) {\n      onStateChange({ error: 'Audio recording is not supported on this device' })\n    }\n\n    return () => {\n      // Cleanup on unmount\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current)\n      }\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop())\n      }\n    }\n  }, [onStateChange])\n\n  // Get the best supported audio format for this device\n  const getSupportedMimeType = () => {\n    // Detect Safari and prioritize Safari-compatible formats\n    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)\n\n    const types = isSafari ? [\n      // Safari-compatible formats first\n      'audio/mp4',\n      'audio/mpeg',\n      'audio/webm',\n      'audio/webm;codecs=opus'\n    ] : [\n      // Other browsers: prefer WebM with Opus\n      'audio/webm;codecs=opus',\n      'audio/webm',\n      'audio/mp4',\n      'audio/mpeg'\n    ]\n\n    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'\n  }\n\n  const startRecording = async () => {\n    try {\n      onStateChange({ error: undefined })\n\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n        }\n      })\n\n      streamRef.current = stream\n\n      const supportedMimeType = getSupportedMimeType()\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: supportedMimeType\n      })\n\n      mediaRecorderRef.current = mediaRecorder\n\n      const chunks: Blob[] = []\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunks.push(event.data)\n        }\n      }\n\n      mediaRecorder.onstop = async () => {\n        const audioBlob = new Blob(chunks, { type: supportedMimeType })\n\n        // Generate appropriate file extension\n        const getFileExtension = (mimeType: string) => {\n          if (mimeType.includes('webm')) return 'webm'\n          if (mimeType.includes('mp4')) return 'mp4'\n          if (mimeType.includes('mpeg')) return 'mp3'\n          return 'webm'\n        }\n\n        const extension = getFileExtension(supportedMimeType)\n\n        // Convert blob to File object for upload\n        const audioFile = new File([audioBlob], `recording_${Date.now()}.${extension}`, {\n          type: supportedMimeType\n        })\n\n        // Validate the audio file\n        const validation = validateFile(audioFile, 'audio')\n        if (!validation.valid) {\n          onStateChange({\n            error: validation.error,\n            isRecording: false,\n          })\n          return\n        }\n\n        onStateChange({\n          audioBlob,\n          audioFile,\n          isRecording: false,\n        })\n\n        // Stop all tracks\n        stream.getTracks().forEach(track => track.stop())\n      }\n\n      mediaRecorder.start()\n      startTimeRef.current = Date.now()\n\n      onStateChange({\n        isRecording: true,\n        duration: 0,\n      })\n\n      // Start duration timer\n      intervalRef.current = setInterval(() => {\n        const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000)\n        onStateChange({ duration: elapsed })\n      }, 1000)\n\n    } catch (error) {\n      console.error('Error starting recording:', error)\n      onStateChange({\n        error: 'Failed to start recording. Please check microphone permissions.',\n        isRecording: false,\n      })\n    }\n  }\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && audioState.isRecording) {\n      mediaRecorderRef.current.stop()\n\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current)\n        intervalRef.current = null\n      }\n    }\n  }\n\n  // OPTIMISTIC: Clear recording without audio playback cleanup\n  const clearRecording = () => {\n    onStateChange({\n      audioBlob: undefined,\n      duration: 0,\n      error: undefined,\n    })\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Recording Controls */}\n      <div className=\"flex flex-col items-center space-y-4\">\n        {!audioState.audioBlob ? (\n          // Recording interface\n          <div className=\"flex flex-col items-center space-y-4\">\n            <button\n              onClick={audioState.isRecording ? stopRecording : startRecording}\n              disabled={!!audioState.error}\n              className={`w-20 h-20 rounded-full flex items-center justify-center text-white transition-all duration-200 ${\n                audioState.isRecording\n                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'\n                  : 'bg-blue-500 hover:bg-blue-600'\n              } disabled:opacity-50 disabled:cursor-not-allowed`}\n            >\n              {audioState.isRecording ? (\n                <Square className=\"w-8 h-8\" />\n              ) : (\n                <Mic className=\"w-8 h-8\" />\n              )}\n            </button>\n\n            <div className=\"text-center\">\n              <p className=\"text-sm font-medium text-gray-700\">\n                {audioState.isRecording ? 'Recording...' : 'Tap to start recording'}\n              </p>\n              {audioState.isRecording && (\n                <p className=\"text-lg font-mono text-red-600\">\n                  {formatDuration(audioState.duration)}\n                </p>\n              )}\n            </div>\n          </div>\n        ) : (\n          // OPTIMISTIC: No playback interface - just show completion status\n          <div className=\"flex flex-col items-center space-y-4 w-full\">\n            <div className=\"flex items-center justify-center space-x-4 p-4 bg-green-50 rounded-lg border border-green-200 w-full\">\n              <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                <Mic className=\"w-4 h-4 text-green-600\" />\n              </div>\n\n              <div className=\"flex-1 text-center\">\n                <p className=\"text-sm font-medium text-green-700\">\n                  Recording completed\n                </p>\n                <p className=\"text-lg font-mono text-green-600\">\n                  {formatDuration(audioState.duration)}\n                </p>\n              </div>\n\n              <button\n                onClick={clearRecording}\n                className=\"px-4 py-2 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-md\"\n              >\n                Clear\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Error Display */}\n      {audioState.error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <p className=\"text-sm text-red-700\">{audioState.error}</p>\n        </div>\n      )}\n\n      {/* Recording Tips */}\n      {!audioState.audioBlob && !audioState.isRecording && (\n        <div className=\"bg-gray-50 rounded-md p-3\">\n          <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Recording Tips:</h4>\n          <ul className=\"text-xs text-gray-600 space-y-1\">\n            <li>• Speak clearly and at normal volume</li>\n            <li>• Minimize background noise</li>\n            <li>• Hold device 6-12 inches from your mouth</li>\n            <li>• Include patient symptoms, diagnosis, and treatment</li>\n          </ul>\n        </div>\n      )}\n    </div>\n  )\n}\n", "'use client'\n\nimport { useRef } from 'react'\nimport { Camera, Upload, X } from 'lucide-react'\nimport { ImageCaptureState, ImageFile } from '@/lib/types'\n// import { fileToBase64, supportsCamera } from '@/lib/utils' // <--- REMOVE fileToBase64\nimport { supportsCamera } from '@/lib/utils' // <--- <PERSON><PERSON><PERSON> supportsCamera\nimport Image from 'next/image'\n\ninterface ImageCaptureProps {\n  imageState: ImageCaptureState\n  onStateChange: (newState: Partial<ImageCaptureState>) => void\n  isMobile?: boolean // Keep if used, otherwise remove\n}\n\nexport function ImageCapture({ imageState, onStateChange, isMobile: _isMobile }: ImageCaptureProps) {\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const cameraInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = async (files: FileList) => {\n    try {\n      onStateChange({ error: undefined })\n\n      const newImages: ImageFile[] = []\n\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i]\n\n        // Validate file type\n        if (!file.type.startsWith('image/')) {\n          onStateChange({ error: `File ${file.name} is not a valid image` })\n          return\n        }\n\n        // Validate file size (max 10MB per file)\n        if (file.size > 10 * 1024 * 1024) {\n          onStateChange({ error: `File ${file.name} is too large. Please select files under 10MB.` })\n          return\n        }\n\n        // OPTIMISTIC: No local preview state - store files for upload only\n        // Final state will be shown after upload completes\n        newImages.push({\n          id: `${Date.now()}-${i}`,\n          file: file, // Store the raw File object for upload\n          name: file.name,\n          type: file.type,\n          size: file.size\n          // No preview property - no local state display\n        })\n      }\n\n      onStateChange({\n        images: [...imageState.images, ...newImages]\n      })\n    } catch (error) {\n      console.error('Error processing images:', error)\n      onStateChange({ error: 'Failed to process images. Please try again.' })\n    }\n  }\n\n\n  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files\n    if (files && files.length > 0) {\n      handleFileSelect(files)\n    }\n  }\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files\n    if (files && files.length > 0) {\n      handleFileSelect(files)\n    }\n  }\n\n  const removeImage = (imageId: string) => {\n    // OPTIMISTIC: No preview URLs to revoke since we don't create them\n    onStateChange({\n      images: imageState.images.filter(img => img.id !== imageId),\n      error: undefined,\n    })\n  }\n\n  const clearAllImages = () => {\n    // OPTIMISTIC: No preview URLs to revoke since we don't create them\n    onStateChange({\n      images: [],\n      error: undefined,\n    })\n\n    // Reset file inputs\n    if (fileInputRef.current) fileInputRef.current.value = ''\n    if (cameraInputRef.current) cameraInputRef.current.value = ''\n  }\n\n  const openCamera = () => {\n    cameraInputRef.current?.click()\n  }\n\n  const openFileSelector = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Upload Controls */}\n      <div className=\"grid grid-cols-1 gap-3\">\n        {/* Camera Capture */}\n        {supportsCamera() && (\n          <button\n            onClick={openCamera}\n            className=\"flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors\"\n          >\n            <Camera className=\"w-6 h-6 text-gray-400\" />\n            <span className=\"text-sm font-medium text-gray-700\">\n              Take Photo\n            </span>\n          </button>\n        )}\n\n        {/* File Upload */}\n        <button\n          onClick={openFileSelector}\n          className=\"flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors\"\n        >\n          <Upload className=\"w-6 h-6 text-gray-400\" />\n          <span className=\"text-sm font-medium text-gray-700\">\n            Upload from Gallery\n          </span>\n        </button>\n      </div>\n\n      {/* Hidden file inputs */}\n      <input\n        ref={cameraInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        capture=\"environment\"\n        onChange={handleCameraCapture}\n        className=\"hidden\"\n      />\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        multiple\n        onChange={handleFileUpload}\n        className=\"hidden\"\n      />\n\n      {/* OPTIMISTIC: No local preview - show only file count for feedback */}\n      {imageState.images.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h4 className=\"text-sm font-medium text-gray-700\">\n              Selected Images ({imageState.images.length})\n            </h4>\n            <button\n              onClick={clearAllImages}\n              className=\"text-xs text-red-600 hover:text-red-800 underline\"\n            >\n              Clear All\n            </button>\n          </div>\n\n          <div className=\"space-y-2\">\n            {imageState.images.map((image) => (\n              <div key={image.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg border\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <Camera className=\"w-4 h-4 text-blue-600\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900 truncate max-w-[200px]\">\n                      {image.name}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      {(image.size / 1024 / 1024).toFixed(1)} MB\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => removeImage(image.id)}\n                  className=\"w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Error Display */}\n      {imageState.error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <p className=\"text-sm text-red-700\">{imageState.error}</p>\n        </div>\n      )}\n    </div>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n];\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('send', __iconNode);\n\nexport default Send;\n", "'use client'\n\nimport { useState } from 'react'\nimport { Send, CheckCircle, AlertCircle } from 'lucide-react'\nimport { createConsultationWithFiles } from '@/lib/actions/consultations'\nimport { retryWithBackoff } from '@/lib/utils'\nimport { ImageFile } from '@/lib/types'\n\ninterface SubmissionFormProps {\n  audioFile?: File\n  imageFiles?: ImageFile[]\n  canSubmit: boolean\n  isSubmitting: boolean\n  onSubmissionStateChange: (isSubmitting: boolean) => void\n}\n\nexport function SubmissionForm({\n  audioFile,\n  imageFiles,\n  canSubmit,\n  isSubmitting,\n  onSubmissionStateChange,\n}: SubmissionFormProps) {\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')\n  const [errorMessage, setErrorMessage] = useState<string>('')\n  const [successMessage, setSuccessMessage] = useState<string>('')\n\n  const handleSubmit = async () => {\n    if (!audioFile || isSubmitting) return\n\n    onSubmissionStateChange(true)\n    setSubmitStatus('idle')\n    setErrorMessage('')\n    setSuccessMessage('')\n\n    try {\n      // Extract File objects from ImageFile array\n      const imageFileObjects = imageFiles?.map(img => img.file) || []\n\n      // Submit with retry logic\n      const result = await retryWithBackoff(\n        () => createConsultationWithFiles(\n          audioFile,\n          imageFileObjects,\n          [], // No additional audio files for now\n          'doctor',\n          undefined, // No doctor notes for mobile submission\n          'outpatient' // Default consultation type\n        ),\n        {\n          maxAttempts: 3,\n          baseDelay: 1000,\n          maxDelay: 5000,\n        }\n      )\n\n      if (result.success) {\n        setSubmitStatus('success')\n        setSuccessMessage(\n          `Consultation submitted successfully! Patient #${result.data?.patient_number || 'N/A'} is ready for processing.`\n        )\n\n        // Auto-clear success message after 5 seconds\n        setTimeout(() => {\n          setSubmitStatus('idle')\n          setSuccessMessage('')\n        }, 5000)\n      } else {\n        setSubmitStatus('error')\n        setErrorMessage(result.error || 'Failed to submit consultation')\n      }\n    } catch (error) {\n      console.error('Submission error:', error)\n      setSubmitStatus('error')\n      setErrorMessage('Network error. Please check your connection and try again.')\n    } finally {\n      onSubmissionStateChange(false)\n    }\n  }\n\n  const getSubmitButtonContent = () => {\n    if (isSubmitting) {\n      return (\n        <>\n          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n          <span>Submitting...</span>\n        </>\n      )\n    }\n\n    if (submitStatus === 'success') {\n      return (\n        <>\n          <CheckCircle className=\"w-4 h-4\" />\n          <span>Submitted!</span>\n        </>\n      )\n    }\n\n    return (\n      <>\n        <Send className=\"w-4 h-4\" />\n        <span>Submit Consultation</span>\n      </>\n    )\n  }\n\n  const getSubmitButtonClass = () => {\n    const baseClass = \"w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors\"\n\n    if (!canSubmit) {\n      return `${baseClass} bg-gray-300 text-gray-500 cursor-not-allowed`\n    }\n\n    if (isSubmitting) {\n      return `${baseClass} bg-blue-400 text-white cursor-not-allowed`\n    }\n\n    if (submitStatus === 'success') {\n      return `${baseClass} bg-green-500 text-white`\n    }\n\n    return `${baseClass} bg-blue-500 hover:bg-blue-600 text-white`\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Submission Summary */}\n      <div className=\"bg-gray-50 rounded-md p-3\">\n        <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Ready to Submit:</h4>\n        <div className=\"space-y-1 text-xs text-gray-600\">\n          <div className=\"flex items-center justify-between\">\n            <span>Audio Recording:</span>\n            <span className={audioFile ? 'text-green-600' : 'text-red-600'}>\n              {audioFile ? '✓ Ready' : '✗ Required'}\n            </span>\n          </div>\n          <div className=\"flex items-center justify-between\">\n            <span>Handwritten Notes:</span>\n            <span className={imageFiles && imageFiles.length > 0 ? 'text-green-600' : 'text-gray-500'}>\n              {imageFiles && imageFiles.length > 0 ? `✓ ${imageFiles.length} image(s)` : '○ Optional'}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Button */}\n      <button\n        onClick={handleSubmit}\n        disabled={!canSubmit || isSubmitting}\n        className={getSubmitButtonClass()}\n      >\n        {getSubmitButtonContent()}\n      </button>\n\n      {/* Status Messages */}\n      {submitStatus === 'success' && successMessage && (\n        <div className=\"bg-green-50 border border-green-200 rounded-md p-3\">\n          <div className=\"flex items-start space-x-2\">\n            <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\" />\n            <p className=\"text-sm text-green-700\">{successMessage}</p>\n          </div>\n        </div>\n      )}\n\n      {submitStatus === 'error' && errorMessage && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-start space-x-2\">\n            <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n            <div className=\"flex-1\">\n              <p className=\"text-sm text-red-700\">{errorMessage}</p>\n              <button\n                onClick={handleSubmit}\n                disabled={!canSubmit || isSubmitting}\n                className=\"mt-2 text-xs text-red-600 hover:text-red-800 underline\"\n              >\n                Try again\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Instructions */}\n      {submitStatus === 'idle' && (\n        <div className=\"bg-blue-50 rounded-md p-3\">\n          <p className=\"text-xs text-blue-700\">\n            <strong>Next steps:</strong> After submission, your receptionist can review and generate\n            the patient summary from the dashboard. The consultation will be assigned a patient number\n            for easy tracking.\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { AudioRecorder } from './audio-recorder'\nimport { ImageCapture } from './image-capture'\nimport { SubmissionForm } from './submission-form'\nimport { AudioRecordingState, ImageCaptureState } from '@/lib/types'\n\nexport function MobileRecordingInterface() {\n  const [audioState, setAudioState] = useState<AudioRecordingState>({\n    isRecording: false,\n    duration: 0,\n  })\n\n  const [imageState, setImageState] = useState<ImageCaptureState>({ images: [], error: null })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const handleAudioStateChange = (newState: Partial<AudioRecordingState>) => {\n    setAudioState(prev => ({ ...prev, ...newState }))\n  }\n\n  const handleImageStateChange = (newState: Partial<ImageCaptureState>) => {\n    setImageState(prev => ({ ...prev, ...newState }))\n  }\n\n  const canSubmit = !!audioState.audioFile && !isSubmitting\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Instructions */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h2 className=\"text-lg font-medium text-blue-900 mb-2\">\n          Record Patient Consultation\n        </h2>\n        <p className=\"text-sm text-blue-700\">\n          1. Tap the microphone to start recording your consultation\n          <br />\n          2. Optionally, take a photo of any handwritten notes\n          <br />\n          3. Submit to generate patient summary\n        </p>\n      </div>\n\n      {/* Audio Recording */}\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6\">\n        <h3 className=\"text-lg font-medium text-slate-800 mb-4\">\n          Audio Recording\n        </h3>\n        <AudioRecorder\n          audioState={audioState}\n          onStateChange={handleAudioStateChange}\n        />\n      </div>\n\n      {/* Image Capture */}\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6\">\n        <h3 className=\"text-lg font-medium text-slate-800 mb-4\">\n          Handwritten Notes (Optional)\n        </h3>\n        <ImageCapture\n          imageState={imageState}\n          onStateChange={handleImageStateChange}\n        />\n      </div>\n\n      {/* Submission */}\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6\">\n        <h3 className=\"text-lg font-medium text-slate-800 mb-4\">\n          Submit Consultation\n        </h3>\n        <SubmissionForm\n          audioFile={audioState.audioFile}\n          imageFiles={imageState.images}\n          canSubmit={canSubmit}\n          isSubmitting={isSubmitting}\n          onSubmissionStateChange={setIsSubmitting}\n        />\n      </div>\n\n      {/* Status */}\n      {(audioState.error || imageState.error) && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-red-800 mb-2\">Errors:</h4>\n          {audioState.error && (\n            <p className=\"text-sm text-red-700\">Audio: {audioState.error}</p>\n          )}\n          {imageState.error && (\n            <p className=\"text-sm text-red-700\">Image: {imageState.error}</p>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": "4GAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAOO,SAAS,EAAc,YAAE,CAAU,eAAE,CAAa,CAAsB,EAE7E,IAAM,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAK,EAAwB,MAChD,EAAY,CAAA,EAAA,EAAA,MAAK,AAAL,EAA2B,MACvC,EAAc,AAFK,CAEL,EAAA,EAAA,MAAA,AAAK,EAAyB,MAC5C,EAAe,AAFH,CAEG,EAAA,EAAA,MAAA,AAAK,EAAU,GAEpC,CAAA,EAAA,EAAA,AAHoB,SAGpB,AAAQ,EAAE,KAEJ,AAAC,CAAA,EAAA,EAAA,AAJc,gBAErB,MAEO,AAAqB,KAAK,AAC7B,EAAc,CAAE,MAAO,CADpB,gDACsE,GAGpE,KAED,EAAY,OAAO,EAAE,AACvB,cAAc,EAAY,OAAO,EAE/B,EAAU,OAAO,EAAE,AACrB,EAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,GAAS,EAAM,IAAI,GAE7D,GACC,CAAC,EAAc,EAGlB,IAAM,EAAuB,IAkBpB,CAhBU,AAEH,iCAFoC,IAAI,CAAC,UAAU,SAAS,EAEjD,CAEvB,YACA,aACA,aACA,yBACD,CAAG,CAEF,yBACA,aACA,YACA,aACD,EAEY,IAAI,CAAC,GAAQ,cAAc,eAAe,CAAC,KAAU,aAG9D,EAAiB,UACrB,GAAI,CACF,EAAc,CAAE,WAAO,CAAU,GAEjC,IAAM,EAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC,CACvD,MAAO,CACL,kBAAkB,EAClB,kBAAkB,CACpB,CACF,EAEA,GAAU,OAAO,CAAG,EAEpB,IAAM,EAAoB,IACpB,EAAgB,IAAI,cAAc,EAAQ,CAC9C,SAAU,CACZ,GAEA,EAAiB,OAAO,CAAG,EAE3B,IAAM,EAAiB,EAAE,CAEzB,EAAc,eAAe,CAAG,AAAC,IAC3B,EAAM,IAAI,CAAC,IAAI,CAAG,GAAG,AACvB,EAAO,IAAI,CAAC,EAAM,IAAI,CAE1B,EAEA,EAAc,MAAM,CAAG,UACrB,IAAM,EAAY,IAAI,KAAK,EAAQ,CAAE,KAAM,CAAkB,GAUvD,EANJ,AAAI,EAAS,QAMG,AANK,CAAC,QAAgB,CAAP,MAC3B,AAK6B,EALpB,QAAQ,CAAC,OAAe,CAAP,KAC1B,EAAS,QAAQ,CAAC,QAAgB,CAAP,KACxB,OAMH,EAAY,IAAI,KAAK,CAAC,EAAU,CAAE,CAAC,UAAU,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,EAAA,CAAW,CAAE,CAC9E,KAAM,CACR,GAGM,EAAa,CAAA,EAAA,EAAA,YAAW,AAAX,EAAa,EAAW,SAC3C,GAAI,CAAC,EAAW,KAAK,CAAE,AADJ,YAEjB,EAAc,CACZ,MAAO,EAAW,KAAK,CACvB,aAAa,CACf,GAIF,EAAc,WACZ,YACA,EACA,aAAa,CACf,GAGA,EAAO,SAAS,GAAG,OAAO,CAAC,GAAS,EAAM,IAAI,GAChD,EAEA,EAAc,KAAK,GACnB,EAAa,OAAO,CAAG,KAAK,GAAG,GAE/B,EAAc,CACZ,aAAa,EACb,SAAU,CACZ,GAGA,EAAY,OAAO,CAAG,YAAY,KAChC,IAAM,EAAU,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,GAAK,EAAa,OAAO,AAAP,EAAW,KACjE,EAAc,CAAE,SAAU,CAAQ,EACpC,EAAG,IAEL,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,GAC3C,EAAc,CACZ,MAAO,kEACP,aAAa,CACf,EACF,CACF,EAsBA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDACZ,AAAC,EAAW,SAAS,CAgCpB,CAAA,CA/BA,CA+BA,EAAA,GAAA,EAAC,MAAA,CAAI,OA/BiB,GA+BP,uDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iHACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8EACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,sBAAd,OAGH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,wBAGlD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4CACV,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,EAAW,QAAQ,OAIvC,CAAA,EAAA,CAJK,CAIL,GAAA,EAAC,SAAA,CACC,QA5DS,CA4DA,IA3DrB,EAAc,CACZ,eAAW,EACX,SAAU,EACV,WAAO,CACT,EACF,EAuDc,UAAU,iFACX,eAhDL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EAAW,WAAW,CA5BrB,EA4BwB,GA3BxC,EAAiB,OAAO,EAAI,EAAW,WAAW,EAAE,CACtD,EAAiB,OAAO,CAAC,IAAI,GAEzB,EAAY,OAAO,EAAE,CACvB,cAAc,EAAY,OAAO,EACjC,EAAY,OAAO,CAAG,MAG5B,EAmB8D,EAClD,SAAU,CAAC,CAAC,EAAW,KAAK,CAC5B,UAAW,CAAC,+FAA+F,EACzG,EAAW,WAAW,CAClB,4CACA,gCACL,gDAAgD,CAAC,UAEjD,EAAW,WAAW,CACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAElB,CAAA,EAAA,EAAA,EAFC,CAED,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,cAInB,CAAA,EAAA,EAAA,GAJK,CAIL,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,6CACV,EAAW,WAAW,CAAG,eAAiB,2BAE5C,EAAW,WAAW,EACrB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,0CACV,GAAA,EAAA,cAAa,AAAb,EAAe,EAAW,QAAQ,WAAlC,CAkCZ,AA5BG,EA4BQ,KAAK,EACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BA7BqD,4BA8BlE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gCAAwB,EAAW,KAAK,KAKxD,CAAC,EAAW,SAAS,EAAI,CAAC,EAAW,WAAW,EAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kDAAyC,oBACvD,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,4CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,yCACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gCACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,8CACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,iEAMhB,6FCzPA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAGA,EAAA,EAAA,CAAA,CAAA,QASO,CATsC,QAS7B,EAAa,YAAE,CAAU,IAT+B,WAS7B,CAAa,CAAE,SAAU,CAAS,CAAqB,EAChG,IAAM,EAAe,CAAA,EAAA,EAAA,MAAK,AAAL,EAAyB,MACxC,EAAiB,CAAA,EAAA,EAAA,MAAA,AAAK,EAAoB,MAE1C,EAAmB,AAHJ,MAGW,IAC9B,GAAI,CACF,EAAc,CAAE,IAJG,OAII,CAAU,GAEjC,IAAM,EAAyB,EAAE,CAEjC,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAAK,CACrC,IAAM,EAAO,CAAK,CAAC,EAAE,CAGrB,GAAI,CAAC,EAAK,IAAI,CAAC,UAAU,CAAC,UAAW,YACnC,EAAc,CAAE,MAAO,CAAC,KAAK,EAAE,EAAK,IAAI,CAAC,qBAAqB,CAAC,AAAC,GAKlE,GAAI,EAAK,IAAI,CAAG,KAAK,IAAa,GAAN,SAC1B,EAAc,CAAE,MAAO,CAAC,KAAK,EAAE,EAAK,IAAI,CAAC,8CAA8C,CAAE,AAAD,GAM1F,EAAU,IAAI,CAAC,CACb,GAAI,CAAA,EAAG,KAAK,GAAG,GAAG,CAAC,EAAE,EAAA,CAAG,CACxB,KAAM,EACN,KAAM,EAAK,IAAI,CACf,KAAM,EAAK,IAAI,CACf,KAAM,EAAK,IAAI,AAEjB,EACF,CAEA,EAAc,CACZ,OAAQ,IAAI,EAAW,MAAM,IAAK,EAAU,AAC9C,EACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2BAA4B,GAC1C,EAAc,CAAE,MAAO,6CAA8C,EACvE,CACF,EAiBM,EAAc,AAAC,IAEnB,EAAc,CACZ,OAAQ,EAAW,MAAM,CAAC,MAAM,CAAC,GAAO,EAAI,EAAE,GAAK,GACnD,WAAO,CACT,EACF,EAsBA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCAEZ,CAAA,EAAA,EAAA,cAAA,AAAa,KACZ,CAAA,EAAA,EAAA,IAAA,EAAC,OADF,EACE,CACC,QAfS,CAeA,IAdjB,EAAe,OAAO,EAAE,OAC1B,EAcU,UAAU,sKAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,OACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6CAAoC,kBAOxD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAvBiB,CAuBR,IAtBf,EAAa,OAAO,EAAE,OACxB,EAsBQ,UAAU,sKAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,OACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6CAAoC,8BAOxD,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,EACL,KAAK,OACL,OAAO,UACP,QAAQ,cACR,SA7EuB,AAAD,CA6EZ,GA5Ed,IAAM,EAAQ,EAAM,MAAM,CAAC,KAAK,CAC5B,GAAS,EAAM,MAAM,CAAG,GAAG,AAC7B,EAAiB,EAErB,EAyEM,UAAU,WAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,EACL,KAAK,OACL,OAAO,UACP,QAAQ,CAAA,CAAA,EACR,SA9EoB,AAAD,CA8ET,GA7Ed,IAAM,EAAQ,EAAM,MAAM,CAAC,KAAK,CAC5B,GAAS,EAAM,MAAM,CAAG,GAAG,AAC7B,EAAiB,EAErB,EA0EM,UAAU,WAIX,EAAW,MAAM,CAAC,MAAM,CAAG,GAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,8CAAoC,oBAC9B,EAAW,MAAM,CAAC,MAAM,CAAC,OAE7C,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QA3EW,CA2EF,IAzEnB,EAAc,CACZ,OAAQ,EAAE,CACV,WAAO,CACT,GAGI,EAAa,OAAO,GAAE,EAAa,OAAO,CAAC,KAAK,CAAG,EAAA,EACnD,EAAe,OAAO,GAAE,EAAe,OAAO,CAAC,KAAK,CAAG,EAAA,CAC7D,EAkEY,UAAU,6DACX,iBAKH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAW,MAAM,CAAC,GAAG,CAAC,AAAC,GACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAmB,UAAU,+EAC5B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,SAEH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oEACV,EAAM,IAAI,GAEb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCACV,CAAC,EAAM,IAAI,CAAG,KAAO,IAAA,CAAI,CAAE,OAAO,CAAC,GAAG,eAI7C,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAY,EAAM,EAAE,EACnC,UAAU,wGAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,gBAlBP,EAAM,EAAE,IAkBb,IASZ,EAAW,KAAK,EACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gCAAwB,EAAW,KAAK,OAK/D,2GCvMO,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAFgC,CAGhC,AAHgC,CAI9B,AAJ8B,CAI3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC7D,CAaM,EAAO,CAAA,CAAP,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLCvBhD,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAWO,SAAS,EAAe,WAC7B,CAAS,YACT,CAAU,CACV,WAAS,cACT,CAAY,yBACZ,CAAuB,CACH,EACpB,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAgC,QACzE,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,AADP,EACiB,IACnD,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAU,EADrB,EAGlC,EAAe,UACnB,GAAI,AAAC,IAAa,GAElB,CAL0C,EAKlB,GACxB,EAAgB,GAHgB,KAIhC,EAAgB,IAChB,EAAkB,IAElB,GAAI,CAEF,IAAM,EAAmB,GAAY,IAAI,GAAO,EAAI,IAAI,GAAK,EAAE,CAGzD,EAAS,MAAM,CAAA,EAAA,EAAA,gBAAA,AAAe,EAClC,IAAM,CAAA,EAAA,EAAA,UADa,iBACb,AAA0B,EAC9B,EACA,EACA,EAAE,CACF,CAJI,aAKJ,EACA,aAAa,CAEf,CACE,YAAa,EACb,UAAW,EAJ8B,EAKzC,SAAU,GACZ,GAGE,EAAO,OAAO,EAChB,AADkB,EACF,WAChB,EACE,CAAC,8CAA8C,EAAE,EAAO,IAAI,EAAE,gBAAkB,MAAM,yBAAyB,CAAC,EAIlH,WAAW,KACT,EAAgB,QAChB,EAAkB,GACpB,EAAG,OAEH,EAAgB,SAChB,EAAgB,EAAO,KAAK,EAAI,iCAEpC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,oBAAqB,GACnC,EAAgB,SAChB,EAAgB,6DAClB,QAAU,CACR,GAAwB,EAC1B,EACF,EA+CA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kDAAyC,qBACvD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,qBACN,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,EAAY,iBAAmB,wBAC7C,EAAY,UAAY,kBAG7B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,uBACN,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,GAAc,EAAW,MAAM,CAAG,EAAI,iBAAmB,yBACvE,GAAc,EAAW,MAAM,CAAG,EAAI,CAAC,EAAE,EAAE,EAAW,MAAM,CAAC,SAAS,CAAC,CAAG,wBAOnF,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,CAAC,GAAa,EACxB,UAAW,CA3CY,KAC3B,IAAM,EAAY,sHAElB,AAAK,EAID,EACK,AALL,CAKK,EAAG,EAAU,EALN,KAIE,mCAC8C,CAAC,CAG5C,WAAW,CAA5B,EACK,CAAA,EAAG,EAAU,wBAAwB,CAAC,CAGxC,CAAA,EAAG,EAAU,yCAAyC,CAAC,CAXrD,CAAA,EAAG,EAAU,6CAA6C,CAAC,CAYtE,aA1CM,AAAJ,CAuEK,CArED,CAAA,EAAA,EAAA,IAAA,EAAA,CAFc,CAEd,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,qBAKS,WAAW,CAA5B,EAEA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,YACvB,CADC,AACD,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,kBAMV,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GAAA,CADC,CACA,OAAA,UAAK,6BAsDU,YAAjB,GAA8B,GAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,aAAtB,mCACD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,kCAA0B,SAK3B,UAAjB,GAA4B,GAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,cAAtB,gCACD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gCAAwB,IACrC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,CAAC,GAAa,EACxB,UAAU,kEACX,sBASR,AAAiB,YAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCACX,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,UAAO,gBAAoB,oLAQxC,yGCjMA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QAGO,SAAS,IACd,GAAM,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAO,AAAP,EAA8B,CAChE,aAAa,EACb,SAAU,CACZ,CAHoC,EAK9B,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAqB,CAAE,OAAQ,EAAE,CAAE,MAAO,IAAK,GACpF,CAAC,EAD6B,AACf,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,IAU3C,EAAY,CAAC,CAAC,EAAW,SAAS,EAAI,CAAC,EAE7C,GAZwC,GAatC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kDAAyC,gCAGvD,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCAAwB,6DAEnC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,uDAEN,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,8CAMV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8FACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,oBAGxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CACZ,WAAY,EACZ,SAFD,KA/ByB,AAAD,CAiCR,GAhCrB,EAAc,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,GAAG,CAAQ,CAAC,CAAC,CACjD,OAoCI,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8FACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,iCAGxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,YAAY,CAAA,CACX,WAAY,EACZ,SAFD,KAtCyB,AAAD,CAwCR,GAvCrB,EAAc,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,GAAG,CAAQ,CAAC,CAAC,CACjD,OA2CI,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8FACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,wBAGxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,cAAc,CAAA,CACb,UAAW,EAAW,SAAS,AADhC,CAEC,WAAY,EAAW,MAAM,CAC7B,UAAW,EACX,aAAc,EACd,wBAAyB,OAK5B,CAAC,EAAW,KAAK,EAAI,EAAW,KAAA,AAAK,GACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iDAAwC,YACrD,EAAW,KAAK,EACf,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,iCAAuB,UAAQ,EAAW,KAAK,IAE7D,EAAW,KAAK,EACf,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,iCAAuB,UAAQ,EAAW,KAAK,SAMxE", "ignoreList": [2]}