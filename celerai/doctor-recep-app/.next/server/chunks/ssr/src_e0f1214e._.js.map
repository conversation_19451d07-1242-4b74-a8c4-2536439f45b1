{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/auth.ts"], "sourcesContent": ["'use server'\n\nimport { redirect } from 'next/navigation'\nimport bcrypt from 'bcryptjs'\nimport { createClient } from '@/lib/supabase/server'\nimport { createSession, deleteSession, refreshSession } from '@/lib/auth/session'\nimport { SignupFormSchema, LoginFormSchema } from '@/lib/validations'\nimport { FormState } from '@/lib/types'\nimport { processReferralSignup } from '@/lib/actions/referrals'\n\nexport async function signup(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = SignupFormSchema.safeParse({\n    name: formData.get('name'),\n    email: formData.get('email'),\n    password: formData.get('password'),\n    clinic_name: formData.get('clinic_name'),\n    phone: formData.get('phone'),\n  })\n\n  // Get referral code from form\n  const referralCode = formData.get('referral_code') as string\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.', // A generic message for validation failure\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  // Prepare data for insertion into database\n  const { name, email, password, clinic_name, phone } = validatedFields.data\n  const hashedPassword = await bcrypt.hash(password, 10)\n\n  try {\n    const supabase = await createClient()\n\n    // Check if user already exists (email or phone)\n    const { data: existingUser } = await supabase\n      .from('doctors')\n      .select('id')\n      .eq('email', email)\n      .single()\n\n    if (existingUser) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An account with this email already exists.',\n      }\n    }\n\n    // Check if phone number already exists (if phone is provided)\n    if (phone) {\n      const { data: existingPhone } = await supabase\n        .from('doctors')\n        .select('id')\n        .eq('phone', phone)\n        .single()\n\n      if (existingPhone) {\n        return {\n          success: false,\n          message: 'An account with this phone number already exists.',\n        }\n      }\n    }\n\n    // Insert the user into the database with approval required and new default quota\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .insert({\n        name,\n        email,\n        password_hash: hashedPassword,\n        clinic_name,\n        phone,\n        approved: false, // New doctors require approval\n        monthly_quota: 50, // Set default quota to 50\n      })\n      .select('id, approved')\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    if (!user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    // Process referral if referral code was provided\n    if (referralCode) {\n      await processReferralSignup(referralCode, user.id)\n    }\n\n    // Don't create session for unapproved users\n    if (!user.approved) {\n      return {\n        success: true, // This is a success state, but with an informative message\n        message: 'Account created successfully! Please wait for admin approval before you can log in.',\n      }\n    }\n\n    // Create user session (only for approved users)\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Signup error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means signup was successful and session created (if approved)\n  // or a success message was returned for pending approval.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function login(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = LoginFormSchema.safeParse({\n    email: formData.get('email'),\n    password: formData.get('password'),\n  })\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.',\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  const { email, password } = validatedFields.data\n\n  try {\n    const supabase = await createClient()\n\n    // Get user from database\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, password_hash, approved')\n      .eq('email', email)\n      .single()\n\n    if (error || !user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Check if user is approved\n    if (!user.approved) {\n      return {\n        success: false, // This is an error state, preventing login\n        message: 'Your account is pending admin approval. Please wait for approval before logging in.',\n      }\n    }\n\n    // Create user session\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Login error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means login was successful and session created.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function logout() {\n  await deleteSession()\n  redirect('/login')\n}\n\nexport async function changePassword(doctorId: string, formData: FormData): Promise<FormState> {\n  const currentPassword = formData.get('currentPassword') as string\n  const newPassword = formData.get('newPassword') as string\n  const confirmPassword = formData.get('confirmPassword') as string\n\n  // Basic validation\n  if (!currentPassword || !newPassword || !confirmPassword) {\n    return {\n      success: false,\n      message: 'All fields are required.',\n    }\n  }\n\n  if (newPassword !== confirmPassword) {\n    return {\n      success: false,\n      message: 'New passwords do not match.',\n    }\n  }\n\n  if (newPassword.length < 8) {\n    return {\n      success: false,\n      message: 'New password must be at least 8 characters long.',\n    }\n  }\n\n  try {\n    const supabase = await createClient()\n\n    // Get current password hash\n    const { data: doctor, error: fetchError } = await supabase\n      .from('doctors')\n      .select('password_hash')\n      .eq('id', doctorId)\n      .single()\n\n    if (fetchError || !doctor) {\n      return {\n        success: false,\n        message: 'Doctor not found.',\n      }\n    }\n\n    // Verify current password\n    const isValidPassword = await bcrypt.compare(currentPassword, doctor.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false,\n        message: 'Current password is incorrect.',\n      }\n    }\n\n    // Hash new password\n    const hashedNewPassword = await bcrypt.hash(newPassword, 10)\n\n    // Update password\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ password_hash: hashedNewPassword })\n      .eq('id', doctorId)\n\n    if (updateError) {\n      console.error('Password update error:', updateError)\n      return {\n        success: false,\n        message: 'Failed to update password.',\n      }\n    }\n\n    // Refresh session with updated credentials\n    await refreshSession(doctorId)\n\n    return {\n      success: true,\n      message: 'Password changed successfully!',\n    }\n  } catch (error) {\n    console.error('Password change error:', error)\n    return {\n      success: false,\n      message: 'An unexpected error occurred.',\n    }\n  }\n}"], "names": [], "mappings": ";;;;;;IAUsB,SAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/auth/signup-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useActionState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { signup } from '@/lib/actions/auth'\nimport { FormState } from '@/lib/types'\nimport { trackAuth } from '@/lib/analytics'\n\n// CORRECTED initialState to conform to FormState interface\nconst initialState: FormState = {\n  success: false,\n  message: '',\n  // fieldErrors is optional, so it can be omitted or set to an empty object if needed\n  // fieldErrors: {},\n}\n\ninterface SignupFormProps {\n  referralCode?: string\n}\n\nexport function SignupForm({ referralCode }: SignupFormProps) {\n  const [state, formAction, isPending] = useActionState(signup, initialState)\n  const router = useRouter()\n\n  // Auto-redirect to login page after 3 seconds for successful account creation\n  useEffect(() => {\n    if (state?.success && state?.message?.includes('Account created successfully')) {\n      // Track successful signup completion\n      trackAuth('signup_completed')\n\n      const timer = setTimeout(() => {\n        router.push('/login')\n      }, 3000)\n\n      return () => clearTimeout(timer)\n    }\n  }, [state?.success, state?.message, router])\n\n  // Handle form submission to track signup started\n  const handleSubmit = (formData: FormData) => {\n    // Track signup attempt\n    trackAuth('signup_started')\n\n    // Call the original form action\n    formAction(formData)\n  }\n\n  return (\n    <form action={handleSubmit} className=\"mt-8 space-y-6\">\n      {/* Hidden field for referral code */}\n      {referralCode && (\n        <input\n          type=\"hidden\"\n          name=\"referral_code\"\n          value={referralCode}\n        />\n      )}\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Full Name\n          </label>\n          <input\n            id=\"name\"\n            name=\"name\"\n            type=\"text\"\n            autoComplete=\"name\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Dr. John Doe\"\n          />\n          {state?.fieldErrors?.name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"<EMAIL>\"\n          />\n          {state?.fieldErrors?.email && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.email[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Password\n          </label>\n          <input\n            id=\"password\"\n            name=\"password\"\n            type=\"password\"\n            autoComplete=\"new-password\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Create a strong password\"\n          />\n          {state?.fieldErrors?.password && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.password[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"clinic_name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Hospital Name\n          </label>\n          <input\n            id=\"clinic_name\"\n            name=\"clinic_name\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"ABC Medical Center\"\n          />\n          {state?.fieldErrors?.clinic_name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.clinic_name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Phone Number\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-4\">\n              <span className=\"text-slate-500 text-sm font-medium\">+91</span>\n            </div>\n            <input\n              id=\"phone\"\n              name=\"phone\"\n              type=\"tel\"\n              autoComplete=\"tel\"\n              className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n              placeholder=\"9876543210\"\n            />\n          </div>\n          {state?.fieldErrors?.phone && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.phone[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"referral_code\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Referral ID (Optional)\n          </label>\n          <input\n            id=\"referral_id\"\n            name=\"referral_code\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Enter referral ID if you have one (optional)\"\n            defaultValue={referralCode || ''}\n          />\n          {state?.fieldErrors?.referral_code && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.referral_id[0]}</span>\n            </p>\n          )}\n        </div>\n\n        {/* Terms of Service and Privacy Policy Checkbox */}\n        <div className=\"flex items-start space-x-3\">\n          <input\n            id=\"terms\"\n            name=\"terms\"\n            type=\"checkbox\"\n            required\n            className=\"mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2\"\n          />\n          <label htmlFor=\"terms\" className=\"text-sm text-slate-600 leading-relaxed\">\n            I accept the{' '}\n            <a href=\"/terms\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Terms of Service\n            </a>\n            {' '}and{' '}\n            <a href=\"/privacy\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Privacy Policy\n            </a>\n          </label>\n        </div>\n      </div>\n\n      {state?.message && (\n        <div className={`rounded-xl p-4 border ${\n          state.success\n            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'\n            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'\n        }`}>\n          <p className={`text-sm font-medium flex items-center space-x-2 ${\n            state.success ? 'text-emerald-800' : 'text-red-800'\n          }`}>\n            <span className={`w-2 h-2 rounded-full ${\n              state.success ? 'bg-emerald-400' : 'bg-red-400'\n            }`}></span>\n            <span>{state.message}</span>\n          </p>\n        </div>\n      )}\n\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isPending}\n          className=\"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2\"\n        >\n          {isPending ? (\n            <>\n              <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n              <span>Creating your magical account...</span>\n            </>\n          ) : (\n            <>\n              <span>Start Creating Magic</span>\n              <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n              </svg>\n            </>\n          )}\n        </button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,2DAA2D;AAC3D,MAAM,eAA0B;IAC9B,SAAS;IACT,SAAS;AAGX;AAMO,SAAS,WAAW,EAAE,YAAY,EAAmB;IAC1D,MAAM,CAAC,OAAO,YAAY,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,6JAAA,CAAA,SAAM,EAAE;IAC9D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,8EAA8E;IAC9E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,WAAW,OAAO,SAAS,SAAS,iCAAiC;YAC9E,qCAAqC;YACrC,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE;YAEV,MAAM,QAAQ,WAAW;gBACvB,OAAO,IAAI,CAAC;YACd,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC,OAAO;QAAS,OAAO;QAAS;KAAO;IAE3C,iDAAiD;IACjD,MAAM,eAAe,CAAC;QACpB,uBAAuB;QACvB,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE;QAEV,gCAAgC;QAChC,WAAW;IACb;IAEA,qBACE,8OAAC;QAAK,QAAQ;QAAc,WAAU;;YAEnC,8BACC,8OAAC;gBACC,MAAK;gBACL,MAAK;gBACL,OAAO;;;;;;0BAIX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAAgD;;;;;;0CAGhF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,sBACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAKtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAAgD;;;;;;0CAGjF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,uBACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAKvC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAgD;;;;;;0CAGpF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,0BACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAgD;;;;;;0CAGvF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,aAAa,6BACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAK7C,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAAgD;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAqC;;;;;;;;;;;kDAEvD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,aAAa,uBACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAKvC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAgB,WAAU;0CAAgD;;;;;;0CAGzF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,WAAU;gCACV,aAAY;gCACZ,cAAc,gBAAgB;;;;;;4BAE/B,OAAO,aAAa,+BACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,MAAM,WAAW,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAAyC;oCAC3D;kDACb,8OAAC;wCAAE,MAAK;wCAAS,QAAO;wCAAS,WAAU;kDAAsE;;;;;;oCAGhH;oCAAI;oCAAI;kDACT,8OAAC;wCAAE,MAAK;wCAAW,QAAO;wCAAS,WAAU;kDAAsE;;;;;;;;;;;;;;;;;;;;;;;;YAOxH,OAAO,yBACN,8OAAC;gBAAI,WAAW,CAAC,sBAAsB,EACrC,MAAM,OAAO,GACT,mEACA,0DACJ;0BACA,cAAA,8OAAC;oBAAE,WAAW,CAAC,gDAAgD,EAC7D,MAAM,OAAO,GAAG,qBAAqB,gBACrC;;sCACA,8OAAC;4BAAK,WAAW,CAAC,qBAAqB,EACrC,MAAM,OAAO,GAAG,mBAAmB,cACnC;;;;;;sCACF,8OAAC;sCAAM,MAAM,OAAO;;;;;;;;;;;;;;;;;0BAK1B,8OAAC;0BACC,cAAA,8OAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,0BACC;;0CACE,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;qDAGR;;0CACE,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;gCAAyD,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChH,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF", "debugId": null}}]}