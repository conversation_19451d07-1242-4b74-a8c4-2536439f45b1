{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/storage.ts"], "sourcesContent": ["import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'\n\n// Storage configuration - Migrated to Cloudflare R2\nexport const STORAGE_CONFIG = {\n  // R2 Configuration\n  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',\n  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',\n\n  // Folder prefixes (replaces separate buckets)\n  AUDIO_PREFIX: 'consultation-audio',\n  IMAGE_PREFIX: 'consultation-images',\n\n  // File limits\n  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file\n  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation\n  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],\n  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],\n  RETENTION_DAYS: 30\n}\n\n// R2 Client configuration\nconst createR2Client = () => {\n  return new S3Client({\n    region: 'auto',\n    endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,\n    credentials: {\n      accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',\n      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************',\n    },\n  })\n}\n\n// File validation\nexport function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {\n  // Check file size\n  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {\n    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }\n  }\n\n  // Check file type\n  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES\n  if (!allowedTypes.includes(file.type)) {\n    return { valid: false, error: `File type ${file.type} is not allowed` }\n  }\n\n  return { valid: true }\n}\n\n// Generate storage path with folder prefix for R2\nexport function generateStoragePath(\n  doctorId: string,\n  consultationId: string,\n  fileName: string,\n  type: 'audio' | 'image'\n): string {\n  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')\n  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`\n}\n\n// Upload file to Cloudflare R2\nexport async function uploadFile(\n  file: File,\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; url?: string; error?: string }> {\n  try {\n    // Validate file\n    const validation = validateFile(file, type)\n    if (!validation.valid) {\n      return { success: false, error: validation.error }\n    }\n\n    const r2Client = createR2Client()\n    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)\n\n    // Convert file to buffer\n    const fileBuffer = await file.arrayBuffer()\n\n    // Upload to R2 - preserve exact Content-Type from file\n    const uploadCommand = new PutObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n      Body: new Uint8Array(fileBuffer),\n      ContentType: file.type, // Use original file.type to preserve codec info\n      CacheControl: 'public, max-age=3600',\n    })\n\n    await r2Client.send(uploadCommand)\n\n    // Generate public URL\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    return { success: true, url: publicUrl }\n  } catch (error) {\n    console.error('R2 upload error:', error)\n    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\n// Upload multiple files\nexport async function uploadMultipleFiles(\n  files: File[],\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {\n  const results = await Promise.all(\n    files.map(file => uploadFile(file, doctorId, consultationId, type))\n  )\n\n  const successful = results.filter(r => r.success)\n  const failed = results.filter(r => !r.success)\n\n  if (failed.length > 0) {\n    return {\n      success: false,\n      errors: failed.map(f => f.error || 'Unknown error')\n    }\n  }\n\n  return {\n    success: true,\n    urls: successful.map(s => s.url!).filter(Boolean)\n  }\n}\n\n// Delete file from R2 storage\nexport async function deleteFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const r2Client = createR2Client()\n\n    const deleteCommand = new DeleteObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n    })\n\n    await r2Client.send(deleteCommand)\n\n    return { success: true }\n  } catch (error) {\n    console.error('R2 delete error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }\n  }\n}\n\n// Extract file path from R2 URL\nexport function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {\n  try {\n    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n    const prefixPath = `/${prefix}/`\n    const index = url.indexOf(prefixPath)\n\n    if (index === -1) return null\n\n    return url.substring(url.indexOf(prefix))\n  } catch {\n    return null\n  }\n}\n\n// Download file from R2 storage\nexport async function downloadFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; data?: Blob; error?: string }> {\n  try {\n    // For public files, we can fetch directly from the custom domain\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    const response = await fetch(publicUrl)\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.blob()\n    return { success: true, data }\n  } catch (error) {\n    console.error('R2 download error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }\n  }\n}\n\n// Calculate total file size\nexport function calculateTotalFileSize(files: File[]): number {\n  return files.reduce((total, file) => total + file.size, 0)\n}\n\n// Validate total consultation file size\nexport function validateTotalSize(files: File[]): { valid: boolean; error?: string } {\n  const totalSize = calculateTotalFileSize(files)\n  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {\n    return {\n      valid: false,\n      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`\n    }\n  }\n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,aAAa,QAAQ,GAAG,CAAC,cAAc,IAAI;IAC3C,YAAY,QAAQ,GAAG,CAAC,aAAa,IAAI;IAEzC,8CAA8C;IAC9C,cAAc;IACd,cAAc;IAEd,cAAc;IACd,eAAe,MAAM,OAAO;IAC5B,gBAAgB,MAAM,OAAO;IAC7B,qBAAqB;QAAC;QAAc;QAAa;QAAa;QAAa;QAAc;QAAa;KAAY;IAClH,qBAAqB;QAAC;QAAc;QAAa;QAAa;QAAc;KAAa;IACzF,gBAAgB;AAClB;AAEA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB,OAAO,IAAI,iJAAA,CAAA,WAAQ,CAAC;QAClB,QAAQ;QACR,UAAU,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,aAAa,IAAI,mCAAmC,yBAAyB,CAAC;QAC/G,aAAa;YACX,aAAa,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC7C,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACvD;IACF;AACF;AAGO,SAAS,aAAa,IAAU,EAAE,IAAuB;IAC9D,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,eAAe,aAAa,EAAE;QAC5C,OAAO;YAAE,OAAO;YAAO,OAAO,CAAC,kBAAkB,EAAE,eAAe,aAAa,GAAG,OAAO,KAAK,QAAQ,CAAC;QAAC;IAC1G;IAEA,kBAAkB;IAClB,MAAM,eAAe,SAAS,UAAU,eAAe,mBAAmB,GAAG,eAAe,mBAAmB;IAC/G,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YAAE,OAAO;YAAO,OAAO,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;QAAC;IACxE;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAGO,SAAS,oBACd,QAAgB,EAChB,cAAsB,EACtB,QAAgB,EAChB,IAAuB;IAEvB,MAAM,oBAAoB,SAAS,OAAO,CAAC,mBAAmB;IAC9D,MAAM,SAAS,SAAS,UAAU,eAAe,YAAY,GAAG,eAAe,YAAY;IAC3F,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,CAAC,EAAE,mBAAmB;AACvE;AAGO,eAAe,WACpB,IAAU,EACV,QAAgB,EAChB,cAAsB,EACtB,IAAuB;IAEvB,IAAI;QACF,gBAAgB;QAChB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,OAAO;gBAAE,SAAS;gBAAO,OAAO,WAAW,KAAK;YAAC;QACnD;QAEA,MAAM,WAAW;QACjB,MAAM,WAAW,oBAAoB,UAAU,gBAAgB,KAAK,IAAI,EAAE;QAE1E,yBAAyB;QACzB,MAAM,aAAa,MAAM,KAAK,WAAW;QAEzC,uDAAuD;QACvD,MAAM,gBAAgB,IAAI,iJAAA,CAAA,mBAAgB,CAAC;YACzC,QAAQ,eAAe,WAAW;YAClC,KAAK;YACL,MAAM,IAAI,WAAW;YACrB,aAAa,KAAK,IAAI;YACtB,cAAc;QAChB;QAEA,MAAM,SAAS,IAAI,CAAC;QAEpB,sBAAsB;QACtB,MAAM,YAAY,GAAG,eAAe,UAAU,CAAC,CAAC,EAAE,UAAU;QAE5D,OAAO;YAAE,SAAS;YAAM,KAAK;QAAU;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,eAAe,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC;IAC/G;AACF;AAGO,eAAe,oBACpB,KAAa,EACb,QAAgB,EAChB,cAAsB,EACtB,IAAuB;IAEvB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG,CAAC,CAAA,OAAQ,WAAW,MAAM,UAAU,gBAAgB;IAG/D,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO;IAChD,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO;IAE7C,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,OAAO;YACL,SAAS;YACT,QAAQ,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI;QACrC;IACF;IAEA,OAAO;QACL,SAAS;QACT,MAAM,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAG,MAAM,CAAC;IAC3C;AACF;AAGO,eAAe,WACpB,QAAgB,EAChB,KAAwB;IAExB,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,gBAAgB,IAAI,iJAAA,CAAA,sBAAmB,CAAC;YAC5C,QAAQ,eAAe,WAAW;YAClC,KAAK;QACP;QAEA,MAAM,SAAS,IAAI,CAAC;QAEpB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB;IAC3F;AACF;AAGO,SAAS,uBAAuB,GAAW,EAAE,IAAuB;IACzE,IAAI;QACF,MAAM,SAAS,SAAS,UAAU,eAAe,YAAY,GAAG,eAAe,YAAY;QAC3F,MAAM,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChC,MAAM,QAAQ,IAAI,OAAO,CAAC;QAE1B,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC;IACnC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,aACpB,QAAgB,EAChB,KAAwB;IAExB,IAAI;QACF,iEAAiE;QACjE,MAAM,YAAY,GAAG,eAAe,UAAU,CAAC,CAAC,EAAE,UAAU;QAE5D,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAkB;IAC7F;AACF;AAGO,SAAS,uBAAuB,KAAa;IAClD,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,IAAI,EAAE;AAC1D;AAGO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,YAAY,uBAAuB;IACzC,IAAI,YAAY,eAAe,cAAc,EAAE;QAC7C,OAAO;YACL,OAAO;YACP,OAAO,CAAC,wBAAwB,EAAE,eAAe,cAAc,GAAG,OAAO,KAAK,QAAQ,CAAC;QACzF;IACF;IACA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}]}