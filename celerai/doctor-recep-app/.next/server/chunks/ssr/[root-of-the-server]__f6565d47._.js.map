{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from '@/lib/types'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/auth/session.ts"], "sourcesContent": ["import 'server-only'\nimport { SignJWT, jwtVerify } from 'jose'\nimport { cookies } from 'next/headers'\nimport { SessionPayload } from '@/lib/types' // UNCOMMENT THIS LINE\n// TODO: Remove this line (type SessionPayload = any)\n// type SessionPayload = any // REMOVE THIS LINE\n\nconst secretKey = process.env.SESSION_SECRET\nconst encodedKey = new TextEncoder().encode(secretKey)\n\nexport async function encrypt(payload: SessionPayload) {\n  // Fix: Cast payload to Record<string, unknown> for SignJWT\n  return new SignJWT(payload as unknown as Record<string, unknown>) // Keep this cast\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('7d')\n    .sign(encodedKey)\n}\n\nexport async function decrypt(session: string | undefined = '') {\n  try {\n    if (!session) {\n      return null\n    }\n\n    const { payload } = await jwtVerify(session, encodedKey, {\n      algorithms: ['HS256'],\n    })\n    // Fix: cast to unknown first, then to SessionPayload for type safety\n    return payload as unknown as SessionPayload // Keep this cast\n  } catch {\n    console.log('Failed to verify session')\n    return null\n  }\n}\n\nexport async function createSession(userId: string) {\n  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n  const session = await encrypt({ userId, expiresAt })\n  const cookieStore = await cookies()\n\n  console.log('DEBUG: Creating session for user:', userId)\n  console.log('DEBUG: Session expires at:', expiresAt)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expiresAt,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session cookie set successfully')\n}\n\nexport async function updateSession() {\n  const cookieStore = await cookies()\n  const session = cookieStore.get('session')?.value\n  const payload = await decrypt(session)\n\n  console.log('DEBUG: Updating session - session exists:', !!session)\n  console.log('DEBUG: Updating session - payload valid:', !!payload)\n\n  if (!session || !payload) {\n    console.log('DEBUG: Cannot update session - missing session or payload')\n    return null\n  }\n\n  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expires,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session updated successfully')\n}\n\nexport async function refreshSession(userId: string) {\n  // Delete old session and create new one\n  console.log('DEBUG: Refreshing session for user:', userId)\n  await deleteSession()\n  await createSession(userId)\n  console.log('DEBUG: Session refresh completed')\n}\n\nexport async function deleteSession() {\n  const cookieStore = await cookies()\n  console.log('DEBUG: Deleting session cookie')\n  cookieStore.delete('session')\n  console.log('DEBUG: Session cookie deleted')\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAEA,qDAAqD;AACrD,gDAAgD;AAEhD,MAAM,YAAY,QAAQ,GAAG,CAAC,cAAc;AAC5C,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;AAErC,eAAe,QAAQ,OAAuB;IACnD,2DAA2D;IAC3D,OAAO,IAAI,qJAAA,CAAA,UAAO,CAAC,SAA+C,iBAAiB;KAChF,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,MAClB,IAAI,CAAC;AACV;AAEO,eAAe,QAAQ,UAA8B,EAAE;IAC5D,IAAI;QACF,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;YACvD,YAAY;gBAAC;aAAQ;QACvB;QACA,qEAAqE;QACrE,OAAO,SAAqC,iBAAiB;IAC/D,EAAE,OAAM;QACN,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;AACF;AAEO,eAAe,cAAc,MAAc;IAChD,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAC3D,MAAM,UAAU,MAAM,QAAQ;QAAE;QAAQ;IAAU;IAClD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,QAAQ,GAAG,CAAC,qCAAqC;IACjD,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,UAAU,YAAY,GAAG,CAAC,YAAY;IAC5C,MAAM,UAAU,MAAM,QAAQ;IAE9B,QAAQ,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,IAAI,CAAC,WAAW,CAAC,SAAS;QACxB,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAEzD,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe,eAAe,MAAc;IACjD,wCAAwC;IACxC,QAAQ,GAAG,CAAC,uCAAuC;IACnD,MAAM;IACN,MAAM,cAAc;IACpB,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,QAAQ,GAAG,CAAC;IACZ,YAAY,MAAM,CAAC;IACnB,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/auth/dal.ts"], "sourcesContent": ["import 'server-only'\nimport { cache } from 'react'\nimport { cookies } from 'next/headers'\nimport { redirect } from 'next/navigation'\nimport { decrypt } from './session'\nimport { createClient } from '@/lib/supabase/server'\nimport { <PERSON> } from '@/lib/types'\n\n\n\nexport const verifySession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    redirect('/login')\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const checkSession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    return null\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const getUser = cache(async (): Promise<Doctor | null> => {\n  const session = await verifySession()\n  if (!session) return null\n\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('*')\n      .eq('id', session.userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n      password_hash: user.password_hash\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\nexport const getUserById = cache(async (userId: string): Promise<Doctor | null> => {\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user by ID:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user by ID:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\n// Get quota information for a doctor\nexport const getDoctorQuota = cache(async (userId: string) => {\n  try {\n    const supabase = await createClient()\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('monthly_quota, quota_used, quota_reset_at')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch quota:', error.message || error)\n      return null\n    }\n\n    const quotaRemaining = doctor.monthly_quota - doctor.quota_used\n    const quotaPercentage = Math.round((doctor.quota_used / doctor.monthly_quota) * 100)\n    const resetDate = new Date(doctor.quota_reset_at)\n    const daysUntilReset = Math.ceil((resetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))\n\n    return {\n      monthly_quota: doctor.monthly_quota,\n      quota_used: doctor.quota_used,\n      quota_remaining: quotaRemaining,\n      quota_percentage: quotaPercentage,\n      quota_reset_at: doctor.quota_reset_at,\n      days_until_reset: Math.max(0, daysUntilReset),\n    }\n  } catch (error) {\n    console.error('Failed to fetch quota:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AAKO,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACjC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,YAAY,GAAG,CAAC,YAAY;IAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE;IAE9B,IAAI,CAAC,SAAS,QAAQ;QACpB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,OAAO;QAAE,QAAQ;QAAM,QAAQ,QAAQ,MAAM;IAAC;AAChD;AAEO,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAA<PERSON>,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,YAAY,GAAG,CAAC,YAAY;IAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE;IAE9B,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,OAAO;QAAE,QAAQ;QAAM,QAAQ,QAAQ,MAAM;IAAC;AAChD;AAEO,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAC3B,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,MAAM,EACvB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB,MAAM,OAAO,IAAI;YACxD,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,OAAO;QAClB,oEAAoE;QACpE,OAAO;YACL,GAAG,IAAI;YACP,eAAe,KAAK,aAAa;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChF,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC,6JACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B,MAAM,OAAO,IAAI;YAC9D,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,OAAO;QAClB,oEAAoE;QACpE,OAAO;YACL,GAAG,IAAI;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtF,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC,6CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B,MAAM,OAAO,IAAI;YACzD,OAAO;QACT;QAEA,MAAM,iBAAiB,OAAO,aAAa,GAAG,OAAO,UAAU;QAC/D,MAAM,kBAAkB,KAAK,KAAK,CAAC,AAAC,OAAO,UAAU,GAAG,OAAO,aAAa,GAAI;QAChF,MAAM,YAAY,IAAI,KAAK,OAAO,cAAc;QAChD,MAAM,iBAAiB,KAAK,IAAI,CAAC,CAAC,UAAU,OAAO,KAAK,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1F,OAAO;YACL,eAAe,OAAO,aAAa;YACnC,YAAY,OAAO,UAAU;YAC7B,iBAAiB;YACjB,kBAAkB;YAClB,gBAAgB,OAAO,cAAc;YACrC,kBAAkB,KAAK,GAAG,CAAC,GAAG;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjF,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\nexport const SignupFormSchema = z.object({\n  name: z\n    .string()\n    .min(2, { message: 'Name must be at least 2 characters long.' })\n    .trim(),\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z\n    .string()\n    .min(8, { message: 'Password must be at least 8 characters long' })\n    .regex(/[a-zA-Z]/, { message: 'Password must contain at least one letter.' })\n    .regex(/[0-9]/, { message: 'Password must contain at least one number.' })\n    .regex(/[^a-zA-Z0-9]/, {\n      message: 'Password must contain at least one special character.',\n    })\n    .trim(),\n  clinic_name: z\n    .string()\n    .min(2, { message: 'Hospital name must be at least 2 characters long.' })\n    .optional(),\n  phone: z\n    .string()\n    .regex(/^\\d{10}$/, { message: 'Please enter exactly 10 digits (excluding +91).' })\n    .optional(),\n})\n\nexport const LoginFormSchema = z.object({\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z.string().min(1, { message: 'Password is required.' }).trim(),\n})\n\nexport const AdminLoginFormSchema = z.object({\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z.string().min(1, { message: 'Password is required.' }).trim(),\n})\n\nexport const ConsultationCreateSchema = z.object({\n  primary_audio_url: z.string().url({ message: 'Valid audio URL is required.' }),\n  additional_audio_urls: z.array(z.string().url()).optional().default([]),\n  image_urls: z.array(z.string().url()).optional().default([]),\n  submitted_by: z.enum(['doctor', 'receptionist']),\n  total_file_size_bytes: z.number().min(0).optional(),\n})\n\nexport const ConsultationUpdateSchema = z.object({\n  edited_note: z.string().min(1, { message: 'Note content is required.' }),\n})\n\n\n\nexport const ProfileUpdateSchema = z.object({\n  name: z\n    .string()\n    .min(2, { message: 'Name must be at least 2 characters long.' })\n    .trim(),\n  clinic_name: z\n    .string()\n    .min(2, { message: 'Hospital name must be at least 2 characters long.' })\n    .optional(),\n  phone: z\n    .string()\n    .regex(/^\\d{10}$/, { message: 'Please enter exactly 10 digits (excluding +91).' })\n    .optional(),\n\n})\n\nexport type SignupFormData = z.infer<typeof SignupFormSchema>\nexport type LoginFormData = z.infer<typeof LoginFormSchema>\nexport type ConsultationCreateData = z.infer<typeof ConsultationCreateSchema>\nexport type ConsultationUpdateData = z.infer<typeof ConsultationUpdateSchema>\n\nexport type ProfileUpdateData = z.infer<typeof ProfileUpdateSchema>\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAEO,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,IAAI;IACP,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA8C,GAChE,KAAK,CAAC,YAAY;QAAE,SAAS;IAA6C,GAC1E,KAAK,CAAC,SAAS;QAAE,SAAS;IAA6C,GACvE,KAAK,CAAC,gBAAgB;QACrB,SAAS;IACX,GACC,IAAI;IACP,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,QAAQ;IACX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,YAAY;QAAE,SAAS;IAAkD,GAC/E,QAAQ;AACb;AAEO,MAAM,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB,GAAG,IAAI;AACxE;AAEO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB,GAAG,IAAI;AACxE;AAEO,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAAE,SAAS;IAA+B;IAC5E,uBAAuB,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,OAAO,CAAC,EAAE;IACtE,YAAY,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,OAAO,CAAC,EAAE;IAC3D,cAAc,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAe;IAC/C,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;AACnD;AAEO,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;AACxE;AAIO,MAAM,sBAAsB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,IAAI;IACP,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,QAAQ;IACX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,YAAY;QAAE,SAAS;IAAkD,GAC/E,QAAQ;AAEb", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/storage.ts"], "sourcesContent": ["import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'\n\n// Storage configuration - Migrated to Cloudflare R2\nexport const STORAGE_CONFIG = {\n  // R2 Configuration\n  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',\n  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',\n\n  // Folder prefixes (replaces separate buckets)\n  AUDIO_PREFIX: 'consultation-audio',\n  IMAGE_PREFIX: 'consultation-images',\n\n  // File limits\n  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file\n  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation\n  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],\n  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],\n  RETENTION_DAYS: 30\n}\n\n// R2 Client configuration\nconst createR2Client = () => {\n  return new S3Client({\n    region: 'auto',\n    endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,\n    credentials: {\n      accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',\n      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************',\n    },\n  })\n}\n\n// File validation\nexport function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {\n  // Check file size\n  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {\n    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }\n  }\n\n  // Check file type\n  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES\n  if (!allowedTypes.includes(file.type)) {\n    return { valid: false, error: `File type ${file.type} is not allowed` }\n  }\n\n  return { valid: true }\n}\n\n// Generate storage path with folder prefix for R2\nexport function generateStoragePath(\n  doctorId: string,\n  consultationId: string,\n  fileName: string,\n  type: 'audio' | 'image'\n): string {\n  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')\n  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`\n}\n\n// Upload file to Cloudflare R2\nexport async function uploadFile(\n  file: File,\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; url?: string; error?: string }> {\n  try {\n    // Validate file\n    const validation = validateFile(file, type)\n    if (!validation.valid) {\n      return { success: false, error: validation.error }\n    }\n\n    const r2Client = createR2Client()\n    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)\n\n    // Convert file to buffer\n    const fileBuffer = await file.arrayBuffer()\n\n    // Upload to R2 - preserve exact Content-Type from file\n    const uploadCommand = new PutObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n      Body: new Uint8Array(fileBuffer),\n      ContentType: file.type, // Use original file.type to preserve codec info\n      CacheControl: 'public, max-age=3600',\n    })\n\n    await r2Client.send(uploadCommand)\n\n    // Generate public URL\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    return { success: true, url: publicUrl }\n  } catch (error) {\n    console.error('R2 upload error:', error)\n    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\n// Upload multiple files\nexport async function uploadMultipleFiles(\n  files: File[],\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {\n  const results = await Promise.all(\n    files.map(file => uploadFile(file, doctorId, consultationId, type))\n  )\n\n  const successful = results.filter(r => r.success)\n  const failed = results.filter(r => !r.success)\n\n  if (failed.length > 0) {\n    return {\n      success: false,\n      errors: failed.map(f => f.error || 'Unknown error')\n    }\n  }\n\n  return {\n    success: true,\n    urls: successful.map(s => s.url!).filter(Boolean)\n  }\n}\n\n// Delete file from R2 storage\nexport async function deleteFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const r2Client = createR2Client()\n\n    const deleteCommand = new DeleteObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n    })\n\n    await r2Client.send(deleteCommand)\n\n    return { success: true }\n  } catch (error) {\n    console.error('R2 delete error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }\n  }\n}\n\n// Extract file path from R2 URL\nexport function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {\n  try {\n    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n    const prefixPath = `/${prefix}/`\n    const index = url.indexOf(prefixPath)\n\n    if (index === -1) return null\n\n    return url.substring(url.indexOf(prefix))\n  } catch {\n    return null\n  }\n}\n\n// Download file from R2 storage\nexport async function downloadFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; data?: Blob; error?: string }> {\n  try {\n    // For public files, we can fetch directly from the custom domain\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    const response = await fetch(publicUrl)\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.blob()\n    return { success: true, data }\n  } catch (error) {\n    console.error('R2 download error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }\n  }\n}\n\n// Calculate total file size\nexport function calculateTotalFileSize(files: File[]): number {\n  return files.reduce((total, file) => total + file.size, 0)\n}\n\n// Validate total consultation file size\nexport function validateTotalSize(files: File[]): { valid: boolean; error?: string } {\n  const totalSize = calculateTotalFileSize(files)\n  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {\n    return {\n      valid: false,\n      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`\n    }\n  }\n  return { valid: true }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,aAAa,QAAQ,GAAG,CAAC,cAAc,IAAI;IAC3C,YAAY,QAAQ,GAAG,CAAC,aAAa,IAAI;IAEzC,8CAA8C;IAC9C,cAAc;IACd,cAAc;IAEd,cAAc;IACd,eAAe,MAAM,OAAO;IAC5B,gBAAgB,MAAM,OAAO;IAC7B,qBAAqB;QAAC;QAAc;QAAa;QAAa;QAAa;QAAc;QAAa;KAAY;IAClH,qBAAqB;QAAC;QAAc;QAAa;QAAa;QAAc;KAAa;IACzF,gBAAgB;AAClB;AAEA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB,OAAO,IAAI,iJAAA,CAAA,WAAQ,CAAC;QAClB,QAAQ;QACR,UAAU,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,aAAa,IAAI,mCAAmC,yBAAyB,CAAC;QAC/G,aAAa;YACX,aAAa,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC7C,iBAAiB,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACvD;IACF;AACF;AAGO,SAAS,aAAa,IAAU,EAAE,IAAuB;IAC9D,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,eAAe,aAAa,EAAE;QAC5C,OAAO;YAAE,OAAO;YAAO,OAAO,CAAC,kBAAkB,EAAE,eAAe,aAAa,GAAG,OAAO,KAAK,QAAQ,CAAC;QAAC;IAC1G;IAEA,kBAAkB;IAClB,MAAM,eAAe,SAAS,UAAU,eAAe,mBAAmB,GAAG,eAAe,mBAAmB;IAC/G,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YAAE,OAAO;YAAO,OAAO,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;QAAC;IACxE;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAGO,SAAS,oBACd,QAAgB,EAChB,cAAsB,EACtB,QAAgB,EAChB,IAAuB;IAEvB,MAAM,oBAAoB,SAAS,OAAO,CAAC,mBAAmB;IAC9D,MAAM,SAAS,SAAS,UAAU,eAAe,YAAY,GAAG,eAAe,YAAY;IAC3F,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,CAAC,EAAE,mBAAmB;AACvE;AAGO,eAAe,WACpB,IAAU,EACV,QAAgB,EAChB,cAAsB,EACtB,IAAuB;IAEvB,IAAI;QACF,gBAAgB;QAChB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,OAAO;gBAAE,SAAS;gBAAO,OAAO,WAAW,KAAK;YAAC;QACnD;QAEA,MAAM,WAAW;QACjB,MAAM,WAAW,oBAAoB,UAAU,gBAAgB,KAAK,IAAI,EAAE;QAE1E,yBAAyB;QACzB,MAAM,aAAa,MAAM,KAAK,WAAW;QAEzC,uDAAuD;QACvD,MAAM,gBAAgB,IAAI,iJAAA,CAAA,mBAAgB,CAAC;YACzC,QAAQ,eAAe,WAAW;YAClC,KAAK;YACL,MAAM,IAAI,WAAW;YACrB,aAAa,KAAK,IAAI;YACtB,cAAc;QAChB;QAEA,MAAM,SAAS,IAAI,CAAC;QAEpB,sBAAsB;QACtB,MAAM,YAAY,GAAG,eAAe,UAAU,CAAC,CAAC,EAAE,UAAU;QAE5D,OAAO;YAAE,SAAS;YAAM,KAAK;QAAU;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,eAAe,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC;IAC/G;AACF;AAGO,eAAe,oBACpB,KAAa,EACb,QAAgB,EAChB,cAAsB,EACtB,IAAuB;IAEvB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,MAAM,GAAG,CAAC,CAAA,OAAQ,WAAW,MAAM,UAAU,gBAAgB;IAG/D,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO;IAChD,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO;IAE7C,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,OAAO;YACL,SAAS;YACT,QAAQ,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI;QACrC;IACF;IAEA,OAAO;QACL,SAAS;QACT,MAAM,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAG,MAAM,CAAC;IAC3C;AACF;AAGO,eAAe,WACpB,QAAgB,EAChB,KAAwB;IAExB,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,gBAAgB,IAAI,iJAAA,CAAA,sBAAmB,CAAC;YAC5C,QAAQ,eAAe,WAAW;YAClC,KAAK;QACP;QAEA,MAAM,SAAS,IAAI,CAAC;QAEpB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB;IAC3F;AACF;AAGO,SAAS,uBAAuB,GAAW,EAAE,IAAuB;IACzE,IAAI;QACF,MAAM,SAAS,SAAS,UAAU,eAAe,YAAY,GAAG,eAAe,YAAY;QAC3F,MAAM,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChC,MAAM,QAAQ,IAAI,OAAO,CAAC;QAE1B,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC;IACnC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,aACpB,QAAgB,EAChB,KAAwB;IAExB,IAAI;QACF,iEAAiE;QACjE,MAAM,YAAY,GAAG,eAAe,UAAU,CAAC,CAAC,EAAE,UAAU;QAE5D,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAkB;IAC7F;AACF;AAGO,SAAS,uBAAuB,KAAa;IAClD,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,IAAI,EAAE;AAC1D;AAGO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,YAAY,uBAAuB;IACzC,IAAI,YAAY,eAAe,cAAc,EAAE;QAC7C,OAAO;YACL,OAAO;YACP,OAAO,CAAC,wBAAwB,EAAE,eAAe,cAAc,GAAG,OAAO,KAAK,QAAQ,CAAC;QACzF;IACF;IACA,OAAO;QAAE,OAAO;IAAK;AACvB", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;;;;;;;;;AAEA,0DAA0D;AAC1D,SAAS,qBAAqB,KAAc;IAC1C,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO;IACjC,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAM;YACN,OAAO,EAAE;QACX;IACF;IACA,OAAO,EAAE;AACX;AAGO,eAAe,4BACpB,SAAe,EACf,UAAkB,EAClB,oBAA4B,EAC5B,WAAsC,EACtC,WAAoB,EACpB,mBAAyI,YAAY,EACrJ,WAAoB;IAEpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,2BAA2B;QAC3B,MAAM,WAAW;YAAC;eAAc;eAAe;SAAqB;QACpE,MAAM,sBAAsB,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9C,IAAI,CAAC,oBAAoB,KAAK,EAAE;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO,oBAAoB,KAAK;YAAE;QAC7D;QAEA,iDAAiD;QACjD,MAAM,iBAAiB,OAAO,UAAU;QAExC,4BAA4B;QAC5B,MAAM,oBAAoB,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,QAAQ,MAAM,EAAE,gBAAgB;QACtF,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,KAAK,EAAE;YAAC;QACpF;QAEA,gCAAgC;QAChC,IAAI,sBAAgC,EAAE;QACtC,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,MAAM,wBAAwB,MAAM,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,sBAAsB,QAAQ,MAAM,EAAE,gBAAgB;YAC9G,IAAI,CAAC,sBAAsB,OAAO,EAAE;gBAClC,OAAO;oBAAE,SAAS;oBAAO,OAAO,CAAC,gCAAgC,EAAE,sBAAsB,MAAM,EAAE,KAAK,OAAO;gBAAC;YAChH;YACA,sBAAsB,sBAAsB,IAAI,IAAI,EAAE;QACxD;QAEA,qBAAqB;QACrB,IAAI,YAAsB,EAAE;QAC5B,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,oBAAoB,MAAM,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,QAAQ,MAAM,EAAE,gBAAgB;YAChG,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBAC9B,OAAO;oBAAE,SAAS;oBAAO,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,MAAM,EAAE,KAAK,OAAO;gBAAC;YACjG;YACA,YAAY,kBAAkB,IAAI,IAAI,EAAE;QAC1C;QAEA,MAAM,gBAAgB,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD,EAAE;QAE7C,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,4CAA4C;QAC5C,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,IAAI;YACJ,WAAW,QAAQ,MAAM;YACzB,mBAAmB,kBAAkB,GAAG;YACxC,uBAAuB;YACvB,YAAY;YACZ,cAAc;YACd,QAAQ;YACR,uBAAuB;YACvB,cAAc,eAAe;YAC7B,mBAAmB;YACnB,cAAc,eAAe;QAC/B,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM;QAA6B;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,qBAAqB,cAAsB,EAAE,OAAe;IAChF,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,6CAA6C;QAC7C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,mBAAmB;YACnB,QAAQ;QACV,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAAE,sDAAsD;;QAEzF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,mBAAmB,QAAkB;IACzD,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,8BAA8B;QAC9B,MAAM,kBAAkB,SAAS,GAAG,CAAC;QACrC,MAAM,4BAA4B,SAAS,GAAG,CAAC;QAC/C,MAAM,kBAAkB,SAAS,GAAG,CAAC;QACrC,MAAM,sBAAsB,SAAS,GAAG,CAAC;QAEzC,IAAI,sBAAgC,EAAE;QACtC,IAAI,YAAsB,EAAE;QAC5B,IAAI,gBAAgB;QAEpB,8BAA8B;QAC9B,IAAI,2BAA2B;YAC7B,IAAI;gBACF,sBAAsB,KAAK,KAAK,CAAC;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;QAEA,mBAAmB;QACnB,IAAI,iBAAiB;YACnB,IAAI;gBACF,YAAY,KAAK,KAAK,CAAC;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA,wBAAwB;QACxB,IAAI,qBAAqB;YACvB,gBAAgB,SAAS,qBAAqB,OAAO;QACvD;QAEA,qBAAqB;QACrB,MAAM,kBAAkB,yHAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC;YACzD,mBAAmB;YACnB,uBAAuB;YACvB,YAAY;YACZ,cAAc,SAAS,GAAG,CAAC;YAC3B,uBAAuB;QACzB;QAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,OAAO;gBACL,SAAS;gBACT,OAAO,wBAAwB,KAAK,SAAS,CAAC,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;YAC3F;QACF;QAEA,MAAM,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,UAAU,EAAE,YAAY,EAAE,qBAAqB,EAAE,GAAG,gBAAgB,IAAI;QAE1H,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,sBAAsB;QACtB,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,WAAW,QAAQ,MAAM;YACzB;YACA,uBAAuB,yBAAyB,EAAE;YAClD,YAAY,cAAc,EAAE;YAC5B;YACA,QAAQ;YACR,uBAAuB,yBAAyB;QAClD,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM;QAA6B;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAGO,eAAe,iBAAiB,EACrC,OAAO,CAAC,EACR,WAAW,EAAE,EACb,MAAM,EACN,UAAU,EAMX,GAAG,CAAC,CAAC;IAKJ,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,WAAW;QAE7B,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,mRAAmR;YAAE,OAAO;QAAQ,GAC3S,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,MAAM;QAEf,sBAAsB;QACtB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,+BAA+B;QAC/B,IAAI,cAAc,WAAW,IAAI,IAAI;YACnC,gFAAgF;YAChF,MAAM,WAAW,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC;YACrD,QAAQ,MAAM,UAAU,CAAC,OAAO;QAClC;QAEA,MAAM,EAAE,MAAM,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEpD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YAEjC,4DAA4D;YAC5D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,eAAe,EAAE;wBACjB,SAAS;wBACT,YAAY,SAAS;oBACvB;gBACF;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,wBAAwB;QACxB,MAAM,qBAAqB,CAAC,iBAAiB,EAAE,EAAE,GAAG,CAAC,CAAC;YACpD,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,WAAW,IAAI,SAAS;gBACxB,cAAc,IAAI,YAAY;gBAC9B,mBAAmB,IAAI,iBAAiB;gBACxC,uBAAuB,qBAAqB,IAAI,qBAAqB;gBACrE,YAAY,qBAAqB,IAAI,UAAU;gBAC/C,mBAAmB,IAAI,iBAAiB,IAAI;gBAC5C,aAAa,IAAI,WAAW,IAAI;gBAChC,QAAQ,IAAI,MAAM;gBAClB,gBAAgB,IAAI,cAAc,IAAI;gBACtC,cAAc,IAAI,YAAY,IAAI;gBAClC,uBAAuB,IAAI,qBAAqB,IAAI;gBACpD,sBAAsB,IAAI,oBAAoB;gBAC9C,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,mBAAmB,IAAI,iBAAiB,IAAI;gBAC5C,cAAc,IAAI,YAAY,IAAI;gBAClC,kBAAkB,IAAI,gBAAgB,IAAI;YAC5C;QACF;QAEA,MAAM,UAAU,CAAC,SAAS,CAAC,IAAI,KAAK;QAEpC,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,eAAe;gBACf;gBACA,YAAY,SAAS;YACvB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAIO,eAAe,sBACpB,cAAsB,EACtB,gBAA2B,EAC3B,OAAiC,EACjC,UAAsC,EACtC,OAAiC,EACjC,gBAAyB,EACzB,WAAoB,EACpB,eAAwB;IAExB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,UAAU;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,mCAAmC;QACnC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,iBACL,MAAM,CAAC,mOACP,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,MAAM;QAET,IAAI,qBAAqB,CAAC,cAAc;YACtC,UAAU;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAIA,MAAM,oBAAoB;QAE1B,iEAAiE;QACjE,MAAM,sBAAsB,qBAAqB,kBAAkB,qBAAqB;QACxF,MAAM,YAAY,qBAAqB,kBAAkB,UAAU;QACnE,MAAM,eAAe;eAAI;eAAe,oBAAoB,EAAE;SAAE;QAEhE,yEAAyE;QACzE,MAAM,WAAW,MAAM,MAAM,6DAAmC,4BAA4B,CAAC,EAAE;YAC7F,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,mBAAmB,kBAAkB,iBAAiB;gBACtD,uBAAuB,MAAM,OAAO,CAAC,uBAAuB,sBAAsB,EAAE;gBACpF,YAAY,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;gBAC3D,cAAc,kBAAkB,YAAY,IAAI;gBAChD,mBAAmB,oBAAoB,kBAAkB,iBAAiB,IAAI;gBAC9E,cAAc,eAAe,kBAAkB,YAAY,IAAI;gBAC/D,kBAAkB,mBAAmB;gBACrC,cAAc,kBAAkB,YAAY,IAAI;gBAChD,aAAa,kBAAkB,OAAO,CAAC,IAAI,IAAI;gBAC/C,YAAY,kBAAkB,UAAU,IAAI;YAC9C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,WAAW,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YAClE,UAAU;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAS;QAC3C;QAEA,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,UAAU;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsB;QACxD;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,cAAc;QAElB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBACnD,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;4BACnC,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,EAAE;gCACtC,2DAA2D;gCAC3D,MAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,UAAU;gCACxE,eAAe;gCACf,uCAAuC;gCACvC,UAAU;4BACZ;wBACF,EAAE,OAAO,IAAI;wBACX,2CAA2C;wBAC7C;oBACF;gBACF;YACF;YAEA,qCAAqC;YACrC,aAAa;YAEb,kEAAkE;YAClE,IAAI;gBACF,MAAM,aAAa,MAAM,qBAAqB,gBAAgB;gBAC9D,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,QAAQ,KAAK,CAAC,qCAAqC,WAAW,KAAK;oBACnE,UAAU,CAAC,sCAAsC,EAAE,WAAW,KAAK,EAAE;oBACrE,OAAO;wBAAE,SAAS;wBAAO,OAAO,CAAC,sCAAsC,EAAE,WAAW,KAAK,EAAE;oBAAC;gBAC9F;YACF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,UAAU;gBACV,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA+D;YACjG;YAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YACf,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAE5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,WAAW,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAC1G,UAAU;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAS;QAC3C;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,UAAU;QACV,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,oBACpB,cAAsB,EACtB,UAAkB;IAElB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,yHAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC;YACzD,aAAa;QACf;QAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuB;QACzD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,sBAAsB;QACtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,aAAa;YACb,QAAQ;QACV,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,yBAAyB,cAAsB,EAAE,SAAmB;IACxF,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,0CAA0C;QAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,YAAY;QACd,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,mBAAmB,cAAsB,EAAE,SAAe;IAC9E,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QACA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,yBAAyB;QACzB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,iBACL,MAAM,CAAC,iCACP,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,MAAM;QACT,IAAI,cAAc,CAAC,cAAc;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QACA,IAAI,aAAa,MAAM,KAAK,YAAY;YACtC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,4DAA4D,EAAE,aAAa,MAAM,EAAE;YAAC;QACvH;QAEA,mCAAmC;QACnC,MAAM,eAAe,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,QAAQ,MAAM,EAAE,gBAAgB;QACjF,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,qBAAqB,EAAE,aAAa,KAAK,EAAE;YAAC;QAC/E;QAEA,MAAM,sBAAsB,qBAAqB,aAAa,qBAAqB;QACnF,oBAAoB,IAAI,CAAC,aAAa,GAAG;QAEzC,0BAA0B;QAC1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,uBAAuB;QAAoB,GACpD,EAAE,CAAC,MAAM;QACZ,IAAI,aAAa;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiC;QACnE;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,oBAAoB,cAAsB,EAAE,UAAkB;IAClF,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QACA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,yBAAyB;QACzB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,iBACL,MAAM,CAAC,sBACP,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,MAAM;QACT,IAAI,cAAc,CAAC,cAAc;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QACA,IAAI,aAAa,MAAM,KAAK,YAAY;YACtC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,6DAA6D,EAAE,aAAa,MAAM,EAAE;YAAC;QACxH;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,WAAW,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ,MAAM,EAAE,gBAAgB;QAG1E,iCAAiC;QACjC,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,OAAO;QACpE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,SAAS,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,IAAI,CAAC;YACpD,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,qBAAqB,EAAE,QAAQ;YAAC;QACnE;QAEA,2CAA2C;QAC3C,MAAM,oBAAoB,qBAAqB,aAAa,UAAU;QACtE,MAAM,eAAe,cAAc,GAAG,CAAC,CAAA,SAAU,OAAO,GAAG,EAAG,MAAM,CAAC,CAAA,MAAO;QAC5E,MAAM,eAAe;eAAI;eAAsB;SAAa;QAE5D,0BAA0B;QAC1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,YAAY;QAAa,GAClC,EAAE,CAAC,MAAM;QACZ,IAAI,aAAa;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,uBAAuB,cAAsB,EAAE,gBAAsI;IACzM,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,2BAA2B;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,mBAAmB;QACrB,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,eAAe,cAAsB,EAAE,UAAkB;IAC7E,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,uCAAuC;QACvC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,aAAa;QACf,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA6B;QAC/D;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,sBAAsB,cAAsB,EAAE,eAAuB;IACzF,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,4CAA4C;QAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,kBAAkB,mBAAmB;QACvC,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,kBAAkB,cAAsB,EAAE,WAAmB;IACjF,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,wCAAwC;QACxC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,cAAc,eAAe;QAC/B,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,gBAAgB,cAAsB;IAC1D,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,sCAAsC;QACtC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,aAAa;QACf,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA8B;QAChE;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAGO,eAAe,sBACpB,cAAsB,EACtB,QAAgB;IAEhB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,2BAA2B;QAC3B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,iBACL,MAAM,CAAC,iCACP,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,MAAM;QAET,IAAI,cAAc,CAAC,cAAc;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,wCAAwC;QACxC,IAAI,aAAa,MAAM,KAAK,YAAY;YACtC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkD;QACpF;QAEA,qCAAqC;QACrC,MAAM,sBAAsB,qBAAqB,aAAa,qBAAqB;QACnF,MAAM,mBAAmB,oBAAoB,MAAM,CAAC,CAAA,MAAO,QAAQ;QAEnE,sBAAsB;QACtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,uBAAuB;QACzB,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE;QAEA,2BAA2B;QAC3B,IAAI;YACF,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;YAC9C,MAAM,WAAW,UAAU;QAC7B,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;QACzC,qDAAqD;QACvD;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAGO,eAAe,wBACpB,cAAsB,EACtB,QAAgB;IAEhB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,2BAA2B;QAC3B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,iBACL,MAAM,CAAC,sBACP,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM,EAC9B,MAAM;QAET,IAAI,cAAc,CAAC,cAAc;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,wCAAwC;QACxC,IAAI,aAAa,MAAM,KAAK,YAAY;YACtC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkD;QACpF;QAEA,qCAAqC;QACrC,MAAM,YAAY,qBAAqB,aAAa,UAAU;QAC9D,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA,MAAO,QAAQ;QAEzD,sBAAsB;QACtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,YAAY;QACd,GACC,EAAE,CAAC,MAAM,gBACT,EAAE,CAAC,aAAa,QAAQ,MAAM;QAEjC,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,2BAA2B;QAC3B,IAAI;YACF,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;YAC9C,MAAM,WAAW,UAAU;QAC7B,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;QACzC,qDAAqD;QACvD;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAGO,eAAe;IAMpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,0BAA0B;YACnE,aAAa,QAAQ,MAAM;QAC7B;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,MAAM,QAAQ,MAAM,CAAC,EAAE,IAAI;YACzB,qBAAqB;YACrB,uBAAuB;YACvB,wBAAwB;YACxB,qBAAqB;QACvB;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,qBAAqB,OAAO,MAAM,mBAAmB;gBACrD,uBAAuB,OAAO,MAAM,qBAAqB;gBACzD,wBAAwB,OAAO,MAAM,sBAAsB;gBAC3D,qBAAqB,OAAO,MAAM,mBAAmB;YACvD;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;;;IAv8BsB;IAyFA;IAiCA;IA2FA;IA0GA;IAwIA;IA6CA;IA+BA;IAgDA;IAyDA;IA+BA;IA+BA;IA+BA;IA+BA;IAgCA;IAkEA;IAkEA;;AA55BA,+OAAA;AAyFA,+OAAA;AAiCA,+OAAA;AA2FA,+OAAA;AA0GA,+OAAA;AAwIA,+OAAA;AA6CA,+OAAA;AA+BA,+OAAA;AAgDA,+OAAA;AAyDA,+OAAA;AA+BA,+OAAA;AA+BA,+OAAA;AA+BA,+OAAA;AA+BA,+OAAA;AAgCA,+OAAA;AAkEA,+OAAA;AAkEA,+OAAA", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/contact-requests.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport interface ContactRequest {\n  id: string\n  doctor_id: string\n  doctor_name: string\n  doctor_email: string\n  clinic_name: string\n  phone_number: string\n  request_type: string\n  message: string | null\n  status: string\n  contacted_at: string | null\n  resolved_at: string | null\n  created_at: string\n  updated_at: string\n}\n\nexport async function createContactRequest(\n  doctorId: string,\n  message?: string,\n  subject?: string\n): Promise<ApiResponse<string>> {\n  try {\n    console.log('Creating contact request for doctorId:', doctorId)\n    const supabase = await createClient()\n\n    // Get doctor information first\n    console.log('Fetching doctor info...')\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select('name, email, clinic_name, phone, quota_used, monthly_quota')\n      .eq('id', doctorId)\n      .single()\n\n    console.log('Doctor fetch result:', { doctor, doctorError })\n\n    if (doctor<PERSON>rror || !doctor) {\n      console.error('Doctor not found:', { doctorId, doctorError })\n      return { success: false, error: `Doctor not found: ${doctorError?.message || 'No doctor data'}` }\n    }\n\n    // Simple insert without checking duplicates for now\n    const insertData = {\n      doctor_id: doctorId,\n      doctor_name: doctor.name,\n      doctor_email: doctor.email,\n      clinic_name: doctor.clinic_name || '',\n      phone_number: doctor.phone || '',\n      current_quota_used: doctor.quota_used || 0,\n      monthly_quota: doctor.monthly_quota || 0,\n      request_type: 'general_contact',\n      message: message || 'Contact request from dashboard',\n      subject: subject || 'general'\n    }\n    \n    console.log('Creating contact request with data:', insertData)\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .insert(insertData)\n      .select('id')\n      .single()\n\n    console.log('Insert result:', { data, error })\n\n    if (error) {\n      console.error('Failed to create contact request:', error)\n      return { success: false, error: `Database error: ${error.message}` }\n    }\n\n    // Force revalidation of admin paths\n    revalidatePath('/admin/dashboard')\n    revalidatePath('/admin')\n    \n    console.log('Contact request created successfully with ID:', data.id)\n    \n    return { success: true, data: data.id }\n  } catch (error) {\n    console.error('Unexpected error creating contact request:', error)\n    return { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\nexport async function getContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Database error fetching contact requests:', error)\n      return { success: false, error: 'Failed to fetch contact requests' }\n    }\n\n    // Only log if this is called with explicit debug flag or if there are new requests\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Fetched contact requests:', {\n        count: data?.length || 0,\n        pending: data?.filter(r => r.status === 'pending').length || 0\n      })\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getPendingContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch pending contact requests' }\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching pending contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateContactRequestStatus(\n  requestId: string,\n  status: 'pending' | 'contacted' | 'resolved'\n): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { error } = await supabase\n      .from('contact_requests')\n      .update({ status })\n      .eq('id', requestId)\n\n    if (error) {\n      return { success: false, error: 'Failed to update contact request status' }\n    }\n\n    revalidatePath('/admin/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error updating contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getContactRequestsCount(): Promise<ApiResponse<{\n  total: number;\n  pending: number;\n  contacted: number;\n  resolved: number;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('status')\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch contact requests count' }\n    }\n\n    const counts = {\n      total: data?.length || 0,\n      pending: data?.filter(r => r.status === 'pending').length || 0,\n      contacted: data?.filter(r => r.status === 'contacted').length || 0,\n      resolved: data?.filter(r => r.status === 'resolved').length || 0\n    }\n\n    return { success: true, data: counts }\n  } catch (error) {\n    console.error('Error fetching contact requests count:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function hasActiveContactRequest(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('id')\n      .eq('doctor_id', doctorId)\n      .eq('status', 'pending')\n      .single()\n\n    if (error && error.code !== 'PGRST116') { // PGRST116 is \"not found\" error\n      return { success: false, error: 'Failed to check contact request status' }\n    }\n\n    return { success: true, data: !!data }\n  } catch (error) {\n    console.error('Error checking contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAEA;;;;;;AAkBO,eAAe,qBACpB,QAAgB,EAChB,OAAgB,EAChB,OAAgB;IAEhB,IAAI;QACF,QAAQ,GAAG,CAAC,0CAA0C;QACtD,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,+BAA+B;QAC/B,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,8DACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,QAAQ,GAAG,CAAC,wBAAwB;YAAE;YAAQ;QAAY;QAE1D,IAAI,eAAe,CAAC,QAAQ;YAC1B,QAAQ,KAAK,CAAC,qBAAqB;gBAAE;gBAAU;YAAY;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,kBAAkB,EAAE,aAAa,WAAW,kBAAkB;YAAC;QAClG;QAEA,oDAAoD;QACpD,MAAM,aAAa;YACjB,WAAW;YACX,aAAa,OAAO,IAAI;YACxB,cAAc,OAAO,KAAK;YAC1B,aAAa,OAAO,WAAW,IAAI;YACnC,cAAc,OAAO,KAAK,IAAI;YAC9B,oBAAoB,OAAO,UAAU,IAAI;YACzC,eAAe,OAAO,aAAa,IAAI;YACvC,cAAc;YACd,SAAS,WAAW;YACpB,SAAS,WAAW;QACtB;QAEA,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,MAAM,CAAC,MACP,MAAM;QAET,QAAQ,GAAG,CAAC,kBAAkB;YAAE;YAAM;QAAM;QAE5C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,MAAM,OAAO,EAAE;YAAC;QACrE;QAEA,oCAAoC;QACpC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,QAAQ,GAAG,CAAC,iDAAiD,KAAK,EAAE;QAEpE,OAAO;YAAE,SAAS;YAAM,MAAM,KAAK,EAAE;QAAC;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC;IAClH;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,mFAAmF;QACnF,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,6BAA6B;gBACvC,OAAO,MAAM,UAAU;gBACvB,SAAS,MAAM,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;YAC/D;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,QAAQ,EAAE;QAAC;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,WACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2C;QAC7E;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,QAAQ,EAAE;QAAC;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,2BACpB,SAAiB,EACjB,MAA4C;IAE5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE;QAAO,GAChB,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0C;QAC5E;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe;IAMpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;QAEV,IAAI,OAAO;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyC;QAC3E;QAEA,MAAM,SAAS;YACb,OAAO,MAAM,UAAU;YACvB,SAAS,MAAM,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;YAC7D,WAAW,MAAM,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;YACjE,UAAU,MAAM,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,UAAU;QACjE;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,wBAAwB,QAAgB;IAC5D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,aAAa,UAChB,EAAE,CAAC,UAAU,WACb,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyC;QAC3E;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,CAAC,CAAC;QAAK;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;;;IA/LsB;IAkEA;IA6BA;IAqBA;IAwBA;IA+BA;;AA3KA,+OAAA;AAkEA,+OAAA;AA6BA,+OAAA;AAqBA,+OAAA;AAwBA,+OAAA;AA+BA,+OAAA", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/referrals.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ReferralInfo, ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {\n  try {\n    const supabase = await createClient()\n\n    // Get doctor's referral information\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select(`\n        referral_code,\n        total_referrals,\n        successful_referrals,\n        referral_discount_earned,\n        referred_by\n      `)\n      .eq('id', doctorId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return { success: false, error: 'Failed to fetch referral information' }\n    }\n\n    // Get pending referrals count\n    const { count: pendingCount } = await supabase\n      .from('referral_analytics')\n      .select('*', { count: 'exact', head: true })\n      .eq('referrer_id', doctorId)\n      .eq('status', 'pending')\n\n    // Get recent referrals\n    const { data: recentReferrals, error: referralsError } = await supabase\n      .from('referral_analytics')\n      .select(`\n        id,\n        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),\n        signup_date,\n        conversion_date,\n        status\n      `)\n      .eq('referrer_id', doctorId)\n      .order('created_at', { ascending: false })\n      .limit(10)\n\n    if (referralsError) {\n      return { success: false, error: 'Failed to fetch recent referrals' }\n    }\n\n    // Get referrer info separately if exists\n    let referredBy = null\n    if (doctor.referred_by) {\n      const { data: referrer } = await supabase\n        .from('doctors')\n        .select('name, referral_code')\n        .eq('id', doctor.referred_by)\n        .single()\n      \n      if (referrer) {\n        referredBy = {\n          name: referrer.name,\n          referral_code: referrer.referral_code || ''\n        }\n      }\n    }\n\n    const referralInfo: ReferralInfo = {\n      referral_code: doctor.referral_code || '',\n      total_referrals: doctor.total_referrals || 0,\n      successful_referrals: doctor.successful_referrals || 0,\n      pending_referrals: pendingCount || 0,\n      discount_earned: doctor.referral_discount_earned || 0,\n      referred_by: referredBy,\n      recent_referrals: (recentReferrals || []).map(ref => ({\n        id: ref.id,\n        name: ref.referred_doctor?.name || 'Unknown',\n        email: ref.referred_doctor?.email || 'Unknown',\n        signup_date: ref.signup_date,\n        conversion_date: ref.conversion_date,\n        status: ref.status as 'pending' | 'converted' | 'expired'\n      }))\n    }\n\n    return { success: true, data: referralInfo }\n  } catch (error) {\n    console.error('Error fetching referral info:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('referral_code')\n      .eq('id', doctorId)\n      .single()\n\n    if (error || !doctor?.referral_code) {\n      return { success: false, error: 'Failed to fetch referral code' }\n    }\n\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'\n    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`\n\n    return { success: true, data: referralLink }\n  } catch (error) {\n    console.error('Error generating referral link:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Find the referrer\n    const { data: referrer, error: referrerError } = await supabase\n      .from('doctors')\n      .select('id, name')\n      .eq('referral_code', referralCode)\n      .single()\n\n    if (referrerError || !referrer) {\n      return { success: false, error: 'Invalid referral code' }\n    }\n\n    // Update the new doctor with referrer information\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ referred_by: referrer.id })\n      .eq('id', newDoctorId)\n\n    if (updateError) {\n      return { success: false, error: 'Failed to process referral signup' }\n    }\n\n    // Create referral analytics record\n    const { error: analyticsError } = await supabase\n      .from('referral_analytics')\n      .insert({\n        referrer_id: referrer.id,\n        referred_doctor_id: newDoctorId,\n        referral_code: referralCode,\n        status: 'pending'\n      })\n\n    if (analyticsError) {\n      console.error('Failed to create analytics record:', analyticsError)\n      // Don't fail the signup for this\n    }\n\n    // Update referrer's total referrals count\n    const { data: currentReferrer } = await supabase\n      .from('doctors')\n      .select('total_referrals')\n      .eq('id', referrer.id)\n      .single()\n\n    if (currentReferrer) {\n      const { error: countError } = await supabase\n        .from('doctors')\n        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })\n        .eq('id', referrer.id)\n\n      if (countError) {\n        console.error('Failed to update referral count:', countError)\n      }\n    }\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error processing referral signup:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Call the database function to handle conversion\n    const { data, error } = await supabase.rpc('handle_referral_conversion', {\n      referred_doctor_uuid: doctorId\n    })\n\n    if (error) {\n      console.error('Error marking referral conversion:', error)\n      return { success: false, error: 'Failed to process referral conversion' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/admin/dashboard')\n\n    return { success: true, data: data || false }\n  } catch (error) {\n    console.error('Error marking referral conversion:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getAdminReferralStats(): Promise<ApiResponse<{\n  total_referrals: number;\n  successful_conversions: number;\n  pending_referrals: number;\n  total_discount_earned: number;\n  top_referrers: Array<{\n    id: string;\n    name: string;\n    referral_code: string;\n    successful_referrals: number;\n    discount_earned: number;\n  }>;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    // Get overall stats\n    const { data: totalStats, error: statsError } = await supabase\n      .from('referral_analytics')\n      .select('status, discount_earned')\n\n    if (statsError) {\n      return { success: false, error: 'Failed to fetch referral statistics' }\n    }\n\n    const totalReferrals = totalStats?.length || 0\n    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0\n    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0\n    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0\n\n    // Get top referrers\n    const { data: topReferrers, error: referrersError } = await supabase\n      .from('doctors')\n      .select('id, name, referral_code, successful_referrals, referral_discount_earned')\n      .gt('successful_referrals', 0)\n      .order('successful_referrals', { ascending: false })\n      .limit(10)\n\n    if (referrersError) {\n      return { success: false, error: 'Failed to fetch top referrers' }\n    }\n\n    return {\n      success: true,\n      data: {\n        total_referrals: totalReferrals,\n        successful_conversions: successfulConversions,\n        pending_referrals: pendingReferrals,\n        total_discount_earned: totalDiscountEarned,\n        top_referrers: (topReferrers || []).map(r => ({\n          id: r.id,\n          name: r.name,\n          referral_code: r.referral_code || '',\n          successful_referrals: r.successful_referrals || 0,\n          discount_earned: r.referral_discount_earned || 0\n        }))\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching admin referral stats:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function validateReferralCode(referralCode: string): Promise<ApiResponse<{\n  valid: boolean;\n  referrer_name?: string;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: referrer, error } = await supabase\n      .from('doctors')\n      .select('name, approved')\n      .eq('referral_code', referralCode)\n      .eq('approved', true)\n      .single()\n\n    if (error || !referrer) {\n      return { \n        success: true, \n        data: { valid: false } \n      }\n    }\n\n    return {\n      success: true,\n      data: {\n        valid: true,\n        referrer_name: referrer.name\n      }\n    }\n  } catch (error) {\n    console.error('Error validating referral code:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAEA;;;;;;AAEO,eAAe,gBAAgB,QAAgB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oCAAoC;QACpC,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,8BAA8B;QAC9B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,sBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,eAAe,UAClB,EAAE,CAAC,UAAU;QAEhB,uBAAuB;QACvB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,eAAe,UAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,gBAAgB;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,yCAAyC;QACzC,IAAI,aAAa;QACjB,IAAI,OAAO,WAAW,EAAE;YACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,WACL,MAAM,CAAC,uBACP,EAAE,CAAC,MAAM,OAAO,WAAW,EAC3B,MAAM;YAET,IAAI,UAAU;gBACZ,aAAa;oBACX,MAAM,SAAS,IAAI;oBACnB,eAAe,SAAS,aAAa,IAAI;gBAC3C;YACF;QACF;QAEA,MAAM,eAA6B;YACjC,eAAe,OAAO,aAAa,IAAI;YACvC,iBAAiB,OAAO,eAAe,IAAI;YAC3C,sBAAsB,OAAO,oBAAoB,IAAI;YACrD,mBAAmB,gBAAgB;YACnC,iBAAiB,OAAO,wBAAwB,IAAI;YACpD,aAAa;YACb,kBAAkB,CAAC,mBAAmB,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;oBACpD,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,eAAe,EAAE,QAAQ;oBACnC,OAAO,IAAI,eAAe,EAAE,SAAS;oBACrC,aAAa,IAAI,WAAW;oBAC5B,iBAAiB,IAAI,eAAe;oBACpC,QAAQ,IAAI,MAAM;gBACpB,CAAC;QACH;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,qBAAqB,QAAgB;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,SAAS,CAAC,QAAQ,eAAe;YACnC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QACnD,MAAM,eAAe,GAAG,QAAQ,YAAY,EAAE,OAAO,aAAa,EAAE;QAEpE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,sBAAsB,YAAoB,EAAE,WAAmB;IACnF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oBAAoB;QACpB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,iBAAiB,cACpB,MAAM;QAET,IAAI,iBAAiB,CAAC,UAAU;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAwB;QAC1D;QAEA,kDAAkD;QAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,WACL,MAAM,CAAC;YAAE,aAAa,SAAS,EAAE;QAAC,GAClC,EAAE,CAAC,MAAM;QAEZ,IAAI,aAAa;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE;QAEA,mCAAmC;QACnC,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,sBACL,MAAM,CAAC;YACN,aAAa,SAAS,EAAE;YACxB,oBAAoB;YACpB,eAAe;YACf,QAAQ;QACV;QAEF,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,iCAAiC;QACnC;QAEA,0CAA0C;QAC1C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,WACL,MAAM,CAAC,mBACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,iBAAiB;YACnB,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC;gBAAE,iBAAiB,CAAC,gBAAgB,eAAe,IAAI,CAAC,IAAI;YAAE,GACrE,EAAE,CAAC,MAAM,SAAS,EAAE;YAEvB,IAAI,YAAY;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,uBAAuB,QAAgB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,kDAAkD;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,8BAA8B;YACvE,sBAAsB;QACxB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAwC;QAC1E;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM,QAAQ;QAAM;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe;IAapB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oBAAoB;QACpB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,sBACL,MAAM,CAAC;QAEV,IAAI,YAAY;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,YAAY,UAAU;QAC7C,MAAM,wBAAwB,YAAY,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;QAC1F,MAAM,mBAAmB,YAAY,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;QACnF,MAAM,sBAAsB,YAAY,OAAO,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,eAAe,IAAI,CAAC,GAAG,MAAM;QAEjG,oBAAoB;QACpB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,WACL,MAAM,CAAC,2EACP,EAAE,CAAC,wBAAwB,GAC3B,KAAK,CAAC,wBAAwB;YAAE,WAAW;QAAM,GACjD,KAAK,CAAC;QAET,IAAI,gBAAgB;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,iBAAiB;gBACjB,wBAAwB;gBACxB,mBAAmB;gBACnB,uBAAuB;gBACvB,eAAe,CAAC,gBAAgB,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC5C,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,eAAe,EAAE,aAAa,IAAI;wBAClC,sBAAsB,EAAE,oBAAoB,IAAI;wBAChD,iBAAiB,EAAE,wBAAwB,IAAI;oBACjD,CAAC;YACH;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,qBAAqB,YAAoB;IAI7D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,WACL,MAAM,CAAC,kBACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,YAAY,MACf,MAAM;QAET,IAAI,SAAS,CAAC,UAAU;YACtB,OAAO;gBACL,SAAS;gBACT,MAAM;oBAAE,OAAO;gBAAM;YACvB;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,eAAe,SAAS,IAAI;YAC9B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;;;IAxSsB;IAuFA;IAwBA;IAiEA;IAwBA;IAgEA;;AAxQA,+OAAA;AAuFA,+OAAA;AAwBA,+OAAA;AAiEA,+OAAA;AAwBA,+OAAA;AAgEA,+OAAA", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/.next-internal/server/app/info/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getConsultationStats as '0021892c2090f634723ac0a0d1cd1d8c637aae5b9c'} from 'ACTIONS_MODULE0'\nexport {getConsultations as '40181945d99bd1b1c806279418d8c4e384a1b0bd15'} from 'ACTIONS_MODULE0'\nexport {clearEditedNote as '40cad8ac11d66cae6a36deaf0347461ff74e2f07d8'} from 'ACTIONS_MODULE0'\nexport {createConsultation as '40d596bef0460f198d588bbf8dede026bc85cca740'} from 'ACTIONS_MODULE0'\nexport {updateConsultationImages as '6016d1147dadc5ab75f7387f44ab799f7cd595708b'} from 'ACTIONS_MODULE0'\nexport {approveConsultation as '60648db8471ac7da6666e2d2e8b2cf27a97b9b4688'} from 'ACTIONS_MODULE0'\nexport {updateAdditionalNotes as '6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7'} from 'ACTIONS_MODULE0'\nexport {saveStreamingSummary as '608aaaf92844312537830e8c8d0c49debb89bda21b'} from 'ACTIONS_MODULE0'\nexport {saveEditedNote as '6090ac31595b581fab8f32262309efcdcab1c7eb47'} from 'ACTIONS_MODULE0'\nexport {updateConsultationType as '60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb'} from 'ACTIONS_MODULE0'\nexport {updatePatientName as '609aacf458083825c0e86748f13c86d5773450588b'} from 'ACTIONS_MODULE0'\nexport {addAdditionalImages as '60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e'} from 'ACTIONS_MODULE0'\nexport {addAdditionalAudio as '60b3bf115b7639c2355ff16ee9fa53425dad65720c'} from 'ACTIONS_MODULE0'\nexport {deleteConsultationImage as '60c855f485dc14093b0fee942b2204ae7dca69142e'} from 'ACTIONS_MODULE0'\nexport {deleteAdditionalAudio as '60fe749e3c9b3a6222eaf37afc1421459616909387'} from 'ACTIONS_MODULE0'\nexport {generateSummaryStream as '7f7dd3bdf6242116733f7ef527b1e8f307d343dacd'} from 'ACTIONS_MODULE0'\nexport {createConsultationWithFiles as '7fc832fd06db7232f37f9b1b44631bd2956c9b940c'} from 'ACTIONS_MODULE0'\nexport {createContactRequest as '70fff2cc329b28db6323e452c9272d2de14164c462'} from 'ACTIONS_MODULE1'\nexport {hasActiveContactRequest as '405d7138432cf8f813249d6a1a52fcfca61c62a7ed'} from 'ACTIONS_MODULE1'\nexport {getReferralInfo as '40757964b8c9b072995f44631743e71306c1784480'} from 'ACTIONS_MODULE2'\nexport {generateReferralLink as '400d27483b0ad440768404c34690724d71663b6083'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AAiBA;AAEA", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/dashboard-stats.tsx"], "sourcesContent": ["import { FileText, Clock, CheckCircle, Calendar } from 'lucide-react'\nimport { DashboardStats as StatsType } from '@/lib/types'\n\ninterface DashboardStatsProps {\n  stats: StatsType\n}\n\nexport function DashboardStats({ stats }: DashboardStatsProps) {\n  const statItems = [\n    {\n      name: 'Total Consultations',\n      value: stats.total_consultations,\n      icon: FileText,\n      color: 'text-teal-600',\n      bgColor: 'bg-teal-100',\n    },\n    {\n      name: 'Pending Review',\n      value: stats.pending_consultations,\n      icon: Clock,\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-100',\n    },\n    {\n      name: 'Approved',\n      value: stats.approved_consultations,\n      icon: CheckCircle,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      name: 'Today',\n      value: stats.today_consultations,\n      icon: Calendar,\n      color: 'text-amber-600',\n      bgColor: 'bg-amber-100',\n    },\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 h-full\">\n      {statItems.map((item) => {\n        const Icon = item.icon\n        return (\n          <div\n            key={item.name}\n            className=\"bg-white/80 backdrop-blur-sm overflow-hidden shadow-lg rounded-lg border border-orange-200/50 hover:shadow-xl transition-all duration-300 hover:scale-105 h-full flex flex-col\"\n          >\n            <div className=\"p-4 flex-1 flex flex-col justify-center items-center text-center\">\n              <div className=\"flex flex-col items-center space-y-2\">\n                <div className=\"flex-shrink-0\">\n                  <div className={`w-6 h-6 ${item.bgColor} rounded-lg flex items-center justify-center shadow-md`}>\n                    <Icon className={`w-4 h-4 ${item.color}`} />\n                  </div>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-slate-800 leading-tight mb-1\">\n                      {item.name}\n                    </dt>\n                    <dd className=\"text-xl font-bold text-slate-800\">\n                      {item.value}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      })}\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAOO,SAAS,eAAe,EAAE,KAAK,EAAuB;IAC3D,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,MAAM,mBAAmB;YAChC,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,qBAAqB;YAClC,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,sBAAsB;YACnC,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO,MAAM,mBAAmB;YAChC,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC;YACd,MAAM,OAAO,KAAK,IAAI;YACtB,qBACE,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,sDAAsD,CAAC;8CAC7F,cAAA,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAhBhB,KAAK,IAAI;;;;;QAwBpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/consultations-list.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConsultationsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConsultationsList() from the server but ConsultationsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/consultations-list.tsx <module evaluation>\",\n    \"ConsultationsList\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,iFACA", "debugId": null}}, {"offset": {"line": 2451, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/consultations-list.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ConsultationsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConsultationsList() from the server but ConsultationsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/consultations-list.tsx\",\n    \"ConsultationsList\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6DACA", "debugId": null}}, {"offset": {"line": 2465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2475, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/quota-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const QuotaCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuotaCard() from the server but QuotaCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/quota-card.tsx <module evaluation>\",\n    \"QuotaCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yEACA", "debugId": null}}, {"offset": {"line": 2489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/quota-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const QuotaCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuotaCard() from the server but QuotaCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/quota-card.tsx\",\n    \"QuotaCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qDACA", "debugId": null}}, {"offset": {"line": 2503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2513, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/referral-stats.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReferralStats = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReferralStats() from the server but ReferralStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/referral-stats.tsx <module evaluation>\",\n    \"ReferralStats\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA", "debugId": null}}, {"offset": {"line": 2527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/referral-stats.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReferralStats = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReferralStats() from the server but ReferralStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/referral-stats.tsx\",\n    \"ReferralStats\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2551, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/data/info-data.tsx"], "sourcesContent": ["import { getUser, getDoctorQuota } from '@/lib/auth/dal'\nimport { getConsultations, getConsultationStats } from '@/lib/actions/consultations'\nimport { DashboardStats } from '@/components/analytics/dashboard-stats'\nimport { ConsultationsList } from '@/components/analytics/consultations-list'\nimport { QuotaCard } from '@/components/analytics/quota-card'\nimport { ReferralStats } from '@/components/analytics/referral-stats'\n\ninterface InfoDataProps {\n  userId: string\n}\n\nexport async function InfoData({ userId }: InfoDataProps) {\n  // This runs inside Suspense boundary, not blocking initial page render\n  // OPTIMIZED: Load only initial page (15 items) for fast initial render + real stats\n  const [user, consultationsResult, quotaInfo, statsResult] = await Promise.all([\n    getUser(),\n    getConsultations({ page: 1, pageSize: 15 }), // Small initial load with pagination\n    getDoctorQuota(userId),\n    getConsultationStats(), // Get real stats from database\n  ])\n\n  const consultations = consultationsResult.success ? consultationsResult.data.consultations || [] : []\n  const hasMore = consultationsResult.success ? consultationsResult.data.hasMore : false\n\n  // Use real stats from database instead of calculating from 15 records\n  const stats = statsResult.success ? {\n    total_consultations: statsResult.data.total_consultations,\n    pending_consultations: statsResult.data.pending_consultations,\n    generated_consultations: consultations.filter(c => c.status === 'generated').length, // Keep from list for now\n    approved_consultations: statsResult.data.approved_consultations,\n    today_consultations: statsResult.data.today_consultations,\n  } : {\n    total_consultations: 0,\n    pending_consultations: 0,\n    generated_consultations: 0,\n    approved_consultations: 0,\n    today_consultations: 0,\n  }\n\n  return (\n    <div className=\"space-y-6 lg:space-y-8\">\n      {/* Dashboard Layout: 2x2 Left, Quota Middle, Referral Right */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* 2x2 Stats Grid - Left (equal width) */}\n        <div className=\"lg:col-span-1\">\n          <DashboardStats stats={stats} />\n        </div>\n        \n        {/* Quota Card - Middle (equal width) */}\n        <div className=\"lg:col-span-1\">\n          {quotaInfo && <QuotaCard quota={quotaInfo} doctorId={userId} />}\n        </div>\n        \n        {/* Referral Card - Right (equal width) */}\n        <div className=\"lg:col-span-1\">\n          <ReferralStats doctorId={userId} />\n        </div>\n      </div>\n\n      {/* Consultations List */}\n      <div className=\"relative\">\n        <div className=\"absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse\"></div>\n        <div className=\"relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl border border-white/20\">\n          <div className=\"px-6 py-4 border-b border-white/30 bg-gradient-to-r from-indigo-50/50 to-purple-50/50\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h2 className=\"text-lg font-medium text-slate-800\">\n                  Patient Consultations\n                </h2>\n                <p className=\"text-sm text-slate-600\">\n                  Review and manage patient consultation summaries\n                </p>\n              </div>\n              <a\n                href=\"/dashboard\"\n                className=\"inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95\"\n                title=\"Add New Recording\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n          <ConsultationsList consultations={consultations} hasMore={hasMore} />\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMO,eAAe,SAAS,EAAE,MAAM,EAAiB;IACtD,uEAAuE;IACvE,oFAAoF;IACpF,MAAM,CAAC,MAAM,qBAAqB,WAAW,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC5E,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;QACN,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD,EAAE;YAAE,MAAM;YAAG,UAAU;QAAG;QACzC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD;KACpB;IAED,MAAM,gBAAgB,oBAAoB,OAAO,GAAG,oBAAoB,IAAI,CAAC,aAAa,IAAI,EAAE,GAAG,EAAE;IACrG,MAAM,UAAU,oBAAoB,OAAO,GAAG,oBAAoB,IAAI,CAAC,OAAO,GAAG;IAEjF,sEAAsE;IACtE,MAAM,QAAQ,YAAY,OAAO,GAAG;QAClC,qBAAqB,YAAY,IAAI,CAAC,mBAAmB;QACzD,uBAAuB,YAAY,IAAI,CAAC,qBAAqB;QAC7D,yBAAyB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACnF,wBAAwB,YAAY,IAAI,CAAC,sBAAsB;QAC/D,qBAAqB,YAAY,IAAI,CAAC,mBAAmB;IAC3D,IAAI;QACF,qBAAqB;QACrB,uBAAuB;QACvB,yBAAyB;QACzB,wBAAwB;QACxB,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qJAAA,CAAA,iBAAc;4BAAC,OAAO;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;kCACZ,2BAAa,8OAAC,gJAAA,CAAA,YAAS;4BAAC,OAAO;4BAAW,UAAU;;;;;;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oJAAA,CAAA,gBAAa;4BAAC,UAAU;;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAGnD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAIxC,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK7E,8OAAC,wJAAA,CAAA,oBAAiB;gCAAC,eAAe;gCAAe,SAAS;;;;;;;;;;;;;;;;;;;;;;;;AAKpE", "debugId": null}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/ui/skeleton-loaders.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminDashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"AdminDashboardSkeleton\",\n);\nexport const ConsultationsListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"ConsultationsListSkeleton\",\n);\nexport const DashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"DashboardSkeleton\",\n);\nexport const DashboardStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"DashboardStatsSkeleton\",\n);\nexport const InfoPageSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"InfoPageSkeleton\",\n);\nexport const QuotaCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"QuotaCardSkeleton\",\n);\nexport const ReferralStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"ReferralStatsSkeleton\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,wEACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,wEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,wEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,wEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,wEACA", "debugId": null}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/ui/skeleton-loaders.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminDashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"AdminDashboardSkeleton\",\n);\nexport const ConsultationsListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"ConsultationsListSkeleton\",\n);\nexport const DashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"DashboardSkeleton\",\n);\nexport const DashboardStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"DashboardStatsSkeleton\",\n);\nexport const InfoPageSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"InfoPageSkeleton\",\n);\nexport const QuotaCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"QuotaCardSkeleton\",\n);\nexport const ReferralStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx\",\n    \"ReferralStatsSkeleton\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,oDACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,oDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,oDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,oDACA", "debugId": null}}, {"offset": {"line": 2842, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/app/info/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { Suspense } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { verifySession } from '@/lib/auth/dal'\nimport { InfoData } from '@/components/data/info-data'\nimport { InfoPageSkeleton } from '@/components/ui/skeleton-loaders'\nimport { <PERSON>rk<PERSON>, BarChart3 } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Info - Celer AI',\n  description: 'View statistics, quota information, and referral details',\n}\n\nexport default async function InfoPage() {\n  // OPTIMIZED: Only verify session (fast), then stream the rest\n  const session = await verifySession()\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden\">\n      {/* Background Elements */}\n      <div className=\"fixed inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse\"></div>\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000\"></div>\n      </div>\n\n      {/* Floating Navigation */}\n      <nav className=\"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20\">\n        <div className=\"flex items-center space-x-8\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"relative w-8 h-8\">\n              <Image\n                src=\"/celer-ai-logo.svg\"\n                alt=\"Celer AI\"\n                width={32}\n                height={32}\n                className=\"rounded-lg\"\n              />\n            </div>\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold\">\n              Celer AI\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <Link\n              href=\"/dashboard\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Dashboard\n            </Link>\n            <Link\n              href=\"/settings\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Settings\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4\">\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse\">\n              Analytics\n            </span>\n          </h1>\n\n          <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12\">\n            <BarChart3 className=\"w-4 h-4 text-indigo-600 animate-pulse\" />\n            <span className=\"text-indigo-700 text-sm font-medium\">Analytics & Insights</span>\n            <Sparkles className=\"w-4 h-4 text-purple-600\" />\n          </div>\n        </div>\n\n        {/* STREAMING: Data loads progressively while user sees immediate structure */}\n        <Suspense fallback={<InfoPageSkeleton />}>\n          <InfoData userId={session.userId} />\n        </Suspense>\n      </main>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe;IAC5B,8DAA8D;IAC9D,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAElC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAK,WAAU;8CAA8H;;;;;;;;;;;;sCAIhJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAA0G;;;;;;;;;;;0CAK5H,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAKxB,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,wBAAU,8OAAC,+IAAA,CAAA,mBAAgB;;;;;kCACnC,cAAA,8OAAC,0IAAA,CAAA,WAAQ;4BAAC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAK1C", "debugId": null}}]}