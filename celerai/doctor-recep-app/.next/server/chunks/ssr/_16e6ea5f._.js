module.exports={234955:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],c=(0,d.default)("circle-check-big",b)}},492629:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({CheckCircle:()=>d.default});var d=a.i(234955)},172862:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DashboardStats:()=>i});var d=a.i(129629),e=a.i(122146),f=a.i(293499),g=a.i(492629),h=a.i(640836);function i({stats:a}){let b=[{name:"Total Consultations",value:a.total_consultations,icon:e.FileText,color:"text-teal-600",bgColor:"bg-teal-100"},{name:"Pending Review",value:a.pending_consultations,icon:f.Clock,color:"text-orange-600",bgColor:"bg-orange-100"},{name:"Approved",value:a.approved_consultations,icon:g.CheckCircle,color:"text-green-600",bgColor:"bg-green-100"},{name:"Today",value:a.today_consultations,icon:h.Calendar,color:"text-amber-600",bgColor:"bg-amber-100"}];return(0,d.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 h-full",children:b.map(a=>{let b=a.icon;return(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm overflow-hidden shadow-lg rounded-lg border border-orange-200/50 hover:shadow-xl transition-all duration-300 hover:scale-105 h-full flex flex-col",children:(0,d.jsx)("div",{className:"p-4 flex-1 flex flex-col justify-center items-center text-center",children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:`w-6 h-6 ${a.bgColor} rounded-lg flex items-center justify-center shadow-md`,children:(0,d.jsx)(b,{className:`w-4 h-4 ${a.color}`})})}),(0,d.jsx)("div",{className:"flex-1 min-w-0",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-slate-800 leading-tight mb-1",children:a.name}),(0,d.jsx)("dd",{className:"text-xl font-bold text-slate-800",children:a.value})]})})]})})},a.name)})})}},469785:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ConsultationsList:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call ConsultationsList() from the server but ConsultationsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/consultations-list.tsx <module evaluation>","ConsultationsList")}},821004:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ConsultationsList:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call ConsultationsList() from the server but ConsultationsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/consultations-list.tsx","ConsultationsList")}},296885:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(469785);var d=a.i(821004);a.n(d)},739600:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({QuotaCard:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call QuotaCard() from the server but QuotaCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/quota-card.tsx <module evaluation>","QuotaCard")}},855161:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({QuotaCard:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call QuotaCard() from the server but QuotaCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/quota-card.tsx","QuotaCard")}},890648:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(739600);var d=a.i(855161);a.n(d)},42015:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ReferralStats:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call ReferralStats() from the server but ReferralStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/referral-stats.tsx <module evaluation>","ReferralStats")}},733401:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ReferralStats:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call ReferralStats() from the server but ReferralStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/analytics/referral-stats.tsx","ReferralStats")}},833421:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(42015);var d=a.i(733401);a.n(d)},350343:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({InfoData:()=>k});var d=a.i(129629),e=a.i(76803),f=a.i(755284),g=a.i(172862),h=a.i(296885),i=a.i(890648),j=a.i(833421);async function k({userId:a}){let[b,c,k,l]=await Promise.all([(0,e.getUser)(),(0,f.getConsultations)({page:1,pageSize:15}),(0,e.getDoctorQuota)(a),(0,f.getConsultationStats)()]),m=c.success&&c.data.consultations||[],n=!!c.success&&c.data.hasMore,o=l.success?{total_consultations:l.data.total_consultations,pending_consultations:l.data.pending_consultations,generated_consultations:m.filter(a=>"generated"===a.status).length,approved_consultations:l.data.approved_consultations,today_consultations:l.data.today_consultations}:{total_consultations:0,pending_consultations:0,generated_consultations:0,approved_consultations:0,today_consultations:0};return(0,d.jsxs)("div",{className:"space-y-6 lg:space-y-8",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)(g.DashboardStats,{stats:o})}),(0,d.jsx)("div",{className:"lg:col-span-1",children:k&&(0,d.jsx)(i.QuotaCard,{quota:k,doctorId:a})}),(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)(j.ReferralStats,{doctorId:a})})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"}),(0,d.jsxs)("div",{className:"relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl border border-white/20",children:[(0,d.jsx)("div",{className:"px-6 py-4 border-b border-white/30 bg-gradient-to-r from-indigo-50/50 to-purple-50/50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-medium text-slate-800",children:"Patient Consultations"}),(0,d.jsx)("p",{className:"text-sm text-slate-600",children:"Review and manage patient consultation summaries"})]}),(0,d.jsx)("a",{href:"/dashboard",className:"inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95",title:"Add New Recording",children:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]})}),(0,d.jsx)(h.ConsultationsList,{consultations:m,hasMore:n})]})]})]})}},273925:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],c=(0,d.default)("chart-column",b)}},948998:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({BarChart3:()=>d.default});var d=a.i(273925)},333566:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>m,metadata:()=>b});var d=a.i(129629),e=a.i(465421),f=a.i(331055),g=a.i(99492),h=a.i(76803),i=a.i(350343),j=a.i(968123),k=a.i(954124),l=a.i(948998);let b={title:"Info - Celer AI",description:"View statistics, quota information, and referral details"};async function m(){let a=await (0,h.verifySession)();return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden",children:[(0,d.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"}),(0,d.jsx)("div",{className:"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"})]}),(0,d.jsx)("nav",{className:"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20",children:(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(g.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold",children:"Celer AI"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(f.default,{href:"/dashboard",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Dashboard"}),(0,d.jsx)(f.default,{href:"/settings",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Settings"})]})]})}),(0,d.jsxs)("main",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4",children:(0,d.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse",children:"Analytics"})}),(0,d.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12",children:[(0,d.jsx)(l.BarChart3,{className:"w-4 h-4 text-indigo-600 animate-pulse"}),(0,d.jsx)("span",{className:"text-indigo-700 text-sm font-medium",children:"Analytics & Insights"}),(0,d.jsx)(k.Sparkles,{className:"w-4 h-4 text-purple-600"})]})]}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(j.InfoPageSkeleton,{}),children:(0,d.jsx)(i.InfoData,{userId:a.userId})})]})]})}}}};

//# sourceMappingURL=_16e6ea5f._.js.map