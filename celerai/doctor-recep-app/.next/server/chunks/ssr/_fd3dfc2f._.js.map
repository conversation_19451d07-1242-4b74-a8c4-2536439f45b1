{"version": 3, "sources": ["turbopack:///[project]/node_modules/@vercel/analytics/src/nextjs/index.tsx", "turbopack:///[project]/node_modules/@vercel/analytics/src/nextjs/utils.ts", "turbopack:///[project]/node_modules/@vercel/analytics/package.json", "turbopack:///[project]/node_modules/@vercel/analytics/src/queue.ts", "turbopack:///[project]/node_modules/@vercel/analytics/src/utils.ts", "turbopack:///[project]/node_modules/@vercel/analytics/src/generic.ts", "turbopack:///[project]/node_modules/@vercel/analytics/src/react/utils.ts", "turbopack:///[project]/node_modules/@vercel/analytics/src/react/index.tsx", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/nextjs/index.tsx", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/nextjs/utils.ts", "turbopack:///[project]/node_modules/@vercel/speed-insights/package.json", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/queue.ts", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/utils.ts", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/generic.ts", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/react/utils.ts", "turbopack:///[project]/node_modules/@vercel/speed-insights/src/react/index.tsx", "turbopack:///[project]/node_modules/next/src/client/set-attributes-from-props.ts", "turbopack:///[project]/node_modules/next/src/client/request-idle-callback.ts", "turbopack:///[project]/node_modules/next/src/client/script.tsx", "turbopack:///[project]/src/components/analytics/analytics-provider.tsx"], "sourcesContent": ["'use client';\nimport React, { Suspense, type ReactNode } from 'react';\nimport { Analytics as AnalyticsScript } from '../react';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath, useRoute } from './utils';\n\ntype Props = Omit<AnalyticsProps, 'route' | 'disableAutoTrack'>;\n\nfunction AnalyticsComponent(props: Props): ReactNode {\n  const { route, path } = useRoute();\n  return (\n    <AnalyticsScript\n      path={path}\n      route={route}\n      {...props}\n      basePath={getBasePath()}\n      framework=\"next\"\n    />\n  );\n}\n\nexport function Analytics(props: Props): null {\n  // Because of incompatible types between ReactNode in React 19 and React 18 we return null (which is also what we render)\n  return (\n    <Suspense fallback={null}>\n      <AnalyticsComponent {...props} />\n    </Suspense>\n  ) as never;\n}\n\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "'use client';\n/* eslint-disable @typescript-eslint/no-unnecessary-condition -- can be empty in pages router */\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js';\nimport { computeRoute } from '../utils';\n\nexport const useRoute = (): {\n  route: string | null;\n  path: string;\n} => {\n  const params = useParams();\n  const searchParams = useSearchParams();\n  const path = usePathname();\n\n  // Until we have route parameters, we don't compute the route\n  if (!params) {\n    return { route: null, path };\n  }\n  // in Next.js@13, useParams() could return an empty object for pages router, and we default to searchParams.\n  const finalParams = Object.keys(params).length\n    ? params\n    : Object.fromEntries(searchParams.entries());\n  return { route: computeRoute(path, finalParams), path };\n};\n\nexport function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.5.0\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.mjs\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.0\",\n    \"@types/react\": \"^18.3.12\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"server-only\": \"^0.0.1\",\n    \"svelte\": \"^5.1.10\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.12\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@remix-run/react\": \"^2\",\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@remix-run/react\": {\n      \"optional\": true\n    },\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, AnalyticsProps, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: AnalyticsProps & { basePath?: string }\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/script.debug.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/insights/script.js`;\n  }\n  return '/_vercel/insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n  BeforeSend,\n  BeforeSendEvent,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n  computeRoute,\n  getScriptSrc,\n} from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n * @param [props.disableAutoTrack] - Whether the injected script should track page views from pushState events. Disable if route is updated after pushState, a manually call page pageview().\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n    disableAutoTrack?: boolean;\n    basePath?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/insights`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({\n  route,\n  path,\n}: {\n  route?: string | null;\n  path?: string;\n}): void {\n  window.va?.('pageview', { route, path });\n}\n\nexport { inject, track, pageview, computeRoute };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "'use client';\nimport { useEffect } from 'react';\nimport { inject, track, pageview } from '../generic';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath } from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @example\n * ```js\n * import { Analytics } from '@vercel/analytics/react';\n *\n * export default function App() {\n *  return (\n *   <div>\n *    <Analytics />\n *    <h1>My App</h1>\n *  </div>\n * );\n * }\n * ```\n */\nfunction Analytics(\n  props: AnalyticsProps & {\n    framework?: string;\n    route?: string | null;\n    path?: string | null;\n    basePath?: string;\n  }\n): null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.va?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: only run once\n  useEffect(() => {\n    inject({\n      framework: props.framework || 'react',\n      basePath: props.basePath ?? getBasePath(),\n      ...(props.route !== undefined && { disableAutoTrack: true }),\n      ...props,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps -- only run once\n  }, []);\n\n  useEffect(() => {\n    // explicitely track page view, since we disabled auto tracking\n    if (props.route && props.path) {\n      pageview({ route: props.route, path: props.path });\n    }\n  }, [props.route, props.path]);\n\n  return null;\n}\n\nexport { track, Analytics };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "'use client';\n\nimport React, { Suspense } from 'react';\nimport { SpeedInsights as SpeedInsightsScript } from '../react';\nimport type { SpeedInsightsProps } from '../types';\nimport { getBasePath, useRoute } from './utils';\n\ntype Props = Omit<SpeedInsightsProps, 'route'>;\n\nfunction SpeedInsightsComponent(props: Props): React.ReactElement {\n  const route = useRoute();\n\n  return (\n    <SpeedInsightsScript\n      route={route}\n      {...props}\n      framework=\"next\"\n      basePath={getBasePath()}\n    />\n  );\n}\n\nexport function SpeedInsights(props: Props): null {\n  // Because of incompatible types between ReactNode in React 19 and React 18 we return null (which is also what we render)\n  return (\n    <Suspense fallback={null}>\n      <SpeedInsightsComponent {...props} />\n    </Suspense>\n  ) as never;\n}\n", "'use client';\n/* eslint-disable @typescript-eslint/no-unnecessary-condition -- can be empty in pages router */\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js';\nimport { computeRoute } from '../utils';\n\nexport const useRoute = (): string | null => {\n  const params = useParams();\n  const searchParams = useSearchParams() || new URLSearchParams();\n  const path = usePathname();\n  // Until we have route parameters, we don't compute the route\n  if (!params) {\n    return null;\n  }\n  // in Next.js@13, useParams() could return an empty object for pages router, and we default to searchParams.\n  const finalParams = Object.keys(params).length\n    ? params\n    : Object.fromEntries(searchParams.entries());\n  return computeRoute(path, finalParams);\n};\n\nexport function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "{\n  \"name\": \"@vercel/speed-insights\",\n  \"version\": \"1.2.0\",\n  \"description\": \"Speed Insights is a tool for measuring web performance and providing suggestions for improvement.\",\n  \"keywords\": [\n    \"speed-insights\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/speed-insights\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.js\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"postinstall\": \"node scripts/postinstall.mjs\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"devDependencies\": {\n    \"@remix-run/react\": \"^2.14.0\",\n    \"@sveltejs/kit\": \"^2.8.1\",\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.1\",\n    \"@types/react\": \"^18.3.12\",\n    \"copyfiles\": \"^2.4.1\",\n    \"jsdom\": \"^25.0.1\",\n    \"next\": \"^14.0.4\",\n    \"react\": \"^18.3.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"svelte\": \"^5.2.7\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.13\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.si) return;\n\n  window.si = function a(...params): void {\n    (window.siq = window.siq || []).push(params);\n  };\n};\n", "import type { SpeedInsightsProps } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function isProduction(): boolean {\n  return detectEnvironment() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return detectEnvironment() === 'development';\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null,\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: SpeedInsightsProps & { basePath?: string },\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.debug.js';\n  }\n  if (props.dsn) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/speed-insights/script.js`;\n  }\n  return '/_vercel/speed-insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type { SpeedInsightsProps } from './types';\nimport { computeRoute, getScriptSrc, isBrowser, isDevelopment } from './utils';\n\n/**\n * Injects the Vercel Speed Insights script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/speed-insights).\n * @param [props] - Speed Insights options.\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.sampleRate] - When setting to 0.5, 50% of the events will be sent to Vercel Speed Insights. Defaults to `1`.\n * @param [props.route] - The dynamic route of the page.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n */\nfunction injectSpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  } = {},\n): {\n  setRoute: (route: string | null) => void;\n} | null {\n  // When route is null, it means that pages router is not ready yet. Will resolve soon\n  if (!isBrowser() || props.route === null) return null;\n\n  initQueue();\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n\n  if (props.beforeSend) {\n    window.si?.('beforeSend', props.beforeSend);\n  }\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.sampleRate) {\n    script.dataset.sampleRate = props.sampleRate.toString();\n  }\n  if (props.route) {\n    script.dataset.route = props.route;\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  script.onerror = (): void => {\n    // eslint-disable-next-line no-console -- Logging is okay here\n    console.log(\n      `[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`,\n    );\n  };\n\n  document.head.appendChild(script);\n\n  return {\n    setRoute: (route: string | null): void => {\n      script.dataset.route = route ?? undefined;\n    },\n  };\n}\n\nexport { injectSpeedInsights, computeRoute };\nexport type { SpeedInsightsProps };\n\n// eslint-disable-next-line import/no-default-export -- Allow default export\nexport default {\n  injectSpeedInsights,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "'use client';\n\nimport { useEffect, useRef } from 'react';\nimport type { SpeedInsightsProps } from '../types';\nimport { computeRoute, injectSpeedInsights } from '../generic';\nimport { getBasePath } from './utils';\n\nexport function SpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  },\n): JSX.Element | null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.si?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  const setScriptRoute = useRef<((path: string) => void) | null>(null);\n  useEffect(() => {\n    if (!setScriptRoute.current) {\n      const script = injectSpeedInsights({\n        framework: props.framework ?? 'react',\n        basePath: props.basePath ?? getBasePath(),\n        ...props,\n      });\n      if (script) {\n        setScriptRoute.current = script.setRoute;\n      }\n    } else if (props.route) {\n      setScriptRoute.current(props.route);\n    }\n  }, [props.route]);\n\n  return null;\n}\n\nexport { computeRoute };\n", "const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n", "export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n", "'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n", "'use client'\n\nimport { useEffect } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { initializeAnalytics, trackPageView } from '@/lib/analytics'\n\n/**\n * Analytics Provider Component\n * Handles client-side analytics initialization and page tracking\n * Respects the two-zone security model\n * Uses lazy initialization to avoid blocking page load\n */\nexport function AnalyticsProvider({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname()\n\n  useEffect(() => {\n    // Lazy initialize analytics after page is fully loaded and idle\n    const timer = setTimeout(() => {\n      // Use requestIdleCallback for better performance\n      if ('requestIdleCallback' in window) {\n        requestIdleCallback(() => initializeAnalytics(), { timeout: 2000 })\n      } else {\n        initializeAnalytics()\n      }\n    }, 500) // Increased delay to avoid blocking initial render\n\n    return () => clearTimeout(timer)\n  }, [])\n\n  useEffect(() => {\n    // Lazy track page changes with debouncing and idle callback\n    const timer = setTimeout(() => {\n      if ('requestIdleCallback' in window) {\n        requestIdleCallback(() => trackPageView(document.title), { timeout: 1000 })\n      } else {\n        trackPageView(document.title)\n      }\n    }, 100)\n\n    return () => clearTimeout(timer)\n  }, [pathname])\n\n  return <>{children}</>\n}\n"], "names": ["get<PERSON><PERSON><PERSON><PERSON>", "Analytics", "SpeedInsights", "setAttributesFromProps", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "includes", "el", "props", "p", "value", "Object", "entries", "hasOwnProperty", "undefined", "toLowerCase", "tagName", "setAttribute", "String", "removeAttribute", "cancelIdleCallback", "requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout", "handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "src", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "body", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "defineProperty"], "mappings": "sFACA,IAAA,EAAgD,CAAzC,CAAyC,CAAA,CAAA,MAAhC,ECChB,EAAiC,EAAuB,CAA/C,AAA+C,CAAA,MAAA,EEF3C,AHCmC,ECC5B,AEFK,KAEnB,CAF+B,MAExB,CFAoB,CEApB,CAAI,CAAA,CAEf,OAAO,EAAA,CAAK,SAAS,GAAK,CAAA,EAAc,AACtC,CAAC,OAAO,GAAA,CAAM,OAAO,GAAA,EAAO,EAAC,EAAG,IAAA,CAAK,EACvC,EACF,ECLO,ADGwC,SCH/B,IACd,MAAyB,EADU,WAC5B,OAAO,MAChB,CAEA,SAAS,IASP,MAAO,UATkD,EAU3D,CAoBO,SAAS,IACd,MAAO,AAAc,MADkB,EACxB,QARR,EADM,IAAc,MAAJ,CAAW,GAAA,CAAM,GAAkB,GAC3C,YAAA,CASjB,CAyEA,SAAS,EAAkB,CAAA,EAAuB,AAChD,OAAO,AAAI,OAAO,CAAA,CAAA,EAAI,AAAa,AAI5B,EAAO,GAJ0B,CAAC,GAI3B,CAAQ,sBAAuB,MAAM,EAJV,WAAA,CAAa,CACxD,CGpFA,SAAS,EACP,CAAA,EAMM,AAyBN,MAxBA,CAAA,EAAA,EAAA,SAAA,EAAU,KArCZ,CAqCkB,GArClB,EAsCQ,EAAM,UAAA,EAAY,CACpB,AAFJ,AAEI,MAAA,CAAA,EAAA,GAAA,IAAO,EAAA,GAAP,EAAA,IAAA,CAAA,OAAY,aAAc,EAAM,WAAA,CAEpC,EAAG,CAAC,EAAM,UAAU,CAAC,EAGrB,CAAA,EAAA,EAAA,SAAA,EAAU,MFbZ,AEakB,AACd,SFdK,AACP,EAII,CACF,OAAO,CACT,AEMA,CFNA,EACM,IAvCR,EAwCE,GAAI,CAAC,IAAa,MAAH,EDtBV,ACsBa,AAElB,SDxBsB,AAAR,EAAqB,MAAA,EAAc,AACjD,GAAa,SAAT,EAAiB,CACnB,OAAO,GAAA,CAAM,IACb,MACF,CAEA,OAJiC,AAI1B,GAAA,CAAM,CACf,ECiBU,EAAM,IAAI,EAElB,IAEI,EAAM,IAFA,MAEA,EAAY,CACpB,OAAA,EAAA,GAAA,IAAO,EAAA,GAAP,EAAA,IAAA,CAAA,OAAY,aAAc,EAAM,WAAA,EAGlC,IAAM,EDwEN,AAAI,ACxEqB,EDwEf,ECxEE,CAAkB,MDwEpB,CACD,CADY,CACN,SAAA,CAEX,IACK,UADS,GAAG,sCAGjB,EAAM,QAAA,CACD,CADW,AACX,EAAG,EAAM,QAAQ,CAAA,mBAAA,CAAA,CAEnB,8BC/EP,GAAI,SAAS,IAAA,CAAK,aAAA,CAAc,CAAA,aAAA,EAAgB,EAAG,CAAA,CAAA,CAAI,EAAG,OAE1D,IAAM,EAAS,SAAS,aAAA,CAAc,QAAQ,EAC9C,EAAO,GAAA,CAAM,EACb,EAAO,KAAA,EAAQ,EACf,EAAO,OAAA,CAAQ,IAAA,CACb,AHzDM,OGyDN,cAAe,EAAM,SAAA,CAAY,CAAA,CAAA,EAAI,EAAM,SAAS,CAAA,CAAA,CAAK,EAAA,CAAA,CAC3D,EAAO,OAAA,CAAQ,IAAA,CHzDJ,EGyDW,MAElB,EAAM,gBAAA,EAAkB,CAC1B,EAAO,OAAA,CAAQ,gBAAA,CAAmB,GAAA,EAEhC,EAAM,QAAA,CACR,CADkB,CACX,OAAA,CAAQ,QAAA,CAAW,EAAM,QAAA,CACvB,EAAM,QAAA,EAAU,CACzB,EAAO,OAAA,CAAQ,QAAA,CAAW,CAAA,EAAG,EAAM,QAAQ,CAAA,UAAA,EAEzC,EAAM,GAAA,EAAK,CACb,EAAO,OAAA,CAAQ,GAAA,CAAM,EAAM,GAAA,EAG7B,EAAO,OAAA,CAAU,KACf,CAD2B,GACrB,EAAe,IACjB,UAD+B,mDAE/B,4IAGJ,QAAQ,GAAA,CACN,CAAA,kDAAA,EAAqD,EAAG,CAAA,CAAA,EAAK,EAAY,CAAA,CAE7E,EAEI,MAAmC,AAJsC,IAItD,EAAM,CAAiB,CAA5B,GAAW,GAC3B,EAAO,OAAA,CAAQ,KAAA,CAAQ,OAAA,EAGzB,SAAS,IAAA,CAAK,WAAA,CAAY,EAC5B,EE5CW,CACL,CF0C4B,SE1CjB,EAAM,SAAA,EAAa,QAC9B,SAAU,EAAM,QAAA,ED/Cf,AC+C2B,SD/ClB,EAKd,CC0C4C,ED1CrB,SALyB,IAK5C,OAAO,SAA2B,KAAuB,IAAhB,QAAQ,CAAqB,EAArB,CAGrD,OAAO,QAAQ,GAAA,CAAI,uCAAA,AACrB,ICuCM,GAAoB,KAAA,IAAhB,EAAM,KAAA,EAAuB,CAAE,kBAAkB,CAAK,CAAA,CAC1D,GAAG,CAAA,AACL,CAAC,CAEH,EAAG,CAAC,CAAC,EAEL,CAAA,EAAA,EAAA,SAAA,EAAU,KAEJ,CAFU,CAEJ,KAAA,EAAS,EAAM,IAAA,EFqF7B,AErFmC,AAC7B,IAHJ,KFuFO,AAAS,OAChB,CAAA,MACA,CAAA,CACF,EAGS,AAnJT,IAAA,CAoJE,OAAA,CAAA,EAAA,EAAA,KAAO,EAAA,GAAP,EAAA,IAAA,CAAA,OAAY,WAAY,OAAE,OAAO,CAAK,EACxC,EE5Fe,CAAE,MAAO,EAAM,KAAA,CAAO,KAAM,EAAM,IAAA,AAAK,CAAC,CAErD,EAAG,CAAC,EAAM,KAAA,CAAO,EAAM,IAAI,CAAC,EAErB,IACT,CNzDO,IAAM,EAAW,KAItB,CADG,GACG,EAAA,CAAA,EAAA,EAAS,SAAA,CAAU,GACnB,EAAA,CAAA,EAAA,EAAe,eAAA,CAAgB,GAC/B,EAAO,CAAA,EAAA,EAAA,WAAA,CAAY,UAGzB,AAAK,EAOE,CAAE,CAPL,IAAS,CAOG,AGuDX,SAAS,AACd,CAAA,CACA,CAAA,EACe,AACf,GAAI,CAAC,GAAY,CAAC,EAChB,OAAO,EAGT,CAJ8B,GAI1B,EAAS,EACb,GAAI,CACF,IAAM,EAAU,OAAO,OAAA,CAAQ,GAE/B,IAAA,GAFyC,AAE9B,CAAC,EAAK,EAAK,GAAA,AAAK,EACzB,GAAI,CAAC,EAD6B,IACvB,OAAA,CAAQ,GAAQ,CACzB,CADsB,GAChB,EAAU,EAAkB,GAC9B,EADmC,AAC3B,IAAA,CAAK,KACf,CADqB,CACZ,EAAO,AADQ,OACR,CAAQ,EAAS,CAAA,EAAA,EAAK,EAAG,CAAA,EAAG,CAEhD,CAGF,IAAA,GAAW,CAAC,EAAK,EAAK,GAAA,AAAK,EACzB,GAAI,GAD8B,GACxB,OAAA,CAAQ,GAAQ,CACxB,CADqB,GACf,EAAU,EAAkB,EAAM,IAAA,CAAK,GAAG,CAAC,EAC7C,EAAQ,IAAA,CAAK,KACf,CADqB,CACZ,EADe,AACR,OAAA,CAAQ,EAAS,CAAA,KAAA,EAAQ,EAAG,CAAA,EAAG,CAEnD,CAEF,OAAO,CACT,CAAA,MAAS,EAAG,CACV,OAAO,CACT,CACF,EHxF+B,EAHT,IAGe,GAHR,IAAA,CAAK,GAAQ,AAGM,GAHR,GAAE,CACpC,EACA,OAAO,WAAA,CAAY,EAAa,OAAA,CAAQ,CAAC,QACI,CAAK,EAN7C,CAAE,MAAO,UAAM,CAAK,CAO/B,EDdA,SAAS,EAAmB,CAAA,EAC1B,AADmD,GAC7C,OAAE,CAAA,MAAO,CAAA,CAAK,CAAI,IACxB,KADiC,EAE/B,EAAA,OAAA,CAAA,GAAA,UAAA,CAAC,EAAA,MACC,EACA,KAFF,GAGG,GAAG,CAAA,CACJ,SCSC,ADTSA,SCSAA,EAKd,EDd0B,CCcH,UALyB,GAK5C,OAAO,SAA2B,KAAuB,IAAhB,QAAQ,CAAqB,EAArB,CAGrD,OAAO,QAAQ,GAAA,CAAI,yCAAA,AACrB,IDjBM,UAAU,MAAA,EAGhB,CAEO,SAASC,EAAU,CAAA,EAAoB,AAE5C,OACE,EAAA,OAAA,CAAA,GAAA,UAAA,CAAA,EAAC,QAAA,CAAA,CAAS,GAAV,MAAoB,IAAA,EAClB,CAAA,CAAA,OAAA,CAAA,IAAA,SAAA,CAAC,EAAA,CAAoB,GAAG,CAAA,CAAO,CACjC,CAEJ,KAHM,wEQvBN,IAAA,EAAgC,CAAzB,CAAyB,CAAA,CAAA,MAAhB,ECAhB,EAAiC,EAAuB,CAA/C,AAA+C,CAAA,MAAA,EEF3C,AHEmB,ECAZ,AEFK,KAEnB,CAF+B,MAExB,CFAoB,CEApB,CAAI,CAAA,CAEf,OAAO,EAAA,CAAK,SAAS,GAAK,CAAA,EACxB,AADsC,CACrC,OAAO,GAAA,CAAM,OAAO,GAAA,EAAO,EAAC,EAAG,IAAA,CAAK,EACvC,EACF,EAF+C,ACiBxC,SAAS,IACd,OAAO,KADgC,AAEzC,CAqCA,SAAS,EAAkB,CAAA,AAtCA,EAuCzB,AADgD,IAtCjB,GAuCxB,AAAI,OAAO,CAAA,CAAA,EAAI,AAAa,AAI5B,EAAO,GAJ0B,CAAC,GAI3B,CAAQ,sBAAuB,MAAM,EAJV,WAAA,CAAa,CACxD,CGxDO,SAAS,EACd,CAAA,EAIoB,AACpB,CAAA,EAAA,EAAA,SAAA,EAAU,KAbZ,CAakB,GAblB,EAcQ,EAAM,UAAA,EAAY,CADxB,AAEI,OAAA,EAAA,GAAA,IAAO,EAAA,GAAP,EAAA,IAAA,CAAA,OAAY,aAAc,EAAM,WAAA,CAEpC,EAAG,CAAC,EAAM,UAAU,CAAC,EAErB,IAAM,EAAA,CAAA,EAAA,EAAiB,MAAA,EAAwC,IAAI,EAgBnE,MAfA,CAAA,EAAA,EAAA,SAAA,EAAU,KACR,CADc,EACT,CAAD,CAAgB,OAAA,CAST,EAAM,KAAA,CAVnB,CAU0B,AACtB,EAAe,OAAA,CAAQ,EAAM,KAAK,MAVP,CAC3B,IAAM,EFRZ,AEQqB,SFRZ,AACP,EAGI,CAAC,CAAA,EAGE,IArBT,EAuBE,GAAI,ADpBqB,CCoBpB,UAAU,EDpBR,OAAO,QCoBsB,KAAM,EAAtB,EAAM,KAAA,CAAgB,OAAO,KAEjD,IAEA,IAAM,EAFI,AD+CV,AAAI,EAAM,EC7CE,OD6CF,CC7Ce,AD8ChB,CADY,CACN,GC9Ce,MD8Cf,CAEX,IACK,UADS,GAAG,qDAGjB,EAAM,GAAA,CACD,CADM,2DAGX,EAAM,QAAA,CACD,CADW,AACX,EAAG,EAAM,QAAQ,CAAA,yBAAA,CAAA,CAEnB,oCCvDP,GAAI,SAAS,IAAA,CAAK,aAAA,CAAc,CAAA,aAAA,EAAgB,EAAG,CAAA,CAAA,CAAI,EAAG,OAAO,IAE7D,GAAM,UAAA,EAAY,CACpB,OAAA,EAAA,GAAA,IAAO,EAAA,GAAP,EAAA,IAAA,CAAA,OAAY,aAAc,EAAM,WAAA,EAGlC,IAAM,EAAS,SAAS,aAAA,CAAc,QAAQ,EAkC9C,OAjCA,EAAO,GAAA,CAAM,EACb,EAAO,KAAA,EAAQ,EACf,EAAO,OAAA,CAAQ,IAAA,CACb,AHtCM,OGsCN,mBAAe,EAAM,SAAA,CAAY,CAAA,CAAA,EAAI,EAAM,SAAS,CAAA,CAAA,CAAK,EAAA,CAAA,CAC3D,EAAO,OAAA,CAAQ,IAAA,CHtCJ,EGsCW,MAElB,EAAM,UAAA,EAAY,AACpB,GAAO,OAAA,CAAQ,UAAA,CAAa,EAAM,UAAA,CAAW,QAAA,EAAS,EAEpD,EAAM,KAAA,EAAO,CACf,EAAO,OAAA,CAAQ,KAAA,CAAQ,EAAM,KAAA,EAE3B,EAAM,QAAA,CACR,CADkB,CACX,OAAA,CAAQ,QAAA,CAAW,EAAM,QAAA,CACvB,EAAM,QAAA,EAAU,CACzB,EAAO,OAAA,CAAQ,QAAA,CAAW,CAAA,EAAG,EAAM,QAAQ,CAAA,uBAAA,EAEzC,EAAM,GAAA,EAAK,CACb,EAAO,OAAA,CAAQ,GAAA,CAAM,EAAM,GAAA,EAEzB,KAAmB,CAAgB,MAAV,CAAiB,CAA5B,GAAW,GAC3B,EAAO,OAAA,CAAQ,KAAA,CAAQ,OAAA,EAGzB,EAAO,OAAA,CAAU,KAEf,CAF2B,OAEnB,GAAA,CACN,CAAA,mDAAA,EAAsD,EAAG,CAAA,gEAAA,CAAA,CAE7D,EAEA,SAAS,IAAA,CAAK,WAAA,CAAY,GAEnB,CACL,EAH8B,OAGnB,AAAD,IACR,EAAO,IADiC,GACjC,CAAQ,KAAA,CAAQ,GAAS,KAAA,CAClC,CACF,CACF,EEpDyC,CACjC,UAAW,EAAM,SAAA,EAAa,QAC9B,SAAU,EAAM,QAAA,EDxBjB,ACwB6B,SDxBpB,EAKd,CCmB8C,EDnBvB,SALyB,IAK5C,OAAO,SAA2B,KAAuB,IAAhB,QAAQ,CAAqB,EAArB,CAGrD,OAAO,QAAQ,GAAA,CAAI,uCAAA,AACrB,ICgBQ,GAAG,CAAA,AACL,CAAC,CACG,KACF,EAAe,CADL,MACK,CAAU,EAAO,QAAA,CAEpC,CAGF,EAAG,CAAC,EAAM,CAHR,IAGa,CAAC,EAET,IACT,CN/BO,IAAM,EAAW,KACtB,CAD2C,GACrC,EAAA,CAAA,EAAA,EAAS,SAAA,CAAU,GACnB,EAAA,CAAA,EAAe,EAAA,eAAA,CAAgB,IAAK,IAAI,gBAAgB,AACxD,EAAA,CAAA,EAAA,EAAO,WAAA,CAAY,UAEpB,AAAL,EGgBK,AHTE,EAPH,IAAS,GGgBC,AACd,CAAA,CACA,CAAA,EACe,AACf,GAAI,CAAC,GAAY,CAAC,EAChB,OAAO,EAGT,CAJ8B,GAI1B,EAAS,EACb,GAAI,CACF,IAAM,EAAU,OAAO,OAAA,CAAQ,GAE/B,IAAA,GAFyC,AAE9B,CAAC,EAAK,EAAK,GAAA,AAAK,EACzB,GAAI,CAAC,EAD6B,IACvB,OAAA,CAAQ,GAAQ,CACzB,CADsB,GAChB,EAAU,EAAkB,GAC9B,EADmC,AAC3B,IAAA,CAAK,KACf,CADqB,CACZ,EADe,AACR,OAAA,CAAQ,EAAS,CAAA,EAAA,EAAK,EAAG,CAAA,EAAG,CAEhD,CAGF,IAAA,GAAW,CAAC,EAAK,EAAK,GAAA,AAAK,EACzB,GAAI,GAD8B,GACxB,OAAA,CAAQ,GAAQ,CACxB,CADqB,GACf,EAAU,EAAkB,EAAM,IAAA,CAAK,GAAG,CAAC,EAC7C,EAAQ,IAAA,CAAK,KACf,CADqB,CACZ,EADe,AACR,OAAA,CAAQ,EAAS,CAAA,KAAA,EAAQ,EAAG,CAAA,CAAG,EAEnD,CAEF,OAAO,CACT,CAAA,MAAS,EAAG,CACV,OAAO,CACT,CACF,EH1CsB,EAHA,IAGM,GAHC,IAAA,CAAK,GAAQ,AAGH,GAHC,GAAE,CACpC,EACA,OAAO,WAAA,CAAY,EAAa,OAAA,CAAQ,CAAC,GALpC,IAOX,EDTA,SAAS,EAAuB,CAAA,EAAkC,AAChE,IAAM,EAAQ,IAEd,KAFuB,EAGrB,EAAA,OAAA,CAAA,GAAA,UAAA,CAAC,EAAA,OACC,EACC,GAAG,CAFN,AAEM,CACJ,UAAU,OACV,SCGC,ADHSD,SCGAA,EAKd,EDR0B,CCQH,UALyB,GAK5C,OAAO,SAA2B,KAAuB,IAAhB,QAAQ,CAAqB,EAArB,CAGrD,OAAO,QAAQ,GAAA,CAAI,yCAAA,AACrB,GDZ4B,EAG5B,CAEO,SAASE,EAAc,CAAA,EAAoB,AAEhD,OACE,EAAA,OAAA,CAAA,GAAA,UAAA,CAAA,EAAC,QAAA,CAAA,CAAS,GAAV,MAAoB,IAAA,EAClB,CAAA,CAAA,OAAA,CAAA,IAAA,SAAA,CAAC,EAAA,CAAwB,GAAG,CAAA,CAAO,CACrC,CAEJ,KAHM,uIQFUC,yBAAAA,qCAAAA,KAxBhB,IAAMC,EAA4C,CAChDC,cAAe,iBACfC,UAAW,QACXC,QAAS,MACTC,UAAW,aACXC,SAAU,UACZ,EAEMC,EAAc,CAClB,SACA,UACA,0BACA,WACA,UACA,WACA,cACD,CAED,SAASC,EACPC,CAAY,EAEZ,MAAO,CAAC,QAAS,QAAS,WAAW,CAACC,QAAQ,CAACD,EACjD,CAEO,SAAST,EAAuBW,CAAe,CAAEC,CAAa,EACnE,IAAK,GAAM,CAACC,EAAGC,EAAM,GAAIC,OAAOC,OAAO,CAACJ,GAAQ,CAC9C,GAAI,CAACA,EAAMK,cAAc,CAACJ,IACtBN,EAAYG,QAAQ,CAACG,IAAI,KAGfK,IAAVJ,EAJ0B,KAIL,IAIzB,IAAML,EAAOR,CAAiB,CAACY,EAAE,EAAIA,EAAEM,WAAW,GAE/B,WAAfR,EAAGS,OAAO,EAAiBZ,EAAyBC,GAGpDE,CAAwB,CAACF,EAHkC,AAG7B,CAAG,CAAC,CAACK,EAErCH,EAAGU,YAAY,CAACZ,EAAMa,OAAOR,MAMnB,IAAVA,GACgB,WAAfH,EAAGS,OAAO,EACTZ,EAAyBC,IACxB,EAACK,GAAD,AAAoB,UAAVA,CAAU,CAAM,GAC7B,CAGAH,EAAGU,YAAY,CAACZ,EAAM,IACtBE,EAAGY,eAAe,CAACd,GAEvB,CACF,kVC1Cae,kBAAkB,CAAA,kBAAlBA,GAhBAC,mBAAmB,CAAA,kBAAnBA,uEAAN,IAAMA,EACM,aAAhB,OAAOC,MACNA,KAAKD,mBAAmB,EACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,SAChC,SAAUC,CAAuB,EAC/B,IAAIC,EAAQC,KAAKC,GAAG,GACpB,OAAON,KAAKO,UAAU,CAAC,WACrBJ,EAAG,CACDK,YAAY,EACZC,cAAe,WACb,OAAOC,KAAKC,GAAG,CAAC,EAAG,IAAMN,CAAAA,IAAKC,GAAG,GAAKF,CAAAA,CAAI,CAC5C,CACF,EACF,EAAG,EACL,EAEWN,EACM,aAAhB,OAAOE,MACNA,KAAKF,kBAAkB,EACvBE,KAAKF,kBAAkB,CAACG,IAAI,CAACC,SAC/B,SAAUU,CAAU,EAClB,OAAOC,aAAaD,EACtB,mVCmWF,OAAqB,CAAA,kBAArB,GA7NgBE,sBAAsB,CAAA,kBAAtBA,GAgCAC,gBAAgB,CAAA,kBAAhBA,6HA1LK,CAAA,CAAA,IAAA,aAC0C,CAAA,CAAA,IAAA,SAE5B,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,QACH,CAAA,CAAA,IAAA,IAE9BC,EAAc,IAAIC,IAClBC,EAAY,IAAIC,IAiBhBC,EAAqBC,AAAD,IAOxB,GAAIC,EAAAA,OAAQ,CAACC,OAAO,CAAE,YACpBF,EAAYG,OAAO,CAAC,AAACC,IACnBH,EAAAA,OAAQ,CAACC,OAAO,CAACE,EAAY,CAAEC,GAAI,OAAQ,EAC7C,GASF,GAAI,AAAkB,oBAAXxB,OAAwB,CACjC,IAAIyB,EAAOC,SAASD,IAAI,CACxBN,EAAYG,OAAO,CAAC,AAACC,IACnB,IAAII,EAAOD,SAASE,aAAa,CAAC,QAElCD,EAAKE,IAAI,CAAG,WACZF,EAAKG,GAAG,CAAG,aACXH,EAAKI,IAAI,CAAGR,EAEZE,EAAKO,WAAW,CAACL,EACnB,EACF,CACF,EAEMM,EAAa,AAACjD,IAClB,GAAM,KACJkD,CAAG,CACHxB,IAAE,CACFyB,SAAS,KAAO,CAAC,SACjBC,EAAU,IAAI,yBACdC,CAAuB,UACvBC,EAAW,EAAE,UACbC,EAAW,kBAAkB,SAC7BC,CAAO,aACPrB,CAAW,CACZ,CAAGnC,EAEEyD,EAAW/B,GAAMwB,EAGvB,GAAIO,GAAYzB,EAAU0B,GAAG,CAACD,GAC5B,OAIF,CALyC,EAKrC3B,EAAY4B,GAAG,CAACR,GAAM,CACxBlB,EAAU2B,GAAG,CAACF,GAGd3B,EAAY8B,GAAG,CAACV,GAAKW,IAAI,CAACV,EAAQK,GAClC,MACF,CAGA,IAAMM,EAAY,KAEZV,GACFA,IAGFpB,EAAU2B,AAJG,GAIA,CAACF,EAChB,EAEM1D,EAAK2C,SAASE,aAAa,CAAC,UAE5BmB,EAAc,IAAIC,QAAc,CAACC,EAASC,KAC9CnE,EAAGoE,gBAAgB,CAAC,OAAQ,SAAUC,CAAC,EACrCH,IACId,GACFA,EAAOkB,GADG,CACC,CAAC,IAAI,CAAED,GAEpBN,GACF,GACA/D,EAAGoE,gBAAgB,CAAC,QAAS,SAAUC,CAAC,EACtCF,EAAOE,EACT,EACF,GAAGE,KAAK,CAAC,SAAUF,CAAC,EACdZ,GACFA,EAAQY,EAEZ,EAHe,CAKXf,GAEFtD,EAAGwE,SAAS,CAAIlB,EAAwBmB,MAAM,EAFnB,AAEkC,GAE7DV,KACSR,GACTvD,EAAG0E,KADgB,MACL,CACQ,AAApB,iBAAOnB,EACHA,EACAoB,MAAMC,OAAO,CAACrB,GACZA,EAASsB,IAAI,CAAC,IACd,GAERd,KACSZ,IACTnD,CADc,CACXmD,GAAG,CAAGA,EAITpB,EAAY+C,GAAG,CAAC3B,EAAKa,IAGvB3E,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAACW,EAAIC,GAEV,UAAU,CAAvBuD,GACFxD,EAAGU,YAAY,CAAC,OAAQ,kBAG1BV,EAAGU,YAAY,CAAC,eAAgB8C,GAG5BpB,GACFD,EAAkBC,GAGpBO,KAJiB,IAIRoC,IAAI,CAAC9B,WAAW,CAACjD,EAC5B,EAEO,SAAS6B,EAAuB5B,CAAkB,EACvD,GAAM,UAAEuD,EAAW,kBAAkB,CAAE,CAAGvD,EACzB,cAAc,CAA3BuD,EACFvC,OAAOmD,gBAAgB,CAAC,OAAQ,KAC9BtD,GAAAA,EAAAA,mBAAAA,AAAmB,EAAC,IAAMoC,EAAWjD,GACvC,GAEAiD,EAAWjD,EAEf,CAuBO,SAAS6B,EAAiByD,CAAgC,EAC/DA,EAAkBhD,OAAO,CAACV,GAXV,AAIhBsD,IAHKxC,SAASyC,gBAAgB,CAAC,yCAC1BzC,SAASyC,gBAAgB,CAAC,qCAC9B,CACO7C,OAAO,CAAC,AAAC8C,IACf,IAAM3B,EAAW2B,EAAO1D,EAAE,EAAI0D,EAAOC,YAAY,CAAC,OAClDrD,EAAU2B,GAAG,CAACF,EAChB,EAMF,CAOA,SAAS8B,EAAOvF,CAAkB,EAChC,GAAM,IACJ0B,CAAE,CACFwB,MAAM,EAAE,QACRC,EAAS,KAAO,CAAC,SACjBC,EAAU,IAAI,UACdG,EAAW,kBAAkB,SAC7BC,CAAO,aACPrB,CAAW,CACX,GAAGqD,EACJ,CAAGxF,EAGE,eAAEyF,CAAa,CAAEP,SAAO,UAAEQ,CAAQ,CAAEC,QAAM,OAAEC,CAAK,CAAE,CACvDC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,kBAAkB,EA4BzBC,EAAyBC,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,GAAC,GAEtCC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACR,IAAMxC,EAAW/B,GAAMwB,EAClB6C,EAAuBG,OAAO,EAAE,CAE/B9C,GAAWK,GAAYzB,EAAU0B,GAAG,CAACD,IACvCL,IAGF2C,EAAuBG,CAJ6B,MAItB,EAAG,EAErC,EAAG,CAAC9C,EAAS1B,EAAIwB,EAAI,EAErB,IAAMiD,EAA4BH,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,GAAC,GAoCzC,GAlCAC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACR,GAAI,CAACE,EAA0BD,OAAO,CAAE,CACtC,GAAiB,oBAAoB,CAAjC3C,EACFN,EAAWjD,OACW,cAAc,CAA3BuD,IA5Fa,YAAY,CAApCb,SAASsC,UAAU,CACrBnE,CAAAA,EAAAA,EAAAA,mBAAmB,AAAnBA,EAAoB,IAAMoC,MAE1BjC,KAFqChB,EAE9BmE,gBAAgB,CAAC,OAAQ,KAC9BtD,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC,IAAMoC,EAyFTjD,GAxFnB,IA2FEmG,EAA0BD,AA5FWlG,OA4FJ,EAAG,CACtC,CACF,EAAG,CAACA,EAAOuD,EAAS,EAEhBA,CAAa,yBAAoC,WAAbA,CAAa,GAAU,CACzDkC,GACFP,CAAO,CAAC3B,EAAS,CAAI2B,CAAAA,CAAO,CAAC3B,EAAS,EADrB,AACyB,EAAC,AAAD,EAAI6C,MAAM,CAAC,CACnD,CACE1E,SACAwB,SACAC,UACAC,UACAI,EACA,GAAGgC,CAAS,AACd,EACD,EACDC,EAAcP,IACLQ,GAAYA,IAErB1D,EAAU2B,GAAG,CAACjC,EAFmB,CAEbwB,GACXwC,GAAY,CAACA,KACtBzC,EAAWjD,IAKX2F,CANkC,CAM1B,CAkBV,GARIxD,GACFA,EAAYG,OAAO,CAAE+D,AADN,AACK,IAClBjE,EAAAA,OAAQ,CAACC,OAAO,CAACgE,EAAU,CAAE7D,GAAI,OAAQ,EAC3C,GAKe,qBAAqB,CAAlCe,EACF,GAAI,CAACL,EASH,GATQ,IAEJsC,AAOJ,EAPcnC,SAOd,cAPqC,EAAE,CAErCmC,EAAUlC,QAAQ,CAAGkC,EAAUnC,uBAAuB,CACnDmB,MAAM,CACT,OAAOgB,EAAUnC,uBAAuB,EAIxC,CAAA,EAAA,EAAA,GAAA,EAAC+B,SAAAA,CACCQ,MAAOA,EACPvC,wBAAyB,CACvBmB,OAAS,0CAAyC8B,KAAKC,SAAS,CAAC,CAC/D,EACA,CAAE,GAAGf,CAAS,IAAE9D,CAAG,EACpB,EAAE,GACL,SAgBJ,OAXAU,AAWA,EAXAA,OAAQ,CAACoE,CAWT,MAXgB,CACdtD,EACAsC,EAAUiB,SAAS,CACf,CACEjE,GAAI,SACJiE,UAAWjB,EAAUiB,SAAS,OAC9Bb,EACAc,YAAalB,EAAUkB,WACzB,AADoC,EAEpC,CAAElE,GAAI,eAAUoD,EAAOc,YAAalB,EAAUkB,WAAW,AAAC,GAG9D,CAAA,EAAA,EAAA,GAAA,EAACtB,SAAAA,CACCQ,MAAOA,EACPvC,wBAAyB,CACvBmB,OAAS,0CAAyC8B,KAAKC,SAAS,CAAC,CAC/DrD,EACA,CAAE,GAAGsC,CAAS,IAAE9D,CAAG,EACpB,EAAE,GACL,IAIgB,oBAAoB,CAAjC6B,GACLL,GAEFd,EAFO,AAEPA,OAAQ,CAACoE,OAAO,CACdtD,EACAsC,EAAUiB,SAAS,CACf,CACEjE,GAAI,SACJiE,UAAWjB,EAAUiB,SAAS,OAC9Bb,EACAc,YAAalB,EAAUkB,WACzB,AADoC,EAEpC,CAAElE,GAAI,eAAUoD,EAAOc,YAAalB,EAAUkB,WAAW,AAAC,EAItE,CAEA,OAAO,IACT,CAEAvG,OAAOwG,cAAc,CAACpB,EAAQ,eAAgB,CAAErF,OAAO,CAAK,OAE5D,EAAeqF,0TCvXf,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAQO,SAAS,EAAkB,UAAE,CAAQ,CAAiC,EAC3E,IAAM,EAAW,CAAA,EAAA,EAAA,WAAA,AAAU,IA6B3B,MA3BA,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,AAFO,KAIf,IAAM,EAAQ,WAAW,IAF3B,CAIQ,wBAAyB,OAC3B,CADmC,mBACf,IAAM,CAAA,EAAA,EAAA,mBAAA,AAAkB,IAAK,CAAE,QAAS,GAAK,EAAvC,CAE1B,CAAA,EAAA,EAAA,mBAAkB,AAAlB,GAEJ,EAAG,KAAK,AAER,MAAO,EAJH,EAIS,aAAa,EAC5B,EAAG,EAAE,EAEL,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,IALmD,CAO3D,IAAM,EAAQ,WAAW,IAF3B,CAGQ,wBAAyB,OAC3B,CADmC,mBACf,IAAM,CAAA,EAAA,EAAA,aAAA,AAAY,EAAE,SAAS,KAAK,EAAG,CAAE,KAAjC,GAA0C,GAAK,GAEzE,CAAA,EAAA,EAAA,aAAA,AAAY,EAAE,SAAS,KAAK,CAEhC,EAAG,KAEH,AAJI,MAIG,IAAM,aAAa,EAC5B,EAAG,CAAC,EAAS,EAEN,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,QAAA,CAAA,UAAG,GACZ", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}