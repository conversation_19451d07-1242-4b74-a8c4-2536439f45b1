{"version": 3, "sources": ["turbopack:///[project]/src/app/global-error.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/global-error.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/global-error.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "2FACe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAsB,EACjC,EADW,SACE,MAAM,AAAI,MAAM,0RAA4R,EACzT,yDACA,wFAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAsB,EACjC,EADW,SACE,MAAM,AAAI,MAAM,sQAAwQ,EACrS,qCACA"}