{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n// RetryConfig type definition\nexport type RetryConfig = {\n  maxAttempts: number\n  baseDelay: number\n  maxDelay: number\n}\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// File utility functions for Supabase storage\nexport function createFileFromBlob(blob: Blob, filename: string): File {\n  return new File([blob], filename, { type: blob.type })\n}\n\n// Format duration in seconds to MM:SS\nexport function formatDuration(seconds: number): string {\n  const mins = Math.floor(seconds / 60)\n  const secs = Math.floor(seconds % 60)\n  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\n// Format date for display (SSR-safe)\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n\n  // Use a consistent format that works on both server and client\n  const year = date.getFullYear()\n  const month = date.toLocaleDateString('en-US', { month: 'short' })\n  const day = date.getDate().toString().padStart(2, '0')\n  const minutes = date.getMinutes().toString().padStart(2, '0')\n  const ampm = date.getHours() >= 12 ? 'PM' : 'AM'\n  const displayHours = date.getHours() % 12 || 12\n\n  return `${month} ${day}, ${year} at ${displayHours.toString().padStart(2, '0')}:${minutes} ${ampm}`\n}\n\n// Format relative time (e.g., \"2 hours ago\")\nexport function formatRelativeTime(dateString: string): string {\n  const date = new Date(dateString)\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'Just now'\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60)\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600)\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`\n  } else {\n    const days = Math.floor(diffInSeconds / 86400)\n    return `${days} day${days > 1 ? 's' : ''} ago`\n  }\n}\n\n// Retry function with exponential backoff\nexport async function retryWithBackoff<T>(\n  fn: () => Promise<T>,\n  config: RetryConfig = {\n    maxAttempts: 3,\n    baseDelay: 1000,\n    maxDelay: 10000\n  }\n): Promise<T> {\n  let lastError: Error\n\n  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {\n    try {\n      return await fn()\n    } catch (error) {\n      lastError = error as Error\n\n      if (attempt === config.maxAttempts) {\n        throw lastError\n      }\n\n      const delay = Math.min(\n        config.baseDelay * Math.pow(2, attempt - 1),\n        config.maxDelay\n      )\n\n      console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, error)\n      await new Promise(resolve => setTimeout(resolve, delay))\n    }\n  }\n\n  throw lastError!\n}\n\n// Validate URL string\nexport function isValidUrl(str: string): boolean {\n  try {\n    new URL(str)\n    return true\n  } catch {\n    return false\n  }\n}\n\n// Generate patient summary prompt for Gemini\nexport function generatePrompt(\n  submittedBy: 'doctor' | 'receptionist'\n): string {\n  const contextNote = submittedBy === 'doctor'\n    ? 'This consultation was recorded by the doctor during patient visit.'\n    : 'This consultation is being reviewed by the receptionist for final summary.'\n\n  return `\nYou are an AI assistant helping Indian doctors create patient consultation summaries.\n\nContext: ${contextNote}\n\nPlease analyze the provided audio recording and any handwritten notes (if image provided) to generate a comprehensive patient summary.\n\nRequirements:\n- Language: English\n- Tone: Professional\n- Format: Standard medical format\n- Include sections: Chief Complaint, History, Examination, Diagnosis, Treatment Plan, Follow-up\n\nInstructions:\n1. Transcribe the audio accurately\n2. Extract key medical information\n3. If image provided, include any relevant handwritten notes\n4. Structure the summary according to the specified sections\n5. Use appropriate medical terminology\n6. Ensure the summary is clear and professional\n\nPlease provide a well-structured patient consultation summary based on the audio and image inputs.\n  `.trim()\n}\n\n// Truncate text with ellipsis\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + '...'\n}\n\n// Check if device supports audio recording\nexport function supportsAudioRecording(): boolean {\n  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)\n}\n\n// Check if device supports camera\nexport function supportsCamera(): boolean {\n  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAQO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,mBAAmB,IAAU,EAAE,QAAgB;IAC7D,OAAO,IAAI,KAAK;QAAC;KAAK,EAAE,UAAU;QAAE,MAAM,KAAK,IAAI;IAAC;AACtD;AAGO,SAAS,eAAe,OAAe;IAC5C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAClC,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAClC,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAClF;AAGO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IAEtB,+DAA+D;IAC/D,MAAM,OAAO,KAAK,WAAW;IAC7B,MAAM,QAAQ,KAAK,kBAAkB,CAAC,SAAS;QAAE,OAAO;IAAQ;IAChE,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAClD,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACzD,MAAM,OAAO,KAAK,QAAQ,MAAM,KAAK,OAAO;IAC5C,MAAM,eAAe,KAAK,QAAQ,KAAK,MAAM;IAE7C,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,IAAI,EAAE,aAAa,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AACrG;AAGO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD;AACF;AAGO,eAAe,iBACpB,EAAoB,EACpB,SAAsB;IACpB,aAAa;IACb,WAAW;IACX,UAAU;AACZ,CAAC;IAED,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,OAAO,WAAW,EAAE,UAAW;QAC9D,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,IAAI,YAAY,OAAO,WAAW,EAAE;gBAClC,MAAM;YACR;YAEA,MAAM,QAAQ,KAAK,GAAG,CACpB,OAAO,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,IACzC,OAAO,QAAQ;YAGjB,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,qBAAqB,EAAE,MAAM,GAAG,CAAC,EAAE;YACnE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM;AACR;AAGO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,eACd,WAAsC;IAEtC,MAAM,cAAc,gBAAgB,WAChC,uEACA;IAEJ,OAAO,CAAC;;;SAGD,EAAE,YAAY;;;;;;;;;;;;;;;;;;;EAmBrB,CAAC,CAAC,IAAI;AACR;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAGO,SAAS;IACd,OAAO,CAAC,CAAC,CAAC,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,YAAY;AACzE;AAGO,SAAS;IACd,OAAO,CAAC,CAAC,CAAC,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,YAAY;AACzE", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/types.ts"], "sourcesContent": ["export type Json =\n  | string\n  | number\n  | boolean\n  | null\n  | { [key: string]: <PERSON><PERSON> | undefined }\n  | Json[]\n\nexport type Database = {\n  public: {\n    Tables: {\n      admins: {\n        Row: {\n          created_at: string\n          email: string\n          id: string\n          name: string\n          password_hash: string\n          role: string\n          updated_at: string\n        }\n        Insert: {\n          created_at?: string\n          email: string\n          id?: string\n          name: string\n          password_hash: string\n          role?: string\n          updated_at?: string\n        }\n        Update: {\n          created_at?: string\n          email?: string\n          id?: string\n          name?: string\n          password_hash?: string\n          role?: string\n          updated_at?: string\n        }\n        Relationships: []\n      }\n      consultations: {\n        Row: {\n          additional_audio_urls: Json | null\n          ai_generated_note: string | null\n          primary_audio_url: string\n          created_at: string\n          doctor_id: string | null\n          edited_note: string | null\n          id: string\n          image_urls: Json | null\n          patient_number: number | null\n          patient_name: string | null\n          status: string\n          submitted_by: string\n          total_file_size_bytes: number | null\n          file_retention_until: string | null\n          updated_at: string\n          consultation_type: string\n          doctor_notes: string | null\n          additional_notes: string | null\n        }\n        Insert: {\n          additional_audio_urls?: Json | null\n          ai_generated_note?: string | null\n          primary_audio_url: string\n          created_at?: string\n          doctor_id?: string | null\n          edited_note?: string | null\n          id?: string\n          image_urls?: Json | null\n          patient_number?: number | null\n          patient_name?: string | null\n          status?: string\n          submitted_by: string\n          total_file_size_bytes?: number | null\n          file_retention_until?: string | null\n          updated_at?: string\n          consultation_type?: string\n          doctor_notes?: string | null\n          additional_notes?: string | null\n        }\n        Update: {\n          additional_audio_urls?: Json | null\n          ai_generated_note?: string | null\n          primary_audio_url?: string\n          created_at?: string\n          doctor_id?: string | null\n          edited_note?: string | null\n          id?: string\n          image_urls?: Json | null\n          patient_number?: number | null\n          patient_name?: string | null\n          status?: string\n          submitted_by?: string\n          total_file_size_bytes?: number | null\n          file_retention_until?: string | null\n          updated_at?: string\n          consultation_type?: string\n          doctor_notes?: string | null\n          additional_notes?: string | null\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"consultations_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      doctors: {\n        Row: {\n          approved: boolean\n          approved_at: string | null\n          approved_by: string | null\n          clinic_name: string | null\n          created_at: string\n          email: string\n          id: string\n          monthly_quota: number\n          name: string\n          password_hash: string\n          phone: string | null\n          quota_reset_at: string\n          quota_used: number\n\n          updated_at: string\n          referral_code: string | null\n          referred_by: string | null\n          conversion_date: string | null\n          referral_discount_earned: number\n          total_referrals: number\n          successful_referrals: number\n          current_plan_id: string | null\n          billing_status: string\n          trial_ends_at: string | null\n          last_payment_date: string | null\n          next_billing_date: string | null\n          available_discount_amount: number\n        }\n        Insert: {\n          approved?: boolean\n          approved_at?: string | null\n          approved_by?: string | null\n          clinic_name?: string | null\n          created_at?: string\n          email: string\n          id?: string\n          monthly_quota?: number\n          name: string\n          password_hash: string\n          phone?: string | null\n          quota_reset_at?: string\n          quota_used?: number\n\n          updated_at?: string\n          referral_code?: string | null\n          referred_by?: string | null\n          conversion_date?: string | null\n          referral_discount_earned?: number\n          total_referrals?: number\n          successful_referrals?: number\n          current_plan_id?: string | null\n          billing_status?: string\n          trial_ends_at?: string | null\n          last_payment_date?: string | null\n          next_billing_date?: string | null\n          available_discount_amount?: number\n        }\n        Update: {\n          approved?: boolean\n          approved_at?: string | null\n          approved_by?: string | null\n          clinic_name?: string | null\n          created_at?: string\n          email?: string\n          id?: string\n          monthly_quota?: number\n          name?: string\n          password_hash?: string\n          phone?: string | null\n          quota_reset_at?: string\n          quota_used?: number\n\n          updated_at?: string\n          referral_code?: string | null\n          referred_by?: string | null\n          conversion_date?: string | null\n          referral_discount_earned?: number\n          total_referrals?: number\n          successful_referrals?: number\n          current_plan_id?: string | null\n          billing_status?: string\n          trial_ends_at?: string | null\n          last_payment_date?: string | null\n          next_billing_date?: string | null\n          available_discount_amount?: number\n        }\n        Relationships: []\n      }\n      usage_logs: {\n        Row: {\n          action_type: string\n          consultation_id: string | null\n          created_at: string\n          doctor_id: string | null\n          id: string\n          metadata: Json | null\n          quota_after: number | null\n          quota_before: number | null\n        }\n        Insert: {\n          action_type: string\n          consultation_id?: string | null\n          created_at?: string\n          doctor_id?: string | null\n          id?: string\n          metadata?: Json | null\n          quota_after?: number | null\n          quota_before?: number | null\n        }\n        Update: {\n          action_type?: string\n          consultation_id?: string | null\n          created_at?: string\n          doctor_id?: string | null\n          id?: string\n          metadata?: Json | null\n          quota_after?: number | null\n          quota_before?: number | null\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"usage_logs_consultation_id_fkey\"\n            columns: [\"consultation_id\"]\n            isOneToOne: false\n            referencedRelation: \"consultations\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"usage_logs_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      referral_analytics: {\n        Row: {\n          id: string\n          referrer_id: string | null\n          referred_doctor_id: string | null\n          referral_code: string\n          signup_date: string\n          conversion_date: string | null\n          discount_earned: number\n          status: string\n          metadata: Json | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          referrer_id?: string | null\n          referred_doctor_id?: string | null\n          referral_code: string\n          signup_date?: string\n          conversion_date?: string | null\n          discount_earned?: number\n          status?: string\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          referrer_id?: string | null\n          referred_doctor_id?: string | null\n          referral_code?: string\n          signup_date?: string\n          conversion_date?: string | null\n          discount_earned?: number\n          status?: string\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"referral_analytics_referrer_id_fkey\"\n            columns: [\"referrer_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"referral_analytics_referred_doctor_id_fkey\"\n            columns: [\"referred_doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      billing_plans: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          monthly_price: number\n          quota_limit: number\n          features: Json | null\n          active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          description?: string | null\n          monthly_price: number\n          quota_limit: number\n          features?: Json | null\n          active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          description?: string | null\n          monthly_price?: number\n          quota_limit?: number\n          features?: Json | null\n          active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: []\n      }\n      billing_transactions: {\n        Row: {\n          id: string\n          doctor_id: string\n          plan_id: string | null\n          amount: number\n          discount_amount: number\n          final_amount: number\n          payment_method: string | null\n          payment_status: string\n          payment_date: string | null\n          billing_period_start: string\n          billing_period_end: string\n          payment_reference: string | null\n          notes: string | null\n          created_by: string | null\n          metadata: Json | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          plan_id?: string | null\n          amount: number\n          discount_amount?: number\n          final_amount: number\n          payment_method?: string | null\n          payment_status?: string\n          payment_date?: string | null\n          billing_period_start: string\n          billing_period_end: string\n          payment_reference?: string | null\n          notes?: string | null\n          created_by?: string | null\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          plan_id?: string | null\n          amount?: number\n          discount_amount?: number\n          final_amount?: number\n          payment_method?: string | null\n          payment_status?: string\n          payment_date?: string | null\n          billing_period_start?: string\n          billing_period_end?: string\n          payment_reference?: string | null\n          notes?: string | null\n          created_by?: string | null\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"billing_transactions_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"billing_transactions_plan_id_fkey\"\n            columns: [\"plan_id\"]\n            isOneToOne: false\n            referencedRelation: \"billing_plans\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      referral_discounts: {\n        Row: {\n          id: string\n          doctor_id: string\n          referral_analytics_id: string\n          discount_percentage: number\n          discount_amount: number\n          original_amount: number\n          applied_to_transaction_id: string | null\n          status: string\n          valid_until: string\n          applied_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          referral_analytics_id: string\n          discount_percentage?: number\n          discount_amount: number\n          original_amount: number\n          applied_to_transaction_id?: string | null\n          status?: string\n          valid_until: string\n          applied_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          referral_analytics_id?: string\n          discount_percentage?: number\n          discount_amount?: number\n          original_amount?: number\n          applied_to_transaction_id?: string | null\n          status?: string\n          valid_until?: string\n          applied_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"referral_discounts_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"referral_discounts_referral_analytics_id_fkey\"\n            columns: [\"referral_analytics_id\"]\n            isOneToOne: false\n            referencedRelation: \"referral_analytics\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      contact_requests: {\n        Row: {\n          id: string\n          doctor_id: string\n          doctor_name: string\n          doctor_email: string\n          clinic_name: string\n          phone_number: string\n          request_type: string\n          message: string | null\n          status: string\n          contacted_at: string | null\n          resolved_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          doctor_name: string\n          doctor_email: string\n          clinic_name: string\n          phone_number: string\n          request_type: string\n          message?: string | null\n          status?: string\n          contacted_at?: string | null\n          resolved_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          doctor_name?: string\n          doctor_email?: string\n          clinic_name?: string\n          phone_number?: string\n          request_type?: string\n          message?: string | null\n          status?: string\n          contacted_at?: string | null\n          resolved_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"contact_requests_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          }\n        ]\n      }\n    }\n    Views: {\n      admin_dashboard_summary: {\n        Row: {\n          total_doctors: number\n          pending_approvals: number\n          approved_doctors: number\n          total_consultations: number\n          total_ai_generations: number\n          quota_usage_percentage: number\n        }\n        Relationships: []\n      }\n      admin_doctors_with_stats: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          phone: string | null\n          clinic_name: string | null\n          monthly_quota: number\n          quota_used: number\n          quota_reset_at: string\n          approved: boolean\n          approved_by: string | null\n          approved_at: string | null\n          referral_code: string | null\n          referred_by: string | null\n          billing_status: string | null\n          trial_ends_at: string | null\n          created_at: string\n          updated_at: string\n          total_consultations: number\n          this_month_generations: number\n          last_activity: string | null\n          quota_percentage: number\n        }\n        Relationships: []\n      }\n    }\n    Functions: {\n      check_and_update_quota: {\n        Args: { doctor_uuid: string }\n        Returns: boolean\n      }\n      handle_referral_conversion: {\n        Args: { referred_doctor_uuid: string }\n        Returns: boolean\n      }\n      generate_referral_code: {\n        Args: { doctor_name: string; doctor_email: string }\n        Returns: string\n      }\n      apply_referral_discount: {\n        Args: { transaction_id: string; discount_amount: number }\n        Returns: boolean\n      }\n      complete_payment: {\n        Args: { transaction_id: string }\n        Returns: boolean\n      }\n      reset_all_quotas: {\n        Args: Record<PropertyKey, never>\n        Returns: number\n      }\n      update_doctor_quota: {\n        Args: { doctor_uuid: string; new_quota: number; admin_uuid: string }\n        Returns: boolean\n      }\n      set_patient_number: {\n        Args: Record<PropertyKey, never>;\n        Returns: unknown;\n      }\n      update_updated_at_column: {\n        Args: Record<PropertyKey, never>;\n        Returns: unknown;\n      }\n      get_consultation_stats: {\n        Args: { doctor_uuid: string }\n        Returns: {\n          total_consultations: number\n          pending_consultations: number\n          approved_consultations: number\n          today_consultations: number\n        }[]\n      }\n    }\n    Enums: {\n      [_ in never]: never\n    }\n    CompositeTypes: {\n      [_ in never]: never\n    }\n  }\n}\n\ntype DefaultSchema = Database[Extract<keyof Database, \"public\">]\n\nexport type Tables<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof (DefaultSchema[\"Tables\"] & DefaultSchema[\"Views\"])\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof (Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"] &\n        Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Views\"])\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? (Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"] &\n      Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Views\"])[TableName] extends {\n      Row: infer R\n    }\n    ? R\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema[\"Tables\"] &\n        DefaultSchema[\"Views\"])\n    ? (DefaultSchema[\"Tables\"] &\n        DefaultSchema[\"Views\"])[DefaultSchemaTableNameOrOptions] extends {\n        Row: infer R\n      }\n      ? R\n      : never\n    : never\n\nexport type TablesInsert<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof DefaultSchema[\"Tables\"]\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"]\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"][TableName] extends {\n      Insert: infer I\n    }\n    ? I\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema[\"Tables\"]\n    ? DefaultSchema[\"Tables\"][DefaultSchemaTableNameOrOptions] extends {\n        Insert: infer I\n      }\n      ? I\n      : never\n    : never\n\nexport type TablesUpdate<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof DefaultSchema[\"Tables\"]\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"]\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"][TableName] extends {\n      Update: infer U\n    }\n    ? U\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema[\"Tables\"]\n    ? DefaultSchema[\"Tables\"][DefaultSchemaTableNameOrOptions] extends {\n        Update: infer U\n      }\n      ? U\n      : never\n    : never\n\nexport type Enums<\n  DefaultSchemaEnumNameOrOptions extends\n    | keyof DefaultSchema[\"Enums\"]\n    | { schema: keyof Database },\n  EnumName extends DefaultSchemaEnumNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaEnumNameOrOptions[\"schema\"]][\"Enums\"]\n    : never = never,\n> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaEnumNameOrOptions[\"schema\"]][\"Enums\"][EnumName]\n  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema[\"Enums\"]\n    ? DefaultSchema[\"Enums\"][DefaultSchemaEnumNameOrOptions]\n    : never\n\n// Consultation type enum for different consultation workflows\nexport type ConsultationType = 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology';\n\n// Consultation type labels for UI display\nexport const CONSULTATION_TYPE_LABELS: Record<ConsultationType, string> = {\n  outpatient: 'Outpatient Consultation',\n  discharge: 'Discharge Summary',\n  surgery: 'Operative Note',\n  radiology: 'Radiology Report',\n  dermatology: 'Dermatology Note',\n  cardiology_echo: 'Echocardiogram Report',\n  ivf_cycle: 'IVF Cycle Summary',\n  pathology: 'Histopathology Report'\n};\n\nexport type CompositeTypes<\n  PublicCompositeTypeNameOrOptions extends\n    | keyof DefaultSchema[\"CompositeTypes\"]\n    | { schema: keyof Database },\n  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof (Database[PublicCompositeTypeNameOrOptions[\"schema\"]][\"CompositeTypes\"])\n    : never = never,\n> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }\n  ? Database[PublicCompositeTypeNameOrOptions[\"schema\"]][\"CompositeTypes\"][CompositeTypeName]\n  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema[\"CompositeTypes\"]\n    ? DefaultSchema[\"CompositeTypes\"][PublicCompositeTypeNameOrOptions]\n    : never\n\nexport const Constants = {\n  public: {\n    Enums: {},\n  },\n} as const\n\nexport type Doctor = Tables<'doctors'>;\nexport type Consultation = Tables<'consultations'>;\nexport type Admin = Tables<'admins'>;\nexport type ReferralAnalytics = Tables<'referral_analytics'>;\n\n\n\nexport type ApiResponse<T> = { success: true; data: T } | { success: false; error: string };\n\nexport interface AdminDashboardStats {\n  total_doctors: number;\n  pending_approvals: number;\n  approved_doctors: number;\n  total_consultations: number;\n  total_ai_generations: number;\n  quota_usage_percentage: number;\n}\n\nexport interface DashboardStats {\n  total_consultations: number;\n  pending_consultations: number;\n  generated_consultations: number;\n  approved_consultations: number;\n  today_consultations: number;\n}\n\nexport interface QuotaInfo {\n  monthly_quota: number;\n  quota_used: number;\n  quota_remaining: number;\n  quota_percentage: number;\n  quota_reset_at: string;\n  days_until_reset: number;\n}\n\nexport interface ReferralInfo {\n  referral_code: string;\n  total_referrals: number;\n  successful_referrals: number;\n  pending_referrals: number;\n  discount_earned: number;\n  referred_by?: {\n    name: string;\n    referral_code: string;\n  } | null;\n  recent_referrals: Array<{\n    id: string;\n    name: string;\n    email: string;\n    signup_date: string;\n    conversion_date: string | null;\n    status: 'pending' | 'converted' | 'expired';\n  }>;\n}\n\nexport type DoctorWithStats = Omit<Tables<'doctors'>, 'consultations' | 'phone' | 'clinic_name' | 'approved_by' | 'approved_at' | 'template_config'> & {\n  phone?: string;\n  clinic_name?: string;\n  approved_by?: string;\n  approved_at?: string;\n  total_consultations: number;\n  this_month_generations: number;\n  quota_percentage: number;\n  last_activity?: string;\n};\n\n// Type for the admin_doctors_with_stats view\nexport type AdminDoctorWithStats = {\n  id: string;\n  email: string;\n  name: string;\n  phone?: string;\n  clinic_name?: string;\n  monthly_quota: number;\n  quota_used: number;\n  quota_reset_at: string;\n  approved: boolean;\n  approved_by?: string;\n  approved_at?: string;\n  referral_code?: string;\n  referred_by?: string;\n  billing_status?: string;\n  trial_ends_at?: string;\n  created_at: string;\n  updated_at: string;\n  total_consultations: number;\n  this_month_generations: number;\n  last_activity?: string;\n  quota_percentage: number;\n};\n\nexport type AdminActionRequest =\n  | { action: 'approve'; doctor_id: string }\n  | { action: 'reject'; doctor_id: string }\n  | { action: 'update_quota'; doctor_id: string; data: { quota: number; reason?: string } }\n  | { action: 'disable'; doctor_id: string }\n  | { action: 'enable'; doctor_id: string };\n\nexport interface FormState {\n  success: boolean;\n  message: string;\n  fieldErrors?: { [key: string]: string[] };\n}\n\nexport interface ImageFile {\n  id: string;\n  file: File;\n  preview?: string; // Optional for optimistic UI - not needed when waiting for final state\n  name: string;\n  type: string;\n  size: number;\n}\n\nexport interface ImageCaptureState {\n  images: ImageFile[];\n  status?: 'idle' | 'capturing' | 'uploaded' | 'error';\n  error: string | null;\n}\n\nexport interface AudioRecordingState {\n  isRecording: boolean;\n  duration: number;\n  audioBlob?: Blob;\n  audioFile?: File;\n  error?: string | null;\n  status?: 'idle' | 'recording' | 'recorded' | 'uploading' | 'uploaded' | 'error';\n}\n\nexport interface SessionPayload {\n  userId: string;\n  expiresAt: Date;\n  adminId?: string;\n  role?: 'admin' | 'super_admin';\n}\n\n"], "names": [], "mappings": ";;;;AAotBO,MAAM,2BAA6D;IACxE,YAAY;IACZ,WAAW;IACX,SAAS;IACT,WAAW;IACX,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,WAAW;AACb;AAiBO,MAAM,YAAY;IACvB,QAAQ;QACN,OAAO,CAAC;IACV;AACF", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA+dsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA2iBsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAgzBsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAk3BsB,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/consultation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { createPortal } from 'react-dom'\nimport { X, Play, Pause, Wand2, Save, Copy, Camera, Upload, Mic, Square, Trash2, Loader2 } from 'lucide-react'\nimport { Consultation, ConsultationType, CONSULTATION_TYPE_LABELS } from '@/lib/types'\nimport { formatDate } from '@/lib/utils'\nimport { approveConsultation, addAdditionalAudio, deleteAdditionalAudio, deleteConsultationImage } from '@/lib/actions/consultations'\nimport { trackConsultation } from '@/lib/analytics'\nimport Image from 'next/image'\n\ninterface ConsultationModalProps {\n  consultation: Consultation\n  onClose: () => void\n  onConsultationUpdate?: (updatedConsultation: Consultation) => void\n  doctorName?: string\n}\n\nexport function ConsultationModal({ consultation, onClose, onConsultationUpdate, doctorName }: ConsultationModalProps) {\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [isApproving, setIsApproving] = useState(false)\n  const [editedNote, setEditedNote] = useState(consultation.edited_note || consultation.ai_generated_note || '')\n  const [playingAudio, setPlayingAudio] = useState<HTMLAudioElement | null>(null)\n  const [isLoadingAudio, setIsLoadingAudio] = useState(false)\n\n  // Web Audio API refs for Safari\n  const audioContextRef = useRef<AudioContext | null>(null)\n  const audioSourceRef = useRef<AudioBufferSourceNode | null>(null)\n  const [success, setSuccess] = useState<string>('')\n  const [additionalImages, setAdditionalImages] = useState<Array<{ id: string; url: string; preview?: string }>>([])\n  const [isRecordingAdditional, setIsRecordingAdditional] = useState(false)\n  const [additionalAudioBlob, setAdditionalAudioBlob] = useState<Blob | null>(null)\n  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)\n\n  const [streamingText, setStreamingText] = useState('')\n  const [mounted, setMounted] = useState(false)\n\n  // New consultation type and additional notes state\n  const [consultationType, setConsultationType] = useState<ConsultationType>(\n    (consultation.consultation_type as ConsultationType) || 'outpatient'\n  )\n  const [additionalNotes, setAdditionalNotes] = useState(consultation.additional_notes || '')\n\n  // Delete functionality state\n  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)\n  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)\n  \n  const fileInputRef = useRef<HTMLInputElement | null>(null)\n  const cameraInputRef = useRef<HTMLInputElement | null>(null)\n  const successTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n\n  // Function to set success message with auto-clear timeout\n  const setSuccessWithTimeout = (message: string, timeout = 3000) => {\n    // Clear any existing timeout\n    if (successTimeoutRef.current) {\n      clearTimeout(successTimeoutRef.current)\n    }\n\n    setSuccess(message)\n\n    // Set new timeout to clear message\n    if (message) {\n      successTimeoutRef.current = setTimeout(() => {\n        setSuccess('')\n        successTimeoutRef.current = null\n      }, timeout)\n    }\n  }\n\n  // Handle client-side mounting\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Clear success messages when consultation changes\n  useEffect(() => {\n    setSuccess('')\n    if (successTimeoutRef.current) {\n      clearTimeout(successTimeoutRef.current)\n      successTimeoutRef.current = null\n    }\n  }, [consultation.id])\n\n  // Cleanup long press timer on unmount\n  useEffect(() => {\n    return () => {\n      if (longPressTimer) {\n        clearTimeout(longPressTimer)\n      }\n    }\n  }, [longPressTimer])\n\n  // Sync editedNote state when consultation data changes\n  useEffect(() => {\n    setEditedNote(consultation.edited_note || consultation.ai_generated_note || '')\n  }, [consultation.edited_note, consultation.ai_generated_note])\n\n  // Sync additionalNotes state when consultation data changes\n  useEffect(() => {\n    setAdditionalNotes(consultation.additional_notes || '')\n  }, [consultation.additional_notes])\n\n  // Handle additional image upload - Upload to Cloudflare R2\n  const handleImageUpload = async (files: FileList) => {\n    try {\n      setSuccess('Uploading images...')\n\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i]\n\n        if (!file.type.startsWith('image/')) continue\n        if (file.size > 10 * 1024 * 1024) continue // 10MB limit\n\n        // Upload to Cloudflare R2\n        const { uploadFile } = await import('@/lib/storage')\n        const uploadResult = await uploadFile(file, consultation.doctor_id || '', consultation.id, 'image')\n\n        if (uploadResult.success && uploadResult.url) {\n          const preview = URL.createObjectURL(file)\n          setAdditionalImages(prev => [...prev, {\n            id: `${Date.now()}-${i}`,\n            url: uploadResult.url!,\n            preview\n          }])\n        } else {\n          setSuccess(`Failed to upload ${file.name}: ${uploadResult.error}`)\n          return\n        }\n      }\n\n      setSuccessWithTimeout('Images uploaded successfully!')\n    } catch (_error) {\n      setSuccess('Failed to upload images')\n    }\n  }\n\n  // Remove additional image\n  const removeAdditionalImage = (id: string) => {\n    setAdditionalImages(prev => {\n      const imageToRemove = prev.find(img => img.id === id)\n      if (imageToRemove && imageToRemove.preview && typeof imageToRemove.preview === 'string') {\n        URL.revokeObjectURL(imageToRemove.preview)\n      }\n      return prev.filter(img => img.id !== id)\n    })\n  }\n\n  const handleGenerateSummary = async () => {\n    setIsGenerating(true)\n    setSuccess('')\n    setStreamingText('')\n\n    try {\n      // Combine original images with additional images\n      const allImages = [\n        ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),\n        ...additionalImages.map(img => img.url)\n      ].filter((img): img is string => typeof img === 'string' && img !== null)\n\n      // Update the consultation with additional images before generating summary\n      if (additionalImages.length > 0) {\n        const { updateConsultationImages } = await import('@/lib/actions/consultations')\n        await updateConsultationImages(consultation.id, allImages)\n      }\n\n      // Simple client-side streaming with quota checking\n      try {\n          const response = await fetch('/api/generate-summary-stream', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              primary_audio_url: consultation.primary_audio_url,\n              additional_audio_urls: Array.isArray(consultation.additional_audio_urls) ? consultation.additional_audio_urls : [],\n              image_urls: [\n                ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),\n                ...allImages\n              ],\n\n              submitted_by: consultation.submitted_by || 'doctor',\n              consultation_type: consultationType,\n              doctor_notes: consultation.doctor_notes || undefined,\n              additional_notes: additionalNotes || undefined,\n              patient_name: consultation.patient_name || undefined,\n              doctor_name: doctorName || undefined,\n              created_at: consultation.created_at || undefined,\n            }),\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n          }\n\n          const reader = response.body?.getReader()\n          if (!reader) {\n            throw new Error('No reader available')\n          }\n\n          const decoder = new TextDecoder()\n          let fullSummary = ''\n          setStreamingText('')\n\n          while (true) {\n            const { done, value } = await reader.read()\n            if (done) break\n\n            const chunk = decoder.decode(value, { stream: true })\n            const lines = chunk.split('\\n')\n\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.slice(6))\n                  if (data.type === 'chunk' && data.text) {\n                    // Format text with proper line breaks\n                    const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                    fullSummary += formattedText\n                    // Instantly update streaming text with accumulated content\n                    setStreamingText(fullSummary)\n                  }\n                } catch (_e) {\n                  // Ignore parse errors\n                }\n              }\n            }\n          }\n\n          setEditedNote(fullSummary)\n          \n          // Save the streamed summary using the proper server action\n          try {\n            const { saveStreamingSummary } = await import('@/lib/actions/consultations')\n            const saveResult = await saveStreamingSummary(consultation.id, fullSummary)\n\n            if (saveResult.success) {\n              setSuccessWithTimeout('Streaming summary generated and saved successfully!')\n\n              // Track successful consultation generation (PII-free)\n              trackConsultation('generated', consultationType)\n\n              // Update the consultation object for the parent component\n              if (onConsultationUpdate) {\n                onConsultationUpdate({\n                  ...consultation,\n                  ai_generated_note: fullSummary,\n                  status: 'generated' as const\n                })\n              }\n            } else {\n              console.error('Failed to save streaming summary:', saveResult.error)\n              setSuccess(`Summary generated but failed to save: ${saveResult.error}`)\n            }\n          } catch (saveError) {\n            console.error('Error saving streaming summary:', saveError)\n            setSuccess('Summary generated but failed to save. Please try regenerate.')\n          }\n          \n          setTimeout(() => setSuccess(''), 3000)\n        } catch (error) {\n          console.error('Streaming error:', error)\n          setSuccess('Failed to generate streaming summary')\n          setTimeout(() => setSuccess(''), 5000)\n        }\n\n    } catch {\n      setSuccess('An unexpected error occurred')\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleApprove = async () => {\n    if (!editedNote.trim()) {\n      setSuccess('Please provide a summary before approving')\n      return\n    }\n\n    setIsApproving(true)\n    setSuccess('')\n\n    try {\n      const result = await approveConsultation(consultation.id, editedNote)\n\n      if (result.success) {\n        setSuccess('Consultation approved successfully!')\n\n        // Track successful consultation approval (PII-free)\n        trackConsultation('approved', consultationType)\n\n        setTimeout(() => {\n          setSuccess('')\n          onClose()\n        }, 2000)\n      } else {\n        setSuccess(result.error || 'Failed to approve consultation')\n      }\n    } catch {\n      setSuccess('An unexpected error occurred')\n    } finally {\n      setIsApproving(false)\n    }\n  }\n\n  const handleCopyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(editedNote)\n      setSuccessWithTimeout('Copied to clipboard!', 2000)\n    } catch {\n      setSuccess('Failed to copy to clipboard')\n    }\n  }\n\n  // Web Audio API playback for Safari\n  const handleWebAudioPlayback = async () => {\n    // Stop logic - if audio is playing, stop it\n    if (audioSourceRef.current) {\n      audioSourceRef.current.stop()\n      audioSourceRef.current = null\n      setPlayingAudio(null)\n      return\n    }\n\n    setIsLoadingAudio(true)\n    setSuccess('Loading audio...')\n\n    try {\n      // Create or resume AudioContext (must be in user gesture)\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()\n      }\n      if (audioContextRef.current.state === 'suspended') {\n        await audioContextRef.current.resume()\n      }\n\n      const audioContext = audioContextRef.current\n\n      // Fetch audio through proxy\n      const proxyUrl = `/api/audio-proxy?url=${encodeURIComponent(consultation.primary_audio_url)}`\n      const response = await fetch(proxyUrl)\n      if (!response.ok) {\n        throw new Error(`Audio fetch failed: ${response.statusText}`)\n      }\n      const audioData = await response.arrayBuffer()\n\n      // Decode audio data (handles format conversion)\n      const audioBuffer = await audioContext.decodeAudioData(audioData)\n\n      // Create source and play\n      const source = audioContext.createBufferSource()\n      source.buffer = audioBuffer\n      source.connect(audioContext.destination)\n      source.start(0)\n\n      // Handle playback end\n      source.onended = () => {\n        setPlayingAudio(null)\n        audioSourceRef.current = null\n      }\n\n      audioSourceRef.current = source\n      setPlayingAudio({} as HTMLAudioElement) // Just to indicate playing state\n      setSuccess('')\n\n    } catch (error) {\n      console.error('Web Audio API Error:', error)\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      setSuccess(`Unable to play audio: ${errorMessage}`)\n      setPlayingAudio(null)\n    } finally {\n      setIsLoadingAudio(false)\n    }\n  }\n\n  const playAudio = async () => {\n    // Detect Safari\n    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)\n\n    // For Safari, use Web Audio API\n    if (isSafari) {\n      await handleWebAudioPlayback()\n      return\n    }\n\n    // For other browsers, use existing logic\n    if (playingAudio) {\n      playingAudio.pause()\n      playingAudio.currentTime = 0\n      setPlayingAudio(null)\n      return\n    }\n\n    try {\n      const audio = new Audio(consultation.primary_audio_url)\n      audio.onended = () => setPlayingAudio(null)\n      audio.onerror = () => {\n        setPlayingAudio(null)\n        setSuccess('Failed to play audio')\n      }\n\n      setPlayingAudio(audio)\n      await audio.play()\n    } catch (error) {\n      console.error('Audio playback error:', error)\n      setPlayingAudio(null)\n      setSuccess('Failed to play audio')\n    }\n  }\n\n  // Get the best supported audio format for this device\n  const getSupportedMimeType = () => {\n    const types = [\n      'audio/webm;codecs=opus',\n      'audio/webm',\n      'audio/mp4',\n      'audio/mpeg'\n    ]\n    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'\n  }\n\n  // Additional audio recording functions\n  const startAdditionalRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })\n      const mimeType = getSupportedMimeType()\n      const recorder = new MediaRecorder(stream, { mimeType })\n      const chunks: BlobPart[] = []\n\n      recorder.ondataavailable = (e) => chunks.push(e.data)\n      recorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType })\n        setAdditionalAudioBlob(blob)\n        stream.getTracks().forEach(track => track.stop())\n      }\n\n      recorder.start()\n      setMediaRecorder(recorder)\n      setIsRecordingAdditional(true)\n    } catch (_error) {\n      setSuccess('Failed to start recording. Please check microphone permissions.')\n    }\n  }\n\n  const stopAdditionalRecording = () => {\n    if (mediaRecorder && mediaRecorder.state === 'recording') {\n      mediaRecorder.stop()\n      setIsRecordingAdditional(false)\n      setMediaRecorder(null)\n    }\n  }\n\n  const uploadAdditionalAudio = async () => {\n    if (!additionalAudioBlob) return\n\n    try {\n      const audioFile = new File([additionalAudioBlob], `additional_audio_${Date.now()}.wav`, {\n        type: 'audio/wav'\n      })\n\n      const result = await addAdditionalAudio(consultation.id, audioFile)\n      if (result.success) {\n        setSuccess('Additional audio uploaded successfully! Refreshing consultation data...')\n        setAdditionalAudioBlob(null)\n\n        // Fetch updated consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n            setSuccess('Additional audio uploaded successfully! Please regenerate summary to include this audio.')\n          }\n        }\n\n        setSuccessWithTimeout('')\n      } else {\n        setSuccess(result.error || 'Failed to upload additional audio')\n      }\n    } catch (_error) {\n      setSuccess('Failed to upload additional audio')\n    }\n  }\n\n  // Delete handlers\n  const handleDeleteAudio = async (audioUrl: string) => {\n    try {\n      setSuccess('Deleting audio...')\n      const result = await deleteAdditionalAudio(consultation.id, audioUrl)\n\n      if (result.success) {\n        setSuccessWithTimeout('Audio deleted successfully!')\n\n        // Refresh consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n          }\n        }\n      } else {\n        setSuccess(result.error || 'Failed to delete audio')\n      }\n    } catch (_error) {\n      setSuccess('Failed to delete audio')\n    } finally {\n      setShowDeleteMenu(null)\n    }\n  }\n\n  const handleDeleteImage = async (imageUrl: string) => {\n    try {\n      setSuccess('Deleting image...')\n      const result = await deleteConsultationImage(consultation.id, imageUrl)\n\n      if (result.success) {\n        setSuccessWithTimeout('Image deleted successfully!')\n\n        // Refresh consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n          }\n        }\n      } else {\n        setSuccess(result.error || 'Failed to delete image')\n      }\n    } catch (_error) {\n      setSuccess('Failed to delete image')\n    } finally {\n      setShowDeleteMenu(null)\n    }\n  }\n\n  // Long press handlers for mobile\n  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {\n    const timer = setTimeout(() => {\n      setShowDeleteMenu({ type, url })\n    }, 500) // 500ms long press\n    setLongPressTimer(timer)\n  }\n\n  const handleLongPressEnd = () => {\n    if (longPressTimer) {\n      clearTimeout(longPressTimer)\n      setLongPressTimer(null)\n    }\n  }\n\n  const modalContent = (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50\">\n      <div className=\"absolute top-4 left-1/2 transform -translate-x-1/2 bg-white rounded-2xl max-w-5xl w-[95%] h-[90vh] shadow-2xl flex flex-col overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 px-3 sm:px-6 py-3 sm:py-4 border-b border-orange-200/50\">\n          <div className=\"flex items-start sm:items-center justify-between gap-2\">\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"text-lg sm:text-xl font-bold text-slate-800 truncate\">\n                {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}\n              </h3>\n              <p className=\"text-xs sm:text-sm text-slate-600 break-words\">\n                {formatDate(consultation.created_at)} • {consultation.submitted_by} • {consultation.status}\n              </p>\n              {consultation.doctor_notes && (\n                <p className=\"text-xs sm:text-sm text-slate-500 mt-1 italic line-clamp-2\">\n                  Doctor&apos;s Notes: {consultation.doctor_notes}\n                </p>\n              )}\n            </div>\n            <div className=\"flex flex-col sm:flex-row items-end sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-shrink-0\">\n              {/* Consultation Type Dropdown */}\n              <div className=\"flex flex-col items-end\">\n                <label className=\"text-xs text-slate-600 mb-1 whitespace-nowrap hidden sm:block\">Consultation Type</label>\n                <label className=\"text-xs text-slate-600 mb-1 whitespace-nowrap sm:hidden\">Type</label>\n                <select\n                  value={consultationType}\n                  onChange={(e) => setConsultationType(e.target.value as ConsultationType)}\n                  className=\"px-2 py-1 text-xs border border-orange-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent w-24 sm:w-auto text-black\"\n                  disabled={consultation.status === 'approved'}\n                >\n                  {Object.entries(CONSULTATION_TYPE_LABELS).map(([value, label]) => (\n                    <option key={value} value={value} className=\"text-black\">\n                      {label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-1.5 sm:p-2 hover:bg-orange-100 rounded-xl transition-colors flex-shrink-0\"\n              >\n                <X className=\"w-4 h-4 sm:w-5 sm:h-5 text-slate-600\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-3 sm:p-6 space-y-4 sm:space-y-6 flex-1 overflow-y-auto\">\n          {/* Audio Section - Side by Side Layout */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\n            {/* Primary Audio */}\n            <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-200\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"text-lg font-semibold text-slate-800 flex items-center\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mr-3\"></div>\n                  Primary Audio\n                </h4>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={playAudio}\n                  disabled={isLoadingAudio}\n                  className=\"flex items-center space-x-3 px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                >\n                  {isLoadingAudio ? (\n                    <Loader2 className=\"w-5 h-5 animate-spin\" />\n                  ) : playingAudio ? (\n                    <Pause className=\"w-5 h-5\" />\n                  ) : (\n                    <Play className=\"w-5 h-5\" />\n                  )}\n                  <span>\n                    {isLoadingAudio ? 'Loading...' : playingAudio ? 'Pause Audio' : 'Play Audio'}\n                  </span>\n                </button>\n              </div>\n            </div>\n\n            {/* Additional Audio */}\n            {consultation.status !== 'approved' && (\n              <div className=\"bg-gradient-to-r from-teal-50 to-emerald-50 rounded-xl p-3 sm:p-5 border border-teal-200\">\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0\">\n                  <h4 className=\"text-base sm:text-lg font-semibold text-slate-800 flex items-center\">\n                    <div className=\"w-2 h-2 bg-teal-500 rounded-full mr-2 sm:mr-3\"></div>\n                    Additional Audio\n                  </h4>\n                  <span className=\"text-xs text-gray-500 bg-white px-2 py-1 rounded-full self-start\">Optional</span>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 gap-2\">\n                    {!isRecordingAdditional && !additionalAudioBlob && (\n                      <button\n                        onClick={startAdditionalRecording}\n                        className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg text-sm font-medium transition-all duration-200 w-full sm:w-auto\"\n                      >\n                        <Mic className=\"w-4 h-4\" />\n                        <span>Record</span>\n                      </button>\n                    )}\n\n                    {isRecordingAdditional && (\n                      <button\n                        onClick={stopAdditionalRecording}\n                        className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg text-sm font-medium animate-pulse transition-all duration-200 w-full sm:w-auto\"\n                      >\n                        <Square className=\"w-4 h-4\" />\n                        <span>Stop Recording</span>\n                      </button>\n                    )}\n\n                    {additionalAudioBlob && (\n                      <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto\">\n                        <button\n                          onClick={uploadAdditionalAudio}\n                          className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-all duration-200\"\n                        >\n                          <Upload className=\"w-4 h-4\" />\n                          <span>Upload</span>\n                        </button>\n                        <button\n                          onClick={() => setAdditionalAudioBlob(null)}\n                          className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-all duration-200\"\n                        >\n                          <X className=\"w-4 h-4\" />\n                          <span>Cancel</span>\n                        </button>\n                      </div>\n                    )}\n                  </div>\n\n                  {isRecordingAdditional && (\n                    <div className=\"flex items-center space-x-2 bg-red-100 px-3 py-2 rounded-lg\">\n                      <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                      <span className=\"text-sm text-red-700 font-medium\">Recording...</span>\n                    </div>\n                  )}\n\n                  {additionalAudioBlob && (\n                    <div className=\"flex items-center space-x-2 bg-green-100 px-3 py-2 rounded-lg\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm text-green-700 font-medium\">Ready to upload</span>\n                    </div>\n                  )}\n\n                  {/* Show existing additional audio files */}\n                  {consultation.additional_audio_urls &&\n                   Array.isArray(consultation.additional_audio_urls) &&\n                   consultation.additional_audio_urls.length > 0 && (\n                    <div className=\"p-3 bg-white rounded-lg border\">\n                      <p className=\"text-sm text-gray-600 mb-2\">Additional Audio Files ({consultation.additional_audio_urls.length})</p>\n                      <div className=\"space-y-2\">\n                        {consultation.additional_audio_urls\n                          .filter((audioUrl): audioUrl is string => typeof audioUrl === 'string' && audioUrl !== null)\n                          .map((audioUrl, index) => (\n                          <div\n                            key={index}\n                            className=\"flex items-center justify-between p-2 bg-gray-50 rounded border group\"\n                            onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url: audioUrl })}\n                            onMouseLeave={() => setShowDeleteMenu(null)}\n                            onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('audio', audioUrl)}\n                            onTouchEnd={handleLongPressEnd}\n                            onTouchCancel={handleLongPressEnd}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <div className=\"w-2 h-2 bg-teal-500 rounded-full\"></div>\n                              <span className=\"text-sm text-gray-700\">Audio {index + 1}</span>\n                            </div>\n\n                            {/* Delete button - Desktop hover / Mobile long press */}\n                            {consultation.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === audioUrl && (\n                              <button\n                                onClick={() => handleDeleteAudio(audioUrl)}\n                                className=\"flex items-center space-x-1 px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-all duration-200\"\n                                title=\"Delete audio\"\n                              >\n                                <Trash2 className=\"w-3 h-3\" />\n                                <span>Delete</span>\n                              </button>\n                            )}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Images Section - Compact Layout */}\n          {consultation.image_urls && Array.isArray(consultation.image_urls) && consultation.image_urls.length > 0 && (\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3\">\n                Original Images ({consultation.image_urls.length} image{consultation.image_urls.length > 1 ? 's' : ''})\n              </h4>\n              <div className=\"flex flex-wrap gap-3\">\n                {consultation.image_urls\n                  .filter((imageUrl): imageUrl is string => typeof imageUrl === 'string' && imageUrl !== null)\n                  .map((imageUrl, index) => (\n                    <div\n                      key={index}\n                      className=\"relative group\"\n                      onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'image', url: imageUrl })}\n                      onMouseLeave={() => setShowDeleteMenu(null)}\n                      onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('image', imageUrl)}\n                      onTouchEnd={handleLongPressEnd}\n                      onTouchCancel={handleLongPressEnd}\n                    >\n                      <div className=\"w-24 h-24 bg-gray-200 rounded border overflow-hidden\">\n                        <Image\n                          src={imageUrl}\n                          alt={`Original image ${index + 1}`}\n                          className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-200 cursor-pointer\"\n                          width={96}\n                          height={96}\n                          priority={index < 2}\n                          loading={index < 2 ? 'eager' : 'lazy'}\n                          onError={(e) => {\n                            console.error('Image failed to load:', imageUrl)\n                            const target = e.currentTarget as HTMLImageElement\n                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5NiA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00OCA2NEw0MCA1Nkw0OCA0OEw1NiA1Nkw0OCA2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'\n                            target.alt = 'Failed to load image'\n                          }}\n                        />\n                      </div>\n                      <div className=\"absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded\">\n                        {index + 1}\n                      </div>\n\n                      {/* Delete button - Desktop hover / Mobile long press */}\n                      {consultation.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === imageUrl && (\n                        <button\n                          onClick={() => handleDeleteImage(imageUrl)}\n                          className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs shadow-lg transition-all duration-200 z-10\"\n                          title=\"Delete image\"\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                        </button>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {/* Additional Images Section for Nurses */}\n          <div className=\"bg-orange-50 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"text-sm font-medium text-slate-800\">\n                Additional Images\n              </h4>\n              <span className=\"text-xs text-slate-500\">Optional</span>\n            </div>\n\n            <div className=\"flex items-center space-x-3 mb-3\">\n              {/* Compact Camera button */}\n              <button\n                onClick={() => cameraInputRef.current?.click()}\n                className=\"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm\"\n              >\n                <Camera className=\"w-4 h-4 text-orange-500\" />\n                <span className=\"text-orange-700\">Camera</span>\n              </button>\n\n              {/* Compact Upload button */}\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm\"\n              >\n                <Upload className=\"w-4 h-4\" />\n                <span className=\"text-orange-700\">Upload</span>\n              </button>\n\n              {additionalImages.length > 0 && (\n                <span className=\"text-xs text-orange-600 font-medium\">\n                  +{additionalImages.length} image{additionalImages.length > 1 ? 's' : ''}\n                </span>\n              )}\n            </div>\n\n            {/* Compact image previews */}\n            {additionalImages.length > 0 && (\n              <div className=\"flex flex-wrap gap-2\">\n                {additionalImages.map((image) => (\n                  <div key={image.id} className=\"relative\">\n                    <Image\n                      src={image.preview || ''}\n                      alt=\"Additional\"\n                      className=\"w-12 h-12 object-cover rounded border hover:w-24 hover:h-24 transition-all duration-200 cursor-pointer\"\n                      width={48}\n                      height={48}\n                    />\n                    <button\n                      onClick={() => removeAdditionalImage(image.id)}\n                      className=\"absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs\"\n                    >\n                      <X className=\"w-2.5 h-2.5\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Hidden inputs */}\n            <input\n              ref={cameraInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              capture=\"environment\"\n              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}\n              className=\"hidden\"\n            />\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              multiple\n              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}\n              className=\"hidden\"\n            />\n          </div>\n\n          {/* Additional Notes Section */}\n          <div className=\"bg-blue-50 rounded-xl p-3 sm:p-5 border border-blue-100\">\n            <h4 className=\"text-base sm:text-lg font-semibold text-slate-800 mb-3 sm:mb-4 flex items-center\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2 sm:mr-3\"></div>\n              Additional Notes (Optional)\n            </h4>\n            <textarea\n              value={additionalNotes}\n              onChange={(e) => setAdditionalNotes(e.target.value)}\n              placeholder=\"Add any additional context or instructions for the AI...\"\n              rows={3}\n              className=\"w-full px-3 sm:px-4 py-2 sm:py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm bg-white\"\n              disabled={consultation.status === 'approved'}\n            />\n            <p className=\"text-xs text-blue-600 mt-2\">\n              These notes will be included in the AI analysis along with the audio recordings.\n            </p>\n          </div>\n\n          {/* Summary Section */}\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mr-3\"></div>\n                AI-Generated Patient Summary\n              </h4>\n              <div className=\"flex flex-wrap items-center gap-2\">\n                {(consultation.status === 'pending' || consultation.status === 'generated') && (\n                  <button\n                    onClick={handleGenerateSummary}\n                    disabled={isGenerating}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-purple-300 disabled:to-purple-400 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    <Wand2 className=\"w-4 h-4\" />\n                    <span className=\"hidden sm:inline\">{isGenerating ? 'Generating...' : consultation.status === 'generated' ? 'Regenerate Summary' : 'Generate Summary'}</span>\n                    <span className=\"sm:hidden\">{isGenerating ? 'Gen...' : 'Generate'}</span>\n                  </button>\n                )}\n\n                {editedNote && (\n                  <button\n                    onClick={handleCopyToClipboard}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    <Copy className=\"w-4 h-4\" />\n                    <span>Copy</span>\n                  </button>\n                )}\n              </div>\n            </div>\n\n            <textarea\n              value={isGenerating ? streamingText : editedNote}\n              onChange={(e) => setEditedNote(e.target.value)}\n              placeholder={\n                consultation.status === 'pending'\n                  ? 'Click \"Generate Summary\" to create an AI-powered patient summary ...'\n                  : 'Edit the patient summary as needed...'\n              }\n              className=\"w-full min-h-[400px] max-h-[600px] p-4 border border-gray-200 rounded-lg resize-y focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white text-black text-sm font-medium flex-1\"\n              style={{\n                height: (isGenerating ? streamingText : editedNote) ? `${Math.max(400, Math.min(600, (isGenerating ? streamingText : editedNote).split('\\n').length * 24 + 80))}px` : '400px'\n              }}\n            />\n            {isGenerating && (\n              <div className=\"text-xs text-purple-600 mt-2 flex items-center\">\n                <span className=\"inline-block w-2 h-2 bg-purple-500 animate-pulse mr-2 rounded-full\"></span>\n                Streaming response...\n              </div>\n            )}\n          </div>\n\n          {/* Status Messages */}\n          {success && (\n            <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <p className=\"text-sm text-green-700 font-medium\">{success}</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Actions Footer */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 px-4 sm:px-6 py-4 border-t border-orange-200/50 flex-shrink-0\">\n          <div className=\"flex flex-col space-y-4\">\n            {/* Status Info */}\n            <div className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center w-fit ${\n                consultation.status === 'pending' ? 'bg-orange-100 text-orange-800 border border-orange-300' :\n                consultation.status === 'generated' ? 'bg-emerald-100 text-emerald-800 border border-emerald-300' :\n                'bg-green-100 text-green-800 border border-green-300'\n              }`}>\n                {consultation.status.toUpperCase()}\n              </div>\n              <span className=\"text-xs sm:text-sm text-slate-600\">\n                Last updated: {formatDate(consultation.updated_at)}\n              </span>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"w-full sm:w-auto px-6 py-3 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-orange-50 transition-all duration-200 text-center\"\n              >\n                Close\n              </button>\n\n              {consultation.status !== 'approved' && editedNote.trim() && (\n                <button\n                  onClick={handleApprove}\n                  disabled={isApproving}\n                  className=\"w-full sm:w-auto flex items-center justify-center space-x-2 px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 disabled:from-teal-300 disabled:to-emerald-400 transition-all duration-200 shadow-lg hover:shadow-xl\"\n                >\n                  <Save className=\"w-4 h-4\" />\n                  <span>{isApproving ? 'Approving...' : 'Approve & Save'}</span>\n                </button>\n              )}\n\n              {consultation.status !== 'approved' && !editedNote.trim() && (\n                <div className=\"w-full sm:w-auto px-6 py-3 text-sm text-center text-gray-500 bg-gray-100 rounded-xl border border-gray-200\">\n                  Generate or add summary to approve\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  // Don't render anything on server-side\n  if (!mounted) {\n    return null\n  }\n\n  // Use portal to render modal at document body level\n  return createPortal(modalContent, document.body)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAkBO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,oBAAoB,EAAE,UAAU,EAA0B;IACnH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,WAAW,IAAI,aAAa,iBAAiB,IAAI;IAC3G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,gCAAgC;IAChC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgC;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwD,EAAE;IACjH,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAEzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,mDAAmD;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD,AAAC,aAAa,iBAAiB,IAAyB;IAE1D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,gBAAgB,IAAI;IAExF,6BAA6B;IAC7B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IAEtG,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACrD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACvD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAExD,0DAA0D;IAC1D,MAAM,wBAAwB,CAAC,SAAiB,UAAU,IAAI;QAC5D,6BAA6B;QAC7B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,aAAa,kBAAkB,OAAO;QACxC;QAEA,WAAW;QAEX,mCAAmC;QACnC,IAAI,SAAS;YACX,kBAAkB,OAAO,GAAG,WAAW;gBACrC,WAAW;gBACX,kBAAkB,OAAO,GAAG;YAC9B,GAAG;QACL;IACF;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,IAAI,kBAAkB,OAAO,EAAE;YAC7B,aAAa,kBAAkB,OAAO;YACtC,kBAAkB,OAAO,GAAG;QAC9B;IACF,GAAG;QAAC,aAAa,EAAE;KAAC;IAEpB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,gBAAgB;gBAClB,aAAa;YACf;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,aAAa,WAAW,IAAI,aAAa,iBAAiB,IAAI;IAC9E,GAAG;QAAC,aAAa,WAAW;QAAE,aAAa,iBAAiB;KAAC;IAE7D,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB,aAAa,gBAAgB,IAAI;IACtD,GAAG;QAAC,aAAa,gBAAgB;KAAC;IAElC,2DAA2D;IAC3D,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YAEX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACrC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM,UAAS,aAAa;gBAExD,0BAA0B;gBAC1B,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,MAAM,eAAe,MAAM,WAAW,MAAM,aAAa,SAAS,IAAI,IAAI,aAAa,EAAE,EAAE;gBAE3F,IAAI,aAAa,OAAO,IAAI,aAAa,GAAG,EAAE;oBAC5C,MAAM,UAAU,IAAI,eAAe,CAAC;oBACpC,oBAAoB,CAAA,OAAQ;+BAAI;4BAAM;gCACpC,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG;gCACxB,KAAK,aAAa,GAAG;gCACrB;4BACF;yBAAE;gBACJ,OAAO;oBACL,WAAW,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,EAAE;oBACjE;gBACF;YACF;YAEA,sBAAsB;QACxB,EAAE,OAAO,QAAQ;YACf,WAAW;QACb;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB,CAAA;YAClB,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAClD,IAAI,iBAAiB,cAAc,OAAO,IAAI,OAAO,cAAc,OAAO,KAAK,UAAU;gBACvF,IAAI,eAAe,CAAC,cAAc,OAAO;YAC3C;YACA,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACvC;IACF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;QAChB,WAAW;QACX,iBAAiB;QAEjB,IAAI;YACF,iDAAiD;YACjD,MAAM,YAAY;mBACZ,MAAM,OAAO,CAAC,aAAa,UAAU,IAAI,aAAa,UAAU,GAAG,EAAE;mBACtE,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG;aACvC,CAAC,MAAM,CAAC,CAAC,MAAuB,OAAO,QAAQ,YAAY,QAAQ;YAEpE,2EAA2E;YAC3E,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,MAAM,EAAE,wBAAwB,EAAE,GAAG;gBACrC,MAAM,yBAAyB,aAAa,EAAE,EAAE;YAClD;YAEA,mDAAmD;YACnD,IAAI;gBACA,MAAM,WAAW,MAAM,MAAM,gCAAgC;oBAC3D,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,mBAAmB,aAAa,iBAAiB;wBACjD,uBAAuB,MAAM,OAAO,CAAC,aAAa,qBAAqB,IAAI,aAAa,qBAAqB,GAAG,EAAE;wBAClH,YAAY;+BACN,MAAM,OAAO,CAAC,aAAa,UAAU,IAAI,aAAa,UAAU,GAAG,EAAE;+BACtE;yBACJ;wBAED,cAAc,aAAa,YAAY,IAAI;wBAC3C,mBAAmB;wBACnB,cAAc,aAAa,YAAY,IAAI;wBAC3C,kBAAkB,mBAAmB;wBACrC,cAAc,aAAa,YAAY,IAAI;wBAC3C,aAAa,cAAc;wBAC3B,YAAY,aAAa,UAAU,IAAI;oBACzC;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBACnE;gBAEA,MAAM,SAAS,SAAS,IAAI,EAAE;gBAC9B,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,UAAU,IAAI;gBACpB,IAAI,cAAc;gBAClB,iBAAiB;gBAEjB,MAAO,KAAM;oBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;oBACzC,IAAI,MAAM;oBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC,OAAO;wBAAE,QAAQ;oBAAK;oBACnD,MAAM,QAAQ,MAAM,KAAK,CAAC;oBAE1B,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;4BAC7B,IAAI;gCACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;gCACnC,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,EAAE;oCACtC,sCAAsC;oCACtC,MAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,UAAU;oCACxE,eAAe;oCACf,2DAA2D;oCAC3D,iBAAiB;gCACnB;4BACF,EAAE,OAAO,IAAI;4BACX,sBAAsB;4BACxB;wBACF;oBACF;gBACF;gBAEA,cAAc;gBAEd,2DAA2D;gBAC3D,IAAI;oBACF,MAAM,EAAE,oBAAoB,EAAE,GAAG;oBACjC,MAAM,aAAa,MAAM,qBAAqB,aAAa,EAAE,EAAE;oBAE/D,IAAI,WAAW,OAAO,EAAE;wBACtB,sBAAsB;wBAEtB,sDAAsD;wBACtD,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa;wBAE/B,0DAA0D;wBAC1D,IAAI,sBAAsB;4BACxB,qBAAqB;gCACnB,GAAG,YAAY;gCACf,mBAAmB;gCACnB,QAAQ;4BACV;wBACF;oBACF,OAAO;wBACL,QAAQ,KAAK,CAAC,qCAAqC,WAAW,KAAK;wBACnE,WAAW,CAAC,sCAAsC,EAAE,WAAW,KAAK,EAAE;oBACxE;gBACF,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,WAAW;gBACb;gBAEA,WAAW,IAAM,WAAW,KAAK;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,WAAW;gBACX,WAAW,IAAM,WAAW,KAAK;YACnC;QAEJ,EAAE,OAAM;YACN,WAAW;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,WAAW;YACX;QACF;QAEA,eAAe;QACf,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,EAAE,EAAE;YAE1D,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW;gBAEX,oDAAoD;gBACpD,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;gBAE9B,WAAW;oBACT,WAAW;oBACX;gBACF,GAAG;YACL,OAAO;gBACL,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF,EAAE,OAAM;YACN,WAAW;QACb,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,sBAAsB,wBAAwB;QAChD,EAAE,OAAM;YACN,WAAW;QACb;IACF;IAEA,oCAAoC;IACpC,MAAM,yBAAyB;QAC7B,4CAA4C;QAC5C,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,IAAI;YAC3B,eAAe,OAAO,GAAG;YACzB,gBAAgB;YAChB;QACF;QAEA,kBAAkB;QAClB,WAAW;QAEX,IAAI;YACF,0DAA0D;YAC1D,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,gBAAgB,OAAO,GAAG,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;YAC1F;YACA,IAAI,gBAAgB,OAAO,CAAC,KAAK,KAAK,aAAa;gBACjD,MAAM,gBAAgB,OAAO,CAAC,MAAM;YACtC;YAEA,MAAM,eAAe,gBAAgB,OAAO;YAE5C,4BAA4B;YAC5B,MAAM,WAAW,CAAC,qBAAqB,EAAE,mBAAmB,aAAa,iBAAiB,GAAG;YAC7F,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YACA,MAAM,YAAY,MAAM,SAAS,WAAW;YAE5C,gDAAgD;YAChD,MAAM,cAAc,MAAM,aAAa,eAAe,CAAC;YAEvD,yBAAyB;YACzB,MAAM,SAAS,aAAa,kBAAkB;YAC9C,OAAO,MAAM,GAAG;YAChB,OAAO,OAAO,CAAC,aAAa,WAAW;YACvC,OAAO,KAAK,CAAC;YAEb,sBAAsB;YACtB,OAAO,OAAO,GAAG;gBACf,gBAAgB;gBAChB,eAAe,OAAO,GAAG;YAC3B;YAEA,eAAe,OAAO,GAAG;YACzB,gBAAgB,CAAC,GAAuB,iCAAiC;;YACzE,WAAW;QAEb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,WAAW,CAAC,sBAAsB,EAAE,cAAc;YAClD,gBAAgB;QAClB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,YAAY;QAChB,gBAAgB;QAChB,MAAM,WAAW,iCAAiC,IAAI,CAAC,UAAU,SAAS;QAE1E,gCAAgC;QAChC,IAAI,UAAU;YACZ,MAAM;YACN;QACF;QAEA,yCAAyC;QACzC,IAAI,cAAc;YAChB,aAAa,KAAK;YAClB,aAAa,WAAW,GAAG;YAC3B,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,IAAI,MAAM,aAAa,iBAAiB;YACtD,MAAM,OAAO,GAAG,IAAM,gBAAgB;YACtC,MAAM,OAAO,GAAG;gBACd,gBAAgB;gBAChB,WAAW;YACb;YAEA,gBAAgB;YAChB,MAAM,MAAM,IAAI;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,gBAAgB;YAChB,WAAW;QACb;IACF;IAEA,sDAAsD;IACtD,MAAM,uBAAuB;QAC3B,MAAM,QAAQ;YACZ;YACA;YACA;YACA;SACD;QACD,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,cAAc,eAAe,CAAC,UAAU;IACpE;IAEA,uCAAuC;IACvC,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,MAAM,WAAW;YACjB,MAAM,WAAW,IAAI,cAAc,QAAQ;gBAAE;YAAS;YACtD,MAAM,SAAqB,EAAE;YAE7B,SAAS,eAAe,GAAG,CAAC,IAAM,OAAO,IAAI,CAAC,EAAE,IAAI;YACpD,SAAS,MAAM,GAAG;gBAChB,MAAM,OAAO,IAAI,KAAK,QAAQ;oBAAE,MAAM;gBAAS;gBAC/C,uBAAuB;gBACvB,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChD;YAEA,SAAS,KAAK;YACd,iBAAiB;YACjB,yBAAyB;QAC3B,EAAE,OAAO,QAAQ;YACf,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,iBAAiB,cAAc,KAAK,KAAK,aAAa;YACxD,cAAc,IAAI;YAClB,yBAAyB;YACzB,iBAAiB;QACnB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,qBAAqB;QAE1B,IAAI;YACF,MAAM,YAAY,IAAI,KAAK;gBAAC;aAAoB,EAAE,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;gBACtF,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,EAAE,EAAE;YACzD,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW;gBACX,uBAAuB;gBAEvB,kCAAkC;gBAClC,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,sBAAsB,MAAM;gBAElC,IAAI,oBAAoB,OAAO,IAAI,oBAAoB,IAAI,EAAE;oBAC3D,MAAM,sBAAsB,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;oBACrG,IAAI,uBAAuB,sBAAsB;wBAC/C,qBAAqB;wBACrB,WAAW;oBACb;gBACF;gBAEA,sBAAsB;YACxB,OAAO;gBACL,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,QAAQ;YACf,WAAW;QACb;IACF;IAEA,kBAAkB;IAClB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,EAAE,EAAE;YAE5D,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBAEtB,4BAA4B;gBAC5B,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,sBAAsB,MAAM;gBAElC,IAAI,oBAAoB,OAAO,IAAI,oBAAoB,IAAI,EAAE;oBAC3D,MAAM,sBAAsB,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;oBACrG,IAAI,uBAAuB,sBAAsB;wBAC/C,qBAAqB;oBACvB;gBACF;YACF,OAAO;gBACL,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,QAAQ;YACf,WAAW;QACb,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa,EAAE,EAAE;YAE9D,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBAEtB,4BAA4B;gBAC5B,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,sBAAsB,MAAM;gBAElC,IAAI,oBAAoB,OAAO,IAAI,oBAAoB,IAAI,EAAE;oBAC3D,MAAM,sBAAsB,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;oBACrG,IAAI,uBAAuB,sBAAsB;wBAC/C,qBAAqB;oBACvB;gBACF;YACF,OAAO;gBACL,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,QAAQ;YACf,WAAW;QACb,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,iCAAiC;IACjC,MAAM,uBAAuB,CAAC,MAAyB;QACrD,MAAM,QAAQ,WAAW;YACvB,kBAAkB;gBAAE;gBAAM;YAAI;QAChC,GAAG,KAAK,mBAAmB;;QAC3B,kBAAkB;IACpB;IAEA,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,aAAa;YACb,kBAAkB;QACpB;IACF;IAEA,MAAM,6BACJ,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,aAAa,YAAY,IAAI,CAAC,SAAS,EAAE,aAAa,cAAc,IAAI,OAAO;;;;;;kDAElF,8OAAC;wCAAE,WAAU;;4CACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,UAAU;4CAAE;4CAAI,aAAa,YAAY;4CAAC;4CAAI,aAAa,MAAM;;;;;;;oCAE3F,aAAa,YAAY,kBACxB,8OAAC;wCAAE,WAAU;;4CAA6D;4CAClD,aAAa,YAAY;;;;;;;;;;;;;0CAIrD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAgE;;;;;;0DACjF,8OAAC;gDAAM,WAAU;0DAA0D;;;;;;0DAC3E,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;gDACV,UAAU,aAAa,MAAM,KAAK;0DAEjC,OAAO,OAAO,CAAC,mHAAA,CAAA,2BAAwB,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBAC3D,8OAAC;wDAAmB,OAAO;wDAAO,WAAU;kEACzC;uDADU;;;;;;;;;;;;;;;;kDAMnB,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;;;;;;oDAAgD;;;;;;;;;;;;sDAInE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;;oDAET,+BACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;+DACjB,6BACF,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;6EAEjB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAElB,8OAAC;kEACE,iBAAiB,eAAe,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;gCAOvE,aAAa,MAAM,KAAK,4BACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;;;;;wDAAsD;;;;;;;8DAGvE,8OAAC;oDAAK,WAAU;8DAAmE;;;;;;;;;;;;sDAErF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,CAAC,yBAAyB,CAAC,qCAC1B,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;8EAAK;;;;;;;;;;;;wDAIT,uCACC,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;wDAIT,qCACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEACC,SAAS,IAAM,uBAAuB;oEACtC,WAAU;;sFAEV,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;sFACb,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;gDAMb,uCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;gDAItD,qCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;gDAKxD,aAAa,qBAAqB,IAClC,MAAM,OAAO,CAAC,aAAa,qBAAqB,KAChD,aAAa,qBAAqB,CAAC,MAAM,GAAG,mBAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;gEAA6B;gEAAyB,aAAa,qBAAqB,CAAC,MAAM;gEAAC;;;;;;;sEAC7G,8OAAC;4DAAI,WAAU;sEACZ,aAAa,qBAAqB,CAChC,MAAM,CAAC,CAAC,WAAiC,OAAO,aAAa,YAAY,aAAa,MACtF,GAAG,CAAC,CAAC,UAAU,sBAChB,8OAAC;oEAEC,WAAU;oEACV,cAAc,IAAM,aAAa,MAAM,KAAK,cAAc,kBAAkB;4EAAE,MAAM;4EAAS,KAAK;wEAAS;oEAC3G,cAAc,IAAM,kBAAkB;oEACtC,cAAc,IAAM,aAAa,MAAM,KAAK,cAAc,qBAAqB,SAAS;oEACxF,YAAY;oEACZ,eAAe;;sFAEf,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAK,WAAU;;wFAAwB;wFAAO,QAAQ;;;;;;;;;;;;;wEAIxD,aAAa,MAAM,KAAK,cAAc,gBAAgB,SAAS,WAAW,gBAAgB,QAAQ,0BACjG,8OAAC;4EACC,SAAS,IAAM,kBAAkB;4EACjC,WAAU;4EACV,OAAM;;8FAEN,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,8OAAC;8FAAK;;;;;;;;;;;;;mEArBL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAmCtB,aAAa,UAAU,IAAI,MAAM,OAAO,CAAC,aAAa,UAAU,KAAK,aAAa,UAAU,CAAC,MAAM,GAAG,mBACrG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAyC;wCACnC,aAAa,UAAU,CAAC,MAAM;wCAAC;wCAAO,aAAa,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM;wCAAG;;;;;;;8CAExG,8OAAC;oCAAI,WAAU;8CACZ,aAAa,UAAU,CACrB,MAAM,CAAC,CAAC,WAAiC,OAAO,aAAa,YAAY,aAAa,MACtF,GAAG,CAAC,CAAC,UAAU,sBACd,8OAAC;4CAEC,WAAU;4CACV,cAAc,IAAM,aAAa,MAAM,KAAK,cAAc,kBAAkB;oDAAE,MAAM;oDAAS,KAAK;gDAAS;4CAC3G,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,aAAa,MAAM,KAAK,cAAc,qBAAqB,SAAS;4CACxF,YAAY;4CACZ,eAAe;;8DAEf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK;wDACL,KAAK,CAAC,eAAe,EAAE,QAAQ,GAAG;wDAClC,WAAU;wDACV,OAAO;wDACP,QAAQ;wDACR,UAAU,QAAQ;wDAClB,SAAS,QAAQ,IAAI,UAAU;wDAC/B,SAAS,CAAC;4DACR,QAAQ,KAAK,CAAC,yBAAyB;4DACvC,MAAM,SAAS,EAAE,aAAa;4DAC9B,OAAO,GAAG,GAAG;4DACb,OAAO,GAAG,GAAG;wDACf;;;;;;;;;;;8DAGJ,8OAAC;oDAAI,WAAU;8DACZ,QAAQ;;;;;;gDAIV,aAAa,MAAM,KAAK,cAAc,gBAAgB,SAAS,WAAW,gBAAgB,QAAQ,0BACjG,8OAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;2CApCjB;;;;;;;;;;;;;;;;sCA8CjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,SAAS,IAAM,eAAe,OAAO,EAAE;4CACvC,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;;;;;;;sDAIpC,8OAAC;4CACC,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;;;;;;;wCAGnC,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;4CAAK,WAAU;;gDAAsC;gDAClD,iBAAiB,MAAM;gDAAC;gDAAO,iBAAiB,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;gCAM1E,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,OAAO,IAAI;oDACtB,KAAI;oDACJ,WAAU;oDACV,OAAO;oDACP,QAAQ;;;;;;8DAEV,8OAAC;oDACC,SAAS,IAAM,sBAAsB,MAAM,EAAE;oDAC7C,WAAU;8DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;2CAZP,MAAM,EAAE;;;;;;;;;;8CAoBxB,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,SAAQ;oCACR,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACnE,WAAU;;;;;;8CAEZ,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,QAAQ;oCACR,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACnE,WAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;;;;;;wCAAsD;;;;;;;8CAGvE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,MAAM;oCACN,WAAU;oCACV,UAAU,aAAa,MAAM,KAAK;;;;;;8CAEpC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;;;;;;gDAA+C;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,aAAa,MAAM,KAAK,aAAa,aAAa,MAAM,KAAK,WAAW,mBACxE,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAoB,eAAe,kBAAkB,aAAa,MAAM,KAAK,cAAc,uBAAuB;;;;;;sEAClI,8OAAC;4DAAK,WAAU;sEAAa,eAAe,WAAW;;;;;;;;;;;;gDAI1D,4BACC,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMd,8OAAC;oCACC,OAAO,eAAe,gBAAgB;oCACtC,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aACE,aAAa,MAAM,KAAK,YACpB,yEACA;oCAEN,WAAU;oCACV,OAAO;wCACL,QAAQ,CAAC,eAAe,gBAAgB,UAAU,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,eAAe,gBAAgB,UAAU,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,KAAK,KAAK,EAAE,CAAC,GAAG;oCACxK;;;;;;gCAED,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA4E;;;;;;;;;;;;;wBAOjG,yBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;8BAO3D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,0EAA0E,EACzF,aAAa,MAAM,KAAK,YAAY,2DACpC,aAAa,MAAM,KAAK,cAAc,8DACtC,uDACA;kDACC,aAAa,MAAM,CAAC,WAAW;;;;;;kDAElC,8OAAC;wCAAK,WAAU;;4CAAoC;4CACnC,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,UAAU;;;;;;;;;;;;;0CAKrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;oCAIA,aAAa,MAAM,KAAK,cAAc,WAAW,IAAI,oBACpD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAM,cAAc,iBAAiB;;;;;;;;;;;;oCAIzC,aAAa,MAAM,KAAK,cAAc,CAAC,WAAW,IAAI,oBACrD,8OAAC;wCAAI,WAAU;kDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW1I,uCAAuC;IACvC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,oDAAoD;IACpD,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD", "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA6OsB,mBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/consultations-list.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef, useCallback } from 'react'\nimport { Clock, FileText, CheckCircle, Eye, Wand2, Filter } from 'lucide-react'\nimport { Consultation } from '@/lib/types'\nimport { formatDate, formatRelativeTime } from '@/lib/utils'\nimport { ConsultationModal } from './consultation-modal'\nimport { getConsultations } from '@/lib/actions/consultations'\n\ninterface ConsultationsListProps {\n  consultations: Consultation[]\n  hasMore?: boolean\n  doctorName?: string\n}\n\nexport function ConsultationsList({ consultations: initialConsultations, hasMore: initialHasMore = false, doctorName }: ConsultationsListProps) {\n  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)\n  const [filter, setFilter] = useState<'all' | 'pending' | 'generated' | 'approved'>('all')\n  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'yesterday' | 'custom'>('all')\n  const [customDate, setCustomDate] = useState<string>('')\n\n  // Infinite scroll state\n  const [consultations, setConsultations] = useState<Consultation[]>(initialConsultations)\n  const [hasMore, setHasMore] = useState(initialHasMore)\n  const [isLoading, setIsLoading] = useState(false)\n  const [currentPage, setCurrentPage] = useState(1)\n\n  // Refs for intersection observer\n  const loadMoreRef = useRef<HTMLDivElement>(null)\n  const observerRef = useRef<IntersectionObserver | null>(null)\n\n  // Update consultations when props change\n  useEffect(() => {\n    setConsultations(initialConsultations)\n    setHasMore(initialHasMore)\n    setCurrentPage(1)\n  }, [initialConsultations, initialHasMore])\n\n  // Load more consultations for infinite scroll\n  const loadMoreConsultations = useCallback(async () => {\n    if (isLoading || !hasMore) {\n      console.log('Skipping load more - isLoading:', isLoading, 'hasMore:', hasMore)\n      return\n    }\n\n    console.log('Loading more consultations - page:', currentPage + 1)\n    setIsLoading(true)\n    try {\n      const result = await getConsultations({\n        page: currentPage + 1,\n        pageSize: 15,\n        status: filter === 'all' ? undefined : filter\n      })\n\n      if (result.success && result.data.consultations.length > 0) {\n        setConsultations(prev => [...prev, ...result.data.consultations])\n        setHasMore(result.data.hasMore)\n        setCurrentPage(prev => prev + 1)\n        console.log('Loaded', result.data.consultations.length, 'more consultations')\n      } else {\n        setHasMore(false)\n        console.log('No more consultations to load')\n      }\n    } catch (error) {\n      console.error('Failed to load more consultations:', error)\n      setHasMore(false)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [currentPage, hasMore, isLoading, filter])\n\n  // Set up intersection observer for infinite scroll\n  useEffect(() => {\n    if (!loadMoreRef.current || !hasMore || isLoading) {\n      if (observerRef.current) {\n        observerRef.current.disconnect()\n        observerRef.current = null\n      }\n      return\n    }\n\n    if (observerRef.current) {\n      observerRef.current.disconnect()\n    }\n\n    observerRef.current = new IntersectionObserver(\n      (entries) => {\n        if (entries[0].isIntersecting && !isLoading && hasMore && consultations.length > 0) {\n          console.log('Info page intersection observer triggered - loading more')\n          loadMoreConsultations()\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    observerRef.current.observe(loadMoreRef.current)\n\n    return () => {\n      if (observerRef.current) {\n        observerRef.current.disconnect()\n        observerRef.current = null\n      }\n    }\n  }, [loadMoreConsultations, hasMore, isLoading, consultations.length])\n\n  // Handle consultation updates from modal\n  const handleConsultationUpdate = (updatedConsultation: Consultation) => {\n    setSelectedConsultation(updatedConsultation)\n    // Also update the consultation in the list\n    setConsultations(prev =>\n      prev.map(c => c.id === updatedConsultation.id ? updatedConsultation : c)\n    )\n  }\n\n  const getDateFilteredConsultations = (consultations: Consultation[]) => {\n    if (dateFilter === 'all') return consultations\n    \n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n    \n    return consultations.filter(consultation => {\n      const consultationDate = new Date(consultation.created_at)\n      \n      switch (dateFilter) {\n        case 'today':\n          return consultationDate.toDateString() === today.toDateString()\n        case 'yesterday':\n          return consultationDate.toDateString() === yesterday.toDateString()\n        case 'custom':\n          if (!customDate) return true\n          const selectedDate = new Date(customDate)\n          return consultationDate.toDateString() === selectedDate.toDateString()\n        default:\n          return true\n      }\n    })\n  }\n\n  const filteredConsultations = getDateFilteredConsultations(consultations).filter(consultation => {\n    if (filter === 'all') return true\n    return consultation.status === filter\n  })\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"w-4 h-4 text-orange-600\" />\n      case 'generated':\n        return <FileText className=\"w-4 h-4 text-emerald-600\" />\n      case 'approved':\n        return <CheckCircle className=\"w-4 h-4 text-green-600\" />\n      default:\n        return <Clock className=\"w-4 h-4 text-slate-500\" />\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'Pending Review'\n      case 'generated':\n        return 'Summary Generated'\n      case 'approved':\n        return 'Approved'\n      default:\n        return 'Unknown'\n    }\n  }\n\n  const getStatusBadgeClass = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-orange-100 text-orange-800 border border-orange-300'\n      case 'generated':\n        return 'bg-emerald-100 text-emerald-800 border border-emerald-300'\n      case 'approved':\n        return 'bg-green-100 text-green-800 border border-green-300'\n      default:\n        return 'bg-slate-100 text-slate-800 border border-slate-300'\n    }\n  }\n\n  const tabKeys: Array<'all' | 'pending' | 'generated' | 'approved'> = ['all', 'pending', 'generated', 'approved']\n\n  if (consultations.length === 0) {\n    return (\n      <div className=\"p-6 text-center\">\n        <FileText className=\"mx-auto h-12 w-12 text-slate-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-slate-800\">No consultations</h3>\n        <p className=\"mt-1 text-sm text-slate-600\">\n          Get started by recording a consultation on the mobile interface.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"p-6\">\n        {/* Filter Tabs with Date Filter on Right */}\n        <div className=\"border-b border-orange-200 mb-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n            {/* Filter Tabs */}\n            <nav className=\"-mb-px flex space-x-2 sm:space-x-8 overflow-x-auto\">\n              {tabKeys.map((key) => (\n                <button\n                  key={key}\n                  onClick={() => setFilter(key)}\n                  className={`whitespace-nowrap py-2 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-200 flex-shrink-0 ${\n                    filter === key\n                      ? 'border-teal-500 text-teal-600'\n                      : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-orange-300'\n                  }`}\n                >\n                  {key.charAt(0).toUpperCase() + key.slice(1)}\n                  {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length > 0 && (\n                    <span className={`ml-1 sm:ml-2 py-0.5 px-1 sm:px-2 rounded-full text-xs ${\n                      filter === key\n                        ? 'bg-teal-100 text-teal-700'\n                        : 'bg-orange-100 text-slate-800'\n                    }`}>\n                      {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length}\n                    </span>\n                  )}\n                </button>\n              ))}\n            </nav>\n\n            {/* Date Filter on Right */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n              <Filter className=\"w-4 h-4 text-slate-600\" />\n              <select\n                value={dateFilter}\n                onChange={(e) => {\n                  const value = e.target.value as 'all' | 'today' | 'yesterday' | 'custom'\n                  setDateFilter(value)\n                  if (value !== 'custom') {\n                    setCustomDate('')\n                  }\n                }}\n                className=\"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n              >\n                <option value=\"all\">All Dates</option>\n                <option value=\"today\">Today</option>\n                <option value=\"yesterday\">Yesterday</option>\n                <option value=\"custom\">Custom Date</option>\n              </select>\n              \n              {dateFilter === 'custom' && (\n                <input\n                  type=\"date\"\n                  value={customDate}\n                  onChange={(e) => setCustomDate(e.target.value)}\n                  className=\"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                />\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Consultations List */}\n        <div className=\"space-y-4\">\n          {filteredConsultations.map((consultation) => (\n            <div\n              key={consultation.id}\n              className=\"bg-white/80 backdrop-blur-sm border border-orange-200/50 rounded-lg p-3 sm:p-4 hover:shadow-lg hover:bg-white/90 transition-all duration-200 hover:scale-[1.02]\"\n            >\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0\">\n                <div className=\"flex items-start sm:items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n                  <div className=\"flex-shrink-0 mt-0.5 sm:mt-0\">\n                    {getStatusIcon(consultation.status)}\n                  </div>\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2\">\n                      <h3 className=\"text-sm font-medium text-slate-800 truncate\">\n                        {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}\n                      </h3>\n                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start ${getStatusBadgeClass(consultation.status)}`}>\n                        {getStatusText(consultation.status)}\n                      </span>\n                    </div>\n\n                    <div className=\"mt-1 flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-slate-600\">\n                      <span className=\"truncate\">Submitted by: {consultation.submitted_by}</span>\n                      <span className=\"hidden sm:inline\">•</span>\n                      <span>{formatRelativeTime(consultation.created_at)}</span>\n                      <span className=\"hidden sm:inline\">•</span>\n                      <span className=\"hidden sm:inline\">{formatDate(consultation.created_at)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                  {/* Primary Action Button - Different for each status */}\n                  <button\n                    onClick={() => setSelectedConsultation(consultation)}\n                    className={`inline-flex items-center px-2 sm:px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-md hover:shadow-lg transition-all duration-200 ${\n                      consultation.status === 'pending'\n                        ? 'border-transparent text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 focus:ring-teal-500'\n                        : consultation.status === 'generated'\n                        ? 'border-transparent text-white bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 focus:ring-blue-500'\n                        : 'border-transparent text-white bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 focus:ring-green-500'\n                    }`}\n                  >\n                    {consultation.status === 'pending' ? (\n                      <>\n                        <Wand2 className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">Generate Summary</span>\n                        <span className=\"sm:hidden\">Generate</span>\n                      </>\n                    ) : consultation.status === 'generated' ? (\n                      <>\n                        <Eye className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">Review Summary</span>\n                        <span className=\"sm:hidden\">Review</span>\n                      </>\n                    ) : (\n                      <>\n                        <Eye className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">View Approved</span>\n                        <span className=\"sm:hidden\">View</span>\n                      </>\n                    )}\n                  </button>\n\n                  {/* Secondary Action Button - View Details (consistent for all) */}\n                  <button\n                    onClick={() => setSelectedConsultation(consultation)}\n                    className=\"inline-flex items-center px-2 sm:px-3 py-1.5 border border-orange-300 hover:border-teal-400 text-xs font-medium rounded text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200\"\n                  >\n                    <Eye className=\"w-3 h-3 mr-1\" />\n                    <span className=\"hidden sm:inline\">View Details</span>\n                    <span className=\"sm:hidden\">Details</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Infinite scroll trigger */}\n        {hasMore && (\n          <div ref={loadMoreRef} className=\"flex justify-center py-4\">\n            {isLoading ? (\n              <div className=\"flex items-center space-x-2 text-sm text-slate-600\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"></div>\n                <span>Loading more consultations...</span>\n              </div>\n            ) : (\n              <div className=\"text-sm text-slate-500\">Scroll to load more</div>\n            )}\n          </div>\n        )}\n\n        {filteredConsultations.length === 0 && filter !== 'all' && (\n          <div className=\"text-center py-8\">\n            <p className=\"text-sm text-slate-600\">\n              No consultations with status &quot;{filter}&quot;.\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Consultation Modal */}\n      {selectedConsultation && (\n        <ConsultationModal\n          consultation={selectedConsultation}\n          onClose={() => setSelectedConsultation(null)}\n          onConsultationUpdate={handleConsultationUpdate}\n          doctorName={doctorName}\n        />\n      )}\n    </>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;;AAeO,SAAS,kBAAkB,EAAE,eAAe,oBAAoB,EAAE,SAAS,iBAAiB,KAAK,EAAE,UAAU,EAA0B;IAC5I,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgD;IACnF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,wBAAwB;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,iCAAiC;IACjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA+B;IAExD,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;QACjB,WAAW;QACX,eAAe;IACjB,GAAG;QAAC;QAAsB;KAAe;IAEzC,8CAA8C;IAC9C,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,IAAI,aAAa,CAAC,SAAS;YACzB,QAAQ,GAAG,CAAC,mCAAmC,WAAW,YAAY;YACtE;QACF;QAEA,QAAQ,GAAG,CAAC,sCAAsC,cAAc;QAChE,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE;gBACpC,MAAM,cAAc;gBACpB,UAAU;gBACV,QAAQ,WAAW,QAAQ,YAAY;YACzC;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC1D,iBAAiB,CAAA,OAAQ;2BAAI;2BAAS,OAAO,IAAI,CAAC,aAAa;qBAAC;gBAChE,WAAW,OAAO,IAAI,CAAC,OAAO;gBAC9B,eAAe,CAAA,OAAQ,OAAO;gBAC9B,QAAQ,GAAG,CAAC,UAAU,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC1D,OAAO;gBACL,WAAW;gBACX,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAa;QAAS;QAAW;KAAO;IAE5C,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,WAAW,WAAW;YACjD,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,UAAU;gBAC9B,YAAY,OAAO,GAAG;YACxB;YACA;QACF;QAEA,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,UAAU;QAChC;QAEA,YAAY,OAAO,GAAG,IAAI,qBACxB,CAAC;YACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,aAAa,WAAW,cAAc,MAAM,GAAG,GAAG;gBAClF,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,YAAY,OAAO,CAAC,OAAO,CAAC,YAAY,OAAO;QAE/C,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,UAAU;gBAC9B,YAAY,OAAO,GAAG;YACxB;QACF;IACF,GAAG;QAAC;QAAuB;QAAS;QAAW,cAAc,MAAM;KAAC;IAEpE,yCAAyC;IACzC,MAAM,2BAA2B,CAAC;QAChC,wBAAwB;QACxB,2CAA2C;QAC3C,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,oBAAoB,EAAE,GAAG,sBAAsB;IAE1E;IAEA,MAAM,+BAA+B,CAAC;QACpC,IAAI,eAAe,OAAO,OAAO;QAEjC,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,OAAO,cAAc,MAAM,CAAC,CAAA;YAC1B,MAAM,mBAAmB,IAAI,KAAK,aAAa,UAAU;YAEzD,OAAQ;gBACN,KAAK;oBACH,OAAO,iBAAiB,YAAY,OAAO,MAAM,YAAY;gBAC/D,KAAK;oBACH,OAAO,iBAAiB,YAAY,OAAO,UAAU,YAAY;gBACnE,KAAK;oBACH,IAAI,CAAC,YAAY,OAAO;oBACxB,MAAM,eAAe,IAAI,KAAK;oBAC9B,OAAO,iBAAiB,YAAY,OAAO,aAAa,YAAY;gBACtE;oBACE,OAAO;YACX;QACF;IACF;IAEA,MAAM,wBAAwB,6BAA6B,eAAe,MAAM,CAAC,CAAA;QAC/E,IAAI,WAAW,OAAO,OAAO;QAC7B,OAAO,aAAa,MAAM,KAAK;IACjC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAA+D;QAAC;QAAO;QAAW;QAAa;KAAW;IAEhH,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAG,WAAU;8BAA0C;;;;;;8BACxD,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;;;;;;;IAKjD;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;4CAEC,SAAS,IAAM,UAAU;4CACzB,WAAW,CAAC,2HAA2H,EACrI,WAAW,MACP,kCACA,kFACJ;;gDAED,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;gDACxC,6BAA6B,eAAe,MAAM,CAAC,CAAA,IAAK,QAAQ,QAAQ,OAAO,EAAE,MAAM,KAAK,KAAK,MAAM,GAAG,mBACzG,8OAAC;oDAAK,WAAW,CAAC,sDAAsD,EACtE,WAAW,MACP,8BACA,gCACJ;8DACC,6BAA6B,eAAe,MAAM,CAAC,CAAA,IAAK,QAAQ,QAAQ,OAAO,EAAE,MAAM,KAAK,KAAK,MAAM;;;;;;;2CAfvG;;;;;;;;;;8CAuBX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC;gDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAC5B,cAAc;gDACd,IAAI,UAAU,UAAU;oDACtB,cAAc;gDAChB;4CACF;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;wCAGxB,eAAe,0BACd,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,cAAc,aAAa,MAAM;;;;;;8DAGpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,aAAa,YAAY,IAAI,CAAC,SAAS,EAAE,aAAa,cAAc,IAAI,OAAO;;;;;;8EAElF,8OAAC;oEAAK,WAAW,CAAC,iFAAiF,EAAE,oBAAoB,aAAa,MAAM,GAAG;8EAC5I,cAAc,aAAa,MAAM;;;;;;;;;;;;sEAItC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAAW;wEAAe,aAAa,YAAY;;;;;;;8EACnE,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;8EACnC,8OAAC;8EAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,UAAU;;;;;;8EACjD,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;8EACnC,8OAAC;oEAAK,WAAU;8EAAoB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAK5E,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,SAAS,IAAM,wBAAwB;oDACvC,WAAW,CAAC,0LAA0L,EACpM,aAAa,MAAM,KAAK,YACpB,6IACA,aAAa,MAAM,KAAK,cACxB,2IACA,+IACJ;8DAED,aAAa,MAAM,KAAK,0BACvB;;0EACE,8OAAC,+MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;uEAE5B,aAAa,MAAM,KAAK,4BAC1B;;0EACE,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;qFAG9B;;0EACE,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;;8DAMlC,8OAAC;oDACC,SAAS,IAAM,wBAAwB;oDACvC,WAAU;;sEAEV,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,8OAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;+BArE7B,aAAa,EAAE;;;;;;;;;;oBA8EzB,yBACC,8OAAC;wBAAI,KAAK;wBAAa,WAAU;kCAC9B,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;iDAGR,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;oBAK7C,sBAAsB,MAAM,KAAK,KAAK,WAAW,uBAChD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAyB;gCACA;gCAAO;;;;;;;;;;;;;;;;;;YAOlD,sCACC,8OAAC,wJAAA,CAAA,oBAAiB;gBAChB,cAAc;gBACd,SAAS,IAAM,wBAAwB;gBACvC,sBAAsB;gBACtB,YAAY;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/quota-warning-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { X, AlertTriangle, Phone, MessageCircle, Clock, CheckCircle } from 'lucide-react'\nimport { QuotaInfo } from '@/lib/types'\n\ninterface QuotaWarningModalProps {\n  isOpen: boolean\n  onClose: () => void\n  quotaInfo: QuotaInfo\n  onContactFounder: () => void\n  isRequesting?: boolean\n  hasRequested?: boolean\n}\n\nexport function QuotaWarningModal({ \n  isOpen, \n  onClose, \n  quotaInfo, \n  onContactFounder, \n  isRequesting = false,\n  hasRequested = false \n}: QuotaWarningModalProps) {\n  if (!isOpen) return null\n\n  const isOverLimit = quotaInfo.quota_percentage >= 95\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden\">\n        {/* Header */}\n        <div className={`p-6 text-white relative ${\n          isOverLimit \n            ? 'bg-gradient-to-r from-red-500 to-red-600' \n            : 'bg-gradient-to-r from-orange-500 to-amber-600'\n        }`}>\n          <button\n            onClick={onClose}\n            className=\"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n          \n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <AlertTriangle className=\"w-8 h-8 text-white\" />\n            </div>\n            <h2 className=\"text-2xl font-bold mb-2\">\n              {isOverLimit ? 'Quota Limit Reached!' : 'Quota Warning'}\n            </h2>\n            <p className=\"text-orange-100\">\n              {isOverLimit \n                ? 'You have reached your consultation limit' \n                : 'You are approaching your consultation limit'\n              }\n            </p>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Quota Status */}\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 p-4 rounded-lg border border-gray-200 mb-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gray-900 mb-1\">\n                {quotaInfo.quota_used} / {quotaInfo.monthly_quota}\n              </div>\n              <div className=\"text-sm text-gray-600 mb-3\">Consultations Used</div>\n              \n              <div className=\"w-full bg-gray-200 rounded-full h-3 mb-2\">\n                <div\n                  className={`h-3 rounded-full transition-all duration-300 ${\n                    isOverLimit ? 'bg-red-500' : 'bg-orange-500'\n                  }`}\n                  style={{ width: `${Math.min(quotaInfo.quota_percentage, 100)}%` }}\n                />\n              </div>\n              \n              <div className={`text-sm font-medium ${\n                isOverLimit ? 'text-red-600' : 'text-orange-600'\n              }`}>\n                {quotaInfo.quota_percentage.toFixed(0)}% Used\n              </div>\n            </div>\n          </div>\n\n          {/* Message */}\n          <div className=\"text-center mb-6\">\n            <p className=\"text-gray-700 leading-relaxed mb-4\">\n              {isOverLimit \n                ? 'To continue using AI consultations, please contact our founder to upgrade your plan or increase your quota.'\n                : 'You have only a few consultations remaining. Consider contacting our founder to discuss upgrading your plan.'\n              }\n            </p>\n            \n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 mb-2\">\n                <Phone className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm font-medium text-blue-800\">Contact Founder</span>\n              </div>\n              <div className=\"text-lg font-bold text-blue-900 mb-1\">\n                +91 8921628177\n              </div>\n              <p className=\"text-xs text-blue-700\">\n                Available Mon-Sat, 9 AM - 8 PM IST\n              </p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"space-y-3\">\n            {hasRequested ? (\n              <div className=\"flex items-center justify-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg\">\n                <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                <span className=\"text-green-800 font-medium\">Request Sent Successfully!</span>\n              </div>\n            ) : (\n              <button\n                onClick={onContactFounder}\n                disabled={isRequesting}\n                className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2\"\n              >\n                {isRequesting ? (\n                  <>\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span>Sending Request...</span>\n                  </>\n                ) : (\n                  <>\n                    <MessageCircle className=\"w-4 h-4\" />\n                    <span>Request Callback</span>\n                  </>\n                )}\n              </button>\n            )}\n\n            <a\n              href=\"tel:+918921628177\"\n              className=\"w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2\"\n            >\n              <Phone className=\"w-4 h-4\" />\n              <span>Call Now</span>\n            </a>\n\n            {!isOverLimit && (\n              <button\n                onClick={onClose}\n                className=\"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-200\"\n              >\n                Continue with Trial\n              </button>\n            )}\n          </div>\n\n          {/* Reset Info */}\n          <div className=\"mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200\">\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <Clock className=\"w-4 h-4\" />\n              <span>\n                Quota resets in {quotaInfo.days_until_reset} day{quotaInfo.days_until_reset !== 1 ? 's' : ''}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAcO,SAAS,kBAAkB,EAChC,MAAM,EACN,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,eAAe,KAAK,EACpB,eAAe,KAAK,EACG;IACvB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc,UAAU,gBAAgB,IAAI;IAElD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,wBAAwB,EACvC,cACI,6CACA,iDACJ;;sCACA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;oCAAG,WAAU;8CACX,cAAc,yBAAyB;;;;;;8CAE1C,8OAAC;oCAAE,WAAU;8CACV,cACG,6CACA;;;;;;;;;;;;;;;;;;8BAOV,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,UAAU;4CAAC;4CAAI,UAAU,aAAa;;;;;;;kDAEnD,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAW,CAAC,6CAA6C,EACvD,cAAc,eAAe,iBAC7B;4CACF,OAAO;gDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,gBAAgB,EAAE,KAAK,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAW,CAAC,oBAAoB,EACnC,cAAc,iBAAiB,mBAC/B;;4CACC,UAAU,gBAAgB,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,cACG,gHACA;;;;;;8CAIN,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;4BAAI,WAAU;;gCACZ,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;yDAG/C,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;qEAGR;;0DACE,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;0DAAK;;;;;;;;;;;;;8CAMd,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;gCAGP,CAAC,6BACA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;4CAAK;4CACa,UAAU,gBAAgB;4CAAC;4CAAK,UAAU,gBAAgB,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1G", "debugId": null}}, {"offset": {"line": 2970, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/contact-requests.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport interface ContactRequest {\n  id: string\n  doctor_id: string\n  doctor_name: string\n  doctor_email: string\n  clinic_name: string\n  phone_number: string\n  request_type: string\n  message: string | null\n  status: string\n  contacted_at: string | null\n  resolved_at: string | null\n  created_at: string\n  updated_at: string\n}\n\nexport async function createContactRequest(\n  doctorId: string,\n  message?: string,\n  subject?: string\n): Promise<ApiResponse<string>> {\n  try {\n    console.log('Creating contact request for doctorId:', doctorId)\n    const supabase = await createClient()\n\n    // Get doctor information first\n    console.log('Fetching doctor info...')\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select('name, email, clinic_name, phone, quota_used, monthly_quota')\n      .eq('id', doctorId)\n      .single()\n\n    console.log('Doctor fetch result:', { doctor, doctorError })\n\n    if (doctor<PERSON>rror || !doctor) {\n      console.error('Doctor not found:', { doctorId, doctorError })\n      return { success: false, error: `Doctor not found: ${doctorError?.message || 'No doctor data'}` }\n    }\n\n    // Simple insert without checking duplicates for now\n    const insertData = {\n      doctor_id: doctorId,\n      doctor_name: doctor.name,\n      doctor_email: doctor.email,\n      clinic_name: doctor.clinic_name || '',\n      phone_number: doctor.phone || '',\n      current_quota_used: doctor.quota_used || 0,\n      monthly_quota: doctor.monthly_quota || 0,\n      request_type: 'general_contact',\n      message: message || 'Contact request from dashboard',\n      subject: subject || 'general'\n    }\n    \n    console.log('Creating contact request with data:', insertData)\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .insert(insertData)\n      .select('id')\n      .single()\n\n    console.log('Insert result:', { data, error })\n\n    if (error) {\n      console.error('Failed to create contact request:', error)\n      return { success: false, error: `Database error: ${error.message}` }\n    }\n\n    // Force revalidation of admin paths\n    revalidatePath('/admin/dashboard')\n    revalidatePath('/admin')\n    \n    console.log('Contact request created successfully with ID:', data.id)\n    \n    return { success: true, data: data.id }\n  } catch (error) {\n    console.error('Unexpected error creating contact request:', error)\n    return { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\nexport async function getContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Database error fetching contact requests:', error)\n      return { success: false, error: 'Failed to fetch contact requests' }\n    }\n\n    // Only log if this is called with explicit debug flag or if there are new requests\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Fetched contact requests:', {\n        count: data?.length || 0,\n        pending: data?.filter(r => r.status === 'pending').length || 0\n      })\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getPendingContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch pending contact requests' }\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching pending contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateContactRequestStatus(\n  requestId: string,\n  status: 'pending' | 'contacted' | 'resolved'\n): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { error } = await supabase\n      .from('contact_requests')\n      .update({ status })\n      .eq('id', requestId)\n\n    if (error) {\n      return { success: false, error: 'Failed to update contact request status' }\n    }\n\n    revalidatePath('/admin/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error updating contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getContactRequestsCount(): Promise<ApiResponse<{\n  total: number;\n  pending: number;\n  contacted: number;\n  resolved: number;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('status')\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch contact requests count' }\n    }\n\n    const counts = {\n      total: data?.length || 0,\n      pending: data?.filter(r => r.status === 'pending').length || 0,\n      contacted: data?.filter(r => r.status === 'contacted').length || 0,\n      resolved: data?.filter(r => r.status === 'resolved').length || 0\n    }\n\n    return { success: true, data: counts }\n  } catch (error) {\n    console.error('Error fetching contact requests count:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function hasActiveContactRequest(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('id')\n      .eq('doctor_id', doctorId)\n      .eq('status', 'pending')\n      .single()\n\n    if (error && error.code !== 'PGRST116') { // PGRST116 is \"not found\" error\n      return { success: false, error: 'Failed to check contact request status' }\n    }\n\n    return { success: true, data: !!data }\n  } catch (error) {\n    console.error('Error checking contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;IAsBsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/contact-requests.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport interface ContactRequest {\n  id: string\n  doctor_id: string\n  doctor_name: string\n  doctor_email: string\n  clinic_name: string\n  phone_number: string\n  request_type: string\n  message: string | null\n  status: string\n  contacted_at: string | null\n  resolved_at: string | null\n  created_at: string\n  updated_at: string\n}\n\nexport async function createContactRequest(\n  doctorId: string,\n  message?: string,\n  subject?: string\n): Promise<ApiResponse<string>> {\n  try {\n    console.log('Creating contact request for doctorId:', doctorId)\n    const supabase = await createClient()\n\n    // Get doctor information first\n    console.log('Fetching doctor info...')\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select('name, email, clinic_name, phone, quota_used, monthly_quota')\n      .eq('id', doctorId)\n      .single()\n\n    console.log('Doctor fetch result:', { doctor, doctorError })\n\n    if (doctor<PERSON>rror || !doctor) {\n      console.error('Doctor not found:', { doctorId, doctorError })\n      return { success: false, error: `Doctor not found: ${doctorError?.message || 'No doctor data'}` }\n    }\n\n    // Simple insert without checking duplicates for now\n    const insertData = {\n      doctor_id: doctorId,\n      doctor_name: doctor.name,\n      doctor_email: doctor.email,\n      clinic_name: doctor.clinic_name || '',\n      phone_number: doctor.phone || '',\n      current_quota_used: doctor.quota_used || 0,\n      monthly_quota: doctor.monthly_quota || 0,\n      request_type: 'general_contact',\n      message: message || 'Contact request from dashboard',\n      subject: subject || 'general'\n    }\n    \n    console.log('Creating contact request with data:', insertData)\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .insert(insertData)\n      .select('id')\n      .single()\n\n    console.log('Insert result:', { data, error })\n\n    if (error) {\n      console.error('Failed to create contact request:', error)\n      return { success: false, error: `Database error: ${error.message}` }\n    }\n\n    // Force revalidation of admin paths\n    revalidatePath('/admin/dashboard')\n    revalidatePath('/admin')\n    \n    console.log('Contact request created successfully with ID:', data.id)\n    \n    return { success: true, data: data.id }\n  } catch (error) {\n    console.error('Unexpected error creating contact request:', error)\n    return { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\nexport async function getContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Database error fetching contact requests:', error)\n      return { success: false, error: 'Failed to fetch contact requests' }\n    }\n\n    // Only log if this is called with explicit debug flag or if there are new requests\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Fetched contact requests:', {\n        count: data?.length || 0,\n        pending: data?.filter(r => r.status === 'pending').length || 0\n      })\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getPendingContactRequests(): Promise<ApiResponse<ContactRequest[]>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('*')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch pending contact requests' }\n    }\n\n    return { success: true, data: data || [] }\n  } catch (error) {\n    console.error('Error fetching pending contact requests:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateContactRequestStatus(\n  requestId: string,\n  status: 'pending' | 'contacted' | 'resolved'\n): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { error } = await supabase\n      .from('contact_requests')\n      .update({ status })\n      .eq('id', requestId)\n\n    if (error) {\n      return { success: false, error: 'Failed to update contact request status' }\n    }\n\n    revalidatePath('/admin/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error updating contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getContactRequestsCount(): Promise<ApiResponse<{\n  total: number;\n  pending: number;\n  contacted: number;\n  resolved: number;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('status')\n\n    if (error) {\n      return { success: false, error: 'Failed to fetch contact requests count' }\n    }\n\n    const counts = {\n      total: data?.length || 0,\n      pending: data?.filter(r => r.status === 'pending').length || 0,\n      contacted: data?.filter(r => r.status === 'contacted').length || 0,\n      resolved: data?.filter(r => r.status === 'resolved').length || 0\n    }\n\n    return { success: true, data: counts }\n  } catch (error) {\n    console.error('Error fetching contact requests count:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function hasActiveContactRequest(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    const { data, error } = await supabase\n      .from('contact_requests')\n      .select('id')\n      .eq('doctor_id', doctorId)\n      .eq('status', 'pending')\n      .single()\n\n    if (error && error.code !== 'PGRST116') { // PGRST116 is \"not found\" error\n      return { success: false, error: 'Failed to check contact request status' }\n    }\n\n    return { success: true, data: !!data }\n  } catch (error) {\n    console.error('Error checking contact request status:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;IAiMsB,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2996, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/quota-card.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Zap, Calendar, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react'\nimport { QuotaInfo } from '@/lib/types'\nimport { QuotaWarningModal } from './quota-warning-modal'\nimport { createContactRequest, hasActiveContactRequest } from '@/lib/actions/contact-requests'\n\ninterface QuotaCardProps {\n  quota: QuotaInfo\n  doctorId: string\n}\n\nexport function QuotaCard({ quota, doctorId }: QuotaCardProps) {\n  const [showWarningModal, setShowWarningModal] = useState(false)\n  const [isRequesting, setIsRequesting] = useState(false)\n  const [hasRequested, setHasRequested] = useState(false)\n\n  useEffect(() => {\n    // Show warning modal when approaching or exceeding limits\n    if (quota.quota_percentage >= 80) {\n      setShowWarningModal(true)\n    }\n\n    // Check if user has already made a contact request\n    const checkContactRequest = async () => {\n      const result = await hasActiveContactRequest(doctorId)\n      if (result.success && result.data) {\n        setHasRequested(true)\n      }\n    }\n    checkContactRequest()\n  }, [quota.quota_percentage, doctorId])\n\n  const handleContactFounder = async () => {\n    setIsRequesting(true)\n    try {\n      const result = await createContactRequest(doctorId, `Quota upgrade request - Currently at ${quota.quota_percentage}% (${quota.quota_used}/${quota.monthly_quota} consultations used)`)\n      if (result.success) {\n        setHasRequested(true)\n      }\n    } catch (error) {\n      console.error('Error creating contact request:', error)\n    } finally {\n      setIsRequesting(false)\n    }\n  }\n\n  const getQuotaColor = () => {\n    if (quota.quota_percentage >= 90) return 'text-red-600'\n    if (quota.quota_percentage >= 70) return 'text-orange-600'\n    return 'text-emerald-600'\n  }\n\n  const getQuotaBgColor = () => {\n    if (quota.quota_percentage >= 90) return 'bg-red-100'\n    if (quota.quota_percentage >= 70) return 'bg-orange-100'\n    return 'bg-emerald-100'\n  }\n\n  const getQuotaIcon = () => {\n    if (quota.quota_percentage >= 90) return AlertTriangle\n    if (quota.quota_percentage >= 70) return Zap\n    return CheckCircle\n  }\n\n  const getProgressBarColor = () => {\n    if (quota.quota_percentage >= 90) return 'bg-red-500'\n    if (quota.quota_percentage >= 70) return 'bg-orange-500'\n    return 'bg-emerald-500'\n  }\n\n  const QuotaIcon = getQuotaIcon()\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-sm font-medium text-slate-800\">Consultations</h3>\n        <div className={`w-6 h-6 rounded-lg ${getQuotaBgColor()} flex items-center justify-center shadow-md`}>\n          <QuotaIcon className={`w-4 h-4 ${getQuotaColor()}`} />\n        </div>\n      </div>\n\n      {/* Main Quota Display */}\n      <div className=\"mb-4 flex-1 space-y-3\">\n        <div className=\"text-center bg-gradient-to-br from-slate-50 to-orange-50 rounded-lg p-3 sm:p-4\">\n          <div className=\"text-xs sm:text-sm text-slate-600 mb-1\">Monthly Usage</div>\n          <div className=\"text-base sm:text-lg font-bold text-slate-800\">\n            {quota.quota_used} / {quota.monthly_quota}\n          </div>\n          <div className=\"w-full bg-orange-100 rounded-full h-2 sm:h-2.5 mt-2\">\n            <div\n              className={`h-2 sm:h-2.5 rounded-full transition-all duration-300 ${getProgressBarColor()}`}\n              style={{ width: `${Math.min(quota.quota_percentage, 100)}%` }}\n            />\n          </div>\n          <div className=\"mt-1\">\n            <span className={`text-sm sm:text-base font-bold ${getQuotaColor()}`}>\n              {quota.quota_percentage}%\n            </span>\n          </div>\n        </div>\n\n        {/* Split Info Grid */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-2 sm:p-3 text-center\">\n            <TrendingUp className=\"w-4 h-4 sm:w-5 sm:h-5 text-teal-600 mx-auto mb-1\" />\n            <div className=\"text-xs sm:text-sm text-slate-600\">Remaining</div>\n            <div className=\"text-sm sm:text-base font-bold text-slate-800\">{quota.quota_remaining}</div>\n          </div>\n          \n          <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-2 sm:p-3 text-center\">\n            <Calendar className=\"w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mx-auto mb-1\" />\n            <div className=\"text-xs sm:text-sm text-slate-600\">Resets in</div>\n            <div className=\"text-sm sm:text-base font-bold text-slate-800\">{quota.days_until_reset} day{quota.days_until_reset !== 1 ? 's' : ''}</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Warning Messages */}\n      {quota.quota_percentage >= 90 && (\n        <div className=\"p-2 bg-red-50 border border-red-200 rounded\">\n          <div className=\"flex items-start\">\n            <AlertTriangle className=\"w-3 h-3 text-red-500 mt-0.5 mr-1 flex-shrink-0\" />\n            <div className=\"text-xs text-red-700\">\n              <p className=\"font-medium\">Quota almost exhausted!</p>\n              <p>Contact admin to increase limit.</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {quota.quota_percentage >= 70 && quota.quota_percentage < 90 && (\n        <div className=\"p-2 bg-orange-50 border border-orange-200 rounded\">\n          <div className=\"flex items-start\">\n            <Zap className=\"w-3 h-3 text-orange-500 mt-0.5 mr-1 flex-shrink-0\" />\n            <div className=\"text-xs text-orange-700\">\n              <p className=\"font-medium\">High usage detected</p>\n              <p>Monitor AI generation usage.</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quota Warning Modal */}\n      <QuotaWarningModal\n        isOpen={showWarningModal}\n        onClose={() => setShowWarningModal(false)}\n        quotaInfo={quota}\n        onContactFounder={handleContactFounder}\n        isRequesting={isRequesting}\n        hasRequested={hasRequested}\n      />\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AANA;;;;;;AAaO,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAkB;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,MAAM,gBAAgB,IAAI,IAAI;YAChC,oBAAoB;QACtB;QAEA,mDAAmD;QACnD,MAAM,sBAAsB;YAC1B,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,gBAAgB;YAClB;QACF;QACA;IACF,GAAG;QAAC,MAAM,gBAAgB;QAAE;KAAS;IAErC,MAAM,uBAAuB;QAC3B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,CAAC,qCAAqC,EAAE,MAAM,gBAAgB,CAAC,GAAG,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,aAAa,CAAC,oBAAoB,CAAC;YACrL,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO,wNAAA,CAAA,gBAAa;QACtD,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO,gMAAA,CAAA,MAAG;QAC5C,OAAO,2NAAA,CAAA,cAAW;IACpB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,IAAI,MAAM,gBAAgB,IAAI,IAAI,OAAO;QACzC,OAAO;IACT;IAEA,MAAM,YAAY;IAElB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAW,CAAC,mBAAmB,EAAE,kBAAkB,2CAA2C,CAAC;kCAClG,cAAA,8OAAC;4BAAU,WAAW,CAAC,QAAQ,EAAE,iBAAiB;;;;;;;;;;;;;;;;;0BAKtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAyC;;;;;;0CACxD,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,UAAU;oCAAC;oCAAI,MAAM,aAAa;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,sDAAsD,EAAE,uBAAuB;oCAC3F,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,gBAAgB,EAAE,KAAK,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW,CAAC,+BAA+B,EAAE,iBAAiB;;wCACjE,MAAM,gBAAgB;wCAAC;;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDAAiD,MAAM,eAAe;;;;;;;;;;;;0CAGvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;;4CAAiD,MAAM,gBAAgB;4CAAC;4CAAK,MAAM,gBAAgB,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;YAMtI,MAAM,gBAAgB,IAAI,oBACzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;YAMV,MAAM,gBAAgB,IAAI,MAAM,MAAM,gBAAgB,GAAG,oBACxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC,4JAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,WAAW;gBACX,kBAAkB;gBAClB,cAAc;gBACd,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 3373, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/referrals.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ReferralInfo, ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {\n  try {\n    const supabase = await createClient()\n\n    // Get doctor's referral information\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select(`\n        referral_code,\n        total_referrals,\n        successful_referrals,\n        referral_discount_earned,\n        referred_by\n      `)\n      .eq('id', doctorId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return { success: false, error: 'Failed to fetch referral information' }\n    }\n\n    // Get pending referrals count\n    const { count: pendingCount } = await supabase\n      .from('referral_analytics')\n      .select('*', { count: 'exact', head: true })\n      .eq('referrer_id', doctorId)\n      .eq('status', 'pending')\n\n    // Get recent referrals\n    const { data: recentReferrals, error: referralsError } = await supabase\n      .from('referral_analytics')\n      .select(`\n        id,\n        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),\n        signup_date,\n        conversion_date,\n        status\n      `)\n      .eq('referrer_id', doctorId)\n      .order('created_at', { ascending: false })\n      .limit(10)\n\n    if (referralsError) {\n      return { success: false, error: 'Failed to fetch recent referrals' }\n    }\n\n    // Get referrer info separately if exists\n    let referredBy = null\n    if (doctor.referred_by) {\n      const { data: referrer } = await supabase\n        .from('doctors')\n        .select('name, referral_code')\n        .eq('id', doctor.referred_by)\n        .single()\n      \n      if (referrer) {\n        referredBy = {\n          name: referrer.name,\n          referral_code: referrer.referral_code || ''\n        }\n      }\n    }\n\n    const referralInfo: ReferralInfo = {\n      referral_code: doctor.referral_code || '',\n      total_referrals: doctor.total_referrals || 0,\n      successful_referrals: doctor.successful_referrals || 0,\n      pending_referrals: pendingCount || 0,\n      discount_earned: doctor.referral_discount_earned || 0,\n      referred_by: referredBy,\n      recent_referrals: (recentReferrals || []).map(ref => ({\n        id: ref.id,\n        name: ref.referred_doctor?.name || 'Unknown',\n        email: ref.referred_doctor?.email || 'Unknown',\n        signup_date: ref.signup_date,\n        conversion_date: ref.conversion_date,\n        status: ref.status as 'pending' | 'converted' | 'expired'\n      }))\n    }\n\n    return { success: true, data: referralInfo }\n  } catch (error) {\n    console.error('Error fetching referral info:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('referral_code')\n      .eq('id', doctorId)\n      .single()\n\n    if (error || !doctor?.referral_code) {\n      return { success: false, error: 'Failed to fetch referral code' }\n    }\n\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'\n    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`\n\n    return { success: true, data: referralLink }\n  } catch (error) {\n    console.error('Error generating referral link:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Find the referrer\n    const { data: referrer, error: referrerError } = await supabase\n      .from('doctors')\n      .select('id, name')\n      .eq('referral_code', referralCode)\n      .single()\n\n    if (referrerError || !referrer) {\n      return { success: false, error: 'Invalid referral code' }\n    }\n\n    // Update the new doctor with referrer information\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ referred_by: referrer.id })\n      .eq('id', newDoctorId)\n\n    if (updateError) {\n      return { success: false, error: 'Failed to process referral signup' }\n    }\n\n    // Create referral analytics record\n    const { error: analyticsError } = await supabase\n      .from('referral_analytics')\n      .insert({\n        referrer_id: referrer.id,\n        referred_doctor_id: newDoctorId,\n        referral_code: referralCode,\n        status: 'pending'\n      })\n\n    if (analyticsError) {\n      console.error('Failed to create analytics record:', analyticsError)\n      // Don't fail the signup for this\n    }\n\n    // Update referrer's total referrals count\n    const { data: currentReferrer } = await supabase\n      .from('doctors')\n      .select('total_referrals')\n      .eq('id', referrer.id)\n      .single()\n\n    if (currentReferrer) {\n      const { error: countError } = await supabase\n        .from('doctors')\n        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })\n        .eq('id', referrer.id)\n\n      if (countError) {\n        console.error('Failed to update referral count:', countError)\n      }\n    }\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error processing referral signup:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Call the database function to handle conversion\n    const { data, error } = await supabase.rpc('handle_referral_conversion', {\n      referred_doctor_uuid: doctorId\n    })\n\n    if (error) {\n      console.error('Error marking referral conversion:', error)\n      return { success: false, error: 'Failed to process referral conversion' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/admin/dashboard')\n\n    return { success: true, data: data || false }\n  } catch (error) {\n    console.error('Error marking referral conversion:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getAdminReferralStats(): Promise<ApiResponse<{\n  total_referrals: number;\n  successful_conversions: number;\n  pending_referrals: number;\n  total_discount_earned: number;\n  top_referrers: Array<{\n    id: string;\n    name: string;\n    referral_code: string;\n    successful_referrals: number;\n    discount_earned: number;\n  }>;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    // Get overall stats\n    const { data: totalStats, error: statsError } = await supabase\n      .from('referral_analytics')\n      .select('status, discount_earned')\n\n    if (statsError) {\n      return { success: false, error: 'Failed to fetch referral statistics' }\n    }\n\n    const totalReferrals = totalStats?.length || 0\n    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0\n    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0\n    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0\n\n    // Get top referrers\n    const { data: topReferrers, error: referrersError } = await supabase\n      .from('doctors')\n      .select('id, name, referral_code, successful_referrals, referral_discount_earned')\n      .gt('successful_referrals', 0)\n      .order('successful_referrals', { ascending: false })\n      .limit(10)\n\n    if (referrersError) {\n      return { success: false, error: 'Failed to fetch top referrers' }\n    }\n\n    return {\n      success: true,\n      data: {\n        total_referrals: totalReferrals,\n        successful_conversions: successfulConversions,\n        pending_referrals: pendingReferrals,\n        total_discount_earned: totalDiscountEarned,\n        top_referrers: (topReferrers || []).map(r => ({\n          id: r.id,\n          name: r.name,\n          referral_code: r.referral_code || '',\n          successful_referrals: r.successful_referrals || 0,\n          discount_earned: r.referral_discount_earned || 0\n        }))\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching admin referral stats:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function validateReferralCode(referralCode: string): Promise<ApiResponse<{\n  valid: boolean;\n  referrer_name?: string;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: referrer, error } = await supabase\n      .from('doctors')\n      .select('name, approved')\n      .eq('referral_code', referralCode)\n      .eq('approved', true)\n      .single()\n\n    if (error || !referrer) {\n      return { \n        success: true, \n        data: { valid: false } \n      }\n    }\n\n    return {\n      success: true,\n      data: {\n        valid: true,\n        referrer_name: referrer.name\n      }\n    }\n  } catch (error) {\n    console.error('Error validating referral code:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;IAMsB,kBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3386, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/referrals.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ReferralInfo, ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {\n  try {\n    const supabase = await createClient()\n\n    // Get doctor's referral information\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select(`\n        referral_code,\n        total_referrals,\n        successful_referrals,\n        referral_discount_earned,\n        referred_by\n      `)\n      .eq('id', doctorId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return { success: false, error: 'Failed to fetch referral information' }\n    }\n\n    // Get pending referrals count\n    const { count: pendingCount } = await supabase\n      .from('referral_analytics')\n      .select('*', { count: 'exact', head: true })\n      .eq('referrer_id', doctorId)\n      .eq('status', 'pending')\n\n    // Get recent referrals\n    const { data: recentReferrals, error: referralsError } = await supabase\n      .from('referral_analytics')\n      .select(`\n        id,\n        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),\n        signup_date,\n        conversion_date,\n        status\n      `)\n      .eq('referrer_id', doctorId)\n      .order('created_at', { ascending: false })\n      .limit(10)\n\n    if (referralsError) {\n      return { success: false, error: 'Failed to fetch recent referrals' }\n    }\n\n    // Get referrer info separately if exists\n    let referredBy = null\n    if (doctor.referred_by) {\n      const { data: referrer } = await supabase\n        .from('doctors')\n        .select('name, referral_code')\n        .eq('id', doctor.referred_by)\n        .single()\n      \n      if (referrer) {\n        referredBy = {\n          name: referrer.name,\n          referral_code: referrer.referral_code || ''\n        }\n      }\n    }\n\n    const referralInfo: ReferralInfo = {\n      referral_code: doctor.referral_code || '',\n      total_referrals: doctor.total_referrals || 0,\n      successful_referrals: doctor.successful_referrals || 0,\n      pending_referrals: pendingCount || 0,\n      discount_earned: doctor.referral_discount_earned || 0,\n      referred_by: referredBy,\n      recent_referrals: (recentReferrals || []).map(ref => ({\n        id: ref.id,\n        name: ref.referred_doctor?.name || 'Unknown',\n        email: ref.referred_doctor?.email || 'Unknown',\n        signup_date: ref.signup_date,\n        conversion_date: ref.conversion_date,\n        status: ref.status as 'pending' | 'converted' | 'expired'\n      }))\n    }\n\n    return { success: true, data: referralInfo }\n  } catch (error) {\n    console.error('Error fetching referral info:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('referral_code')\n      .eq('id', doctorId)\n      .single()\n\n    if (error || !doctor?.referral_code) {\n      return { success: false, error: 'Failed to fetch referral code' }\n    }\n\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'\n    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`\n\n    return { success: true, data: referralLink }\n  } catch (error) {\n    console.error('Error generating referral link:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Find the referrer\n    const { data: referrer, error: referrerError } = await supabase\n      .from('doctors')\n      .select('id, name')\n      .eq('referral_code', referralCode)\n      .single()\n\n    if (referrerError || !referrer) {\n      return { success: false, error: 'Invalid referral code' }\n    }\n\n    // Update the new doctor with referrer information\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ referred_by: referrer.id })\n      .eq('id', newDoctorId)\n\n    if (updateError) {\n      return { success: false, error: 'Failed to process referral signup' }\n    }\n\n    // Create referral analytics record\n    const { error: analyticsError } = await supabase\n      .from('referral_analytics')\n      .insert({\n        referrer_id: referrer.id,\n        referred_doctor_id: newDoctorId,\n        referral_code: referralCode,\n        status: 'pending'\n      })\n\n    if (analyticsError) {\n      console.error('Failed to create analytics record:', analyticsError)\n      // Don't fail the signup for this\n    }\n\n    // Update referrer's total referrals count\n    const { data: currentReferrer } = await supabase\n      .from('doctors')\n      .select('total_referrals')\n      .eq('id', referrer.id)\n      .single()\n\n    if (currentReferrer) {\n      const { error: countError } = await supabase\n        .from('doctors')\n        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })\n        .eq('id', referrer.id)\n\n      if (countError) {\n        console.error('Failed to update referral count:', countError)\n      }\n    }\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error processing referral signup:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Call the database function to handle conversion\n    const { data, error } = await supabase.rpc('handle_referral_conversion', {\n      referred_doctor_uuid: doctorId\n    })\n\n    if (error) {\n      console.error('Error marking referral conversion:', error)\n      return { success: false, error: 'Failed to process referral conversion' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/admin/dashboard')\n\n    return { success: true, data: data || false }\n  } catch (error) {\n    console.error('Error marking referral conversion:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getAdminReferralStats(): Promise<ApiResponse<{\n  total_referrals: number;\n  successful_conversions: number;\n  pending_referrals: number;\n  total_discount_earned: number;\n  top_referrers: Array<{\n    id: string;\n    name: string;\n    referral_code: string;\n    successful_referrals: number;\n    discount_earned: number;\n  }>;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    // Get overall stats\n    const { data: totalStats, error: statsError } = await supabase\n      .from('referral_analytics')\n      .select('status, discount_earned')\n\n    if (statsError) {\n      return { success: false, error: 'Failed to fetch referral statistics' }\n    }\n\n    const totalReferrals = totalStats?.length || 0\n    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0\n    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0\n    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0\n\n    // Get top referrers\n    const { data: topReferrers, error: referrersError } = await supabase\n      .from('doctors')\n      .select('id, name, referral_code, successful_referrals, referral_discount_earned')\n      .gt('successful_referrals', 0)\n      .order('successful_referrals', { ascending: false })\n      .limit(10)\n\n    if (referrersError) {\n      return { success: false, error: 'Failed to fetch top referrers' }\n    }\n\n    return {\n      success: true,\n      data: {\n        total_referrals: totalReferrals,\n        successful_conversions: successfulConversions,\n        pending_referrals: pendingReferrals,\n        total_discount_earned: totalDiscountEarned,\n        top_referrers: (topReferrers || []).map(r => ({\n          id: r.id,\n          name: r.name,\n          referral_code: r.referral_code || '',\n          successful_referrals: r.successful_referrals || 0,\n          discount_earned: r.referral_discount_earned || 0\n        }))\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching admin referral stats:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function validateReferralCode(referralCode: string): Promise<ApiResponse<{\n  valid: boolean;\n  referrer_name?: string;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: referrer, error } = await supabase\n      .from('doctors')\n      .select('name, approved')\n      .eq('referral_code', referralCode)\n      .eq('approved', true)\n      .single()\n\n    if (error || !referrer) {\n      return { \n        success: true, \n        data: { valid: false } \n      }\n    }\n\n    return {\n      success: true,\n      data: {\n        valid: true,\n        referrer_name: referrer.name\n      }\n    }\n  } catch (error) {\n    console.error('Error validating referral code:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;IA6FsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3399, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/referral-stats.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Users, Gift, Crown, Copy, Check } from 'lucide-react'\nimport { ReferralInfo } from '@/lib/types'\nimport { getReferralInfo, generateReferralLink } from '@/lib/actions/referrals'\n\ninterface ReferralStatsProps {\n  doctorId: string\n}\n\nexport function ReferralStats({ doctorId }: ReferralStatsProps) {\n  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)\n  const [referralLink, setReferralLink] = useState<string>('')\n  const [copied, setCopied] = useState(false)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      const [infoResult, linkResult] = await Promise.all([\n        getReferralInfo(doctorId),\n        generateReferralLink(doctorId)\n      ])\n\n      if (infoResult.success) {\n        setReferralInfo(infoResult.data)\n      }\n      \n      if (linkResult.success) {\n        setReferralLink(linkResult.data)\n      }\n      \n      setLoading(false)\n    }\n\n    fetchData()\n  }, [doctorId])\n\n  const handleCopyLink = async () => {\n    if (referralLink) {\n      await navigator.clipboard.writeText(referralLink)\n      setCopied(true)\n      setTimeout(() => setCopied(false), 2000)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm shadow border border-orange-200/50 rounded-lg p-4 h-full\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-1/3\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!referralInfo) {\n    return null\n  }\n\n  const getBadgeIcon = () => {\n    if (referralInfo.successful_referrals >= 3) {\n      return <Crown className=\"w-5 h-5 text-amber-500\" />\n    }\n    return <Gift className=\"w-5 h-5 text-emerald-500\" />\n  }\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          {getBadgeIcon()}\n          <h3 className=\"text-sm font-medium text-slate-800\">Referral Program</h3>\n        </div>\n        <div className=\"w-6 h-6 rounded-lg bg-emerald-100 flex items-center justify-center shadow-md\">\n          {getBadgeIcon()}\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"flex-1 space-y-3\">\n        {/* Main Stats Grid */}\n        <div className=\"grid grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-emerald-50 to-green-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-emerald-700 font-medium mb-1\">Success</div>\n            <div className=\"text-lg font-bold text-emerald-800\">{referralInfo.successful_referrals}</div>\n          </div>\n          <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-purple-700 font-medium mb-1\">Earned</div>\n            <div className=\"text-sm font-bold text-purple-800\">₹{referralInfo.discount_earned.toFixed(0)}</div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-blue-50 to-cyan-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-blue-700 font-medium mb-1\">Total</div>\n            <div className=\"text-lg font-bold text-blue-800\">{referralInfo.total_referrals}</div>\n          </div>\n          <div className=\"bg-gradient-to-br from-amber-50 to-yellow-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-amber-700 font-medium mb-1\">Pending</div>\n            <div className=\"text-lg font-bold text-amber-800\">{referralInfo.pending_referrals}</div>\n          </div>\n        </div>\n\n        {/* Referral Link */}\n        <div className=\"bg-gradient-to-r from-slate-50 to-gray-50 p-3 rounded-lg\">\n          <div className=\"text-xs text-slate-600 mb-2 text-center\">Your Referral Link</div>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={referralLink}\n              readOnly\n              className=\"flex-1 px-2 py-1 rounded text-xs bg-white/70 text-slate-800 focus:outline-none\"\n            />\n            <button\n              onClick={handleCopyLink}\n              className=\"px-3 py-1 bg-teal-600 hover:bg-teal-700 text-white rounded text-xs transition-colors font-medium\"\n            >\n              {copied ? <Check className=\"w-3 h-3\" /> : <Copy className=\"w-3 h-3\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Referred By Section */}\n        {referralInfo.referred_by && (\n          <div className=\"p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg\">\n            <div className=\"flex items-center justify-center space-x-2\">\n              <Users className=\"w-3 h-3 text-blue-600\" />\n              <span className=\"text-xs text-blue-700\">\n                Referred by <span className=\"font-medium\">{referralInfo.referred_by.name}</span>\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AALA;;;;;AAWO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,MAAM,CAAC,YAAY,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjD,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD,EAAE;gBAChB,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE;aACtB;YAED,IAAI,WAAW,OAAO,EAAE;gBACtB,gBAAgB,WAAW,IAAI;YACjC;YAEA,IAAI,WAAW,OAAO,EAAE;gBACtB,gBAAgB,WAAW,IAAI;YACjC;YAEA,WAAW;QACb;QAEA;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,IAAI,cAAc;YAChB,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa,oBAAoB,IAAI,GAAG;YAC1C,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;QACA,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;;;;;;;kCAErD,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAAsC,aAAa,oBAAoB;;;;;;;;;;;;0CAExF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;kDAC1D,8OAAC;wCAAI,WAAU;;4CAAoC;4CAAE,aAAa,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kCAI9F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDACxD,8OAAC;wCAAI,WAAU;kDAAmC,aAAa,eAAe;;;;;;;;;;;;0CAEhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAoC,aAAa,iBAAiB;;;;;;;;;;;;;;;;;;kCAKrF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA0C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,uBAAS,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAM/D,aAAa,WAAW,kBACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;;wCAAwB;sDAC1B,8OAAC;4CAAK,WAAU;sDAAe,aAAa,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxF", "debugId": null}}, {"offset": {"line": 3778, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/ui/loading-wrapper.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\n\ninterface LoadingWrapperProps {\n  isLoading: boolean\n  children: ReactNode\n  loadingText?: string\n  className?: string\n}\n\nexport function LoadingWrapper({ isLoading, children, loadingText = 'Loading...', className = '' }: LoadingWrapperProps) {\n  if (isLoading) {\n    return (\n      <div className={`animate-pulse ${className}`}>\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-4 h-4 bg-orange-400 rounded-full animate-bounce\"></div>\n            <div className=\"w-4 h-4 bg-orange-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n            <div className=\"w-4 h-4 bg-orange-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            <span className=\"ml-2 text-slate-600\">{loadingText}</span>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n\nexport function SkeletonBox({ className = '', height = 'h-4' }: { className?: string, height?: string }) {\n  return (\n    <div className={`${height} bg-slate-200 rounded animate-pulse ${className}`}></div>\n  )\n}\n\nexport function FormSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <SkeletonBox height=\"h-6\" className=\"w-1/3\" />\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <SkeletonBox height=\"h-10\" />\n          <SkeletonBox height=\"h-10\" />\n        </div>\n      </div>\n      <div className=\"space-y-4\">\n        <SkeletonBox height=\"h-6\" className=\"w-1/4\" />\n        <SkeletonBox height=\"h-10\" className=\"max-w-md\" />\n      </div>\n      <div className=\"space-y-4\">\n        <SkeletonBox height=\"h-6\" className=\"w-1/4\" />\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n          {Array.from({ length: 6 }).map((_, i) => (\n            <SkeletonBox key={i} height=\"h-8\" />\n          ))}\n        </div>\n      </div>\n      <div className=\"flex justify-between pt-6\">\n        <SkeletonBox height=\"h-10\" className=\"w-32\" />\n        <SkeletonBox height=\"h-10\" className=\"w-32\" />\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;;;AAAA;;AAWO,SAAS,eAAe,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,YAAY,EAAE,YAAY,EAAE,EAAuB;IACrH,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAC,cAAc,EAAE,WAAW;sBAC1C,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAoD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCACnG,8OAAC;4BAAI,WAAU;4BAAoD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCACnG,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;;;;;;;;;;;IAKjD;IAEA,qBAAO;kBAAG;;AACZ;AAEO,SAAS,YAAY,EAAE,YAAY,EAAE,EAAE,SAAS,KAAK,EAA2C;IACrG,qBACE,8OAAC;QAAI,WAAW,GAAG,OAAO,oCAAoC,EAAE,WAAW;;;;;;AAE/E;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAY,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAY,QAAO;;;;;;0CACpB,8OAAC;gCAAY,QAAO;;;;;;;;;;;;;;;;;;0BAGxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAY,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC;wBAAY,QAAO;wBAAO,WAAU;;;;;;;;;;;;0BAEvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAY,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gCAAoB,QAAO;+BAAV;;;;;;;;;;;;;;;;0BAIxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAY,QAAO;wBAAO,WAAU;;;;;;kCACrC,8OAAC;wBAAY,QAAO;wBAAO,WAAU;;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/ui/skeleton-loaders.tsx"], "sourcesContent": ["'use client'\n\nimport { SkeletonBox } from '@/components/ui/loading-wrapper'\n\n// Dashboard skeleton components\nexport function DashboardSkeleton() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50\">\n      {/* Header skeleton */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <SkeletonBox height=\"h-8\" className=\"w-48\" />\n          <SkeletonBox height=\"h-10\" className=\"w-32\" />\n        </div>\n      </div>\n\n      {/* Main content skeleton */}\n      <div className=\"flex h-[calc(100vh-80px)]\">\n        {/* Left sidebar skeleton */}\n        <div className=\"w-80 border-r border-gray-200 p-6 space-y-4\">\n          <SkeletonBox height=\"h-6\" className=\"w-32\" />\n          <div className=\"space-y-3\">\n            {Array.from({ length: 5 }).map((_, i) => (\n              <div key={i} className=\"p-4 border rounded-lg space-y-2\">\n                <SkeletonBox height=\"h-4\" className=\"w-24\" />\n                <SkeletonBox height=\"h-3\" className=\"w-full\" />\n                <SkeletonBox height=\"h-3\" className=\"w-3/4\" />\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Main recording interface skeleton */}\n        <div className=\"flex-1 p-6 space-y-6\">\n          <div className=\"text-center space-y-4\">\n            <SkeletonBox height=\"h-12\" className=\"w-12 rounded-full mx-auto\" />\n            <SkeletonBox height=\"h-6\" className=\"w-48 mx-auto\" />\n            <SkeletonBox height=\"h-4\" className=\"w-64 mx-auto\" />\n          </div>\n\n          <div className=\"max-w-md mx-auto space-y-4\">\n            <SkeletonBox height=\"h-10\" className=\"w-full\" />\n            <SkeletonBox height=\"h-10\" className=\"w-full\" />\n            <SkeletonBox height=\"h-32\" className=\"w-full\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Info page skeleton\nexport function InfoPageSkeleton() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50\">\n      {/* Header skeleton */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <SkeletonBox height=\"h-8\" className=\"w-32\" />\n      </div>\n\n      {/* Stats grid skeleton */}\n      <div className=\"p-6 space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* 2x2 Stats Grid */}\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              {Array.from({ length: 4 }).map((_, i) => (\n                <div key={i} className=\"p-4 bg-white rounded-lg border space-y-2\">\n                  <SkeletonBox height=\"h-8\" className=\"w-16\" />\n                  <SkeletonBox height=\"h-4\" className=\"w-24\" />\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Quota Card */}\n          <div className=\"p-6 bg-white rounded-lg border space-y-4\">\n            <SkeletonBox height=\"h-6\" className=\"w-32\" />\n            <SkeletonBox height=\"h-4\" className=\"w-full\" />\n            <SkeletonBox height=\"h-2\" className=\"w-full\" />\n            <SkeletonBox height=\"h-4\" className=\"w-24\" />\n          </div>\n\n          {/* Referral Card */}\n          <div className=\"p-6 bg-white rounded-lg border space-y-4\">\n            <SkeletonBox height=\"h-6\" className=\"w-32\" />\n            <SkeletonBox height=\"h-4\" className=\"w-full\" />\n            <SkeletonBox height=\"h-4\" className=\"w-3/4\" />\n          </div>\n        </div>\n\n        {/* Consultations list skeleton */}\n        <div className=\"bg-white rounded-lg border p-6 space-y-4\">\n          <SkeletonBox height=\"h-6\" className=\"w-48\" />\n          <div className=\"space-y-3\">\n            {Array.from({ length: 8 }).map((_, i) => (\n              <div key={i} className=\"flex items-center justify-between p-3 border rounded\">\n                <div className=\"space-y-2\">\n                  <SkeletonBox height=\"h-4\" className=\"w-32\" />\n                  <SkeletonBox height=\"h-3\" className=\"w-48\" />\n                </div>\n                <SkeletonBox height=\"h-6\" className=\"w-20\" />\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Admin dashboard skeleton\nexport function AdminDashboardSkeleton() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header skeleton */}\n      <div className=\"bg-white border-b p-6\">\n        <div className=\"flex items-center justify-between\">\n          <SkeletonBox height=\"h-8\" className=\"w-48\" />\n          <SkeletonBox height=\"h-10\" className=\"w-32\" />\n        </div>\n      </div>\n\n      {/* Stats cards skeleton */}\n      <div className=\"p-6 space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {Array.from({ length: 6 }).map((_, i) => (\n            <div key={i} className=\"p-6 bg-white rounded-lg border space-y-2\">\n              <SkeletonBox height=\"h-8\" className=\"w-16\" />\n              <SkeletonBox height=\"h-4\" className=\"w-24\" />\n            </div>\n          ))}\n        </div>\n\n        {/* Table skeleton */}\n        <div className=\"bg-white rounded-lg border\">\n          <div className=\"p-6 border-b\">\n            <SkeletonBox height=\"h-6\" className=\"w-32\" />\n          </div>\n          <div className=\"p-6 space-y-4\">\n            {Array.from({ length: 10 }).map((_, i) => (\n              <div key={i} className=\"flex items-center justify-between p-3 border-b\">\n                <div className=\"flex items-center space-x-4\">\n                  <SkeletonBox height=\"h-10\" className=\"w-10 rounded-full\" />\n                  <div className=\"space-y-2\">\n                    <SkeletonBox height=\"h-4\" className=\"w-32\" />\n                    <SkeletonBox height=\"h-3\" className=\"w-48\" />\n                  </div>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <SkeletonBox height=\"h-8\" className=\"w-20\" />\n                  <SkeletonBox height=\"h-8\" className=\"w-20\" />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Consultations list skeleton (reusable component)\nexport function ConsultationsListSkeleton() {\n  return (\n    <div className=\"space-y-3\">\n      {Array.from({ length: 6 }).map((_, i) => (\n        <div key={i} className=\"p-4 border rounded-lg space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <SkeletonBox height=\"h-4\" className=\"w-24\" />\n            <SkeletonBox height=\"h-6\" className=\"w-16\" />\n          </div>\n          <SkeletonBox height=\"h-3\" className=\"w-full\" />\n          <SkeletonBox height=\"h-3\" className=\"w-3/4\" />\n          <div className=\"flex items-center space-x-2 pt-2\">\n            <SkeletonBox height=\"h-3\" className=\"w-16\" />\n            <SkeletonBox height=\"h-3\" className=\"w-20\" />\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Dashboard stats skeleton\nexport function DashboardStatsSkeleton() {\n  return (\n    <div className=\"grid grid-cols-2 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"p-4 bg-white rounded-lg border space-y-2\">\n          <SkeletonBox height=\"h-8\" className=\"w-16\" />\n          <SkeletonBox height=\"h-4\" className=\"w-24\" />\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Quota card skeleton\nexport function QuotaCardSkeleton() {\n  return (\n    <div className=\"p-6 bg-white rounded-lg border space-y-4\">\n      <SkeletonBox height=\"h-6\" className=\"w-32\" />\n      <SkeletonBox height=\"h-4\" className=\"w-full\" />\n      <SkeletonBox height=\"h-2\" className=\"w-full\" />\n      <div className=\"flex justify-between\">\n        <SkeletonBox height=\"h-4\" className=\"w-16\" />\n        <SkeletonBox height=\"h-4\" className=\"w-20\" />\n      </div>\n    </div>\n  )\n}\n\n// Referral stats skeleton\nexport function ReferralStatsSkeleton() {\n  return (\n    <div className=\"p-6 bg-white rounded-lg border space-y-4\">\n      <SkeletonBox height=\"h-6\" className=\"w-32\" />\n      <div className=\"space-y-3\">\n        <div className=\"flex justify-between\">\n          <SkeletonBox height=\"h-4\" className=\"w-24\" />\n          <SkeletonBox height=\"h-4\" className=\"w-8\" />\n        </div>\n        <div className=\"flex justify-between\">\n          <SkeletonBox height=\"h-4\" className=\"w-32\" />\n          <SkeletonBox height=\"h-4\" className=\"w-12\" />\n        </div>\n        <div className=\"flex justify-between\">\n          <SkeletonBox height=\"h-4\" className=\"w-28\" />\n          <SkeletonBox height=\"h-4\" className=\"w-16\" />\n        </div>\n      </div>\n      <SkeletonBox height=\"h-10\" className=\"w-full\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAFA;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,cAAW;4BAAC,QAAO;4BAAM,WAAU;;;;;;sCACpC,8OAAC,8IAAA,CAAA,cAAW;4BAAC,QAAO;4BAAO,WAAU;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC,8IAAA,CAAA,cAAW;gDAAC,QAAO;gDAAM,WAAU;;;;;;0DACpC,8OAAC,8IAAA,CAAA,cAAW;gDAAC,QAAO;gDAAM,WAAU;;;;;;0DACpC,8OAAC,8IAAA,CAAA,cAAW;gDAAC,QAAO;gDAAM,WAAU;;;;;;;uCAH5B;;;;;;;;;;;;;;;;kCAUhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAO,WAAU;;;;;;kDACrC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;;;;;;;0CAGtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAO,WAAU;;;;;;kDACrC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAO,WAAU;;;;;;kDACrC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,cAAW;oBAAC,QAAO;oBAAM,WAAU;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC,8IAAA,CAAA,cAAW;oDAAC,QAAO;oDAAM,WAAU;;;;;;8DACpC,8OAAC,8IAAA,CAAA,cAAW;oDAAC,QAAO;oDAAM,WAAU;;;;;;;2CAF5B;;;;;;;;;;;;;;;0CAShB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;;;;;;;0CAItC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,cAAW;wDAAC,QAAO;wDAAM,WAAU;;;;;;kEACpC,8OAAC,8IAAA,CAAA,cAAW;wDAAC,QAAO;wDAAM,WAAU;;;;;;;;;;;;0DAEtC,8OAAC,8IAAA,CAAA,cAAW;gDAAC,QAAO;gDAAM,WAAU;;;;;;;uCAL5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaxB;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,cAAW;4BAAC,QAAO;4BAAM,WAAU;;;;;;sCACpC,8OAAC,8IAAA,CAAA,cAAW;4BAAC,QAAO;4BAAO,WAAU;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;kDACpC,8OAAC,8IAAA,CAAA,cAAW;wCAAC,QAAO;wCAAM,WAAU;;;;;;;+BAF5B;;;;;;;;;;kCAQd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8IAAA,CAAA,cAAW;oCAAC,QAAO;oCAAM,WAAU;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,cAAW;wDAAC,QAAO;wDAAO,WAAU;;;;;;kEACrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,cAAW;gEAAC,QAAO;gEAAM,WAAU;;;;;;0EACpC,8OAAC,8IAAA,CAAA,cAAW;gEAAC,QAAO;gEAAM,WAAU;;;;;;;;;;;;;;;;;;0DAGxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,cAAW;wDAAC,QAAO;wDAAM,WAAU;;;;;;kEACpC,8OAAC,8IAAA,CAAA,cAAW;wDAAC,QAAO;wDAAM,WAAU;;;;;;;;;;;;;uCAV9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBxB;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;;;;;;;kCAEtC,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;;;;;;;;eAT9B;;;;;;;;;;AAelB;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;;eAF5B;;;;;;;;;;AAOlB;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAA,CAAA,cAAW;gBAAC,QAAO;gBAAM,WAAU;;;;;;0BACpC,8OAAC,8IAAA,CAAA,cAAW;gBAAC,QAAO;gBAAM,WAAU;;;;;;0BACpC,8OAAC,8IAAA,CAAA,cAAW;gBAAC,QAAO;gBAAM,WAAU;;;;;;0BACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;kCACpC,8OAAC,8IAAA,CAAA,cAAW;wBAAC,QAAO;wBAAM,WAAU;;;;;;;;;;;;;;;;;;AAI5C;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAA,CAAA,cAAW;gBAAC,QAAO;gBAAM,WAAU;;;;;;0BACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;;;;;;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;;;;;;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;0CACpC,8OAAC,8IAAA,CAAA,cAAW;gCAAC,QAAO;gCAAM,WAAU;;;;;;;;;;;;;;;;;;0BAGxC,8OAAC,8IAAA,CAAA,cAAW;gBAAC,QAAO;gBAAO,WAAU;;;;;;;;;;;;AAG3C", "debugId": null}}]}