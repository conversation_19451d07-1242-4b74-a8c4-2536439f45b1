module.exports={771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},403155:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createAdminSession:()=>i,decryptAdminSession:()=>h,deleteAdminSession:()=>k,deleteSession:()=>k,encryptAdminSession:()=>g,updateAdminSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify admin session"),null}}async function i(a,b="admin"){let c=new Date(Date.now()+6048e5),d=await g({adminId:a,role:b,expiresAt:c}),e=await (0,f.cookies)();console.log("DEBUG: Creating admin session for admin:",a,"role:",b),console.log("DEBUG: Admin session expires at:",c),e.set("admin_session",d,{httpOnly:!0,secure:!1,expires:c,sameSite:"lax",path:"/"}),console.log("DEBUG: Admin session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("admin_session")?.value,c=await h(b);if(!b||!c)return null;let d=new Date(Date.now()+6048e5);a.set("admin_session",b,{httpOnly:!0,secure:!0,expires:d,sameSite:"lax",path:"/"})}async function k(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting admin session cookie"),a.delete("admin_session"),console.log("DEBUG: Admin session cookie deleted")}}},534304:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({adminLogin:()=>i,adminLogout:()=>j,createAdminUser:()=>k});var d=a.i(930768);a.i(996945);var e=a.i(437713),f=a.i(729149),g=a.i(403155),h=a.i(635512);async function i(a,b){let c=h.AdminLoginFormSchema.safeParse({email:b.get("email"),password:b.get("password")});if(!c.success)return{success:!1,message:"Invalid form fields."};let{email:d,password:i}=c.data;try{let a=await (0,f.createClient)(),{data:b}=await a.from("admins").select("id, password_hash, role").eq("email",d).single();if(!b||!await e.default.compare(i,b.password_hash))return{success:!1,message:"Invalid email or password."};return await (0,g.createAdminSession)(b.id,b.role),{success:!0,message:"Login successful! Redirecting..."}}catch(a){return console.error("Admin Login Error:",a),{success:!1,message:"An unexpected error occurred."}}}async function j(){return await (0,g.deleteSession)(),{success:!0}}async function k(a,b,c,d="admin"){try{let g=await (0,f.createClient)(),{data:h}=await g.from("admins").select("id").eq("email",a).single();if(h)return{success:!1,error:"An admin with this email already exists."};let i=await e.default.hash(b,10),{data:j,error:k}=await g.from("admins").insert({name:c,email:a,password_hash:i,role:d}).select("id").single();if(k)return console.error("Database error:",k),{success:!1,error:"An error occurred while creating the admin account."};if(!j)return{success:!1,error:"An error occurred while creating the admin account."};return{success:!0,adminId:j.id}}catch(a){return console.error("Create admin error:",a),{success:!1,error:"An unexpected error occurred."}}}(0,a.i(377991).ensureServerEntryExports)([i,j,k]),(0,d.registerServerReference)(i,"60e96432ee93dbc1f2cacf69805b78047ca4cf9f56",null),(0,d.registerServerReference)(j,"0052d311627b1e498ec699db5ec34cf21bfb47393c",null),(0,d.registerServerReference)(k,"787a2e1247c0a22d20605e42975e36fd0fe310203e",null)},845867:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(534304)},800519:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(534304),a.i(845867)},422731:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"60e96432ee93dbc1f2cacf69805b78047ca4cf9f56":()=>d.adminLogin});var d=a.i(534304);a.i(845867)},677179:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"60e96432ee93dbc1f2cacf69805b78047ca4cf9f56":()=>d["60e96432ee93dbc1f2cacf69805b78047ca4cf9f56"]}),a.i(800519);var d=a.i(422731)},288913:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminLoginForm:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AdminLoginForm() from the server but AdminLoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/admin-login-form.tsx <module evaluation>","AdminLoginForm")}},380408:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminLoginForm:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AdminLoginForm() from the server but AdminLoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/admin-login-form.tsx","AdminLoginForm")}},38666:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(288913);var d=a.i(380408);a.n(d)},135818:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>f,metadata:()=>b});var d=a.i(129629),e=a.i(38666);let b={title:"Admin Login - Celer AI",description:"Admin login for Celer AI"};function f(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Admin Access"}),(0,d.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to the admin dashboard"})]}),(0,d.jsx)(e.AdminLoginForm,{})]})})}}},432882:a=>{var{g:b,__dirname:c}=a;a.n(a.i(135818))},939491:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(432882),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["admin",{children:["login",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/admin/login/page.tsx"]}]},{metadata:{}}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/admin/login/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},199361:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(432882),a.i(660874),a.i(715847),a.i(781045),a.i(939491)},473588:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(939491)},663729:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(199361);var d=a.i(473588)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__65230092._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__7a29843d._.js.map