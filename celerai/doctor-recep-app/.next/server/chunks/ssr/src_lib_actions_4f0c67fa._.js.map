{"version": 3, "sources": ["turbopack:///[project]/src/lib/actions/referrals.ts", "turbopack:///[project]/src/lib/actions/auth.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ReferralInfo, ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {\n  try {\n    const supabase = await createClient()\n\n    // Get doctor's referral information\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select(`\n        referral_code,\n        total_referrals,\n        successful_referrals,\n        referral_discount_earned,\n        referred_by\n      `)\n      .eq('id', doctorId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return { success: false, error: 'Failed to fetch referral information' }\n    }\n\n    // Get pending referrals count\n    const { count: pendingCount } = await supabase\n      .from('referral_analytics')\n      .select('*', { count: 'exact', head: true })\n      .eq('referrer_id', doctorId)\n      .eq('status', 'pending')\n\n    // Get recent referrals\n    const { data: recentReferrals, error: referralsError } = await supabase\n      .from('referral_analytics')\n      .select(`\n        id,\n        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),\n        signup_date,\n        conversion_date,\n        status\n      `)\n      .eq('referrer_id', doctorId)\n      .order('created_at', { ascending: false })\n      .limit(10)\n\n    if (referralsError) {\n      return { success: false, error: 'Failed to fetch recent referrals' }\n    }\n\n    // Get referrer info separately if exists\n    let referredBy = null\n    if (doctor.referred_by) {\n      const { data: referrer } = await supabase\n        .from('doctors')\n        .select('name, referral_code')\n        .eq('id', doctor.referred_by)\n        .single()\n      \n      if (referrer) {\n        referredBy = {\n          name: referrer.name,\n          referral_code: referrer.referral_code || ''\n        }\n      }\n    }\n\n    const referralInfo: ReferralInfo = {\n      referral_code: doctor.referral_code || '',\n      total_referrals: doctor.total_referrals || 0,\n      successful_referrals: doctor.successful_referrals || 0,\n      pending_referrals: pendingCount || 0,\n      discount_earned: doctor.referral_discount_earned || 0,\n      referred_by: referredBy,\n      recent_referrals: (recentReferrals || []).map(ref => ({\n        id: ref.id,\n        name: ref.referred_doctor?.name || 'Unknown',\n        email: ref.referred_doctor?.email || 'Unknown',\n        signup_date: ref.signup_date,\n        conversion_date: ref.conversion_date,\n        status: ref.status as 'pending' | 'converted' | 'expired'\n      }))\n    }\n\n    return { success: true, data: referralInfo }\n  } catch (error) {\n    console.error('Error fetching referral info:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('referral_code')\n      .eq('id', doctorId)\n      .single()\n\n    if (error || !doctor?.referral_code) {\n      return { success: false, error: 'Failed to fetch referral code' }\n    }\n\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'\n    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`\n\n    return { success: true, data: referralLink }\n  } catch (error) {\n    console.error('Error generating referral link:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Find the referrer\n    const { data: referrer, error: referrerError } = await supabase\n      .from('doctors')\n      .select('id, name')\n      .eq('referral_code', referralCode)\n      .single()\n\n    if (referrerError || !referrer) {\n      return { success: false, error: 'Invalid referral code' }\n    }\n\n    // Update the new doctor with referrer information\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ referred_by: referrer.id })\n      .eq('id', newDoctorId)\n\n    if (updateError) {\n      return { success: false, error: 'Failed to process referral signup' }\n    }\n\n    // Create referral analytics record\n    const { error: analyticsError } = await supabase\n      .from('referral_analytics')\n      .insert({\n        referrer_id: referrer.id,\n        referred_doctor_id: newDoctorId,\n        referral_code: referralCode,\n        status: 'pending'\n      })\n\n    if (analyticsError) {\n      console.error('Failed to create analytics record:', analyticsError)\n      // Don't fail the signup for this\n    }\n\n    // Update referrer's total referrals count\n    const { data: currentReferrer } = await supabase\n      .from('doctors')\n      .select('total_referrals')\n      .eq('id', referrer.id)\n      .single()\n\n    if (currentReferrer) {\n      const { error: countError } = await supabase\n        .from('doctors')\n        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })\n        .eq('id', referrer.id)\n\n      if (countError) {\n        console.error('Failed to update referral count:', countError)\n      }\n    }\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error processing referral signup:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Call the database function to handle conversion\n    const { data, error } = await supabase.rpc('handle_referral_conversion', {\n      referred_doctor_uuid: doctorId\n    })\n\n    if (error) {\n      console.error('Error marking referral conversion:', error)\n      return { success: false, error: 'Failed to process referral conversion' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/admin/dashboard')\n\n    return { success: true, data: data || false }\n  } catch (error) {\n    console.error('Error marking referral conversion:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getAdminReferralStats(): Promise<ApiResponse<{\n  total_referrals: number;\n  successful_conversions: number;\n  pending_referrals: number;\n  total_discount_earned: number;\n  top_referrers: Array<{\n    id: string;\n    name: string;\n    referral_code: string;\n    successful_referrals: number;\n    discount_earned: number;\n  }>;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    // Get overall stats\n    const { data: totalStats, error: statsError } = await supabase\n      .from('referral_analytics')\n      .select('status, discount_earned')\n\n    if (statsError) {\n      return { success: false, error: 'Failed to fetch referral statistics' }\n    }\n\n    const totalReferrals = totalStats?.length || 0\n    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0\n    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0\n    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0\n\n    // Get top referrers\n    const { data: topReferrers, error: referrersError } = await supabase\n      .from('doctors')\n      .select('id, name, referral_code, successful_referrals, referral_discount_earned')\n      .gt('successful_referrals', 0)\n      .order('successful_referrals', { ascending: false })\n      .limit(10)\n\n    if (referrersError) {\n      return { success: false, error: 'Failed to fetch top referrers' }\n    }\n\n    return {\n      success: true,\n      data: {\n        total_referrals: totalReferrals,\n        successful_conversions: successfulConversions,\n        pending_referrals: pendingReferrals,\n        total_discount_earned: totalDiscountEarned,\n        top_referrers: (topReferrers || []).map(r => ({\n          id: r.id,\n          name: r.name,\n          referral_code: r.referral_code || '',\n          successful_referrals: r.successful_referrals || 0,\n          discount_earned: r.referral_discount_earned || 0\n        }))\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching admin referral stats:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function validateReferralCode(referralCode: string): Promise<ApiResponse<{\n  valid: boolean;\n  referrer_name?: string;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: referrer, error } = await supabase\n      .from('doctors')\n      .select('name, approved')\n      .eq('referral_code', referralCode)\n      .eq('approved', true)\n      .single()\n\n    if (error || !referrer) {\n      return { \n        success: true, \n        data: { valid: false } \n      }\n    }\n\n    return {\n      success: true,\n      data: {\n        valid: true,\n        referrer_name: referrer.name\n      }\n    }\n  } catch (error) {\n    console.error('Error validating referral code:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}", "'use server'\n\nimport { redirect } from 'next/navigation'\nimport bcrypt from 'bcryptjs'\nimport { createClient } from '@/lib/supabase/server'\nimport { createSession, deleteSession, refreshSession } from '@/lib/auth/session'\nimport { SignupFormSchema, LoginFormSchema } from '@/lib/validations'\nimport { FormState } from '@/lib/types'\nimport { processReferralSignup } from '@/lib/actions/referrals'\n\nexport async function signup(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = SignupFormSchema.safeParse({\n    name: formData.get('name'),\n    email: formData.get('email'),\n    password: formData.get('password'),\n    clinic_name: formData.get('clinic_name'),\n    phone: formData.get('phone'),\n  })\n\n  // Get referral code from form\n  const referralCode = formData.get('referral_code') as string\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.', // A generic message for validation failure\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  // Prepare data for insertion into database\n  const { name, email, password, clinic_name, phone } = validatedFields.data\n  const hashedPassword = await bcrypt.hash(password, 10)\n\n  try {\n    const supabase = await createClient()\n\n    // Check if user already exists (email or phone)\n    const { data: existingUser } = await supabase\n      .from('doctors')\n      .select('id')\n      .eq('email', email)\n      .single()\n\n    if (existingUser) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An account with this email already exists.',\n      }\n    }\n\n    // Check if phone number already exists (if phone is provided)\n    if (phone) {\n      const { data: existingPhone } = await supabase\n        .from('doctors')\n        .select('id')\n        .eq('phone', phone)\n        .single()\n\n      if (existingPhone) {\n        return {\n          success: false,\n          message: 'An account with this phone number already exists.',\n        }\n      }\n    }\n\n    // Insert the user into the database with approval required and new default quota\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .insert({\n        name,\n        email,\n        password_hash: hashedPassword,\n        clinic_name,\n        phone,\n        approved: false, // New doctors require approval\n        monthly_quota: 50, // Set default quota to 50\n      })\n      .select('id, approved')\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    if (!user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    // Process referral if referral code was provided\n    if (referralCode) {\n      await processReferralSignup(referralCode, user.id)\n    }\n\n    // Don't create session for unapproved users\n    if (!user.approved) {\n      return {\n        success: true, // This is a success state, but with an informative message\n        message: 'Account created successfully! Please wait for admin approval before you can log in.',\n      }\n    }\n\n    // Create user session (only for approved users)\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Signup error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means signup was successful and session created (if approved)\n  // or a success message was returned for pending approval.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function login(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = LoginFormSchema.safeParse({\n    email: formData.get('email'),\n    password: formData.get('password'),\n  })\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.',\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  const { email, password } = validatedFields.data\n\n  try {\n    const supabase = await createClient()\n\n    // Get user from database\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, password_hash, approved')\n      .eq('email', email)\n      .single()\n\n    if (error || !user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Check if user is approved\n    if (!user.approved) {\n      return {\n        success: false, // This is an error state, preventing login\n        message: 'Your account is pending admin approval. Please wait for approval before logging in.',\n      }\n    }\n\n    // Create user session\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Login error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means login was successful and session created.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function logout() {\n  await deleteSession()\n  redirect('/login')\n}\n\nexport async function changePassword(doctorId: string, formData: FormData): Promise<FormState> {\n  const currentPassword = formData.get('currentPassword') as string\n  const newPassword = formData.get('newPassword') as string\n  const confirmPassword = formData.get('confirmPassword') as string\n\n  // Basic validation\n  if (!currentPassword || !newPassword || !confirmPassword) {\n    return {\n      success: false,\n      message: 'All fields are required.',\n    }\n  }\n\n  if (newPassword !== confirmPassword) {\n    return {\n      success: false,\n      message: 'New passwords do not match.',\n    }\n  }\n\n  if (newPassword.length < 8) {\n    return {\n      success: false,\n      message: 'New password must be at least 8 characters long.',\n    }\n  }\n\n  try {\n    const supabase = await createClient()\n\n    // Get current password hash\n    const { data: doctor, error: fetchError } = await supabase\n      .from('doctors')\n      .select('password_hash')\n      .eq('id', doctorId)\n      .single()\n\n    if (fetchError || !doctor) {\n      return {\n        success: false,\n        message: 'Doctor not found.',\n      }\n    }\n\n    // Verify current password\n    const isValidPassword = await bcrypt.compare(currentPassword, doctor.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false,\n        message: 'Current password is incorrect.',\n      }\n    }\n\n    // Hash new password\n    const hashedNewPassword = await bcrypt.hash(newPassword, 10)\n\n    // Update password\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ password_hash: hashedNewPassword })\n      .eq('id', doctorId)\n\n    if (updateError) {\n      console.error('Password update error:', updateError)\n      return {\n        success: false,\n        message: 'Failed to update password.',\n      }\n    }\n\n    // Refresh session with updated credentials\n    await refreshSession(doctorId)\n\n    return {\n      success: true,\n      message: 'Password changed successfully!',\n    }\n  } catch (error) {\n    console.error('Password change error:', error)\n    return {\n      success: false,\n      message: 'An unexpected error occurred.',\n    }\n  }\n}"], "names": [], "mappings": "qQAEA,IAAA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAEO,eAAe,EAAgB,CAAgB,EACpD,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAM,CAAE,MAAO,CAAW,CAAE,CAAG,IAHtB,EAG4B,EAChD,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,KAAM,GACT,MAAM,GAET,GAAI,GAAe,CAAC,EAClB,MAD0B,AACnB,CAAE,QAAS,GAAO,MAAO,sCAAuC,EAIzE,GAAM,CAAE,MAAO,CAAY,CAAE,CAAG,MAAM,EACnC,IAAI,CAAC,sBACL,MAAM,CAAC,IAAK,CAAE,MAAO,QAAS,MAAM,CAAK,GACzC,EAAE,CAAC,cAAe,GAClB,EAAE,CAAC,SAAU,WAGV,CAAE,KAAM,CAAe,CAAE,MAAO,CAAc,CAAE,CAAG,MAAM,EAC5D,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,cAAe,GAClB,KAAK,CAAC,aAAc,CAAE,WAAW,CAAM,GACvC,KAAK,CAAC,IAET,GAAI,EACF,MAAO,CAAE,OADS,EACA,EAAO,MAAO,kCAAmC,EAIrE,IAAI,EAAa,KACjB,GAAI,EAAO,WAAW,CAAE,CACtB,GAAM,CAAE,KAAM,CAAQ,CAAE,CAAG,MAAM,EAC9B,IAAI,CAAC,WACL,MAAM,CAAC,uBACP,EAAE,CAAC,KAAM,EAAO,WAAW,EAC3B,MAAM,GAEL,IACF,EAAa,CACX,GAFU,EAEJ,EAAS,IAAI,CACnB,cAAe,EAAS,aAAa,EAAI,EAC3C,EAEJ,CAEA,IAAM,EAA6B,CACjC,cAAe,EAAO,aAAa,EAAI,GACvC,gBAAiB,EAAO,eAAe,EAAI,EAC3C,qBAAsB,EAAO,oBAAoB,EAAI,EACrD,kBAAmB,GAAgB,EACnC,gBAAiB,EAAO,wBAAwB,EAAI,EACpD,YAAa,EACb,iBAAkB,CAAC,GAAmB,EAAE,AAAF,EAAI,GAAG,CAAC,IAAQ,CACpD,CADmD,EAC/C,EAAI,EAAE,CACV,KAAM,EAAI,eAAe,EAAE,MAAQ,UACnC,MAAO,EAAI,eAAe,EAAE,OAAS,UACrC,YAAa,EAAI,WAAW,CAC5B,gBAAiB,EAAI,eAAe,CACpC,OAAQ,EAAI,MAAM,CACpB,CAAC,CACH,EAEA,MAAO,CAAE,SAAS,EAAM,KAAM,CAAa,CAC7C,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,CAAE,QAAS,GAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAqB,CAAgB,EACzD,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAE5B,CAAE,KAAM,CAAM,CAAE,OAAK,CAAE,CAAG,IAFT,EAEe,EACnC,IAAI,CAAC,WACL,MAAM,CAAC,iBACP,EAAE,CAAC,KAAM,GACT,MAAM,GAET,GAAI,GAAS,CAAC,GAAQ,cACpB,CADmC,KAC5B,CAAE,SAAS,EAAO,MAAO,+BAAgC,EAGlE,IAAM,EAAU,QAAQ,GAAG,CAAC,mBAAmB,EAAI,6BAC7C,EAAe,CAAA,EAAG,EAAQ,YAAY,EAAE,EAAO,aAAa,CAAA,CAAE,CAEpE,MAAO,CAAE,SAAS,EAAM,KAAM,CAAa,CAC7C,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,CAAE,QAAS,GAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAsB,CAAoB,CAAE,CAAmB,EACnF,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAQ,CAAE,MAAO,CAAa,CAAE,CAAG,IAH1B,EAGgC,EACpD,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,gBAAiB,GACpB,MAAM,GAET,GAAI,GAAiB,CAAC,EACpB,MAAO,CAAE,CADqB,QACZ,EAAO,MAAO,uBAAwB,EAI1D,GAAM,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,IAAI,CAAC,WACL,MAAM,CAAC,CAAE,YAAa,EAAS,EAAE,AAAC,GAClC,EAAE,CAAC,KAAM,GAEZ,GAAI,EACF,MAAO,CAAE,IADM,IACG,GAAO,MAAO,mCAAoC,EAItE,GAAM,CAAE,MAAO,CAAc,CAAE,CAAG,MAAM,EACrC,IAAI,CAAC,sBACL,MAAM,CAAC,CACN,YAAa,EAAS,EAAE,CACxB,mBAAoB,EACpB,cAAe,EACf,OAAQ,SACV,GAEE,GACF,QAAQ,KADU,AACL,CAAC,qCAAsC,GAKtD,GAAM,CAAE,KAAM,CAAe,CAAE,CAAG,MAAM,EACrC,IAAI,CAAC,WACL,MAAM,CAAC,mBACP,EAAE,CAAC,KAAM,EAAS,EAAE,EACpB,MAAM,GAET,GAAI,EAAiB,CACnB,GAAM,CAAE,MAAO,CAAU,CAAE,CAAG,MAAM,EACjC,IAAI,CAAC,WACL,MAAM,CAAC,CAAE,gBAAiB,CAAC,EAAgB,eAAe,GAAI,CAAC,CAAI,CAAE,GACrE,EAAE,CAAC,KAAM,EAAS,EAAE,EAEnB,GACF,QAAQ,CADM,IACD,CAAC,mCAAoC,EAEtD,CAEA,MAAO,CAAE,SAAS,EAAM,MAAM,CAAK,CACrC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAuB,CAAgB,EAC3D,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,IAHD,EAGO,EAAS,GAAG,CAAC,6BAA8B,CACvE,qBAAsB,CACxB,GAEA,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,CAAE,QAAS,GAAO,MAAO,uCAAwC,EAM1E,MAHA,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,cACf,CAAA,EAAA,EAAA,EADA,YACA,AAAa,EAAE,oBAER,CAFP,AAES,SAAS,EAAM,KAAM,GAAQ,EAAM,CAC9C,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,IAapB,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAU,CAAE,MAAO,CAAU,CAAE,CAAG,IAHzB,EAG+B,EACnD,IAAI,CAAC,sBACL,MAAM,CAAC,2BAEV,GAAI,EACF,MAAO,CAAE,GADK,MACI,EAAO,MAAO,qCAAsC,EAGxE,IAAM,EAAiB,GAAY,QAAU,EACvC,EAAwB,GAAY,OAAO,GAAK,AAAa,gBAAX,MAAM,EAAkB,QAAU,EACpF,EAAmB,GAAY,OAAO,GAAkB,YAAb,EAAE,MAAM,EAAgB,QAAU,EAC7E,EAAsB,GAAY,OAAO,CAAC,EAAK,IAAM,GAAO,EAAE,CAAH,cAAkB,GAAI,CAAC,CAAG,IAAM,EAG3F,CAAE,KAAM,CAAY,CAAE,MAAO,CAAc,CAAE,CAAG,MAAM,EACzD,IAAI,CAAC,WACL,MAAM,CAAC,2EACP,EAAE,CAAC,uBAAwB,GAC3B,KAAK,CAAC,uBAAwB,CAAE,WAAW,CAAM,GACjD,KAAK,CAAC,IAET,GAAI,EACF,MAAO,CAAE,OADS,EACA,EAAO,MAAO,+BAAgC,EAGlE,MAAO,CACL,SAAS,EACT,KAAM,CACJ,gBAAiB,EACjB,uBAAwB,EACxB,kBAAmB,EACnB,sBAAuB,EACvB,cAAe,AAAC,IAAgB,EAAA,AAAE,EAAE,GAAG,CAAC,IAAK,AAAC,CAC5C,GAAI,EAAE,EAAE,CACR,KAAM,EAAE,IAAI,CACZ,cAAe,EAAE,aAAa,EAAI,GAClC,qBAAsB,EAAE,oBAAoB,EAAI,EAChD,gBAAiB,EAAE,wBAAwB,EAAI,CACjD,CAAC,EACH,CACF,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,uCAAwC,GAC/C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,CAEO,eAAe,EAAqB,CAAoB,EAI7D,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAE5B,CAAE,KAAM,CAAQ,OAAE,CAAK,CAAE,CAAG,IAFX,EAEiB,EACrC,IAAI,CAAC,WACL,MAAM,CAAC,kBACP,EAAE,CAAC,gBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,MAAM,GAET,GAAI,GAAS,CAAC,EACZ,MAAO,CACL,CAFoB,QAEX,EACT,KAAM,CAAE,MAAO,EAAM,CACvB,EAGF,MAAO,CACL,SAAS,EACT,KAAM,CACJ,OAAO,EACP,cAAe,EAAS,IAAI,AAC9B,CACF,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,CAAE,SAAS,EAAO,MAAO,8BAA+B,CACjE,CACF,2CAxSsB,EAuFA,EAwBA,EAiEA,EAwBA,EAgEA,IAxQA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAuFA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAwBA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAiEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAwBA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAgEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,sJC5QtB,EAAA,CAAA,CAAA,QAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAEO,eAAe,EAAO,CAAgB,CAAE,CAAkB,EAE/D,IAAM,EAAkB,EAAA,gBAAgB,CAAC,SAAS,CAAC,CACjD,KAAM,EAAS,EADO,CACJ,CAAC,QACnB,MAAO,EAAS,GAAG,CAAC,SACpB,SAAU,EAAS,GAAG,CAAC,YACvB,YAAa,EAAS,GAAG,CAAC,eAC1B,MAAO,EAAS,GAAG,CAAC,QACtB,GAGM,EAAe,EAAS,GAAG,CAAC,iBAGlC,GAAI,CAAC,EAAgB,OAAO,CAC1B,CAD4B,KACrB,CAEL,SAAS,EACT,QAAS,uBAET,YAAa,EAAgB,KAAK,CAAC,OAAO,GAAG,WAAW,AAC1D,EAIF,GAAM,MAAE,CAAI,CAAE,OAAK,UAAE,CAAQ,CAAE,aAAW,OAAE,CAAK,CAAE,CAAG,EAAgB,IAAI,CACpE,EAAiB,MAAM,EAAA,OAAM,CAAC,IAAI,CAAC,EAAU,IAEnD,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,CAHI,CAGJ,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAY,CAAE,CAAG,MAAM,EAClC,IAAI,AAJgB,CAIf,WACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAS,GACZ,MAAM,GAET,GAAI,EACF,MAAO,CACL,KAFc,IAEL,EACT,QAAS,4CACX,EAIF,GAAI,EAAO,CACT,GAAM,CAAE,KAAM,CAAa,CAAE,CAAG,MAAM,EACnC,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAS,GACZ,MAAM,GAET,GAAI,EACF,MAAO,CACL,MAFe,GAEN,EACT,QAAS,mDACX,CAEJ,CAGA,GAAM,CAAE,KAAM,CAAI,CAAE,OAAK,CAAE,CAAG,MAAM,EACjC,IAAI,CAAC,WACL,MAAM,CAAC,MACN,QACA,EACA,cAAe,EACf,oBACA,EACA,UAAU,EACV,cAAe,EACjB,GACC,MAAM,CAAC,gBACP,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,CACL,SAAS,EACT,QAAS,gDACX,EAGF,GAAI,CAAC,EACH,IADS,EACF,CACL,QAAS,GACT,QAAS,gDACX,EASF,GALI,GACF,MAAM,CAAA,EAAA,EADU,AACV,qBAAA,AAAoB,EAAE,EAAc,EAAK,EAAE,EAI/C,CAAC,EAAK,GAJF,KAIU,CAChB,CADkB,KACX,CACL,SAAS,EACT,QAAS,qFACX,CAIF,OAAM,CAAA,EAAA,EAAA,aAAA,AAAY,EAAE,EAAK,EAAE,CAC7B,CAAE,MAAO,EAAO,CAEd,OAHM,AAEN,QAAQ,KAAK,CAAC,gBAAiB,GACxB,CACL,SAAS,EACT,QAAS,+BACX,CACF,CAOA,MAFA,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,cAEF,CAAE,SAAS,EAAM,CAFxB,OAEiC,gBAAiB,CACpD,CAEO,eAAe,EAAM,CAAgB,CAAE,CAAkB,EAE9D,IAAM,EAAkB,EAAA,eAAe,CAAC,SAAS,CAAC,CAChD,MAAO,EAAS,EADM,CACH,CAAC,SACpB,SAAU,EAAS,GAAG,CAAC,WACzB,GAGA,GAAI,CAAC,EAAgB,OAAO,CAC1B,CAD4B,KACrB,CAEL,SAAS,EACT,QAAS,uBAET,YAAa,EAAgB,KAAK,CAAC,OAAO,GAAG,WAC/C,AAD0D,EAI5D,GAAM,OAAE,CAAK,UAAE,CAAQ,CAAE,CAAG,EAAgB,IAAI,CAEhD,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAI,OAAE,CAAK,CAAE,CAAG,IAHP,EAGa,EACjC,IAAI,CAAC,WACL,MAAM,CAAC,+BACP,EAAE,CAAC,QAAS,GACZ,MAAM,GAET,GAAI,GAAS,CAAC,GAUV,CAFoB,AAEnB,EAVe,IAQU,EAAA,OAAM,CAAC,CAEf,MAFsB,CAAC,EAAU,EAAK,aAAa,EAPvE,EAO4B,IAPrB,CACL,SAAS,EACT,QAAS,4BACX,EAcF,GAAI,CAAC,EAAK,QAAQ,CAChB,CADkB,KACX,CACL,SAAS,EACT,QAAS,qFACX,CAIF,OAAM,GAAA,EAAA,aAAA,AAAY,EAAE,EAAK,EAAE,CAC7B,CAAE,MAAO,EAAO,CAEd,OADA,AAFM,QAEE,KAAK,CAAC,eAAgB,GACvB,CACL,SAAS,EACT,QAAS,+BACX,CACF,CAMA,MAFA,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,cAEF,CAAE,SAAS,EAAM,CAFxB,OAEiC,gBAAiB,CACpD,CAEO,eAAe,IACpB,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAClB,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,KADH,IAER,CAEO,eAAe,EAHpB,AAGmC,CAAgB,CAAE,CAAkB,EACvE,IAAM,EAAkB,EAAS,GAAG,CAAC,mBAC/B,EAAc,EAAS,GAAG,CAAC,eAC3B,EAAkB,EAAS,GAAG,CAAC,mBAGrC,GAAI,CAAC,GAAmB,CAAC,GAAe,CAAC,EACvC,MAAO,CACL,QAFsD,CAE7C,EACT,QAAS,0BACX,EAGF,GAAI,IAAgB,EAClB,MAAO,CACL,QAFiC,CAExB,EACT,QAAS,6BACX,EAGF,GAAI,EAAY,MAAM,CAAG,EACvB,CAD0B,KACnB,CACL,SAAS,EACT,QAAS,kDACX,EAGF,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAM,CAAE,MAAO,CAAU,CAAE,CAAG,IAHrB,EAG2B,EAC/C,IAAI,CAAC,WACL,MAAM,CAAC,iBACP,EAAE,CAAC,KAAM,GACT,MAAM,GAET,GAAI,GAAc,CAAC,EACjB,MADyB,AAClB,CACL,QAAS,GACT,QAAS,mBACX,EAMF,GAAI,CAFoB,AAEnB,MAFyB,EAAA,OAAM,CAAC,CAEf,MAFsB,CAAC,EAAiB,EAAO,aAAa,EAGhF,EAH4B,IAGrB,CACL,SAAS,EACT,QAAS,gCACX,EAIF,IAAM,EAAoB,MAAM,EAAA,OAAM,CAAC,IAAI,CAAC,EAAa,IAGnD,CAAE,MAAO,CAAW,CAAE,CAAG,MAAM,EAClC,AAJ6B,IAIzB,CAAC,WACL,MAAM,CAAC,CAAE,cAAe,CAAkB,GAC1C,EAAE,CAAC,KAAM,GAEZ,GAAI,EAEF,OADA,IADe,IACP,KAAK,CAAC,yBAA0B,GACjC,CACL,SAAS,EACT,QAAS,4BACX,EAMF,OAFA,MAAM,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,GAEd,CACL,SAAS,EACT,MAJI,EAIK,gCACX,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,yBAA0B,GACjC,CACL,SAAS,EACT,QAAS,+BACX,CACF,CACF,2CA5RsB,EA0HA,EAwEA,EAKA,IAvMA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA0HA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAwEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAKA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA"}