module.exports={269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},520304:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasA11yProp:()=>f,mergeClasses:()=>e,toCamelCase:()=>c,toKebabCase:()=>b,toPascalCase:()=>d});let b=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase()),d=a=>{let b=c(a);return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim(),f=a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0}}},91640:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>d});var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}},252747:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(91640),f=a.i(520304);let b=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e.default,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:(0,f.mergeClasses)("lucide",h),...!i&&!(0,f.hasA11yProp)(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]]))}},274453:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(520304),f=a.i(252747);let b=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},h)=>(0,d.createElement)(f.default,{ref:h,iconNode:b,className:(0,e.mergeClasses)(`lucide-${(0,e.toKebabCase)((0,e.toPascalCase)(a))}`,`lucide-${a}`,c),...g}));return c.displayName=(0,e.toPascalCase)(a),c}}},161982:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))}},502549:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{let{createClientModuleProxy:b}=a.r(77624);a.n(b("[project]/node_modules/next/dist/client/app-dir/link.js"))}},331055:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(161982);var d=a.i(502549);a.n(d)},743226:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],c=(0,d.default)("arrow-left",b)}},299599:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ArrowLeft:()=>d.default});var d=a.i(743226)},448482:function(a){var{g:b,__dirname:c,m:d,e:e}=a},607873:a=>{var{g:b,__dirname:c}=a;a.n(a.i(28313))},305417:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(607873),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["terms",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/terms/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/terms/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},171206:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(607873),a.i(660874),a.i(715847),a.i(781045),a.i(305417)},844774:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(305417)},481530:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(171206);var d=a.i(844774)},572024:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]],c=(0,d.default)("stethoscope",b)}},848088:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Stethoscope:()=>d.default});var d=a.i(572024)},28313:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>h,metadata:()=>b});var d=a.i(129629),e=a.i(331055),f=a.i(299599),g=a.i(848088);let b={title:"Terms of Service - Celer AI",description:"Terms of Service for Celer AI medical documentation platform"};function h(){return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)(e.default,{href:"/",className:"inline-flex items-center text-teal-600 hover:text-teal-700 mb-4",children:[(0,d.jsx)(f.ArrowLeft,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)(g.Stethoscope,{className:"w-5 h-5 text-white"})}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-slate-800",children:"Terms of Service"})]}),(0,d.jsx)("p",{className:"text-slate-600",children:"Last updated: June 17, 2025"})]}),(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg border border-orange-200/50 p-8",children:(0,d.jsxs)("div",{className:"prose prose-slate max-w-none text-black",children:[(0,d.jsx)("h2",{children:"1. Acceptance of Terms"}),(0,d.jsx)("p",{children:'By accessing and using Celer AI ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.'}),(0,d.jsx)("h2",{children:"2. Description of Service"}),(0,d.jsx)("p",{children:"Celer AI is an AI-powered medical documentation platform designed to assist healthcare providers in generating consultation summaries, reports, and medical documentation. The Service is intended for use by licensed medical professionals only."}),(0,d.jsx)("h2",{children:"3. User Responsibilities"}),(0,d.jsx)("p",{children:"You agree to:"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Provide accurate and complete information when using the Service"}),(0,d.jsx)("li",{children:"Use the Service only for lawful purposes and in accordance with applicable medical regulations"}),(0,d.jsx)("li",{children:"Maintain the confidentiality of your account credentials"}),(0,d.jsx)("li",{children:"Review and verify all AI-generated content before using it in medical practice"}),(0,d.jsx)("li",{children:"Comply with all applicable laws, regulations, and professional standards"})]}),(0,d.jsx)("h2",{children:"4. Medical Disclaimer"}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"IMPORTANT:"})," Celer AI is a documentation tool only. It does not provide medical advice, diagnosis, or treatment recommendations. All AI-generated content must be reviewed, verified, and approved by qualified medical professionals before use. The Service is not a substitute for professional medical judgment, and users remain fully responsible for all medical decisions and patient care."]}),(0,d.jsx)("h2",{children:"5. Limitation of Liability"}),(0,d.jsx)("p",{children:"TO THE MAXIMUM EXTENT PERMITTED BY LAW, CELER AI AND ITS AFFILIATES SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR USE OF THE SERVICE."}),(0,d.jsx)("h2",{children:"6. Data Privacy and Security"}),(0,d.jsx)("p",{children:"We take data privacy seriously and implement appropriate security measures to protect your information. Please refer to our Privacy Policy for detailed information about how we collect, use, and protect your data."}),(0,d.jsx)("h2",{children:"7. Intellectual Property"}),(0,d.jsx)("p",{children:"The Service and its original content, features, and functionality are owned by Celer AI and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws."}),(0,d.jsx)("h2",{children:"8. Subscription and Payment Terms"}),(0,d.jsx)("p",{children:"Subscription fees are charged in advance on a monthly basis. All fees are non-refundable except as required by law. We reserve the right to change our pricing with 30 days' notice."}),(0,d.jsx)("h2",{children:"9. Termination"}),(0,d.jsx)("p",{children:"We may terminate or suspend your account and access to the Service immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms."}),(0,d.jsx)("h2",{children:"10. Governing Law"}),(0,d.jsx)("p",{children:"These Terms shall be interpreted and governed by the laws of India. Any disputes arising from these Terms shall be subject to the exclusive jurisdiction of the courts in India."}),(0,d.jsx)("h2",{children:"11. Changes to Terms"}),(0,d.jsx)("p",{children:"We reserve the right to modify these Terms at any time. We will notify users of any material changes via email or through the Service. Continued use of the Service after such modifications constitutes acceptance of the updated Terms."}),(0,d.jsx)("h2",{children:"12. Contact Information"}),(0,d.jsx)("p",{children:"If you have any questions about these Terms of Service, please contact us at:"}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:"Email: <EMAIL>"}),(0,d.jsx)("li",{children:"Phone: +91 **********"}),(0,d.jsx)("li",{children:"WhatsApp: +91 **********"})]}),(0,d.jsx)("div",{className:"mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-amber-800 mb-0",children:[(0,d.jsx)("strong",{children:"Professional Use Only:"})," This service is intended exclusively for licensed healthcare professionals. By using Celer AI, you confirm that you are a qualified medical practitioner authorized to provide medical care."]})})]})})]})})}}}};

//# sourceMappingURL=_eb39b80a._.js.map