{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/segment.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-status-code.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect.ts", "turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts", "turbopack:///[project]/node_modules/next/src/client/components/not-found.ts", "turbopack:///[project]/node_modules/next/src/client/components/forbidden.ts", "turbopack:///[project]/node_modules/next/src/client/components/unauthorized.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/node_modules/next/src/client/components/is-next-router-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.browser.ts", "turbopack:///[project]/node_modules/next/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/node_modules/next/src/server/lib/router-utils/is-postpone.ts", "turbopack:///[project]/node_modules/next/src/client/components/hooks-server-context.ts", "turbopack:///[project]/node_modules/next/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/node_modules/next/src/lib/metadata/metadata-constants.tsx", "turbopack:///[project]/node_modules/next/src/lib/scheduler.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/dynamic-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.server.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.react-server.ts", "turbopack:///[project]/node_modules/next/src/client/components/bailout-to-client-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n", "const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n", "import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["getSegmentValue", "segment", "Array", "isArray", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "TemporaryRedirect", "Error", "getStore", "isAction", "push", "replace", "PermanentRedirect", "HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "prefix", "httpStatus", "has", "notFound", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "unauthorized", "BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "constructor", "reason", "err", "isNextRouterError", "unstable_rethrow", "cause", "isHangingPromiseRejectionError", "makeHangingPromise", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "$$typeof", "DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "description", "StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "code", "METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "resolve", "then", "NEXT_RUNTIME", "nextTick", "setImmediate", "r", "Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "store", "workUnitStore", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "NODE_ENV", "prerenderStore", "workUnitAsyncStorage", "_store", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "controller", "abort", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "NEXT_PRERENDER_INTERRUPTED", "serverDynamic", "clientDynamic", "filter", "access", "map", "line", "AbortController", "x", "cacheSignal", "inputReady", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "ReadonlyURLSearchParams", "ReadonlyURLSearchParamsError", "URLSearchParams", "append", "delete", "sort", "bailoutToClientRendering", "ServerInsertedHTMLContext", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "PathnameContext", "router", "AppRouterContext", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "segmentValue", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "selectedLayoutSegment"], "mappings": "+EAEO,SAASA,EAAgBC,CAAgB,EAC9C,OAAOC,MAAMC,OAAO,CAACF,GAAWA,CAAO,CAAC,EAAE,CAAGA,CAC/C,0EAFgBD,kBAAAA,qCAAAA,qVC0BHI,mBAAmB,CAAA,kBAAnBA,GADAC,gBAAgB,CAAA,kBAAhBA,GAhBGC,4BAA4B,CAAA,kBAA5BA,GATAC,cAAc,CAAA,kBAAdA,GAKAC,sBAAsB,CAAA,kBAAtBA,uEALT,SAASD,EAAeN,CAAe,EAE5C,MAAOA,AAAe,OAAR,CAAC,EAAE,EAAYA,EAAQQ,QAAQ,CAAC,IAChD,CAEO,SAASD,EAAuBP,CAAe,EACpD,OAAOA,EAAQS,UAAU,CAAC,MAAoB,cAAZT,CACpC,CAEO,SAASK,EACdL,CAAgB,CAChBU,CAA2D,EAI3D,GAFsBV,CAElBW,CAF0BC,QAAQ,CAACR,GAEpB,CACjB,IAAMS,EAAmBC,KAAKC,SAAS,CAACL,GACxC,MAA4B,OAArBG,EACHT,EAAmB,IAAMS,EACzBT,CACN,CAEA,OAAOJ,CACT,CAEO,IAAMI,EAAmB,WACnBD,EAAsB,uCC5BvBa,kBAAAA,kGAAAA,qBAAAA,qCAAAA,KAAL,IAAKA,qBAAAA,WAAAA,GAAAA,gGAAAA,qVCECC,mBAAmB,CAAA,kBAAnBA,GAEDC,YAAY,CAAA,kBAAZA,GAgBIC,eAAe,CAAA,kBAAfA,+EApBmB,CAAA,CAAA,IAAA,IAEtBF,EAAsB,gBAE5B,IAAKC,IAAAA,WAAAA,CAAAA,UAAAA,GAAAA,aAAAA,GAgBL,SAASC,EAAgBC,CAAc,EAC5C,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UACxB,AADA,OAAOA,EAAMC,MAAM,CAEnB,OAAO,EAGT,IAAMA,EAASD,EAAMC,MAAM,CAACC,KAAK,CAAC,KAC5B,CAACC,EAAWC,EAAK,CAAGH,EACpBI,EAAcJ,EAAOK,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAGvCG,EAAaC,OAFJV,AAEWO,EAFJC,EAAE,CAAC,CAAC,IAI1B,OACEN,IAAcN,IACJ,YAATO,GAA+B,IAA/BA,KAAsBA,CAAS,CAAK,EACd,UAAvB,OAAOC,GACP,CAACO,MAAMF,IACPA,KAAcd,EAAAA,kBAAkB,AAEpC,kVC7BgBiB,gBAAgB,CAAA,kBAAhBA,GA6EAC,8BAA8B,CAAA,kBAA9BA,GARAC,wBAAwB,CAAA,kBAAxBA,GARAC,uBAAuB,CAAA,kBAAvBA,GAhBAC,iBAAiB,CAAA,kBAAjBA,GAvBAC,QAAQ,CAAA,kBAARA,+EArCmB,CAAA,CAAA,IAAA,QAM5B,CAAA,CAAA,IAAA,IAEDC,EACc,aAAlB,OAAOC,OAEDC,EAAQ,CAAA,CAAA,IAAA,IACRF,kBAAkB,MACpBG,EAEC,SAAST,EACdU,CAAW,CACXnB,CAAkB,CAClBM,CAAqE,EAArEA,KAAAA,IAAAA,GAAAA,GAAiCd,EAAAA,kBAAkB,CAAC4B,iBAAAA,AAAiB,EAErE,IAAMxB,EAAQ,OAAA,cAA8B,CAA9B,AAAIyB,MAAM5B,EAAAA,mBAAmB,EAA7B,oBAAA,OAAA,mBAAA,gBAAA,CAA6B,GAE3C,OADAG,EAAMC,MAAM,CAAMJ,EAAAA,mBAAmB,CAAC,IAAGO,EAAK,IAAGmB,EAAI,IAAGb,EAAW,IAC5DV,CACT,CAcO,SAASkB,EAEdK,CAAW,CACXnB,CAAmB,IAFnB,EAISe,CAIT,OAJAf,MAAAA,CAAAA,GAAAA,EAASe,CAAAA,IAJkB,EAIlBA,CAAAA,EAAAA,AAA4B,GAA5BA,IAAAA,EAAAA,EAAoBO,QAAQ,EAAA,CAAA,CAAA,KAAA,EAA5BP,EAAgCQ,QAAQ,EAC7C7B,EAAAA,YAAY,CAAC8B,IAAI,CACjB9B,EAAAA,YAAY,CAAC+B,OAAAA,AAAO,EAElBhB,EAAiBU,EAAKnB,EAAMR,EAAAA,kBAAkB,CAAC4B,iBAAiB,CACxE,CAaO,SAASP,EAEdM,CAAW,CACXnB,CAAyC,EAEzC,MAFAA,KAAAA,AAFA,IAEAA,IAAAA,EAAqBN,EAAAA,YAAY,CAAC+B,EAFP,KAEOA,AAAO,EAEnChB,EAAiBU,EAAKnB,EAAMR,EAAAA,kBAAkB,CAACkC,iBAAiB,CACxE,CAUO,SAASd,EAAwBhB,CAAc,QAC/CD,AAAL,CAAKA,EAAAA,CAAD,CAACA,eAAAA,AAAe,EAACC,GAIdA,EAAMC,GAJgB,GAIV,CAACC,KAAK,CAAC,KAAKI,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAJb,IAKtC,CAEO,SAASQ,EAAyBf,CAAoB,EAC3D,GAAI,CAACD,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAIyB,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOzB,EAAMC,MAAM,CAACC,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,AACtC,CAEO,SAASY,EAA+Bd,CAAoB,EACjE,GAAI,CAACD,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAIyB,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOd,OAAOX,EAAMC,MAAM,CAACC,KAAK,CAAC,KAAKO,EAAE,CAAC,CAAC,GAC5C,kVClGasB,qBAAqB,CAAA,kBAArBA,GAQAC,8BAA8B,CAAA,kBAA9BA,GAuCGC,kCAAkC,CAAA,kBAAlCA,GAPAC,2BAA2B,CAAA,kBAA3BA,GAnBAC,yBAAyB,CAAA,kBAAzBA,uEArBT,IAAMJ,EAAwB,CACnCK,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAEMC,EAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX,IAE/BC,EAAiC,2BAavC,SAASG,EACdnC,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACG,AAAVA,UACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAET,GAAM,CAAC0C,EAAQC,EAAW,CAAG5C,EAAMC,MAAM,CAACC,KAAK,CAAC,KAEhD,OACEyC,IAAWX,GACXO,EAAcM,GAAG,CAAClC,OAAOiC,GAE7B,CAEO,SAASV,EACdlC,CAA8B,EAG9B,OAAOW,OADYX,AACL4C,EADW3C,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAAS+B,EACdzB,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,mWCtCgBsC,WAAAA,qCAAAA,KAFhB,IAAMC,EAAU,GAAEf,EAjBX,CAAA,CAAA,IAAA,IAiBWA,8BAA8B,CAAC,OAE1C,SAASc,IAEd,IAAM9C,EAAQ,OAAA,cAAiB,CAAjB,AAAIyB,MAAMsB,GAAV,oBAAA,OAAA,kBAAA,gBAAA,EAAgB,EAG9B,OAFE/C,EAAkCC,MAAM,CAAG8C,EAEvC/C,CACR,yRCPO,SAASgD,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAIvB,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,0EAXgBuB,YAAAA,qCAAAA,KAFEhB,EAhBX,CAAA,CAAA,IAAA,IAgBWA,8BAA8B,GAAC,qRCG1C,SAASoB,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI3B,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,0EAXgB2B,eAAAA,qCAAAA,KAFEpB,EAjBX,CAAA,CAAA,IAAA,IAiBWA,8BAA8B,GAAC,8UChBpCqB,iBAAiB,CAAA,kBAAjBA,GASGC,mBAAmB,CAAA,kBAAnBA,uEAZhB,IAAMC,EAAiB,kCAGhB,OAAMF,UAA0B5B,MAGrC+B,YAA4BC,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZxD,MAAAA,CAASsD,CAIzB,CACF,CAGO,SAASD,EAAoBI,CAAY,QAC3B,AAAnB,UAAI,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIzD,CAJwD,KAIlD,GAAKsD,CACxB,4ICRgBI,oBAAAA,qCAAAA,aART,CAAA,CAAA,IAAA,QAC6C,CAAA,CAAA,IAAA,IAO7C,SAASA,EACd3D,CAAc,EAEd,MAAOD,GAAAA,EAAAA,eAAAA,AAAe,EAACC,IAAUmC,CAAAA,EAAAA,EAAAA,yBAAAA,AAAyB,EAACnC,EAC7D,mWCZgB4D,mBAAAA,qCAAAA,AAAT,SAASA,EAAiB5D,CAAc,EAC7C,GAAI2D,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC3D,IAAUsD,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACtD,GAClD,KAD0D,CACpDA,EAGJA,aAAiByB,OAAS,UAAWzB,GACvC4D,EAAiB5D,EAD6B,AACvB6D,KAAK,CAEhC,aAXoC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,oVCDlBC,8BAA8B,CAAA,kBAA9BA,GAgCAC,kBAAkB,CAAA,kBAAlBA,uEAhCT,SAASD,EACdJ,CAAY,QAEO,AAAnB,UAAI,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIzD,CAJwD,KAIlD,GAAK+D,CACxB,CAEA,IAAMA,EAA4B,2BAElC,OAAMC,UAAqCxC,MAGzC+B,YAA4BU,CAAkB,CAAE,CAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,qJAAqJ,CAAC,EAAA,IAAA,CAFnRA,UAAAA,CAAAA,EAAAA,IAAAA,CAFZjE,MAAAA,CAAS+D,CAMzB,CACF,CAGA,IAAMG,EAAyB,IAAIC,QAS5B,SAASL,EACdM,CAAmB,CACnBH,CAAkB,EAElB,GAAIG,EAAOC,OAAO,CAChB,CADkB,MACXC,QAAQC,MAAM,CAAC,IAAIP,EAA6BC,GAClD,EACL,IAAMO,EAAiB,IAAIF,QAAW,CAACG,EAAGF,KACxC,IAAMG,EAAiBH,EAAOI,IAAI,CAChC,KACA,IAAIX,EAA6BC,IAE/BW,EAAmBV,EAAuBW,GAAG,CAACT,GAClD,GAAIQ,EACFA,EAAiBjD,IAAI,CAAC+C,OACjB,CACL,CAHoB,GAGdI,EAAY,CAACJ,EAAe,CAClCR,EAAuBa,GAAG,CAACX,EAAQU,GACnCV,EAAOY,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUI,MAAM,CAAED,IAAK,AACzCH,CAAS,CAACG,EAAE,EAEhB,EACA,CAAEE,MAAM,CAAK,EAEjB,CACF,GAKA,OADAX,EAAeY,KAAK,CAACC,GACdb,CACT,CACF,CAEA,SAASa,IAAgB,6ICnETC,aAAAA,qCAAAA,KAFhB,IAAMC,EAA8BC,OAAOC,GAAG,CAAC,kBAExC,SAASH,EAAWvF,CAAU,EACnC,MACmB,UAAjB,OAAOA,GACG,OAAVA,GACAA,EAAM2F,QAAQ,GAAKH,CAEvB,4HCNaI,kBAAkB,CAAA,kBAAlBA,GAQGC,oBAAoB,CAAA,kBAApBA,uEAVhB,IAAMC,EAAqB,sBAEpB,OAAMF,UAA2BnE,MAGtC+B,YAA4BuC,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5B9F,MAAAA,CAAoC6F,CAIpC,CACF,CAEO,SAASD,EAAqBnC,CAAY,QAC/C,AACiB,UAAf,OAAOA,GACC,OAARA,CACA,CAAE,CAAA,WAAYA,GAAE,AACM,UAAtB,AACA,OADOA,EAAIzD,MAAM,EAKZyD,EAAIzD,MAAM,GAAK6F,CACxB,kVCnBaE,qBAAqB,CAAA,kBAArBA,GAIGC,uBAAuB,CAAA,kBAAvBA,uEANhB,IAAMC,EAA0B,yBAEzB,OAAMF,UAA8BvE,wBAApC,KAAA,IAAA,GAAA,IAAA,CACW0E,IAAAA,CAAOD,EACzB,CAEO,SAASD,EACdjG,CAAc,QAEO,AAArB,UAAI,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAJ4D,AAItDmG,GAJ0D,CAItD,GAAKD,CACxB,kVCdaE,sBAAsB,CAAA,kBAAtBA,GAEAC,oBAAoB,CAAA,kBAApBA,GADAC,sBAAsB,CAAA,kBAAtBA,uEADN,IAAMF,EAAyB,6BACzBE,EAAyB,6BACzBD,EAAuB,qJC0CpBE,cAAc,CAAA,kBAAdA,GAbHC,iBAAiB,CAAA,kBAAjBA,GAtBAC,kBAAkB,CAAA,kBAAlBA,GAgDGC,6BAA6B,CAAA,kBAA7BA,uEAhDT,IAAMD,EAAqB,AAAWE,IAO3CpC,QAAQqC,OAAO,GAAGC,IAAI,CAAC,KAInB5D,QAAQ8D,QAAQ,CAACJ,EAErB,EACF,EAQaH,EAAoB,AAAWG,IAIxCK,aAAaL,EAEjB,EAOO,SAASJ,IACd,OAAO,IAAIhC,QAAc,AAACqC,GAAYJ,EAAkBI,GAC1D,CAWO,SAASF,IAIZ,OAAO,IAAInC,QAAQ,AAAC0C,GAAMD,aAAaC,GAE3C,uDC3CC,uEAoVeC,QAAQ,CAAA,kBAARA,GA3CAC,2CAA2C,CAAA,kBAA3CA,GAlCAC,kCAAkC,CAAA,kBAAlCA,GAuKAC,mBAAmB,CAAA,kBAAnBA,GA4GAC,qBAAqB,CAAA,kBAArBA,GAtGAC,oBAAoB,CAAA,kBAApBA,GAhXAC,0BAA0B,CAAA,kBAA1BA,GAWAC,4BAA4B,CAAA,kBAA5BA,GAmbAC,6BAA6B,CAAA,kBAA7BA,GAjBAC,0BAA0B,CAAA,kBAA1BA,GAlDAC,wBAAwB,CAAA,kBAAxBA,GAtWAC,qBAAqB,CAAA,kBAArBA,GAgSAC,iBAAiB,CAAA,kBAAjBA,GAwCAC,2BAA2B,CAAA,kBAA3BA,GA3TAC,yBAAyB,CAAA,kBAAzBA,GAuPAC,oBAAoB,CAAA,kBAApBA,GAgSAC,wBAAwB,CAAA,kBAAxBA,GAvcAC,gCAAgC,CAAA,kBAAhCA,GA6ZAC,yBAAyB,CAAA,kBAAzBA,GApYAC,+BAA+B,CAAA,kBAA/BA,GAzCAC,0BAA0B,CAAA,kBAA1BA,GAiHAC,qCAAqC,CAAA,kBAArCA,GAmDHC,sCAAsC,CAAA,kBAAtCA,GA+NGC,qBAAqB,CAAA,kBAArBA,kFA9hBE,CAAA,CAAA,IAAA,qCAEiB,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,OACE,CAAA,CAAA,IAAA,QAK5B,CAAA,CAAA,IAAA,QAC4B,CAAA,CAAA,IAAA,IAE7BC,EAAiD,YAAnC,OAAOC,EAAAA,OAAK,CAACC,iBAAiB,CA2C3C,SAASpB,EACdqB,CAA2C,EAE3C,MAAO,CACLA,yBACAC,gBAAiB,EAAE,CACnBC,2BAAuBzH,EACvB0H,0BAA2B,IAC7B,CACF,CAEO,SAASvB,IACd,MAAO,CACLwB,qBAAqB,EACrBC,oBAAoB,EACpBC,mBAAoB,GACpBC,sBAAsB,EACtBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASxB,EACdyB,CAAmC,MAE5BA,EAAP,OAAA,AAAuC,OAAhCA,EAAAA,EAAcR,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCQ,EAAkCpF,UAAU,AACrD,CASO,SAAS8D,EACduB,CAAgB,CAChBC,CAAuE,CACvEtF,CAAkB,EAElB,GAAIsF,KAEuB,UAAvBA,EAAcpJ,IAAI,EACK,kBACvB,CADAoJ,EAAcpJ,IAAI,AAHlBoJ,GAAe,CAefD,EAAME,YAAY,GAAIF,EAAMG,WAAW,EAAE,AAE7C,GAAIH,EAAMI,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAI3D,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEuD,EAAMK,KAAK,CAAC,8EAA8E,EAAE1F,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,eAAA,EAEN,GAGF,GAAIsF,GACF,GAA2B,SADV,QAC2B,CAAxCA,EAAcpJ,IAAI,CACpB6H,EACEsB,EAAMK,KAAK,CACX1F,EACAsF,EAAcK,eAAe,OAE1B,GAA2B,qBAAvBL,EAAcpJ,IAAI,CAAyB,CACpDoJ,EAAcM,UAAU,CAAG,EAG3B,IAAMpG,EAAM,OAAA,cAEX,CAFW,IAAIkC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE2D,EAAMK,KAAK,CAAC,iDAAiD,EAAE1F,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHAqF,EAAMQ,uBAAuB,CAAG7F,EAChCqF,EAAMS,iBAAiB,CAAGtG,EAAIuG,KAAK,CAE7BvG,CACR,CAMA,EAEJ,CAUO,GAlBI,IACLT,EAiBUqF,EACdiB,CAAgB,CAChBrF,CAAkB,CAnBNhB,CAqBZ,EArBe,CAACgH,CAqBVC,EAAiBC,EAAAA,GArBC,KAAK,UAGzB,EAkBuC,CAAC1I,IApBxC8H,IAoBgD,GAC/CW,GAAkBA,AAAwB,OApB3CX,UAoB4D,GAA1BpJ,CApBpBA,GAoBwB,CApBpB,CAsBtB6H,EAAqBsB,EAtBM,AAsBAK,KAAK,CAAE1F,EAAYiG,EAAeN,eAAe,CAC9E,CAQO,SAAS1B,EACdjE,CAAkB,CAClBqF,CAAgB,CAChBY,CAAoC,EAGpC,IAAMzG,EAAM,OAAA,cAEX,CAFW,IAAIkC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE2D,EAAMK,KAAK,CAAC,mDAAmD,EAAE1F,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAOA,OALAiG,EAAeL,UAAU,CAAG,EAE5BP,EAAMQ,uBAAuB,CAAG7F,EAChCqF,EAAMS,iBAAiB,CAAGtG,EAAIuG,KAAK,CAE7BvG,CACR,CASO,SAAS2E,EACdgC,CAAiB,CACjBb,CAAmC,EAE/BA,GAEuB,UAAvBA,EAFe,AAEDpJ,IAAI,EACK,kBACvB,CADAoJ,EAAcpJ,IAAI,EAQlBoJ,CAAuB,gBAATpJ,IAAI,EACK,qBAAvBoJ,EAAcpJ,IAAI,AAAK,GACvB,CACAoJ,EAAcM,UAAU,EAAG,CASjC,CAKA,SAASQ,EACPV,CAAa,CACb1F,CAAkB,CAClBiG,CAAoC,EAIpC,IAAMnK,EAAQuK,EAFC,CAAC,MAAM,EAAEX,EAAM,mBAEgBnG,8CAFiD,EAAES,EAAW,CAAC,CAAC,EAI9GiG,EAAeK,UAAU,CAACC,KAAK,CAACzK,GAEhC,IAAM6J,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBf,YADG,GACY,CAAClH,IAAI,CAAC,CAGnCqI,MAAOJ,EAAgBhB,sBAAsB,CACzC,AAAIpH,QAAQwI,KAAK,MACjB3I,EACJ4C,YACF,EAEJ,CAEO,SAASkD,EACdwC,CAAa,CACb1F,CAAkB,CAClBwG,CAAqB,CACrBP,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBb,KADD,oBAC0B,GAC3Ca,EAAgBd,qBAAqB,CAAG7E,EACxC2F,EAAgBb,yBAAyB,CAAG0B,GAGhDJ,EAAoCV,EAAO1F,EAAYiG,EACzD,CAEO,SAAS5B,EACdoC,CAA0B,EAI1BA,EAAaC,cAAc,EAAG,CAChC,CAYO,SAASzD,EACdyC,CAAa,CACb1F,CAAkB,CAClBwG,CAAqB,CACrBP,CAAoC,EAGpC,IAAgC,IADRA,AACpBU,EADmCL,UAAU,CAACnG,MAAM,CACpCC,OAAO,CAAY,CAMrC,IAAMuF,EAAkBM,EAAeN,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBb,KADD,oBAC0B,GAC3Ca,EAAgBd,qBAAqB,CAAG7E,EACxC2F,EAAgBb,yBAAyB,CAAG0B,GACV,IAA9BP,EAAeW,AAAqB,UAAX,GAG3BjB,EAAgBkB,iBAAiB,EAAG,CAAA,GAI1CT,EAAoCV,EAAO1F,EAAYiG,EACzD,CACA,MAAMI,EACJ,CAAC,MAAM,EAAEX,EAAM,iEAAiE,EAAE1F,EAAW,CAAC,CAAC,CAEnG,CAGO,IAAMsE,EACXD,EASK,SAASrB,EAAS,QAAEzD,CAAM,OAAEmG,CAAK,CAAiB,EACvD,IAAMO,EAAiBC,EAAAA,oBAAoB,CAAC1I,QAAQ,GAKpDuG,EAAqB2B,EAAOnG,EAH1B0G,GAA0C,GAGRN,eAHhBM,EAAe/J,IAAI,CACjC+J,EAAeN,eAAe,CAC9B,KAER,CAEO,SAAS5B,EACd2B,CAAa,CACb1F,CAAkB,CAClB2F,CAA4C,EAE5CmB,IACInB,GACFA,EAAgBf,YADG,GACY,CAAClH,IAAI,CAAC,CAGnCqI,MAAOJ,EAAgBhB,sBAAsB,CACzC,AAAIpH,QAAQwI,KAAK,MACjB3I,aACJ4C,CACF,GAGFyE,EAAAA,OAAK,CAACC,iBAAiB,CAACqC,EAAqBrB,EAAO1F,GACtD,CAEA,SAAS+G,EAAqBrB,CAAa,CAAE1F,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAE0F,EAAM,iEAAiE,EAAE1F,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKW4D,EAAkBpE,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACgC,UAAhC,AACA,OADQA,EAAYwH,OAAO,EAEpBC,EAAyBzH,EAXgD,AAWpCwH,CAXqC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASC,EAAwB1H,CAAc,EAC7C,OACEA,EAAOjE,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnFiE,EAAOjE,QAAQ,CACb,gEAGN,CAEA,IAAoE,IAAhE2L,EAAwBF,CAA+C,CAA1B,MAAO,QACtD,MAAM,OAAA,cAEL,CAFSxJ,AAAJ,MACJ,0FADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAM2J,EAA6B,6BAEnC,SAASb,EAAgCW,CAAe,EACtD,IAAMlL,EAAQ,OAAA,cAAkB,CAAlB,AAAIyB,MAAMyJ,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADElL,EAAcC,MAAM,CAAGmL,EAClBpL,CACT,CAMO,SAAS+H,EACd/H,CAAc,EAEd,MACmB,AAAjB,iBAAOA,GACG,OAAVA,GACCA,EAAcC,MAAM,GAAKmL,GAC1B,SAAUpL,GACV,YAAaA,GACbA,aAAiByB,KAErB,CAEO,SAAS4F,EACdyB,CAAqC,EAErC,OAAOA,EAAgB3D,MAAM,CAAG,CAClC,CAEO,SAASoC,EACd8D,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAcvC,eAAe,CAAClH,IAAI,IAAI0J,EAAcxC,eAAe,EAC5DuC,EAAcvC,eAAe,AACtC,CAEO,SAASlB,EACdkB,CAAqC,EAErC,OAAOA,EACJyC,MAAM,CACL,AAACC,GACyB,UAAxB,OAAOA,EAAOvB,KAAK,EAAiBuB,EAAOvB,KAAK,CAAC9E,MAAM,CAAG,GAE7DsG,GAAG,CAAC,CAAC,YAAEvH,CAAU,CAAE+F,OAAK,CAAE,IACzBA,EAAQA,EACL/J,KAAK,CAAC,MAINI,AAHD,KAGM,CAAC,GACNiL,MAAM,CAAC,AAACG,KAEHA,EAAKlM,QAAQ,CAAC,uBAAuB,AAKrCkM,EAAKlM,QAAQ,CAAC,MAXoD,aAWjC,AAKjCkM,EAAKlM,QAAQ,CAAC,YAAY,CAM/Be,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAE2D,EAAW;AAAG,EAAE+F,EAAAA,CAAO,EAEjE,CAEA,SAASe,IACP,GAAI,CAACtC,EACH,MAAM,KADU,EACV,cAEL,CAFK,AAAIjH,MACR,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEJ,CAMO,SAASkG,EAA2BlE,CAAc,EACvDuH,IACA,IAAMR,EAAa,IAAImB,gBAEvB,GAAI,CACFhD,EAAAA,OAAK,CAACC,iBAAiB,CAACnF,EAC1B,CAAE,MAAOmI,EAAY,CACnBpB,EAAWC,KAAK,CAACmB,EACnB,CACA,OAAOpB,EAAWnG,MAAM,AAC1B,CAOO,SAASqD,EACd8B,CAAmC,EAEnC,IAAMgB,EAAa,IAAImB,gBAkBvB,OAhBInC,EAAcqC,WAAW,CAI3BrC,CAJ6B,CAIfqC,WAAW,CAACC,UAAU,GAAGjF,IAAI,CAAC,KAC1C2D,EAAWC,KAAK,EAClB,GAOAhE,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC,IAAM+D,EAAWC,KAAK,IAGpCD,EAAWnG,MAAM,AAC1B,CAEO,SAASiD,EACdpD,CAAkB,CAClBiG,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBf,YADG,GACY,CAAClH,IAAI,CAAC,CACnCqI,MAAOJ,EAAgBhB,sBAAsB,CACzC,AAAIpH,QAAQwI,KAAK,MACjB3I,aACJ4C,CACF,EAEJ,CAEO,SAASuE,EAAsBvE,CAAkB,EACtD,IAAM6H,EAAYC,EAAAA,gBAAgB,CAACtK,QAAQ,GAE3C,GACEqK,GACAA,EAAUE,kBAAkB,EAC5BF,EAAUG,mBAAmB,EAC7BH,EAAUG,mBAAmB,CAACC,IAAI,CAAG,EACrC,CAGA,IAAM3C,EAAgBY,EAAAA,oBAAoB,CAAC1I,QAAQ,GAC/C8H,IAEyB,WAFV,EAEuB,CAApCA,EAAcpJ,IAAI,CAIpBuI,EAAAA,OAAK,CAACyD,GAAG,CAACrI,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACyF,EAAc6C,YAAY,CAAEnI,IACzB,iBAAiB,CAAxCsF,EAAcpJ,IAAI,CAE3B6H,EACE8D,EAAUnC,KAAK,CACf1F,EACAsF,EAAcK,eAAe,EAEC,oBAAoB,CAA3CL,EAAcpJ,IAAI,EAC3B+H,EAAiCjE,EAAY6H,EAAWvC,GAG9D,CACF,CAEA,IAAM8C,EAAmB,mCACnBC,EAAmB,AAAIC,OAC3B,CAAC,UAAU,EAAEpG,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCqG,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAElG,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCoG,EAAiB,AAAIF,OAAO,CAAC,UAAU,EAAEnG,EAAAA,oBAAoB,CAAC,QAAQ,CAAC,EAEtE,SAAS+B,EACdwB,CAAa,CACb+C,CAAsB,CACtBC,CAAyC,CACzCvB,CAAmC,CACnCC,CAAmC,EAEnC,IAAIoB,EAAeG,IAAI,CAACF,IAGjB,GAAIJ,EAAiBM,IAAI,CAACF,GAHQ,AAGS,CAChDC,EAAkB1D,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAIuD,EAAiBI,IAAI,CAACF,GAAiB,CAChDC,EAAkBzD,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAImD,EAAiBO,IAAI,CAACF,GAAiB,CAChDC,EAAkB3D,mBAAmB,CAAG,GACxC,MACF,MAAO,GACLoC,EAAcrC,yBAAyB,EACvCsC,EAActC,yBAAyB,CACvC,CACA4D,EAAkBxD,oBAAoB,EAAG,EACzC,MACF,KAAO,CAEL,IAAMpJ,EAAQ8M,AAMlB,SACE5B,AADO4B,CACQ,CACfH,CAAsB,EAEtB,IAAM3M,EAAQ,OAAA,GAVgCkL,WAUd,CAAlB,AAAIzJ,MAAMyJ,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADAlL,EAAMiK,KAAK,CAAG,UAAYiB,EAAUyB,EAC7B3M,CACT,EAdoB,CAAC,OAAO,EAAE4J,EAAM,+UAA+U,CAAC,CAC3T+C,GACrDC,EAAkBvD,aAAa,CAACzH,IAAI,CAAC5B,GACrC,MACF,EACF,CAWO,SAASkI,EACd0B,CAAa,CACbgD,CAAyC,CACzCvB,CAAmC,CACnCC,CAAmC,MAE/ByB,EACAC,EACAC,EAeJ,GAdI5B,EAAcrC,yBAAyB,EAAE,AAC3C+D,EAAY1B,EAAcrC,yBAAyB,CACnDgE,EAAiB3B,EAActC,qBAAqB,CACpDkE,GAAiD,IAApC5B,EAAcN,iBAAiB,EACnCO,EAActC,yBAAyB,EAAE,AAClD+D,EAAYzB,EAActC,yBAAyB,CACnDgE,EAAiB1B,EAAcvC,qBAAqB,CACpDkE,EAAiD,KAApC3B,EAAcP,iBAAiB,GAE5CgC,EAAY,KACZC,OAAiB1L,EACjB2L,GAAa,GAGXL,EAAkBxD,oBAAoB,EAAI2D,EAO5C,MANI,AAACE,GAGHC,AAJqD,QAI7ClN,CAHO,IAGF,CAAC+M,GAGV,IAAI/G,EAAAA,qBAAqB,CAGjC,IAAMqD,EAAgBuD,EAAkBvD,aAAa,CACrD,GAAIA,EAAclE,MAAM,CAAE,CACxB,IAAK,IAAID,EAAI,EAAGA,EAAImE,EAAclE,MAAM,CAAED,IAAK,AAC7CgI,QAAQlN,KAAK,CAACqJ,CAAa,CAACnE,EAAE,CAGhC,OAAM,IAAIc,EAAAA,qBAAqB,AACjC,CAEA,GAAI,CAAC4G,EAAkB3D,mBAAmB,EAAE,AAC1C,GAAI2D,EAAkB1D,kBAAkB,CAAE,CACxC,GAAI6D,EAEF,MADAG,GADa,KACLlN,KAAK,CAAC+M,GACR,OAAA,cAEL,CAFK,IAAI/G,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE4D,EAAM,oEAAoE,EAAEoD,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAIhH,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE4D,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,kBAAA,iBAAA,CAEN,EACF,MAAO,GAAIgD,EAAkBzD,kBAAkB,CAAE,CAC/C,GAAI4D,EAEF,MADAG,GADa,KACLlN,KAAK,CAAC+M,GACR,OAAA,cAEL,CAFK,IAAI/G,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE4D,EAAM,oEAAoE,EAAEoD,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAIhH,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE4D,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GACF,CAEJ,6IC3sBgBhG,mBAAAA,qCAAAA,AAAT,SAASA,EAAiB5D,CAAc,EAC7C,GACE2D,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC3D,IAClBsD,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACtD,IACpB6F,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAC7F,IACrB8H,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC9H,IAClBuF,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACvF,IACX8D,GAAAA,EAAAA,8BAAAA,AAA8B,EAAC9D,GAE/B,KADA,CACMA,EAGJA,aAAiByB,OAAS,UAAWzB,GACvC4D,EAAiB5D,EAD6B,AACvB6D,KAAK,CAEhC,aAtB+C,CAAA,CAAA,IAAA,QACpB,CAAA,CAAA,IAAA,QACS,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,OACA,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,gRCCpC,sFACYD,mBAAAA,qCAAAA,KAAN,IAAMA,EACO,aAAlB,OAAOxC,OAEDC,EAAQ,CAAA,CAAA,IAAA,IACRuC,gBAAgB,CAEhBvC,EAAQ,CAAA,CAAA,IAAA,IACRuC,gBAAgB,4QCdV,qEAkCLuJ,uBAAuB,CAAA,kBAAvBA,GALArN,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAEZkD,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTF,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAFE7B,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EAA3BC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIRkC,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EACZQ,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,8EALmB,CAAA,CAAA,IAAA,QACf,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,GAhCjC,OAAMwJ,UAAqC3L,MACzC+B,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAM2J,UAAgCE,gBAEpCC,QAAS,CACP,MAAM,IAAIF,CACZ,CAEAG,QAAS,CACP,MAAM,IAAIH,CACZ,CAEApI,KAAM,CACJ,MAAM,IAAIoI,CACZ,CAEAI,MAAO,CACL,MAAM,IAAIJ,CACZ,CACF,mWCvBgBK,2BAAAA,qCAAAA,aAHkB,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,GAE1B,SAASA,EAAyBhK,CAAc,EACrD,IAAMsI,EAAYC,EAAAA,gBAAgB,CAACtK,QAAQ,GAE3C,GAAIqK,CAAAA,OAAAA,GAAAA,EAAAA,AAAWrC,WAAAA,AAAW,EAAE,EAExBqC,MAAAA,EAAAA,KAAAA,EAAAA,EAAWE,kBAAAA,AAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,IAAI5I,EAAAA,iBAAiB,CAACI,GAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAA4B,EACvE,kVCiRE0J,uBAAuB,CAAA,kBAAvBA,EAAAA,uBAAuB,EADvBrN,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EApLZ4N,yBAAyB,CAAA,kBAAzBA,EAAAA,yBAAyB,EAgLzB1K,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTF,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIR7B,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EADjBC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EADRkC,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAKZQ,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,EApIF+J,SAAS,CAAA,kBAATA,GA5DAC,WAAW,CAAA,kBAAXA,GAiCAC,SAAS,CAAA,kBAATA,GA9EAC,eAAe,CAAA,kBAAfA,GA6MAC,wBAAwB,CAAA,kBAAxBA,GA/BAC,yBAAyB,CAAA,kBAAzBA,GAtHdC,qBAAqB,CAAA,kBAArBA,EAAAA,qBAAqB,8EAnGa,CAAA,CAAA,IAAA,QAK7B,CAAA,CAAA,IAAA,QAKA,CAAA,CAAA,IAAA,QACyB,CAAA,CAAA,IAAA,QACsB,CAAA,CAAA,IAAA,QACd,CAAA,CAAA,IAAA,OAuFjC,CAAA,CAAA,IAAA,IArFDxF,EACc,aAAlB,OAAOrH,OAEDC,EAAQ,CAAA,CAAA,IAAA,IACRoH,qBAAqB,MACvBnH,EAuBC,SAASwM,IACd,IAAMxO,EAAe4O,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,mBAAmB,EAK7CC,EAAuBC,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,IACnC,AAAK/O,EAME,EANH,EAMO6N,EAAAA,MANQ,iBAMe,CAAC7N,GAH1B,KAIR,CAACA,EAAa,EAEjB,GAAsB,aAAlB,OAAO8B,OAAwB,CAEjC,GAAM,CAAEqM,0BAAwB,CAAE,CAChCpM,EAAQ,CAAA,CAAA,IAAA,IAEVoM,EAAyB,oBAC3B,CAEA,OAAOW,CACT,CAoBO,SAASR,IAKd,OAJAnF,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,gBAIOyF,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACI,EAAAA,eAAe,CACnC,CA2BO,SAAST,IACd,IAAMU,EAASL,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACM,EAAAA,gBAAgB,EAC1C,GAAe,MAAM,CAAjBD,EACF,MAAM,OAAA,cAAwD,CAAxD,AAAI9M,MAAM,+CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAuD,GAG/D,OAAO8M,CACT,CAoBO,SAASZ,IAGd,OAFAlF,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,cAEOyF,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACO,EAAAA,iBAAiB,CACrC,CAiEO,SAAST,EACdY,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3BnG,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,8BAEA,IAAM0G,EAAUjB,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACkB,EAAAA,mBAAmB,SAE9C,AAAKD,EAEET,AAtET,EAoEM,KAAU,EApEPA,EACPC,CAAuB,CACvBC,CAAwB,CACxBC,CAAY,CACZC,CAA0B,MAEtBC,EACJ,GAJAF,KAAAA,IAAAA,IAAAA,GAAQ,CAAA,EACRC,KAAAA,IAAAA,IAAAA,EAAwB,EAAE,AAAF,EAGpBD,EAEFE,EAAOJ,CAAI,CAAC,CAFH,CAEK,CAACC,EAAiB,KAC3B,KAGEI,EADP,IAAMA,EAAiBL,CAAI,CAAC,EAAE,CAC9BI,EAA8B,AAAvBC,OAAAA,EAAAA,EAAeC,QAAAA,AAAQ,EAAvBD,EAA2BvM,OAAOC,MAAM,CAACsM,EAAe,CAAC,EAClE,AADoE,CAGpE,GAAI,CAACD,EAAM,OAAOD,EAClB,IAAMlQ,EAAUmQ,CAAI,CAAC,EAAE,CAEnBG,EAAevQ,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,SAEnC,AAAI,CAACsQ,GAAgBA,EAAa7P,UAAU,CAACL,EAAAA,gBAAgB,EACpD8P,CADuD,EAIhEA,EAAYlN,IAAI,CAACsN,GAEVR,EACLK,EACAH,GACA,EACAE,GAEJ,EAqCsCK,EAAQE,UAAU,CAAET,GAFnC,IAGvB,CAqBO,SAASb,EACda,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3BnG,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,6BAEA,IAAM6G,EAAyBtB,EAA0BY,GAEzD,GAAI,CAACU,GAA4D,GAAG,CAArCA,EAAuBnK,MAAM,CAC1D,OAAO,KAGT,IAAMoK,EACiB,aAArBX,EACIU,CAAsB,CAAC,EAAE,CACzBA,CAAsB,CAACA,EAAuBnK,MAAM,CAAG,EAAE,CAI/D,OAAOoK,IAA0BxQ,EAAAA,mBAAmB,CAChD,KACAwQ,CACN", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}