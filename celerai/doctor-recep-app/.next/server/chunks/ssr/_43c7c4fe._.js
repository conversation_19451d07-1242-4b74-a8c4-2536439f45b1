module.exports={649495:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],c=(0,d.default)("shield",b)}},790002:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Shield:()=>d.default});var d=a.i(649495)},315051:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],c=(0,d.default)("chart-column",b)}},588145:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({BarChart3:()=>d.default});var d=a.i(315051)},825407:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],c=(0,d.default)("bell",b)}},28912:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Bell:()=>d.default});var d=a.i(825407)},535624:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({adminLogout:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("0052d311627b1e498ec699db5ec34cf21bfb47393c",d.callServer,void 0,d.findSourceMapURL,"adminLogout")},48084:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getContactRequestsCount:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9",d.callServer,void 0,d.findSourceMapURL,"getContactRequestsCount")},620377:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({resolveFetch:()=>b});let b=b=>{let c;return c=b||("undefined"==typeof fetch?(...b)=>a.r(578285)(a.i).then(({default:a})=>a(...b)):fetch),(...a)=>c(...a)}}},327032:a=>{"use strict";var b,{g:c,__dirname:d}=a;{a.s({FunctionRegion:()=>b,FunctionsError:()=>c,FunctionsFetchError:()=>d,FunctionsHttpError:()=>f,FunctionsRelayError:()=>e});class c extends Error{constructor(a,b="FunctionsError",c){super(a),this.name=b,this.context=c}}class d extends c{constructor(a){super("Failed to send a request to the Edge Function","FunctionsFetchError",a)}}class e extends c{constructor(a){super("Relay Error invoking the Edge Function","FunctionsRelayError",a)}}class f extends c{constructor(a){super("Edge Function returned a non-2xx status code","FunctionsHttpError",a)}}!function(a){a.Any="any",a.ApNortheast1="ap-northeast-1",a.ApNortheast2="ap-northeast-2",a.ApSouth1="ap-south-1",a.ApSoutheast1="ap-southeast-1",a.ApSoutheast2="ap-southeast-2",a.CaCentral1="ca-central-1",a.EuCentral1="eu-central-1",a.EuWest1="eu-west-1",a.EuWest2="eu-west-2",a.EuWest3="eu-west-3",a.SaEast1="sa-east-1",a.UsEast1="us-east-1",a.UsWest1="us-west-1",a.UsWest2="us-west-2"}(b||(b={}))}},138160:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({FunctionsClient:()=>b});var d=a.i(620377),e=a.i(327032),f=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};class b{constructor(a,{headers:b={},customFetch:c,region:f=e.FunctionRegion.Any}={}){this.url=a,this.headers=b,this.region=f,this.fetch=(0,d.resolveFetch)(c)}setAuth(a){this.headers.Authorization=`Bearer ${a}`}invoke(a,b={}){var c;return f(this,void 0,void 0,function*(){try{let d,f,{headers:g,method:h,body:i}=b,j={},{region:k}=b;k||(k=this.region),k&&"any"!==k&&(j["x-region"]=k),i&&(g&&!Object.prototype.hasOwnProperty.call(g,"Content-Type")||!g)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(j["Content-Type"]="application/octet-stream",d=i):"string"==typeof i?(j["Content-Type"]="text/plain",d=i):"undefined"!=typeof FormData&&i instanceof FormData?d=i:(j["Content-Type"]="application/json",d=JSON.stringify(i)));let l=yield this.fetch(`${this.url}/${a}`,{method:h||"POST",headers:Object.assign(Object.assign(Object.assign({},j),this.headers),g),body:d}).catch(a=>{throw new e.FunctionsFetchError(a)}),m=l.headers.get("x-relay-error");if(m&&"true"===m)throw new e.FunctionsRelayError(l);if(!l.ok)throw new e.FunctionsHttpError(l);let n=(null!=(c=l.headers.get("Content-Type"))?c:"text/plain").split(";")[0].trim();return{data:"application/json"===n?yield l.json():"application/octet-stream"===n?yield l.blob():"text/event-stream"===n?l:"multipart/form-data"===n?yield l.formData():yield l.text(),error:null}}catch(a){return{data:null,error:a}}})}}}},716732:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=class extends Error{constructor(a){super(a.message),this.name="PostgrestError",this.details=a.details,this.hint=a.hint,this.code=a.code}}},326064:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0});let b=f(a.r(540080)),c=f(a.r(716732));e.default=class{constructor(a){this.shouldThrowOnError=!1,this.method=a.method,this.url=a.url,this.headers=a.headers,this.schema=a.schema,this.body=a.body,this.shouldThrowOnError=a.shouldThrowOnError,this.signal=a.signal,this.isMaybeSingle=a.isMaybeSingle,a.fetch?this.fetch=a.fetch:"undefined"==typeof fetch?this.fetch=b.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(a,b){return this.headers=Object.assign({},this.headers),this.headers[a]=b,this}then(a,b){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let d=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async a=>{var b,d,e;let f=null,g=null,h=null,i=a.status,j=a.statusText;if(a.ok){if("HEAD"!==this.method){let b=await a.text();""===b||(g="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?b:JSON.parse(b))}let c=null==(b=this.headers.Prefer)?void 0:b.match(/count=(exact|planned|estimated)/),e=null==(d=a.headers.get("content-range"))?void 0:d.split("/");c&&e&&e.length>1&&(h=parseInt(e[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(g)&&(g.length>1?(f={code:"PGRST116",details:`Results contain ${g.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},g=null,h=null,i=406,j="Not Acceptable"):g=1===g.length?g[0]:null)}else{let b=await a.text();try{f=JSON.parse(b),Array.isArray(f)&&404===a.status&&(g=[],f=null,i=200,j="OK")}catch(c){404===a.status&&""===b?(i=204,j="No Content"):f={message:b}}if(f&&this.isMaybeSingle&&(null==(e=null==f?void 0:f.details)?void 0:e.includes("0 rows"))&&(f=null,i=200,j="OK"),f&&this.shouldThrowOnError)throw new c.default(f)}return{error:f,data:g,count:h,status:i,statusText:j}});return this.shouldThrowOnError||(d=d.catch(a=>{var b,c,d;return{error:{message:`${null!=(b=null==a?void 0:a.name)?b:"FetchError"}: ${null==a?void 0:a.message}`,details:`${null!=(c=null==a?void 0:a.stack)?c:""}`,hint:"",code:`${null!=(d=null==a?void 0:a.code)?d:""}`},data:null,count:null,status:0,statusText:""}})),d.then(a,b)}returns(){return this}overrideTypes(){return this}}}},350500:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0});let b=f(a.r(326064));class c extends b.default{select(a){let b=!1,c=(null!=a?a:"*").split("").map(a=>/\s/.test(a)&&!b?"":('"'===a&&(b=!b),a)).join("");return this.url.searchParams.set("select",c),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(a,{ascending:b=!0,nullsFirst:c,foreignTable:d,referencedTable:e=d}={}){let f=e?`${e}.order`:"order",g=this.url.searchParams.get(f);return this.url.searchParams.set(f,`${g?`${g},`:""}${a}.${b?"asc":"desc"}${void 0===c?"":c?".nullsfirst":".nullslast"}`),this}limit(a,{foreignTable:b,referencedTable:c=b}={}){let d=void 0===c?"limit":`${c}.limit`;return this.url.searchParams.set(d,`${a}`),this}range(a,b,{foreignTable:c,referencedTable:d=c}={}){let e=void 0===d?"offset":`${d}.offset`,f=void 0===d?"limit":`${d}.limit`;return this.url.searchParams.set(e,`${a}`),this.url.searchParams.set(f,`${b-a+1}`),this}abortSignal(a){return this.signal=a,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:a=!1,verbose:b=!1,settings:c=!1,buffers:d=!1,wal:e=!1,format:f="text"}={}){var g;let h=[a?"analyze":null,b?"verbose":null,c?"settings":null,d?"buffers":null,e?"wal":null].filter(Boolean).join("|"),i=null!=(g=this.headers.Accept)?g:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${f}; for="${i}"; options=${h};`,this}rollback(){var a;return(null!=(a=this.headers.Prefer)?a:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}e.default=c}},118585:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0});let b=f(a.r(350500));class c extends b.default{eq(a,b){return this.url.searchParams.append(a,`eq.${b}`),this}neq(a,b){return this.url.searchParams.append(a,`neq.${b}`),this}gt(a,b){return this.url.searchParams.append(a,`gt.${b}`),this}gte(a,b){return this.url.searchParams.append(a,`gte.${b}`),this}lt(a,b){return this.url.searchParams.append(a,`lt.${b}`),this}lte(a,b){return this.url.searchParams.append(a,`lte.${b}`),this}like(a,b){return this.url.searchParams.append(a,`like.${b}`),this}likeAllOf(a,b){return this.url.searchParams.append(a,`like(all).{${b.join(",")}}`),this}likeAnyOf(a,b){return this.url.searchParams.append(a,`like(any).{${b.join(",")}}`),this}ilike(a,b){return this.url.searchParams.append(a,`ilike.${b}`),this}ilikeAllOf(a,b){return this.url.searchParams.append(a,`ilike(all).{${b.join(",")}}`),this}ilikeAnyOf(a,b){return this.url.searchParams.append(a,`ilike(any).{${b.join(",")}}`),this}is(a,b){return this.url.searchParams.append(a,`is.${b}`),this}in(a,b){let c=Array.from(new Set(b)).map(a=>"string"==typeof a&&RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(a,`in.(${c})`),this}contains(a,b){return"string"==typeof b?this.url.searchParams.append(a,`cs.${b}`):Array.isArray(b)?this.url.searchParams.append(a,`cs.{${b.join(",")}}`):this.url.searchParams.append(a,`cs.${JSON.stringify(b)}`),this}containedBy(a,b){return"string"==typeof b?this.url.searchParams.append(a,`cd.${b}`):Array.isArray(b)?this.url.searchParams.append(a,`cd.{${b.join(",")}}`):this.url.searchParams.append(a,`cd.${JSON.stringify(b)}`),this}rangeGt(a,b){return this.url.searchParams.append(a,`sr.${b}`),this}rangeGte(a,b){return this.url.searchParams.append(a,`nxl.${b}`),this}rangeLt(a,b){return this.url.searchParams.append(a,`sl.${b}`),this}rangeLte(a,b){return this.url.searchParams.append(a,`nxr.${b}`),this}rangeAdjacent(a,b){return this.url.searchParams.append(a,`adj.${b}`),this}overlaps(a,b){return"string"==typeof b?this.url.searchParams.append(a,`ov.${b}`):this.url.searchParams.append(a,`ov.{${b.join(",")}}`),this}textSearch(a,b,{config:c,type:d}={}){let e="";"plain"===d?e="pl":"phrase"===d?e="ph":"websearch"===d&&(e="w");let f=void 0===c?"":`(${c})`;return this.url.searchParams.append(a,`${e}fts${f}.${b}`),this}match(a){return Object.entries(a).forEach(([a,b])=>{this.url.searchParams.append(a,`eq.${b}`)}),this}not(a,b,c){return this.url.searchParams.append(a,`not.${b}.${c}`),this}or(a,{foreignTable:b,referencedTable:c=b}={}){let d=c?`${c}.or`:"or";return this.url.searchParams.append(d,`(${a})`),this}filter(a,b,c){return this.url.searchParams.append(a,`${b}.${c}`),this}}e.default=c}},697315:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0});let b=f(a.r(118585));e.default=class{constructor(a,{headers:b={},schema:c,fetch:d}){this.url=a,this.headers=b,this.schema=c,this.fetch=d}select(a,{head:c=!1,count:d}={}){let e=!1,f=(null!=a?a:"*").split("").map(a=>/\s/.test(a)&&!e?"":('"'===a&&(e=!e),a)).join("");return this.url.searchParams.set("select",f),d&&(this.headers.Prefer=`count=${d}`),new b.default({method:c?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(a,{count:c,defaultToNull:d=!0}={}){let e=[];if(this.headers.Prefer&&e.push(this.headers.Prefer),c&&e.push(`count=${c}`),d||e.push("missing=default"),this.headers.Prefer=e.join(","),Array.isArray(a)){let b=a.reduce((a,b)=>a.concat(Object.keys(b)),[]);if(b.length>0){let a=[...new Set(b)].map(a=>`"${a}"`);this.url.searchParams.set("columns",a.join(","))}}return new b.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}upsert(a,{onConflict:c,ignoreDuplicates:d=!1,count:e,defaultToNull:f=!0}={}){let g=[`resolution=${d?"ignore":"merge"}-duplicates`];if(void 0!==c&&this.url.searchParams.set("on_conflict",c),this.headers.Prefer&&g.push(this.headers.Prefer),e&&g.push(`count=${e}`),f||g.push("missing=default"),this.headers.Prefer=g.join(","),Array.isArray(a)){let b=a.reduce((a,b)=>a.concat(Object.keys(b)),[]);if(b.length>0){let a=[...new Set(b)].map(a=>`"${a}"`);this.url.searchParams.set("columns",a.join(","))}}return new b.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(a,{count:c}={}){let d=[];return this.headers.Prefer&&d.push(this.headers.Prefer),c&&d.push(`count=${c}`),this.headers.Prefer=d.join(","),new b.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}delete({count:a}={}){let c=[];return a&&c.push(`count=${a}`),this.headers.Prefer&&c.unshift(this.headers.Prefer),this.headers.Prefer=c.join(","),new b.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}}},295981:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.version=void 0,e.version="0.0.0-automated"},374474:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DEFAULT_HEADERS=void 0;let b=a.r(295981);e.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${b.version}`}}},884645:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0});let b=f(a.r(697315)),c=f(a.r(118585)),d=a.r(374474);class g{constructor(a,{headers:b={},schema:c,fetch:e}={}){this.url=a,this.headers=Object.assign(Object.assign({},d.DEFAULT_HEADERS),b),this.schemaName=c,this.fetch=e}from(a){let c=new URL(`${this.url}/${a}`);return new b.default(c,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(a){return new g(this.url,{headers:this.headers,schema:a,fetch:this.fetch})}rpc(a,b={},{head:d=!1,get:e=!1,count:f}={}){let g,h,i=new URL(`${this.url}/rpc/${a}`);d||e?(g=d?"HEAD":"GET",Object.entries(b).filter(([a,b])=>void 0!==b).map(([a,b])=>[a,Array.isArray(b)?`{${b.join(",")}}`:`${b}`]).forEach(([a,b])=>{i.searchParams.append(a,b)})):(g="POST",h=b);let j=Object.assign({},this.headers);return f&&(j.Prefer=`count=${f}`),new c.default({method:g,url:i,headers:j,schema:this.schemaName,body:h,fetch:this.fetch,allowEmpty:!1})}}e.default=g}},515614:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";var f=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(e,"__esModule",{value:!0}),e.PostgrestError=e.PostgrestBuilder=e.PostgrestTransformBuilder=e.PostgrestFilterBuilder=e.PostgrestQueryBuilder=e.PostgrestClient=void 0;let b=f(a.r(884645));e.PostgrestClient=b.default;let c=f(a.r(697315));e.PostgrestQueryBuilder=c.default;let d=f(a.r(118585));e.PostgrestFilterBuilder=d.default;let g=f(a.r(350500));e.PostgrestTransformBuilder=g.default;let h=f(a.r(326064));e.PostgrestBuilder=h.default;let i=f(a.r(716732));e.PostgrestError=i.default,e.default={PostgrestClient:b.default,PostgrestQueryBuilder:c.default,PostgrestFilterBuilder:d.default,PostgrestTransformBuilder:g.default,PostgrestBuilder:h.default,PostgrestError:i.default}}},894152:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PostgrestBuilder:()=>f,PostgrestClient:()=>b,PostgrestError:()=>g,PostgrestFilterBuilder:()=>d,PostgrestQueryBuilder:()=>c,PostgrestTransformBuilder:()=>e,default:()=>h});let{PostgrestClient:b,PostgrestQueryBuilder:c,PostgrestFilterBuilder:d,PostgrestTransformBuilder:e,PostgrestBuilder:f,PostgrestError:g}=a.i(515614).default,h={PostgrestClient:b,PostgrestQueryBuilder:c,PostgrestFilterBuilder:d,PostgrestTransformBuilder:e,PostgrestBuilder:f,PostgrestError:g}}},333001:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({version:()=>b});let b="2.11.2"}},246352:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({CHANNEL_EVENTS:()=>f,CHANNEL_STATES:()=>e,CONNECTION_STATE:()=>h,DEFAULT_HEADERS:()=>b,DEFAULT_TIMEOUT:()=>j,SOCKET_STATES:()=>d,TRANSPORTS:()=>g,VSN:()=>c,WS_CLOSE_NORMAL:()=>k});var d,e,f,g,h,i=a.i(333001);let b={"X-Client-Info":`realtime-js/${i.version}`},c="1.0.0",j=1e4,k=1e3;!function(a){a[a.connecting=0]="connecting",a[a.open=1]="open",a[a.closing=2]="closing",a[a.closed=3]="closed"}(d||(d={})),function(a){a.closed="closed",a.errored="errored",a.joined="joined",a.joining="joining",a.leaving="leaving"}(e||(e={})),function(a){a.close="phx_close",a.error="phx_error",a.join="phx_join",a.reply="phx_reply",a.leave="phx_leave",a.access_token="access_token"}(f||(f={})),(g||(g={})).websocket="websocket",function(a){a.Connecting="connecting",a.Open="open",a.Closing="closing",a.Closed="closed"}(h||(h={}))}},242756:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});class b{constructor(){this.HEADER_LENGTH=1}decode(a,b){return a.constructor===ArrayBuffer?b(this._binaryDecode(a)):"string"==typeof a?b(JSON.parse(a)):b({})}_binaryDecode(a){let b=new DataView(a),c=new TextDecoder;return this._decodeBroadcast(a,b,c)}_decodeBroadcast(a,b,c){let d=b.getUint8(1),e=b.getUint8(2),f=this.HEADER_LENGTH+2,g=c.decode(a.slice(f,f+d));f+=d;let h=c.decode(a.slice(f,f+e));return f+=e,{ref:null,topic:g,event:h,payload:JSON.parse(c.decode(a.slice(f,a.byteLength)))}}}}},964350:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});class b{constructor(a,b){this.callback=a,this.timerCalc=b,this.timer=void 0,this.tries=0,this.callback=a,this.timerCalc=b}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}}},683126:a=>{"use strict";var b,{g:c,__dirname:d}=a;{a.s({PostgresTypes:()=>b,convertCell:()=>e,convertChangeData:()=>c,convertColumn:()=>d,httpEndpointURL:()=>l,toArray:()=>j,toBoolean:()=>g,toJson:()=>i,toNumber:()=>h,toTimestampString:()=>k}),function(a){a.abstime="abstime",a.bool="bool",a.date="date",a.daterange="daterange",a.float4="float4",a.float8="float8",a.int2="int2",a.int4="int4",a.int4range="int4range",a.int8="int8",a.int8range="int8range",a.json="json",a.jsonb="jsonb",a.money="money",a.numeric="numeric",a.oid="oid",a.reltime="reltime",a.text="text",a.time="time",a.timestamp="timestamp",a.timestamptz="timestamptz",a.timetz="timetz",a.tsrange="tsrange",a.tstzrange="tstzrange"}(b||(b={}));let c=(a,b,c={})=>{var e;let f=null!=(e=c.skipTypes)?e:[];return Object.keys(b).reduce((c,e)=>(c[e]=d(e,a,b,f),c),{})},d=(a,b,c,d)=>{let g=b.find(b=>b.name===a),h=null==g?void 0:g.type,i=c[a];return h&&!d.includes(h)?e(h,i):f(i)},e=(a,c)=>{if("_"===a.charAt(0))return j(c,a.slice(1,a.length));switch(a){case b.bool:return g(c);case b.float4:case b.float8:case b.int2:case b.int4:case b.int8:case b.numeric:case b.oid:return h(c);case b.json:case b.jsonb:return i(c);case b.timestamp:return k(c);case b.abstime:case b.date:case b.daterange:case b.int4range:case b.int8range:case b.money:case b.reltime:case b.text:case b.time:case b.timestamptz:case b.timetz:case b.tsrange:case b.tstzrange:default:return f(c)}},f=a=>a,g=a=>{switch(a){case"t":return!0;case"f":return!1;default:return a}},h=a=>{if("string"==typeof a){let b=parseFloat(a);if(!Number.isNaN(b))return b}return a},i=a=>{if("string"==typeof a)try{return JSON.parse(a)}catch(a){console.log(`JSON parse error: ${a}`)}return a},j=(a,b)=>{if("string"!=typeof a)return a;let c=a.length-1,d=a[c];if("{"===a[0]&&"}"===d){let d,f=a.slice(1,c);try{d=JSON.parse("["+f+"]")}catch(a){d=f?f.split(","):[]}return d.map(a=>e(b,a))}return a},k=a=>"string"==typeof a?a.replace(" ","T"):a,l=a=>{let b=a;return(b=(b=b.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")}}},920244:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(246352);class b{constructor(a,b,c={},e=d.DEFAULT_TIMEOUT){this.channel=a,this.event=b,this.payload=c,this.timeout=e,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(a){this.timeout=a,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(a){this.payload=Object.assign(Object.assign({},this.payload),a)}receive(a,b){var c;return this._hasReceived(a)&&b(null==(c=this.receivedResp)?void 0:c.response),this.recHooks.push({status:a,callback:b}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},a=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=a,this._matchReceive(a)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(a,b){this.refEvent&&this.channel._trigger(this.refEvent,{status:a,response:b})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:a,response:b}){this.recHooks.filter(b=>b.status===a).forEach(a=>a.callback(b))}_hasReceived(a){return this.receivedResp&&this.receivedResp.status===a}}}},420451:a=>{"use strict";var b,{g:c,__dirname:d}=a;{a.s({REALTIME_PRESENCE_LISTEN_EVENTS:()=>b,default:()=>c}),function(a){a.SYNC="sync",a.JOIN="join",a.LEAVE="leave"}(b||(b={}));class c{constructor(a,b){this.channel=a,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let d=(null==b?void 0:b.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(d.state,{},a=>{let{onJoin:b,onLeave:d,onSync:e}=this.caller;this.joinRef=this.channel._joinRef(),this.state=c.syncState(this.state,a,b,d),this.pendingDiffs.forEach(a=>{this.state=c.syncDiff(this.state,a,b,d)}),this.pendingDiffs=[],e()}),this.channel._on(d.diff,{},a=>{let{onJoin:b,onLeave:d,onSync:e}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(a):(this.state=c.syncDiff(this.state,a,b,d),e())}),this.onJoin((a,b,c)=>{this.channel._trigger("presence",{event:"join",key:a,currentPresences:b,newPresences:c})}),this.onLeave((a,b,c)=>{this.channel._trigger("presence",{event:"leave",key:a,currentPresences:b,leftPresences:c})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(a,b,c,d){let e=this.cloneDeep(a),f=this.transformState(b),g={},h={};return this.map(e,(a,b)=>{f[a]||(h[a]=b)}),this.map(f,(a,b)=>{let c=e[a];if(c){let d=b.map(a=>a.presence_ref),e=c.map(a=>a.presence_ref),f=b.filter(a=>0>e.indexOf(a.presence_ref)),i=c.filter(a=>0>d.indexOf(a.presence_ref));f.length>0&&(g[a]=f),i.length>0&&(h[a]=i)}else g[a]=b}),this.syncDiff(e,{joins:g,leaves:h},c,d)}static syncDiff(a,b,c,d){let{joins:e,leaves:f}={joins:this.transformState(b.joins),leaves:this.transformState(b.leaves)};return c||(c=()=>{}),d||(d=()=>{}),this.map(e,(b,d)=>{var e;let f=null!=(e=a[b])?e:[];if(a[b]=this.cloneDeep(d),f.length>0){let c=a[b].map(a=>a.presence_ref),d=f.filter(a=>0>c.indexOf(a.presence_ref));a[b].unshift(...d)}c(b,f,d)}),this.map(f,(b,c)=>{let e=a[b];if(!e)return;let f=c.map(a=>a.presence_ref);e=e.filter(a=>0>f.indexOf(a.presence_ref)),a[b]=e,d(b,e,c),0===e.length&&delete a[b]}),a}static map(a,b){return Object.getOwnPropertyNames(a).map(c=>b(c,a[c]))}static transformState(a){return Object.getOwnPropertyNames(a=this.cloneDeep(a)).reduce((b,c)=>{let d=a[c];return"metas"in d?b[c]=d.metas.map(a=>(a.presence_ref=a.phx_ref,delete a.phx_ref,delete a.phx_ref_prev,a)):b[c]=d,b},{})}static cloneDeep(a){return JSON.parse(JSON.stringify(a))}onJoin(a){this.caller.onJoin=a}onLeave(a){this.caller.onLeave=a}onSync(a){this.caller.onSync=a}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}}},766756:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({REALTIME_CHANNEL_STATES:()=>b,REALTIME_LISTEN_TYPES:()=>e,REALTIME_POSTGRES_CHANGES_LISTEN_EVENT:()=>d,REALTIME_SUBSCRIBE_STATES:()=>f,default:()=>c});var d,e,f,g=a.i(246352),h=a.i(920244),i=a.i(964350),j=a.i(420451),k=a.i(683126);!function(a){a.ALL="*",a.INSERT="INSERT",a.UPDATE="UPDATE",a.DELETE="DELETE"}(d||(d={})),function(a){a.BROADCAST="broadcast",a.PRESENCE="presence",a.POSTGRES_CHANGES="postgres_changes",a.SYSTEM="system"}(e||(e={})),function(a){a.SUBSCRIBED="SUBSCRIBED",a.TIMED_OUT="TIMED_OUT",a.CLOSED="CLOSED",a.CHANNEL_ERROR="CHANNEL_ERROR"}(f||(f={}));let b=g.CHANNEL_STATES;class c{constructor(a,b={config:{}},c){this.topic=a,this.params=b,this.socket=c,this.bindings={},this.state=g.CHANNEL_STATES.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=a.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},b.config),this.timeout=this.socket.timeout,this.joinPush=new h.default(this,g.CHANNEL_EVENTS.join,this.params,this.timeout),this.rejoinTimer=new i.default(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=g.CHANNEL_STATES.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(a=>a.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=g.CHANNEL_STATES.closed,this.socket._remove(this)}),this._onError(a=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,a),this.state=g.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=g.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this._on(g.CHANNEL_EVENTS.reply,{},(a,b)=>{this._trigger(this._replyEventName(b),a)}),this.presence=new j.default(this),this.broadcastEndpointURL=(0,k.httpEndpointURL)(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(a,b=this.timeout){var c,d;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:e,presence:g,private:h}}=this.params;this._onError(b=>null==a?void 0:a(f.CHANNEL_ERROR,b)),this._onClose(()=>null==a?void 0:a(f.CLOSED));let i={},j={broadcast:e,presence:g,postgres_changes:null!=(d=null==(c=this.bindings.postgres_changes)?void 0:c.map(a=>a.filter))?d:[],private:h};this.socket.accessTokenValue&&(i.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:j},i)),this.joinedOnce=!0,this._rejoin(b),this.joinPush.receive("ok",async({postgres_changes:b})=>{var c;if(this.socket.setAuth(),void 0===b){null==a||a(f.SUBSCRIBED);return}{let d=this.bindings.postgres_changes,e=null!=(c=null==d?void 0:d.length)?c:0,g=[];for(let c=0;c<e;c++){let e=d[c],{filter:{event:h,schema:i,table:j,filter:k}}=e,l=b&&b[c];if(l&&l.event===h&&l.schema===i&&l.table===j&&l.filter===k)g.push(Object.assign(Object.assign({},e),{id:l.id}));else{this.unsubscribe(),null==a||a(f.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=g,a&&a(f.SUBSCRIBED);return}}).receive("error",b=>{null==a||a(f.CHANNEL_ERROR,Error(JSON.stringify(Object.values(b).join(", ")||"error")))}).receive("timeout",()=>{null==a||a(f.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(a,b={}){return await this.send({type:"presence",event:"track",payload:a},b.timeout||this.timeout)}async untrack(a={}){return await this.send({type:"presence",event:"untrack"},a)}on(a,b,c){return this._on(a,b,c)}async send(a,b={}){var c,d;if(this._canPush()||"broadcast"!==a.type)return new Promise(c=>{var d,e,f;let g=this._push(a.type,a,b.timeout||this.timeout);"broadcast"!==a.type||(null==(f=null==(e=null==(d=this.params)?void 0:d.config)?void 0:e.broadcast)?void 0:f.ack)||c("ok"),g.receive("ok",()=>c("ok")),g.receive("error",()=>c("error")),g.receive("timeout",()=>c("timed out"))});{let{event:e,payload:f}=a,g={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:e,payload:f,private:this.private}]})};try{let a=await this._fetchWithTimeout(this.broadcastEndpointURL,g,null!=(c=b.timeout)?c:this.timeout);return await (null==(d=a.body)?void 0:d.cancel()),a.ok?"ok":"error"}catch(a){if("AbortError"===a.name)return"timed out";return"error"}}}updateJoinPayload(a){this.joinPush.updatePayload(a)}unsubscribe(a=this.timeout){this.state=g.CHANNEL_STATES.leaving;let b=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(g.CHANNEL_EVENTS.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(c=>{let d=new h.default(this,g.CHANNEL_EVENTS.leave,{},a);d.receive("ok",()=>{b(),c("ok")}).receive("timeout",()=>{b(),c("timed out")}).receive("error",()=>{c("error")}),d.send(),this._canPush()||d.trigger("ok",{})})}async _fetchWithTimeout(a,b,c){let d=new AbortController,e=setTimeout(()=>d.abort(),c),f=await this.socket.fetch(a,Object.assign(Object.assign({},b),{signal:d.signal}));return clearTimeout(e),f}_push(a,b,c=this.timeout){if(!this.joinedOnce)throw`tried to push '${a}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let d=new h.default(this,a,b,c);return this._canPush()?d.send():(d.startTimeout(),this.pushBuffer.push(d)),d}_onMessage(a,b,c){return b}_isMember(a){return this.topic===a}_joinRef(){return this.joinPush.ref}_trigger(a,b,c){var d,e;let f=a.toLocaleLowerCase(),{close:h,error:i,leave:j,join:k}=g.CHANNEL_EVENTS;if(c&&[h,i,j,k].indexOf(f)>=0&&c!==this._joinRef())return;let l=this._onMessage(f,b,c);if(b&&!l)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?null==(d=this.bindings.postgres_changes)||d.filter(a=>{var b,c,d;return(null==(b=a.filter)?void 0:b.event)==="*"||(null==(d=null==(c=a.filter)?void 0:c.event)?void 0:d.toLocaleLowerCase())===f}).map(a=>a.callback(l,c)):null==(e=this.bindings[f])||e.filter(a=>{var c,d,e,g,h,i;if(!["broadcast","presence","postgres_changes"].includes(f))return a.type.toLocaleLowerCase()===f;if("id"in a){let f=a.id,g=null==(c=a.filter)?void 0:c.event;return f&&(null==(d=b.ids)?void 0:d.includes(f))&&("*"===g||(null==g?void 0:g.toLocaleLowerCase())===(null==(e=b.data)?void 0:e.type.toLocaleLowerCase()))}{let c=null==(h=null==(g=null==a?void 0:a.filter)?void 0:g.event)?void 0:h.toLocaleLowerCase();return"*"===c||c===(null==(i=null==b?void 0:b.event)?void 0:i.toLocaleLowerCase())}}).map(a=>{if("object"==typeof l&&"ids"in l){let a=l.data,{schema:b,table:c,commit_timestamp:d,type:e,errors:f}=a;l=Object.assign(Object.assign({},{schema:b,table:c,commit_timestamp:d,eventType:e,new:{},old:{},errors:f}),this._getPayloadRecords(a))}a.callback(l,c)})}_isClosed(){return this.state===g.CHANNEL_STATES.closed}_isJoined(){return this.state===g.CHANNEL_STATES.joined}_isJoining(){return this.state===g.CHANNEL_STATES.joining}_isLeaving(){return this.state===g.CHANNEL_STATES.leaving}_replyEventName(a){return`chan_reply_${a}`}_on(a,b,c){let d=a.toLocaleLowerCase(),e={type:d,filter:b,callback:c};return this.bindings[d]?this.bindings[d].push(e):this.bindings[d]=[e],this}_off(a,b){let d=a.toLocaleLowerCase();return this.bindings[d]=this.bindings[d].filter(a=>{var e;return!((null==(e=a.type)?void 0:e.toLocaleLowerCase())===d&&c.isEqual(a.filter,b))}),this}static isEqual(a,b){if(Object.keys(a).length!==Object.keys(b).length)return!1;for(let c in a)if(a[c]!==b[c])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(a){this._on(g.CHANNEL_EVENTS.close,{},a)}_onError(a){this._on(g.CHANNEL_EVENTS.error,{},b=>a(b))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(a=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=g.CHANNEL_STATES.joining,this.joinPush.resend(a))}_getPayloadRecords(a){let b={new:{},old:{}};return("INSERT"===a.type||"UPDATE"===a.type)&&(b.new=(0,k.convertChangeData)(a.columns,a.record)),("UPDATE"===a.type||"DELETE"===a.type)&&(b.old=(0,k.convertChangeData)(a.columns,a.old_record)),b}}}},824399:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>j});var d=a.i(246352),e=a.i(242756),f=a.i(964350),g=a.i(683126),h=a.i(766756);let b=()=>{},c="undefined"!=typeof WebSocket,i=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class j{constructor(c,h){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=d.DEFAULT_HEADERS,this.params={},this.timeout=d.DEFAULT_TIMEOUT,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=b,this.conn=null,this.sendBuffer=[],this.serializer=new e.default,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=b=>{let c;return c=b||("undefined"==typeof fetch?(...b)=>a.r(578285)(a.i).then(({default:a})=>a(...b)):fetch),(...a)=>c(...a)},this.endPoint=`${c}/${d.TRANSPORTS.websocket}`,this.httpEndpoint=(0,g.httpEndpointURL)(c),(null==h?void 0:h.transport)?this.transport=h.transport:this.transport=null,(null==h?void 0:h.params)&&(this.params=h.params),(null==h?void 0:h.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),h.headers)),(null==h?void 0:h.timeout)&&(this.timeout=h.timeout),(null==h?void 0:h.logger)&&(this.logger=h.logger),(null==h?void 0:h.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=h.heartbeatIntervalMs);let j=null==(i=null==h?void 0:h.params)?void 0:i.apikey;if(j&&(this.accessTokenValue=j,this.apiKey=j),this.reconnectAfterMs=(null==h?void 0:h.reconnectAfterMs)?h.reconnectAfterMs:a=>[1e3,2e3,5e3,1e4][a-1]||1e4,this.encode=(null==h?void 0:h.encode)?h.encode:(a,b)=>b(JSON.stringify(a)),this.decode=(null==h?void 0:h.decode)?h.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new f.default(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==h?void 0:h.fetch),null==h?void 0:h.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==h?void 0:h.worker)||!1,this.workerUrl=null==h?void 0:h.workerUrl}this.accessToken=(null==h?void 0:h.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(c){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new k(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),a.r(184751)(a.i).then(({default:a})=>{this.conn=new a(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:d.VSN}))}disconnect(a,b){this.conn&&(this.conn.onclose=function(){},a?this.conn.close(a,null!=b?b:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(a){let b=await a.unsubscribe();return 0===this.channels.length&&this.disconnect(),b}async removeAllChannels(){let a=await Promise.all(this.channels.map(a=>a.unsubscribe()));return this.disconnect(),a}log(a,b,c){this.logger(a,b,c)}connectionState(){switch(this.conn&&this.conn.readyState){case d.SOCKET_STATES.connecting:return d.CONNECTION_STATE.Connecting;case d.SOCKET_STATES.open:return d.CONNECTION_STATE.Open;case d.SOCKET_STATES.closing:return d.CONNECTION_STATE.Closing;default:return d.CONNECTION_STATE.Closed}}isConnected(){return this.connectionState()===d.CONNECTION_STATE.Open}channel(a,b={config:{}}){let c=new h.default(`realtime:${a}`,b,this);return this.channels.push(c),c}push(a){let{topic:b,event:c,payload:d,ref:e}=a,f=()=>{this.encode(a,a=>{var b;null==(b=this.conn)||b.send(a)})};this.log("push",`${b} ${c} (${e})`,d),this.isConnected()?f():this.sendBuffer.push(f)}async setAuth(a=null){let b=a||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(b){let a=null;try{a=JSON.parse(atob(b.split(".")[1]))}catch(a){}if(a&&a.exp&&!(Math.floor(Date.now()/1e3)-a.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${a.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${a.exp}`);this.accessTokenValue=b,this.channels.forEach(a=>{b&&a.updateJoinPayload({access_token:b}),a.joinedOnce&&a._isJoined()&&a._push(d.CHANNEL_EVENTS.access_token,{access_token:b})})}}async sendHeartbeat(){var a;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null==(a=this.conn)||a.close(d.WS_CLOSE_NORMAL,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(a=>a()),this.sendBuffer=[])}_makeRef(){let a=this.ref+1;return a===this.ref?this.ref=0:this.ref=a,this.ref.toString()}_leaveOpenTopic(a){let b=this.channels.find(b=>b.topic===a&&(b._isJoined()||b._isJoining()));b&&(this.log("transport",`leaving duplicate topic "${a}"`),b.unsubscribe())}_remove(a){this.channels=this.channels.filter(b=>b._joinRef()!==a._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=a=>this._onConnError(a),this.conn.onmessage=a=>this._onConnMessage(a),this.conn.onclose=a=>this._onConnClose(a))}_onConnMessage(a){this.decode(a.data,a=>{let{topic:b,event:c,payload:d,ref:e}=a;e&&e===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${d.status||""} ${b} ${c} ${e&&"("+e+")"||""}`,d),this.channels.filter(a=>a._isMember(b)).forEach(a=>a._trigger(c,d,e)),this.stateChangeCallbacks.message.forEach(b=>b(a))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let a=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(a),this.workerRef.onerror=a=>{this.log("worker","worker error",a.message),this.workerRef.terminate()},this.workerRef.onmessage=a=>{"keepAlive"===a.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(a=>a())}_onConnClose(a){this.log("transport","close",a),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(b=>b(a))}_onConnError(a){this.log("transport",a.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(b=>b(a))}_triggerChanError(){this.channels.forEach(a=>a._trigger(d.CHANNEL_EVENTS.error))}_appendParams(a,b){if(0===Object.keys(b).length)return a;let c=a.match(/\?/)?"&":"?",d=new URLSearchParams(b);return`${a}${c}${d}`}_workerObjectUrl(a){let b;if(a)b=a;else{let a=new Blob([i],{type:"application/javascript"});b=URL.createObjectURL(a)}return b}}class k{constructor(a,b,c){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=d.SOCKET_STATES.connecting,this.send=()=>{},this.url=null,this.url=a,this.close=c.close}}}},511964:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(824399),a.i(766756),a.i(420451)},426437:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(824399),a.i(766756),a.i(420451),a.i(511964)},27493:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({RealtimeClient:()=>d.default});var d=a.i(824399)},831909:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({StorageApiError:()=>c,StorageError:()=>b,StorageUnknownError:()=>e,isStorageError:()=>d});class b extends Error{constructor(a){super(a),this.__isStorageError=!0,this.name="StorageError"}}function d(a){return"object"==typeof a&&null!==a&&"__isStorageError"in a}class c extends b{constructor(a,b){super(a),this.name="StorageApiError",this.status=b}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class e extends b{constructor(a,b){super(a),this.name="StorageUnknownError",this.originalError=b}}}},878321:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({recursiveToCamel:()=>e,resolveFetch:()=>b,resolveResponse:()=>c});var d=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let b=b=>{let c;return c=b||("undefined"==typeof fetch?(...b)=>a.r(578285)(a.i).then(({default:a})=>a(...b)):fetch),(...a)=>c(...a)},c=()=>d(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield a.r(578285)(a.i)).Response:Response}),e=a=>{if(Array.isArray(a))return a.map(a=>e(a));if("function"==typeof a||a!==Object(a))return a;let b={};return Object.entries(a).forEach(([a,c])=>{b[a.replace(/([-_][a-z])/gi,a=>a.toUpperCase().replace(/[-_]/g,""))]=e(c)}),b}}},517405:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({get:()=>h,head:()=>k,post:()=>i,put:()=>j,remove:()=>l});var d=a.i(831909),e=a.i(878321),f=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let b=a=>a.msg||a.message||a.error_description||a.error||JSON.stringify(a),c=(a,c,g)=>f(void 0,void 0,void 0,function*(){a instanceof(yield(0,e.resolveResponse)())&&!(null==g?void 0:g.noResolveJson)?a.json().then(e=>{c(new d.StorageApiError(b(e),a.status||500))}).catch(a=>{c(new d.StorageUnknownError(b(a),a))}):c(new d.StorageUnknownError(b(a),a))}),m=(a,b,c,d)=>{let e={method:a,headers:(null==b?void 0:b.headers)||{}};return"GET"===a?e:(e.headers=Object.assign({"Content-Type":"application/json"},null==b?void 0:b.headers),d&&(e.body=JSON.stringify(d)),Object.assign(Object.assign({},e),c))};function g(a,b,d,e,g,h){return f(this,void 0,void 0,function*(){return new Promise((f,i)=>{a(d,m(b,e,g,h)).then(a=>{if(!a.ok)throw a;return(null==e?void 0:e.noResolveJson)?a:a.json()}).then(a=>f(a)).catch(a=>c(a,i,e))})})}function h(a,b,c,d){return f(this,void 0,void 0,function*(){return g(a,"GET",b,c,d)})}function i(a,b,c,d,e){return f(this,void 0,void 0,function*(){return g(a,"POST",b,d,e,c)})}function j(a,b,c,d,e){return f(this,void 0,void 0,function*(){return g(a,"PUT",b,d,e,c)})}function k(a,b,c,d){return f(this,void 0,void 0,function*(){return g(a,"HEAD",b,Object.assign(Object.assign({},c),{noResolveJson:!0}),d)})}function l(a,b,c,d,e){return f(this,void 0,void 0,function*(){return g(a,"DELETE",b,d,e,c)})}}},8526:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>h});var d=a.i(831909),e=a.i(517405),f=a.i(878321),g=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let b={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},c={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class h{constructor(a,b={},c,d){this.url=a,this.headers=b,this.bucketId=c,this.fetch=(0,f.resolveFetch)(d)}uploadOrUpdate(a,b,e,f){return g(this,void 0,void 0,function*(){try{let d,g=Object.assign(Object.assign({},c),f),h=Object.assign(Object.assign({},this.headers),"POST"===a&&{"x-upsert":String(g.upsert)}),i=g.metadata;"undefined"!=typeof Blob&&e instanceof Blob?((d=new FormData).append("cacheControl",g.cacheControl),i&&d.append("metadata",this.encodeMetadata(i)),d.append("",e)):"undefined"!=typeof FormData&&e instanceof FormData?((d=e).append("cacheControl",g.cacheControl),i&&d.append("metadata",this.encodeMetadata(i))):(d=e,h["cache-control"]=`max-age=${g.cacheControl}`,h["content-type"]=g.contentType,i&&(h["x-metadata"]=this.toBase64(this.encodeMetadata(i)))),(null==f?void 0:f.headers)&&(h=Object.assign(Object.assign({},h),f.headers));let j=this._removeEmptyFolders(b),k=this._getFinalPath(j),l=yield this.fetch(`${this.url}/object/${k}`,Object.assign({method:a,body:d,headers:h},(null==g?void 0:g.duplex)?{duplex:g.duplex}:{})),m=yield l.json();if(l.ok)return{data:{path:j,id:m.Id,fullPath:m.Key},error:null};return{data:null,error:m}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}upload(a,b,c){return g(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",a,b,c)})}uploadToSignedUrl(a,b,e,f){return g(this,void 0,void 0,function*(){let g=this._removeEmptyFolders(a),h=this._getFinalPath(g),i=new URL(this.url+`/object/upload/sign/${h}`);i.searchParams.set("token",b);try{let a,b=Object.assign({upsert:c.upsert},f),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(b.upsert)});"undefined"!=typeof Blob&&e instanceof Blob?((a=new FormData).append("cacheControl",b.cacheControl),a.append("",e)):"undefined"!=typeof FormData&&e instanceof FormData?(a=e).append("cacheControl",b.cacheControl):(a=e,d["cache-control"]=`max-age=${b.cacheControl}`,d["content-type"]=b.contentType);let h=yield this.fetch(i.toString(),{method:"PUT",body:a,headers:d}),j=yield h.json();if(h.ok)return{data:{path:g,fullPath:j.Key},error:null};return{data:null,error:j}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(a,b){return g(this,void 0,void 0,function*(){try{let c=this._getFinalPath(a),f=Object.assign({},this.headers);(null==b?void 0:b.upsert)&&(f["x-upsert"]="true");let g=yield(0,e.post)(this.fetch,`${this.url}/object/upload/sign/${c}`,{},{headers:f}),h=new URL(this.url+g.url),i=h.searchParams.get("token");if(!i)throw new d.StorageError("No token returned by API");return{data:{signedUrl:h.toString(),path:a,token:i},error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}update(a,b,c){return g(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",a,b,c)})}move(a,b,c){return g(this,void 0,void 0,function*(){try{return{data:yield(0,e.post)(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:a,destinationKey:b,destinationBucket:null==c?void 0:c.destinationBucket},{headers:this.headers}),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}copy(a,b,c){return g(this,void 0,void 0,function*(){try{return{data:{path:(yield(0,e.post)(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:a,destinationKey:b,destinationBucket:null==c?void 0:c.destinationBucket},{headers:this.headers})).Key},error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}createSignedUrl(a,b,c){return g(this,void 0,void 0,function*(){try{let d=this._getFinalPath(a),f=yield(0,e.post)(this.fetch,`${this.url}/object/sign/${d}`,Object.assign({expiresIn:b},(null==c?void 0:c.transform)?{transform:c.transform}:{}),{headers:this.headers}),g=(null==c?void 0:c.download)?`&download=${!0===c.download?"":c.download}`:"";return{data:f={signedUrl:encodeURI(`${this.url}${f.signedURL}${g}`)},error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}createSignedUrls(a,b,c){return g(this,void 0,void 0,function*(){try{let d=yield(0,e.post)(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:b,paths:a},{headers:this.headers}),f=(null==c?void 0:c.download)?`&download=${!0===c.download?"":c.download}`:"";return{data:d.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${f}`):null})),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}download(a,b){return g(this,void 0,void 0,function*(){let c=void 0!==(null==b?void 0:b.transform),f=this.transformOptsToQueryString((null==b?void 0:b.transform)||{}),g=f?`?${f}`:"";try{let b=this._getFinalPath(a),d=yield(0,e.get)(this.fetch,`${this.url}/${c?"render/image/authenticated":"object"}/${b}${g}`,{headers:this.headers,noResolveJson:!0});return{data:yield d.blob(),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}info(a){return g(this,void 0,void 0,function*(){let b=this._getFinalPath(a);try{let a=yield(0,e.get)(this.fetch,`${this.url}/object/info/${b}`,{headers:this.headers});return{data:(0,f.recursiveToCamel)(a),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}exists(a){return g(this,void 0,void 0,function*(){let b=this._getFinalPath(a);try{return yield(0,e.head)(this.fetch,`${this.url}/object/${b}`,{headers:this.headers}),{data:!0,error:null}}catch(a){if((0,d.isStorageError)(a)&&a instanceof d.StorageUnknownError){let b=a.originalError;if([400,404].includes(null==b?void 0:b.status))return{data:!1,error:a}}throw a}})}getPublicUrl(a,b){let c=this._getFinalPath(a),d=[],e=(null==b?void 0:b.download)?`download=${!0===b.download?"":b.download}`:"";""!==e&&d.push(e);let f=void 0!==(null==b?void 0:b.transform),g=this.transformOptsToQueryString((null==b?void 0:b.transform)||{});""!==g&&d.push(g);let h=d.join("&");return""!==h&&(h=`?${h}`),{data:{publicUrl:encodeURI(`${this.url}/${f?"render/image":"object"}/public/${c}${h}`)}}}remove(a){return g(this,void 0,void 0,function*(){try{return{data:yield(0,e.remove)(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:a},{headers:this.headers}),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}list(a,c,f){return g(this,void 0,void 0,function*(){try{let d=Object.assign(Object.assign(Object.assign({},b),c),{prefix:a||""});return{data:yield(0,e.post)(this.fetch,`${this.url}/object/list/${this.bucketId}`,d,{headers:this.headers},f),error:null}}catch(a){if((0,d.isStorageError)(a))return{data:null,error:a};throw a}})}encodeMetadata(a){return JSON.stringify(a)}toBase64(a){return"undefined"!=typeof Buffer?Buffer.from(a).toString("base64"):btoa(a)}_getFinalPath(a){return`${this.bucketId}/${a}`}_removeEmptyFolders(a){return a.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(a){let b=[];return a.width&&b.push(`width=${a.width}`),a.height&&b.push(`height=${a.height}`),a.resize&&b.push(`resize=${a.resize}`),a.format&&b.push(`format=${a.format}`),a.quality&&b.push(`quality=${a.quality}`),b.join("&")}}}},120336:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({version:()=>b});let b="2.7.1"}},772524:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DEFAULT_HEADERS:()=>b});var d=a.i(120336);let b={"X-Client-Info":`storage-js/${d.version}`}}},777333:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(772524),e=a.i(831909),f=a.i(517405),g=a.i(878321),h=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};class b{constructor(a,b={},c){this.url=a,this.headers=Object.assign(Object.assign({},d.DEFAULT_HEADERS),b),this.fetch=(0,g.resolveFetch)(c)}listBuckets(){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.get)(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}getBucket(a){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.get)(this.fetch,`${this.url}/bucket/${a}`,{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}createBucket(a,b={public:!1}){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.post)(this.fetch,`${this.url}/bucket`,{id:a,name:a,public:b.public,file_size_limit:b.fileSizeLimit,allowed_mime_types:b.allowedMimeTypes},{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}updateBucket(a,b){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.put)(this.fetch,`${this.url}/bucket/${a}`,{id:a,name:a,public:b.public,file_size_limit:b.fileSizeLimit,allowed_mime_types:b.allowedMimeTypes},{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}emptyBucket(a){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.post)(this.fetch,`${this.url}/bucket/${a}/empty`,{},{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}deleteBucket(a){return h(this,void 0,void 0,function*(){try{return{data:yield(0,f.remove)(this.fetch,`${this.url}/bucket/${a}`,{},{headers:this.headers}),error:null}}catch(a){if((0,e.isStorageError)(a))return{data:null,error:a};throw a}})}}}},506437:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({StorageClient:()=>b});var d=a.i(8526),e=a.i(777333);class b extends e.default{constructor(a,b={},c){super(a,b,c)}from(a){return new d.default(this.url,this.headers,a,this.fetch)}}}},608144:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({version:()=>b});let b="2.49.8"}},306737:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DEFAULT_AUTH_OPTIONS:()=>g,DEFAULT_DB_OPTIONS:()=>f,DEFAULT_GLOBAL_OPTIONS:()=>e,DEFAULT_HEADERS:()=>c,DEFAULT_REALTIME_OPTIONS:()=>h});var d=a.i(608144);let b="";b="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let c={"X-Client-Info":`supabase-js-${b}/${d.version}`},e={headers:c},f={schema:"public"},g={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},h={}}},913619:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({fetchWithAuth:()=>f,resolveFetch:()=>b,resolveHeadersConstructor:()=>c});var d=a.i(540080),e=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let b=a=>{let b;return b=a||("undefined"==typeof fetch?d.default:fetch),(...a)=>b(...a)},c=()=>"undefined"==typeof Headers?d.Headers:Headers,f=(a,d,f)=>{let g=b(f),h=c();return(b,c)=>e(void 0,void 0,void 0,function*(){var e;let f=null!=(e=yield d())?e:a,i=new h(null==c?void 0:c.headers);return i.has("apikey")||i.set("apikey",a),i.has("Authorization")||i.set("Authorization",`Bearer ${f}`),g(b,Object.assign(Object.assign({},c),{headers:i}))})}}},528049:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({applySettingDefaults:()=>g,ensureTrailingSlash:()=>f,isBrowser:()=>b,uuid:()=>e});var d=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};function e(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0;return("x"==a?b:3&b|8).toString(16)})}function f(a){return a.endsWith("/")?a:a+"/"}let b=()=>"undefined"!=typeof window;function g(a,b){var c,e;let{db:f,auth:g,realtime:h,global:i}=a,{db:j,auth:k,realtime:l,global:m}=b,n={db:Object.assign(Object.assign({},j),f),auth:Object.assign(Object.assign({},k),g),realtime:Object.assign(Object.assign({},l),h),global:Object.assign(Object.assign(Object.assign({},m),i),{headers:Object.assign(Object.assign({},null!=(c=null==m?void 0:m.headers)?c:{}),null!=(e=null==i?void 0:i.headers)?e:{})}),accessToken:()=>d(this,void 0,void 0,function*(){return""})};return a.accessToken?n.accessToken=a.accessToken:delete n.accessToken,n}}},606030:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({version:()=>b});let b="2.69.1"}},426657:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({API_VERSIONS:()=>l,API_VERSION_HEADER_NAME:()=>k,AUDIENCE:()=>h,AUTO_REFRESH_TICK_DURATION_MS:()=>b,AUTO_REFRESH_TICK_THRESHOLD:()=>c,BASE64URL_REGEX:()=>m,DEFAULT_HEADERS:()=>i,EXPIRY_MARGIN_MS:()=>e,GOTRUE_URL:()=>f,JWKS_TTL:()=>n,NETWORK_FAILURE:()=>j,STORAGE_KEY:()=>g});var d=a.i(606030);let b=3e4,c=3,e=9e4,f="http://localhost:9999",g="supabase.auth.token",h="",i={"X-Client-Info":`gotrue-js/${d.version}`},j={MAX_RETRIES:10,RETRY_INTERVAL:2},k="X-Supabase-Api-Version",l={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},m=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,n=6e5}},523934:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AuthApiError:()=>c,AuthError:()=>b,AuthImplicitGrantRedirectError:()=>o,AuthInvalidCredentialsError:()=>n,AuthInvalidJwtError:()=>s,AuthInvalidTokenResponseError:()=>m,AuthPKCEGrantCodeExchangeError:()=>p,AuthRetryableFetchError:()=>q,AuthSessionMissingError:()=>l,AuthUnknownError:()=>j,AuthWeakPasswordError:()=>r,CustomAuthError:()=>k,isAuthApiError:()=>e,isAuthError:()=>d,isAuthImplicitGrantRedirectError:()=>g,isAuthRetryableFetchError:()=>h,isAuthSessionMissingError:()=>f,isAuthWeakPasswordError:()=>i});class b extends Error{constructor(a,b,c){super(a),this.__isAuthError=!0,this.name="AuthError",this.status=b,this.code=c}}function d(a){return"object"==typeof a&&null!==a&&"__isAuthError"in a}class c extends b{constructor(a,b,c){super(a,b,c),this.name="AuthApiError",this.status=b,this.code=c}}function e(a){return d(a)&&"AuthApiError"===a.name}class j extends b{constructor(a,b){super(a),this.name="AuthUnknownError",this.originalError=b}}class k extends b{constructor(a,b,c,d){super(a,c,d),this.name=b,this.status=c}}class l extends k{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function f(a){return d(a)&&"AuthSessionMissingError"===a.name}class m extends k{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class n extends k{constructor(a){super(a,"AuthInvalidCredentialsError",400,void 0)}}class o extends k{constructor(a,b=null){super(a,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=b}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function g(a){return d(a)&&"AuthImplicitGrantRedirectError"===a.name}class p extends k{constructor(a,b=null){super(a,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=b}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class q extends k{constructor(a,b){super(a,"AuthRetryableFetchError",b,void 0)}}function h(a){return d(a)&&"AuthRetryableFetchError"===a.name}class r extends k{constructor(a,b,c){super(a,"AuthWeakPasswordError",b,"weak_password"),this.reasons=c}}function i(a){return d(a)&&"AuthWeakPasswordError"===a.name}class s extends k{constructor(a){super(a,"AuthInvalidJwtError",400,"invalid_jwt")}}}},891909:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({base64UrlToUint8Array:()=>k,byteFromBase64URL:()=>e,byteToBase64URL:()=>d,codepointToUTF8:()=>h,stringFromBase64URL:()=>g,stringFromUTF8:()=>j,stringToBase64URL:()=>f,stringToUTF8:()=>i,stringToUint8Array:()=>l});let b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),c=" 	\n\r=".split(""),m=(()=>{let a=Array(128);for(let b=0;b<a.length;b+=1)a[b]=-1;for(let b=0;b<c.length;b+=1)a[c[b].charCodeAt(0)]=-2;for(let c=0;c<b.length;c+=1)a[b[c].charCodeAt(0)]=c;return a})();function d(a,c,d){if(null!==a)for(c.queue=c.queue<<8|a,c.queuedBits+=8;c.queuedBits>=6;)d(b[c.queue>>c.queuedBits-6&63]),c.queuedBits-=6;else if(c.queuedBits>0)for(c.queue=c.queue<<6-c.queuedBits,c.queuedBits=6;c.queuedBits>=6;)d(b[c.queue>>c.queuedBits-6&63]),c.queuedBits-=6}function e(a,b,c){let d=m[a];if(d>-1)for(b.queue=b.queue<<6|d,b.queuedBits+=6;b.queuedBits>=8;)c(b.queue>>b.queuedBits-8&255),b.queuedBits-=8;else if(-2===d)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(a)}"`)}function f(a){let b=[],c=a=>{b.push(a)},e={queue:0,queuedBits:0};return i(a,a=>{d(a,e,c)}),d(null,e,c),b.join("")}function g(a){let b=[],c=a=>{b.push(String.fromCodePoint(a))},d={utf8seq:0,codepoint:0},f={queue:0,queuedBits:0},g=a=>{j(a,d,c)};for(let b=0;b<a.length;b+=1)e(a.charCodeAt(b),f,g);return b.join("")}function h(a,b){if(a<=127)return void b(a);if(a<=2047){b(192|a>>6),b(128|63&a);return}if(a<=65535){b(224|a>>12),b(128|a>>6&63),b(128|63&a);return}if(a<=1114111){b(240|a>>18),b(128|a>>12&63),b(128|a>>6&63),b(128|63&a);return}throw Error(`Unrecognized Unicode codepoint: ${a.toString(16)}`)}function i(a,b){for(let c=0;c<a.length;c+=1){let d=a.charCodeAt(c);if(d>55295&&d<=56319){let b=(d-55296)*1024&65535;d=(a.charCodeAt(c+1)-56320&65535|b)+65536,c+=1}h(d,b)}}function j(a,b,c){if(0===b.utf8seq){if(a<=127)return void c(a);for(let c=1;c<6;c+=1)if((a>>7-c&1)==0){b.utf8seq=c;break}if(2===b.utf8seq)b.codepoint=31&a;else if(3===b.utf8seq)b.codepoint=15&a;else if(4===b.utf8seq)b.codepoint=7&a;else throw Error("Invalid UTF-8 sequence");b.utf8seq-=1}else if(b.utf8seq>0){if(a<=127)throw Error("Invalid UTF-8 sequence");b.codepoint=b.codepoint<<6|63&a,b.utf8seq-=1,0===b.utf8seq&&c(b.codepoint)}}function k(a){let b=[],c={queue:0,queuedBits:0},d=a=>{b.push(a)};for(let b=0;b<a.length;b+=1)e(a.charCodeAt(b),c,d);return new Uint8Array(b)}function l(a){let b=[];return i(a,a=>b.push(a)),new Uint8Array(b)}}},16007:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Deferred:()=>A,decodeJWT:()=>j,expiresAt:()=>g,generatePKCEChallenge:()=>p,generatePKCEVerifier:()=>n,getAlgorithm:()=>t,getCodeChallengeAndMethod:()=>q,getItemAsync:()=>y,isBrowser:()=>b,looksLikeFetchResponse:()=>w,parseParametersFromURL:()=>i,parseResponseAPIVersion:()=>r,removeItemAsync:()=>z,resolveFetch:()=>v,retryable:()=>l,setItemAsync:()=>x,sleep:()=>k,supportsLocalStorage:()=>u,uuid:()=>h,validateExp:()=>s});var d=a.i(426657),e=a.i(523934),f=a.i(891909);function g(a){return Math.round(Date.now()/1e3)+a}function h(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){let b=16*Math.random()|0;return("x"==a?b:3&b|8).toString(16)})}let b=()=>"undefined"!=typeof window&&"undefined"!=typeof document,c={tested:!1,writable:!1},u=()=>{if(!b())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(a){return!1}if(c.tested)return c.writable;let a=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(a,a),globalThis.localStorage.removeItem(a),c.tested=!0,c.writable=!0}catch(a){c.tested=!0,c.writable=!1}return c.writable};function i(a){let b={},c=new URL(a);if(c.hash&&"#"===c.hash[0])try{new URLSearchParams(c.hash.substring(1)).forEach((a,c)=>{b[c]=a})}catch(a){}return c.searchParams.forEach((a,c)=>{b[c]=a}),b}let v=b=>{let c;return c=b||("undefined"==typeof fetch?(...b)=>a.r(578285)(a.i).then(({default:a})=>a(...b)):fetch),(...a)=>c(...a)},w=a=>"object"==typeof a&&null!==a&&"status"in a&&"ok"in a&&"json"in a&&"function"==typeof a.json,x=async(a,b,c)=>{await a.setItem(b,JSON.stringify(c))},y=async(a,b)=>{let c=await a.getItem(b);if(!c)return null;try{return JSON.parse(c)}catch(a){return c}},z=async(a,b)=>{await a.removeItem(b)};class A{constructor(){this.promise=new A.promiseConstructor((a,b)=>{this.resolve=a,this.reject=b})}}function j(a){let b=a.split(".");if(3!==b.length)throw new e.AuthInvalidJwtError("Invalid JWT structure");for(let a=0;a<b.length;a++)if(!d.BASE64URL_REGEX.test(b[a]))throw new e.AuthInvalidJwtError("JWT not in base64url format");return{header:JSON.parse((0,f.stringFromBase64URL)(b[0])),payload:JSON.parse((0,f.stringFromBase64URL)(b[1])),signature:(0,f.base64UrlToUint8Array)(b[2]),raw:{header:b[0],payload:b[1]}}}async function k(a){return await new Promise(b=>{setTimeout(()=>b(null),a)})}function l(a,b){return new Promise((c,d)=>{(async()=>{for(let e=0;e<1/0;e++)try{let d=await a(e);if(!b(e,null,d))return void c(d)}catch(a){if(!b(e,a))return void d(a)}})()})}function m(a){return("0"+a.toString(16)).substr(-2)}function n(){let a=new Uint32Array(56);if("undefined"==typeof crypto){let a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",b=a.length,c="";for(let d=0;d<56;d++)c+=a.charAt(Math.floor(Math.random()*b));return c}return crypto.getRandomValues(a),Array.from(a,m).join("")}async function o(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>String.fromCharCode(a)).join("")}async function p(a){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),a):btoa(await o(a)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function q(a,b,c=!1){let d=n(),e=d;c&&(e+="/PASSWORD_RECOVERY"),await x(a,`${b}-code-verifier`,e);let f=await p(d),g=d===f?"plain":"s256";return[f,g]}A.promiseConstructor=Promise;let B=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function r(a){let b=a.headers.get(d.API_VERSION_HEADER_NAME);if(!b||!b.match(B))return null;try{return new Date(`${b}T00:00:00.0Z`)}catch(a){return null}}function s(a){if(!a)throw Error("Missing exp claim");if(a<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}function t(a){switch(a){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}}},68217:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({_generateLinkResponse:()=>o,_noResolveJsonResponse:()=>p,_request:()=>i,_sessionResponse:()=>k,_sessionResponsePassword:()=>l,_ssoResponse:()=>n,_userResponse:()=>m,handleError:()=>h});var d=a.i(426657),e=a.i(16007),f=a.i(523934),g=this&&this.__rest||function(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c};let b=a=>a.msg||a.message||a.error_description||a.error||JSON.stringify(a),c=[502,503,504];async function h(a){var g;let h,i;if(!(0,e.looksLikeFetchResponse)(a))throw new f.AuthRetryableFetchError(b(a),0);if(c.includes(a.status))throw new f.AuthRetryableFetchError(b(a),a.status);try{h=await a.json()}catch(a){throw new f.AuthUnknownError(b(a),a)}let j=(0,e.parseResponseAPIVersion)(a);if(j&&j.getTime()>=d.API_VERSIONS["2024-01-01"].timestamp&&"object"==typeof h&&h&&"string"==typeof h.code?i=h.code:"object"==typeof h&&h&&"string"==typeof h.error_code&&(i=h.error_code),i){if("weak_password"===i)throw new f.AuthWeakPasswordError(b(h),a.status,(null==(g=h.weak_password)?void 0:g.reasons)||[]);else if("session_not_found"===i)throw new f.AuthSessionMissingError}else if("object"==typeof h&&h&&"object"==typeof h.weak_password&&h.weak_password&&Array.isArray(h.weak_password.reasons)&&h.weak_password.reasons.length&&h.weak_password.reasons.reduce((a,b)=>a&&"string"==typeof b,!0))throw new f.AuthWeakPasswordError(b(h),a.status,h.weak_password.reasons);throw new f.AuthApiError(b(h),a.status||500,i)}let q=(a,b,c,d)=>{let e={method:a,headers:(null==b?void 0:b.headers)||{}};return"GET"===a?e:(e.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==b?void 0:b.headers),e.body=JSON.stringify(d),Object.assign(Object.assign({},e),c))};async function i(a,b,c,e){var f;let g=Object.assign({},null==e?void 0:e.headers);g[d.API_VERSION_HEADER_NAME]||(g[d.API_VERSION_HEADER_NAME]=d.API_VERSIONS["2024-01-01"].name),(null==e?void 0:e.jwt)&&(g.Authorization=`Bearer ${e.jwt}`);let h=null!=(f=null==e?void 0:e.query)?f:{};(null==e?void 0:e.redirectTo)&&(h.redirect_to=e.redirectTo);let i=Object.keys(h).length?"?"+new URLSearchParams(h).toString():"",k=await j(a,b,c+i,{headers:g,noResolveJson:null==e?void 0:e.noResolveJson},{},null==e?void 0:e.body);return(null==e?void 0:e.xform)?null==e?void 0:e.xform(k):{data:Object.assign({},k),error:null}}async function j(a,c,d,e,g,i){let j,k=q(c,e,g,i);try{j=await a(d,Object.assign({},k))}catch(a){throw console.error(a),new f.AuthRetryableFetchError(b(a),0)}if(j.ok||await h(j),null==e?void 0:e.noResolveJson)return j;try{return await j.json()}catch(a){await h(a)}}function k(a){var b,c;let d=null;return(c=a).access_token&&c.refresh_token&&c.expires_in&&(d=Object.assign({},a),a.expires_at||(d.expires_at=(0,e.expiresAt)(a.expires_in))),{data:{session:d,user:null!=(b=a.user)?b:a},error:null}}function l(a){let b=k(a);return!b.error&&a.weak_password&&"object"==typeof a.weak_password&&Array.isArray(a.weak_password.reasons)&&a.weak_password.reasons.length&&a.weak_password.message&&"string"==typeof a.weak_password.message&&a.weak_password.reasons.reduce((a,b)=>a&&"string"==typeof b,!0)&&(b.data.weak_password=a.weak_password),b}function m(a){var b;return{data:{user:null!=(b=a.user)?b:a},error:null}}function n(a){return{data:a,error:null}}function o(a){let{action_link:b,email_otp:c,hashed_token:d,redirect_to:e,verification_type:f}=a;return{data:{properties:{action_link:b,email_otp:c,hashed_token:d,redirect_to:e,verification_type:f},user:Object.assign({},g(a,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function p(a){return a}}},333022:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(68217),e=a.i(16007),f=a.i(523934),g=this&&this.__rest||function(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c};class b{constructor({url:a="",headers:b={},fetch:c}){this.url=a,this.headers=b,this.fetch=(0,e.resolveFetch)(c),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(a,b="global"){try{return await (0,d._request)(this.fetch,"POST",`${this.url}/logout?scope=${b}`,{headers:this.headers,jwt:a,noResolveJson:!0}),{data:null,error:null}}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async inviteUserByEmail(a,b={}){try{return await (0,d._request)(this.fetch,"POST",`${this.url}/invite`,{body:{email:a,data:b.data},headers:this.headers,redirectTo:b.redirectTo,xform:d._userResponse})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async generateLink(a){try{let{options:b}=a,c=g(a,["options"]),e=Object.assign(Object.assign({},c),b);return"newEmail"in c&&(e.new_email=null==c?void 0:c.newEmail,delete e.newEmail),await (0,d._request)(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:e,headers:this.headers,xform:d._generateLinkResponse,redirectTo:null==b?void 0:b.redirectTo})}catch(a){if((0,f.isAuthError)(a))return{data:{properties:null,user:null},error:a};throw a}}async createUser(a){try{return await (0,d._request)(this.fetch,"POST",`${this.url}/admin/users`,{body:a,headers:this.headers,xform:d._userResponse})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async listUsers(a){var b,c,e,g,h,i,j;try{let f={nextPage:null,lastPage:0,total:0},k=await (0,d._request)(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(c=null==(b=null==a?void 0:a.page)?void 0:b.toString())?c:"",per_page:null!=(g=null==(e=null==a?void 0:a.perPage)?void 0:e.toString())?g:""},xform:d._noResolveJsonResponse});if(k.error)throw k.error;let l=await k.json(),m=null!=(h=k.headers.get("x-total-count"))?h:0,n=null!=(j=null==(i=k.headers.get("link"))?void 0:i.split(","))?j:[];return n.length>0&&(n.forEach(a=>{let b=parseInt(a.split(";")[0].split("=")[1].substring(0,1)),c=JSON.parse(a.split(";")[1].split("=")[1]);f[`${c}Page`]=b}),f.total=parseInt(m)),{data:Object.assign(Object.assign({},l),f),error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{users:[]},error:a};throw a}}async getUserById(a){try{return await (0,d._request)(this.fetch,"GET",`${this.url}/admin/users/${a}`,{headers:this.headers,xform:d._userResponse})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async updateUserById(a,b){try{return await (0,d._request)(this.fetch,"PUT",`${this.url}/admin/users/${a}`,{body:b,headers:this.headers,xform:d._userResponse})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async deleteUser(a,b=!1){try{return await (0,d._request)(this.fetch,"DELETE",`${this.url}/admin/users/${a}`,{headers:this.headers,body:{should_soft_delete:b},xform:d._userResponse})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async _listFactors(a){try{let{data:b,error:c}=await (0,d._request)(this.fetch,"GET",`${this.url}/admin/users/${a.userId}/factors`,{headers:this.headers,xform:a=>({data:{factors:a},error:null})});return{data:b,error:c}}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async _deleteFactor(a){try{return{data:await (0,d._request)(this.fetch,"DELETE",`${this.url}/admin/users/${a.userId}/factors/${a.id}`,{headers:this.headers}),error:null}}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}}}},612154:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({localStorageAdapter:()=>b,memoryLocalStorageAdapter:()=>e});var d=a.i(16007);let b={getItem:a=>(0,d.supportsLocalStorage)()?globalThis.localStorage.getItem(a):null,setItem:(a,b)=>{(0,d.supportsLocalStorage)()&&globalThis.localStorage.setItem(a,b)},removeItem:a=>{(0,d.supportsLocalStorage)()&&globalThis.localStorage.removeItem(a)}};function e(a={}){return{getItem:b=>a[b]||null,setItem:(b,c)=>{a[b]=c},removeItem:b=>{delete a[b]}}}}},316983:a=>{"use strict";var{g:b,__dirname:c}=a;function d(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(a){"undefined"!=typeof self&&(self.globalThis=self)}}a.s({polyfillGlobalThis:()=>d})},716523:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({LockAcquireTimeoutError:()=>c,NavigatorLockAcquireTimeoutError:()=>g,ProcessLockAcquireTimeoutError:()=>h,internals:()=>b,navigatorLock:()=>e,processLock:()=>f});var d=a.i(16007);let b={debug:!!(globalThis&&(0,d.supportsLocalStorage)()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class c extends Error{constructor(a){super(a),this.isAcquireTimeout=!0}}class g extends c{}class h extends c{}async function e(a,c,d){b.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",a,c);let e=new globalThis.AbortController;return c>0&&setTimeout(()=>{e.abort(),b.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",a)},c),await Promise.resolve().then(()=>globalThis.navigator.locks.request(a,0===c?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:e.signal},async e=>{if(e){b.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",a,e.name);try{return await d()}finally{b.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",a,e.name)}}if(0===c)throw b.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",a),new g(`Acquiring an exclusive Navigator LockManager lock "${a}" immediately failed`);if(b.debug)try{let a=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(a,null,"  "))}catch(a){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",a)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await d()}))}let i={};async function f(a,b,c){var d;let e=null!=(d=i[a])?d:Promise.resolve(),f=Promise.race([e.catch(()=>null),b>=0?new Promise((c,d)=>{setTimeout(()=>{d(new h(`Acquring process lock with name "${a}" timed out`))},b)}):null].filter(a=>a)).catch(a=>{if(a&&a.isAcquireTimeout)throw a;return null}).then(async()=>await c());return i[a]=f.catch(async a=>{if(a&&a.isAcquireTimeout)return await e,null;throw a}),await f}}},79877:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>c});var d=a.i(333022),e=a.i(426657),f=a.i(523934),g=a.i(68217),h=a.i(16007),i=a.i(612154),j=a.i(316983),k=a.i(606030),l=a.i(716523),m=a.i(891909);(0,j.polyfillGlobalThis)();let b={url:e.GOTRUE_URL,storageKey:e.STORAGE_KEY,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:e.DEFAULT_HEADERS,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function n(a,b,c){return await c()}class c{constructor(a){var e,f;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=c.nextInstanceID,c.nextInstanceID+=1,this.instanceID>0&&(0,h.isBrowser)()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let g=Object.assign(Object.assign({},b),a);if(this.logDebugMessages=!!g.debug,"function"==typeof g.debug&&(this.logger=g.debug),this.persistSession=g.persistSession,this.storageKey=g.storageKey,this.autoRefreshToken=g.autoRefreshToken,this.admin=new d.default({url:g.url,headers:g.headers,fetch:g.fetch}),this.url=g.url,this.headers=g.headers,this.fetch=(0,h.resolveFetch)(g.fetch),this.lock=g.lock||n,this.detectSessionInUrl=g.detectSessionInUrl,this.flowType=g.flowType,this.hasCustomAuthorizationHeader=g.hasCustomAuthorizationHeader,g.lock?this.lock=g.lock:(0,h.isBrowser)()&&(null==(e=null==globalThis?void 0:globalThis.navigator)?void 0:e.locks)?this.lock=l.navigatorLock:this.lock=n,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?g.storage?this.storage=g.storage:(0,h.supportsLocalStorage)()?this.storage=i.localStorageAdapter:(this.memoryStorage={},this.storage=(0,i.memoryLocalStorageAdapter)(this.memoryStorage)):(this.memoryStorage={},this.storage=(0,i.memoryLocalStorageAdapter)(this.memoryStorage)),(0,h.isBrowser)()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(a){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",a)}null==(f=this.broadcastChannel)||f.addEventListener("message",async a=>{this._debug("received broadcast notification from other tab or client",a),await this._notifyAllSubscribers(a.data.event,a.data.session,!1)})}this.initialize()}_debug(...a){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${k.version}) ${new Date().toISOString()}`,...a),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var a;try{let b=(0,h.parseParametersFromURL)(window.location.href),c="none";if(this._isImplicitGrantCallback(b)?c="implicit":await this._isPKCECallback(b)&&(c="pkce"),(0,h.isBrowser)()&&this.detectSessionInUrl&&"none"!==c){let{data:d,error:e}=await this._getSessionFromURL(b,c);if(e){if(this._debug("#_initialize()","error detecting session from URL",e),(0,f.isAuthImplicitGrantRedirectError)(e)){let b=null==(a=e.details)?void 0:a.code;if("identity_already_exists"===b||"identity_not_found"===b||"single_identity_not_deletable"===b)return{error:e}}return await this._removeSession(),{error:e}}let{session:g,redirectType:h}=d;return this._debug("#_initialize()","detected session in URL",g,"redirect type",h),await this._saveSession(g),setTimeout(async()=>{"recovery"===h?await this._notifyAllSubscribers("PASSWORD_RECOVERY",g):await this._notifyAllSubscribers("SIGNED_IN",g)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(a){if((0,f.isAuthError)(a))return{error:a};return{error:new f.AuthUnknownError("Unexpected error during initialization",a)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(a){var b,c,d;try{let{data:e,error:f}=await (0,g._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(c=null==(b=null==a?void 0:a.options)?void 0:b.data)?c:{},gotrue_meta_security:{captcha_token:null==(d=null==a?void 0:a.options)?void 0:d.captchaToken}},xform:g._sessionResponse});if(f||!e)return{data:{user:null,session:null},error:f};let h=e.session,i=e.user;return e.session&&(await this._saveSession(e.session),await this._notifyAllSubscribers("SIGNED_IN",h)),{data:{user:i,session:h},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(a){var b,c,d;try{let e;if("email"in a){let{email:c,password:d,options:f}=a,i=null,j=null;"pkce"===this.flowType&&([i,j]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey)),e=await (0,g._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==f?void 0:f.emailRedirectTo,body:{email:c,password:d,data:null!=(b=null==f?void 0:f.data)?b:{},gotrue_meta_security:{captcha_token:null==f?void 0:f.captchaToken},code_challenge:i,code_challenge_method:j},xform:g._sessionResponse})}else if("phone"in a){let{phone:b,password:f,options:h}=a;e=await (0,g._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:b,password:f,data:null!=(c=null==h?void 0:h.data)?c:{},channel:null!=(d=null==h?void 0:h.channel)?d:"sms",gotrue_meta_security:{captcha_token:null==h?void 0:h.captchaToken}},xform:g._sessionResponse})}else throw new f.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:i,error:j}=e;if(j||!i)return{data:{user:null,session:null},error:j};let k=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",k)),{data:{user:l,session:k},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(a){try{let b;if("email"in a){let{email:c,password:d,options:e}=a;b=await (0,g._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:c,password:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:g._sessionResponsePassword})}else if("phone"in a){let{phone:c,password:d,options:e}=a;b=await (0,g._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:c,password:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:g._sessionResponsePassword})}else throw new f.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:c,error:d}=b;if(d)return{data:{user:null,session:null},error:d};if(!c||!c.session||!c.user)return{data:{user:null,session:null},error:new f.AuthInvalidTokenResponseError};return c.session&&(await this._saveSession(c.session),await this._notifyAllSubscribers("SIGNED_IN",c.session)),{data:Object.assign({user:c.user,session:c.session},c.weak_password?{weakPassword:c.weak_password}:null),error:d}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithOAuth(a){var b,c,d,e;return await this._handleProviderSignIn(a.provider,{redirectTo:null==(b=a.options)?void 0:b.redirectTo,scopes:null==(c=a.options)?void 0:c.scopes,queryParams:null==(d=a.options)?void 0:d.queryParams,skipBrowserRedirect:null==(e=a.options)?void 0:e.skipBrowserRedirect})}async exchangeCodeForSession(a){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(a))}async _exchangeCodeForSession(a){let b=await (0,h.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`),[c,d]=(null!=b?b:"").split("/");try{let{data:b,error:e}=await (0,g._request)(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:a,code_verifier:c},xform:g._sessionResponse});if(await (0,h.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`),e)throw e;if(!b||!b.session||!b.user)return{data:{user:null,session:null,redirectType:null},error:new f.AuthInvalidTokenResponseError};return b.session&&(await this._saveSession(b.session),await this._notifyAllSubscribers("SIGNED_IN",b.session)),{data:Object.assign(Object.assign({},b),{redirectType:null!=d?d:null}),error:e}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(a){try{let{options:b,provider:c,token:d,access_token:e,nonce:h}=a,{data:i,error:j}=await (0,g._request)(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:c,id_token:d,access_token:e,nonce:h,gotrue_meta_security:{captcha_token:null==b?void 0:b.captchaToken}},xform:g._sessionResponse});if(j)return{data:{user:null,session:null},error:j};if(!i||!i.session||!i.user)return{data:{user:null,session:null},error:new f.AuthInvalidTokenResponseError};return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:i,error:j}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithOtp(a){var b,c,d,e,i;try{if("email"in a){let{email:d,options:e}=a,f=null,i=null;"pkce"===this.flowType&&([f,i]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{error:j}=await (0,g._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:d,data:null!=(b=null==e?void 0:e.data)?b:{},create_user:null==(c=null==e?void 0:e.shouldCreateUser)||c,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken},code_challenge:f,code_challenge_method:i},redirectTo:null==e?void 0:e.emailRedirectTo});return{data:{user:null,session:null},error:j}}if("phone"in a){let{phone:b,options:c}=a,{data:f,error:h}=await (0,g._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:b,data:null!=(d=null==c?void 0:c.data)?d:{},create_user:null==(e=null==c?void 0:c.shouldCreateUser)||e,gotrue_meta_security:{captcha_token:null==c?void 0:c.captchaToken},channel:null!=(i=null==c?void 0:c.channel)?i:"sms"}});return{data:{user:null,session:null,messageId:null==f?void 0:f.message_id},error:h}}throw new f.AuthInvalidCredentialsError("You must provide either an email or phone number.")}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(a){var b,c;try{let d,e;"options"in a&&(d=null==(b=a.options)?void 0:b.redirectTo,e=null==(c=a.options)?void 0:c.captchaToken);let{data:f,error:h}=await (0,g._request)(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},a),{gotrue_meta_security:{captcha_token:e}}),redirectTo:d,xform:g._sessionResponse});if(h)throw h;if(!f)throw Error("An error occurred on token verification.");let i=f.session,j=f.user;return(null==i?void 0:i.access_token)&&(await this._saveSession(i),await this._notifyAllSubscribers("recovery"==a.type?"PASSWORD_RECOVERY":"SIGNED_IN",i)),{data:{user:j,session:i},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithSSO(a){var b,c,d;try{let e=null,f=null;return"pkce"===this.flowType&&([e,f]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey)),await (0,g._request)(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in a?{provider_id:a.providerId}:null),"domain"in a?{domain:a.domain}:null),{redirect_to:null!=(c=null==(b=a.options)?void 0:b.redirectTo)?c:void 0}),(null==(d=null==a?void 0:a.options)?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:a.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:e,code_challenge_method:f}),headers:this.headers,xform:g._ssoResponse})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async a=>{let{data:{session:b},error:c}=a;if(c)throw c;if(!b)throw new f.AuthSessionMissingError;let{error:d}=await (0,g._request)(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:b.access_token});return{data:{user:null,session:null},error:d}})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async resend(a){try{let b=`${this.url}/resend`;if("email"in a){let{email:c,type:d,options:e}=a,{error:f}=await (0,g._request)(this.fetch,"POST",b,{headers:this.headers,body:{email:c,type:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},redirectTo:null==e?void 0:e.emailRedirectTo});return{data:{user:null,session:null},error:f}}if("phone"in a){let{phone:c,type:d,options:e}=a,{data:f,error:h}=await (0,g._request)(this.fetch,"POST",b,{headers:this.headers,body:{phone:c,type:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}}});return{data:{user:null,session:null,messageId:null==f?void 0:f.message_id},error:h}}throw new f.AuthInvalidCredentialsError("You must provide either an email or phone number and a type")}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async a=>a))}async _acquireLock(a,b){this._debug("#_acquireLock","begin",a);try{if(this.lockAcquired){let a=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),c=(async()=>(await a,await b()))();return this.pendingInLock.push((async()=>{try{await c}catch(a){}})()),c}return await this.lock(`lock:${this.storageKey}`,a,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let a=b();for(this.pendingInLock.push((async()=>{try{await a}catch(a){}})()),await a;this.pendingInLock.length;){let a=[...this.pendingInLock];await Promise.all(a),this.pendingInLock.splice(0,a.length)}return await a}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(a){this._debug("#_useSession","begin");try{let b=await this.__loadSession();return await a(b)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let a=null,b=await (0,h.getItemAsync)(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",b),null!==b&&(this._isValidSession(b)?a=b:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!a)return{data:{session:null},error:null};let c=!!a.expires_at&&1e3*a.expires_at-Date.now()<e.EXPIRY_MARGIN_MS;if(this._debug("#__loadSession()",`session has${c?"":" not"} expired`,"expires_at",a.expires_at),!c){if(this.storage.isServer){let b=this.suppressGetSessionWarning;a=new Proxy(a,{get:(a,c,d)=>(b||"user"!==c||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),b=!0,this.suppressGetSessionWarning=!0),Reflect.get(a,c,d))})}return{data:{session:a},error:null}}let{session:d,error:f}=await this._callRefreshToken(a.refresh_token);if(f)return{data:{session:null},error:f};return{data:{session:d},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(a){return a?await this._getUser(a):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(a){try{if(a)return await (0,g._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:a,xform:g._userResponse});return await this._useSession(async a=>{var b,c,d;let{data:e,error:h}=a;if(h)throw h;return(null==(b=e.session)?void 0:b.access_token)||this.hasCustomAuthorizationHeader?await (0,g._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(d=null==(c=e.session)?void 0:c.access_token)?d:void 0,xform:g._userResponse}):{data:{user:null},error:new f.AuthSessionMissingError}})}catch(a){if((0,f.isAuthError)(a))return(0,f.isAuthSessionMissingError)(a)&&(await this._removeSession(),await (0,h.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:a};throw a}}async updateUser(a,b={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(a,b))}async _updateUser(a,b={}){try{return await this._useSession(async c=>{let{data:d,error:e}=c;if(e)throw e;if(!d.session)throw new f.AuthSessionMissingError;let i=d.session,j=null,k=null;"pkce"===this.flowType&&null!=a.email&&([j,k]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{data:l,error:m}=await (0,g._request)(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==b?void 0:b.emailRedirectTo,body:Object.assign(Object.assign({},a),{code_challenge:j,code_challenge_method:k}),jwt:i.access_token,xform:g._userResponse});if(m)throw m;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null},error:a};throw a}}async setSession(a){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(a))}async _setSession(a){try{if(!a.access_token||!a.refresh_token)throw new f.AuthSessionMissingError;let b=Date.now()/1e3,c=b,d=!0,e=null,{payload:g}=(0,h.decodeJWT)(a.access_token);if(g.exp&&(d=(c=g.exp)<=b),d){let{session:b,error:c}=await this._callRefreshToken(a.refresh_token);if(c)return{data:{user:null,session:null},error:c};if(!b)return{data:{user:null,session:null},error:null};e=b}else{let{data:d,error:f}=await this._getUser(a.access_token);if(f)throw f;e={access_token:a.access_token,refresh_token:a.refresh_token,user:d.user,token_type:"bearer",expires_in:c-b,expires_at:c},await this._saveSession(e),await this._notifyAllSubscribers("SIGNED_IN",e)}return{data:{user:e.user,session:e},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{session:null,user:null},error:a};throw a}}async refreshSession(a){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(a))}async _refreshSession(a){try{return await this._useSession(async b=>{var c;if(!a){let{data:d,error:e}=b;if(e)throw e;a=null!=(c=d.session)?c:void 0}if(!(null==a?void 0:a.refresh_token))throw new f.AuthSessionMissingError;let{session:d,error:e}=await this._callRefreshToken(a.refresh_token);return e?{data:{user:null,session:null},error:e}:d?{data:{user:d.user,session:d},error:null}:{data:{user:null,session:null},error:null}})}catch(a){if((0,f.isAuthError)(a))return{data:{user:null,session:null},error:a};throw a}}async _getSessionFromURL(a,b){try{if(!(0,h.isBrowser)())throw new f.AuthImplicitGrantRedirectError("No browser detected.");if(a.error||a.error_description||a.error_code)throw new f.AuthImplicitGrantRedirectError(a.error_description||"Error in URL with unspecified error_description",{error:a.error||"unspecified_error",code:a.error_code||"unspecified_code"});switch(b){case"implicit":if("pkce"===this.flowType)throw new f.AuthPKCEGrantCodeExchangeError("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new f.AuthImplicitGrantRedirectError("Not a valid implicit grant flow url.")}if("pkce"===b){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!a.code)throw new f.AuthPKCEGrantCodeExchangeError("No code detected.");let{data:b,error:c}=await this._exchangeCodeForSession(a.code);if(c)throw c;let d=new URL(window.location.href);return d.searchParams.delete("code"),window.history.replaceState(window.history.state,"",d.toString()),{data:{session:b.session,redirectType:null},error:null}}let{provider_token:c,provider_refresh_token:d,access_token:g,refresh_token:i,expires_in:j,expires_at:k,token_type:l}=a;if(!g||!j||!i||!l)throw new f.AuthImplicitGrantRedirectError("No session defined in URL");let m=Math.round(Date.now()/1e3),n=parseInt(j),o=m+n;k&&(o=parseInt(k));let p=o-m;1e3*p<=e.AUTO_REFRESH_TICK_DURATION_MS&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${p}s, should have been closer to ${n}s`);let q=o-n;m-q>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",q,o,m):m-q<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",q,o,m);let{data:r,error:s}=await this._getUser(g);if(s)throw s;let t={provider_token:c,provider_refresh_token:d,access_token:g,expires_in:n,expires_at:o,refresh_token:i,token_type:l,user:r.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:t,redirectType:a.type},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:{session:null,redirectType:null},error:a};throw a}}_isImplicitGrantCallback(a){return!!(a.access_token||a.error_description)}async _isPKCECallback(a){let b=await (0,h.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`);return!!(a.code&&b)}async signOut(a={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(a))}async _signOut({scope:a}={scope:"global"}){return await this._useSession(async b=>{var c;let{data:d,error:e}=b;if(e)return{error:e};let g=null==(c=d.session)?void 0:c.access_token;if(g){let{error:b}=await this.admin.signOut(g,a);if(b&&!((0,f.isAuthApiError)(b)&&(404===b.status||401===b.status||403===b.status)))return{error:b}}return"others"!==a&&(await this._removeSession(),await (0,h.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(a){let b=(0,h.uuid)(),c={id:b,callback:a,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",b),this.stateChangeEmitters.delete(b)}};return this._debug("#onAuthStateChange()","registered callback with id",b),this.stateChangeEmitters.set(b,c),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(b)})})(),{data:{subscription:c}}}async _emitInitialSession(a){return await this._useSession(async b=>{var c,d;try{let{data:{session:d},error:e}=b;if(e)throw e;await (null==(c=this.stateChangeEmitters.get(a))?void 0:c.callback("INITIAL_SESSION",d)),this._debug("INITIAL_SESSION","callback id",a,"session",d)}catch(b){await (null==(d=this.stateChangeEmitters.get(a))?void 0:d.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",a,"error",b),console.error(b)}})}async resetPasswordForEmail(a,b={}){let c=null,d=null;"pkce"===this.flowType&&([c,d]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey,!0));try{return await (0,g._request)(this.fetch,"POST",`${this.url}/recover`,{body:{email:a,code_challenge:c,code_challenge_method:d,gotrue_meta_security:{captcha_token:b.captchaToken}},headers:this.headers,redirectTo:b.redirectTo})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async getUserIdentities(){var a;try{let{data:b,error:c}=await this.getUser();if(c)throw c;return{data:{identities:null!=(a=b.user.identities)?a:[]},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async linkIdentity(a){var b;try{let{data:c,error:d}=await this._useSession(async b=>{var c,d,e,f,h;let{data:i,error:j}=b;if(j)throw j;let k=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,a.provider,{redirectTo:null==(c=a.options)?void 0:c.redirectTo,scopes:null==(d=a.options)?void 0:d.scopes,queryParams:null==(e=a.options)?void 0:e.queryParams,skipBrowserRedirect:!0});return await (0,g._request)(this.fetch,"GET",k,{headers:this.headers,jwt:null!=(h=null==(f=i.session)?void 0:f.access_token)?h:void 0})});if(d)throw d;return!(0,h.isBrowser)()||(null==(b=a.options)?void 0:b.skipBrowserRedirect)||window.location.assign(null==c?void 0:c.url),{data:{provider:a.provider,url:null==c?void 0:c.url},error:null}}catch(b){if((0,f.isAuthError)(b))return{data:{provider:a.provider,url:null},error:b};throw b}}async unlinkIdentity(a){try{return await this._useSession(async b=>{var c,d;let{data:e,error:f}=b;if(f)throw f;return await (0,g._request)(this.fetch,"DELETE",`${this.url}/user/identities/${a.identity_id}`,{headers:this.headers,jwt:null!=(d=null==(c=e.session)?void 0:c.access_token)?d:void 0})})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async _refreshAccessToken(a){let b=`#_refreshAccessToken(${a.substring(0,5)}...)`;this._debug(b,"begin");try{let c=Date.now();return await (0,h.retryable)(async c=>(c>0&&await (0,h.sleep)(200*Math.pow(2,c-1)),this._debug(b,"refreshing attempt",c),await (0,g._request)(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:a},headers:this.headers,xform:g._sessionResponse})),(a,b)=>{let d=200*Math.pow(2,a);return b&&(0,f.isAuthRetryableFetchError)(b)&&Date.now()+d-c<e.AUTO_REFRESH_TICK_DURATION_MS})}catch(a){if(this._debug(b,"error",a),(0,f.isAuthError)(a))return{data:{session:null,user:null},error:a};throw a}finally{this._debug(b,"end")}}_isValidSession(a){return"object"==typeof a&&null!==a&&"access_token"in a&&"refresh_token"in a&&"expires_at"in a}async _handleProviderSignIn(a,b){let c=await this._getUrlForProvider(`${this.url}/authorize`,a,{redirectTo:b.redirectTo,scopes:b.scopes,queryParams:b.queryParams});return this._debug("#_handleProviderSignIn()","provider",a,"options",b,"url",c),(0,h.isBrowser)()&&!b.skipBrowserRedirect&&window.location.assign(c),{data:{provider:a,url:c},error:null}}async _recoverAndRefresh(){var a;let b="#_recoverAndRefresh()";this._debug(b,"begin");try{let c=await (0,h.getItemAsync)(this.storage,this.storageKey);if(this._debug(b,"session from storage",c),!this._isValidSession(c)){this._debug(b,"session is not valid"),null!==c&&await this._removeSession();return}let d=(null!=(a=c.expires_at)?a:1/0)*1e3-Date.now()<e.EXPIRY_MARGIN_MS;if(this._debug(b,`session has${d?"":" not"} expired with margin of ${e.EXPIRY_MARGIN_MS}s`),d){if(this.autoRefreshToken&&c.refresh_token){let{error:a}=await this._callRefreshToken(c.refresh_token);a&&(console.error(a),(0,f.isAuthRetryableFetchError)(a)||(this._debug(b,"refresh failed with a non-retryable error, removing the session",a),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",c)}catch(a){this._debug(b,"error",a),console.error(a);return}finally{this._debug(b,"end")}}async _callRefreshToken(a){var b,c;if(!a)throw new f.AuthSessionMissingError;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let d=`#_callRefreshToken(${a.substring(0,5)}...)`;this._debug(d,"begin");try{this.refreshingDeferred=new h.Deferred;let{data:b,error:c}=await this._refreshAccessToken(a);if(c)throw c;if(!b.session)throw new f.AuthSessionMissingError;await this._saveSession(b.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",b.session);let d={session:b.session,error:null};return this.refreshingDeferred.resolve(d),d}catch(a){if(this._debug(d,"error",a),(0,f.isAuthError)(a)){let c={session:null,error:a};return(0,f.isAuthRetryableFetchError)(a)||await this._removeSession(),null==(b=this.refreshingDeferred)||b.resolve(c),c}throw null==(c=this.refreshingDeferred)||c.reject(a),a}finally{this.refreshingDeferred=null,this._debug(d,"end")}}async _notifyAllSubscribers(a,b,c=!0){let d=`#_notifyAllSubscribers(${a})`;this._debug(d,"begin",b,`broadcast = ${c}`);try{this.broadcastChannel&&c&&this.broadcastChannel.postMessage({event:a,session:b});let d=[],e=Array.from(this.stateChangeEmitters.values()).map(async c=>{try{await c.callback(a,b)}catch(a){d.push(a)}});if(await Promise.all(e),d.length>0){for(let a=0;a<d.length;a+=1)console.error(d[a]);throw d[0]}}finally{this._debug(d,"end")}}async _saveSession(a){this._debug("#_saveSession()",a),this.suppressGetSessionWarning=!0,await (0,h.setItemAsync)(this.storage,this.storageKey,a)}async _removeSession(){this._debug("#_removeSession()"),await (0,h.removeItemAsync)(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let a=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{a&&(0,h.isBrowser)()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",a)}catch(a){console.error("removing visibilitychange callback failed",a)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let a=setInterval(()=>this._autoRefreshTokenTick(),e.AUTO_REFRESH_TICK_DURATION_MS);this.autoRefreshTicker=a,a&&"object"==typeof a&&"function"==typeof a.unref?a.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(a),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let a=this.autoRefreshTicker;this.autoRefreshTicker=null,a&&clearInterval(a)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let a=Date.now();try{return await this._useSession(async b=>{let{data:{session:c}}=b;if(!c||!c.refresh_token||!c.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let d=Math.floor((1e3*c.expires_at-a)/e.AUTO_REFRESH_TICK_DURATION_MS);this._debug("#_autoRefreshTokenTick()",`access token expires in ${d} ticks, a tick lasts ${e.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${e.AUTO_REFRESH_TICK_THRESHOLD} ticks`),d<=e.AUTO_REFRESH_TICK_THRESHOLD&&await this._callRefreshToken(c.refresh_token)})}catch(a){console.error("Auto refresh tick failed with error. This is likely a transient error.",a)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(a){if(a.isAcquireTimeout||a instanceof l.LockAcquireTimeoutError)this._debug("auto refresh token tick lock not available");else throw a}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!(0,h.isBrowser)()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(a){console.error("_handleVisibilityChange",a)}}async _onVisibilityChanged(a){let b=`#_onVisibilityChanged(${a})`;this._debug(b,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),a||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(b,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(a,b,c){let d=[`provider=${encodeURIComponent(b)}`];if((null==c?void 0:c.redirectTo)&&d.push(`redirect_to=${encodeURIComponent(c.redirectTo)}`),(null==c?void 0:c.scopes)&&d.push(`scopes=${encodeURIComponent(c.scopes)}`),"pkce"===this.flowType){let[a,b]=await (0,h.getCodeChallengeAndMethod)(this.storage,this.storageKey),c=new URLSearchParams({code_challenge:`${encodeURIComponent(a)}`,code_challenge_method:`${encodeURIComponent(b)}`});d.push(c.toString())}if(null==c?void 0:c.queryParams){let a=new URLSearchParams(c.queryParams);d.push(a.toString())}return(null==c?void 0:c.skipBrowserRedirect)&&d.push(`skip_http_redirect=${c.skipBrowserRedirect}`),`${a}?${d.join("&")}`}async _unenroll(a){try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;return e?{data:null,error:e}:await (0,g._request)(this.fetch,"DELETE",`${this.url}/factors/${a.factorId}`,{headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token})})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async _enroll(a){try{return await this._useSession(async b=>{var c,d;let{data:e,error:f}=b;if(f)return{data:null,error:f};let h=Object.assign({friendly_name:a.friendlyName,factor_type:a.factorType},"phone"===a.factorType?{phone:a.phone}:{issuer:a.issuer}),{data:i,error:j}=await (0,g._request)(this.fetch,"POST",`${this.url}/factors`,{body:h,headers:this.headers,jwt:null==(c=null==e?void 0:e.session)?void 0:c.access_token});return j?{data:null,error:j}:("totp"===a.factorType&&(null==(d=null==i?void 0:i.totp)?void 0:d.qr_code)&&(i.totp.qr_code=`data:image/svg+xml;utf-8,${i.totp.qr_code}`),{data:i,error:null})})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}async _verify(a){return this._acquireLock(-1,async()=>{try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;if(e)return{data:null,error:e};let{data:f,error:h}=await (0,g._request)(this.fetch,"POST",`${this.url}/factors/${a.factorId}/verify`,{body:{code:a.code,challenge_id:a.challengeId},headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token});return h?{data:null,error:h}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:h})})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}})}async _challenge(a){return this._acquireLock(-1,async()=>{try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;return e?{data:null,error:e}:await (0,g._request)(this.fetch,"POST",`${this.url}/factors/${a.factorId}/challenge`,{body:{channel:a.channel},headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token})})}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}})}async _challengeAndVerify(a){let{data:b,error:c}=await this._challenge({factorId:a.factorId});return c?{data:null,error:c}:await this._verify({factorId:a.factorId,challengeId:b.id,code:a.code})}async _listFactors(){let{data:{user:a},error:b}=await this.getUser();if(b)return{data:null,error:b};let c=(null==a?void 0:a.factors)||[],d=c.filter(a=>"totp"===a.factor_type&&"verified"===a.status),e=c.filter(a=>"phone"===a.factor_type&&"verified"===a.status);return{data:{all:c,totp:d,phone:e},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async a=>{var b,c;let{data:{session:d},error:e}=a;if(e)return{data:null,error:e};if(!d)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:f}=(0,h.decodeJWT)(d.access_token),g=null;f.aal&&(g=f.aal);let i=g;return(null!=(c=null==(b=d.user.factors)?void 0:b.filter(a=>"verified"===a.status))?c:[]).length>0&&(i="aal2"),{data:{currentLevel:g,nextLevel:i,currentAuthenticationMethods:f.amr||[]},error:null}}))}async fetchJwk(a,b={keys:[]}){let c=b.keys.find(b=>b.kid===a);if(c||(c=this.jwks.keys.find(b=>b.kid===a))&&this.jwks_cached_at+e.JWKS_TTL>Date.now())return c;let{data:d,error:h}=await (0,g._request)(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(h)throw h;if(!d.keys||0===d.keys.length)throw new f.AuthInvalidJwtError("JWKS is empty");if(this.jwks=d,this.jwks_cached_at=Date.now(),!(c=d.keys.find(b=>b.kid===a)))throw new f.AuthInvalidJwtError("No matching signing key found in JWKS");return c}async getClaims(a,b={keys:[]}){try{let c=a;if(!c){let{data:a,error:b}=await this.getSession();if(b||!a.session)return{data:null,error:b};c=a.session.access_token}let{header:d,payload:e,signature:g,raw:{header:i,payload:j}}=(0,h.decodeJWT)(c);if((0,h.validateExp)(e.exp),!d.kid||"HS256"===d.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:a}=await this.getUser(c);if(a)throw a;return{data:{claims:e,header:d,signature:g},error:null}}let k=(0,h.getAlgorithm)(d.alg),l=await this.fetchJwk(d.kid,b),n=await crypto.subtle.importKey("jwk",l,k,!0,["verify"]);if(!await crypto.subtle.verify(k,n,g,(0,m.stringToUint8Array)(`${i}.${j}`)))throw new f.AuthInvalidJwtError("Invalid JWT signature");return{data:{claims:e,header:d,signature:g},error:null}}catch(a){if((0,f.isAuthError)(a))return{data:null,error:a};throw a}}}c.nextInstanceID=0}},625117:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=a.i(333022).default}},680631:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});let b=a.i(79877).default}},629979:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({})},603234:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(333022),a.i(79877),a.i(625117),a.i(680631),a.i(629979),a.i(523934),a.i(716523)},915334:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(333022),a.i(79877),a.i(625117),a.i(680631),a.i(629979),a.i(523934),a.i(716523),a.i(603234)},741905:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AuthClient:()=>d.default});var d=a.i(680631)},528708:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SupabaseAuthClient:()=>b}),a.i(915334);var d=a.i(741905);class b extends d.AuthClient{constructor(a){super(a)}}}},801288:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(138160),e=a.i(894152);a.i(426437);var f=a.i(27493),g=a.i(506437),h=a.i(306737),i=a.i(913619),j=a.i(528049),k=a.i(528708),l=this&&this.__awaiter||function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};class b{constructor(a,b,c){var d,f,g;if(this.supabaseUrl=a,this.supabaseKey=b,!a)throw Error("supabaseUrl is required.");if(!b)throw Error("supabaseKey is required.");let k=new URL((0,j.ensureTrailingSlash)(a));this.realtimeUrl=new URL("realtime/v1",k),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",k),this.storageUrl=new URL("storage/v1",k),this.functionsUrl=new URL("functions/v1",k);let l=`sb-${k.hostname.split(".")[0]}-auth-token`,m={db:h.DEFAULT_DB_OPTIONS,realtime:h.DEFAULT_REALTIME_OPTIONS,auth:Object.assign(Object.assign({},h.DEFAULT_AUTH_OPTIONS),{storageKey:l}),global:h.DEFAULT_GLOBAL_OPTIONS},n=(0,j.applySettingDefaults)(null!=c?c:{},m);this.storageKey=null!=(d=n.auth.storageKey)?d:"",this.headers=null!=(f=n.global.headers)?f:{},n.accessToken?(this.accessToken=n.accessToken,this.auth=new Proxy({},{get:(a,b)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(b)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(g=n.auth)?g:{},this.headers,n.global.fetch),this.fetch=(0,i.fetchWithAuth)(b,this._getAccessToken.bind(this),n.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},n.realtime)),this.rest=new e.PostgrestClient(new URL("rest/v1",k).href,{headers:this.headers,schema:n.db.schema,fetch:this.fetch}),n.accessToken||this._listenForAuthEvents()}get functions(){return new d.FunctionsClient(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new g.StorageClient(this.storageUrl.href,this.headers,this.fetch)}from(a){return this.rest.from(a)}schema(a){return this.rest.schema(a)}rpc(a,b={},c={}){return this.rest.rpc(a,b,c)}channel(a,b={config:{}}){return this.realtime.channel(a,b)}getChannels(){return this.realtime.getChannels()}removeChannel(a){return this.realtime.removeChannel(a)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var a,b;return l(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();let{data:c}=yield this.auth.getSession();return null!=(b=null==(a=c.session)?void 0:a.access_token)?b:null})}_initSupabaseAuthClient({autoRefreshToken:a,persistSession:b,detectSessionInUrl:c,storage:d,storageKey:e,flowType:f,lock:g,debug:h},i,j){let l={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new k.SupabaseAuthClient({url:this.authUrl.href,headers:Object.assign(Object.assign({},l),i),storageKey:e,autoRefreshToken:a,persistSession:b,detectSessionInUrl:c,storage:d,flowType:f,lock:g,debug:h,fetch:j,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(a){return new f.RealtimeClient(this.realtimeUrl.href,Object.assign(Object.assign({},a),{params:Object.assign({apikey:this.supabaseKey},null==a?void 0:a.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((a,b)=>{this._handleTokenChanged(a,"CLIENT",null==b?void 0:b.access_token)})}_handleTokenChanged(a,b,c){("TOKEN_REFRESHED"===a||"SIGNED_IN"===a)&&this.changedAccessToken!==c?this.changedAccessToken=c:"SIGNED_OUT"===a&&(this.realtime.setAuth(),"STORAGE"==b&&this.auth.signOut(),this.changedAccessToken=void 0)}}}},525222:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createClient:()=>b});var d=a.i(801288);a.i(915334),a.i(894152),a.i(426437);let b=(a,b,c)=>new d.default(a,b,c)}},302258:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({VERSION:()=>b});let b="0.6.1"}},756372:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parse=function(a,b){let c=new j,d=a.length;if(d<2)return c;let e=b?.decode||h,i=0;do{let b=a.indexOf("=",i);if(-1===b)break;let h=a.indexOf(";",i),j=-1===h?d:h;if(b>j){i=a.lastIndexOf(";",b-1)+1;continue}let k=f(a,i,b),l=g(a,b,k),m=a.slice(k,l);if(void 0===c[m]){let d=f(a,b+1,j),h=g(a,j,d),i=e(a.slice(d,h));c[m]=i}i=j+1}while(i<d)return c},e.serialize=function(e,f,g){let h=g?.encode||encodeURIComponent;if(!a.test(e))throw TypeError(`argument name is invalid: ${e}`);let j=h(f);if(!b.test(j))throw TypeError(`argument val is invalid: ${f}`);let k=e+"="+j;if(!g)return k;if(void 0!==g.maxAge){if(!Number.isInteger(g.maxAge))throw TypeError(`option maxAge is invalid: ${g.maxAge}`);k+="; Max-Age="+g.maxAge}if(g.domain){if(!c.test(g.domain))throw TypeError(`option domain is invalid: ${g.domain}`);k+="; Domain="+g.domain}if(g.path){if(!d.test(g.path))throw TypeError(`option path is invalid: ${g.path}`);k+="; Path="+g.path}if(g.expires){var l;if(l=g.expires,"[object Date]"!==i.call(l)||!Number.isFinite(g.expires.valueOf()))throw TypeError(`option expires is invalid: ${g.expires}`);k+="; Expires="+g.expires.toUTCString()}if(g.httpOnly&&(k+="; HttpOnly"),g.secure&&(k+="; Secure"),g.partitioned&&(k+="; Partitioned"),g.priority)switch("string"==typeof g.priority?g.priority.toLowerCase():void 0){case"low":k+="; Priority=Low";break;case"medium":k+="; Priority=Medium";break;case"high":k+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${g.priority}`)}if(g.sameSite)switch("string"==typeof g.sameSite?g.sameSite.toLowerCase():g.sameSite){case!0:case"strict":k+="; SameSite=Strict";break;case"lax":k+="; SameSite=Lax";break;case"none":k+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${g.sameSite}`)}return k};let a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,b=/^[\u0021-\u003A\u003C-\u007E]*$/,c=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,d=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,j=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function f(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c)return c}function g(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function h(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}}},612711:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isBrowser:()=>g,parse:()=>b,parseCookieHeader:()=>e,serialize:()=>c,serializeCookieHeader:()=>f});var d=a.i(756372);let b=d.parse,c=d.serialize;function e(a){let b=(0,d.parse)(a);return Object.keys(b??{}).map(a=>({name:a,value:b[a]}))}function f(a,b,c){return(0,d.serialize)(a,b,c)}function g(){return"undefined"!=typeof window&&void 0!==window.document}}},577560:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DEFAULT_COOKIE_OPTIONS:()=>b});let b={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4}}},243655:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MAX_CHUNK_SIZE:()=>b,combineChunks:()=>f,createChunks:()=>e,deleteChunks:()=>g,isChunkLike:()=>d});let b=3180,c=/^(.*)[.](0|[1-9][0-9]*)$/;function d(a,b){if(a===b)return!0;let d=a.match(c);return!!d&&d[1]===b}function e(a,c,d){let e=d??b,f=encodeURIComponent(c);if(f.length<=e)return[{name:a,value:c}];let g=[];for(;f.length>0;){let a=f.slice(0,e),b=a.lastIndexOf("%");b>e-3&&(a=a.slice(0,b));let c="";for(;a.length>0;)try{c=decodeURIComponent(a);break}catch(b){if(b instanceof URIError&&"%"===a.at(-3)&&a.length>3)a=a.slice(0,a.length-3);else throw b}g.push(c),f=f.slice(a.length)}return g.map((b,c)=>({name:`${a}.${c}`,value:b}))}async function f(a,b){let c=await b(a);if(c)return c;let d=[];for(let c=0;;c++){let e=`${a}.${c}`,f=await b(e);if(!f)break;d.push(f)}return d.length>0?d.join(""):null}async function g(a,b,c){await b(a)&&await c(a);for(let d=0;;d++){let e=`${a}.${d}`;if(!await b(e))break;await c(e)}}}},227864:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({codepointToUTF8:()=>f,stringFromBase64URL:()=>e,stringFromUTF8:()=>h,stringToBase64URL:()=>d,stringToUTF8:()=>g});let b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),c=" 	\n\r=".split(""),i=(()=>{let a=Array(128);for(let b=0;b<a.length;b+=1)a[b]=-1;for(let b=0;b<c.length;b+=1)a[c[b].charCodeAt(0)]=-2;for(let c=0;c<b.length;c+=1)a[b[c].charCodeAt(0)]=c;return a})();function d(a){let c=[],d=0,e=0;if(g(a,a=>{for(d=d<<8|a,e+=8;e>=6;){let a=d>>e-6&63;c.push(b[a]),e-=6}}),e>0)for(d<<=6-e,e=6;e>=6;){let a=d>>e-6&63;c.push(b[a]),e-=6}return c.join("")}function e(a){let b=[],c=a=>{b.push(String.fromCodePoint(a))},d={utf8seq:0,codepoint:0},e=0,f=0;for(let b=0;b<a.length;b+=1){let g=i[a.charCodeAt(b)];if(g>-1)for(e=e<<6|g,f+=6;f>=8;)h(e>>f-8&255,d,c),f-=8;else if(-2===g)continue;else throw Error(`Invalid Base64-URL character "${a.at(b)}" at position ${b}`)}return b.join("")}function f(a,b){if(a<=127)return void b(a);if(a<=2047){b(192|a>>6),b(128|63&a);return}if(a<=65535){b(224|a>>12),b(128|a>>6&63),b(128|63&a);return}if(a<=1114111){b(240|a>>18),b(128|a>>12&63),b(128|a>>6&63),b(128|63&a);return}throw Error(`Unrecognized Unicode codepoint: ${a.toString(16)}`)}function g(a,b){for(let c=0;c<a.length;c+=1){let d=a.charCodeAt(c);if(d>55295&&d<=56319){let b=(d-55296)*1024&65535;d=(a.charCodeAt(c+1)-56320&65535|b)+65536,c+=1}f(d,b)}}function h(a,b,c){if(0===b.utf8seq){if(a<=127)return void c(a);for(let c=1;c<6;c+=1)if((a>>7-c&1)==0){b.utf8seq=c;break}if(2===b.utf8seq)b.codepoint=31&a;else if(3===b.utf8seq)b.codepoint=15&a;else if(4===b.utf8seq)b.codepoint=7&a;else throw Error("Invalid UTF-8 sequence");b.utf8seq-=1}else if(b.utf8seq>0){if(a<=127)throw Error("Invalid UTF-8 sequence");b.codepoint=b.codepoint<<6|63&a,b.utf8seq-=1,0===b.utf8seq&&c(b.codepoint)}}}},824631:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(612711),a.i(577560),a.i(243655),a.i(227864)},378652:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(612711),a.i(577560),a.i(243655),a.i(227864),a.i(824631)},567707:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({applyServerStorage:()=>j,createStorageFromOptions:()=>i});var d=a.i(756372);a.i(378652);var e=a.i(577560),f=a.i(243655),g=a.i(612711),h=a.i(227864);let b="base64-";function i(a,c){let i,k,l=a.cookies??null,m=a.cookieEncoding,n={},o={};if(l)if("get"in l){let a=async a=>{let b=a.flatMap(a=>[a,...Array.from({length:5}).map((b,c)=>`${a}.${c}`)]),c=[];for(let a=0;a<b.length;a+=1){let d=await l.get(b[a]);(d||"string"==typeof d)&&c.push({name:b[a],value:d})}return c};if(i=async b=>await a(b),"set"in l&&"remove"in l)k=async a=>{for(let b=0;b<a.length;b+=1){let{name:c,value:d,options:e}=a[b];d?await l.set(c,d,e):await l.remove(c,e)}};else if(c)k=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in l)if(i=async()=>await l.getAll(),"setAll"in l)k=l.setAll;else if(c)k=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${c?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,g.isBrowser)()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!c&&(0,g.isBrowser)()){let a=()=>{let a=(0,d.parse)(document.cookie);return Object.keys(a).map(b=>({name:b,value:a[b]??""}))};i=()=>a(),k=a=>{a.forEach(({name:a,value:b,options:c})=>{document.cookie=(0,d.serialize)(a,b,c)})}}else if(c)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else i=()=>[],k=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return c?{getAll:i,setAll:k,setItems:n,removedItems:o,storage:{isServer:!0,getItem:async a=>{if("string"==typeof n[a])return n[a];if(o[a])return null;let c=await i([a]),d=await (0,f.combineChunks)(a,async a=>{let b=c?.find(({name:b})=>b===a)||null;return b?b.value:null});if(!d)return null;let e=d;return"string"==typeof d&&d.startsWith(b)&&(e=(0,h.stringFromBase64URL)(d.substring(b.length))),e},setItem:async(b,c)=>{b.endsWith("-code-verifier")&&await j({getAll:i,setAll:k,setItems:{[b]:c},removedItems:{}},{cookieOptions:a?.cookieOptions??null,cookieEncoding:m}),n[b]=c,delete o[b]},removeItem:async a=>{delete n[a],o[a]=!0}}}:{getAll:i,setAll:k,setItems:n,removedItems:o,storage:{isServer:!1,getItem:async a=>{let c=await i([a]),d=await (0,f.combineChunks)(a,async a=>{let b=c?.find(({name:b})=>b===a)||null;return b?b.value:null});if(!d)return null;let e=d;return d.startsWith(b)&&(e=(0,h.stringFromBase64URL)(d.substring(b.length))),e},setItem:async(c,d)=>{let g=await i([c]),j=new Set((g?.map(({name:a})=>a)||[]).filter(a=>(0,f.isChunkLike)(a,c))),l=d;"base64url"===m&&(l=b+(0,h.stringToBase64URL)(d));let n=(0,f.createChunks)(c,l);n.forEach(({name:a})=>{j.delete(a)});let o={...e.DEFAULT_COOKIE_OPTIONS,...a?.cookieOptions,maxAge:0},p={...e.DEFAULT_COOKIE_OPTIONS,...a?.cookieOptions,maxAge:e.DEFAULT_COOKIE_OPTIONS.maxAge};delete o.name,delete p.name;let q=[...[...j].map(a=>({name:a,value:"",options:o})),...n.map(({name:a,value:b})=>({name:a,value:b,options:p}))];q.length>0&&await k(q)},removeItem:async b=>{let c=await i([b]),d=(c?.map(({name:a})=>a)||[]).filter(a=>(0,f.isChunkLike)(a,b)),g={...e.DEFAULT_COOKIE_OPTIONS,...a?.cookieOptions,maxAge:0};delete g.name,d.length>0&&await k(d.map(a=>({name:a,value:"",options:g})))}}}}async function j({getAll:a,setAll:c,setItems:d,removedItems:g},i){let j=i.cookieEncoding,k=i.cookieOptions??null,l=await a([...d?Object.keys(d):[],...g?Object.keys(g):[]]),m=l?.map(({name:a})=>a)||[],n=Object.keys(g).flatMap(a=>m.filter(b=>(0,f.isChunkLike)(b,a))),o=Object.keys(d).flatMap(a=>{let c=new Set(m.filter(b=>(0,f.isChunkLike)(b,a))),e=d[a];"base64url"===j&&(e=b+(0,h.stringToBase64URL)(e));let g=(0,f.createChunks)(a,e);return g.forEach(a=>{c.delete(a.name)}),n.push(...c),g}),p={...e.DEFAULT_COOKIE_OPTIONS,...k,maxAge:0},q={...e.DEFAULT_COOKIE_OPTIONS,...k,maxAge:e.DEFAULT_COOKIE_OPTIONS.maxAge};delete p.name,delete q.name,await c([...n.map(a=>({name:a,value:"",options:p})),...o.map(({name:a,value:b})=>({name:a,value:b,options:q}))])}}},915653:a=>{"use strict";var{g:b,__dirname:c}=a;{let b;a.s({createBrowserClient:()=>h});var d=a.i(525222),e=a.i(302258);a.i(378652);var f=a.i(612711),g=a.i(567707);function h(a,c,h){let i=h?.isSingleton===!0||(!h||!("isSingleton"in h))&&(0,f.isBrowser)();if(i&&b)return b;if(!a||!c)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:j}=(0,g.createStorageFromOptions)({...h,cookieEncoding:h?.cookieEncoding??"base64url"},!1),k=(0,d.createClient)(a,c,{...h,global:{...h?.global,headers:{...h?.global?.headers,"X-Client-Info":`supabase-ssr/${e.VERSION} createBrowserClient`}},auth:{...h?.auth,...h?.cookieOptions?.name?{storageKey:h.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:(0,f.isBrowser)(),detectSessionInUrl:(0,f.isBrowser)(),persistSession:!0,storage:j}});return i&&(b=k),k}}},354768:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createServerClient:()=>g});var d=a.i(525222),e=a.i(302258),f=a.i(567707);function g(a,b,c){if(!a||!b)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:g,getAll:h,setAll:i,setItems:j,removedItems:k}=(0,f.createStorageFromOptions)({...c,cookieEncoding:c?.cookieEncoding??"base64url"},!0),l=(0,d.createClient)(a,b,{...c,global:{...c?.global,headers:{...c?.global?.headers,"X-Client-Info":`supabase-ssr/${e.VERSION} createServerClient`}},auth:{...c?.cookieOptions?.name?{storageKey:c.cookieOptions.name}:null,...c?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:g}});return l.auth.onAuthStateChange(async a=>{(Object.keys(j).length>0||Object.keys(k).length>0)&&("SIGNED_IN"===a||"TOKEN_REFRESHED"===a||"USER_UPDATED"===a||"PASSWORD_RECOVERY"===a||"SIGNED_OUT"===a||"MFA_CHALLENGE_VERIFIED"===a)&&await (0,f.applyServerStorage)({getAll:h,setAll:i,setItems:j,removedItems:k},{cookieOptions:c?.cookieOptions??null,cookieEncoding:c?.cookieEncoding??"base64url"})}),l}},198911:function(a){var{g:b,__dirname:c,m:d,e:e}=a},307921:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(915653),a.i(354768),a.i(198911),a.i(378652)},785787:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(915653),a.i(354768),a.i(198911),a.i(378652),a.i(307921)},224533:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createClient:()=>e}),a.i(785787);var d=a.i(915653);function e(){return(0,d.createBrowserClient)("https://tzjelqzwdgidsjqhmvkr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6amVscXp3ZGdpZHNqcWhtdmtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTAsImV4cCI6MjA2MzkwNDY5MH0.MVkBlC_QI1IT7licw9CzLoS9yyo-QGXR7EFyXYZDyZc")}},425547:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AdminDashboardHeader:()=>p});var d=a.i(674420),e=a.i(816486),f=a.i(918656),g=a.i(722851),h=a.i(558981),i=a.i(790002),j=a.i(588145),k=a.i(30358),l=a.i(28912),m=a.i(535624),n=a.i(48084),o=a.i(224533);function p({admin:a}){let b=(0,f.useRouter)(),[c,p]=(0,g.useState)(0);(0,g.useEffect)(()=>{let a=async()=>{try{let a=await (0,n.getContactRequestsCount)();a.success&&p(a.data.pending||0)}catch(a){console.error("Error fetching contact requests:",a)}};a();let b=(0,o.createClient)(),c=b.channel("contact_requests_changes").on("postgres_changes",{event:"*",schema:"public",table:"contact_requests"},b=>{console.log("Contact request change detected:",b),a()}).subscribe();return()=>{b.removeChannel(c)}},[]);let q=async()=>{try{await (0,m.adminLogout)(),b.push("/admin/login")}catch(a){console.error("Logout error:",a),b.push("/admin/login")}};return(0,d.jsx)("header",{className:"bg-white shadow",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.Shield,{className:"w-6 h-6 text-red-600"}),(0,d.jsx)("h2",{className:"text-lg font-bold text-slate-900",children:"Admin Dashboard"})]}),a?.role==="super_admin"&&(0,d.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:"Super Admin"})]}),(0,d.jsxs)("p",{className:"text-sm text-slate-900 mt-1",children:["Welcome back, ",a?.name,(0,d.jsxs)("span",{className:"ml-2 text-slate-600",children:["• ",a?.email]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 flex-shrink-0 mx-6",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,d.jsx)(k.Stethoscope,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent",children:"Celer AI"}),(0,d.jsx)("p",{className:"text-sm text-teal-600/80",children:"Admin Portal"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 flex-1 justify-end",children:[c>0&&(0,d.jsxs)(e.default,{href:"/admin/dashboard?tab=billing",className:"flex items-center space-x-1 px-3 py-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-md transition-colors",children:[(0,d.jsx)(l.Bell,{className:"w-4 h-4 text-orange-600"}),(0,d.jsxs)("span",{className:"text-sm font-medium text-orange-800",children:[c," pending contact",1!==c?"s":""]})]}),(0,d.jsx)("nav",{className:"hidden md:flex space-x-4",children:(0,d.jsxs)(e.default,{href:"/admin/dashboard",className:"flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium",children:[(0,d.jsx)(j.BarChart3,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Dashboard"})]})}),(0,d.jsxs)("button",{onClick:q,className:"flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium",children:[(0,d.jsx)(h.LogOut,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Logout"})]})]})]})})})}},634373:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],c=(0,d.default)("square-pen",b)}},635001:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Edit:()=>d.default});var d=a.i(634373)},291499:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]],c=(0,d.default)("ban",b)}},489332:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Ban:()=>d.default});var d=a.i(291499)},820192:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({performAdminAction:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("400ef906d85e45dc697968c1d9fb5f164b7102286a",d.callServer,void 0,d.findSourceMapURL,"performAdminAction")},537202:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({resetDoctorQuota:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("4068a3d4eaa9d307836531ef9e56a76d4f4a38edea",d.callServer,void 0,d.findSourceMapURL,"resetDoctorQuota")},508932:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DoctorsTable:()=>p});var d=a.i(674420),e=a.i(722851),f=a.i(24353),g=a.i(767332),h=a.i(635001),i=a.i(455558),j=a.i(489332),k=a.i(72732),l=a.i(582126),m=a.i(820192),n=a.i(537202),o=a.i(148530);function p({doctors:a}){let[b,c]=(0,e.useState)(null),[p,q]=(0,e.useState)(null),r=async(a,b)=>{c(b),q(null);try{let c=await (0,m.performAdminAction)({action:a,doctor_id:b});c.success?(q({type:"success",text:`Doctor ${a}d successfully!`}),window.location.reload()):q({type:"error",text:c.error||`Failed to ${a} doctor`})}catch(a){q({type:"error",text:"An error occurred. Please try again."})}finally{c(null)}},s=async a=>{let b=prompt("Enter new monthly quota:");b&&!isNaN(Number(b))&&await r("approve",a)},t=async a=>{c(a),q(null);try{let b=await (0,n.resetDoctorQuota)(a);b.success?(q({type:"success",text:"Quota reset successfully!"}),window.location.reload()):q({type:"error",text:b.error||"Failed to reset quota"})}catch(a){q({type:"error",text:"An error occurred while resetting quota"})}finally{c(null)}},u=async a=>{confirm("Are you sure you want to reject this doctor? This action cannot be undone.")&&await r("reject",a)},v=async a=>{confirm("Are you sure you want to disable this doctor? They will not be able to log in until re-enabled.")&&await r("disable",a)},w=async a=>{confirm("Are you sure you want to reset this doctor's quota usage to zero?")&&await t(a)},x=a=>a.approved?(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,d.jsx)(k.CheckCircle,{className:"w-3 h-3 mr-1"}),"Active"]}):(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,d.jsx)(l.Clock,{className:"w-3 h-3 mr-1"}),"Pending"]}),y=a=>{let b=a.quota_percentage||0;return(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsxs)("div",{className:"font-medium text-gray-900",children:[a.quota_used," / ",a.monthly_quota]}),(0,d.jsxs)("div",{className:`text-xs ${b>=90?"text-red-600":b>=70?"text-orange-600":"text-green-600"}`,children:[b,"% used"]})]})};return(0,d.jsxs)("div",{className:"overflow-hidden",children:[p&&(0,d.jsx)("div",{className:`p-4 mb-4 rounded-md ${"success"===p.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"}`,children:p.text}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Doctor"}),(0,d.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quota"}),(0,d.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell",children:"Activity"}),(0,d.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.name}),(0,d.jsx)("div",{className:"text-xs sm:text-sm text-gray-500 break-all",children:a.email}),a.clinic_name?(0,d.jsx)("div",{className:"text-xs text-gray-400 hidden sm:block",children:a.clinic_name}):(0,d.jsx)("div",{className:"text-xs text-gray-400 hidden sm:block",children:"No clinic specified"})]})}),(0,d.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:x(a)}),(0,d.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:y(a)}),(0,d.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell",children:(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsxs)("div",{className:"text-xs",children:["Joined: ",(0,o.formatDate)(a.created_at)]}),(0,d.jsxs)("div",{className:"text-xs",children:["Last active: ",a.last_activity?(0,o.formatDate)(a.last_activity):"Never"]})]})}),(0,d.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsx)("div",{className:"flex flex-wrap gap-1 sm:gap-2",children:a.approved?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:()=>s(a.id),disabled:b===a.id,className:"text-blue-600 hover:text-blue-900 disabled:opacity-50",title:"Update Quota",children:(0,d.jsx)(h.Edit,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>w(a.id),disabled:b===a.id,className:"text-purple-600 hover:text-purple-900 disabled:opacity-50",title:"Reset Quota",children:(0,d.jsx)(i.RotateCcw,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>v(a.id),disabled:b===a.id,className:"text-red-600 hover:text-red-900 disabled:opacity-50",title:"Disable Doctor",children:(0,d.jsx)(j.Ban,{className:"w-4 h-4"})})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:()=>r("approve",a.id),disabled:b===a.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",title:"Approve Doctor",children:(0,d.jsx)(f.Check,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>u(a.id),disabled:b===a.id,className:"text-red-600 hover:text-red-900 disabled:opacity-50",title:"Reject Doctor",children:(0,d.jsx)(g.X,{className:"w-4 h-4"})})]})})})]},a.id))})]})}),0===a.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No doctors found"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"No doctors have signed up yet."})]})]})}},794026:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],c=(0,d.default)("credit-card",b)}},557684:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({CreditCard:()=>d.default});var d=a.i(794026)},414829:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],c=(0,d.default)("dollar-sign",b)}},384054:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DollarSign:()=>d.default});var d=a.i(414829)},212134:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({PlanSelectionModal:()=>h});var d=a.i(674420),e=a.i(722851),f=a.i(767332),g=a.i(557684);function h({isOpen:a,onClose:b,onSelectPlan:c,plans:h,doctorName:i,isLoading:j=!1}){let[k,l]=(0,e.useState)(null);if(!a)return null;let m=a=>`₹${a.toLocaleString()}`;return(0,d.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-md w-full p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g.CreditCard,{className:"w-5 h-5 text-teal-600"}),(0,d.jsxs)("h3",{className:"text-lg font-semibold text-slate-800",children:["Select Plan for Dr. ",i]})]}),(0,d.jsx)("button",{onClick:b,className:"text-slate-400 hover:text-slate-600",children:(0,d.jsx)(f.X,{className:"w-5 h-5"})})]}),(0,d.jsx)("div",{className:"space-y-3 mb-6",children:h.map(a=>(0,d.jsxs)("label",{className:`block p-4 rounded-lg cursor-pointer transition-colors ${k===a.id?"bg-teal-50 border-2 border-teal-500":"bg-slate-50 border-2 border-transparent hover:bg-slate-100"}`,children:[(0,d.jsx)("input",{type:"radio",name:"plan",value:a.id,checked:k===a.id,onChange:a=>l(a.target.value),className:"sr-only"}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-slate-800",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-slate-600",children:[a.quota_limit," consultations"]})]}),(0,d.jsx)("div",{className:"text-lg font-bold text-slate-800",children:m(a.monthly_price)})]})]},a.id))}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)("button",{type:"button",onClick:b,disabled:j,className:"flex-1 px-4 py-2 text-slate-700 bg-slate-200 rounded-lg hover:bg-slate-300 disabled:opacity-50",children:"Cancel"}),(0,d.jsx)("button",{type:"button",onClick:()=>{if(k){let a=h.find(a=>a.id===k);a&&c(k,a.monthly_price)}},disabled:!k||j,className:"flex-1 px-4 py-2 text-white bg-teal-600 rounded-lg hover:bg-teal-700 disabled:opacity-50",children:j?"Creating...":"Create Bill"})]})]})})}},943326:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getBillingStats:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("005135c7e18ac53e83714924ee812a087cbd5961ac",d.callServer,void 0,d.findSourceMapURL,"getBillingStats")},435332:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getDoctorsBillingInfo:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("00de095d837551bbb86629fce0ed19e4828a33db55",d.callServer,void 0,d.findSourceMapURL,"getDoctorsBillingInfo")},891464:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getAllBillingTransactions:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("601593b7e410bcaa66088bb5ce3de5cefb59afd238",d.callServer,void 0,d.findSourceMapURL,"getAllBillingTransactions")},226475:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({markPaymentPaid:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("70b9ce8de127fd1018b818463398d90f2be7ba0dd1",d.callServer,void 0,d.findSourceMapURL,"markPaymentPaid")},203545:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createBillingTransaction:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("7c5e8dd2e45b0074066c6bf877fc2898170b991828",d.callServer,void 0,d.findSourceMapURL,"createBillingTransaction")},524210:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getBillingPlans:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("00eac522dfe7cabe2a6e71d6d21856221a9f52a549",d.callServer,void 0,d.findSourceMapURL,"getBillingPlans")},355654:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getContactRequests:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("0009c0c46815af213900a610cc4a7e2d00a036b8b7",d.callServer,void 0,d.findSourceMapURL,"getContactRequests")},653149:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({updateContactRequestStatus:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("60bd2570fa48d9714b2922cc0537d755b4c2180d2e",d.callServer,void 0,d.findSourceMapURL,"updateContactRequestStatus")},630188:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({BillingManagement:()=>C});var d=a.i(674420),e=a.i(722851),f=a.i(557684),g=a.i(384054),h=a.i(485878),i=a.i(425181),j=a.i(72732),k=a.i(676261),l=a.i(582126),m=a.i(605754),n=a.i(647852),o=a.i(28912),p=a.i(584543),q=a.i(177286),r=a.i(212134),s=a.i(943326),t=a.i(435332),u=a.i(891464),v=a.i(226475),w=a.i(203545),x=a.i(524210),y=a.i(355654),z=a.i(653149),A=a.i(48084),B=a.i(224533);function C(){let[a,b]=(0,e.useState)("overview"),[c,C]=(0,e.useState)(null),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)([]),[H,I]=(0,e.useState)([]),[J,K]=(0,e.useState)([]),[L,M]=(0,e.useState)(null),[N,O]=(0,e.useState)(!0),[P,Q]=(0,e.useState)(""),[R,S]=(0,e.useState)("all"),[T,U]=(0,e.useState)(null),[V,W]=(0,e.useState)(!1),[X,Y]=(0,e.useState)(null),[Z,$]=(0,e.useState)(null),_=(0,e.useRef)(a);(0,e.useEffect)(()=>{_.current=a},[a]);let aa=(0,e.useCallback)(async()=>{try{let[a,b]=await Promise.all([(0,y.getContactRequests)(),(0,A.getContactRequestsCount)()]);a.success&&K(a.data),b.success&&M(b.data)}catch(a){console.error("Error loading contact requests data:",a)}},[]),ab=(0,e.useCallback)(async()=>{try{let[a,b]=await Promise.all([(0,s.getBillingStats)(),(0,u.getAllBillingTransactions)(100)]);a.success&&C(a.data),b.success&&G(b.data.transactions)}catch(a){console.error("Error loading billing data:",a)}},[]),ac=(0,e.useCallback)(async()=>{try{let a=await (0,t.getDoctorsBillingInfo)();a.success&&E(a.data)}catch(a){console.error("Error loading doctors data:",a)}},[]),ad=(0,e.useCallback)(async()=>{O(!0);try{let[b,c,d]=await Promise.all([(0,s.getBillingStats)(),(0,A.getContactRequestsCount)(),(0,x.getBillingPlans)()]);b.success&&C(b.data),c.success&&M(c.data),d.success&&I(d.data),"transactions"===a?await (0,u.getAllBillingTransactions)(50).then(a=>a.success&&G(a.data.transactions)):"doctors"===a?await (0,t.getDoctorsBillingInfo)().then(a=>a.success&&E(a.data)):"requests"===a&&await (0,y.getContactRequests)().then(a=>a.success&&K(a.data))}catch(a){console.error("Error loading billing data:",a)}finally{O(!1)}},[a]);(0,e.useEffect)(()=>{ad()},[ad]),(0,e.useEffect)(()=>{let a=(0,B.createClient)(),b=a.channel("contact_requests_billing_changes").on("postgres_changes",{event:"*",schema:"public",table:"contact_requests"},a=>{console.log("Contact request change detected:",a),"requests"===_.current||"INSERT"===a.eventType?aa():(0,A.getContactRequestsCount)().then(a=>a.success&&M(a.data))}).subscribe(),c=a.channel("billing_transactions_changes").on("postgres_changes",{event:"*",schema:"public",table:"billing_transactions"},a=>{console.log("Billing transaction change detected:",a),(0,s.getBillingStats)().then(a=>a.success&&C(a.data)),"transactions"===_.current&&ab()}).subscribe(),d=a.channel("doctors_billing_changes").on("postgres_changes",{event:"*",schema:"public",table:"doctors"},a=>{console.log("Doctor billing info change detected:",a),(0,s.getBillingStats)().then(a=>a.success&&C(a.data)),"doctors"===_.current&&ac()}).subscribe(),e=a.channel("referral_discounts_changes").on("postgres_changes",{event:"*",schema:"public",table:"referral_discounts"},a=>{console.log("Referral discount change detected:",a),("INSERT"===a.eventType||"UPDATE"===a.eventType&&a.new?.status==="applied")&&(0,s.getBillingStats)().then(a=>a.success&&C(a.data))}).subscribe();return()=>{a.removeChannel(b),a.removeChannel(c),a.removeChannel(d),a.removeChannel(e)}},[ab,aa,ac]);let ae=async a=>{if(confirm("Are you sure you want to mark this payment as paid?")){U(a),$(null);try{let b=await (0,v.markPaymentPaid)(a,"admin_manual","Manual payment confirmation");b.success?($({type:"success",text:"Payment marked as paid successfully!"}),await ad()):$({type:"error",text:b.error||"Failed to mark payment as paid"})}catch(a){$({type:"error",text:"An unexpected error occurred."})}finally{U(null)}}},af=async(a,b,c)=>{U(a),$(null);try{let d=await (0,w.createBillingTransaction)(a,b,c);d.success?(W(!1),Y(null),await ad()):$({type:"error",text:d.error||"Failed to create bill"})}catch(a){$({type:"error",text:"An unexpected error occurred."})}finally{U(null)}},ag=async(a,b)=>{U(a),$(null);try{let c=await (0,z.updateContactRequestStatus)(a,b);c.success?await ad():$({type:"error",text:c.error||"Failed to update status"})}catch(a){$({type:"error",text:"An unexpected error occurred."})}finally{U(null)}},ah=a=>`₹${a.toLocaleString()}`,ai=a=>{switch(a){case"paid":return(0,d.jsx)(j.CheckCircle,{className:"w-4 h-4 text-green-600"});case"pending":return(0,d.jsx)(l.Clock,{className:"w-4 h-4 text-yellow-600"});case"failed":return(0,d.jsx)(k.AlertCircle,{className:"w-4 h-4 text-red-600"});default:return(0,d.jsx)(l.Clock,{className:"w-4 h-4 text-gray-600"})}},aj=a=>{switch(a){case"active":return"bg-green-100 text-green-800";case"trial":return"bg-blue-100 text-blue-800";case"suspended":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},ak=D.filter(a=>a.name.toLowerCase().includes(P.toLowerCase())||a.email.toLowerCase().includes(P.toLowerCase())).filter(a=>"all"===R||a.billing_status===R),al=F.filter(a=>a.doctor.name.toLowerCase().includes(P.toLowerCase())||a.doctor.email.toLowerCase().includes(P.toLowerCase())).filter(a=>"all"===R||a.payment_status===R);if(N&&!c)return(0,d.jsx)("div",{className:"p-6 max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-2"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mt-8",children:[1,2,3,4].map(a=>(0,d.jsx)("div",{className:"bg-gray-200 h-32 rounded-lg"},a))}),(0,d.jsx)("div",{className:"bg-gray-200 h-64 rounded-lg mt-6"})]})});let am=[{id:"overview",label:"Overview",icon:i.TrendingUp},{id:"transactions",label:"Transactions",icon:f.CreditCard},{id:"doctors",label:"Doctors",icon:h.Users},{id:"plans",label:"Plans",icon:m.Gift},{id:"requests",label:`Requests ${L?.pending?`(${L.pending})`:""}`,icon:o.Bell}];return(0,d.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Billing Management"}),(0,d.jsx)("p",{className:"text-slate-800",children:"Manage payments, subscriptions, and referral discounts"})]}),Z&&(0,d.jsx)("div",{className:`mb-6 p-4 rounded-md ${"success"===Z.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:(0,d.jsx)("p",{className:"text-sm font-medium",children:Z.text})}),(0,d.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,d.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:am.map(c=>{let e=c.icon;return(0,d.jsxs)("button",{onClick:()=>{b(c.id),Q(""),S("all")},className:`flex-shrink-0 flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${a===c.id?"border-teal-500 text-teal-600":"border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300"}`,children:[(0,d.jsx)(e,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:c.label})]},c.id)})})}),"overview"===a&&c&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Total Revenue"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:ah(c.total_revenue)})]}),(0,d.jsx)(g.DollarSign,{className:"w-8 h-8 text-green-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Monthly Revenue"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:ah(c.monthly_revenue)})]}),(0,d.jsx)(i.TrendingUp,{className:"w-8 h-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Pending Payments"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:ah(c.pending_payments)})]}),(0,d.jsx)(l.Clock,{className:"w-8 h-8 text-yellow-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Active Subscriptions"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:c.active_subscriptions})]}),(0,d.jsx)(h.Users,{className:"w-8 h-8 text-purple-600"})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-4",children:"User Distribution"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-slate-800",children:"Trial Users"}),(0,d.jsx)("span",{className:"font-medium text-slate-900",children:c.trial_users})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-slate-800",children:"Active Subscribers"}),(0,d.jsx)("span",{className:"font-medium text-slate-900",children:c.active_subscriptions})]})]})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-4",children:"Referral Program"}),(0,d.jsx)("div",{className:"space-y-3",children:(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-slate-800",children:"Total Discounts Given"}),(0,d.jsx)("span",{className:"font-medium text-slate-900",children:ah(c.referral_discounts_given)})]})})]})]})]}),"transactions"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(n.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,d.jsx)("input",{type:"text",placeholder:"Search transactions...",value:P,onChange:a=>Q(a.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white"})]}),(0,d.jsxs)("select",{value:R,onChange:a=>S(a.target.value),className:"px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white",children:[(0,d.jsx)("option",{value:"all",children:"All Status"}),(0,d.jsx)("option",{value:"pending",children:"Pending"}),(0,d.jsx)("option",{value:"paid",children:"Paid"}),(0,d.jsx)("option",{value:"failed",children:"Failed"})]})]}),(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Amount"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Period"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:al.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-slate-900",children:a.doctor.name}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:a.doctor.email})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"text-sm text-slate-900",children:[ah(a.final_amount),a.discount_amount>0&&(0,d.jsxs)("div",{className:"text-xs text-green-600",children:[ah(a.discount_amount)," discount applied"]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[ai(a.payment_status),(0,d.jsx)("span",{className:"text-sm capitalize",children:a.payment_status})]})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:[new Date(a.billing_period_start).toLocaleDateString()," - ",new Date(a.billing_period_end).toLocaleDateString()]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:"pending"===a.payment_status&&(0,d.jsx)("button",{onClick:()=>ae(a.id),disabled:T===a.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",title:"Mark Payment as Paid",children:T===a.id?"Processing...":"Mark Paid"})})]},a.id))})]}),0===al.length&&!N&&(0,d.jsx)("div",{className:"text-center py-12 text-gray-500",children:"No transactions found."})]})]}),"doctors"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(n.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,d.jsx)("input",{type:"text",placeholder:"Search doctors...",value:P,onChange:a=>Q(a.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white"})]}),(0,d.jsxs)("select",{value:R,onChange:a=>S(a.target.value),className:"px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white",children:[(0,d.jsx)("option",{value:"all",children:"All Status"}),(0,d.jsx)("option",{value:"trial",children:"Trial"}),(0,d.jsx)("option",{value:"active",children:"Active"}),(0,d.jsx)("option",{value:"suspended",children:"Suspended"}),(0,d.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Total Paid"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Referrals"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Available Discount"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:ak.map(a=>(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-slate-900",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:a.email}),a.clinic_name&&(0,d.jsx)("div",{className:"text-xs text-slate-500",children:a.clinic_name})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs rounded-full font-medium ${aj(a.billing_status)}`,children:a.billing_status})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-900",children:[ah(a.total_paid),a.pending_payments>0&&(0,d.jsxs)("div",{className:"text-xs text-yellow-600",children:[ah(a.pending_payments)," pending"]})]}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:[a.referral_info.successful_referrals," successful",a.referral_info.referred_by&&(0,d.jsxs)("div",{className:"text-xs text-blue-600",children:["Referred by ",a.referral_info.referred_by]})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-900",children:ah(a.available_discount_amount)}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:(0,d.jsx)("button",{onClick:()=>{Y(a),W(!0)},className:"text-blue-600 hover:text-blue-900",children:"Create Bill"})})]},a.id))})]}),0===ak.length&&!N&&(0,d.jsx)("div",{className:"text-center py-12 text-gray-500",children:"No doctors found."})]})]}),"plans"===a&&(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:H.map(a=>(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-2",children:a.name}),a.description&&(0,d.jsx)("p",{className:"text-slate-800 mb-4",children:a.description}),(0,d.jsxs)("div",{className:"text-2xl font-bold text-slate-900 mb-4",children:[ah(a.monthly_price),"/month"]}),(0,d.jsxs)("div",{className:"text-sm text-slate-800 mb-4",children:[a.quota_limit," consultations"]}),a.features&&"object"==typeof a.features&&"features"in a.features&&Array.isArray(a.features.features)&&(0,d.jsx)("ul",{className:"text-sm text-slate-800 space-y-1",children:a.features.features.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.CheckCircle,{className:"w-3 h-3 text-green-500"}),(0,d.jsx)("span",{children:a})]},b))})]},a.id))}),"requests"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Total Requests"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:L?.total||0})]}),(0,d.jsx)(q.MessageCircle,{className:"w-8 h-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Pending"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:L?.pending||0})]}),(0,d.jsx)(l.Clock,{className:"w-8 h-8 text-orange-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Contacted"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:L?.contacted||0})]}),(0,d.jsx)(p.Phone,{className:"w-8 h-8 text-blue-600"})]})}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Resolved"}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600",children:L?.resolved||0})]}),(0,d.jsx)(j.CheckCircle,{className:"w-8 h-8 text-green-600"})]})})]}),(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,d.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-900",children:"Contact Requests"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-slate-800",children:"Doctors requesting quota upgrades or plan changes"})]}),(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Request Type"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Contact Info"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Date"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:J.map(a=>(0,d.jsxs)("tr",{className:"pending"===a.status?"bg-orange-50":"",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-slate-900",children:a.doctor_name}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:a.doctor_email}),a.clinic_name&&(0,d.jsx)("div",{className:"text-xs text-slate-500",children:a.clinic_name})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm text-slate-900",children:a.request_type})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm text-slate-900",children:"+91 8921628177"}),(0,d.jsx)("a",{href:`mailto:${a.doctor_email}`,className:"text-xs text-blue-600 hover:text-blue-900",children:"Send Email"})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`inline-flex px-2 py-1 text-xs rounded-full font-medium ${"pending"===a.status?"bg-orange-100 text-orange-800":"contacted"===a.status?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:a.status})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:new Date(a.created_at).toLocaleDateString()}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:["pending"===a.status&&(0,d.jsxs)(d.Fragment,{children:[" ",(0,d.jsx)("button",{onClick:()=>ag(a.id,"contacted"),disabled:T===a.id,className:"text-blue-600 hover:text-blue-900 disabled:opacity-50",children:"Mark Contacted"}),(0,d.jsx)("button",{onClick:()=>ag(a.id,"resolved"),disabled:T===a.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",children:"Mark Resolved"})," "]}),"contacted"===a.status&&(0,d.jsx)("button",{onClick:()=>ag(a.id,"resolved"),disabled:T===a.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",children:"Mark Resolved"})]})]},a.id))})]}),0===J.length&&!N&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(q.MessageCircle,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-900",children:"No contact requests"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"No doctors have requested contact yet."})]})]})]}),(0,d.jsx)(r.PlanSelectionModal,{isOpen:V,onClose:()=>{W(!1),Y(null)},onSelectPlan:(a,b)=>{X&&af(X.id,a,b)},plans:H,doctorName:X?.name||"",isLoading:!!T})]})}}};

//# sourceMappingURL=_43c7c4fe._.js.map