{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts", "turbopack:///[project]/src/lib/actions/data:e8d848 <text/javascript>", "turbopack:///[project]/src/components/auth/signup-form.tsx"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n", "/* __next_internal_action_entry_do_not_use__ [{\"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d\":\"signup\"},\"src/lib/actions/auth.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var signup=/*#__PURE__*/createServerReference(\"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d\",callServer,void 0,findSourceMapURL,\"signup\");", "'use client'\n\nimport { useActionState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { signup } from '@/lib/actions/auth'\nimport { FormState } from '@/lib/types'\nimport { trackAuth } from '@/lib/analytics'\n\n// CORRECTED initialState to conform to FormState interface\nconst initialState: FormState = {\n  success: false,\n  message: '',\n  // fieldErrors is optional, so it can be omitted or set to an empty object if needed\n  // fieldErrors: {},\n}\n\ninterface SignupFormProps {\n  referralCode?: string\n}\n\nexport function SignupForm({ referralCode }: SignupFormProps) {\n  const [state, formAction, isPending] = useActionState(signup, initialState)\n  const router = useRouter()\n\n  // Auto-redirect to login page after 3 seconds for successful account creation\n  useEffect(() => {\n    if (state?.success && state?.message?.includes('Account created successfully')) {\n      // Track successful signup completion\n      trackAuth('signup_completed')\n\n      const timer = setTimeout(() => {\n        router.push('/login')\n      }, 3000)\n\n      return () => clearTimeout(timer)\n    }\n  }, [state?.success, state?.message, router])\n\n  // Handle form submission to track signup started\n  const handleSubmit = (formData: FormData) => {\n    // Track signup attempt\n    trackAuth('signup_started')\n\n    // Call the original form action\n    formAction(formData)\n  }\n\n  return (\n    <form action={handleSubmit} className=\"mt-8 space-y-6\">\n      {/* Hidden field for referral code */}\n      {referralCode && (\n        <input\n          type=\"hidden\"\n          name=\"referral_code\"\n          value={referralCode}\n        />\n      )}\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Full Name\n          </label>\n          <input\n            id=\"name\"\n            name=\"name\"\n            type=\"text\"\n            autoComplete=\"name\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Dr. John Doe\"\n          />\n          {state?.fieldErrors?.name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"<EMAIL>\"\n          />\n          {state?.fieldErrors?.email && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.email[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Password\n          </label>\n          <input\n            id=\"password\"\n            name=\"password\"\n            type=\"password\"\n            autoComplete=\"new-password\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Create a strong password\"\n          />\n          {state?.fieldErrors?.password && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.password[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"clinic_name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Hospital Name\n          </label>\n          <input\n            id=\"clinic_name\"\n            name=\"clinic_name\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"ABC Medical Center\"\n          />\n          {state?.fieldErrors?.clinic_name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.clinic_name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Phone Number\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-4\">\n              <span className=\"text-slate-500 text-sm font-medium\">+91</span>\n            </div>\n            <input\n              id=\"phone\"\n              name=\"phone\"\n              type=\"tel\"\n              autoComplete=\"tel\"\n              className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n              placeholder=\"9876543210\"\n            />\n          </div>\n          {state?.fieldErrors?.phone && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.phone[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"referral_code\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Referral ID (Optional)\n          </label>\n          <input\n            id=\"referral_id\"\n            name=\"referral_code\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Enter referral ID if you have one (optional)\"\n            defaultValue={referralCode || ''}\n          />\n          {state?.fieldErrors?.referral_code && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.referral_id[0]}</span>\n            </p>\n          )}\n        </div>\n\n        {/* Terms of Service and Privacy Policy Checkbox */}\n        <div className=\"flex items-start space-x-3\">\n          <input\n            id=\"terms\"\n            name=\"terms\"\n            type=\"checkbox\"\n            required\n            className=\"mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2\"\n          />\n          <label htmlFor=\"terms\" className=\"text-sm text-slate-600 leading-relaxed\">\n            I accept the{' '}\n            <a href=\"/terms\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Terms of Service\n            </a>\n            {' '}and{' '}\n            <a href=\"/privacy\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Privacy Policy\n            </a>\n          </label>\n        </div>\n      </div>\n\n      {state?.message && (\n        <div className={`rounded-xl p-4 border ${\n          state.success\n            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'\n            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'\n        }`}>\n          <p className={`text-sm font-medium flex items-center space-x-2 ${\n            state.success ? 'text-emerald-800' : 'text-red-800'\n          }`}>\n            <span className={`w-2 h-2 rounded-full ${\n              state.success ? 'bg-emerald-400' : 'bg-red-400'\n            }`}></span>\n            <span>{state.message}</span>\n          </p>\n        </div>\n      )}\n\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isPending}\n          className=\"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2\"\n        >\n          {isPending ? (\n            <>\n              <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n              <span>Creating your magical account...</span>\n            </>\n          ) : (\n            <>\n              <span>Start Creating Magic</span>\n              <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n              </svg>\n            </>\n          )}\n        </button>\n      </div>\n    </form>\n  )\n}"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "wIAGSA,UAAU,CAAA,kBAAVA,EAAAA,UAAU,EASNC,qBAAqB,CAAA,kBAArBA,GARJC,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,8EADE,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,IAQpBD,EACV,AAEW,CAFX,CAAC,AAEU,CAFTE,AAES,CAAA,OAFDC,AAKXH,GALc,CAACI,KAIXC,OAJuB,CAIf,EAFRA,EAGiB,MAHT,WAEiC,iCCjBqF,EAAA,CAAA,CAAA,gBAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAoB,CAAA,EAAA,EAAA,EAAb,WAAW,QAAsB,AAApB,EAAsB,cAAtB,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAC,AAAnC,oBAAkB,iFCEhY,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAGA,IAAM,EAA0B,CAC9B,SAAS,EACT,QAAS,EAGX,EAMO,SAAS,EAAW,cAAE,CAAY,CAAmB,EAC1D,GAAM,CAAC,EAAO,EAAY,EAAU,CAAG,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,EAAA,MAAM,CAAE,GACxD,EAAS,CAAA,EAAA,EAAA,EADwB,OACxB,AAAQ,UAGvB,CAJsD,EAItD,EAAA,SAAA,AAAQ,EAAE,EAHK,GAIb,GAAI,GAAO,SAAW,GAAO,GAD/B,MACwC,SAAS,gCAAiC,CAE9E,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,oBAEV,IAAM,EAAQ,AAFd,WAEyB,KACvB,EAAO,IAAI,CAAC,SACd,EAAG,KAEH,MAAO,IAAM,aAAa,EAC5B,CACF,EAAG,CAAC,GAAO,QAAS,GAAO,QAAS,EAAO,EAYzC,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,OATc,AAAD,CASL,GAPd,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,kBAGV,EAAW,EACb,EAG8B,EAP5B,QAOsC,2BAEnC,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,SACL,KAAK,gBACL,MAAO,IAIX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,OAAO,UAAU,yDAAgD,cAGhF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,OACH,KAAK,OACL,KAAK,OACL,aAAa,OACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,iBAEb,GAAO,aAAa,MACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,IAAI,CAAC,EAAE,SAKtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,yDAAgD,kBAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,QACL,aAAa,QACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,uBAEb,GAAO,aAAa,OACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,KAAK,CAAC,EAAE,SAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,WAAW,UAAU,yDAAgD,aAGpF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,WACL,aAAa,eACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,6BAEb,GAAO,aAAa,UACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,QAAQ,CAAC,EAAE,SAK1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,cAAc,UAAU,yDAAgD,kBAGvF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,cACH,KAAK,cACL,KAAK,OACL,UAAU,mOACV,YAAY,uBAEb,GAAO,aAAa,aACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,WAAW,CAAC,EAAE,SAK7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,yDAAgD,iBAGjF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8CAAqC,UAEvD,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,MACL,aAAa,MACb,UAAU,yOACV,YAAY,kBAGf,GAAO,aAAa,OACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,KAAK,CAAC,EAAE,SAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,gBAAgB,UAAU,yDAAgD,2BAGzF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,cACH,KAAK,gBACL,KAAK,OACL,UAAU,mOACV,YAAY,+CACZ,aAAc,GAAgB,KAE/B,GAAO,aAAa,eACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,WAAW,CAAC,EAAE,SAM7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,WACL,QAAQ,CAAA,CAAA,EACR,UAAU,6FAEZ,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,mDAAyC,eAC3D,IACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,SAAS,OAAO,SAAS,UAAU,+EAAsE,qBAGhH,IAAI,MAAI,IACT,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,WAAW,OAAO,SAAS,UAAU,+EAAsE,4BAOxH,GAAO,SACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,sBAAsB,EACrC,EAAM,OAAO,CACT,iEACA,yDAAA,CACJ,UACA,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAW,CAAC,gDAAgD,EAC7D,EAAM,OAAO,CAAG,mBAAqB,eAAA,CACrC,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,qBAAqB,EACrC,EAAM,OAAO,CAAG,iBAAmB,aAAA,CACnC,GACF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,OAAO,QAK1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,2WAET,EACC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8EACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,wCAGR,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,yBACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDAAyD,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAChH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wCAQrF", "ignoreList": [0]}