module.exports={177813:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AudioRecorder:()=>j});var d=a.i(674420),e=a.i(722851),f=a.i(629551),g=a.i(954780),h=a.i(148530),i=a.i(711656);function j({audioState:a,onStateChange:b}){let c=(0,e.useRef)(null),j=(0,e.useRef)(null),k=(0,e.useRef)(null),l=(0,e.useRef)(0);(0,e.useEffect)(()=>((0,h.supportsAudioRecording)()||b({error:"Audio recording is not supported on this device"}),()=>{k.current&&clearInterval(k.current),j.current&&j.current.getTracks().forEach(a=>a.stop())}),[b]);let m=()=>(/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?["audio/mp4","audio/mpeg","audio/webm","audio/webm;codecs=opus"]:["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/mpeg"]).find(a=>MediaRecorder.isTypeSupported(a))||"audio/webm",n=async()=>{try{b({error:void 0});let a=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0}});j.current=a;let d=m(),e=new MediaRecorder(a,{mimeType:d});c.current=e;let f=[];e.ondataavailable=a=>{a.data.size>0&&f.push(a.data)},e.onstop=async()=>{let c=new Blob(f,{type:d}),e=d.includes("webm")?"webm":d.includes("mp4")?"mp4":d.includes("mpeg")?"mp3":"webm",g=new File([c],`recording_${Date.now()}.${e}`,{type:d}),h=(0,i.validateFile)(g,"audio");if(!h.valid)return void b({error:h.error,isRecording:!1});b({audioBlob:c,audioFile:g,isRecording:!1}),a.getTracks().forEach(a=>a.stop())},e.start(),l.current=Date.now(),b({isRecording:!0,duration:0}),k.current=setInterval(()=>{let a=Math.floor((Date.now()-l.current)/1e3);b({duration:a})},1e3)}catch(a){console.error("Error starting recording:",a),b({error:"Failed to start recording. Please check microphone permissions.",isRecording:!1})}};return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex flex-col items-center space-y-4",children:a.audioBlob?(0,d.jsx)("div",{className:"flex flex-col items-center space-y-4 w-full",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4 p-4 bg-green-50 rounded-lg border border-green-200 w-full",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(f.Mic,{className:"w-4 h-4 text-green-600"})}),(0,d.jsxs)("div",{className:"flex-1 text-center",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-700",children:"Recording completed"}),(0,d.jsx)("p",{className:"text-lg font-mono text-green-600",children:(0,h.formatDuration)(a.duration)})]}),(0,d.jsx)("button",{onClick:()=>{b({audioBlob:void 0,duration:0,error:void 0})},className:"px-4 py-2 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-md",children:"Clear"})]})}):(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,d.jsx)("button",{onClick:a.isRecording?()=>{c.current&&a.isRecording&&(c.current.stop(),k.current&&(clearInterval(k.current),k.current=null))}:n,disabled:!!a.error,className:`w-20 h-20 rounded-full flex items-center justify-center text-white transition-all duration-200 ${a.isRecording?"bg-red-500 hover:bg-red-600 animate-pulse":"bg-blue-500 hover:bg-blue-600"} disabled:opacity-50 disabled:cursor-not-allowed`,children:a.isRecording?(0,d.jsx)(g.Square,{className:"w-8 h-8"}):(0,d.jsx)(f.Mic,{className:"w-8 h-8"})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-700",children:a.isRecording?"Recording...":"Tap to start recording"}),a.isRecording&&(0,d.jsx)("p",{className:"text-lg font-mono text-red-600",children:(0,h.formatDuration)(a.duration)})]})]})}),a.error&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,d.jsx)("p",{className:"text-sm text-red-700",children:a.error})}),!a.audioBlob&&!a.isRecording&&(0,d.jsxs)("div",{className:"bg-gray-50 rounded-md p-3",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Recording Tips:"}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 space-y-1",children:[(0,d.jsx)("li",{children:"• Speak clearly and at normal volume"}),(0,d.jsx)("li",{children:"• Minimize background noise"}),(0,d.jsx)("li",{children:"• Hold device 6-12 inches from your mouth"}),(0,d.jsx)("li",{children:"• Include patient symptoms, diagnosis, and treatment"})]})]})]})}},56076:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ImageCapture:()=>j});var d=a.i(674420),e=a.i(722851),f=a.i(388457),g=a.i(525128),h=a.i(767332),i=a.i(148530);function j({imageState:a,onStateChange:b,isMobile:c}){let j=(0,e.useRef)(null),k=(0,e.useRef)(null),l=async c=>{try{b({error:void 0});let d=[];for(let a=0;a<c.length;a++){let e=c[a];if(!e.type.startsWith("image/"))return void b({error:`File ${e.name} is not a valid image`});if(e.size>0xa00000)return void b({error:`File ${e.name} is too large. Please select files under 10MB.`});d.push({id:`${Date.now()}-${a}`,file:e,name:e.name,type:e.type,size:e.size})}b({images:[...a.images,...d]})}catch(a){console.error("Error processing images:",a),b({error:"Failed to process images. Please try again."})}},m=c=>{b({images:a.images.filter(a=>a.id!==c),error:void 0})};return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,i.supportsCamera)()&&(0,d.jsxs)("button",{onClick:()=>{k.current?.click()},className:"flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors",children:[(0,d.jsx)(f.Camera,{className:"w-6 h-6 text-gray-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Take Photo"})]}),(0,d.jsxs)("button",{onClick:()=>{j.current?.click()},className:"flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors",children:[(0,d.jsx)(g.Upload,{className:"w-6 h-6 text-gray-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Upload from Gallery"})]})]}),(0,d.jsx)("input",{ref:k,type:"file",accept:"image/*",capture:"environment",onChange:a=>{let b=a.target.files;b&&b.length>0&&l(b)},className:"hidden"}),(0,d.jsx)("input",{ref:j,type:"file",accept:"image/*",multiple:!0,onChange:a=>{let b=a.target.files;b&&b.length>0&&l(b)},className:"hidden"}),a.images.length>0&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:["Selected Images (",a.images.length,")"]}),(0,d.jsx)("button",{onClick:()=>{b({images:[],error:void 0}),j.current&&(j.current.value=""),k.current&&(k.current.value="")},className:"text-xs text-red-600 hover:text-red-800 underline",children:"Clear All"})]}),(0,d.jsx)("div",{className:"space-y-2",children:a.images.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg border",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,d.jsx)(f.Camera,{className:"w-4 h-4 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate max-w-[200px]",children:a.name}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[(a.size/1024/1024).toFixed(1)," MB"]})]})]}),(0,d.jsx)("button",{onClick:()=>m(a.id),className:"w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center",children:(0,d.jsx)(h.X,{className:"w-3 h-3"})})]},a.id))})]}),a.error&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,d.jsx)("p",{className:"text-sm text-red-700",children:a.error})})]})}},295224:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],c=(0,d.default)("send",b)}},613687:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Send:()=>d.default});var d=a.i(295224)},195177:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({SubmissionForm:()=>k});var d=a.i(674420),e=a.i(722851),f=a.i(613687),g=a.i(72732),h=a.i(676261),i=a.i(826542),j=a.i(148530);function k({audioFile:a,imageFiles:b,canSubmit:c,isSubmitting:k,onSubmissionStateChange:l}){let[m,n]=(0,e.useState)("idle"),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)(""),s=async()=>{if(a&&!k){l(!0),n("idle"),p(""),r("");try{let c=b?.map(a=>a.file)||[],d=await (0,j.retryWithBackoff)(()=>(0,i.createConsultationWithFiles)(a,c,[],"doctor",void 0,"outpatient"),{maxAttempts:3,baseDelay:1e3,maxDelay:5e3});d.success?(n("success"),r(`Consultation submitted successfully! Patient #${d.data?.patient_number||"N/A"} is ready for processing.`),setTimeout(()=>{n("idle"),r("")},5e3)):(n("error"),p(d.error||"Failed to submit consultation"))}catch(a){console.error("Submission error:",a),n("error"),p("Network error. Please check your connection and try again.")}finally{l(!1)}}};return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-md p-3",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Ready to Submit:"}),(0,d.jsxs)("div",{className:"space-y-1 text-xs text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Audio Recording:"}),(0,d.jsx)("span",{className:a?"text-green-600":"text-red-600",children:a?"✓ Ready":"✗ Required"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:"Handwritten Notes:"}),(0,d.jsx)("span",{className:b&&b.length>0?"text-green-600":"text-gray-500",children:b&&b.length>0?`✓ ${b.length} image(s)`:"○ Optional"})]})]})]}),(0,d.jsx)("button",{onClick:s,disabled:!c||k,className:(()=>{let a="w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors";return c?k?`${a} bg-blue-400 text-white cursor-not-allowed`:"success"===m?`${a} bg-green-500 text-white`:`${a} bg-blue-500 hover:bg-blue-600 text-white`:`${a} bg-gray-300 text-gray-500 cursor-not-allowed`})(),children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,d.jsx)("span",{children:"Submitting..."})]}):"success"===m?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.CheckCircle,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Submitted!"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.Send,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"Submit Consultation"})]})}),"success"===m&&q&&(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)(g.CheckCircle,{className:"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,d.jsx)("p",{className:"text-sm text-green-700",children:q})]})}),"error"===m&&o&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)(h.AlertCircle,{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm text-red-700",children:o}),(0,d.jsx)("button",{onClick:s,disabled:!c||k,className:"mt-2 text-xs text-red-600 hover:text-red-800 underline",children:"Try again"})]})]})}),"idle"===m&&(0,d.jsx)("div",{className:"bg-blue-50 rounded-md p-3",children:(0,d.jsxs)("p",{className:"text-xs text-blue-700",children:[(0,d.jsx)("strong",{children:"Next steps:"})," After submission, your receptionist can review and generate the patient summary from the dashboard. The consultation will be assigned a patient number for easy tracking."]})})]})}},86242:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({MobileRecordingInterface:()=>i});var d=a.i(674420),e=a.i(722851),f=a.i(177813),g=a.i(56076),h=a.i(195177);function i(){let[a,b]=(0,e.useState)({isRecording:!1,duration:0}),[c,i]=(0,e.useState)({images:[],error:null}),[j,k]=(0,e.useState)(!1),l=!!a.audioFile&&!j;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsx)("h2",{className:"text-lg font-medium text-blue-900 mb-2",children:"Record Patient Consultation"}),(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:["1. Tap the microphone to start recording your consultation",(0,d.jsx)("br",{}),"2. Optionally, take a photo of any handwritten notes",(0,d.jsx)("br",{}),"3. Submit to generate patient summary"]})]}),(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-800 mb-4",children:"Audio Recording"}),(0,d.jsx)(f.AudioRecorder,{audioState:a,onStateChange:a=>{b(b=>({...b,...a}))}})]}),(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-800 mb-4",children:"Handwritten Notes (Optional)"}),(0,d.jsx)(g.ImageCapture,{imageState:c,onStateChange:a=>{i(b=>({...b,...a}))}})]}),(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg shadow-lg border border-orange-200/50 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-800 mb-4",children:"Submit Consultation"}),(0,d.jsx)(h.SubmissionForm,{audioFile:a.audioFile,imageFiles:c.images,canSubmit:l,isSubmitting:j,onSubmissionStateChange:k})]}),(a.error||c.error)&&(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-red-800 mb-2",children:"Errors:"}),a.error&&(0,d.jsxs)("p",{className:"text-sm text-red-700",children:["Audio: ",a.error]}),c.error&&(0,d.jsxs)("p",{className:"text-sm text-red-700",children:["Image: ",c.error]})]})]})}}};

//# sourceMappingURL=_378d5de8._.js.map