module.exports={505291:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],c=(0,d.default)("house",b)}},426802:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Home:()=>d.default});var d=a.i(505291)},665225:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DashboardHeader:()=>o});var d=a.i(674420),e=a.i(816486),f=a.i(558981),g=a.i(265033),h=a.i(584543),i=a.i(30358),j=a.i(767332),k=a.i(426802),l=a.i(141126),m=a.i(293957),n=a.i(722851);function o({user:a}){let[b,c]=(0,n.useState)(!1),[o,p]=(0,n.useState)(null),[q,r]=(0,n.useState)("general"),[s,t]=(0,n.useState)(""),[u,v]=(0,n.useState)(!1),w=async()=>{c(!0),p(null),r("general"),t("")},x=async()=>{if(!s.trim())return void p("Please enter a message");v(!0),p(null);try{if(a?.id){let b=await (0,m.createContactRequest)(a.id,`Subject: ${q}

Message: ${s}`,q);b.success?(console.log("Contact request created successfully"),setTimeout(()=>c(!1),2e3)):(p(b.error||"Failed to create contact request"),console.error("Contact request failed:",b.error))}else p("User not found")}catch(a){p(a instanceof Error?a.message:"Unknown error"),console.error("Error creating contact request:",a)}finally{v(!1)}};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"bg-white/80 backdrop-blur-sm shadow-sm border-b border-orange-200/50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between py-4 sm:py-6",children:[(0,d.jsxs)("div",{className:"hidden sm:block flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"text-lg sm:text-xl font-bold text-slate-800",children:(0,d.jsxs)("h2",{className:"truncate",children:["Dr. ",a?.name||"Doctor"]})}),(0,d.jsx)("p",{className:"text-sm text-slate-600 truncate",children:a?.clinic_name||"Medical Practice"})]}),(0,d.jsxs)(e.default,{href:"/dashboard",className:"flex items-center space-x-4 flex-shrink-0 mx-4 hover:opacity-80 transition-opacity",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,d.jsx)(i.Stethoscope,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"})]}),(0,d.jsxs)("div",{className:"hidden sm:block text-center",children:[(0,d.jsx)("h1",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent",children:"Celer AI"}),(0,d.jsx)("p",{className:"text-sm text-teal-600/80",children:"AI-Powered Healthcare"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 sm:gap-4 flex-1 justify-end",children:[(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-1",children:(0,d.jsxs)(e.default,{href:"/dashboard",className:"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-orange-50 hover:text-teal-700 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,d.jsx)(k.Home,{className:"w-4 h-4 mr-2"}),"Dashboard"]})}),(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-3",children:[(0,d.jsxs)("button",{type:"button",onClick:w,className:"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,d.jsx)(h.Phone,{className:"w-4 h-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Contact"})]}),(0,d.jsxs)(e.default,{href:"/settings",prefetch:!0,className:"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,d.jsx)(g.Settings,{className:"w-4 h-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Settings"})]}),(0,d.jsx)("form",{action:l.logout,children:(0,d.jsxs)("button",{type:"submit",className:"inline-flex items-center px-2 sm:px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-md hover:shadow-lg transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,d.jsx)(f.LogOut,{className:"w-4 h-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})})]})]})]})})}),b&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,d.jsx)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 ease-out scale-100",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-teal-500 to-emerald-600 rounded-t-2xl p-6 text-white",children:[(0,d.jsx)("button",{onClick:()=>c(!1),className:"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors",children:(0,d.jsx)(j.X,{className:"w-6 h-6"})}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center",children:(0,d.jsx)(h.Phone,{className:"w-6 h-6"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold",children:"Contact Founder"}),(0,d.jsx)("p",{className:"text-emerald-100",children:"Get in touch with us"})]})]})]}),(0,d.jsx)("div",{className:"p-6 space-y-4",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Subject"}),(0,d.jsxs)("select",{value:q,onChange:a=>r(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-black bg-white",children:[(0,d.jsx)("option",{value:"general",children:"General Feedback"}),(0,d.jsx)("option",{value:"technical",children:"Technical Issue"}),(0,d.jsx)("option",{value:"billing",children:"Billing Question"}),(0,d.jsx)("option",{value:"feature",children:"Feature Request"}),(0,d.jsx)("option",{value:"bug",children:"Bug Report"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Message"}),(0,d.jsx)("textarea",{value:s,onChange:a=>t(a.target.value),placeholder:"Please describe your issue or feedback...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none text-black bg-white placeholder-gray-500"})]}),o?(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"text-sm text-red-700 font-medium",children:["❌ ",o]})}):u?(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,d.jsx)("div",{className:"text-sm text-blue-700 font-medium",children:"📤 Submitting your request..."})}):null,(0,d.jsx)("button",{onClick:x,disabled:u||!s.trim(),className:"w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:u?"Submitting...":"Submit Request"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,d.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,d.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,d.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"or contact directly"})})]}),(0,d.jsxs)("div",{className:"text-center space-y-3",children:[(0,d.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-slate-800 mb-2",children:"📞 +91 8921628177"}),(0,d.jsx)("div",{className:"text-sm text-slate-600",children:"Available: 9 AM - 9 PM IST"})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)("a",{href:"tel:+918921628177",className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"📞 Call Now"}),(0,d.jsx)("a",{href:"https://wa.me/918921628177",target:"_blank",rel:"noopener noreferrer",className:"flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"💬 WhatsApp"})]})]})]})})]})})})]})}},699999:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({TemplatesSidebar:()=>i});var d=a.i(674420),e=a.i(701090),f=a.i(314928),g=a.i(582126),h=a.i(148530);function i({templates:a,selectedTemplate:b,onSelectTemplate:c,onNewTemplate:i}){return(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col",children:[(0,d.jsxs)("div",{className:"p-4 border-b border-orange-200/50",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-slate-800",children:"Templates"}),(0,d.jsxs)("button",{onClick:i,className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,d.jsx)(e.Plus,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"New"})]})]}),(0,d.jsxs)("div",{className:"text-xs text-slate-600",children:[a.length," template",1!==a.length?"s":""]})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:0===a.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(f.FileText,{className:"mx-auto h-12 w-12 text-slate-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-800",children:"No templates"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"Get started by creating your first template."})]}):a.map(a=>(0,d.jsx)("div",{onClick:()=>c(a),className:`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${b?.id===a.id?"bg-gradient-to-r from-teal-50 to-emerald-50 border-teal-200 shadow-md":"bg-white/70 border-orange-200/50 hover:bg-orange-50/50"}`,children:(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-slate-800 truncate",children:a.name}),(0,d.jsxs)("div",{className:"mt-1 flex items-center space-x-2 text-xs text-slate-600",children:[(0,d.jsx)(g.Clock,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:(0,h.formatRelativeTime)(a.updated_at)})]}),(0,d.jsxs)("p",{className:"mt-2 text-xs text-slate-500 line-clamp-2",children:[a.content.substring(0,80),"..."]})]})})},a.id))}),(0,d.jsx)("div",{className:"p-4 border-t border-orange-200/50",children:(0,d.jsx)("div",{className:"text-xs text-slate-500 text-center",children:"Templates help standardize your consultation notes"})})]})}},67137:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({TemplateEditor:()=>k});var d=a.i(674420),e=a.i(722851),f=a.i(12372),g=a.i(86111),h=a.i(447888),i=a.i(314928),j=a.i(148530);function k({template:a,onSave:b}){let[c,k]=(0,e.useState)(""),[l,m]=(0,e.useState)(""),[n,o]=(0,e.useState)(!1);(0,e.useEffect)(()=>{a&&(k(a.name),m(a.content),o(!1))},[a]);let p=async()=>{if(l)try{await navigator.clipboard.writeText(l)}catch(a){console.error("Failed to copy template content:",a)}};return a?(0,d.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col",children:[(0,d.jsx)("div",{className:"p-4 border-b border-orange-200/50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[n?(0,d.jsx)("input",{type:"text",value:c,onChange:a=>k(a.target.value),className:"text-lg font-semibold text-slate-800 bg-transparent border-b border-teal-300 focus:outline-none focus:border-teal-500 w-full",placeholder:"Template name..."}):(0,d.jsx)("h2",{className:"text-lg font-semibold text-slate-800 truncate",children:a.name}),(0,d.jsxs)("div",{className:"mt-1 text-xs text-slate-600",children:["Created: ",(0,j.formatDate)(a.created_at)," • Last updated: ",(0,j.formatDate)(a.updated_at)]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,d.jsxs)("button",{onClick:p,className:"flex items-center space-x-2 px-3 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-lg text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-200",children:[(0,d.jsx)(g.Copy,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Copy"})]}),n?(0,d.jsxs)("button",{onClick:()=>{a&&(b({...a,name:c,content:l,updated_at:new Date().toISOString()}),o(!1))},className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,d.jsx)(f.Save,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Save"})]}):(0,d.jsxs)("button",{onClick:()=>o(!0),className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,d.jsx)(h.Edit3,{className:"w-4 h-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"Edit"})]})]})]})}),(0,d.jsx)("div",{className:"flex-1 p-4",children:(0,d.jsxs)("div",{className:"h-full",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Template Content"}),(0,d.jsx)("textarea",{value:l,onChange:a=>m(a.target.value),placeholder:"Enter your template content here...",className:"w-full h-full p-4 border border-orange-200 rounded-lg resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white text-slate-800 text-sm font-mono",readOnly:!n,style:{minHeight:"400px"}})]})}),(0,d.jsx)("div",{className:"p-4 border-t border-orange-200/50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between text-xs text-slate-500",children:[(0,d.jsxs)("span",{children:[l.length," characters • ",l.split("\n").length," lines"]}),n&&(0,d.jsx)("span",{className:"text-teal-600 font-medium",children:"Editing mode - Click Save to apply changes"})]})})]}):(0,d.jsx)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(i.FileText,{className:"mx-auto h-12 w-12 text-slate-400"}),(0,d.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-800",children:"No template selected"}),(0,d.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"Select a template from the sidebar to view and edit it."})]})})}},132941:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],c=(0,d.default)("sparkles",b)}},246390:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Sparkles:()=>d.default});var d=a.i(132941)},926063:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ComingSoonOverlay:()=>j});var d=a.i(674420),e=a.i(722851),f=a.i(767332),g=a.i(246390),h=a.i(582126),i=a.i(953731);function j(){let[a,b]=(0,e.useState)(!0);return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full -translate-y-16 translate-x-16 opacity-50"}),(0,d.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-100 to-amber-100 rounded-full translate-y-12 -translate-x-12 opacity-50"}),(0,d.jsx)("button",{onClick:()=>b(!1),className:"absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,d.jsx)(f.X,{className:"w-5 h-5 text-gray-500"})}),(0,d.jsxs)("div",{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-teal-500 to-emerald-600 rounded-full mb-4",children:(0,d.jsx)(g.Sparkles,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Coming Soon!"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Custom templates feature is under development"})]}),(0,d.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-lg",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(i.Zap,{className:"w-5 h-5 text-teal-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Smart Templates"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"AI-powered template suggestions"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(h.Clock,{className:"w-5 h-5 text-orange-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Quick Access"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"One-click template application"})]})]})]}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 text-white rounded-lg text-sm font-medium",children:[(0,d.jsx)(g.Sparkles,{className:"w-4 h-4 mr-2"}),"Expected: Q1 2025"]})}),(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Click the X button to close this overlay"})})]})]})}):null}},640005:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({TemplatesInterface:()=>j});var d=a.i(674420),e=a.i(722851),f=a.i(665225),g=a.i(699999),h=a.i(67137),i=a.i(926063);let b=[{id:"1",name:"General Consultation",content:"Patient presents with...\n\nChief Complaint:\n\nHistory of Present Illness:\n\nPhysical Examination:\n\nAssessment:\n\nPlan:",created_at:"2024-01-15T10:00:00Z",updated_at:"2024-01-15T10:00:00Z"},{id:"2",name:"Follow-up Visit",content:"Follow-up visit for...\n\nInterval History:\n\nCurrent Medications:\n\nReview of Systems:\n\nPhysical Examination:\n\nAssessment and Plan:",created_at:"2024-01-10T14:30:00Z",updated_at:"2024-01-12T09:15:00Z"},{id:"3",name:"Discharge Summary",content:"Discharge Summary\n\nAdmission Date:\nDischarge Date:\n\nPrimary Diagnosis:\nSecondary Diagnoses:\n\nHospital Course:\n\nDischarge Medications:\n\nFollow-up Instructions:",created_at:"2024-01-08T16:45:00Z",updated_at:"2024-01-08T16:45:00Z"}];function j({user:a,doctorId:c}){let[j,k]=(0,e.useState)(b[0]),[l]=(0,e.useState)(b);return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative",children:[(0,d.jsx)(f.DashboardHeader,{user:a}),(0,d.jsx)("main",{className:"pt-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)(g.TemplatesSidebar,{templates:l,selectedTemplate:j,onSelectTemplate:k,onNewTemplate:()=>{k({id:`new-${Date.now()}`,name:"New Template",content:"",created_at:new Date().toISOString(),updated_at:new Date().toISOString()})}})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsx)(h.TemplateEditor,{template:j,onSave:a=>{console.log("Saving template:",a)}})})]})})}),(0,d.jsx)(i.ComingSoonOverlay,{})]})}}}};

//# sourceMappingURL=_01f54f46._.js.map