{"version": 3, "sources": ["turbopack:///[project]/node_modules/lucide-react/src/icons/funnel.ts", "turbopack:///[project]/src/lib/types.ts", "turbopack:///[project]/src/components/analytics/consultation-modal.tsx", "turbopack:///[project]/src/components/analytics/consultations-list.tsx", "turbopack:///[project]/node_modules/lucide-react/src/icons/triangle-alert.ts", "turbopack:///[project]/src/components/analytics/quota-warning-modal.tsx", "turbopack:///[project]/src/lib/actions/data:9c0639 <text/javascript>", "turbopack:///[project]/src/components/analytics/quota-card.tsx", "turbopack:///[project]/src/lib/actions/data:7c22ae <text/javascript>", "turbopack:///[project]/src/components/analytics/referral-stats.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n", "export type Json =\n  | string\n  | number\n  | boolean\n  | null\n  | { [key: string]: <PERSON><PERSON> | undefined }\n  | Json[]\n\nexport type Database = {\n  public: {\n    Tables: {\n      admins: {\n        Row: {\n          created_at: string\n          email: string\n          id: string\n          name: string\n          password_hash: string\n          role: string\n          updated_at: string\n        }\n        Insert: {\n          created_at?: string\n          email: string\n          id?: string\n          name: string\n          password_hash: string\n          role?: string\n          updated_at?: string\n        }\n        Update: {\n          created_at?: string\n          email?: string\n          id?: string\n          name?: string\n          password_hash?: string\n          role?: string\n          updated_at?: string\n        }\n        Relationships: []\n      }\n      consultations: {\n        Row: {\n          additional_audio_urls: Json | null\n          ai_generated_note: string | null\n          primary_audio_url: string\n          created_at: string\n          doctor_id: string | null\n          edited_note: string | null\n          id: string\n          image_urls: Json | null\n          patient_number: number | null\n          patient_name: string | null\n          status: string\n          submitted_by: string\n          total_file_size_bytes: number | null\n          file_retention_until: string | null\n          updated_at: string\n          consultation_type: string\n          doctor_notes: string | null\n          additional_notes: string | null\n        }\n        Insert: {\n          additional_audio_urls?: Json | null\n          ai_generated_note?: string | null\n          primary_audio_url: string\n          created_at?: string\n          doctor_id?: string | null\n          edited_note?: string | null\n          id?: string\n          image_urls?: Json | null\n          patient_number?: number | null\n          patient_name?: string | null\n          status?: string\n          submitted_by: string\n          total_file_size_bytes?: number | null\n          file_retention_until?: string | null\n          updated_at?: string\n          consultation_type?: string\n          doctor_notes?: string | null\n          additional_notes?: string | null\n        }\n        Update: {\n          additional_audio_urls?: Json | null\n          ai_generated_note?: string | null\n          primary_audio_url?: string\n          created_at?: string\n          doctor_id?: string | null\n          edited_note?: string | null\n          id?: string\n          image_urls?: Json | null\n          patient_number?: number | null\n          patient_name?: string | null\n          status?: string\n          submitted_by?: string\n          total_file_size_bytes?: number | null\n          file_retention_until?: string | null\n          updated_at?: string\n          consultation_type?: string\n          doctor_notes?: string | null\n          additional_notes?: string | null\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"consultations_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      doctors: {\n        Row: {\n          approved: boolean\n          approved_at: string | null\n          approved_by: string | null\n          clinic_name: string | null\n          created_at: string\n          email: string\n          id: string\n          monthly_quota: number\n          name: string\n          password_hash: string\n          phone: string | null\n          quota_reset_at: string\n          quota_used: number\n\n          updated_at: string\n          referral_code: string | null\n          referred_by: string | null\n          conversion_date: string | null\n          referral_discount_earned: number\n          total_referrals: number\n          successful_referrals: number\n          current_plan_id: string | null\n          billing_status: string\n          trial_ends_at: string | null\n          last_payment_date: string | null\n          next_billing_date: string | null\n          available_discount_amount: number\n        }\n        Insert: {\n          approved?: boolean\n          approved_at?: string | null\n          approved_by?: string | null\n          clinic_name?: string | null\n          created_at?: string\n          email: string\n          id?: string\n          monthly_quota?: number\n          name: string\n          password_hash: string\n          phone?: string | null\n          quota_reset_at?: string\n          quota_used?: number\n\n          updated_at?: string\n          referral_code?: string | null\n          referred_by?: string | null\n          conversion_date?: string | null\n          referral_discount_earned?: number\n          total_referrals?: number\n          successful_referrals?: number\n          current_plan_id?: string | null\n          billing_status?: string\n          trial_ends_at?: string | null\n          last_payment_date?: string | null\n          next_billing_date?: string | null\n          available_discount_amount?: number\n        }\n        Update: {\n          approved?: boolean\n          approved_at?: string | null\n          approved_by?: string | null\n          clinic_name?: string | null\n          created_at?: string\n          email?: string\n          id?: string\n          monthly_quota?: number\n          name?: string\n          password_hash?: string\n          phone?: string | null\n          quota_reset_at?: string\n          quota_used?: number\n\n          updated_at?: string\n          referral_code?: string | null\n          referred_by?: string | null\n          conversion_date?: string | null\n          referral_discount_earned?: number\n          total_referrals?: number\n          successful_referrals?: number\n          current_plan_id?: string | null\n          billing_status?: string\n          trial_ends_at?: string | null\n          last_payment_date?: string | null\n          next_billing_date?: string | null\n          available_discount_amount?: number\n        }\n        Relationships: []\n      }\n      usage_logs: {\n        Row: {\n          action_type: string\n          consultation_id: string | null\n          created_at: string\n          doctor_id: string | null\n          id: string\n          metadata: Json | null\n          quota_after: number | null\n          quota_before: number | null\n        }\n        Insert: {\n          action_type: string\n          consultation_id?: string | null\n          created_at?: string\n          doctor_id?: string | null\n          id?: string\n          metadata?: Json | null\n          quota_after?: number | null\n          quota_before?: number | null\n        }\n        Update: {\n          action_type?: string\n          consultation_id?: string | null\n          created_at?: string\n          doctor_id?: string | null\n          id?: string\n          metadata?: Json | null\n          quota_after?: number | null\n          quota_before?: number | null\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"usage_logs_consultation_id_fkey\"\n            columns: [\"consultation_id\"]\n            isOneToOne: false\n            referencedRelation: \"consultations\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"usage_logs_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      referral_analytics: {\n        Row: {\n          id: string\n          referrer_id: string | null\n          referred_doctor_id: string | null\n          referral_code: string\n          signup_date: string\n          conversion_date: string | null\n          discount_earned: number\n          status: string\n          metadata: Json | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          referrer_id?: string | null\n          referred_doctor_id?: string | null\n          referral_code: string\n          signup_date?: string\n          conversion_date?: string | null\n          discount_earned?: number\n          status?: string\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          referrer_id?: string | null\n          referred_doctor_id?: string | null\n          referral_code?: string\n          signup_date?: string\n          conversion_date?: string | null\n          discount_earned?: number\n          status?: string\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"referral_analytics_referrer_id_fkey\"\n            columns: [\"referrer_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"referral_analytics_referred_doctor_id_fkey\"\n            columns: [\"referred_doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      billing_plans: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          monthly_price: number\n          quota_limit: number\n          features: Json | null\n          active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          description?: string | null\n          monthly_price: number\n          quota_limit: number\n          features?: Json | null\n          active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          description?: string | null\n          monthly_price?: number\n          quota_limit?: number\n          features?: Json | null\n          active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: []\n      }\n      billing_transactions: {\n        Row: {\n          id: string\n          doctor_id: string\n          plan_id: string | null\n          amount: number\n          discount_amount: number\n          final_amount: number\n          payment_method: string | null\n          payment_status: string\n          payment_date: string | null\n          billing_period_start: string\n          billing_period_end: string\n          payment_reference: string | null\n          notes: string | null\n          created_by: string | null\n          metadata: Json | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          plan_id?: string | null\n          amount: number\n          discount_amount?: number\n          final_amount: number\n          payment_method?: string | null\n          payment_status?: string\n          payment_date?: string | null\n          billing_period_start: string\n          billing_period_end: string\n          payment_reference?: string | null\n          notes?: string | null\n          created_by?: string | null\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          plan_id?: string | null\n          amount?: number\n          discount_amount?: number\n          final_amount?: number\n          payment_method?: string | null\n          payment_status?: string\n          payment_date?: string | null\n          billing_period_start?: string\n          billing_period_end?: string\n          payment_reference?: string | null\n          notes?: string | null\n          created_by?: string | null\n          metadata?: Json | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"billing_transactions_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"billing_transactions_plan_id_fkey\"\n            columns: [\"plan_id\"]\n            isOneToOne: false\n            referencedRelation: \"billing_plans\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      referral_discounts: {\n        Row: {\n          id: string\n          doctor_id: string\n          referral_analytics_id: string\n          discount_percentage: number\n          discount_amount: number\n          original_amount: number\n          applied_to_transaction_id: string | null\n          status: string\n          valid_until: string\n          applied_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          referral_analytics_id: string\n          discount_percentage?: number\n          discount_amount: number\n          original_amount: number\n          applied_to_transaction_id?: string | null\n          status?: string\n          valid_until: string\n          applied_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          referral_analytics_id?: string\n          discount_percentage?: number\n          discount_amount?: number\n          original_amount?: number\n          applied_to_transaction_id?: string | null\n          status?: string\n          valid_until?: string\n          applied_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"referral_discounts_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          },\n          {\n            foreignKeyName: \"referral_discounts_referral_analytics_id_fkey\"\n            columns: [\"referral_analytics_id\"]\n            isOneToOne: false\n            referencedRelation: \"referral_analytics\"\n            referencedColumns: [\"id\"]\n          },\n        ]\n      }\n      contact_requests: {\n        Row: {\n          id: string\n          doctor_id: string\n          doctor_name: string\n          doctor_email: string\n          clinic_name: string\n          phone_number: string\n          request_type: string\n          message: string | null\n          status: string\n          contacted_at: string | null\n          resolved_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          doctor_id: string\n          doctor_name: string\n          doctor_email: string\n          clinic_name: string\n          phone_number: string\n          request_type: string\n          message?: string | null\n          status?: string\n          contacted_at?: string | null\n          resolved_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          doctor_id?: string\n          doctor_name?: string\n          doctor_email?: string\n          clinic_name?: string\n          phone_number?: string\n          request_type?: string\n          message?: string | null\n          status?: string\n          contacted_at?: string | null\n          resolved_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Relationships: [\n          {\n            foreignKeyName: \"contact_requests_doctor_id_fkey\"\n            columns: [\"doctor_id\"]\n            isOneToOne: false\n            referencedRelation: \"doctors\"\n            referencedColumns: [\"id\"]\n          }\n        ]\n      }\n    }\n    Views: {\n      admin_dashboard_summary: {\n        Row: {\n          total_doctors: number\n          pending_approvals: number\n          approved_doctors: number\n          total_consultations: number\n          total_ai_generations: number\n          quota_usage_percentage: number\n        }\n        Relationships: []\n      }\n      admin_doctors_with_stats: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          phone: string | null\n          clinic_name: string | null\n          monthly_quota: number\n          quota_used: number\n          quota_reset_at: string\n          approved: boolean\n          approved_by: string | null\n          approved_at: string | null\n          referral_code: string | null\n          referred_by: string | null\n          billing_status: string | null\n          trial_ends_at: string | null\n          created_at: string\n          updated_at: string\n          total_consultations: number\n          this_month_generations: number\n          last_activity: string | null\n          quota_percentage: number\n        }\n        Relationships: []\n      }\n    }\n    Functions: {\n      check_and_update_quota: {\n        Args: { doctor_uuid: string }\n        Returns: boolean\n      }\n      handle_referral_conversion: {\n        Args: { referred_doctor_uuid: string }\n        Returns: boolean\n      }\n      generate_referral_code: {\n        Args: { doctor_name: string; doctor_email: string }\n        Returns: string\n      }\n      apply_referral_discount: {\n        Args: { transaction_id: string; discount_amount: number }\n        Returns: boolean\n      }\n      complete_payment: {\n        Args: { transaction_id: string }\n        Returns: boolean\n      }\n      reset_all_quotas: {\n        Args: Record<PropertyKey, never>\n        Returns: number\n      }\n      update_doctor_quota: {\n        Args: { doctor_uuid: string; new_quota: number; admin_uuid: string }\n        Returns: boolean\n      }\n      set_patient_number: {\n        Args: Record<PropertyKey, never>;\n        Returns: unknown;\n      }\n      update_updated_at_column: {\n        Args: Record<PropertyKey, never>;\n        Returns: unknown;\n      }\n      get_consultation_stats: {\n        Args: { doctor_uuid: string }\n        Returns: {\n          total_consultations: number\n          pending_consultations: number\n          approved_consultations: number\n          today_consultations: number\n        }[]\n      }\n    }\n    Enums: {\n      [_ in never]: never\n    }\n    CompositeTypes: {\n      [_ in never]: never\n    }\n  }\n}\n\ntype DefaultSchema = Database[Extract<keyof Database, \"public\">]\n\nexport type Tables<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof (DefaultSchema[\"Tables\"] & DefaultSchema[\"Views\"])\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof (Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"] &\n        Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Views\"])\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? (Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"] &\n      Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Views\"])[TableName] extends {\n      Row: infer R\n    }\n    ? R\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema[\"Tables\"] &\n        DefaultSchema[\"Views\"])\n    ? (DefaultSchema[\"Tables\"] &\n        DefaultSchema[\"Views\"])[DefaultSchemaTableNameOrOptions] extends {\n        Row: infer R\n      }\n      ? R\n      : never\n    : never\n\nexport type TablesInsert<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof DefaultSchema[\"Tables\"]\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"]\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"][TableName] extends {\n      Insert: infer I\n    }\n    ? I\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema[\"Tables\"]\n    ? DefaultSchema[\"Tables\"][DefaultSchemaTableNameOrOptions] extends {\n        Insert: infer I\n      }\n      ? I\n      : never\n    : never\n\nexport type TablesUpdate<\n  DefaultSchemaTableNameOrOptions extends\n    | keyof DefaultSchema[\"Tables\"]\n    | { schema: keyof Database },\n  TableName extends DefaultSchemaTableNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"]\n    : never = never,\n> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaTableNameOrOptions[\"schema\"]][\"Tables\"][TableName] extends {\n      Update: infer U\n    }\n    ? U\n    : never\n  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema[\"Tables\"]\n    ? DefaultSchema[\"Tables\"][DefaultSchemaTableNameOrOptions] extends {\n        Update: infer U\n      }\n      ? U\n      : never\n    : never\n\nexport type Enums<\n  DefaultSchemaEnumNameOrOptions extends\n    | keyof DefaultSchema[\"Enums\"]\n    | { schema: keyof Database },\n  EnumName extends DefaultSchemaEnumNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof Database[DefaultSchemaEnumNameOrOptions[\"schema\"]][\"Enums\"]\n    : never = never,\n> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }\n  ? Database[DefaultSchemaEnumNameOrOptions[\"schema\"]][\"Enums\"][EnumName]\n  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema[\"Enums\"]\n    ? DefaultSchema[\"Enums\"][DefaultSchemaEnumNameOrOptions]\n    : never\n\n// Consultation type enum for different consultation workflows\nexport type ConsultationType = 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology';\n\n// Consultation type labels for UI display\nexport const CONSULTATION_TYPE_LABELS: Record<ConsultationType, string> = {\n  outpatient: 'Outpatient Consultation',\n  discharge: 'Discharge Summary',\n  surgery: 'Operative Note',\n  radiology: 'Radiology Report',\n  dermatology: 'Dermatology Note',\n  cardiology_echo: 'Echocardiogram Report',\n  ivf_cycle: 'IVF Cycle Summary',\n  pathology: 'Histopathology Report'\n};\n\nexport type CompositeTypes<\n  PublicCompositeTypeNameOrOptions extends\n    | keyof DefaultSchema[\"CompositeTypes\"]\n    | { schema: keyof Database },\n  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {\n    schema: keyof Database\n  }\n    ? keyof (Database[PublicCompositeTypeNameOrOptions[\"schema\"]][\"CompositeTypes\"])\n    : never = never,\n> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }\n  ? Database[PublicCompositeTypeNameOrOptions[\"schema\"]][\"CompositeTypes\"][CompositeTypeName]\n  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema[\"CompositeTypes\"]\n    ? DefaultSchema[\"CompositeTypes\"][PublicCompositeTypeNameOrOptions]\n    : never\n\nexport const Constants = {\n  public: {\n    Enums: {},\n  },\n} as const\n\nexport type Doctor = Tables<'doctors'>;\nexport type Consultation = Tables<'consultations'>;\nexport type Admin = Tables<'admins'>;\nexport type ReferralAnalytics = Tables<'referral_analytics'>;\n\n\n\nexport type ApiResponse<T> = { success: true; data: T } | { success: false; error: string };\n\nexport interface AdminDashboardStats {\n  total_doctors: number;\n  pending_approvals: number;\n  approved_doctors: number;\n  total_consultations: number;\n  total_ai_generations: number;\n  quota_usage_percentage: number;\n}\n\nexport interface DashboardStats {\n  total_consultations: number;\n  pending_consultations: number;\n  generated_consultations: number;\n  approved_consultations: number;\n  today_consultations: number;\n}\n\nexport interface QuotaInfo {\n  monthly_quota: number;\n  quota_used: number;\n  quota_remaining: number;\n  quota_percentage: number;\n  quota_reset_at: string;\n  days_until_reset: number;\n}\n\nexport interface ReferralInfo {\n  referral_code: string;\n  total_referrals: number;\n  successful_referrals: number;\n  pending_referrals: number;\n  discount_earned: number;\n  referred_by?: {\n    name: string;\n    referral_code: string;\n  } | null;\n  recent_referrals: Array<{\n    id: string;\n    name: string;\n    email: string;\n    signup_date: string;\n    conversion_date: string | null;\n    status: 'pending' | 'converted' | 'expired';\n  }>;\n}\n\nexport type DoctorWithStats = Omit<Tables<'doctors'>, 'consultations' | 'phone' | 'clinic_name' | 'approved_by' | 'approved_at' | 'template_config'> & {\n  phone?: string;\n  clinic_name?: string;\n  approved_by?: string;\n  approved_at?: string;\n  total_consultations: number;\n  this_month_generations: number;\n  quota_percentage: number;\n  last_activity?: string;\n};\n\n// Type for the admin_doctors_with_stats view\nexport type AdminDoctorWithStats = {\n  id: string;\n  email: string;\n  name: string;\n  phone?: string;\n  clinic_name?: string;\n  monthly_quota: number;\n  quota_used: number;\n  quota_reset_at: string;\n  approved: boolean;\n  approved_by?: string;\n  approved_at?: string;\n  referral_code?: string;\n  referred_by?: string;\n  billing_status?: string;\n  trial_ends_at?: string;\n  created_at: string;\n  updated_at: string;\n  total_consultations: number;\n  this_month_generations: number;\n  last_activity?: string;\n  quota_percentage: number;\n};\n\nexport type AdminActionRequest =\n  | { action: 'approve'; doctor_id: string }\n  | { action: 'reject'; doctor_id: string }\n  | { action: 'update_quota'; doctor_id: string; data: { quota: number; reason?: string } }\n  | { action: 'disable'; doctor_id: string }\n  | { action: 'enable'; doctor_id: string };\n\nexport interface FormState {\n  success: boolean;\n  message: string;\n  fieldErrors?: { [key: string]: string[] };\n}\n\nexport interface ImageFile {\n  id: string;\n  file: File;\n  preview?: string; // Optional for optimistic UI - not needed when waiting for final state\n  name: string;\n  type: string;\n  size: number;\n}\n\nexport interface ImageCaptureState {\n  images: ImageFile[];\n  status?: 'idle' | 'capturing' | 'uploaded' | 'error';\n  error: string | null;\n}\n\nexport interface AudioRecordingState {\n  isRecording: boolean;\n  duration: number;\n  audioBlob?: Blob;\n  audioFile?: File;\n  error?: string | null;\n  status?: 'idle' | 'recording' | 'recorded' | 'uploading' | 'uploaded' | 'error';\n}\n\nexport interface SessionPayload {\n  userId: string;\n  expiresAt: Date;\n  adminId?: string;\n  role?: 'admin' | 'super_admin';\n}\n\n", "'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { createPortal } from 'react-dom'\nimport { X, Play, Pause, Wand2, Save, Copy, Camera, Upload, Mic, Square, Trash2, Loader2 } from 'lucide-react'\nimport { Consultation, ConsultationType, CONSULTATION_TYPE_LABELS } from '@/lib/types'\nimport { formatDate } from '@/lib/utils'\nimport { approveConsultation, addAdditionalAudio, deleteAdditionalAudio, deleteConsultationImage } from '@/lib/actions/consultations'\nimport { trackConsultation } from '@/lib/analytics'\nimport Image from 'next/image'\n\ninterface ConsultationModalProps {\n  consultation: Consultation\n  onClose: () => void\n  onConsultationUpdate?: (updatedConsultation: Consultation) => void\n  doctorName?: string\n}\n\nexport function ConsultationModal({ consultation, onClose, onConsultationUpdate, doctorName }: ConsultationModalProps) {\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [isApproving, setIsApproving] = useState(false)\n  const [editedNote, setEditedNote] = useState(consultation.edited_note || consultation.ai_generated_note || '')\n  const [playingAudio, setPlayingAudio] = useState<HTMLAudioElement | null>(null)\n  const [isLoadingAudio, setIsLoadingAudio] = useState(false)\n\n  // Web Audio API refs for Safari\n  const audioContextRef = useRef<AudioContext | null>(null)\n  const audioSourceRef = useRef<AudioBufferSourceNode | null>(null)\n  const [success, setSuccess] = useState<string>('')\n  const [additionalImages, setAdditionalImages] = useState<Array<{ id: string; url: string; preview?: string }>>([])\n  const [isRecordingAdditional, setIsRecordingAdditional] = useState(false)\n  const [additionalAudioBlob, setAdditionalAudioBlob] = useState<Blob | null>(null)\n  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)\n\n  const [streamingText, setStreamingText] = useState('')\n  const [mounted, setMounted] = useState(false)\n\n  // New consultation type and additional notes state\n  const [consultationType, setConsultationType] = useState<ConsultationType>(\n    (consultation.consultation_type as ConsultationType) || 'outpatient'\n  )\n  const [additionalNotes, setAdditionalNotes] = useState(consultation.additional_notes || '')\n\n  // Delete functionality state\n  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)\n  const [showDeleteMenu, setShowDeleteMenu] = useState<{ type: 'audio' | 'image', url: string } | null>(null)\n  \n  const fileInputRef = useRef<HTMLInputElement | null>(null)\n  const cameraInputRef = useRef<HTMLInputElement | null>(null)\n  const successTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n\n  // Function to set success message with auto-clear timeout\n  const setSuccessWithTimeout = (message: string, timeout = 3000) => {\n    // Clear any existing timeout\n    if (successTimeoutRef.current) {\n      clearTimeout(successTimeoutRef.current)\n    }\n\n    setSuccess(message)\n\n    // Set new timeout to clear message\n    if (message) {\n      successTimeoutRef.current = setTimeout(() => {\n        setSuccess('')\n        successTimeoutRef.current = null\n      }, timeout)\n    }\n  }\n\n  // Handle client-side mounting\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Clear success messages when consultation changes\n  useEffect(() => {\n    setSuccess('')\n    if (successTimeoutRef.current) {\n      clearTimeout(successTimeoutRef.current)\n      successTimeoutRef.current = null\n    }\n  }, [consultation.id])\n\n  // Cleanup long press timer on unmount\n  useEffect(() => {\n    return () => {\n      if (longPressTimer) {\n        clearTimeout(longPressTimer)\n      }\n    }\n  }, [longPressTimer])\n\n  // Sync editedNote state when consultation data changes\n  useEffect(() => {\n    setEditedNote(consultation.edited_note || consultation.ai_generated_note || '')\n  }, [consultation.edited_note, consultation.ai_generated_note])\n\n  // Sync additionalNotes state when consultation data changes\n  useEffect(() => {\n    setAdditionalNotes(consultation.additional_notes || '')\n  }, [consultation.additional_notes])\n\n  // Handle additional image upload - Upload to Cloudflare R2\n  const handleImageUpload = async (files: FileList) => {\n    try {\n      setSuccess('Uploading images...')\n\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i]\n\n        if (!file.type.startsWith('image/')) continue\n        if (file.size > 10 * 1024 * 1024) continue // 10MB limit\n\n        // Upload to Cloudflare R2\n        const { uploadFile } = await import('@/lib/storage')\n        const uploadResult = await uploadFile(file, consultation.doctor_id || '', consultation.id, 'image')\n\n        if (uploadResult.success && uploadResult.url) {\n          const preview = URL.createObjectURL(file)\n          setAdditionalImages(prev => [...prev, {\n            id: `${Date.now()}-${i}`,\n            url: uploadResult.url!,\n            preview\n          }])\n        } else {\n          setSuccess(`Failed to upload ${file.name}: ${uploadResult.error}`)\n          return\n        }\n      }\n\n      setSuccessWithTimeout('Images uploaded successfully!')\n    } catch (_error) {\n      setSuccess('Failed to upload images')\n    }\n  }\n\n  // Remove additional image\n  const removeAdditionalImage = (id: string) => {\n    setAdditionalImages(prev => {\n      const imageToRemove = prev.find(img => img.id === id)\n      if (imageToRemove && imageToRemove.preview && typeof imageToRemove.preview === 'string') {\n        URL.revokeObjectURL(imageToRemove.preview)\n      }\n      return prev.filter(img => img.id !== id)\n    })\n  }\n\n  const handleGenerateSummary = async () => {\n    setIsGenerating(true)\n    setSuccess('')\n    setStreamingText('')\n\n    try {\n      // Combine original images with additional images\n      const allImages = [\n        ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),\n        ...additionalImages.map(img => img.url)\n      ].filter((img): img is string => typeof img === 'string' && img !== null)\n\n      // Update the consultation with additional images before generating summary\n      if (additionalImages.length > 0) {\n        const { updateConsultationImages } = await import('@/lib/actions/consultations')\n        await updateConsultationImages(consultation.id, allImages)\n      }\n\n      // Simple client-side streaming with quota checking\n      try {\n          const response = await fetch('/api/generate-summary-stream', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              primary_audio_url: consultation.primary_audio_url,\n              additional_audio_urls: Array.isArray(consultation.additional_audio_urls) ? consultation.additional_audio_urls : [],\n              image_urls: [\n                ...(Array.isArray(consultation.image_urls) ? consultation.image_urls : []),\n                ...allImages\n              ],\n\n              submitted_by: consultation.submitted_by || 'doctor',\n              consultation_type: consultationType,\n              doctor_notes: consultation.doctor_notes || undefined,\n              additional_notes: additionalNotes || undefined,\n              patient_name: consultation.patient_name || undefined,\n              doctor_name: doctorName || undefined,\n              created_at: consultation.created_at || undefined,\n            }),\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n          }\n\n          const reader = response.body?.getReader()\n          if (!reader) {\n            throw new Error('No reader available')\n          }\n\n          const decoder = new TextDecoder()\n          let fullSummary = ''\n          setStreamingText('')\n\n          while (true) {\n            const { done, value } = await reader.read()\n            if (done) break\n\n            const chunk = decoder.decode(value, { stream: true })\n            const lines = chunk.split('\\n')\n\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.slice(6))\n                  if (data.type === 'chunk' && data.text) {\n                    // Format text with proper line breaks\n                    const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                    fullSummary += formattedText\n                    // Instantly update streaming text with accumulated content\n                    setStreamingText(fullSummary)\n                  }\n                } catch (_e) {\n                  // Ignore parse errors\n                }\n              }\n            }\n          }\n\n          setEditedNote(fullSummary)\n          \n          // Save the streamed summary using the proper server action\n          try {\n            const { saveStreamingSummary } = await import('@/lib/actions/consultations')\n            const saveResult = await saveStreamingSummary(consultation.id, fullSummary)\n\n            if (saveResult.success) {\n              setSuccessWithTimeout('Streaming summary generated and saved successfully!')\n\n              // Track successful consultation generation (PII-free)\n              trackConsultation('generated', consultationType)\n\n              // Update the consultation object for the parent component\n              if (onConsultationUpdate) {\n                onConsultationUpdate({\n                  ...consultation,\n                  ai_generated_note: fullSummary,\n                  status: 'generated' as const\n                })\n              }\n            } else {\n              console.error('Failed to save streaming summary:', saveResult.error)\n              setSuccess(`Summary generated but failed to save: ${saveResult.error}`)\n            }\n          } catch (saveError) {\n            console.error('Error saving streaming summary:', saveError)\n            setSuccess('Summary generated but failed to save. Please try regenerate.')\n          }\n          \n          setTimeout(() => setSuccess(''), 3000)\n        } catch (error) {\n          console.error('Streaming error:', error)\n          setSuccess('Failed to generate streaming summary')\n          setTimeout(() => setSuccess(''), 5000)\n        }\n\n    } catch {\n      setSuccess('An unexpected error occurred')\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handleApprove = async () => {\n    if (!editedNote.trim()) {\n      setSuccess('Please provide a summary before approving')\n      return\n    }\n\n    setIsApproving(true)\n    setSuccess('')\n\n    try {\n      const result = await approveConsultation(consultation.id, editedNote)\n\n      if (result.success) {\n        setSuccess('Consultation approved successfully!')\n\n        // Track successful consultation approval (PII-free)\n        trackConsultation('approved', consultationType)\n\n        setTimeout(() => {\n          setSuccess('')\n          onClose()\n        }, 2000)\n      } else {\n        setSuccess(result.error || 'Failed to approve consultation')\n      }\n    } catch {\n      setSuccess('An unexpected error occurred')\n    } finally {\n      setIsApproving(false)\n    }\n  }\n\n  const handleCopyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(editedNote)\n      setSuccessWithTimeout('Copied to clipboard!', 2000)\n    } catch {\n      setSuccess('Failed to copy to clipboard')\n    }\n  }\n\n  // Web Audio API playback for Safari\n  const handleWebAudioPlayback = async () => {\n    // Stop logic - if audio is playing, stop it\n    if (audioSourceRef.current) {\n      audioSourceRef.current.stop()\n      audioSourceRef.current = null\n      setPlayingAudio(null)\n      return\n    }\n\n    setIsLoadingAudio(true)\n    setSuccess('Loading audio...')\n\n    try {\n      // Create or resume AudioContext (must be in user gesture)\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()\n      }\n      if (audioContextRef.current.state === 'suspended') {\n        await audioContextRef.current.resume()\n      }\n\n      const audioContext = audioContextRef.current\n\n      // Fetch audio through proxy\n      const proxyUrl = `/api/audio-proxy?url=${encodeURIComponent(consultation.primary_audio_url)}`\n      const response = await fetch(proxyUrl)\n      if (!response.ok) {\n        throw new Error(`Audio fetch failed: ${response.statusText}`)\n      }\n      const audioData = await response.arrayBuffer()\n\n      // Decode audio data (handles format conversion)\n      const audioBuffer = await audioContext.decodeAudioData(audioData)\n\n      // Create source and play\n      const source = audioContext.createBufferSource()\n      source.buffer = audioBuffer\n      source.connect(audioContext.destination)\n      source.start(0)\n\n      // Handle playback end\n      source.onended = () => {\n        setPlayingAudio(null)\n        audioSourceRef.current = null\n      }\n\n      audioSourceRef.current = source\n      setPlayingAudio({} as HTMLAudioElement) // Just to indicate playing state\n      setSuccess('')\n\n    } catch (error) {\n      console.error('Web Audio API Error:', error)\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      setSuccess(`Unable to play audio: ${errorMessage}`)\n      setPlayingAudio(null)\n    } finally {\n      setIsLoadingAudio(false)\n    }\n  }\n\n  const playAudio = async () => {\n    // Detect Safari\n    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)\n\n    // For Safari, use Web Audio API\n    if (isSafari) {\n      await handleWebAudioPlayback()\n      return\n    }\n\n    // For other browsers, use existing logic\n    if (playingAudio) {\n      playingAudio.pause()\n      playingAudio.currentTime = 0\n      setPlayingAudio(null)\n      return\n    }\n\n    try {\n      const audio = new Audio(consultation.primary_audio_url)\n      audio.onended = () => setPlayingAudio(null)\n      audio.onerror = () => {\n        setPlayingAudio(null)\n        setSuccess('Failed to play audio')\n      }\n\n      setPlayingAudio(audio)\n      await audio.play()\n    } catch (error) {\n      console.error('Audio playback error:', error)\n      setPlayingAudio(null)\n      setSuccess('Failed to play audio')\n    }\n  }\n\n  // Get the best supported audio format for this device\n  const getSupportedMimeType = () => {\n    const types = [\n      'audio/webm;codecs=opus',\n      'audio/webm',\n      'audio/mp4',\n      'audio/mpeg'\n    ]\n    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm'\n  }\n\n  // Additional audio recording functions\n  const startAdditionalRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })\n      const mimeType = getSupportedMimeType()\n      const recorder = new MediaRecorder(stream, { mimeType })\n      const chunks: BlobPart[] = []\n\n      recorder.ondataavailable = (e) => chunks.push(e.data)\n      recorder.onstop = () => {\n        const blob = new Blob(chunks, { type: mimeType })\n        setAdditionalAudioBlob(blob)\n        stream.getTracks().forEach(track => track.stop())\n      }\n\n      recorder.start()\n      setMediaRecorder(recorder)\n      setIsRecordingAdditional(true)\n    } catch (_error) {\n      setSuccess('Failed to start recording. Please check microphone permissions.')\n    }\n  }\n\n  const stopAdditionalRecording = () => {\n    if (mediaRecorder && mediaRecorder.state === 'recording') {\n      mediaRecorder.stop()\n      setIsRecordingAdditional(false)\n      setMediaRecorder(null)\n    }\n  }\n\n  const uploadAdditionalAudio = async () => {\n    if (!additionalAudioBlob) return\n\n    try {\n      const audioFile = new File([additionalAudioBlob], `additional_audio_${Date.now()}.wav`, {\n        type: 'audio/wav'\n      })\n\n      const result = await addAdditionalAudio(consultation.id, audioFile)\n      if (result.success) {\n        setSuccess('Additional audio uploaded successfully! Refreshing consultation data...')\n        setAdditionalAudioBlob(null)\n\n        // Fetch updated consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n            setSuccess('Additional audio uploaded successfully! Please regenerate summary to include this audio.')\n          }\n        }\n\n        setSuccessWithTimeout('')\n      } else {\n        setSuccess(result.error || 'Failed to upload additional audio')\n      }\n    } catch (_error) {\n      setSuccess('Failed to upload additional audio')\n    }\n  }\n\n  // Delete handlers\n  const handleDeleteAudio = async (audioUrl: string) => {\n    try {\n      setSuccess('Deleting audio...')\n      const result = await deleteAdditionalAudio(consultation.id, audioUrl)\n\n      if (result.success) {\n        setSuccessWithTimeout('Audio deleted successfully!')\n\n        // Refresh consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n          }\n        }\n      } else {\n        setSuccess(result.error || 'Failed to delete audio')\n      }\n    } catch (_error) {\n      setSuccess('Failed to delete audio')\n    } finally {\n      setShowDeleteMenu(null)\n    }\n  }\n\n  const handleDeleteImage = async (imageUrl: string) => {\n    try {\n      setSuccess('Deleting image...')\n      const result = await deleteConsultationImage(consultation.id, imageUrl)\n\n      if (result.success) {\n        setSuccessWithTimeout('Image deleted successfully!')\n\n        // Refresh consultation data\n        const { getConsultations } = await import('@/lib/actions/consultations')\n        const consultationsResult = await getConsultations()\n\n        if (consultationsResult.success && consultationsResult.data) {\n          const updatedConsultation = consultationsResult.data.consultations.find(c => c.id === consultation.id)\n          if (updatedConsultation && onConsultationUpdate) {\n            onConsultationUpdate(updatedConsultation)\n          }\n        }\n      } else {\n        setSuccess(result.error || 'Failed to delete image')\n      }\n    } catch (_error) {\n      setSuccess('Failed to delete image')\n    } finally {\n      setShowDeleteMenu(null)\n    }\n  }\n\n  // Long press handlers for mobile\n  const handleLongPressStart = (type: 'audio' | 'image', url: string) => {\n    const timer = setTimeout(() => {\n      setShowDeleteMenu({ type, url })\n    }, 500) // 500ms long press\n    setLongPressTimer(timer)\n  }\n\n  const handleLongPressEnd = () => {\n    if (longPressTimer) {\n      clearTimeout(longPressTimer)\n      setLongPressTimer(null)\n    }\n  }\n\n  const modalContent = (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50\">\n      <div className=\"absolute top-4 left-1/2 transform -translate-x-1/2 bg-white rounded-2xl max-w-5xl w-[95%] h-[90vh] shadow-2xl flex flex-col overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 px-3 sm:px-6 py-3 sm:py-4 border-b border-orange-200/50\">\n          <div className=\"flex items-start sm:items-center justify-between gap-2\">\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"text-lg sm:text-xl font-bold text-slate-800 truncate\">\n                {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}\n              </h3>\n              <p className=\"text-xs sm:text-sm text-slate-600 break-words\">\n                {formatDate(consultation.created_at)} • {consultation.submitted_by} • {consultation.status}\n              </p>\n              {consultation.doctor_notes && (\n                <p className=\"text-xs sm:text-sm text-slate-500 mt-1 italic line-clamp-2\">\n                  Doctor&apos;s Notes: {consultation.doctor_notes}\n                </p>\n              )}\n            </div>\n            <div className=\"flex flex-col sm:flex-row items-end sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-shrink-0\">\n              {/* Consultation Type Dropdown */}\n              <div className=\"flex flex-col items-end\">\n                <label className=\"text-xs text-slate-600 mb-1 whitespace-nowrap hidden sm:block\">Consultation Type</label>\n                <label className=\"text-xs text-slate-600 mb-1 whitespace-nowrap sm:hidden\">Type</label>\n                <select\n                  value={consultationType}\n                  onChange={(e) => setConsultationType(e.target.value as ConsultationType)}\n                  className=\"px-2 py-1 text-xs border border-orange-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent w-24 sm:w-auto text-black\"\n                  disabled={consultation.status === 'approved'}\n                >\n                  {Object.entries(CONSULTATION_TYPE_LABELS).map(([value, label]) => (\n                    <option key={value} value={value} className=\"text-black\">\n                      {label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-1.5 sm:p-2 hover:bg-orange-100 rounded-xl transition-colors flex-shrink-0\"\n              >\n                <X className=\"w-4 h-4 sm:w-5 sm:h-5 text-slate-600\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-3 sm:p-6 space-y-4 sm:space-y-6 flex-1 overflow-y-auto\">\n          {/* Audio Section - Side by Side Layout */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\n            {/* Primary Audio */}\n            <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-200\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"text-lg font-semibold text-slate-800 flex items-center\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mr-3\"></div>\n                  Primary Audio\n                </h4>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={playAudio}\n                  disabled={isLoadingAudio}\n                  className=\"flex items-center space-x-3 px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                >\n                  {isLoadingAudio ? (\n                    <Loader2 className=\"w-5 h-5 animate-spin\" />\n                  ) : playingAudio ? (\n                    <Pause className=\"w-5 h-5\" />\n                  ) : (\n                    <Play className=\"w-5 h-5\" />\n                  )}\n                  <span>\n                    {isLoadingAudio ? 'Loading...' : playingAudio ? 'Pause Audio' : 'Play Audio'}\n                  </span>\n                </button>\n              </div>\n            </div>\n\n            {/* Additional Audio */}\n            {consultation.status !== 'approved' && (\n              <div className=\"bg-gradient-to-r from-teal-50 to-emerald-50 rounded-xl p-3 sm:p-5 border border-teal-200\">\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0\">\n                  <h4 className=\"text-base sm:text-lg font-semibold text-slate-800 flex items-center\">\n                    <div className=\"w-2 h-2 bg-teal-500 rounded-full mr-2 sm:mr-3\"></div>\n                    Additional Audio\n                  </h4>\n                  <span className=\"text-xs text-gray-500 bg-white px-2 py-1 rounded-full self-start\">Optional</span>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 gap-2\">\n                    {!isRecordingAdditional && !additionalAudioBlob && (\n                      <button\n                        onClick={startAdditionalRecording}\n                        className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg text-sm font-medium transition-all duration-200 w-full sm:w-auto\"\n                      >\n                        <Mic className=\"w-4 h-4\" />\n                        <span>Record</span>\n                      </button>\n                    )}\n\n                    {isRecordingAdditional && (\n                      <button\n                        onClick={stopAdditionalRecording}\n                        className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg text-sm font-medium animate-pulse transition-all duration-200 w-full sm:w-auto\"\n                      >\n                        <Square className=\"w-4 h-4\" />\n                        <span>Stop Recording</span>\n                      </button>\n                    )}\n\n                    {additionalAudioBlob && (\n                      <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto\">\n                        <button\n                          onClick={uploadAdditionalAudio}\n                          className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-all duration-200\"\n                        >\n                          <Upload className=\"w-4 h-4\" />\n                          <span>Upload</span>\n                        </button>\n                        <button\n                          onClick={() => setAdditionalAudioBlob(null)}\n                          className=\"flex items-center justify-center space-x-2 px-3 sm:px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-all duration-200\"\n                        >\n                          <X className=\"w-4 h-4\" />\n                          <span>Cancel</span>\n                        </button>\n                      </div>\n                    )}\n                  </div>\n\n                  {isRecordingAdditional && (\n                    <div className=\"flex items-center space-x-2 bg-red-100 px-3 py-2 rounded-lg\">\n                      <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                      <span className=\"text-sm text-red-700 font-medium\">Recording...</span>\n                    </div>\n                  )}\n\n                  {additionalAudioBlob && (\n                    <div className=\"flex items-center space-x-2 bg-green-100 px-3 py-2 rounded-lg\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-sm text-green-700 font-medium\">Ready to upload</span>\n                    </div>\n                  )}\n\n                  {/* Show existing additional audio files */}\n                  {consultation.additional_audio_urls &&\n                   Array.isArray(consultation.additional_audio_urls) &&\n                   consultation.additional_audio_urls.length > 0 && (\n                    <div className=\"p-3 bg-white rounded-lg border\">\n                      <p className=\"text-sm text-gray-600 mb-2\">Additional Audio Files ({consultation.additional_audio_urls.length})</p>\n                      <div className=\"space-y-2\">\n                        {consultation.additional_audio_urls\n                          .filter((audioUrl): audioUrl is string => typeof audioUrl === 'string' && audioUrl !== null)\n                          .map((audioUrl, index) => (\n                          <div\n                            key={index}\n                            className=\"flex items-center justify-between p-2 bg-gray-50 rounded border group\"\n                            onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'audio', url: audioUrl })}\n                            onMouseLeave={() => setShowDeleteMenu(null)}\n                            onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('audio', audioUrl)}\n                            onTouchEnd={handleLongPressEnd}\n                            onTouchCancel={handleLongPressEnd}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <div className=\"w-2 h-2 bg-teal-500 rounded-full\"></div>\n                              <span className=\"text-sm text-gray-700\">Audio {index + 1}</span>\n                            </div>\n\n                            {/* Delete button - Desktop hover / Mobile long press */}\n                            {consultation.status !== 'approved' && showDeleteMenu?.type === 'audio' && showDeleteMenu?.url === audioUrl && (\n                              <button\n                                onClick={() => handleDeleteAudio(audioUrl)}\n                                className=\"flex items-center space-x-1 px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-all duration-200\"\n                                title=\"Delete audio\"\n                              >\n                                <Trash2 className=\"w-3 h-3\" />\n                                <span>Delete</span>\n                              </button>\n                            )}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Images Section - Compact Layout */}\n          {consultation.image_urls && Array.isArray(consultation.image_urls) && consultation.image_urls.length > 0 && (\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3\">\n                Original Images ({consultation.image_urls.length} image{consultation.image_urls.length > 1 ? 's' : ''})\n              </h4>\n              <div className=\"flex flex-wrap gap-3\">\n                {consultation.image_urls\n                  .filter((imageUrl): imageUrl is string => typeof imageUrl === 'string' && imageUrl !== null)\n                  .map((imageUrl, index) => (\n                    <div\n                      key={index}\n                      className=\"relative group\"\n                      onMouseEnter={() => consultation.status !== 'approved' && setShowDeleteMenu({ type: 'image', url: imageUrl })}\n                      onMouseLeave={() => setShowDeleteMenu(null)}\n                      onTouchStart={() => consultation.status !== 'approved' && handleLongPressStart('image', imageUrl)}\n                      onTouchEnd={handleLongPressEnd}\n                      onTouchCancel={handleLongPressEnd}\n                    >\n                      <div className=\"w-24 h-24 bg-gray-200 rounded border overflow-hidden\">\n                        <Image\n                          src={imageUrl}\n                          alt={`Original image ${index + 1}`}\n                          className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-200 cursor-pointer\"\n                          width={96}\n                          height={96}\n                          priority={index < 2}\n                          loading={index < 2 ? 'eager' : 'lazy'}\n                          onError={(e) => {\n                            console.error('Image failed to load:', imageUrl)\n                            const target = e.currentTarget as HTMLImageElement\n                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5NiA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00OCA2NEw0MCA1Nkw0OCA0OEw1NiA1Nkw0OCA2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'\n                            target.alt = 'Failed to load image'\n                          }}\n                        />\n                      </div>\n                      <div className=\"absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded\">\n                        {index + 1}\n                      </div>\n\n                      {/* Delete button - Desktop hover / Mobile long press */}\n                      {consultation.status !== 'approved' && showDeleteMenu?.type === 'image' && showDeleteMenu?.url === imageUrl && (\n                        <button\n                          onClick={() => handleDeleteImage(imageUrl)}\n                          className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs shadow-lg transition-all duration-200 z-10\"\n                          title=\"Delete image\"\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                        </button>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {/* Additional Images Section for Nurses */}\n          <div className=\"bg-orange-50 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"text-sm font-medium text-slate-800\">\n                Additional Images\n              </h4>\n              <span className=\"text-xs text-slate-500\">Optional</span>\n            </div>\n\n            <div className=\"flex items-center space-x-3 mb-3\">\n              {/* Compact Camera button */}\n              <button\n                onClick={() => cameraInputRef.current?.click()}\n                className=\"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm\"\n              >\n                <Camera className=\"w-4 h-4 text-orange-500\" />\n                <span className=\"text-orange-700\">Camera</span>\n              </button>\n\n              {/* Compact Upload button */}\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"flex items-center space-x-2 px-3 py-2 border border-orange-300 rounded-md hover:bg-orange-100 transition-colors text-sm\"\n              >\n                <Upload className=\"w-4 h-4\" />\n                <span className=\"text-orange-700\">Upload</span>\n              </button>\n\n              {additionalImages.length > 0 && (\n                <span className=\"text-xs text-orange-600 font-medium\">\n                  +{additionalImages.length} image{additionalImages.length > 1 ? 's' : ''}\n                </span>\n              )}\n            </div>\n\n            {/* Compact image previews */}\n            {additionalImages.length > 0 && (\n              <div className=\"flex flex-wrap gap-2\">\n                {additionalImages.map((image) => (\n                  <div key={image.id} className=\"relative\">\n                    <Image\n                      src={image.preview || ''}\n                      alt=\"Additional\"\n                      className=\"w-12 h-12 object-cover rounded border hover:w-24 hover:h-24 transition-all duration-200 cursor-pointer\"\n                      width={48}\n                      height={48}\n                    />\n                    <button\n                      onClick={() => removeAdditionalImage(image.id)}\n                      className=\"absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs\"\n                    >\n                      <X className=\"w-2.5 h-2.5\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Hidden inputs */}\n            <input\n              ref={cameraInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              capture=\"environment\"\n              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}\n              className=\"hidden\"\n            />\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              multiple\n              onChange={(e) => e.target.files && handleImageUpload(e.target.files)}\n              className=\"hidden\"\n            />\n          </div>\n\n          {/* Additional Notes Section */}\n          <div className=\"bg-blue-50 rounded-xl p-3 sm:p-5 border border-blue-100\">\n            <h4 className=\"text-base sm:text-lg font-semibold text-slate-800 mb-3 sm:mb-4 flex items-center\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2 sm:mr-3\"></div>\n              Additional Notes (Optional)\n            </h4>\n            <textarea\n              value={additionalNotes}\n              onChange={(e) => setAdditionalNotes(e.target.value)}\n              placeholder=\"Add any additional context or instructions for the AI...\"\n              rows={3}\n              className=\"w-full px-3 sm:px-4 py-2 sm:py-3 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm bg-white\"\n              disabled={consultation.status === 'approved'}\n            />\n            <p className=\"text-xs text-blue-600 mt-2\">\n              These notes will be included in the AI analysis along with the audio recordings.\n            </p>\n          </div>\n\n          {/* Summary Section */}\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mr-3\"></div>\n                AI-Generated Patient Summary\n              </h4>\n              <div className=\"flex flex-wrap items-center gap-2\">\n                {(consultation.status === 'pending' || consultation.status === 'generated') && (\n                  <button\n                    onClick={handleGenerateSummary}\n                    disabled={isGenerating}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-purple-300 disabled:to-purple-400 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    <Wand2 className=\"w-4 h-4\" />\n                    <span className=\"hidden sm:inline\">{isGenerating ? 'Generating...' : consultation.status === 'generated' ? 'Regenerate Summary' : 'Generate Summary'}</span>\n                    <span className=\"sm:hidden\">{isGenerating ? 'Gen...' : 'Generate'}</span>\n                  </button>\n                )}\n\n                {editedNote && (\n                  <button\n                    onClick={handleCopyToClipboard}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    <Copy className=\"w-4 h-4\" />\n                    <span>Copy</span>\n                  </button>\n                )}\n              </div>\n            </div>\n\n            <textarea\n              value={isGenerating ? streamingText : editedNote}\n              onChange={(e) => setEditedNote(e.target.value)}\n              placeholder={\n                consultation.status === 'pending'\n                  ? 'Click \"Generate Summary\" to create an AI-powered patient summary ...'\n                  : 'Edit the patient summary as needed...'\n              }\n              className=\"w-full min-h-[400px] max-h-[600px] p-4 border border-gray-200 rounded-lg resize-y focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white text-black text-sm font-medium flex-1\"\n              style={{\n                height: (isGenerating ? streamingText : editedNote) ? `${Math.max(400, Math.min(600, (isGenerating ? streamingText : editedNote).split('\\n').length * 24 + 80))}px` : '400px'\n              }}\n            />\n            {isGenerating && (\n              <div className=\"text-xs text-purple-600 mt-2 flex items-center\">\n                <span className=\"inline-block w-2 h-2 bg-purple-500 animate-pulse mr-2 rounded-full\"></span>\n                Streaming response...\n              </div>\n            )}\n          </div>\n\n          {/* Status Messages */}\n          {success && (\n            <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <p className=\"text-sm text-green-700 font-medium\">{success}</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Actions Footer */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 px-4 sm:px-6 py-4 border-t border-orange-200/50 flex-shrink-0\">\n          <div className=\"flex flex-col space-y-4\">\n            {/* Status Info */}\n            <div className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4\">\n              <div className={`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center w-fit ${\n                consultation.status === 'pending' ? 'bg-orange-100 text-orange-800 border border-orange-300' :\n                consultation.status === 'generated' ? 'bg-emerald-100 text-emerald-800 border border-emerald-300' :\n                'bg-green-100 text-green-800 border border-green-300'\n              }`}>\n                {consultation.status.toUpperCase()}\n              </div>\n              <span className=\"text-xs sm:text-sm text-slate-600\">\n                Last updated: {formatDate(consultation.updated_at)}\n              </span>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"w-full sm:w-auto px-6 py-3 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-xl text-slate-700 bg-white hover:bg-orange-50 transition-all duration-200 text-center\"\n              >\n                Close\n              </button>\n\n              {consultation.status !== 'approved' && editedNote.trim() && (\n                <button\n                  onClick={handleApprove}\n                  disabled={isApproving}\n                  className=\"w-full sm:w-auto flex items-center justify-center space-x-2 px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 disabled:from-teal-300 disabled:to-emerald-400 transition-all duration-200 shadow-lg hover:shadow-xl\"\n                >\n                  <Save className=\"w-4 h-4\" />\n                  <span>{isApproving ? 'Approving...' : 'Approve & Save'}</span>\n                </button>\n              )}\n\n              {consultation.status !== 'approved' && !editedNote.trim() && (\n                <div className=\"w-full sm:w-auto px-6 py-3 text-sm text-center text-gray-500 bg-gray-100 rounded-xl border border-gray-200\">\n                  Generate or add summary to approve\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  // Don't render anything on server-side\n  if (!mounted) {\n    return null\n  }\n\n  // Use portal to render modal at document body level\n  return createPortal(modalContent, document.body)\n}\n", "'use client'\n\nimport { useState, useEffect, useRef, useCallback } from 'react'\nimport { Clock, FileText, CheckCircle, Eye, Wand2, Filter } from 'lucide-react'\nimport { Consultation } from '@/lib/types'\nimport { formatDate, formatRelativeTime } from '@/lib/utils'\nimport { ConsultationModal } from './consultation-modal'\nimport { getConsultations } from '@/lib/actions/consultations'\n\ninterface ConsultationsListProps {\n  consultations: Consultation[]\n  hasMore?: boolean\n  doctorName?: string\n}\n\nexport function ConsultationsList({ consultations: initialConsultations, hasMore: initialHasMore = false, doctorName }: ConsultationsListProps) {\n  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)\n  const [filter, setFilter] = useState<'all' | 'pending' | 'generated' | 'approved'>('all')\n  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'yesterday' | 'custom'>('all')\n  const [customDate, setCustomDate] = useState<string>('')\n\n  // Infinite scroll state\n  const [consultations, setConsultations] = useState<Consultation[]>(initialConsultations)\n  const [hasMore, setHasMore] = useState(initialHasMore)\n  const [isLoading, setIsLoading] = useState(false)\n  const [currentPage, setCurrentPage] = useState(1)\n\n  // Refs for intersection observer\n  const loadMoreRef = useRef<HTMLDivElement>(null)\n  const observerRef = useRef<IntersectionObserver | null>(null)\n\n  // Update consultations when props change\n  useEffect(() => {\n    setConsultations(initialConsultations)\n    setHasMore(initialHasMore)\n    setCurrentPage(1)\n  }, [initialConsultations, initialHasMore])\n\n  // Load more consultations for infinite scroll\n  const loadMoreConsultations = useCallback(async () => {\n    if (isLoading || !hasMore) {\n      console.log('Skipping load more - isLoading:', isLoading, 'hasMore:', hasMore)\n      return\n    }\n\n    console.log('Loading more consultations - page:', currentPage + 1)\n    setIsLoading(true)\n    try {\n      const result = await getConsultations({\n        page: currentPage + 1,\n        pageSize: 15,\n        status: filter === 'all' ? undefined : filter\n      })\n\n      if (result.success && result.data.consultations.length > 0) {\n        setConsultations(prev => [...prev, ...result.data.consultations])\n        setHasMore(result.data.hasMore)\n        setCurrentPage(prev => prev + 1)\n        console.log('Loaded', result.data.consultations.length, 'more consultations')\n      } else {\n        setHasMore(false)\n        console.log('No more consultations to load')\n      }\n    } catch (error) {\n      console.error('Failed to load more consultations:', error)\n      setHasMore(false)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [currentPage, hasMore, isLoading, filter])\n\n  // Set up intersection observer for infinite scroll\n  useEffect(() => {\n    if (!loadMoreRef.current || !hasMore || isLoading) {\n      if (observerRef.current) {\n        observerRef.current.disconnect()\n        observerRef.current = null\n      }\n      return\n    }\n\n    if (observerRef.current) {\n      observerRef.current.disconnect()\n    }\n\n    observerRef.current = new IntersectionObserver(\n      (entries) => {\n        if (entries[0].isIntersecting && !isLoading && hasMore && consultations.length > 0) {\n          console.log('Info page intersection observer triggered - loading more')\n          loadMoreConsultations()\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    observerRef.current.observe(loadMoreRef.current)\n\n    return () => {\n      if (observerRef.current) {\n        observerRef.current.disconnect()\n        observerRef.current = null\n      }\n    }\n  }, [loadMoreConsultations, hasMore, isLoading, consultations.length])\n\n  // Handle consultation updates from modal\n  const handleConsultationUpdate = (updatedConsultation: Consultation) => {\n    setSelectedConsultation(updatedConsultation)\n    // Also update the consultation in the list\n    setConsultations(prev =>\n      prev.map(c => c.id === updatedConsultation.id ? updatedConsultation : c)\n    )\n  }\n\n  const getDateFilteredConsultations = (consultations: Consultation[]) => {\n    if (dateFilter === 'all') return consultations\n    \n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n    \n    return consultations.filter(consultation => {\n      const consultationDate = new Date(consultation.created_at)\n      \n      switch (dateFilter) {\n        case 'today':\n          return consultationDate.toDateString() === today.toDateString()\n        case 'yesterday':\n          return consultationDate.toDateString() === yesterday.toDateString()\n        case 'custom':\n          if (!customDate) return true\n          const selectedDate = new Date(customDate)\n          return consultationDate.toDateString() === selectedDate.toDateString()\n        default:\n          return true\n      }\n    })\n  }\n\n  const filteredConsultations = getDateFilteredConsultations(consultations).filter(consultation => {\n    if (filter === 'all') return true\n    return consultation.status === filter\n  })\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"w-4 h-4 text-orange-600\" />\n      case 'generated':\n        return <FileText className=\"w-4 h-4 text-emerald-600\" />\n      case 'approved':\n        return <CheckCircle className=\"w-4 h-4 text-green-600\" />\n      default:\n        return <Clock className=\"w-4 h-4 text-slate-500\" />\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'Pending Review'\n      case 'generated':\n        return 'Summary Generated'\n      case 'approved':\n        return 'Approved'\n      default:\n        return 'Unknown'\n    }\n  }\n\n  const getStatusBadgeClass = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-orange-100 text-orange-800 border border-orange-300'\n      case 'generated':\n        return 'bg-emerald-100 text-emerald-800 border border-emerald-300'\n      case 'approved':\n        return 'bg-green-100 text-green-800 border border-green-300'\n      default:\n        return 'bg-slate-100 text-slate-800 border border-slate-300'\n    }\n  }\n\n  const tabKeys: Array<'all' | 'pending' | 'generated' | 'approved'> = ['all', 'pending', 'generated', 'approved']\n\n  if (consultations.length === 0) {\n    return (\n      <div className=\"p-6 text-center\">\n        <FileText className=\"mx-auto h-12 w-12 text-slate-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-slate-800\">No consultations</h3>\n        <p className=\"mt-1 text-sm text-slate-600\">\n          Get started by recording a consultation on the mobile interface.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"p-6\">\n        {/* Filter Tabs with Date Filter on Right */}\n        <div className=\"border-b border-orange-200 mb-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n            {/* Filter Tabs */}\n            <nav className=\"-mb-px flex space-x-2 sm:space-x-8 overflow-x-auto\">\n              {tabKeys.map((key) => (\n                <button\n                  key={key}\n                  onClick={() => setFilter(key)}\n                  className={`whitespace-nowrap py-2 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-200 flex-shrink-0 ${\n                    filter === key\n                      ? 'border-teal-500 text-teal-600'\n                      : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-orange-300'\n                  }`}\n                >\n                  {key.charAt(0).toUpperCase() + key.slice(1)}\n                  {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length > 0 && (\n                    <span className={`ml-1 sm:ml-2 py-0.5 px-1 sm:px-2 rounded-full text-xs ${\n                      filter === key\n                        ? 'bg-teal-100 text-teal-700'\n                        : 'bg-orange-100 text-slate-800'\n                    }`}>\n                      {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length}\n                    </span>\n                  )}\n                </button>\n              ))}\n            </nav>\n\n            {/* Date Filter on Right */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n              <Filter className=\"w-4 h-4 text-slate-600\" />\n              <select\n                value={dateFilter}\n                onChange={(e) => {\n                  const value = e.target.value as 'all' | 'today' | 'yesterday' | 'custom'\n                  setDateFilter(value)\n                  if (value !== 'custom') {\n                    setCustomDate('')\n                  }\n                }}\n                className=\"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n              >\n                <option value=\"all\">All Dates</option>\n                <option value=\"today\">Today</option>\n                <option value=\"yesterday\">Yesterday</option>\n                <option value=\"custom\">Custom Date</option>\n              </select>\n              \n              {dateFilter === 'custom' && (\n                <input\n                  type=\"date\"\n                  value={customDate}\n                  onChange={(e) => setCustomDate(e.target.value)}\n                  className=\"text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                />\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Consultations List */}\n        <div className=\"space-y-4\">\n          {filteredConsultations.map((consultation) => (\n            <div\n              key={consultation.id}\n              className=\"bg-white/80 backdrop-blur-sm border border-orange-200/50 rounded-lg p-3 sm:p-4 hover:shadow-lg hover:bg-white/90 transition-all duration-200 hover:scale-[1.02]\"\n            >\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0\">\n                <div className=\"flex items-start sm:items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n                  <div className=\"flex-shrink-0 mt-0.5 sm:mt-0\">\n                    {getStatusIcon(consultation.status)}\n                  </div>\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2\">\n                      <h3 className=\"text-sm font-medium text-slate-800 truncate\">\n                        {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}\n                      </h3>\n                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start ${getStatusBadgeClass(consultation.status)}`}>\n                        {getStatusText(consultation.status)}\n                      </span>\n                    </div>\n\n                    <div className=\"mt-1 flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-slate-600\">\n                      <span className=\"truncate\">Submitted by: {consultation.submitted_by}</span>\n                      <span className=\"hidden sm:inline\">•</span>\n                      <span>{formatRelativeTime(consultation.created_at)}</span>\n                      <span className=\"hidden sm:inline\">•</span>\n                      <span className=\"hidden sm:inline\">{formatDate(consultation.created_at)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                  {/* Primary Action Button - Different for each status */}\n                  <button\n                    onClick={() => setSelectedConsultation(consultation)}\n                    className={`inline-flex items-center px-2 sm:px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-md hover:shadow-lg transition-all duration-200 ${\n                      consultation.status === 'pending'\n                        ? 'border-transparent text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 focus:ring-teal-500'\n                        : consultation.status === 'generated'\n                        ? 'border-transparent text-white bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 focus:ring-blue-500'\n                        : 'border-transparent text-white bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 focus:ring-green-500'\n                    }`}\n                  >\n                    {consultation.status === 'pending' ? (\n                      <>\n                        <Wand2 className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">Generate Summary</span>\n                        <span className=\"sm:hidden\">Generate</span>\n                      </>\n                    ) : consultation.status === 'generated' ? (\n                      <>\n                        <Eye className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">Review Summary</span>\n                        <span className=\"sm:hidden\">Review</span>\n                      </>\n                    ) : (\n                      <>\n                        <Eye className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">View Approved</span>\n                        <span className=\"sm:hidden\">View</span>\n                      </>\n                    )}\n                  </button>\n\n                  {/* Secondary Action Button - View Details (consistent for all) */}\n                  <button\n                    onClick={() => setSelectedConsultation(consultation)}\n                    className=\"inline-flex items-center px-2 sm:px-3 py-1.5 border border-orange-300 hover:border-teal-400 text-xs font-medium rounded text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200\"\n                  >\n                    <Eye className=\"w-3 h-3 mr-1\" />\n                    <span className=\"hidden sm:inline\">View Details</span>\n                    <span className=\"sm:hidden\">Details</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Infinite scroll trigger */}\n        {hasMore && (\n          <div ref={loadMoreRef} className=\"flex justify-center py-4\">\n            {isLoading ? (\n              <div className=\"flex items-center space-x-2 text-sm text-slate-600\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500\"></div>\n                <span>Loading more consultations...</span>\n              </div>\n            ) : (\n              <div className=\"text-sm text-slate-500\">Scroll to load more</div>\n            )}\n          </div>\n        )}\n\n        {filteredConsultations.length === 0 && filter !== 'all' && (\n          <div className=\"text-center py-8\">\n            <p className=\"text-sm text-slate-600\">\n              No consultations with status &quot;{filter}&quot;.\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Consultation Modal */}\n      {selectedConsultation && (\n        <ConsultationModal\n          consultation={selectedConsultation}\n          onClose={() => setSelectedConsultation(null)}\n          onConsultationUpdate={handleConsultationUpdate}\n          doctorName={doctorName}\n        />\n      )}\n    </>\n  )\n}", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n", "'use client'\n\nimport { X, AlertTriangle, Phone, MessageCircle, Clock, CheckCircle } from 'lucide-react'\nimport { QuotaInfo } from '@/lib/types'\n\ninterface QuotaWarningModalProps {\n  isOpen: boolean\n  onClose: () => void\n  quotaInfo: QuotaInfo\n  onContactFounder: () => void\n  isRequesting?: boolean\n  hasRequested?: boolean\n}\n\nexport function QuotaWarningModal({ \n  isOpen, \n  onClose, \n  quotaInfo, \n  onContactFounder, \n  isRequesting = false,\n  hasRequested = false \n}: QuotaWarningModalProps) {\n  if (!isOpen) return null\n\n  const isOverLimit = quotaInfo.quota_percentage >= 95\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden\">\n        {/* Header */}\n        <div className={`p-6 text-white relative ${\n          isOverLimit \n            ? 'bg-gradient-to-r from-red-500 to-red-600' \n            : 'bg-gradient-to-r from-orange-500 to-amber-600'\n        }`}>\n          <button\n            onClick={onClose}\n            className=\"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n          \n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <AlertTriangle className=\"w-8 h-8 text-white\" />\n            </div>\n            <h2 className=\"text-2xl font-bold mb-2\">\n              {isOverLimit ? 'Quota Limit Reached!' : 'Quota Warning'}\n            </h2>\n            <p className=\"text-orange-100\">\n              {isOverLimit \n                ? 'You have reached your consultation limit' \n                : 'You are approaching your consultation limit'\n              }\n            </p>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Quota Status */}\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 p-4 rounded-lg border border-gray-200 mb-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-gray-900 mb-1\">\n                {quotaInfo.quota_used} / {quotaInfo.monthly_quota}\n              </div>\n              <div className=\"text-sm text-gray-600 mb-3\">Consultations Used</div>\n              \n              <div className=\"w-full bg-gray-200 rounded-full h-3 mb-2\">\n                <div\n                  className={`h-3 rounded-full transition-all duration-300 ${\n                    isOverLimit ? 'bg-red-500' : 'bg-orange-500'\n                  }`}\n                  style={{ width: `${Math.min(quotaInfo.quota_percentage, 100)}%` }}\n                />\n              </div>\n              \n              <div className={`text-sm font-medium ${\n                isOverLimit ? 'text-red-600' : 'text-orange-600'\n              }`}>\n                {quotaInfo.quota_percentage.toFixed(0)}% Used\n              </div>\n            </div>\n          </div>\n\n          {/* Message */}\n          <div className=\"text-center mb-6\">\n            <p className=\"text-gray-700 leading-relaxed mb-4\">\n              {isOverLimit \n                ? 'To continue using AI consultations, please contact our founder to upgrade your plan or increase your quota.'\n                : 'You have only a few consultations remaining. Consider contacting our founder to discuss upgrading your plan.'\n              }\n            </p>\n            \n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center space-x-2 mb-2\">\n                <Phone className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm font-medium text-blue-800\">Contact Founder</span>\n              </div>\n              <div className=\"text-lg font-bold text-blue-900 mb-1\">\n                +91 8921628177\n              </div>\n              <p className=\"text-xs text-blue-700\">\n                Available Mon-Sat, 9 AM - 8 PM IST\n              </p>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"space-y-3\">\n            {hasRequested ? (\n              <div className=\"flex items-center justify-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg\">\n                <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                <span className=\"text-green-800 font-medium\">Request Sent Successfully!</span>\n              </div>\n            ) : (\n              <button\n                onClick={onContactFounder}\n                disabled={isRequesting}\n                className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2\"\n              >\n                {isRequesting ? (\n                  <>\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span>Sending Request...</span>\n                  </>\n                ) : (\n                  <>\n                    <MessageCircle className=\"w-4 h-4\" />\n                    <span>Request Callback</span>\n                  </>\n                )}\n              </button>\n            )}\n\n            <a\n              href=\"tel:+918921628177\"\n              className=\"w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2\"\n            >\n              <Phone className=\"w-4 h-4\" />\n              <span>Call Now</span>\n            </a>\n\n            {!isOverLimit && (\n              <button\n                onClick={onClose}\n                className=\"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-4 rounded-lg transition-all duration-200\"\n              >\n                Continue with Trial\n              </button>\n            )}\n          </div>\n\n          {/* Reset Info */}\n          <div className=\"mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200\">\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <Clock className=\"w-4 h-4\" />\n              <span>\n                Quota resets in {quotaInfo.days_until_reset} day{quotaInfo.days_until_reset !== 1 ? 's' : ''}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}", "/* __next_internal_action_entry_do_not_use__ [{\"405d7138432cf8f813249d6a1a52fcfca61c62a7ed\":\"hasActiveContactRequest\"},\"src/lib/actions/contact-requests.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var hasActiveContactRequest=/*#__PURE__*/createServerReference(\"405d7138432cf8f813249d6a1a52fcfca61c62a7ed\",callServer,void 0,findSourceMapURL,\"hasActiveContactRequest\");", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Zap, Calendar, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react'\nimport { QuotaInfo } from '@/lib/types'\nimport { QuotaWarningModal } from './quota-warning-modal'\nimport { createContactRequest, hasActiveContactRequest } from '@/lib/actions/contact-requests'\n\ninterface QuotaCardProps {\n  quota: QuotaInfo\n  doctorId: string\n}\n\nexport function QuotaCard({ quota, doctorId }: QuotaCardProps) {\n  const [showWarningModal, setShowWarningModal] = useState(false)\n  const [isRequesting, setIsRequesting] = useState(false)\n  const [hasRequested, setHasRequested] = useState(false)\n\n  useEffect(() => {\n    // Show warning modal when approaching or exceeding limits\n    if (quota.quota_percentage >= 80) {\n      setShowWarningModal(true)\n    }\n\n    // Check if user has already made a contact request\n    const checkContactRequest = async () => {\n      const result = await hasActiveContactRequest(doctorId)\n      if (result.success && result.data) {\n        setHasRequested(true)\n      }\n    }\n    checkContactRequest()\n  }, [quota.quota_percentage, doctorId])\n\n  const handleContactFounder = async () => {\n    setIsRequesting(true)\n    try {\n      const result = await createContactRequest(doctorId, `Quota upgrade request - Currently at ${quota.quota_percentage}% (${quota.quota_used}/${quota.monthly_quota} consultations used)`)\n      if (result.success) {\n        setHasRequested(true)\n      }\n    } catch (error) {\n      console.error('Error creating contact request:', error)\n    } finally {\n      setIsRequesting(false)\n    }\n  }\n\n  const getQuotaColor = () => {\n    if (quota.quota_percentage >= 90) return 'text-red-600'\n    if (quota.quota_percentage >= 70) return 'text-orange-600'\n    return 'text-emerald-600'\n  }\n\n  const getQuotaBgColor = () => {\n    if (quota.quota_percentage >= 90) return 'bg-red-100'\n    if (quota.quota_percentage >= 70) return 'bg-orange-100'\n    return 'bg-emerald-100'\n  }\n\n  const getQuotaIcon = () => {\n    if (quota.quota_percentage >= 90) return AlertTriangle\n    if (quota.quota_percentage >= 70) return Zap\n    return CheckCircle\n  }\n\n  const getProgressBarColor = () => {\n    if (quota.quota_percentage >= 90) return 'bg-red-500'\n    if (quota.quota_percentage >= 70) return 'bg-orange-500'\n    return 'bg-emerald-500'\n  }\n\n  const QuotaIcon = getQuotaIcon()\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-sm font-medium text-slate-800\">Consultations</h3>\n        <div className={`w-6 h-6 rounded-lg ${getQuotaBgColor()} flex items-center justify-center shadow-md`}>\n          <QuotaIcon className={`w-4 h-4 ${getQuotaColor()}`} />\n        </div>\n      </div>\n\n      {/* Main Quota Display */}\n      <div className=\"mb-4 flex-1 space-y-3\">\n        <div className=\"text-center bg-gradient-to-br from-slate-50 to-orange-50 rounded-lg p-3 sm:p-4\">\n          <div className=\"text-xs sm:text-sm text-slate-600 mb-1\">Monthly Usage</div>\n          <div className=\"text-base sm:text-lg font-bold text-slate-800\">\n            {quota.quota_used} / {quota.monthly_quota}\n          </div>\n          <div className=\"w-full bg-orange-100 rounded-full h-2 sm:h-2.5 mt-2\">\n            <div\n              className={`h-2 sm:h-2.5 rounded-full transition-all duration-300 ${getProgressBarColor()}`}\n              style={{ width: `${Math.min(quota.quota_percentage, 100)}%` }}\n            />\n          </div>\n          <div className=\"mt-1\">\n            <span className={`text-sm sm:text-base font-bold ${getQuotaColor()}`}>\n              {quota.quota_percentage}%\n            </span>\n          </div>\n        </div>\n\n        {/* Split Info Grid */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-2 sm:p-3 text-center\">\n            <TrendingUp className=\"w-4 h-4 sm:w-5 sm:h-5 text-teal-600 mx-auto mb-1\" />\n            <div className=\"text-xs sm:text-sm text-slate-600\">Remaining</div>\n            <div className=\"text-sm sm:text-base font-bold text-slate-800\">{quota.quota_remaining}</div>\n          </div>\n          \n          <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-2 sm:p-3 text-center\">\n            <Calendar className=\"w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mx-auto mb-1\" />\n            <div className=\"text-xs sm:text-sm text-slate-600\">Resets in</div>\n            <div className=\"text-sm sm:text-base font-bold text-slate-800\">{quota.days_until_reset} day{quota.days_until_reset !== 1 ? 's' : ''}</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Warning Messages */}\n      {quota.quota_percentage >= 90 && (\n        <div className=\"p-2 bg-red-50 border border-red-200 rounded\">\n          <div className=\"flex items-start\">\n            <AlertTriangle className=\"w-3 h-3 text-red-500 mt-0.5 mr-1 flex-shrink-0\" />\n            <div className=\"text-xs text-red-700\">\n              <p className=\"font-medium\">Quota almost exhausted!</p>\n              <p>Contact admin to increase limit.</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {quota.quota_percentage >= 70 && quota.quota_percentage < 90 && (\n        <div className=\"p-2 bg-orange-50 border border-orange-200 rounded\">\n          <div className=\"flex items-start\">\n            <Zap className=\"w-3 h-3 text-orange-500 mt-0.5 mr-1 flex-shrink-0\" />\n            <div className=\"text-xs text-orange-700\">\n              <p className=\"font-medium\">High usage detected</p>\n              <p>Monitor AI generation usage.</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quota Warning Modal */}\n      <QuotaWarningModal\n        isOpen={showWarningModal}\n        onClose={() => setShowWarningModal(false)}\n        quotaInfo={quota}\n        onContactFounder={handleContactFounder}\n        isRequesting={isRequesting}\n        hasRequested={hasRequested}\n      />\n    </div>\n  )\n}", "/* __next_internal_action_entry_do_not_use__ [{\"400d27483b0ad440768404c34690724d71663b6083\":\"generateReferralLink\"},\"src/lib/actions/referrals.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var generateReferralLink=/*#__PURE__*/createServerReference(\"400d27483b0ad440768404c34690724d71663b6083\",callServer,void 0,findSourceMapURL,\"generateReferralLink\");", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Users, Gift, Crown, Copy, Check } from 'lucide-react'\nimport { ReferralInfo } from '@/lib/types'\nimport { getReferralInfo, generateReferralLink } from '@/lib/actions/referrals'\n\ninterface ReferralStatsProps {\n  doctorId: string\n}\n\nexport function ReferralStats({ doctorId }: ReferralStatsProps) {\n  const [referralInfo, setReferralInfo] = useState<ReferralInfo | null>(null)\n  const [referralLink, setReferralLink] = useState<string>('')\n  const [copied, setCopied] = useState(false)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      const [infoResult, linkResult] = await Promise.all([\n        getReferralInfo(doctorId),\n        generateReferralLink(doctorId)\n      ])\n\n      if (infoResult.success) {\n        setReferralInfo(infoResult.data)\n      }\n      \n      if (linkResult.success) {\n        setReferralLink(linkResult.data)\n      }\n      \n      setLoading(false)\n    }\n\n    fetchData()\n  }, [doctorId])\n\n  const handleCopyLink = async () => {\n    if (referralLink) {\n      await navigator.clipboard.writeText(referralLink)\n      setCopied(true)\n      setTimeout(() => setCopied(false), 2000)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm shadow border border-orange-200/50 rounded-lg p-4 h-full\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-1/3\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!referralInfo) {\n    return null\n  }\n\n  const getBadgeIcon = () => {\n    if (referralInfo.successful_referrals >= 3) {\n      return <Crown className=\"w-5 h-5 text-amber-500\" />\n    }\n    return <Gift className=\"w-5 h-5 text-emerald-500\" />\n  }\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg p-4 border border-orange-200/50 hover:shadow-xl transition-all duration-300 h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          {getBadgeIcon()}\n          <h3 className=\"text-sm font-medium text-slate-800\">Referral Program</h3>\n        </div>\n        <div className=\"w-6 h-6 rounded-lg bg-emerald-100 flex items-center justify-center shadow-md\">\n          {getBadgeIcon()}\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"flex-1 space-y-3\">\n        {/* Main Stats Grid */}\n        <div className=\"grid grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-emerald-50 to-green-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-emerald-700 font-medium mb-1\">Success</div>\n            <div className=\"text-lg font-bold text-emerald-800\">{referralInfo.successful_referrals}</div>\n          </div>\n          <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-purple-700 font-medium mb-1\">Earned</div>\n            <div className=\"text-sm font-bold text-purple-800\">₹{referralInfo.discount_earned.toFixed(0)}</div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-2\">\n          <div className=\"bg-gradient-to-br from-blue-50 to-cyan-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-blue-700 font-medium mb-1\">Total</div>\n            <div className=\"text-lg font-bold text-blue-800\">{referralInfo.total_referrals}</div>\n          </div>\n          <div className=\"bg-gradient-to-br from-amber-50 to-yellow-50 p-3 rounded-lg text-center\">\n            <div className=\"text-xs text-amber-700 font-medium mb-1\">Pending</div>\n            <div className=\"text-lg font-bold text-amber-800\">{referralInfo.pending_referrals}</div>\n          </div>\n        </div>\n\n        {/* Referral Link */}\n        <div className=\"bg-gradient-to-r from-slate-50 to-gray-50 p-3 rounded-lg\">\n          <div className=\"text-xs text-slate-600 mb-2 text-center\">Your Referral Link</div>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"text\"\n              value={referralLink}\n              readOnly\n              className=\"flex-1 px-2 py-1 rounded text-xs bg-white/70 text-slate-800 focus:outline-none\"\n            />\n            <button\n              onClick={handleCopyLink}\n              className=\"px-3 py-1 bg-teal-600 hover:bg-teal-700 text-white rounded text-xs transition-colors font-medium\"\n            >\n              {copied ? <Check className=\"w-3 h-3\" /> : <Copy className=\"w-3 h-3\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Referred By Section */}\n        {referralInfo.referred_by && (\n          <div className=\"p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg\">\n            <div className=\"flex items-center justify-center space-x-2\">\n              <Users className=\"w-3 h-3 text-blue-600\" />\n              <span className=\"text-xs text-blue-700\">\n                Referred by <span className=\"font-medium\">{referralInfo.referred_by.name}</span>\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": "wHAGO,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAA6B,CAClC,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAFgC,CAGhC,AAHgC,CAAA,AAI9B,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACF,CAaM,EAAS,CAAA,EAAA,CAAT,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8LC4rB7C,IAAM,EAA6D,CACxE,WAAY,0BACZ,UAAW,oBACX,QAAS,iBACT,UAAW,mBACX,YAAa,mBACb,gBAAiB,wBACjB,UAAW,oBACX,UAAW,uBACb,EAiBa,EAAY,CACvB,OAAQ,CACN,MAAO,CAAC,CACV,CACF,oGChvBA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QASO,SAAS,EAAkB,CAAE,cAAY,SAAE,CAAO,sBAAE,CAAoB,YAAE,CAAU,CAA0B,EACnH,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,GAC3C,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADP,EAElC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,EAAa,AADpB,WAC+B,EAAI,EAAa,UAAlD,OAAmE,EAAI,IACrG,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAA2B,MACpE,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EADX,CACa,GAG/C,EAAkB,CAAA,EAAA,EAAA,MAAA,AAAK,EAAuB,MAC9C,EAAiB,AAJqB,CAIrB,EAAA,EAAA,MAAA,AAAK,EAAgC,MACtD,CAAC,CAFiB,CAER,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAU,EADxB,EAEjB,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAwD,EADjF,AACmF,EAC3G,CAAC,EAAuB,EAAyB,CAAG,GAAA,EAAA,QAAA,AAAO,GAAE,CADnB,EAE1C,CAAC,EAAqB,EAAuB,CAAG,GAAA,EAAA,QAAA,AAAO,EAAe,EADlB,IAEpD,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAwB,AADnB,MAGhD,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAFP,AAES,IAC7C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADG,EAIpC,CAAC,GAAkB,GAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAHzB,AAI3B,EAAa,iBAAiB,EAAyB,MADV,QAG1C,CAAC,GAAiB,GAAmB,CAAG,GAAA,EAAA,QAAA,AAAO,EAAE,EAAa,gBAAgB,EAAI,IAGlF,CAAC,EAHuC,CAGvB,GAAkB,CAAG,GAAA,EAAA,QAAA,AAAO,EAAyB,MACtE,CAAC,GAAgB,GAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,AADP,EAC0D,MAEhG,GAAe,CAAA,EAAA,EAAA,MAAA,AAAK,EAA2B,KAFT,CAGtC,GAAiB,CAAA,EAAA,EAAA,MAAK,AAAL,EAAgC,MACjD,CAFe,EAEK,CAAA,EAAA,EAAA,MAAA,AAAK,EAAyB,MAGlD,CAJiB,EAIO,CAAC,EAAiB,EAAU,GAAI,IAExD,GAAkB,KALE,EAKK,EAAE,AAC7B,aAAa,GAAkB,OAAO,EAGxC,EAAW,GAGP,IACF,GAAkB,EADP,KACc,CAAG,WAAW,KACrC,EAAW,IACX,GAAkB,OAAO,CAAG,IAC9B,EAAG,EAAA,CAEP,EAGA,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KACR,GAAW,EACb,EAAG,EAAE,EAGL,CAAA,EAAA,EAAA,KALA,IAKA,AAAQ,EAAE,KACR,EAAW,IACP,GAAkB,OAAO,EAAE,CAC7B,EAHJ,WAGiB,GAAkB,OAAO,EACtC,GAAkB,OAAO,CAAG,KAEhC,EAAG,CAAC,EAAa,EAAE,CAAC,EAGpB,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,IACD,KACD,IACF,YADkB,CAFxB,AAGmB,GAEjB,EACC,CAAC,GAAe,EAGnB,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KACR,EAAc,EAAa,WAAW,EAAI,EAAa,EADzD,eAC0E,EAAI,GAC9E,EAAG,CAAC,EAAa,WAAW,CAAE,EAAa,iBAAiB,CAAC,EAG7D,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KACR,GAAmB,EAAa,gBADlC,AACkD,EAAI,GACtD,EAAG,CAAC,EAAa,gBAAgB,CAAC,EAGlC,IAAM,GAAoB,MAAO,IAC/B,GAAI,CACF,EAAW,uBAEX,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAAK,CACrC,IAAM,EAAO,CAAK,CAAC,EAAE,CAErB,GAAI,CAAC,EAAK,IAAI,CAAC,UAAU,CAAC,WACtB,EAAK,IAAI,CAAG,KAAK,IADgB,GACT,MAAM,AAGlC,GAAM,OAHqC,KAGnC,CAAU,CAAE,CAAG,KAHiC,CAGjC,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACjB,EAAe,MAAM,EAAW,EAAM,EAAa,SAAS,EAAI,GAAI,EAAa,EAAE,CAAE,SAE3F,IAAI,EAAa,OAAO,GAAI,EAAa,GAAG,CAOrC,YACL,EAAW,CAAC,iBAAiB,EAAE,EAAK,IAAI,CAAC,EAAE,EAAE,EAAa,KAAK,CAAA,CAAE,CARrB,EAC5C,IAAM,EAAU,IAAI,eAAe,CAAC,GACpC,EAAoB,GAAQ,IAAI,EAAM,CACpC,GAAI,CAAA,EAAG,KAAK,GAAG,GAAG,CAAC,EAAE,EAAA,CAAG,CACxB,IAAK,EAAa,GAAG,SACrB,CACF,EAAE,CACJ,CAIF,CAEA,GAAsB,gCACxB,CAAE,MAAO,EAAQ,CACf,EAAW,0BACb,CACF,EAGM,GAAwB,AAAC,IAC7B,EAAoB,IAClB,IAAM,EAAgB,EAAK,IAAI,CAAC,GAAO,EAAI,EAAE,GAAK,GAIlD,OAHI,GAAiB,EAAc,OAAO,EAAqC,UAAU,AAA3C,OAAO,EAAc,OAAO,EACxE,IAAI,eAAe,CAAC,EAAc,OAAO,EAEpC,EAAK,MAAM,CAAC,GAAO,EAAI,EAAE,GAAK,EACvC,EACF,EAEM,GAAwB,UAC5B,GAAgB,GAChB,EAAW,IACX,EAAiB,IAEjB,GAAI,CAEF,IAAM,EAAY,IACZ,MAAM,OAAO,CAAC,EAAa,UAAU,EAAI,EAAa,UAAU,CAAG,EAAE,IACtE,EAAiB,GAAG,CAAC,GAAO,EAAI,GAAG,EACvC,CAAC,MAAM,CAAC,AAAC,GAAuB,AAAe,iBAAR,GAA4B,AAAR,UAG5D,GAAI,EAAiB,MAAM,CAAG,EAAG,CAC/B,GAAM,0BAAE,CAAwB,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,CACrC,OAAM,EAAyB,EAAa,EAAE,CAAE,EAClD,CAGA,GAAI,CACA,IAAM,EAAW,MAAM,MAAM,+BAAgC,CAC3D,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,kBAAmB,EAAa,iBAAiB,CACjD,sBAAuB,MAAM,OAAO,CAAC,EAAa,qBAAqB,EAAI,EAAa,qBAAqB,CAAG,EAAE,CAClH,WAAY,IACN,MAAM,OAAO,CAAC,EAAa,UAAU,EAAI,EAAa,UAAU,CAAG,EAAE,IACtE,EACJ,CAED,aAAc,EAAa,YAAY,EAAI,SAC3C,kBAAmB,GACnB,aAAc,EAAa,YAAY,OAAI,EAC3C,iBAAkB,SAAmB,EACrC,aAAc,EAAa,YAAY,OAAI,EAC3C,YAAa,QAAc,EAC3B,WAAY,EAAa,UAAU,OAAI,CACzC,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,CAAC,KAAK,EAAE,EAAS,MAAM,CAAC,EAAE,EAAE,EAAS,UAAU,CAAA,CAAE,EAGnE,IAAM,EAAS,EAAS,IAAI,EAAE,YAC9B,GAAI,CAAC,EACH,MADW,AACL,AAAI,MAAM,uBAGlB,IAAM,EAAU,IAAI,YAChB,EAAc,GAGlB,IAFA,EAAiB,AAEV,MAAM,CACX,GAAM,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,EAAM,MAKV,IAAK,IAAM,KAHG,AACA,EADQ,CAGH,KAHS,CAAC,AAGH,EAHU,CAAE,QAAQ,CAAK,GAC/B,KAAK,CAAC,MAGxB,GAAI,EAAK,UAAU,CAAC,UAClB,CAD6B,EACzB,CACF,IAAM,EAAO,KAAK,KAAK,CAAC,EAAK,KAAK,CAAC,IACnC,GAAkB,UAAd,EAAK,IAAI,EAAgB,EAAK,IAAI,CAAE,CAEtC,IAAM,EAAgB,EAAK,IAAI,CAAC,OAAO,CAAC,OAAQ,MAAM,OAAO,CAAC,SAAU,QACxE,GAAe,EAEf,EAAiB,EACnB,CACF,CAAE,MAAO,EAAI,CAEb,CAGN,CAEA,EAAc,GAGd,GAAI,CACF,GAAM,sBAAE,CAAoB,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EAC3B,EAAa,MAAM,EAAqB,EAAa,EAAE,CAAE,GAE3D,EAAW,OAAO,EAAE,AACtB,GAAsB,uDAGtB,CAAA,EAAA,EAAA,iBAAA,AAAgB,EAAE,YAAa,IAG3B,EAHJ,CAIE,EAAqB,CACnB,GAAG,CAAY,CACf,WAHsB,OAGH,EACnB,OAAQ,WACV,KAGF,QAAQ,KAAK,CAAC,oCAAqC,EAAW,KAAK,EACnE,EAAW,CAAC,sCAAsC,EAAE,EAAW,KAAK,CAAA,CAAE,EAE1E,CAAE,MAAO,EAAW,CAClB,QAAQ,KAAK,CAAC,kCAAmC,GACjD,EAAW,+DACb,CAEA,WAAW,IAAM,EAAW,IAAK,IACnC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,mBAAoB,GAClC,EAAW,wCACX,WAAW,IAAM,EAAW,IAAK,IACnC,CAEJ,CAAE,KAAM,CACN,EAAW,+BACb,QAAU,CACR,GAAgB,EAClB,CACF,EAEM,GAAgB,UACpB,GAAI,CAAC,EAAW,IAAI,GAAI,YACtB,EAAW,6CAIb,EAAe,IACf,EAAW,IAEX,GAAI,CACF,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,mBAAA,AAAkB,EAAE,EAAa,EAAE,CAAE,GAEtD,EAAO,MAFU,CAEH,EAAE,AAClB,EAAW,uCAGX,CAAA,EAAA,EAAA,iBAAA,AAAgB,EAAE,WAAY,IAE9B,GAFA,QAEW,KACT,EAAW,IACX,GACF,EAAG,MAEH,EAAW,EAAO,KAAK,EAAI,iCAE/B,CAAE,KAAM,CACN,EAAW,+BACb,QAAU,CACR,GAAe,EACjB,CACF,EAEM,GAAwB,UAC5B,GAAI,CACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,GACpC,GAAsB,uBAAwB,IAChD,CAAE,KAAM,CACN,EAAW,8BACb,CACF,EAGM,GAAyB,UAE7B,GAAI,EAAe,OAAO,CAAE,CAC1B,EAAe,OAAO,CAAC,IAAI,GAC3B,EAAe,OAAO,CAAG,KACzB,EAAgB,MAChB,MACF,CAEA,GAAkB,GAClB,EAAW,oBAEX,GAAI,CAEE,AAAC,EAAgB,OAAO,EAAE,CAC5B,EAAgB,OAAO,CAAG,IAAI,AAAC,OAAO,YAAY,EAAK,OAAe,kBAAA,AAAkB,CAAA,EAEpD,aAAa,CAA/C,EAAgB,OAAO,CAAC,KAAK,EAC/B,MAAM,EAAgB,OAAO,CAAC,MAAM,GAGtC,IAAM,EAAe,EAAgB,OAAO,CAGtC,EAAW,CAAC,qBAAqB,EAAE,mBAAmB,EAAa,iBAAiB,EAAA,CAAG,CACvF,EAAW,MAAM,MAAM,GAC7B,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACN,AAAJ,MAAU,CAAC,oBAAoB,EAAE,EAAS,UAAU,CAAA,CAAE,EAE9D,IAAM,EAAY,MAAM,EAAS,WAAW,GAGtC,EAAc,MAAM,EAAa,eAAe,CAAC,GAGjD,EAAS,EAAa,kBAAkB,GAC9C,EAAO,MAAM,CAAG,EAChB,EAAO,OAAO,CAAC,EAAa,WAAW,EACvC,EAAO,KAAK,CAAC,GAGb,EAAO,OAAO,CAAG,KACf,EAAgB,MAChB,EAAe,OAAO,CAAG,IAC3B,EAEA,EAAe,OAAO,CAAG,EACzB,EAAgB,CAAC,GAAuB,AACxC,EAAW,GAEb,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,IAJ2D,mBAInC,GACtC,IAAM,EAAe,aAAiB,MAAQ,EAAM,OAAO,CAAG,OAAO,GACrE,EAAW,CAAC,sBAAsB,EAAE,EAAA,CAAc,EAClD,EAAgB,KAClB,QAAU,CACR,GAAkB,EACpB,CACF,EAEM,GAAY,UAKhB,GAHiB,CAGb,gCAH8C,IAAI,CAAC,UAAU,SAAS,EAG5D,YACZ,MAAM,KAKR,GAAI,EAAc,CAChB,EAAa,KAAK,GAClB,EAAa,WAAW,CAAG,EAC3B,EAAgB,MAChB,MACF,CAEA,GAAI,CACF,IAAM,EAAQ,IAAI,MAAM,EAAa,iBAAiB,CACtD,GAAM,OAAO,CAAG,IAAM,EAAgB,MACtC,EAAM,OAAO,CAAG,KACd,EAAgB,MAChB,EAAW,uBACb,EAEA,EAAgB,GAChB,MAAM,EAAM,IAAI,EAClB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wBAAyB,GACvC,EAAgB,MAChB,EAAW,uBACb,CACF,EAGM,GAAuB,IACb,AAMP,CALL,yBACA,aACA,YACA,aACD,CACY,IAAI,CAAC,GAAQ,cAAc,eAAe,CAAC,KAAU,aAI9D,GAA2B,UAC/B,GAAI,CACF,IAAM,EAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC,CAAE,OAAO,CAAK,GACjE,EAAW,KACX,EAAW,IAAI,cAAc,EAAQ,UAAE,CAAS,GAChD,EAAqB,EAE3B,AAF6B,GAEpB,eAAe,CAAG,AAAC,GAAM,EAAO,IAAI,CAAC,EAAE,IAAI,EACpD,EAAS,MAAM,CAAG,KAChB,IAAM,EAAO,IAAI,KAAK,EAAQ,CAAE,KAAM,CAAS,GAC/C,EAAuB,GACvB,EAAO,SAAS,GAAG,OAAO,CAAC,GAAS,EAAM,IAAI,GAChD,EAEA,EAAS,KAAK,GACd,EAAiB,GACjB,GAAyB,EAC3B,CAAE,MAAO,EAAQ,CACf,EAAW,kEACb,CACF,EAUM,GAAwB,UAC5B,GAAK,CAAD,CAEJ,GAAI,CACF,IAAM,EAAY,IAAI,KAHE,AAGG,CAAC,EAAoB,CAAE,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,CAAE,CACtF,KAAM,WACR,GAEM,EAAS,MAAM,CAAA,EAAA,EAAA,kBAAiB,AAAjB,EAAmB,EAAa,EAAE,CAAE,GACzD,GAAI,EAAO,IADU,GACH,CAAE,CAClB,EAAW,2EACX,EAAuB,MAGvB,GAAM,CAAE,kBAAgB,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACvB,EAAsB,MAAM,IAElC,GAAI,EAAoB,OAAO,EAAI,EAAoB,IAAI,CAAE,CAC3D,IAAM,EAAsB,EAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,EAAa,EAAE,EACjG,GAAuB,IACzB,EAAqB,GACrB,EAAW,WAFoC,iFAInD,CAEA,GAAsB,GACxB,MACE,CADK,CACM,EAAO,KAAK,EAAI,oCAE/B,CAAE,MAAO,EAAQ,CACf,EAAW,oCACb,CACF,EAGM,GAAoB,MAAO,IAC/B,GAAI,CACF,EAAW,qBACX,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,qBAAA,AAAoB,EAAE,EAAa,EAAE,CAAE,GAE5D,GAAI,EAAO,CAFU,MAEH,CAAE,CAClB,GAAsB,+BAGtB,GAAM,kBAAE,CAAgB,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACvB,EAAsB,MAAM,IAElC,GAAI,EAAoB,OAAO,EAAI,EAAoB,IAAI,CAAE,CAC3D,IAAM,EAAsB,EAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,EAAa,EAAE,EACjG,GAAuB,GACzB,EAAqB,EAEzB,CACF,MACE,CADK,CACM,EAAO,IALiC,CAK5B,EAAI,yBAE/B,CAAE,MAAO,EAAQ,CACf,EAAW,yBACb,QAAU,CACR,GAAkB,KACpB,CACF,EAEM,GAAoB,MAAO,IAC/B,GAAI,CACF,EAAW,qBACX,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,uBAAsB,AAAtB,EAAwB,EAAa,EAAE,CAAE,GAE9D,GAAI,CAFiB,CAEV,OAAO,CAAE,CAClB,GAAsB,+BAGtB,GAAM,CAAE,kBAAgB,CAAE,CAAG,MAAA,EAAA,CAAA,CAAA,QAAA,EAAA,CAAA,EACvB,EAAsB,MAAM,IAElC,GAAI,EAAoB,OAAO,EAAI,EAAoB,IAAI,CAAE,CAC3D,IAAM,EAAsB,EAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,EAAa,EAAE,EACjG,GAAuB,GACzB,EAAqB,EAEzB,CACF,MACE,CADK,CACM,EAAO,IALiC,CAK5B,EAAI,yBAE/B,CAAE,MAAO,EAAQ,CACf,EAAW,yBACb,QAAU,CACR,GAAkB,KACpB,CACF,EAGM,GAAuB,CAAC,EAAyB,KAIrD,GAHc,WAAW,IAGP,CAFhB,GAAkB,MAAE,MAAM,CAAI,EAChC,EAAG,KAAK,AAEV,EAEM,GAAqB,KACrB,KACF,IANyB,OAKP,EACL,IACb,GAAkB,MAEtB,EAEM,GACJ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wJAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+GACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gEACX,EAAa,YAAY,EAAI,CAAC,SAAS,EAAE,EAAa,cAAc,EAAI,MAAA,CAAO,GAElF,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,0DACV,GAAA,EAAA,UAAA,AAAS,EAAE,EAAa,UAAU,EAAE,MAAI,EAAa,GAArD,SAAiE,CAAC,MAAI,EAAa,MAAM,IAE3F,EAAa,YAAY,EACxB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,uEAA6D,mBAClD,EAAa,YAAY,OAIrD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kHAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,yEAAgE,sBACjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mEAA0D,SAC3E,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,MAAO,GACP,SAAU,AAAC,GAAM,GAAoB,EAAE,MAAM,CAAC,KAAK,EACnD,UAAU,0KACV,SAAkC,aAAxB,EAAa,MAAM,UAE5B,OAAO,OAAO,CAAC,EAAA,wBAAwB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAO,EAAM,CAA7C,EACd,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAmB,MAAO,EAAO,UAAU,sBACzC,GADU,SAMnB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,uFAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,wBAAZ,2BAOT,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qEAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,mEACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAgD,qBAInE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,SAAU,EACV,UAAU,sSAET,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAC,UAAU,kBAAlB,OACC,EACF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YAEjB,CAAA,EAAA,EAAA,GAAA,AAFC,EAEA,EAAA,IAAI,CAAA,CAAC,UAAU,YAElB,CAAA,EAAA,EAAA,GAAA,CAFG,CAEF,OAAA,UACE,EAAiB,aAAe,EAAe,cAAgB,uBAO/C,aAAxB,EAAa,MAAM,EAClB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6GACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,gFACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAsD,sBAGvE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4EAAmE,gBAErF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACZ,CAAC,GAAyB,CAAC,GAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,UAAU,8LAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,YACf,CAAA,EAAA,EAAA,GAAA,EADC,AACA,OAAA,UAAK,cAIT,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAzNU,CAyND,IAxNzB,GAAyC,aAAa,CAArC,EAAc,KAAK,GACtC,EAAc,IAAI,GAClB,GAAyB,GACzB,EAAiB,MAErB,EAoNsB,UAAU,4MAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAClB,CAAA,EAAA,EAAA,EADC,CACD,EAAC,OAAA,UAAK,sBAIT,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2FACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,UAAU,+KAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAClB,CAAA,EAAA,EAAA,EADC,CACD,EAAC,OAAA,UAAK,cAER,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAuB,MACtC,UAAU,6KAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,YACb,CAAA,EAAA,EAAA,GAAA,EAAC,EADA,KACA,UAAK,oBAMb,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4CAAmC,oBAItD,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8CAAqC,uBAKxD,EAAa,qBAAqB,EAClC,MAAM,OAAO,CAAC,EAAa,qBAAqB,GAChD,EAAa,qBAAqB,CAAC,MAAM,CAAG,GAC3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,uCAA6B,2BAAyB,EAAa,qBAAqB,CAAC,MAAM,CAAC,OAC7G,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAa,qBAAqB,CAChC,MAAM,CAAC,AAAC,GAAqD,UAApB,OAAO,GAAsC,OAAb,GACzE,GAAG,CAAC,CAAC,EAAU,IAChB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,UAAU,wEACV,aAAc,IAAM,AAAwB,eAAX,MAAM,EAAmB,GAAkB,CAAE,KAAM,QAAS,IAAK,CAAS,GAC3G,aAAc,IAAM,GAAkB,MACtC,aAAc,IAA8B,aAAxB,EAAa,MAAM,EAAmB,GAAqB,QAAS,GACxF,WAAY,GACZ,cAAe,aAEf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCACf,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,kCAAwB,SAAO,EAAQ,QAIhC,aAAxB,EAAa,MAAM,EAAmB,IAAgB,OAAS,SAAW,IAAgB,MAAQ,GACjG,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAkB,GACjC,UAAU,2HACV,MAAM,yBAEN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAClB,CAAA,EAAA,EAAA,EADC,CACD,EAAC,OAAA,UAAK,gBArBL,kBAmCtB,EAAa,UAAU,EAAI,MAAM,OAAO,CAAC,EAAa,UAAU,GAAK,EAAa,UAAU,CAAC,MAAM,CAAG,GACrG,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,mDAAyC,oBACnC,EAAa,UAAU,CAAC,MAAM,CAAC,SAAO,EAAa,UAAU,CAAC,MAAM,CAAG,EAAI,IAAM,GAAG,OAExG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gCACZ,EAAa,UAAU,CACrB,MAAM,CAAC,AAAC,GAAqD,UAApB,OAAO,GAAyB,AAAa,UACtF,GAAG,CAAC,CAAC,EAAU,IACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,UAAU,iBACV,aAAc,IAA8B,aAAxB,EAAa,MAAM,EAAmB,GAAkB,CAAE,KAAM,QAAS,IAAK,CAAS,GAC3G,aAAc,IAAM,GAAkB,MACtC,aAAc,IAA8B,aAAxB,EAAa,MAAM,EAAmB,GAAqB,QAAS,GACxF,WAAY,GACZ,cAAe,aAEf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAK,EACL,IAAK,CAAC,eAAe,EAAE,AAFxB,EAEgC,EAAA,CAAG,CAClC,UAAU,oGACV,MAAO,GACP,OAAQ,GACR,SAAU,EAAQ,EAClB,QAAS,EAAQ,EAAI,QAAU,OAC/B,QAAS,AAAC,IACR,QAAQ,KAAK,CAAC,wBAAyB,GACvC,IAAM,EAAS,EAAE,aAAa,CAC9B,EAAO,GAAG,CAAG,iTACb,EAAO,GAAG,CAAG,sBACf,MAGJ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kGACZ,EAAQ,IAIc,aAAxB,EAAa,MAAM,EAAmB,IAAgB,OAAS,SAAW,IAAgB,MAAQ,GACjG,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAkB,GACjC,UAAU,2KACV,MAAM,wBAEN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,gBApCjB,GAoCA,MAUjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,8CAAqC,sBAGnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,kCAAyB,gBAG3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CAEb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAe,OAAO,EAAE,QACvC,UAAU,oIAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,SACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,2BAAkB,cAIpC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAa,OAAO,EAAE,QACrC,UAAU,oIAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAClB,CAAA,EAAA,EAAA,EADC,CACD,EAAC,OAAA,CAAK,UAAU,2BAAkB,cAGnC,EAAiB,MAAM,CAAG,GACzB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,gDAAsC,IAClD,EAAiB,MAAM,CAAC,SAAO,EAAiB,MAAM,CAAG,EAAI,IAAM,SAM1E,EAAiB,MAAM,CAAG,GACzB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gCACZ,EAAiB,GAAG,CAAC,AAAC,GACrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAmB,UAAU,qBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAK,EAAM,OAAO,EAAI,GACtB,IAAI,MAFL,OAGC,UAAU,yGACV,MAAO,GACP,OAAQ,KAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAsB,EAAM,EAAE,EAC7C,UAAU,yIAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,oBAZP,EAAM,EAYX,AAZa,KAoBxB,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,GACL,KAAK,OACL,OAAO,UACP,QAAQ,cACR,SAAU,AAAC,GAAM,EAAE,MAAM,CAAC,KAAK,EAAI,GAAkB,EAAE,MAAM,CAAC,KAAK,EACnE,UAAU,WAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,GACL,KAAK,OACL,OAAO,UACP,QAAQ,CAAA,CAAA,EACR,SAAU,AAAC,GAAM,EAAE,MAAM,CAAC,KAAK,EAAI,GAAkB,EAAE,MAAM,CAAC,KAAK,EACnE,UAAU,cAKd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,6FACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAsD,iCAGvE,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,GACP,SAAU,AAAC,GAAM,GAAmB,EAAE,MAAM,CAAC,KAAK,EAClD,YAAY,2DACZ,KAAM,EACN,UAAU,+KACV,SAAkC,aAAxB,EAAa,MAAM,GAE/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,wFAM5C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,kEACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CAA+C,kCAGhE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACZ,CAAyB,YAAxB,EAAa,MAAM,EAA0C,cAAxB,EAAa,MAAM,AAAK,CAAW,EACxE,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,SAAU,EACV,UAAU,2RAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GADC,AACD,EAAC,OAAA,CAAK,UAAU,4BAAoB,EAAe,gBAA0C,cAAxB,EAAa,MAAM,CAAmB,qBAAuB,qBAClI,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAa,EAAe,SAAW,gBAI1D,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,UAAU,gLAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GAAA,AADC,EACA,OAAA,UAAK,kBAMd,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,EAAe,EAAgB,EACtC,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,YAC0B,YAAxB,EAAa,MAAM,CACf,uEACA,wCAEN,UAAU,+LACV,MAAO,CACL,OAAQ,CAAC,EAAe,EAAgB,CAAA,CAAU,CAAI,CAAA,EAAG,KAAK,GAAG,CAAC,IAAK,KAAK,GAAG,CAAC,IAAsE,GAAjE,CAAC,EAAe,EAAgB,CAAA,CAAU,CAAE,KAAK,CAAC,MAAM,MAAM,CAAQ,KAAK,EAAE,CAAC,CAAG,OACxK,IAED,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uEAA4E,8BAOjG,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAsC,YAO3D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qHACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0FACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,0EAA0E,EACjE,YAAxB,EAAa,MAAM,CAAiB,yDACZ,cAAxB,EAAa,MAAM,CAAmB,4DACtC,sDAAA,CACA,UACC,EAAa,MAAM,CAAC,WAAW,KAElC,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,8CAAoC,iBACnC,CAAA,EAAA,EAAA,UAAS,AAAT,EAAW,EAAa,UAAU,QAKrD,CAAA,EAAA,EAAA,AALmB,IAKnB,EAAC,MAAA,CAAI,UAAU,wGACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,uMACX,UAIwB,aAAxB,EAAa,MAAM,EAAmB,EAAW,IAAI,IACpD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,GACT,SAAU,EACV,UAAU,kVAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GADC,AACD,EAAC,OAAA,UAAM,EAAc,eAAiB,sBAIjB,aAAxB,EAAa,MAAM,EAAmB,CAAC,EAAW,IAAI,IACrD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sHAA6G,2DAY1I,AAAK,EAKE,CAAA,CALH,CAKG,EAAA,EALO,UAKP,AAAW,EAAE,GAAc,SAAS,IAAI,EAJtC,IAKX,CADS,kGCx/BT,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAQO,SAAS,EAAkB,CAAE,cAAe,CAAoB,CAAE,QAAS,GAAiB,CAAK,YAAE,CAAU,CAA0B,EAC5I,GAAM,CAAC,EAAsB,EAAwB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAuB,MAChF,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EADqB,AAC2B,OAC7E,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,CADf,CAC2D,OACjF,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,CADP,CACiB,IAG/C,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAkB,EAH/B,CAI9B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,GACjC,AAFoC,CAEnC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,EADb,CAExB,CAAC,EAAa,EAAe,CAAG,GAAA,EAAA,QAAA,AAAO,EAAE,EADb,CAI5B,EAAc,CAAA,EAAA,EAAA,MAAA,AAAK,EAAkB,MACrC,EAAc,CAAA,AAJkB,EAIlB,EAAA,MAAA,AAAK,EAA+B,MAGxD,CAAA,CAJoB,CAIpB,EAAA,SAAQ,AAAR,EAAU,KACR,EAAiB,AAJC,GAKlB,EAAW,GACX,EAAe,EACjB,EAAG,CAAC,EAAsB,EAAe,AAJzC,EAOA,IAAM,EAAwB,GAAA,EAAA,WAAA,AAAU,EAAE,UACxC,GAAI,GAAa,CAAC,EAAS,KADC,OAE1B,QAAQ,GAAG,CAAC,kCAAmC,EAAW,WAAY,GAIxE,QAAQ,GAAG,CAAC,qCAAsC,EAAc,GAChE,GAAa,GACb,GAAI,CACF,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,gBAAA,AAAe,EAAE,CACpC,KAAM,EAAc,EACpB,SAFmB,AAET,GACV,OAAmB,QAAX,OAAmB,EAAY,CACzC,GAEI,EAAO,OAAO,EAAI,EAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAG,GAAG,AAC1D,EAAiB,GAAQ,IAAI,KAAS,EAAO,IAAI,CAAC,aAAa,CAAC,EAChE,EAAW,EAAO,IAAI,CAAC,OAAO,EAC9B,EAAe,GAAQ,EAAO,GAC9B,QAAQ,GAAG,CAAC,SAAU,EAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE,wBAExD,GAAW,GACX,QAAQ,GAAG,CAAC,iCAEhB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,qCAAsC,GACpD,GAAW,EACb,QAAU,CACR,GAAa,EACf,CACF,EAAG,CAAC,EAAa,EAAS,EAAW,EAAO,EAG5C,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KACR,GAAI,CAAC,EAAY,OAAO,EAAI,CAAC,GAAW,EAD1C,AACqD,CAC7C,EAAY,OAAO,EAAE,CACvB,EAAY,OAAO,CAAC,UAAU,GAC9B,EAAY,OAAO,CAAG,MAExB,MACF,CAkBA,OAhBI,EAAY,OAAO,EAAE,AACvB,EAAY,OAAO,CAAC,UAAU,GAGhC,EAAY,OAAO,CAAG,IAAI,qBACxB,AAAC,IACK,CAAO,CAAC,EAAE,CAAC,cAAc,EAAI,CAAC,GAAa,GAAW,EAAc,MAAM,CAAG,GAAG,CAClF,QAAQ,GAAG,CAAC,4DACZ,IAEJ,EACA,CAAE,UAAW,EAAI,GAGnB,EAAY,OAAO,CAAC,OAAO,CAAC,EAAY,OAAO,EAExC,KACD,EAAY,OAAO,EAAE,CACvB,EAAY,OAAO,CAAC,UAAU,GAC9B,EAAY,OAAO,CAAG,KAE1B,CACF,EAAG,CAAC,EAAuB,EAAS,EAAW,EAAc,MAAM,CAAC,EAWpE,IAAM,EAA+B,AAAC,IACpC,GAAI,AAAe,UAAO,OAAO,EAEjC,IAAM,EAAQ,IAAI,KACZ,EAAY,IAAI,KAAK,GAG3B,OAFA,EAAU,OAAO,CAAC,EAAU,OAAO,GAAK,GAEjC,EAAc,MAAM,CAAC,IAC1B,IAAM,EAAmB,IAAI,KAAK,EAAa,UAAU,EAEzD,OAAQ,GACN,IAAK,QACH,OAAO,EAAiB,YAAY,KAAO,EAAM,YAAY,EAC/D,KAAK,YACH,OAAO,EAAiB,YAAY,KAAO,EAAU,YAAY,EACnE,KAAK,SACH,GAAI,CAAC,EAAY,OAAO,EACxB,IAAM,EAAe,IAAI,KAAK,GAC9B,OAAO,EAAiB,YAAY,KAAO,EAAa,YAAY,EACtE,SACE,OAAO,CACX,CACF,EACF,EAEM,EAAwB,EAA6B,GAAe,MAAM,CAAC,GAC/E,AAAe,OAAO,CAAlB,GACG,EAAa,CADS,KACH,GAAK,GAG3B,EAAgB,AAAC,IACrB,OAAQ,GACN,IAAK,UACH,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,OACV,KAAK,YACH,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,WACV,KAAK,WACH,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,aAAtB,aACV,SACE,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,MACZ,CACF,EAEM,EAAgB,AAAC,IACrB,OAAQ,GACN,IAAK,UACH,MAAO,gBACT,KAAK,YACH,MAAO,mBACT,KAAK,WACH,MAAO,UACT,SACE,MAAO,SACX,CACF,EAEM,EAAsB,AAAC,IAC3B,OAAQ,GACN,IAAK,UACH,MAAO,wDACT,KAAK,YACH,MAAO,2DACT,KAAK,WACH,MAAO,qDACT,SACE,MAAO,qDACX,CACF,SAIA,AAA6B,GAAG,CAA5B,EAAc,MAAM,CAEpB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,oBACD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,qBACxD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,wEAQ/C,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DArB4C,AAsBxD,CAtByD,MAAO,UAAW,YAAa,WAAW,CAsB3F,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAEC,QAAS,IAAM,EAAU,GACzB,UAAW,CAAC,2HAA2H,EACrI,IAAW,EACP,gCACA,iFAAA,CACJ,WAED,EAAI,MAAM,CAAC,GAAG,WAAW,GAAK,EAAI,KAAK,CAAC,GACxC,EAA6B,GAAe,MAAM,CAAC,GAAa,QAAR,AAAgB,GAAO,EAAE,MAAM,GAAK,GAAK,MAAM,CAAG,GACzG,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,sDAAsD,EACtE,IAAW,EACP,4BACA,+BAAA,CACJ,UACC,EAA6B,GAAe,MAAM,CAAC,GAAa,QAAR,AAAgB,GAAO,EAAE,MAAM,GAAK,GAAK,MAAM,KAfvG,MAuBX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sDACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,QACD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,MAAO,EACP,SAAU,AAAC,IACT,IAAM,EAAQ,EAAE,MAAM,CAAC,KAAK,CAC5B,EAAc,GACA,UAAU,CAApB,GACF,EAAc,GAElB,EACA,UAAU,oKAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,eAAM,cACpB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,UACtB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,qBAAY,cAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,kBAAS,mBAGT,WAAf,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,UAAU,oKAQpB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAsB,GAAG,CAAC,AAAC,GAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAU,2KAEV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wCACZ,EAAc,EAAa,MAAM,IAGpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0FACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uDACX,EAAa,YAAY,EAAI,CAAC,SAAS,EAAE,EAAa,cAAc,EAAI,MAAA,CAAO,GAElF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,iFAAiF,EAAE,EAAoB,EAAa,MAAM,EAAA,CAAG,UAC5I,EAAc,EAAa,MAAM,OAItC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iIACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,qBAAW,iBAAe,EAAa,YAAY,IACnE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,MACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,CAAA,EAAA,EAAA,kBAAA,AAAiB,EAAE,EAAa,UAAU,IACjD,CADO,AACP,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,MACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAoB,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAa,UAAU,aAAlC,AAK1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAwB,GACvC,UAAW,CAAC,0LAA0L,EAC5K,YAAxB,EAAa,MAAM,CACf,2IACwB,cAAxB,EAAa,MAAM,CACnB,yIACA,8IAAA,CACJ,UAEuB,YAAxB,EAAa,MAAM,CAClB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,iBACjB,CAAA,EADC,AACD,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,qBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,gBAEJ,cAAxB,EAAa,MAAM,CACrB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,iBACf,CAAA,EAAA,EADC,AACD,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,mBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,cAG9B,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,iBACf,CAAA,EAAA,EAAA,AADC,GACD,EAAC,OAAA,CAAK,UAAU,4BAAmB,kBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,cAMlC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAwB,GACvC,UAAU,sRAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,iBACf,CAAA,EAAA,EADC,AACD,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,iBACnC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qBAAY,sBArE7B,EAAa,EAAE,KA8EzB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,EAAa,UAAU,oCAC9B,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mEACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,qCAGR,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,0BAKZ,IAAjC,EAAsB,MAAM,EAAqB,QAAX,GACrC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,mCAAyB,iCACA,EAAO,aAOlD,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,iBAAiB,CAAA,CAChB,aAAc,EACd,GAFD,KAEU,IAAM,EAAwB,MACvC,qBAxQ0B,AAAD,CAwQH,GAvQ5B,EAAwB,GAExB,EAAiB,GACf,EAAK,GAAG,CAAC,GAAK,EAAE,EAAE,GAAK,EAAoB,EAAE,CAAG,EAAsB,GAE1E,EAmQQ,WAAY,MAKtB,2GCrXO,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAFgC,CAGhC,AAHgC,CAAA,AAI9B,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC7C,CAaM,EAAgB,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAhB,AAAgB,CAAA,AAAiB,CAAjB,CAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8LCxBnE,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAYO,SAAS,EAAkB,QAChC,CAAM,SACN,CAAO,WACP,CAAS,CACT,kBAAgB,cAChB,EAAe,EAAK,cACpB,GAAe,CAAK,CACG,EACvB,GAAI,CAAC,EAAQ,OAAO,KAEpB,IAAM,EAAc,EAAU,gBAAgB,EAAI,GAElD,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0FACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gFAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,wBAAwB,EACvC,EACI,2CACA,gDAAA,CACJ,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,mFAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,cAGf,CAAA,EAAA,EAAA,IAAA,CAHG,CAGF,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uGACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,UAAU,YAAxB,aAEH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mCACX,EAAc,uBAAyB,kBAE1C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,2BACV,EACG,2CACA,sDAOV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACZ,EAAU,UAAU,CAAC,MAAI,EAAU,aAAa,IAEnD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCAA6B,uBAE5C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAC,6CAA6C,EACvD,EAAc,aAAe,gBAAA,CAC7B,CACF,MAAO,CAAE,MAAO,CAAA,EAAG,KAAK,GAAG,CAAC,EAAU,gBAAgB,CAAE,KAAK,CAAC,CAAC,AAAC,MAIpE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,oBAAoB,EACnC,EAAc,eAAiB,kBAAA,CAC/B,WACC,EAAU,gBAAgB,CAAC,OAAO,CAAC,GAAG,iBAM7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CACV,EACG,8GACA,iHAIN,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,MACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6CAAoC,uBAEtD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDAAuC,mBAGtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,6CAOzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACZ,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0GACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,aAAtB,cACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,sCAA6B,kCAG/C,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,EACV,UAAU,kSAET,EACC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iFACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,0BAGR,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,UAAU,YAAxB,AACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,0BAMd,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CACC,KAAK,oBACL,UAAU,2PAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GADC,AACD,EAAC,OAAA,UAAK,gBAGP,CAAC,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,2HACX,2BAOL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GADC,CACD,EAAC,OAAA,WAAK,mBACa,EAAU,gBAAgB,CAAC,OAAoC,IAA/B,EAAU,gBAAgB,CAAS,IAAM,kBAQ1G,kDCtKiK,EAAA,CAAA,CAAA,iCAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAqC,CAAA,EAAA,EAAA,mBAAb,EAAa,AAAoB,EAAE,OAAxB,OAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,gGCE9a,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAOO,SAAS,EAAU,CAAE,OAAK,CAAE,UAAQ,CAAkB,EAC3D,GAAM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,GACnD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,EADD,EAE1C,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADT,EAGxC,CAAA,EAAA,EAAA,SAAQ,AAAR,EAAU,KAEJ,EAAM,AAJ4B,gBAIZ,EAAI,CAFhC,GAEoC,AAChC,GAAoB,GAUtB,CAN4B,UAC1B,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,uBAAA,AAAsB,EAAE,GACzC,EAAO,OAAO,AADG,EACC,EAAO,IAAI,EAAE,AACjC,GAAgB,GAEpB,GAEF,EAAG,CAAC,EAAM,gBAAgB,CAAE,EAAS,EAErC,IAAM,EAAuB,UAC3B,GAAgB,GAChB,GAAI,CAEE,AADW,OAAM,CAAA,EAAA,EAAA,oBAAA,AAAmB,EAAE,EAAU,CAAC,YAAhC,yBAAqE,EAAE,EAAM,gBAAgB,CAAC,GAAG,EAAE,EAAM,UAAU,CAAC,CAAC,EAAE,EAAM,aAAa,CAAC,oBAAoB,EAAC,EAC1K,OAAO,EAChB,AADkB,GACF,EAEpB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,kCAAmC,EACnD,QAAU,CACR,GAAgB,EAClB,CACF,EAEM,EAAgB,IACpB,AAAI,EAAM,gBAAgB,EAAI,GAAW,CAAP,cAC9B,EAAM,gBAAgB,EAAI,GAAW,CAAP,iBAC3B,mBAqBH,EAXJ,AAAI,EAAM,QAWM,QAXU,EAAI,GAAW,CAAP,CAAO,aAAa,CAClD,EAAM,gBAAgB,EAAI,GADW,AACA,CAAP,CAAO,GAAG,CACrC,EAAA,WAAW,CAWpB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,EAbwC,IAaxC,CAZM,AAYF,UAAU,+JACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,8CAAqC,kBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,mBAAmB,EAvBxC,AAAI,AAuBsC,EAvBhC,gBAAgB,EAAI,GAAW,CAAP,YAC9B,EAAM,gBAAgB,EAAI,GAAW,CAAP,eAC3B,iBAqBqD,2CAA2C,CAAC,UAClG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAU,UAAW,CAAC,QAAQ,EAAE,IAAA,CAAiB,QAKtD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2FACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAyC,kBACxD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DACZ,EAAM,UAAU,CAAC,MAAI,EAAM,aAAa,IAE3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAC,sDAAsD,EAzB5E,AAAI,AAyB0E,EAzBpE,gBAAgB,EAAI,GAAW,CAAP,YAC9B,EAAM,gBAAgB,EAAI,GAAW,CAAP,eAC3B,iBAuBuE,CAAuB,CAC3F,MAAO,CAAE,MAAO,CAAA,EAAG,KAAK,GAAG,CAAC,EAAM,gBAAgB,CAAE,KAAK,CAAC,CAAE,AAAD,MAG/D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAW,CAAC,+BAA+B,EAAE,IAAA,CAAiB,WACjE,EAAM,gBAAgB,CAAC,YAM9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2FACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,eAArB,sCACD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAoC,cACnD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDAAiD,EAAM,eAAe,MAGvF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2FACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,gBAAnB,sCACD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAoC,cACnD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DAAiD,EAAM,gBAAgB,CAAC,OAAgC,IAA3B,EAAM,gBAAgB,CAAS,IAAM,eAMtI,EAAM,gBAAgB,EAAI,IACzB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,UAAU,YAAxB,uCACD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uBAAc,4BAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,6CAMV,EAAM,gBAAgB,EAAI,IAAM,EAAM,gBAAgB,CAAG,IACxD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,sBAAd,gCACD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uBAAc,wBAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,yCAOX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,iBAAiB,CAAA,CAChB,OAAQ,EACR,QAFD,AAEU,IAAM,GAAoB,GACnC,UAAW,EACX,iBAAkB,EAClB,aAAc,EACd,aAAc,MAItB,kDC3JuJ,EAAA,CAAA,CAAA,8BAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAkC,CAAA,EAAA,EAAA,gBAAb,KAAa,AAAoB,EAAE,IAAxB,UAAE,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,iGCEja,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAMO,SAAS,EAAc,UAAE,CAAQ,CAAsB,EAC5D,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAuB,MAChE,CAAC,EAAc,EAAgB,CAAG,GAAA,EAAA,QAAA,AAAO,EADP,AACiB,IACnD,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADG,EAElC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADX,EAG5B,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KAkBR,CAjBkB,CAHU,SAI1B,GAAM,CAAC,EAAY,EAAW,CAAG,CAFrC,KAE2C,QAAQ,GAAG,CAAC,CACjD,CAAA,EAAA,EAAA,eAAA,AAAc,EAAE,GAChB,CAAA,EAAA,EAAA,YADA,QACmB,AAAnB,EAAqB,GACtB,EAEG,EAAW,OAAO,CAHpB,CAGsB,AACtB,EAAgB,EAAW,IAAI,EAG7B,EAAW,OAAO,EAAE,AACtB,EAAgB,EAAW,IAAI,EAGjC,GAAW,GACb,GAGF,EAAG,CAAC,EAAS,EAEb,IAAM,EAAiB,UACjB,IACF,MAAM,IADU,MACA,SAAS,CAAC,SAAS,CAAC,GACpC,GAAU,GACV,WAAW,IAAM,GAAU,GAAQ,KAEvC,EAEA,GAAI,EACF,MACE,CAAA,AAFS,EAET,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAMvB,GAAI,CAAC,EACH,OAAO,KADU,AAInB,IAAM,EAAe,IACnB,AAAI,EAAa,oBAAoB,EAAI,EAChC,CADmC,AACnC,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,OAEH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,qBAAf,QAGV,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+JAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACZ,IACD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,8CAAqC,wBAErD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wFACZ,SAKL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAA4C,YAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CAAsC,EAAa,oBAAoB,MAExF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDAA2C,WAC1D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CAAoC,IAAE,EAAa,eAAe,CAAC,OAAO,CAAC,YAI9F,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAyC,UACxD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CAAmC,EAAa,eAAe,MAEhF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAA0C,YACzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAoC,EAAa,iBAAiB,SAKrF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAA0C,uBACzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,MAAO,EACP,QAAQ,CAAA,CAAA,EACR,UAAU,mFAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,4GAET,EAAS,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YAAe,CAAA,EAAA,EAAA,EAA/B,CAA+B,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,oBAAf,AAMhD,EAAa,WAAW,EACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,MACD,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,kCAAwB,eAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBAAe,EAAa,WAAW,CAAC,IAAI,gBAQxF", "ignoreList": [0, 4]}