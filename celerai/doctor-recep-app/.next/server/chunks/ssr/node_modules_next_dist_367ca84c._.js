module.exports={438061:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={atLeastOneTask:function(){return h},scheduleImmediate:function(){return b},scheduleOnNextTick:function(){return a},waitAtLeastOneReactRenderTask:function(){return i}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a=a=>{Promise.resolve().then(()=>{process.nextTick(a)})},b=a=>{setImmediate(a)};function h(){return new Promise(a=>b(a))}function i(){return new Promise(a=>setImmediate(a))}}},959020:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={DynamicServerError:function(){return b},isDynamicServerError:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a="DYNAMIC_SERVER_USAGE";class b extends Error{constructor(b){super("Dynamic server usage: "+b),this.description=b,this.digest=a}}function h(b){return"object"==typeof b&&null!==b&&"digest"in b&&"string"==typeof b.digest&&b.digest===a}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},730136:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={StaticGenBailoutError:function(){return b},isStaticGenBailoutError:function(){return h}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a="NEXT_STATIC_GEN_BAILOUT";class b extends Error{constructor(...b){super(...b),this.code=a}}function h(b){return"object"==typeof b&&null!==b&&"code"in b&&b.code===a}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},891939:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={isHangingPromiseRejectionError:function(){return h},makeHangingPromise:function(){return i}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});function h(b){return"object"==typeof b&&null!==b&&"digest"in b&&b.digest===a}let a="HANGING_PROMISE_REJECTION";class b extends Error{constructor(b){super(`During prerendering, ${b} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${b} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=b,this.digest=a}}let c=new WeakMap;function i(a,d){if(a.aborted)return Promise.reject(new b(d));{let e=new Promise((e,f)=>{let g=f.bind(null,new b(d)),h=c.get(a);if(h)h.push(g);else{let b=[g];c.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return e.catch(j),e}}function j(){}}},116624:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={METADATA_BOUNDARY_NAME:function(){return a},OUTLET_BOUNDARY_NAME:function(){return c},VIEWPORT_BOUNDARY_NAME:function(){return b}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let a="__next_metadata_boundary__",b="__next_viewport_boundary__",c="__next_outlet_boundary__"}},241664:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f,g={Postpone:function(){return t},abortAndThrowOnSynchronousRequestDataAccess:function(){return s},abortOnSynchronousPlatformIOAccess:function(){return q},accessedDynamicData:function(){return A},annotateDynamicAccess:function(){return G},consumeDynamicAccess:function(){return B},createDynamicTrackingState:function(){return i},createDynamicValidationState:function(){return j},createHangingInputAbortSignal:function(){return F},createPostponedAbortSignal:function(){return E},formatDynamicAPIAccesses:function(){return C},getFirstDynamicReason:function(){return k},isDynamicPostpone:function(){return w},isPrerenderInterruptedError:function(){return z},markCurrentScopeAsDynamic:function(){return l},postponeWithTracking:function(){return u},throwIfDisallowedDynamic:function(){return J},throwToInterruptStaticGeneration:function(){return n},trackAllowedDynamicAccess:function(){return I},trackDynamicDataInDynamicRender:function(){return o},trackFallbackParamAccessed:function(){return m},trackSynchronousPlatformIOAccessInDev:function(){return r},trackSynchronousRequestDataAccessInDev:function(){return Q},useDynamicRouteParams:function(){return H}};for(var h in g)Object.defineProperty(e,h,{enumerable:!0,get:g[h]});let b=(f=a.r(465421))&&f.__esModule?f:{default:f},c=a.r(959020),d=a.r(730136),K=a.r(983943),L=a.r(86103),M=a.r(891939),N=a.r(116624),O=a.r(438061),P="function"==typeof b.default.unstable_postpone;function i(a){return{isDebugDynamicAccesses:a,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function j(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function k(a){var b;return null==(b=a.dynamicAccesses[0])?void 0:b.expression}function l(a,b,e){if((!b||"cache"!==b.type&&"unstable-cache"!==b.type)&&!a.forceDynamic&&!a.forceStatic){if(a.dynamicShouldError)throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(b){if("prerender-ppr"===b.type)u(a.route,e,b.dynamicTracking);else if("prerender-legacy"===b.type){b.revalidate=0;let d=Object.defineProperty(new c.DynamicServerError(`Route ${a.route} couldn't be rendered statically because it used ${e}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw a.dynamicUsageDescription=e,a.dynamicUsageStack=d.stack,d}}}}function m(a,b){let c=K.workUnitAsyncStorage.getStore();c&&"prerender-ppr"===c.type&&u(a.route,b,c.dynamicTracking)}function n(a,b,d){let e=Object.defineProperty(new c.DynamicServerError(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw d.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=e.stack,e}function o(a,b){b&&"cache"!==b.type&&"unstable-cache"!==b.type&&("prerender"===b.type||"prerender-legacy"===b.type)&&(b.revalidate=0)}function p(a,b,c){let d=y(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`);c.controller.abort(d);let e=c.dynamicTracking;e&&e.dynamicAccesses.push({stack:e.isDebugDynamicAccesses?Error().stack:void 0,expression:b})}function q(a,b,c,d){let e=d.dynamicTracking;e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicExpression=b,e.syncDynamicErrorWithStack=c),p(a,b,d)}function r(a){a.prerenderPhase=!1}function s(a,b,c,d){if(!1===d.controller.signal.aborted){let e=d.dynamicTracking;e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicExpression=b,e.syncDynamicErrorWithStack=c,!0===d.validating&&(e.syncDynamicLogged=!0)),p(a,b,d)}throw y(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`)}let Q=r;function t({reason:a,route:b}){let c=K.workUnitAsyncStorage.getStore();u(b,a,c&&"prerender-ppr"===c.type?c.dynamicTracking:null)}function u(a,c,d){D(),d&&d.dynamicAccesses.push({stack:d.isDebugDynamicAccesses?Error().stack:void 0,expression:c}),b.default.unstable_postpone(v(a,c))}function v(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function w(a){return"object"==typeof a&&null!==a&&"string"==typeof a.message&&x(a.message)}function x(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===x(v("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let R="NEXT_PRERENDER_INTERRUPTED";function y(a){let b=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return b.digest=R,b}function z(a){return"object"==typeof a&&null!==a&&a.digest===R&&"name"in a&&"message"in a&&a instanceof Error}function A(a){return a.length>0}function B(a,b){return a.dynamicAccesses.push(...b.dynamicAccesses),a.dynamicAccesses}function C(a){return a.filter(a=>"string"==typeof a.stack&&a.stack.length>0).map(({expression:a,stack:b})=>(b=b.split("\n").slice(4).filter(a=>!(a.includes("node_modules/next/")||a.includes(" (<anonymous>)")||a.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${a}:
${b}`))}function D(){if(!P)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function E(a){D();let c=new AbortController;try{b.default.unstable_postpone(a)}catch(a){c.abort(a)}return c.signal}function F(a){let b=new AbortController;return a.cacheSignal?a.cacheSignal.inputReady().then(()=>{b.abort()}):(0,O.scheduleOnNextTick)(()=>b.abort()),b.signal}function G(a,b){let c=b.dynamicTracking;c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:a})}function H(a){let c=L.workAsyncStorage.getStore();if(c&&c.isStaticGeneration&&c.fallbackRouteParams&&c.fallbackRouteParams.size>0){let d=K.workUnitAsyncStorage.getStore();d&&("prerender"===d.type?b.default.use((0,M.makeHangingPromise)(d.renderSignal,a)):"prerender-ppr"===d.type?u(c.route,a,d.dynamicTracking):"prerender-legacy"===d.type&&n(a,c,d))}}let S=/\n\s+at Suspense \(<anonymous>\)/,T=RegExp(`\\n\\s+at ${N.METADATA_BOUNDARY_NAME}[\\n\\s]`),U=RegExp(`\\n\\s+at ${N.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),V=RegExp(`\\n\\s+at ${N.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function I(a,b,c,d,e){if(!V.test(b)){if(T.test(b)){c.hasDynamicMetadata=!0;return}if(U.test(b)){c.hasDynamicViewport=!0;return}if(S.test(b)){c.hasSuspendedDynamic=!0;return}else if(d.syncDynamicErrorWithStack||e.syncDynamicErrorWithStack){c.hasSyncDynamicErrors=!0;return}else{let d=function(a,b){let c=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return c.stack="Error: "+a+b,c}(`Route "${a}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,b);c.dynamicErrors.push(d);return}}}function J(a,b,c,e){let f,g,h;if(c.syncDynamicErrorWithStack?(f=c.syncDynamicErrorWithStack,g=c.syncDynamicExpression,h=!0===c.syncDynamicLogged):e.syncDynamicErrorWithStack?(f=e.syncDynamicErrorWithStack,g=e.syncDynamicExpression,h=!0===e.syncDynamicLogged):(f=null,g=void 0,h=!1),b.hasSyncDynamicErrors&&f)throw h||console.error(f),new d.StaticGenBailoutError;let i=b.dynamicErrors;if(i.length){for(let a=0;a<i.length;a++)console.error(i[a]);throw new d.StaticGenBailoutError}if(!b.hasSuspendedDynamic){if(b.hasDynamicMetadata){if(f)throw console.error(f),Object.defineProperty(new d.StaticGenBailoutError(`Route "${a}" has a \`generateMetadata\` that could not finish rendering before ${g} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new d.StaticGenBailoutError(`Route "${a}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(b.hasDynamicViewport){if(f)throw console.error(f),Object.defineProperty(new d.StaticGenBailoutError(`Route "${a}" has a \`generateViewport\` that could not finish rendering before ${g} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new d.StaticGenBailoutError(`Route "${a}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}}}};

//# sourceMappingURL=node_modules_next_dist_367ca84c._.js.map