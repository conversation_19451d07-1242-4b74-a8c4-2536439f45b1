module.exports={81725:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},654129:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("@aws-sdk/client-s3",()=>require("@aws-sdk/client-s3"))},711656:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({STORAGE_CONFIG:()=>b,calculateTotalFileSize:()=>l,deleteFile:()=>i,downloadFile:()=>k,extractFilePathFromUrl:()=>j,generateStoragePath:()=>f,uploadFile:()=>g,uploadMultipleFiles:()=>h,validateFile:()=>e,validateTotalSize:()=>m}),a.i(81725);var d=a.i(654129);let b={BUCKET_NAME:process.env.R2_BUCKET_NAME||"celerai-storage",PUBLIC_URL:process.env.R2_PUBLIC_URL||"https://celerai.tallyup.pro",AUDIO_PREFIX:"consultation-audio",IMAGE_PREFIX:"consultation-images",MAX_FILE_SIZE:0x6400000,MAX_TOTAL_SIZE:0xc800000,ALLOWED_AUDIO_TYPES:["audio/webm","audio/mp3","audio/wav","audio/m4a","audio/mpeg","audio/mp4","audio/ogg"],ALLOWED_IMAGE_TYPES:["image/jpeg","image/jpg","image/png","image/webp","image/heic"],RETENTION_DAYS:30},c=()=>new d.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID||"57014886c6cd87ebacf23a94e56a6e0c"}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID||"4dff08f96bf2f040b48bf3973813f7f0",secretAccessKey:process.env.R2_SECRET_ACCESS_KEY||"****************************************************************"}});function e(a,c){return a.size>b.MAX_FILE_SIZE?{valid:!1,error:`File size exceeds ${b.MAX_FILE_SIZE/1024/1024}MB limit`}:("audio"===c?b.ALLOWED_AUDIO_TYPES:b.ALLOWED_IMAGE_TYPES).includes(a.type)?{valid:!0}:{valid:!1,error:`File type ${a.type} is not allowed`}}function f(a,c,d,e){let f=d.replace(/[^a-zA-Z0-9.-]/g,"_"),g="audio"===e?b.AUDIO_PREFIX:b.IMAGE_PREFIX;return`${g}/${a}/${c}/${f}`}async function g(a,g,h,i){try{let j=e(a,i);if(!j.valid)return{success:!1,error:j.error};let k=c(),l=f(g,h,a.name,i),m=await a.arrayBuffer(),n=new d.PutObjectCommand({Bucket:b.BUCKET_NAME,Key:l,Body:new Uint8Array(m),ContentType:a.type,CacheControl:"public, max-age=3600"});await k.send(n);let o=`${b.PUBLIC_URL}/${l}`;return{success:!0,url:o}}catch(a){return console.error("R2 upload error:",a),{success:!1,error:`Upload failed: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(a,b,c,d){let e=await Promise.all(a.map(a=>g(a,b,c,d))),f=e.filter(a=>a.success),h=e.filter(a=>!a.success);return h.length>0?{success:!1,errors:h.map(a=>a.error||"Unknown error")}:{success:!0,urls:f.map(a=>a.url).filter(Boolean)}}async function i(a,e){try{let e=c(),f=new d.DeleteObjectCommand({Bucket:b.BUCKET_NAME,Key:a});return await e.send(f),{success:!0}}catch(a){return console.error("R2 delete error:",a),{success:!1,error:a instanceof Error?a.message:"Delete failed"}}}function j(a,c){try{let d="audio"===c?b.AUDIO_PREFIX:b.IMAGE_PREFIX,e=`/${d}/`,f=a.indexOf(e);if(-1===f)return null;return a.substring(a.indexOf(d))}catch{return null}}async function k(a,c){try{let c=`${b.PUBLIC_URL}/${a}`,d=await fetch(c);if(!d.ok)throw Error(`HTTP ${d.status}: ${d.statusText}`);let e=await d.blob();return{success:!0,data:e}}catch(a){return console.error("R2 download error:",a),{success:!1,error:a instanceof Error?a.message:"Download failed"}}}function l(a){return a.reduce((a,b)=>a+b.size,0)}function m(a){return l(a)>b.MAX_TOTAL_SIZE?{valid:!1,error:`Total file size exceeds ${b.MAX_TOTAL_SIZE/1024/1024}MB limit`}:{valid:!0}}}}};

//# sourceMappingURL=%5Broot-of-the-server%5D__987ee301._.js.map