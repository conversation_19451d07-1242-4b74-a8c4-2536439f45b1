module.exports={269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},137496:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSession:()=>i,decrypt:()=>h,deleteSession:()=>l,encrypt:()=>g,refreshSession:()=>k,updateSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify session"),null}}async function i(a){let b=new Date(Date.now()+6048e5),c=await g({userId:a,expiresAt:b}),d=await (0,f.cookies)();console.log("DEBUG: Creating session for user:",a),console.log("DEBUG: Session expires at:",b),d.set("session",c,{httpOnly:!0,secure:!1,expires:b,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("session")?.value,c=await h(b);if(console.log("DEBUG: Updating session - session exists:",!!b),console.log("DEBUG: Updating session - payload valid:",!!c),!b||!c)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let d=new Date(Date.now()+6048e5);a.set("session",b,{httpOnly:!0,secure:!1,expires:d,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function k(a){console.log("DEBUG: Refreshing session for user:",a),await l(),await i(a),console.log("DEBUG: Session refresh completed")}async function l(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting session cookie"),a.delete("session"),console.log("DEBUG: Session cookie deleted")}}},76803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkSession:()=>c,getDoctorQuota:()=>k,getUser:()=>i,getUserById:()=>j,verifySession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(137496),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId||(0,f.redirect)("/login"),{isAuth:!0,userId:c.userId}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId?{isAuth:!0,userId:c.userId}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("*").eq("id",a.userId).single();if(d)return console.error("Failed to fetch user:",d.message||d),null;if(!c)return null;return{...c,password_hash:c.password_hash}}catch(a){return console.error("Failed to fetch user:",a instanceof Error?a.message:a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",a).single();if(d)return console.error("Failed to fetch user by ID:",d.message||d),null;if(!c)return null;return{...c}}catch(a){return console.error("Failed to fetch user by ID:",a instanceof Error?a.message:a),null}}),k=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",a).single();if(d)return console.error("Failed to fetch quota:",d.message||d),null;let e=c.monthly_quota-c.quota_used,f=Math.round(c.quota_used/c.monthly_quota*100),g=new Date(c.quota_reset_at),i=Math.ceil((g.getTime()-Date.now())/864e5);return{monthly_quota:c.monthly_quota,quota_used:c.quota_used,quota_remaining:e,quota_percentage:f,quota_reset_at:c.quota_reset_at,days_until_reset:Math.max(0,i)}}catch(a){return console.error("Failed to fetch quota:",a instanceof Error?a.message:a),null}})}},753354:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],c=(0,d.default)("clock",b)}},293499:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Clock:()=>d.default});var d=a.i(753354)},834277:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],c=(0,d.default)("zap",b)}},533023:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Zap:()=>d.default});var d=a.i(834277)},805517:function(a){var{g:b,__dirname:c,m:d,e:e}=a},555430:a=>{var{g:b,__dirname:c}=a;a.n(a.i(610056))},202070:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(555430),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/page.tsx"]}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},694989:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(555430),a.i(660874),a.i(715847),a.i(781045),a.i(202070)},586461:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(202070)},919468:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(694989);var d=a.i(586461)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__76cf8bdc._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__67a6890b._.js.map