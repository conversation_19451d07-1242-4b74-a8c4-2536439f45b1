module.exports={13979:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],c=(0,d.default)("chevron-down",b)}},256051:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ChevronDown:()=>d.default});var d=a.i(13979)},749346:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){if("undefined"==typeof Proxy)return a;let b=new Map;return new Proxy((...b)=>a(...b),{get:(c,d)=>"create"===d?a:(b.has(d)||b.set(d,a(d)),b.get(d))})}a.s({createDOMMotionComponentProxy:()=>d})},188221:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}a.s({isAnimationControls:()=>d})},69840:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function e(a,b,c,e){if("function"==typeof b){let[f,g]=d(e);b=b(void 0!==c?c:a.custom,f,g)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[f,g]=d(e);b=b(void 0!==c?c:a.custom,f,g)}return b}a.s({resolveVariantFromProps:()=>e})},197507:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({resolveVariant:()=>e});var d=a.i(69840);function e(a,b,c){let e=a.getProps();return(0,d.resolveVariantFromProps)(e,b,void 0!==c?c:e.custom,a)}},722409:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){return a?.[b]??a?.default??a}a.s({getValueTransition:()=>d})},498314:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({noop:()=>b});let b=a=>a}},804927:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MotionGlobalConfig:()=>b});let b={}}},814911:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({stepsOrder:()=>b});let b=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"]}},539322:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({statsBuffer:()=>b});let b={value:null,addProjectionMetrics:null}}},214345:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createRenderStep:()=>e});var d=a.i(539322);function e(a,b){let c=new Set,e=new Set,f=!1,g=!1,h=new WeakSet,i={delta:0,timestamp:0,isProcessing:!1},j=0;function k(b){h.has(b)&&(l.schedule(b),a()),j++,b(i)}let l={schedule:(a,b=!1,d=!1)=>{let g=d&&f?c:e;return b&&h.add(a),g.has(a)||g.add(a),a},cancel:a=>{e.delete(a),h.delete(a)},process:a=>{if(i=a,f){g=!0;return}f=!0,[c,e]=[e,c],c.forEach(k),b&&d.statsBuffer.value&&d.statsBuffer.value.frameloop[b].push(j),j=0,c.clear(),f=!1,g&&(g=!1,l.process(a))}};return l}},560276:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createRenderBatcher:()=>g});var d=a.i(804927),e=a.i(814911),f=a.i(214345);function g(a,b){let c=!1,g=!0,h={delta:0,timestamp:0,isProcessing:!1},i=()=>c=!0,j=e.stepsOrder.reduce((a,c)=>(a[c]=(0,f.createRenderStep)(i,b?c:void 0),a),{}),{setup:k,read:l,resolveKeyframes:m,preUpdate:n,update:o,preRender:p,render:q,postRender:r}=j,s=()=>{let e=d.MotionGlobalConfig.useManualTiming?h.timestamp:performance.now();c=!1,d.MotionGlobalConfig.useManualTiming||(h.delta=g?1e3/60:Math.max(Math.min(e-h.timestamp,40),1)),h.timestamp=e,h.isProcessing=!0,k.process(h),l.process(h),m.process(h),n.process(h),o.process(h),p.process(h),q.process(h),r.process(h),h.isProcessing=!1,c&&b&&(g=!1,a(s))},t=()=>{c=!0,g=!0,h.isProcessing||a(s)};return{schedule:e.stepsOrder.reduce((a,b)=>{let d=j[b];return a[b]=(a,b=!1,e=!1)=>(c||t(),d.schedule(a,b,e)),a},{}),cancel:a=>{for(let b=0;b<e.stepsOrder.length;b++)j[e.stepsOrder[b]].cancel(a)},state:h,steps:j}}},259255:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cancelFrame:()=>c,frame:()=>b,frameData:()=>e,frameSteps:()=>f});var d=a.i(498314);let{schedule:b,cancel:c,state:e,steps:f}=(0,a.i(560276).createRenderBatcher)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:d.noop,!0)}},237979:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({transformPropOrder:()=>b,transformProps:()=>c});let b=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],c=new Set(b)}},605667:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({positionalKeys:()=>b});let b=new Set(["width","height","top","left","right","bottom",...a.i(237979).transformPropOrder])}},740786:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){-1===a.indexOf(b)&&a.push(b)}function e(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}function f([...a],b,c){let d=b<0?a.length+b:b;if(d>=0&&d<a.length){let d=c<0?a.length+c:c,[e]=a.splice(b,1);a.splice(d,0,e)}return a}a.s({addUniqueItem:()=>d,moveItem:()=>f,removeItem:()=>e})},827277:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SubscriptionManager:()=>b});var d=a.i(740786);class b{constructor(){this.subscriptions=[]}add(a){return(0,d.addUniqueItem)(this.subscriptions,a),()=>(0,d.removeItem)(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}}},231184:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){return b?1e3/b*a:0}a.s({velocityPerSecond:()=>d})},153512:a=>{"use strict";var{g:b,__dirname:c}=a;{let b;a.s({time:()=>c});var d=a.i(804927),e=a.i(259255);function f(){b=void 0}let c={now:()=>(void 0===b&&c.set(e.frameData.isProcessing||d.MotionGlobalConfig.useManualTiming?e.frameData.timestamp:performance.now()),b),set:a=>{b=a,queueMicrotask(f)}}}},121149:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MotionValue:()=>i,collectMotionValues:()=>c,motionValue:()=>h});var d=a.i(827277),e=a.i(231184),f=a.i(153512),g=a.i(259255);let b=a=>!isNaN(parseFloat(a)),c={current:void 0};class i{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(a,b=!0)=>{let c=f.time.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty();b&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=f.time.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=b(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new d.SubscriptionManager);let c=this.events[a].add(b);return"change"===a?()=>{c(),g.frame.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a,b=!0){b&&this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a,b)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return c.current&&c.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let a=f.time.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||a-this.updatedAt>30)return 0;let b=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,e.velocityPerSecond)(parseFloat(this.current)-parseFloat(this.prevFrameValue),b)}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(a,b){return new i(a,b)}}},384376:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isKeyframesTarget:()=>b});let b=a=>Array.isArray(a)}},774063:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({setTarget:()=>g});var d=a.i(121149),e=a.i(384376),f=a.i(197507);function g(a,b){let{transitionEnd:c={},transition:g={},...h}=(0,f.resolveVariant)(a,b)||{};for(let b in h={...h,...c}){var i;let c=(i=h[b],(0,e.isKeyframesTarget)(i)?i[i.length-1]||0:i);a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,(0,d.motionValue)(c))}}},990148:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isMotionValue:()=>b});let b=a=>!!(a&&a.getVelocity)}},403573:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isWillChangeMotionValue:()=>e});var d=a.i(990148);function e(a){return!!((0,d.isMotionValue)(a)&&a.add)}},857121:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({addValueToWillChange:()=>f});var d=a.i(804927),e=a.i(403573);function f(a,b){let c=a.getValue("willChange");if((0,e.isWillChangeMotionValue)(c))return c.add(b);if(!c&&d.MotionGlobalConfig.WillChange){let c=new d.MotionGlobalConfig.WillChange("auto");a.addValue("willChange",c),c.add(b)}}},820280:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({camelToDash:()=>b});let b=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()}},198265:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({optimizedAppearDataAttribute:()=>c,optimizedAppearDataId:()=>b});var d=a.i(820280);let b="framerAppearId",c="data-"+(0,d.camelToDash)(b)}},953288:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getOptimisedAppearId:()=>e});var d=a.i(198265);function e(a){return a.props[d.optimizedAppearDataAttribute]}},272218:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({pipe:()=>c});let b=(a,b)=>c=>b(a(c)),c=(...a)=>a.reduce(b)}},95706:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({clamp:()=>b});let b=(a,b,c)=>c>b?b:c<a?a:c}},638463:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({millisecondsToSeconds:()=>c,secondsToMilliseconds:()=>b});let b=a=>1e3*a,c=a=>a/1e3}},765015:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({activeAnimations:()=>b});let b={layout:0,mainThread:0,waapi:0}}},761310:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({invariant:()=>c,warning:()=>b});let b=()=>{},c=()=>{}}},863483:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isCSSVariableName:()=>c,isCSSVariableToken:()=>e});let b=a=>b=>"string"==typeof b&&b.startsWith(a),c=b("--"),d=b("var(--"),e=a=>!!d(a)&&f.test(a.split("/*")[0].trim()),f=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu}},846550:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({alpha:()=>c,number:()=>b,scale:()=>e});var d=a.i(95706);let b={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},c={...b,transform:a=>(0,d.clamp)(0,1,a)},e={...b,default:1}}},117042:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({sanitize:()=>b});let b=a=>Math.round(1e5*a)/1e5}},222277:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({floatRegex:()=>b});let b=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu}},382079:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return null==a}a.s({isNullish:()=>d})},82049:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({singleColorRegex:()=>b});let b=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu}},319580:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isColorString:()=>b,splitColor:()=>c});var d=a.i(222277),e=a.i(382079),f=a.i(82049);let b=(a,b)=>c=>!!("string"==typeof c&&f.singleColorRegex.test(c)&&c.startsWith(a)||b&&!(0,e.isNullish)(c)&&Object.prototype.hasOwnProperty.call(c,b)),c=(a,b,c)=>e=>{if("string"!=typeof e)return e;let[f,g,h,i]=e.match(d.floatRegex);return{[a]:parseFloat(f),[b]:parseFloat(g),[c]:parseFloat(h),alpha:void 0!==i?parseFloat(i):1}}}},151444:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({rgbUnit:()=>c,rgba:()=>h});var d=a.i(95706),e=a.i(846550),f=a.i(117042),g=a.i(319580);let b=a=>(0,d.clamp)(0,255,a),c={...e.number,transform:a=>Math.round(b(a))},h={test:(0,g.isColorString)("rgb","red"),parse:(0,g.splitColor)("red","green","blue"),transform:({red:a,green:b,blue:d,alpha:g=1})=>"rgba("+c.transform(a)+", "+c.transform(b)+", "+c.transform(d)+", "+(0,f.sanitize)(e.alpha.transform(g))+")"}}},207897:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hex:()=>b});var d=a.i(151444);let b={test:(0,a.i(319580).isColorString)("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:d.rgba.transform}}},128779:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({degrees:()=>c,percent:()=>d,progressPercentage:()=>h,px:()=>e,vh:()=>f,vw:()=>g});let b=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),c=b("deg"),d=b("%"),e=b("px"),f=b("vh"),g=b("vw"),h={...d,parse:a=>d.parse(a)/100,transform:a=>d.transform(100*a)}}},202913:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hsla:()=>b});var d=a.i(846550),e=a.i(128779),f=a.i(117042),g=a.i(319580);let b={test:(0,g.isColorString)("hsl","hue"),parse:(0,g.splitColor)("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:g=1})=>"hsla("+Math.round(a)+", "+e.percent.transform((0,f.sanitize)(b))+", "+e.percent.transform((0,f.sanitize)(c))+", "+(0,f.sanitize)(d.alpha.transform(g))+")"}}},322e3:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({color:()=>b});var d=a.i(207897),e=a.i(202913),f=a.i(151444);let b={test:a=>f.rgba.test(a)||d.hex.test(a)||e.hsla.test(a),parse:a=>f.rgba.test(a)?f.rgba.parse(a):e.hsla.test(a)?e.hsla.parse(a):d.hex.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?f.rgba.transform(a):e.hsla.transform(a)}}},423984:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({colorRegex:()=>b});let b=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu}},504233:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({analyseComplexValue:()=>h,complex:()=>m});var d=a.i(322e3),e=a.i(423984),f=a.i(222277),g=a.i(117042);let b="number",c="color",k=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(a){let e=a.toString(),f=[],g={color:[],number:[],var:[]},h=[],i=0,j=e.replace(k,a=>(d.color.test(a)?(g.color.push(i),h.push(c),f.push(d.color.parse(a))):a.startsWith("var(")?(g.var.push(i),h.push("var"),f.push(a)):(g.number.push(i),h.push(b),f.push(parseFloat(a))),++i,"${}")).split("${}");return{values:f,split:j,indexes:g,types:h}}function i(a){return h(a).values}function j(a){let{split:e,types:f}=h(a),i=e.length;return a=>{let h="";for(let j=0;j<i;j++)if(h+=e[j],void 0!==a[j]){let e=f[j];e===b?h+=(0,g.sanitize)(a[j]):e===c?h+=d.color.transform(a[j]):h+=a[j]}return h}}let l=a=>"number"==typeof a?0:a,m={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(f.floatRegex)?.length||0)+(a.match(e.colorRegex)?.length||0)>0},parse:i,createTransformer:j,getAnimatableNone:function(a){let b=i(a);return j(a)(b.map(l))}}}},919356:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function e({hue:a,saturation:b,lightness:c,alpha:e}){a/=360,c/=100;let f=0,g=0,h=0;if(b/=100){let e=c<.5?c*(1+b):c+b-c*b,i=2*c-e;f=d(i,e,a+1/3),g=d(i,e,a),h=d(i,e,a-1/3)}else f=g=h=c;return{red:Math.round(255*f),green:Math.round(255*g),blue:Math.round(255*h),alpha:e}}a.s({hslaToRgba:()=>e})},161933:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){return c=>c>0?b:a}a.s({mixImmediate:()=>d})},217969:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({mixNumber:()=>b});let b=(a,b,c)=>a+(b-a)*c}},496125:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({mixColor:()=>m,mixLinearColor:()=>b});var d=a.i(761310),e=a.i(207897),f=a.i(202913),g=a.i(919356),h=a.i(151444),i=a.i(161933),j=a.i(217969);let b=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},c=[e.hex,h.rgba,f.hsla],l=a=>c.find(b=>b.test(a));function k(a){let b=l(a);if((0,d.warning)(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`),!b)return!1;let c=b.parse(a);return b===f.hsla&&(c=(0,g.hslaToRgba)(c)),c}let m=(a,c)=>{let d=k(a),e=k(c);if(!d||!e)return(0,i.mixImmediate)(a,c);let f={...d};return a=>(f.red=b(d.red,e.red,a),f.green=b(d.green,e.green,a),f.blue=b(d.blue,e.blue,a),f.alpha=(0,j.mixNumber)(d.alpha,e.alpha,a),h.rgba.transform(f))}}},771115:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({invisibleValues:()=>b,mixVisibility:()=>d});let b=new Set(["none","hidden"]);function d(a,c){return b.has(a)?b=>b<=0?a:c:b=>b>=1?c:a}}},159174:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getMixer:()=>n,mixArray:()=>o,mixComplex:()=>b,mixObject:()=>p});var d=a.i(272218),e=a.i(761310),f=a.i(863483),g=a.i(322e3),h=a.i(504233),i=a.i(496125),j=a.i(161933),k=a.i(217969),l=a.i(771115);function m(a,b){return c=>(0,k.mixNumber)(a,b,c)}function n(a){return"number"==typeof a?m:"string"==typeof a?(0,f.isCSSVariableToken)(a)?j.mixImmediate:g.color.test(a)?i.mixColor:b:Array.isArray(a)?o:"object"==typeof a?g.color.test(a)?i.mixColor:p:j.mixImmediate}function o(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>n(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function p(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=n(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let b=(a,b)=>{let c=h.complex.createTransformer(b),f=(0,h.analyseComplexValue)(a),g=(0,h.analyseComplexValue)(b);return f.indexes.var.length===g.indexes.var.length&&f.indexes.color.length===g.indexes.color.length&&f.indexes.number.length>=g.indexes.number.length?l.invisibleValues.has(a)&&!g.values.length||l.invisibleValues.has(b)&&!f.values.length?(0,l.mixVisibility)(a,b):(0,d.pipe)(o(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(f,g),g.values),c):((0,e.warning)(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),(0,j.mixImmediate)(a,b))}}},216087:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({mix:()=>f});var d=a.i(159174),e=a.i(217969);function f(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?(0,e.mixNumber)(a,b,c):(0,d.getMixer)(a)(a,b)}},57070:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({frameloopDriver:()=>b});var d=a.i(153512),e=a.i(259255);let b=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>e.frame.update(b,a),stop:()=>(0,e.cancelFrame)(b),now:()=>e.frameData.isProcessing?e.frameData.timestamp:d.time.now()}}}},658015:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({generateLinearEasing:()=>b});let b=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=a(b/(e-1))+", ";return`linear(${d.substring(0,d.length-2)})`}}},831914:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({calcGeneratorDuration:()=>d,maxGeneratorDuration:()=>b});let b=2e4;function d(a){let c=0,d=a.next(c);for(;!d.done&&c<b;)c+=50,d=a.next(c);return c>=b?1/0:c}}},894623:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createGeneratorEasing:()=>f});var d=a.i(638463),e=a.i(831914);function f(a,b=100,c){let g=c({...a,keyframes:[0,b]}),h=Math.min((0,e.calcGeneratorDuration)(g),e.maxGeneratorDuration);return{type:"keyframes",ease:a=>g.next(h*a).value/b,duration:(0,d.millisecondsToSeconds)(h)}}},284163:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({calcGeneratorVelocity:()=>e});var d=a.i(231184);function e(a,b,c){let e=Math.max(b-5,0);return(0,d.velocityPerSecond)(c-a(e),b-e)}},969445:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({springDefaults:()=>b});let b={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1}}},122196:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({calcAngularFreq:()=>i,findSpring:()=>h});var d=a.i(761310),e=a.i(638463),f=a.i(95706),g=a.i(969445);function h({duration:a=g.springDefaults.duration,bounce:b=g.springDefaults.bounce,velocity:c=g.springDefaults.velocity,mass:h=g.springDefaults.mass}){let j,k;(0,d.warning)(a<=(0,e.secondsToMilliseconds)(g.springDefaults.maxDuration),"Spring duration must be 10 seconds or less");let l=1-b;l=(0,f.clamp)(g.springDefaults.minDamping,g.springDefaults.maxDamping,l),a=(0,f.clamp)(g.springDefaults.minDuration,g.springDefaults.maxDuration,(0,e.millisecondsToSeconds)(a)),l<1?(j=b=>{let d=b*l,e=d*a;return .001-(d-c)/i(b,l)*Math.exp(-e)},k=b=>{let d=b*l*a,e=Math.pow(l,2)*Math.pow(b,2)*a,f=Math.exp(-d),g=i(Math.pow(b,2),l);return(d*c+c-e)*f*(-j(b)+.001>0?-1:1)/g}):(j=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),k=b=>a*a*(c-b)*Math.exp(-b*a));let m=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(j,k,5/a);if(a=(0,e.secondsToMilliseconds)(a),isNaN(m))return{stiffness:g.springDefaults.stiffness,damping:g.springDefaults.damping,duration:a};{let b=Math.pow(m,2)*h;return{stiffness:b,damping:2*l*Math.sqrt(h*b),duration:a}}}function i(a,b){return a*Math.sqrt(1-b*b)}},433006:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({spring:()=>m});var d=a.i(638463),e=a.i(95706),f=a.i(658015),g=a.i(831914),h=a.i(894623),i=a.i(284163),j=a.i(969445),k=a.i(122196);let b=["duration","bounce"],c=["stiffness","damping","mass"];function l(a,b){return b.some(b=>void 0!==a[b])}function m(a=j.springDefaults.visualDuration,h=j.springDefaults.bounce){let n,o="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:h}:a,{restSpeed:p,restDelta:q}=o,r=o.keyframes[0],s=o.keyframes[o.keyframes.length-1],t={done:!1,value:r},{stiffness:u,damping:v,mass:w,duration:x,velocity:y,isResolvedFromDuration:z}=function(a){let d={velocity:j.springDefaults.velocity,stiffness:j.springDefaults.stiffness,damping:j.springDefaults.damping,mass:j.springDefaults.mass,isResolvedFromDuration:!1,...a};if(!l(a,c)&&l(a,b))if(a.visualDuration){let b=2*Math.PI/(1.2*a.visualDuration),c=b*b,f=2*(0,e.clamp)(.05,1,1-(a.bounce||0))*Math.sqrt(c);d={...d,mass:j.springDefaults.mass,stiffness:c,damping:f}}else{let b=(0,k.findSpring)(a);(d={...d,...b,mass:j.springDefaults.mass}).isResolvedFromDuration=!0}return d}({...o,velocity:-(0,d.millisecondsToSeconds)(o.velocity||0)}),A=y||0,B=v/(2*Math.sqrt(u*w)),C=s-r,D=(0,d.millisecondsToSeconds)(Math.sqrt(u/w)),E=5>Math.abs(C);if(p||(p=E?j.springDefaults.restSpeed.granular:j.springDefaults.restSpeed.default),q||(q=E?j.springDefaults.restDelta.granular:j.springDefaults.restDelta.default),B<1){let a=(0,k.calcAngularFreq)(D,B);n=b=>s-Math.exp(-B*D*b)*((A+B*D*C)/a*Math.sin(a*b)+C*Math.cos(a*b))}else if(1===B)n=a=>s-Math.exp(-D*a)*(C+(A+D*C)*a);else{let a=D*Math.sqrt(B*B-1);n=b=>{let c=Math.exp(-B*D*b),d=Math.min(a*b,300);return s-c*((A+B*D*C)*Math.sinh(d)+a*C*Math.cosh(d))/a}}let F={calculatedDuration:z&&x||null,next:a=>{let b=n(a);if(z)t.done=a>=x;else{let c=0===a?A:0;B<1&&(c=0===a?(0,d.secondsToMilliseconds)(A):(0,i.calcGeneratorVelocity)(n,a,b));let e=Math.abs(s-b)<=q;t.done=Math.abs(c)<=p&&e}return t.value=t.done?s:b,t},toString:()=>{let a=Math.min((0,g.calcGeneratorDuration)(F),g.maxGeneratorDuration),b=(0,f.generateLinearEasing)(b=>F.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return F}m.applyToOptions=a=>{let b=(0,h.createGeneratorEasing)(a,100,m);return a.ease=b.ease,a.duration=(0,d.secondsToMilliseconds)(b.duration),a.type="keyframes",a}}},112525:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({inertia:()=>f});var d=a.i(433006),e=a.i(284163);function f({keyframes:a,velocity:b=0,power:c=.8,timeConstant:f=325,bounceDamping:g=10,bounceStiffness:h=500,modifyTarget:i,min:j,max:k,restDelta:l=.5,restSpeed:m}){let n,o,p=a[0],q={done:!1,value:p},r=a=>void 0!==j&&a<j||void 0!==k&&a>k,s=a=>void 0===j?k:void 0===k||Math.abs(j-a)<Math.abs(k-a)?j:k,t=c*b,u=p+t,v=void 0===i?u:i(u);v!==u&&(t=v-p);let w=a=>-t*Math.exp(-a/f),x=a=>v+w(a),y=a=>{let b=w(a),c=x(a);q.done=Math.abs(b)<=l,q.value=q.done?v:c},z=a=>{r(q.value)&&(n=a,o=(0,d.spring)({keyframes:[q.value,s(q.value)],velocity:(0,e.calcGeneratorVelocity)(x,a,q.value),damping:g,stiffness:h,restDelta:l,restSpeed:m}))};return z(0),{calculatedDuration:null,next:a=>{let b=!1;return(o||void 0!==n||(b=!0,y(a),z(a)),void 0!==n&&a>=n)?o.next(a-n):(b||y(a),q)}}}},29359:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cubicBezier:()=>e});var d=a.i(498314);let b=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function e(a,c,e,f){if(a===c&&e===f)return d.noop;let g=c=>(function(a,c,d,e,f){let g,h,i=0;do(g=b(h=c+(d-c)/2,e,f)-a)>0?d=h:c=h;while(Math.abs(g)>1e-7&&++i<12)return h})(c,0,1,a,e);return a=>0===a||1===a?a:b(g(a),c,f)}}},906925:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({easeIn:()=>b,easeInOut:()=>e,easeOut:()=>c});var d=a.i(29359);let b=(0,d.cubicBezier)(.42,0,1,1),c=(0,d.cubicBezier)(0,0,.58,1),e=(0,d.cubicBezier)(.42,0,.58,1)}},2572:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isEasingArray:()=>b});let b=a=>Array.isArray(a)&&"number"!=typeof a[0]}},343114:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({mirrorEasing:()=>b});let b=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2}},247984:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({reverseEasing:()=>b});let b=a=>b=>1-a(1-b)}},755291:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({backIn:()=>c,backInOut:()=>g,backOut:()=>b});var d=a.i(29359),e=a.i(343114),f=a.i(247984);let b=(0,d.cubicBezier)(.33,1.53,.69,.99),c=(0,f.reverseEasing)(b),g=(0,e.mirrorEasing)(c)}},254464:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({anticipate:()=>b});var d=a.i(755291);let b=a=>(a*=2)<1?.5*(0,d.backIn)(a):.5*(2-Math.pow(2,-10*(a-1)))}},461655:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({circIn:()=>b,circInOut:()=>f,circOut:()=>c});var d=a.i(343114),e=a.i(247984);let b=a=>1-Math.sin(Math.acos(a)),c=(0,e.reverseEasing)(b),f=(0,d.mirrorEasing)(b)}},53787:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isBezierDefinition:()=>b});let b=a=>Array.isArray(a)&&"number"==typeof a[0]}},460052:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({easingDefinitionToFunction:()=>l});var d=a.i(761310),e=a.i(498314),f=a.i(254464),g=a.i(755291),h=a.i(461655),i=a.i(29359),j=a.i(906925),k=a.i(53787);let b={linear:e.noop,easeIn:j.easeIn,easeInOut:j.easeInOut,easeOut:j.easeOut,circIn:h.circIn,circInOut:h.circInOut,circOut:h.circOut,backIn:g.backIn,backInOut:g.backInOut,backOut:g.backOut,anticipate:f.anticipate},c=a=>"string"==typeof a,l=a=>{if((0,k.isBezierDefinition)(a)){(0,d.invariant)(4===a.length,"Cubic bezier arrays must contain four numerical values.");let[b,c,e,f]=a;return(0,i.cubicBezier)(b,c,e,f)}return c(a)?((0,d.invariant)(void 0!==b[a],`Invalid easing type '${a}'`),b[a]):a}}},847121:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({progress:()=>b});let b=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d}}},968683:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({interpolate:()=>k});var d=a.i(761310),e=a.i(95706),f=a.i(804927),g=a.i(498314),h=a.i(272218),i=a.i(847121),j=a.i(216087);function k(a,b,{clamp:c=!0,ease:l,mixer:m}={}){let n=a.length;if((0,d.invariant)(n===b.length,"Both input and output ranges must be the same length"),1===n)return()=>b[0];if(2===n&&b[0]===b[1])return()=>b[1];let o=a[0]===a[1];a[0]>a[n-1]&&(a=[...a].reverse(),b=[...b].reverse());let p=function(a,b,c){let d=[],e=c||f.MotionGlobalConfig.mix||j.mix,i=a.length-1;for(let c=0;c<i;c++){let f=e(a[c],a[c+1]);if(b){let a=Array.isArray(b)?b[c]||g.noop:b;f=(0,h.pipe)(a,f)}d.push(f)}return d}(b,l,m),q=p.length,r=c=>{if(o&&c<a[0])return b[0];let d=0;if(q>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=(0,i.progress)(a[d],a[d+1],c);return p[d](e)};return c?b=>r((0,e.clamp)(a[0],a[n-1],b)):r}},497885:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({fillOffset:()=>f});var d=a.i(847121),e=a.i(217969);function f(a,b){let c=a[a.length-1];for(let f=1;f<=b;f++){let g=(0,d.progress)(0,b,f);a.push((0,e.mixNumber)(c,1,g))}}},946825:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({defaultOffset:()=>e});var d=a.i(497885);function e(a){let b=[0];return(0,d.fillOffset)(b,a.length-1),b}},797686:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){return a.map(a=>a*b)}a.s({convertOffsetToTimes:()=>d})},704834:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({defaultEasing:()=>j,keyframes:()=>k});var d=a.i(906925),e=a.i(2572),f=a.i(460052),g=a.i(968683),h=a.i(946825),i=a.i(797686);function j(a,b){return a.map(()=>b||d.easeInOut).splice(0,a.length-1)}function k({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){let k=(0,e.isEasingArray)(d)?d.map(f.easingDefinitionToFunction):(0,f.easingDefinitionToFunction)(d),l={done:!1,value:b[0]},m=(0,i.convertOffsetToTimes)(c&&c.length===b.length?c:(0,h.defaultOffset)(b),a),n=(0,g.interpolate)(m,b,{ease:Array.isArray(k)?k:j(b,k)});return{calculatedDuration:a,next:b=>(l.value=n(b),l.done=b>=a,l)}}},937952:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getFinalKeyframe:()=>d});let b=a=>null!==a;function d(a,{repeat:c,repeatType:e="loop"},f,g=1){let h=a.filter(b),i=g<0||c&&"loop"!==e&&c%2==1?0:h.length-1;return i&&void 0!==f?f:h[i]}}},288778:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({replaceTransitionType:()=>g});var d=a.i(112525),e=a.i(704834),f=a.i(433006);let b={decay:d.inertia,inertia:d.inertia,tween:e.keyframes,keyframes:e.keyframes,spring:f.spring};function g(a){"string"==typeof a.type&&(a.type=b[a.type])}}},522123:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({WithPromise:()=>b});class b{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}}},842205:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({JSAnimation:()=>c,animateValue:()=>q});var d=a.i(272218),e=a.i(95706),f=a.i(638463),g=a.i(153512),h=a.i(765015),i=a.i(216087),j=a.i(57070),k=a.i(112525),l=a.i(704834),m=a.i(831914),n=a.i(937952),o=a.i(288778),p=a.i(522123);let b=a=>a/100;class c extends p.WithPromise{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==g.time.now()&&this.tick(g.time.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},h.activeAnimations.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;(0,o.replaceTransitionType)(a);let{type:c=l.keyframes,repeat:e=0,repeatDelay:f=0,repeatType:g,velocity:h=0}=a,{keyframes:j}=a,k=c||l.keyframes;k!==l.keyframes&&"number"!=typeof j[0]&&(this.mixKeyframes=(0,d.pipe)(b,(0,i.mix)(j[0],j[1])),j=[0,100]);let n=k({...a,keyframes:j});"mirror"===g&&(this.mirroredGenerator=k({...a,keyframes:[...j].reverse(),velocity:-h})),null===n.calculatedDuration&&(n.calculatedDuration=(0,m.calcGeneratorDuration)(n));let{calculatedDuration:p}=n;this.calculatedDuration=p,this.resolvedDuration=p+f,this.totalDuration=this.resolvedDuration*(e+1)-f,this.generator=n}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:f,mirroredGenerator:g,resolvedDuration:h,calculatedDuration:i}=this;if(null===this.startTime)return c.next(0);let{delay:j=0,keyframes:l,repeat:m,repeatType:o,repeatDelay:p,type:q,onUpdate:r,finalKeyframe:s}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let t=this.currentTime-j*(this.playbackSpeed>=0?1:-1),u=this.playbackSpeed>=0?t<0:t>d;this.currentTime=Math.max(t,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let v=this.currentTime,w=c;if(m){let a=Math.min(this.currentTime,d)/h,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,m+1))%2&&("reverse"===o?(c=1-c,p&&(c-=p/h)):"mirror"===o&&(w=g)),v=(0,e.clamp)(0,1,c)*h}let x=u?{done:!1,value:l[0]}:w.next(v);f&&(x.value=f(x.value));let{done:y}=x;u||null===i||(y=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let z=null===this.holdTime&&("finished"===this.state||"running"===this.state&&y);return z&&q!==k.inertia&&(x.value=(0,n.getFinalKeyframe)(l,this.options,s,this.speed)),r&&r(x.value),z&&this.finish(),x}then(a,b){return this.finished.then(a,b)}get duration(){return(0,f.millisecondsToSeconds)(this.calculatedDuration)}get time(){return(0,f.millisecondsToSeconds)(this.currentTime)}set time(a){a=(0,f.secondsToMilliseconds)(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(g.time.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=(0,f.millisecondsToSeconds)(this.currentTime))}play(){if(this.isStopped)return;let{driver:a=j.frameloopDriver,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(g.time.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,h.activeAnimations.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function q(a){return new c(a)}}},606353:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}a.s({fillWildcards:()=>d})},769169:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({defaultTransformValue:()=>d,parseValueFromTransform:()=>e,readTransformValue:()=>l});let b=a=>180*a/Math.PI,c=a=>h(b(Math.atan2(a[1],a[0]))),g={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:c,rotateZ:c,skewX:a=>b(Math.atan(a[1])),skewY:a=>b(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},h=a=>((a%=360)<0&&(a+=360),a),i=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),j=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),k={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:i,scaleY:j,scale:a=>(i(a)+j(a))/2,rotateX:a=>h(b(Math.atan2(a[6],a[5]))),rotateY:a=>h(b(Math.atan2(-a[2],a[0]))),rotateZ:c,rotate:c,skewX:a=>b(Math.atan(a[4])),skewY:a=>b(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function d(a){return+!!a.includes("scale")}function e(a,b){let c,e;if(!a||"none"===a)return d(b);let h=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(h)c=k,e=h;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=g,e=b}if(!e)return d(b);let i=c[b],j=e[1].split(",").map(f);return"function"==typeof i?i(j):j[i]}let l=(a,b)=>{let{transform:c="none"}=getComputedStyle(a);return e(c,b)};function f(a){return parseFloat(a.trim())}}},479779:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isNumOrPxType:()=>b,positionalValues:()=>j,removeNonTranslationalTransform:()=>h});var d=a.i(769169),e=a.i(237979),f=a.i(846550),g=a.i(128779);let b=a=>a===f.number||a===g.px,c=new Set(["x","y","z"]),i=e.transformPropOrder.filter(a=>!c.has(a));function h(a){let b=[];return i.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}let j={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>(0,d.parseValueFromTransform)(b,"x"),y:(a,{transform:b})=>(0,d.parseValueFromTransform)(b,"y")};j.translateX=j.x,j.translateY=j.y}},382386:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({KeyframeResolver:()=>l,flushKeyframeResolvers:()=>i});var d=a.i(606353),e=a.i(479779),f=a.i(259255);let b=new Set,c=!1,j=!1,k=!1;function g(){if(j){let a=Array.from(b).filter(a=>a.needsMeasurement),c=new Set(a.map(a=>a.element)),d=new Map;c.forEach(a=>{let b=(0,e.removeNonTranslationalTransform)(a);b.length&&(d.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),c.forEach(a=>{a.render();let b=d.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}j=!1,c=!1,b.forEach(a=>a.complete(k)),b.clear()}function h(){b.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(j=!0)})}function i(){k=!0,h(),g(),k=!1}class l{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(b.add(this),c||(c=!0,f.frame.read(h),f.frame.resolveKeyframes(g))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:e}=this;if(null===a[0]){let d=e?.get(),f=a[a.length-1];if(void 0!==d)a[0]=d;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),e&&void 0===d&&e.set(a[0])}(0,d.fillWildcards)(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),b.delete(this)}cancel(){"scheduled"===this.state&&(b.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}}},888068:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isCSSVar:()=>b});let b=a=>a.startsWith("--")}},214343:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({setStyle:()=>e});var d=a.i(888068);function e(a,b,c){(0,d.isCSSVar)(b)?a.style.setProperty(b,c):a.style[b]=c}},657229:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){let b;return()=>(void 0===b&&(b=a()),b)}a.s({memo:()=>d})},847145:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({supportsScrollTimeline:()=>b});let b=(0,a.i(657229).memo)(()=>void 0!==window.ScrollTimeline)}},46916:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({supportsFlags:()=>b});let b={}}},103530:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({memoSupports:()=>f});var d=a.i(657229),e=a.i(46916);function f(a,b){let c=(0,d.memo)(a);return()=>e.supportsFlags[b]??c()}},789307:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({supportsLinearEasing:()=>b});let b=(0,a.i(103530).memoSupports)(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing")}},985911:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cubicBezierAsString:()=>b});let b=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`}},836936:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({supportedWaapiEasing:()=>b});var d=a.i(985911);let b={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:(0,d.cubicBezierAsString)([0,.65,.55,1]),circOut:(0,d.cubicBezierAsString)([.55,0,1,.45]),backIn:(0,d.cubicBezierAsString)([.31,.01,.66,-.59]),backOut:(0,d.cubicBezierAsString)([.33,1.53,.69,.99])}}},678324:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({mapEasingToNativeEasing:()=>function a(b,c){if(b)return"function"==typeof b?(0,e.supportsLinearEasing)()?(0,f.generateLinearEasing)(b,c):"ease-out":(0,d.isBezierDefinition)(b)?(0,g.cubicBezierAsString)(b):Array.isArray(b)?b.map(b=>a(b,c)||h.supportedWaapiEasing.easeOut):h.supportedWaapiEasing[b]}});var d=a.i(53787),e=a.i(789307),f=a.i(658015),g=a.i(985911),h=a.i(836936)},963611:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({startWaapiAnimation:()=>g});var d=a.i(765015),e=a.i(539322),f=a.i(678324);function g(a,b,c,{delay:h=0,duration:i=300,repeat:j=0,repeatType:k="loop",ease:l="easeOut",times:m}={},n){let o={[b]:c};m&&(o.offset=m);let p=(0,f.mapEasingToNativeEasing)(l,i);Array.isArray(p)&&(o.easing=p),e.statsBuffer.value&&d.activeAnimations.waapi++;let q={delay:h,duration:i,easing:Array.isArray(p)?"linear":p,fill:"both",iterations:j+1,direction:"reverse"===k?"alternate":"normal"};n&&(q.pseudoElement=n);let r=a.animate(o,q);return e.statsBuffer.value&&r.finished.finally(()=>{d.activeAnimations.waapi--}),r}},555260:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return"function"==typeof a&&"applyToOptions"in a}a.s({isGenerator:()=>d})},467118:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({applyGeneratorOptions:()=>f});var d=a.i(789307),e=a.i(555260);function f({type:a,...b}){return(0,e.isGenerator)(a)&&(0,d.supportsLinearEasing)()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}},670549:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({NativeAnimation:()=>b});var d=a.i(761310),e=a.i(638463),f=a.i(498314),g=a.i(214343),h=a.i(847145),i=a.i(937952),j=a.i(522123),k=a.i(963611),l=a.i(467118);class b extends j.WithPromise{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:e,pseudoElement:f,allowFlatten:h=!1,finalKeyframe:j,onComplete:m}=a;this.isPseudoElement=!!f,this.allowFlatten=h,this.options=a,(0,d.invariant)("string"!=typeof a.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let n=(0,l.applyGeneratorOptions)(a);this.animation=(0,k.startWaapiAnimation)(b,c,e,n,f),!1===n.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!f){let a=(0,i.getFinalKeyframe)(e,this.options,j,this.speed);this.updateMotionValue?this.updateMotionValue(a):(0,g.setStyle)(b,c,a),this.animation.cancel()}m?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let a=this.animation.effect?.getComputedTiming?.().duration||0;return(0,e.millisecondsToSeconds)(Number(a))}get time(){return(0,e.millisecondsToSeconds)(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=(0,e.secondsToMilliseconds)(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&(0,h.supportsScrollTimeline)())?(this.animation.timeline=a,f.noop):b(this)}}}},383066:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({replaceStringEasing:()=>g});var d=a.i(254464),e=a.i(755291),f=a.i(461655);let b={anticipate:d.anticipate,backInOut:e.backInOut,circInOut:f.circInOut};function g(a){"string"==typeof a.ease&&a.ease in b&&(a.ease=b[a.ease])}}},518966:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({NativeAnimationExtended:()=>b});var d=a.i(638463),e=a.i(842205),f=a.i(670549),g=a.i(288778),h=a.i(383066);class b extends f.NativeAnimation{constructor(a){(0,h.replaceStringEasing)(a),(0,g.replaceTransitionType)(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:f,element:g,...h}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let i=new e.JSAnimation({...h,autoplay:!1}),j=(0,d.secondsToMilliseconds)(this.finishedTime??this.time);b.setWithVelocity(i.sample(j-10).value,i.sample(j).value,10),i.stop()}}}},880888:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isAnimatable:()=>b});var d=a.i(504233);let b=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(d.complex.test(a)||"0"===a)&&!a.startsWith("url("))}},615313:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({canAnimate:()=>g});var d=a.i(761310),e=a.i(555260),f=a.i(880888);function g(a,b,c,g){let h=a[0];if(null===h)return!1;if("display"===b||"visibility"===b)return!0;let i=a[a.length-1],j=(0,f.isAnimatable)(h,b),k=(0,f.isAnimatable)(i,b);return(0,d.warning)(j===k,`You are trying to animate ${b} from "${h}" to "${i}". ${h} is not an animatable value - to enable this animation set ${h} to a value animatable to ${i} via the \`style\` property.`),!!j&&!!k&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||(0,e.isGenerator)(c))&&g)}},595324:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return"object"==typeof a&&null!==a}a.s({isObject:()=>d})},949208:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isHTMLElement:()=>e});var d=a.i(595324);function e(a){return(0,d.isObject)(a)&&"offsetHeight"in a}},861191:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({supportsBrowserAnimation:()=>f});var d=a.i(657229),e=a.i(949208);let b=new Set(["opacity","clipPath","filter","transform"]),c=(0,d.memo)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function f(a){let{motionValue:d,name:f,repeatDelay:g,repeatType:h,damping:i,type:j}=a;if(!(0,e.isHTMLElement)(d?.owner?.current))return!1;let{onUpdate:k,transformTemplate:l}=d.owner.getProps();return c()&&f&&b.has(f)&&("transform"!==f||!l)&&!k&&!g&&"mirror"!==h&&0!==i&&"inertia"!==j}}},481642:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AsyncMotionValueAnimation:()=>b});var d=a.i(804927),e=a.i(498314),f=a.i(153512),g=a.i(842205),h=a.i(937952),i=a.i(382386),j=a.i(518966),k=a.i(615313),l=a.i(522123),m=a.i(861191);class b extends l.WithPromise{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:g="loop",keyframes:h,name:j,motionValue:k,element:l,...m}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=f.time.now();let n={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:g,name:j,motionValue:k,element:l,...m},o=l?.KeyframeResolver||i.KeyframeResolver;this.keyframeResolver=new o(h,(a,b,c)=>this.onKeyframesResolved(a,b,n,!c),j,k,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,i){this.keyframeResolver=void 0;let{name:l,type:n,velocity:o,delay:p,isHandoff:q,onUpdate:r}=c;this.resolvedAt=f.time.now(),(0,k.canAnimate)(a,l,n,o)||((d.MotionGlobalConfig.instantAnimations||!p)&&r?.((0,h.getFinalKeyframe)(a,c,b)),a[0]=a[a.length-1],c.duration=0,c.repeat=0);let s={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},t=!q&&(0,m.supportsBrowserAnimation)(s)?new j.NativeAnimationExtended({...s,element:s.motionValue.owner.current}):new g.JSAnimation(s);t.finished.then(()=>this.notifyFinished()).catch(e.noop),this.pendingTimeline&&(this.stopTimeline=t.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=t}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,i.flushKeyframeResolvers)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}}},514728:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getFinalKeyframe:()=>d});let b=a=>null!==a;function d(a,{repeat:c,repeatType:d="loop"},e){let f=a.filter(b),g=c&&"loop"!==d&&c%2==1?0:f.length-1;return g&&void 0!==e?e:f[g]}}},592181:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getDefaultTransition:()=>g});var d=a.i(237979);let b={type:"spring",stiffness:500,damping:25,restSpeed:10},c=a=>({type:"spring",stiffness:550,damping:0===a?2*Math.sqrt(550):30,restSpeed:10}),e={type:"keyframes",duration:.8},f={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},g=(a,{keyframes:g})=>g.length>2?e:d.transformProps.has(a)?a.startsWith("scale")?c(g[1]):b:f}},324294:a=>{"use strict";var{g:b,__dirname:c}=a;function d({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}a.s({isTransitionDefined:()=>d})},822839:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({animateMotionValue:()=>b});var d=a.i(722409),e=a.i(259255),f=a.i(842205),g=a.i(481642),h=a.i(638463),i=a.i(804927),j=a.i(514728),k=a.i(592181),l=a.i(324294);let b=(a,b,c,m={},n,o)=>p=>{let q=(0,d.getValueTransition)(m,a)||{},r=q.delay||m.delay||0,{elapsed:s=0}=m;s-=(0,h.secondsToMilliseconds)(r);let t={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...q,delay:-s,onUpdate:a=>{b.set(a),q.onUpdate&&q.onUpdate(a)},onComplete:()=>{p(),q.onComplete&&q.onComplete()},name:a,motionValue:b,element:o?void 0:n};(0,l.isTransitionDefined)(q)||Object.assign(t,(0,k.getDefaultTransition)(a,t)),t.duration&&(t.duration=(0,h.secondsToMilliseconds)(t.duration)),t.repeatDelay&&(t.repeatDelay=(0,h.secondsToMilliseconds)(t.repeatDelay)),void 0!==t.from&&(t.keyframes[0]=t.from);let u=!1;if(!1!==t.type&&(0!==t.duration||t.repeatDelay)||(t.duration=0,0===t.delay&&(u=!0)),(i.MotionGlobalConfig.instantAnimations||i.MotionGlobalConfig.skipAnimations)&&(u=!0,t.duration=0,t.delay=0),t.allowFlatten=!q.type&&!q.ease,u&&!o&&void 0!==b.get()){let a=(0,j.getFinalKeyframe)(t.keyframes,q);if(void 0!==a)return void e.frame.update(()=>{t.onUpdate(a),t.onComplete()})}return q.isSync?new f.JSAnimation(t):new g.AsyncMotionValueAnimation(t)}}},922140:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({animateTarget:()=>k});var d=a.i(722409),e=a.i(259255),f=a.i(605667),g=a.i(774063),h=a.i(857121),i=a.i(953288),j=a.i(822839);function k(a,b,{delay:c=0,transitionOverride:l,type:m}={}){let{transition:n=a.getDefaultTransition(),transitionEnd:o,...p}=b;l&&(n=l);let q=[],r=m&&a.animationState&&a.animationState.getState()[m];for(let b in p){let g=a.getValue(b,a.latestValues[b]??null),k=p[b];if(void 0===k||r&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(r,b))continue;let l={delay:c,...(0,d.getValueTransition)(n||{},b)},m=g.get();if(void 0!==m&&!g.isAnimating&&!Array.isArray(k)&&k===m&&!l.velocity)continue;let o=!1;if(window.MotionHandoffAnimation){let c=(0,i.getOptimisedAppearId)(a);if(c){let a=window.MotionHandoffAnimation(c,b,e.frame);null!==a&&(l.startTime=a,o=!0)}}(0,h.addValueToWillChange)(a,b),g.start((0,j.animateMotionValue)(b,g,k,a.shouldReduceMotion&&f.positionalKeys.has(b)?{type:!1}:l,a,o));let s=g.animation;s&&q.push(s)}return o&&Promise.all(q).then(()=>{e.frame.update(()=>{o&&(0,g.setTarget)(a,o)})}),q}},226853:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({animateVariant:()=>function a(b,c,g={}){let h=(0,d.resolveVariant)(b,c,"exit"===g.type?b.presenceContext?.custom:void 0),{transition:i=b.getDefaultTransition()||{}}=h||{};g.transitionOverride&&(i=g.transitionOverride);let j=h?()=>Promise.all((0,e.animateTarget)(b,h,g)):()=>Promise.resolve(),k=b.variantChildren&&b.variantChildren.size?(d=0)=>{let{delayChildren:e=0,staggerChildren:h,staggerDirection:j}=i;return function(b,c,d=0,e=0,g=1,h){let i=[],j=(b.variantChildren.size-1)*e,k=1===g?(a=0)=>a*e:(a=0)=>j-a*e;return Array.from(b.variantChildren).sort(f).forEach((b,e)=>{b.notify("AnimationStart",c),i.push(a(b,c,{...h,delay:d+k(e)}).then(()=>b.notify("AnimationComplete",c)))}),Promise.all(i)}(b,c,e+d,h,j,g)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([j(),k(g.delay)]);{let[a,b]="beforeChildren"===l?[j,k]:[k,j];return a().then(()=>b())}},sortByTreeOrder:()=>f});var d=a.i(197507),e=a.i(922140);function f(a,b){return a.sortNodePosition(b)}},65377:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({animateVisualElement:()=>g});var d=a.i(197507),e=a.i(922140),f=a.i(226853);function g(a,b,c={}){let h;if(a.notify("AnimationStart",b),Array.isArray(b))h=Promise.all(b.map(b=>(0,f.animateVariant)(a,b,c)));else if("string"==typeof b)h=(0,f.animateVariant)(a,b,c);else{let f="function"==typeof b?(0,d.resolveVariant)(a,b,c.custom):b;h=Promise.all((0,e.animateTarget)(a,f,c))}return h.then(()=>{a.notify("AnimationComplete",b)})}},194038:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}a.s({shallowCompare:()=>d})},997226:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return"string"==typeof a||Array.isArray(a)}a.s({isVariantLabel:()=>d})},21279:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({variantPriorityOrder:()=>b,variantProps:()=>c});let b=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...b]}},316339:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getVariantContext:()=>function a(c){if(!c)return;if(!c.isControllingVariants){let b=c.parent&&a(c.parent)||{};return void 0!==c.props.initial&&(b.initial=c.props.initial),b}let f={};for(let a=0;a<b;a++){let b=e.variantProps[a],g=c.props[b];((0,d.isVariantLabel)(g)||!1===g)&&(f[b]=g)}return f}});var d=a.i(997226),e=a.i(21279);let b=e.variantProps.length}},91225:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkVariantsDidChange:()=>m,createAnimationState:()=>l});var d=a.i(65377),e=a.i(188221),f=a.i(384376),g=a.i(194038),h=a.i(316339),i=a.i(997226),j=a.i(197507),k=a.i(21279);let b=[...k.variantPriorityOrder].reverse(),c=k.variantPriorityOrder.length;function l(a){let k=b=>Promise.all(b.map(({animation:b,options:c})=>(0,d.animateVisualElement)(a,b,c))),l=o(),n=!0,p=b=>(c,d)=>{let e=(0,j.resolveVariant)(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function q(d){let{props:o}=a,q=(0,h.getVariantContext)(a.parent)||{},r=[],s=new Set,t={},u=1/0;for(let h=0;h<c;h++){let c=b[h],j=l[c],k=void 0!==o[c]?o[c]:q[c],v=(0,i.isVariantLabel)(k),w=c===d?j.isActive:null;!1===w&&(u=h);let x=k===q[c]&&k!==o[c]&&v;if(x&&n&&a.manuallyAnimateOnMount&&(x=!1),j.protectedKeys={...t},!j.isActive&&null===w||!k&&!j.prevProp||(0,e.isAnimationControls)(k)||"boolean"==typeof k)continue;let y=m(j.prevProp,k),z=y||c===d&&j.isActive&&!x&&v||h>u&&v,A=!1,B=Array.isArray(k)?k:[k],C=B.reduce(p(c),{});!1===w&&(C={});let{prevResolvedValues:D={}}=j,E={...D,...C},F=b=>{z=!0,s.has(b)&&(A=!0,s.delete(b)),j.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in E){let b=C[a],c=D[a];if(t.hasOwnProperty(a))continue;let d=!1;((0,f.isKeyframesTarget)(b)&&(0,f.isKeyframesTarget)(c)?(0,g.shallowCompare)(b,c):b===c)?void 0!==b&&s.has(a)?F(a):j.protectedKeys[a]=!0:null!=b?F(a):s.add(a)}j.prevProp=k,j.prevResolvedValues=C,j.isActive&&(t={...t,...C}),n&&a.blockInitialAnimation&&(z=!1);let G=!(x&&y)||A;z&&G&&r.push(...B.map(a=>({animation:a,options:{type:c}})))}if(s.size){let b={};if("boolean"!=typeof o.initial){let c=(0,j.resolveVariant)(a,Array.isArray(o.initial)?o.initial[0]:o.initial);c&&c.transition&&(b.transition=c.transition)}s.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),r.push({animation:b})}let v=!!r.length;return n&&(!1===o.initial||o.initial===o.animate)&&!a.manuallyAnimateOnMount&&(v=!1),n=!1,v?k(r):Promise.resolve()}return{animateChanges:q,setActive:function(b,c){if(l[b].isActive===c)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,c)),l[b].isActive=c;let d=q(b);for(let a in l)l[a].protectedKeys={};return d},setAnimateFunction:function(b){k=b(a)},getState:()=>l,reset:()=>{l=o(),n=!0}}}function m(a,b){return"string"==typeof b?b!==a:!!Array.isArray(b)&&!(0,g.shallowCompare)(b,a)}function n(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function o(){return{animate:n(!0),whileInView:n(),whileHover:n(),whileTap:n(),whileDrag:n(),whileFocus:n(),exit:n()}}}},94176:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Feature:()=>b});class b{constructor(a){this.isMounted=!1,this.node=a}update(){}}}},77405:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AnimationFeature:()=>b});var d=a.i(188221),e=a.i(91225),f=a.i(94176);class b extends f.Feature{constructor(a){super(a),a.animationState||(a.animationState=(0,e.createAnimationState)(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();(0,d.isAnimationControls)(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}}},383705:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ExitAnimationFeature:()=>c});var d=a.i(94176);let b=0;class c extends d.Feature{constructor(){super(...arguments),this.id=b++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}}},814340:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({animations:()=>b});var d=a.i(77405),e=a.i(383705);let b={animation:{Feature:d.AnimationFeature},exit:{Feature:e.ExitAnimationFeature}}}},514832:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isDragActive:()=>d,isDragging:()=>b});let b={x:!1,y:!1};function d(){return b.x||b.y}}},820904:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({setDragLock:()=>e});var d=a.i(514832);function e(a){if("x"===a||"y"===a)if(d.isDragging[a])return null;else return d.isDragging[a]=!0,()=>{d.isDragging[a]=!1};return d.isDragging.x||d.isDragging.y?null:(d.isDragging.x=d.isDragging.y=!0,()=>{d.isDragging.x=d.isDragging.y=!1})}},676407:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b,c,e={passive:!0}){return a.addEventListener(b,c,e),()=>a.removeEventListener(b,c)}a.s({addDomEvent:()=>d})},157960:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isPrimaryPointer:()=>b});let b=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary}},961381:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({addPointerInfo:()=>b,extractEventInfo:()=>e});var d=a.i(157960);function e(a){return{point:{x:a.pageX,y:a.pageY}}}let b=a=>b=>(0,d.isPrimaryPointer)(b)&&a(b,e(b))}},257644:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({addPointerEvent:()=>f});var d=a.i(676407),e=a.i(961381);function f(a,b,c,f){return(0,d.addDomEvent)(a,b,(0,e.addPointerInfo)(c),f)}},129627:a=>{"use strict";var{g:b,__dirname:c}=a;function d({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}function e({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}function f(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}a.s({convertBoundingBoxToBox:()=>d,convertBoxToBoundingBox:()=>e,transformBoxPoints:()=>f})},518434:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({calcAxisDelta:()=>g,calcBoxDelta:()=>h,calcLength:()=>e,calcRelativeAxis:()=>i,calcRelativeAxisPosition:()=>k,calcRelativeBox:()=>j,calcRelativePosition:()=>l,isNear:()=>f});var d=a.i(217969);function e(a){return a.max-a.min}function f(a,b,c){return Math.abs(a-b)<=c}function g(a,b,c,f=.5){a.origin=f,a.originPoint=(0,d.mixNumber)(b.min,b.max,a.origin),a.scale=e(c)/e(b),a.translate=(0,d.mixNumber)(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function h(a,b,c,d){g(a.x,b.x,c.x,d?d.originX:void 0),g(a.y,b.y,c.y,d?d.originY:void 0)}function i(a,b,c){a.min=c.min+b.min,a.max=a.min+e(b)}function j(a,b,c){i(a.x,b.x,c.x),i(a.y,b.y,c.y)}function k(a,b,c){a.min=b.min-c.min,a.max=a.min+e(b)}function l(a,b,c){k(a.x,b.x,c.x),k(a.y,b.y,c.y)}},35838:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createAxis:()=>d,createAxisDelta:()=>b,createBox:()=>e,createDelta:()=>c});let b=()=>({translate:0,scale:1,origin:0,originPoint:0}),c=()=>({x:b(),y:b()}),d=()=>({min:0,max:0}),e=()=>({x:d(),y:d()})}},300650:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return[a("x"),a("y")]}a.s({eachAxis:()=>d})},525602:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return void 0===a||1===a}function e({scale:a,scaleX:b,scaleY:c}){return!d(a)||!d(b)||!d(c)}function f(a){return e(a)||g(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function g(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}a.s({has2DTranslate:()=>g,hasScale:()=>e,hasTransform:()=>f})},468106:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({applyAxisDelta:()=>h,applyBoxDelta:()=>i,applyPointDelta:()=>g,applyTreeDeltas:()=>j,scalePoint:()=>f,transformAxis:()=>l,transformBox:()=>m,translateAxis:()=>k});var d=a.i(217969),e=a.i(525602);function f(a,b,c){return c+b*(a-c)}function g(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function h(a,b=0,c=1,d,e){a.min=g(a.min,b,c,d,e),a.max=g(a.max,b,c,d,e)}function i(a,{x:b,y:c}){h(a.x,b.translate,b.scale,b.originPoint),h(a.y,c.translate,c.scale,c.originPoint)}function j(a,b,c,d=!1){let f,g,h=c.length;if(h){b.x=b.y=1;for(let j=0;j<h;j++){g=(f=c[j]).projectionDelta;let{visualElement:h}=f.options;(!h||!h.props.style||"contents"!==h.props.style.display)&&(d&&f.options.layoutScroll&&f.scroll&&f!==f.root&&m(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),g&&(b.x*=g.x.scale,b.y*=g.y.scale,i(a,g)),d&&(0,e.hasTransform)(f.latestValues)&&m(a,f.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}function k(a,b){a.min=a.min+b,a.max=a.max+b}function l(a,b,c,e,f=.5){let g=(0,d.mixNumber)(a.min,a.max,f);h(a,b,c,g,e)}function m(a,b){l(a.x,b.x,b.scaleX,b.scale,b.originX),l(a.y,b.y,b.scaleY,b.scale,b.originY)}},723163:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({measurePageBox:()=>g,measureViewportBox:()=>f});var d=a.i(129627),e=a.i(468106);function f(a,b){return(0,d.convertBoundingBoxToBox)((0,d.transformBoxPoints)(a.getBoundingClientRect(),b))}function g(a,b,c){let d=f(a,c),{scroll:g}=b;return g&&((0,e.translateAxis)(d.x,g.offset.x),(0,e.translateAxis)(d.y,g.offset.y)),d}},612375:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getContextWindow:()=>b});let b=({current:a})=>a?a.ownerDocument.defaultView:null}},947340:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}a.s({isRefObject:()=>d})},431325:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({distance:()=>b,distance2D:()=>d});let b=(a,b)=>Math.abs(a-b);function d(a,c){return Math.sqrt(b(a.x,c.x)**2+b(a.y,c.y)**2)}}},592544:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PanSession:()=>b});var d=a.i(259255),e=a.i(157960),f=a.i(272218),g=a.i(638463),h=a.i(257644),i=a.i(961381),j=a.i(431325);class b{constructor(a,b,{transformPagePoint:c,contextWindow:g,dragSnapToOrigin:l=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=m(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=(0,j.distance2D)(a.offset,{x:0,y:0})>=3;if(!b&&!c)return;let{point:e}=a,{timestamp:f}=d.frameData;this.history.push({...e,timestamp:f});let{onStart:g,onMove:h}=this.handlers;b||(g&&g(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=k(b,this.transformPagePoint),d.frame.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=m("pointercancel"===a.type?this.lastMoveEventInfo:k(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!(0,e.isPrimaryPointer)(a))return;this.dragSnapToOrigin=l,this.handlers=b,this.transformPagePoint=c,this.contextWindow=g||window;let n=k((0,i.extractEventInfo)(a),this.transformPagePoint),{point:o}=n,{timestamp:p}=d.frameData;this.history=[{...o,timestamp:p}];let{onSessionStart:q}=b;q&&q(a,m(n,this.history)),this.removeListeners=(0,f.pipe)((0,h.addPointerEvent)(this.contextWindow,"pointermove",this.handlePointerMove),(0,h.addPointerEvent)(this.contextWindow,"pointerup",this.handlePointerUp),(0,h.addPointerEvent)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),(0,d.cancelFrame)(this.updatePoint)}}function k(a,b){return b?{point:b(a.point)}:a}function l(a,b){return{x:a.x-b.x,y:a.y-b.y}}function m({point:a},b){return{point:a,delta:l(a,n(b)),offset:l(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=n(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>(0,g.secondsToMilliseconds)(.1)));)c--;if(!d)return{x:0,y:0};let f=(0,g.millisecondsToSeconds)(e.timestamp-d.timestamp);if(0===f)return{x:0,y:0};let h={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}(b,.1)}}function n(a){return a[a.length-1]}}},909932:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({applyConstraints:()=>h,calcOrigin:()=>m,calcRelativeAxisConstraints:()=>i,calcRelativeConstraints:()=>j,calcViewportAxisConstraints:()=>k,calcViewportConstraints:()=>l,defaultElastic:()=>b,rebaseAxisConstraints:()=>n,resolveAxisElastic:()=>p,resolveDragElastic:()=>o,resolvePointElastic:()=>q});var d=a.i(217969),e=a.i(847121),f=a.i(95706),g=a.i(518434);function h(a,{min:b,max:c},e){return void 0!==b&&a<b?a=e?(0,d.mixNumber)(b,a,e.min):Math.max(a,b):void 0!==c&&a>c&&(a=e?(0,d.mixNumber)(c,a,e.max):Math.min(a,c)),a}function i(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function j(a,{top:b,left:c,bottom:d,right:e}){return{x:i(a.x,c,e),y:i(a.y,b,d)}}function k(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function l(a,b){return{x:k(a.x,b.x),y:k(a.y,b.y)}}function m(a,b){let c=.5,d=(0,g.calcLength)(a),h=(0,g.calcLength)(b);return h>d?c=(0,e.progress)(b.min,b.max-d,a.min):d>h&&(c=(0,e.progress)(a.min,a.max-h,b.min)),(0,f.clamp)(0,1,c)}function n(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}let b=.35;function o(a=b){return!1===a?a=0:!0===a&&(a=b),{x:p(a,"left","right"),y:p(a,"top","bottom")}}function p(a,b,c){return{min:q(a,b),max:q(a,c)}}function q(a,b){return"number"==typeof a?a:a[b]||0}}},370192:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({VisualElementDragControls:()=>c,elementDragControls:()=>b});var d=a.i(259255),e=a.i(217969),f=a.i(820904),g=a.i(128779),h=a.i(761310),i=a.i(822839),j=a.i(676407),k=a.i(257644),l=a.i(961381),m=a.i(129627),n=a.i(518434),o=a.i(35838),p=a.i(300650),q=a.i(723163),r=a.i(612375),s=a.i(947340),t=a.i(857121),u=a.i(592544),v=a.i(909932);let b=new WeakMap;class c{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,o.createBox)(),this.visualElement=a}start(a,{snapToCursor:b=!1}={}){let{presenceContext:c}=this.visualElement;if(c&&!1===c.isPresent)return;let{dragSnapToOrigin:e}=this.getProps();this.panSession=new u.PanSession(a,{onSessionStart:a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor((0,l.extractEventInfo)(a).point)},onStart:(a,b)=>{let{drag:c,dragPropagation:e,onDragStart:h}=this.getProps();if(c&&!e&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,f.setDragLock)(c),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,p.eachAxis)(a=>{let b=this.getAxisMotionValue(a).get()||0;if(g.percent.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=(0,n.calcLength)(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),h&&d.frame.postRender(()=>h(a,b)),(0,t.addValueToWillChange)(this.visualElement,"transform");let{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(a,b)=>{let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},onSessionEnd:(a,b)=>this.stop(a,b),resumeAnimation:()=>(0,p.eachAxis)(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:e,contextWindow:(0,r.getContextWindow)(this.visualElement)})}stop(a,b){let c=this.isDragging;if(this.cancel(),!c)return;let{velocity:e}=b;this.startAnimation(e);let{onDragEnd:f}=this.getProps();f&&d.frame.postRender(()=>f(a,b))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!w(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=(0,v.applyConstraints)(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&(0,s.isRefObject)(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=(0,v.calcRelativeConstraints)(c.layoutBox,a):this.constraints=!1,this.elastic=(0,v.resolveDragElastic)(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&(0,p.eachAxis)(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=(0,v.rebaseAxisConstraints)(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){let{dragConstraints:a,onMeasureDragConstraints:b}=this.getProps();if(!a||!(0,s.isRefObject)(a))return!1;let c=a.current;(0,h.invariant)(null!==c,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:d}=this.visualElement;if(!d||!d.layout)return!1;let e=(0,q.measurePageBox)(c,d.root,this.visualElement.getTransformPagePoint()),f=(0,v.calcViewportConstraints)(d.layout.layoutBox,e);if(b){let a=b((0,m.convertBoxToBoundingBox)(f));this.hasMutatedConstraints=!!a,a&&(f=(0,m.convertBoundingBoxToBox)(a))}return f}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all((0,p.eachAxis)(g=>{if(!w(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return(0,t.addValueToWillChange)(this.visualElement,a),c.start((0,i.animateMotionValue)(a,c,0,b,this.visualElement,!1))}stopAnimation(){(0,p.eachAxis)(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){(0,p.eachAxis)(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){(0,p.eachAxis)(b=>{let{drag:c}=this.getProps();if(!w(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,f=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:g}=d.layout.layoutBox[b];f.set(a[b]-(0,e.mixNumber)(c,g,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!(0,s.isRefObject)(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};(0,p.eachAxis)(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=(0,v.calcOrigin)({min:c,max:c},this.constraints[a])}});let{transformTemplate:f}=this.visualElement.getProps();this.visualElement.current.style.transform=f?f({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),(0,p.eachAxis)(b=>{if(!w(b,a,null))return;let c=this.getAxisMotionValue(b),{min:f,max:g}=this.constraints[b];c.set((0,e.mixNumber)(f,g,d[b]))})}addListeners(){if(!this.visualElement.current)return;b.set(this.visualElement,this);let a=this.visualElement.current,c=(0,k.addPointerEvent)(a,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),e=()=>{let{dragConstraints:a}=this.getProps();(0,s.isRefObject)(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:f}=this.visualElement,g=f.addEventListener("measure",e);f&&!f.layout&&(f.root&&f.root.updateScroll(),f.updateLayout()),d.frame.read(e);let h=(0,j.addDomEvent)(window,"resize",()=>this.scalePositionWithinConstraints()),i=f.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&((0,p.eachAxis)(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{h(),c(),g(),i&&i()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=v.defaultElastic,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function w(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}}},807978:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DragGesture:()=>b});var d=a.i(94176),e=a.i(498314),f=a.i(370192);class b extends d.Feature{constructor(a){super(a),this.removeGroupControls=e.noop,this.removeListeners=e.noop,this.controls=new f.VisualElementDragControls(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||e.noop}unmount(){this.removeGroupControls(),this.removeListeners()}}}},13612:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PanGesture:()=>c});var d=a.i(259255),e=a.i(498314),f=a.i(257644),g=a.i(94176),h=a.i(612375),i=a.i(592544);let b=a=>(b,c)=>{a&&d.frame.postRender(()=>a(b,c))};class c extends g.Feature{constructor(){super(...arguments),this.removePointerDownListener=e.noop}onPointerDown(a){this.session=new i.PanSession(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,h.getContextWindow)(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:c,onPan:e,onPanEnd:f}=this.node.getProps();return{onSessionStart:b(a),onStart:b(c),onMove:e,onEnd:(a,b)=>{delete this.session,f&&d.frame.postRender(()=>f(a,b))}}}mount(){this.removePointerDownListener=(0,f.addPointerEvent)(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}}},440025:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cancelMicrotask:()=>c,microtask:()=>b});let{schedule:b,cancel:c}=(0,a.i(560276).createRenderBatcher)(queueMicrotask,!1)}},647216:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PresenceContext:()=>b});let b=(0,a.i(722851).createContext)(null)}},502394:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isPresent:()=>h,useIsPresent:()=>g,usePresence:()=>f});var d=a.i(722851),e=a.i(647216);function f(a=!0){let b=(0,d.useContext)(e.PresenceContext);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:g,register:h}=b,i=(0,d.useId)();(0,d.useEffect)(()=>{if(a)return h(i)},[a]);let j=(0,d.useCallback)(()=>a&&g&&g(i),[i,g,a]);return!c&&g?[!1,j]:[!0]}function g(){return h((0,d.useContext)(e.PresenceContext))}function h(a){return null===a||a.isPresent}},14275:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({LayoutGroupContext:()=>b});let b=(0,a.i(722851).createContext)({})}},188396:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SwitchLayoutGroupContext:()=>b});let b=(0,a.i(722851).createContext)({})}},209700:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({globalProjectionState:()=>b});let b={hasAnimatedSinceResize:!0,hasEverUpdated:!1}}},709980:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({correctBorderRadius:()=>b,pixelsToPercent:()=>e});var d=a.i(128779);function e(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let b={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!d.px.test(a))return a;else a=parseFloat(a);let c=e(a,b.target.x),f=e(a,b.target.y);return`${c}% ${f}%`}}}},330842:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({correctBoxShadow:()=>b});var d=a.i(504233),e=a.i(217969);let b={correct:(a,{treeScale:b,projectionDelta:c})=>{let f=d.complex.parse(a);if(f.length>5)return a;let g=d.complex.createTransformer(a),h=+("number"!=typeof f[0]),i=c.x.scale*b.x,j=c.y.scale*b.y;f[0+h]/=i,f[1+h]/=j;let k=(0,e.mixNumber)(i,j,.5);return"number"==typeof f[2+h]&&(f[2+h]/=k),"number"==typeof f[3+h]&&(f[3+h]/=k),g(f)}}}},173484:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({addScaleCorrector:()=>e,scaleCorrectors:()=>b});var d=a.i(863483);let b={};function e(a){for(let c in a)b[c]=a[c],(0,d.isCSSVariableName)(c)&&(b[c].isCSSVariable=!0)}}},651497:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MeasureLayout:()=>o});var d=a.i(674420),e=a.i(259255),f=a.i(440025),g=a.i(722851),h=a.i(502394),i=a.i(14275),j=a.i(188396),k=a.i(209700),l=a.i(709980),m=a.i(330842),n=a.i(173484);class b extends g.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:d,layoutId:e}=this.props,{projection:f}=a;(0,n.addScaleCorrector)(c),f&&(b.group&&b.group.add(f),d&&d.register&&e&&d.register(f),f.root.didUpdate(),f.addEventListener("animationComplete",()=>{this.safeToRemove()}),f.setOptions({...f.options,onExitComplete:()=>this.safeToRemove()})),k.globalProjectionState.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:f}=this.props,{projection:g}=c;return g&&(g.isPresent=f,d||a.layoutDependency!==b||void 0===b||a.isPresent!==f?g.willUpdate():this.safeToRemove(),a.isPresent!==f&&(f?g.promote():g.relegate()||e.frame.postRender(()=>{let a=g.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),f.microtask.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function o(a){let[c,e]=(0,h.usePresence)(),f=(0,g.useContext)(i.LayoutGroupContext);return(0,d.jsx)(b,{...a,layoutGroup:f,switchLayoutGroup:(0,g.useContext)(j.SwitchLayoutGroupContext),isPresent:c,safeToRemove:e})}let c={borderRadius:{...l.correctBorderRadius,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:l.correctBorderRadius,borderTopRightRadius:l.correctBorderRadius,borderBottomLeftRadius:l.correctBorderRadius,borderBottomRightRadius:l.correctBorderRadius,boxShadow:m.correctBoxShadow}}},197182:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isSVGElement:()=>e});var d=a.i(595324);function e(a){return(0,d.isObject)(a)&&"ownerSVGElement"in a}},179153:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isSVGSVGElement:()=>e});var d=a.i(197182);function e(a){return(0,d.isSVGElement)(a)&&"svg"===a.tagName}},271269:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({animateSingleValue:()=>g});var d=a.i(990148),e=a.i(121149),f=a.i(822839);function g(a,b,c){let g=(0,d.isMotionValue)(a)?a:(0,e.motionValue)(a);return g.start((0,f.animateMotionValue)("",g,b,c)),g.animation}},933875:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({compareByDepth:()=>b});let b=(a,b)=>a.depth-b.depth}},502450:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({FlatTree:()=>b});var d=a.i(740786),e=a.i(933875);class b{constructor(){this.children=[],this.isDirty=!1}add(a){(0,d.addUniqueItem)(this.children,a),this.isDirty=!0}remove(a){(0,d.removeItem)(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(e.compareByDepth),this.isDirty=!1,this.children.forEach(a)}}}},202059:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({delay:()=>g,delayInSeconds:()=>h});var d=a.i(153512),e=a.i(259255),f=a.i(638463);function g(a,b){let c=d.time.now(),f=({timestamp:d})=>{let g=d-c;g>=b&&((0,e.cancelFrame)(f),a(g-b))};return e.frame.setup(f,!0),()=>(0,e.cancelFrame)(f)}function h(a,b){return g(a,(0,f.secondsToMilliseconds)(b))}},806838:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({resolveMotionValue:()=>e});var d=a.i(990148);function e(a){return(0,d.isMotionValue)(a)?a.get():a}},16418:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({mixValues:()=>i});var d=a.i(217969),e=a.i(128779),f=a.i(847121),g=a.i(461655),h=a.i(498314);let b=["TopLeft","TopRight","BottomLeft","BottomRight"],c=b.length,l=a=>"string"==typeof a?parseFloat(a):a,m=a=>"number"==typeof a||e.px.test(a);function i(a,f,g,h,i,k){i?(a.opacity=(0,d.mixNumber)(0,g.opacity??1,n(h)),a.opacityExit=(0,d.mixNumber)(f.opacity??1,0,o(h))):k&&(a.opacity=(0,d.mixNumber)(f.opacity??1,g.opacity??1,h));for(let i=0;i<c;i++){let c=`border${b[i]}Radius`,k=j(f,c),n=j(g,c);(void 0!==k||void 0!==n)&&(k||(k=0),n||(n=0),0===k||0===n||m(k)===m(n)?(a[c]=Math.max((0,d.mixNumber)(l(k),l(n),h),0),(e.percent.test(n)||e.percent.test(k))&&(a[c]+="%")):a[c]=n)}(f.rotate||g.rotate)&&(a.rotate=(0,d.mixNumber)(f.rotate||0,g.rotate||0,h))}function j(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let n=k(0,.5,g.circOut),o=k(.5,.95,h.noop);function k(a,b,c){return d=>d<a?0:d>b?1:c((0,f.progress)(a,b,d))}}},564062:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b){a.min=b.min,a.max=b.max}function e(a,b){d(a.x,b.x),d(a.y,b.y)}function f(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}a.s({copyAxisDeltaInto:()=>f,copyAxisInto:()=>d,copyBoxInto:()=>e})},529032:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({removeAxisDelta:()=>h,removeAxisTransforms:()=>i,removeBoxTransforms:()=>j,removePointDelta:()=>g});var d=a.i(128779),e=a.i(217969),f=a.i(468106);function g(a,b,c,d,e){return a-=b,a=(0,f.scalePoint)(a,1/c,d),void 0!==e&&(a=(0,f.scalePoint)(a,1/e,d)),a}function h(a,b=0,c=1,f=.5,i,j=a,k=a){if(d.percent.test(b)&&(b=parseFloat(b),b=(0,e.mixNumber)(k.min,k.max,b/100)-k.min),"number"!=typeof b)return;let l=(0,e.mixNumber)(j.min,j.max,f);a===j&&(l-=b),a.min=g(a.min,b,c,l,i),a.max=g(a.max,b,c,l,i)}function i(a,b,[c,d,e],f,g){h(a,b[c],b[d],b[e],b.scale,f,g)}let b=["x","scaleX","originX"],c=["y","scaleY","originY"];function j(a,d,e,f){i(a.x,d,b,e?e.x:void 0,f?f.x:void 0),i(a.y,d,c,e?e.y:void 0,f?f.y:void 0)}}},202786:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({aspectRatio:()=>k,axisDeltaEquals:()=>l,axisEquals:()=>g,axisEqualsRounded:()=>i,boxEquals:()=>h,boxEqualsRounded:()=>j,isDeltaZero:()=>f});var d=a.i(518434);function e(a){return 0===a.translate&&1===a.scale}function f(a){return e(a.x)&&e(a.y)}function g(a,b){return a.min===b.min&&a.max===b.max}function h(a,b){return g(a.x,b.x)&&g(a.y,b.y)}function i(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function j(a,b){return i(a.x,b.x)&&i(a.y,b.y)}function k(a){return(0,d.calcLength)(a.x)/(0,d.calcLength)(a.y)}function l(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}},452508:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({NodeStack:()=>b});var d=a.i(740786);class b{constructor(){this.members=[]}add(a){(0,d.addUniqueItem)(this.members,a),a.scheduleRender()}remove(a){if((0,d.removeItem)(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}}},727514:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}a.s({buildProjectionTransform:()=>d})},840879:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cleanDirtyNodes:()=>N,createProjectionNode:()=>J,mixAxis:()=>Y,mixAxisDelta:()=>X,mixBox:()=>Z,propagateDirtyNodes:()=>M});var d=a.i(539322),e=a.i(197182),f=a.i(179153),g=a.i(722409),h=a.i(259255),i=a.i(153512),j=a.i(440025),k=a.i(765015),l=a.i(121149),m=a.i(217969),n=a.i(827277),o=a.i(95706),p=a.i(498314),q=a.i(271269),r=a.i(953288),s=a.i(502450),t=a.i(202059),u=a.i(806838),v=a.i(16418),w=a.i(564062),x=a.i(468106),y=a.i(518434),z=a.i(529032),A=a.i(35838),B=a.i(202786),C=a.i(452508),D=a.i(173484),E=a.i(727514),F=a.i(300650),G=a.i(525602),H=a.i(209700);let b={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},c=["","X","Y","Z"],ac={visibility:"hidden"},ad=0;function I(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function J({attachResizeListener:a,defaultParent:m,measureScroll:p,checkIsScrollRoot:F,resetTransform:J}){return class{constructor(a={},c=m?.()){this.id=ad++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.statsBuffer.value&&(b.nodes=b.calculatedTargetDeltas=b.calculatedProjections=0),this.nodes.forEach(M),this.nodes.forEach(T),this.nodes.forEach(U),this.nodes.forEach(N),d.statsBuffer.addProjectionMetrics&&d.statsBuffer.addProjectionMetrics(b)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new s.FlatTree)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new n.SubscriptionManager),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=(0,e.isSVGElement)(b)&&!(0,f.isSVGSVGElement)(b),this.instance=b;let{layoutId:c,layout:d,visualElement:h}=this.options;if(h&&!h.current&&h.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=()=>this.root.updateBlockedByResize=!1;a(b,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=(0,t.delay)(d,250),H.globalProjectionState.hasAnimatedSinceResize&&(H.globalProjectionState.hasAnimatedSinceResize=!1,this.nodes.forEach(S))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&h&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let e=this.options.transition||h.getDefaultTransition()||ae,{onLayoutAnimationStart:f,onLayoutAnimationComplete:i}=h.getProps(),j=!this.targetLayout||!(0,B.boxEqualsRounded)(this.targetLayout,d),k=!b&&c;if(this.options.layoutRoot||this.resumeFrom||k||b&&(j||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...(0,g.getValueTransition)(e,"layout"),onPlay:f,onComplete:i};(h.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,k)}else b||S(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,h.cancelFrame)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(V),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=(0,r.getOptimisedAppearId)(c);if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",h.frame,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(P);return}this.isUpdating||this.nodes.forEach(Q),this.isUpdating=!1,this.nodes.forEach(R),this.nodes.forEach(K),this.nodes.forEach(L),this.clearAllSnapshots();let a=i.time.now();h.frameData.delta=(0,o.clamp)(0,1e3/60,a-h.frameData.timestamp),h.frameData.timestamp=a,h.frameData.isProcessing=!0,h.frameSteps.update.process(h.frameData),h.frameSteps.preRender.process(h.frameData),h.frameSteps.render.process(h.frameData),h.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,j.microtask.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(O),this.sharedNodes.forEach(W)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,h.frame.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){h.frame.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,y.calcLength)(this.snapshot.measuredBox.x)||(0,y.calcLength)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,A.createBox)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=F(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:p(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!J)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!(0,B.isDeltaZero)(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,e=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||(0,G.hasTransform)(this.latestValues)||e)&&(J(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),_((b=d).x),_(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return(0,A.createBox)();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ab))){let{scroll:a}=this.root;a&&((0,x.translateAxis)(b.x,a.offset.x),(0,x.translateAxis)(b.y,a.offset.y))}return b}removeElementScroll(a){let b=(0,A.createBox)();if((0,w.copyBoxInto)(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&(0,w.copyBoxInto)(b,a),(0,x.translateAxis)(b.x,e.offset.x),(0,x.translateAxis)(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=(0,A.createBox)();(0,w.copyBoxInto)(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&(0,x.transformBox)(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),(0,G.hasTransform)(d.latestValues)&&(0,x.transformBox)(c,d.latestValues)}return(0,G.hasTransform)(this.latestValues)&&(0,x.transformBox)(c,this.latestValues),c}removeTransform(a){let b=(0,A.createBox)();(0,w.copyBoxInto)(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!(0,G.hasTransform)(c.latestValues))continue;(0,G.hasScale)(c.latestValues)&&c.updateSnapshot();let d=(0,A.createBox)(),e=c.measurePageBox();(0,w.copyBoxInto)(d,e),(0,z.removeBoxTransforms)(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return(0,G.hasTransform)(this.latestValues)&&(0,z.removeBoxTransforms)(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);let e=!!this.resumingFrom||this!==c;if(!(a||e&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:f,layoutId:g}=this.options;if(this.layout&&(f||g)){if(this.resolvedRelativeTargetAt=h.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,A.createBox)(),this.relativeTargetOrigin=(0,A.createBox)(),(0,y.calcRelativePosition)(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),(0,w.copyBoxInto)(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,A.createBox)(),this.targetWithTransforms=(0,A.createBox)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,y.calcRelativeBox)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):(0,w.copyBoxInto)(this.target,this.layout.layoutBox),(0,x.applyBoxDelta)(this.target,this.targetDelta)):(0,w.copyBoxInto)(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,A.createBox)(),this.relativeTargetOrigin=(0,A.createBox)(),(0,y.calcRelativePosition)(this.relativeTargetOrigin,this.target,a.target),(0,w.copyBoxInto)(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.statsBuffer.value&&b.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,G.hasScale)(this.parent.latestValues)||(0,G.has2DTranslate)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),c=!!this.resumingFrom||this!==a,e=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(e=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(e=!1),this.resolvedRelativeTargetAt===h.frameData.timestamp&&(e=!1),e)return;let{layout:f,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||g))return;(0,w.copyBoxInto)(this.layoutCorrected,this.layout.layoutBox);let i=this.treeScale.x,j=this.treeScale.y;(0,x.applyTreeDeltas)(this.layoutCorrected,this.treeScale,this.path,c),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=(0,A.createBox)());let{target:k}=a;if(!k){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?((0,w.copyAxisDeltaInto)(this.prevProjectionDelta.x,this.projectionDelta.x),(0,w.copyAxisDeltaInto)(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,y.calcBoxDelta)(this.projectionDelta,this.layoutCorrected,k,this.latestValues),this.treeScale.x===i&&this.treeScale.y===j&&(0,B.axisDeltaEquals)(this.projectionDelta.x,this.prevProjectionDelta.x)&&(0,B.axisDeltaEquals)(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",k)),d.statsBuffer.value&&b.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,A.createDelta)(),this.projectionDelta=(0,A.createDelta)(),this.projectionDeltaWithTransform=(0,A.createDelta)()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=(0,A.createDelta)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=(0,A.createBox)(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some($));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;X(g.x,a.x,d),X(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&((0,y.calcRelativePosition)(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Z(this.relativeTarget,this.relativeTargetOrigin,h,d),c&&(0,B.boxEquals)(this.relativeTarget,c)&&(this.isProjectionDirty=!1),c||(c=(0,A.createBox)()),(0,w.copyBoxInto)(c,this.relativeTarget)),i&&(this.animationValues=f,(0,v.mixValues)(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,h.cancelFrame)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=h.frame.update(()=>{H.globalProjectionState.hasAnimatedSinceResize=!0,k.activeAnimations.layout++,this.motionValue||(this.motionValue=(0,l.motionValue)(0)),this.currentAnimation=(0,q.animateSingleValue)(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{k.activeAnimations.layout--},onComplete:()=>{k.activeAnimations.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&aa(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||(0,A.createBox)();let b=(0,y.calcLength)(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=(0,y.calcLength)(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}(0,w.copyBoxInto)(b,c),(0,x.transformBox)(b,e),(0,y.calcBoxDelta)(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new C.NodeStack),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:d}=a;if((d.z||d.rotate||d.rotateX||d.rotateY||d.rotateZ||d.skewX||d.skewY)&&(b=!0),!b)return;let e={};d.z&&I("z",a,e,this.animationValues);for(let b=0;b<c.length;b++)I(`rotate${c[b]}`,a,e,this.animationValues),I(`skew${c[b]}`,a,e,this.animationValues);for(let b in a.render(),e)a.setStaticValue(b,e[b]),this.animationValues&&(this.animationValues[b]=e[b]);a.scheduleRender()}getProjectionStyles(a){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ac;let b={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,b.opacity="",b.pointerEvents=(0,u.resolveMotionValue)(a?.pointerEvents)||"",b.transform=c?c(this.latestValues,""):"none",b;let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){let b={};return this.options.layoutId&&(b.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,b.pointerEvents=(0,u.resolveMotionValue)(a?.pointerEvents)||""),this.hasProjected&&!(0,G.hasTransform)(this.latestValues)&&(b.transform=c?c({},""):"none",this.hasProjected=!1),b}let e=d.animationValues||d.latestValues;this.applyTransformsToTarget(),b.transform=(0,E.buildProjectionTransform)(this.projectionDeltaWithTransform,this.treeScale,e),c&&(b.transform=c(e,b.transform));let{x:f,y:g}=this.projectionDelta;for(let a in b.transformOrigin=`${100*f.origin}% ${100*g.origin}% 0`,d.animationValues?b.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:b.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,D.scaleCorrectors){if(void 0===e[a])continue;let{correct:c,applyTo:f,isCSSVariable:g}=D.scaleCorrectors[a],h="none"===b.transform?e[a]:c(e[a],d);if(f){let a=f.length;for(let c=0;c<a;c++)b[f[c]]=h}else g?this.options.visualElement.renderState.vars[a]=h:b[a]=h}return this.options.layoutId&&(b.pointerEvents=d===this?(0,u.resolveMotionValue)(a?.pointerEvents)||"":"none"),b}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(P),this.root.sharedNodes.clear()}}}function K(a){a.updateLayout()}function L(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?(0,F.eachAxis)(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=(0,y.calcLength)(d);d.min=c[a].min,d.max=d.min+e}):aa(e,b.layoutBox,c)&&(0,F.eachAxis)(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=(0,y.calcLength)(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=(0,A.createDelta)();(0,y.calcBoxDelta)(g,c,b.layoutBox);let h=(0,A.createDelta)();f?(0,y.calcBoxDelta)(h,a.applyTransform(d,!0),b.measuredBox):(0,y.calcBoxDelta)(h,c,b.layoutBox);let i=!(0,B.isDeltaZero)(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=(0,A.createBox)();(0,y.calcRelativePosition)(g,b.layoutBox,e.layoutBox);let h=(0,A.createBox)();(0,y.calcRelativePosition)(h,c,f.layoutBox),(0,B.boxEqualsRounded)(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function M(a){d.statsBuffer.value&&b.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function N(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function O(a){a.clearSnapshot()}function P(a){a.clearMeasurements()}function Q(a){a.isLayoutDirty=!1}function R(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function S(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function T(a){a.resolveTargetDelta()}function U(a){a.calcProjection()}function V(a){a.resetSkewAndRotation()}function W(a){a.removeLeadSnapshot()}function X(a,b,c){a.translate=(0,m.mixNumber)(b.translate,0,c),a.scale=(0,m.mixNumber)(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function Y(a,b,c,d){a.min=(0,m.mixNumber)(b.min,c.min,d),a.max=(0,m.mixNumber)(b.max,c.max,d)}function Z(a,b,c,d){Y(a.x,b.x,c.x,d),Y(a.y,b.y,c.y,d)}function $(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let ae={duration:.45,ease:[.4,0,.1,1]},af=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),ag=af("applewebkit/")&&!af("chrome/")?Math.round:p.noop;function _(a){a.min=ag(a.min),a.max=ag(a.max)}function aa(a,b,c){return"position"===a||"preserve-aspect"===a&&!(0,y.isNear)((0,B.aspectRatio)(b),(0,B.aspectRatio)(c),.2)}function ab(a){return a!==a.root&&a.scroll?.wasRoot}}},896308:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DocumentProjectionNode:()=>b});var d=a.i(840879),e=a.i(676407);let b=(0,d.createProjectionNode)({attachResizeListener:(a,b)=>(0,e.addDomEvent)(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0})}},410738:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({HTMLProjectionNode:()=>c,rootProjectionNode:()=>b});var d=a.i(840879),e=a.i(896308);let b={current:void 0},c=(0,d.createProjectionNode)({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!b.current){let a=new e.DocumentProjectionNode({});a.mount(window),a.setOptions({layoutScroll:!0}),b.current=a}return b.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position})}},2392:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({drag:()=>b});var d=a.i(807978),e=a.i(13612),f=a.i(651497),g=a.i(410738);let b={pan:{Feature:e.PanGesture},drag:{Feature:d.DragGesture,ProjectionNode:g.HTMLProjectionNode,MeasureLayout:f.MeasureLayout}}}},950248:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let d=document;b&&(d=b.current);let e=c?.[a]??d.querySelectorAll(a);return e?Array.from(e):[]}return Array.from(a)}a.s({resolveElements:()=>d})},665614:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({setupGesture:()=>e});var d=a.i(950248);function e(a,b){let c=(0,d.resolveElements)(a),e=new AbortController;return[c,{passive:!0,...b,signal:e.signal},()=>e.abort()]}},977117:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({hover:()=>g});var d=a.i(514832),e=a.i(665614);function f(a){return!("touch"===a.pointerType||(0,d.isDragActive)())}function g(a,b,c={}){let[d,h,i]=(0,e.setupGesture)(a,c),j=a=>{if(!f(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let e=a=>{f(a)&&(d(a),c.removeEventListener("pointerleave",e))};c.addEventListener("pointerleave",e,h)};return d.forEach(a=>{a.addEventListener("pointerenter",j,h)}),i}},799343:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({HoverGesture:()=>b});var d=a.i(977117),e=a.i(259255),f=a.i(961381),g=a.i(94176);function h(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let g=d["onHover"+c];g&&e.frame.postRender(()=>g(b,(0,f.extractEventInfo)(b)))}class b extends g.Feature{mount(){let{current:a}=this.node;a&&(this.unmount=(0,d.hover)(a,(a,b)=>(h(this.node,b,"Start"),a=>h(this.node,a,"End"))))}unmount(){}}}},27407:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({FocusGesture:()=>b});var d=a.i(272218),e=a.i(676407),f=a.i(94176);class b extends f.Feature{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,d.pipe)((0,e.addDomEvent)(this.node.current,"focus",()=>this.onFocus()),(0,e.addDomEvent)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}}},877321:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isNodeOrChild:()=>b});let b=(a,c)=>!!c&&(a===c||b(a,c.parentElement))}},607220:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isElementKeyboardAccessible:()=>d});let b=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function d(a){return b.has(a.tagName)||-1!==a.tabIndex}}},422511:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isPressing:()=>b});let b=new WeakSet}},968260:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({enableKeyboardPress:()=>b});var d=a.i(422511);function e(a){return b=>{"Enter"===b.key&&a(b)}}function f(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}let b=(a,b)=>{let c=a.currentTarget;if(!c)return;let g=e(()=>{if(d.isPressing.has(c))return;f(c,"down");let a=e(()=>{f(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>f(c,"cancel"),b)});c.addEventListener("keydown",g,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",g),b)}}},526984:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({press:()=>m});var d=a.i(949208),e=a.i(514832),f=a.i(877321),g=a.i(157960),h=a.i(665614),i=a.i(607220),j=a.i(968260),k=a.i(422511);function l(a){return(0,g.isPrimaryPointer)(a)&&!(0,e.isDragActive)()}function m(a,b,c={}){let[e,g,n]=(0,h.setupGesture)(a,c),o=a=>{let d=a.currentTarget;if(!l(a))return;k.isPressing.add(d);let e=b(d,a),h=(a,b)=>{window.removeEventListener("pointerup",i),window.removeEventListener("pointercancel",j),k.isPressing.has(d)&&k.isPressing.delete(d),l(a)&&"function"==typeof e&&e(a,{success:b})},i=a=>{h(a,d===window||d===document||c.useGlobalTarget||(0,f.isNodeOrChild)(d,a.target))},j=a=>{h(a,!1)};window.addEventListener("pointerup",i,g),window.addEventListener("pointercancel",j,g)};return e.forEach(a=>{(c.useGlobalTarget?window:a).addEventListener("pointerdown",o,g),(0,d.isHTMLElement)(a)&&(a.addEventListener("focus",a=>(0,j.enableKeyboardPress)(a,g)),(0,i.isElementKeyboardAccessible)(a)||a.hasAttribute("tabindex")||(a.tabIndex=0))}),n}},750525:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PressGesture:()=>b});var d=a.i(526984),e=a.i(259255),f=a.i(961381),g=a.i(94176);function h(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let g=d["onTap"+("End"===c?"":c)];g&&e.frame.postRender(()=>g(b,(0,f.extractEventInfo)(b)))}class b extends g.Feature{mount(){let{current:a}=this.node;a&&(this.unmount=(0,d.press)(a,(a,b)=>(h(this.node,b,"Start"),(a,{success:b})=>h(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}}},5235:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({observeIntersection:()=>d});let b=new WeakMap,c=new WeakMap,e=a=>{let c=b.get(a.target);c&&c(a)},f=a=>{a.forEach(e)};function d(a,d,e){let g=function({root:a,...b}){let d=a||document;c.has(d)||c.set(d,{});let e=c.get(d),g=JSON.stringify(b);return e[g]||(e[g]=new IntersectionObserver(f,{root:a,...b})),e[g]}(d);return b.set(a,e),g.observe(a),()=>{b.delete(a),g.unobserve(a)}}}},23828:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({InViewFeature:()=>c});var d=a.i(94176),e=a.i(5235);let b={some:0,all:1};class c extends d.Feature{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:c,margin:d,amount:f="some",once:g}=a,h={root:c?c.current:void 0,rootMargin:d,threshold:"number"==typeof f?f:b[f]};return(0,e.observeIntersection)(this.node.current,h,a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,g&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),e=b?c:d;e&&e(a)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}}},240172:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({gestureAnimations:()=>b});var d=a.i(799343),e=a.i(27407),f=a.i(750525);let b={inView:{Feature:a.i(23828).InViewFeature},tap:{Feature:f.PressGesture},focus:{Feature:e.FocusGesture},hover:{Feature:d.HoverGesture}}}},412926:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({layout:()=>b});var d=a.i(410738),e=a.i(651497);let b={layout:{ProjectionNode:d.HTMLProjectionNode,MeasureLayout:e.MeasureLayout}}}},81421:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({LazyContext:()=>b});let b=(0,a.i(722851).createContext)({strict:!1})}},602698:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MotionConfigContext:()=>b});let b=(0,a.i(722851).createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"})}},104968:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({MotionContext:()=>b});let b=(0,a.i(722851).createContext)({})}},481756:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isControllingVariants:()=>g,isVariantNode:()=>h});var d=a.i(188221),e=a.i(997226),f=a.i(21279);function g(a){return(0,d.isAnimationControls)(a.animate)||f.variantProps.some(b=>(0,e.isVariantLabel)(a[b]))}function h(a){return!!(g(a)||a.variants)}},145185:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getCurrentTreeVariants:()=>f});var d=a.i(481756),e=a.i(997226);function f(a,b){if((0,d.isControllingVariants)(a)){let{initial:b,animate:c}=a;return{initial:!1===b||(0,e.isVariantLabel)(b)?b:void 0,animate:(0,e.isVariantLabel)(c)?c:void 0}}return!1!==a.inherit?b:{}}},584249:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({useCreateMotionContext:()=>g});var d=a.i(722851),e=a.i(104968),f=a.i(145185);function g(a){let{initial:b,animate:c}=(0,f.getCurrentTreeVariants)(a,(0,d.useContext)(e.MotionContext));return(0,d.useMemo)(()=>({initial:b,animate:c}),[h(b),h(c)])}function h(a){return Array.isArray(a)?a.join(" "):a}},910521:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isBrowser:()=>b});let b="undefined"!=typeof window}},140460:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({featureDefinitions:()=>c});let b={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},c={};for(let a in b)c[a]={isEnabled:c=>b[a].some(a=>!!c[a])}}},187869:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({loadFeatures:()=>e});var d=a.i(140460);function e(a){for(let b in a)d.featureDefinitions[b]={...d.featureDefinitions[b],...a[b]}}},25387:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({motionComponentSymbol:()=>b});let b=Symbol.for("motionComponentSymbol")}},609188:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({useMotionRef:()=>f});var d=a.i(722851),e=a.i(947340);function f(a,b,c){return(0,d.useCallback)(d=>{d&&a.onMount&&a.onMount(d),b&&(d?b.mount(d):b.unmount()),c&&("function"==typeof c?c(d):(0,e.isRefObject)(c)&&(c.current=d))},[b])}},31751:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({useIsomorphicLayoutEffect:()=>b});var d=a.i(722851);let b=a.i(910521).isBrowser?d.useLayoutEffect:d.useEffect}},407975:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({useVisualElement:()=>n});var d=a.i(440025),e=a.i(722851),f=a.i(198265),g=a.i(81421),h=a.i(602698),i=a.i(104968),j=a.i(647216),k=a.i(188396),l=a.i(947340),m=a.i(31751);function n(a,b,c,n,o){let{visualElement:p}=(0,e.useContext)(i.MotionContext),q=(0,e.useContext)(g.LazyContext),r=(0,e.useContext)(j.PresenceContext),s=(0,e.useContext)(h.MotionConfigContext).reducedMotion,t=(0,e.useRef)(null);n=n||q.renderer,!t.current&&n&&(t.current=n(a,{visualState:b,parent:p,props:c,presenceContext:r,blockInitialAnimation:!!r&&!1===r.initial,reducedMotionConfig:s}));let u=t.current,v=(0,e.useContext)(k.SwitchLayoutGroupContext);u&&!u.projection&&o&&("html"===u.type||"svg"===u.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j,layoutCrossfade:k}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&(0,l.isRefObject)(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,crossfade:k,layoutScroll:i,layoutRoot:j})}(t.current,c,o,v);let w=(0,e.useRef)(!1);(0,e.useInsertionEffect)(()=>{u&&w.current&&u.update(c,r)});let x=c[f.optimizedAppearDataAttribute],y=(0,e.useRef)(!!x&&!window.MotionHandoffIsComplete?.(x)&&window.MotionHasOptimisedAnimation?.(x));return(0,m.useIsomorphicLayoutEffect)(()=>{u&&(w.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),d.microtask.render(u.render),y.current&&u.animationState&&u.animationState.animateChanges())}),(0,e.useEffect)(()=>{u&&(!y.current&&u.animationState&&u.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(x)}),y.current=!1))}),u}},785044:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createRendererMotionComponent:()=>q});var d=a.i(674420),e=a.i(722851),f=a.i(14275),g=a.i(81421),h=a.i(602698),i=a.i(104968),j=a.i(584249),k=a.i(910521),l=a.i(140460),m=a.i(187869),n=a.i(25387),o=a.i(609188),p=a.i(407975);function q({preloadedFeatures:a,createVisualElement:b,useRender:c,useVisualState:q,Component:r}){function s(a,m){let n,s={...(0,e.useContext)(h.MotionConfigContext),...a,layoutId:function({layoutId:a}){let b=(0,e.useContext)(f.LayoutGroupContext).id;return b&&void 0!==a?b+"-"+a:a}(a)},{isStatic:t}=s,u=(0,j.useCreateMotionContext)(a),v=q(a,t);if(!t&&k.isBrowser){var w,x;w=0,x=0,(0,e.useContext)(g.LazyContext).strict;let a=function(a){let{drag:b,layout:c}=l.featureDefinitions;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:b?.isEnabled(a)||c?.isEnabled(a)?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(s);n=a.MeasureLayout,u.visualElement=(0,p.useVisualElement)(r,v,s,b,a.ProjectionNode)}return(0,d.jsxs)(i.MotionContext.Provider,{value:u,children:[n&&u.visualElement?(0,d.jsx)(n,{visualElement:u.visualElement,...s}):null,c(r,a,(0,o.useMotionRef)(v,u.visualElement,m),v,t,u.visualElement)]})}a&&(0,m.loadFeatures)(a),s.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let t=(0,e.forwardRef)(s);return t[n.motionComponentSymbol]=r,t}},110789:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isForcedMotionValue:()=>f});var d=a.i(237979),e=a.i(173484);function f(a,{layout:b,layoutId:c}){return d.transformProps.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!e.scaleCorrectors[a]||"opacity"===a)}},940953:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getValueAsType:()=>b});let b=(a,b)=>b&&"number"==typeof a?b.transform(a):a}},956483:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({int:()=>b});let b={...a.i(846550).number,transform:Math.round}}},689523:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({transformValueTypes:()=>b});var d=a.i(846550),e=a.i(128779);let b={rotate:e.degrees,rotateX:e.degrees,rotateY:e.degrees,rotateZ:e.degrees,scale:d.scale,scaleX:d.scale,scaleY:d.scale,scaleZ:d.scale,skew:e.degrees,skewX:e.degrees,skewY:e.degrees,distance:e.px,translateX:e.px,translateY:e.px,translateZ:e.px,x:e.px,y:e.px,z:e.px,perspective:e.px,transformPerspective:e.px,opacity:d.alpha,originX:e.progressPercentage,originY:e.progressPercentage,originZ:e.px}}},318684:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({numberValueTypes:()=>b});var d=a.i(956483),e=a.i(846550),f=a.i(128779),g=a.i(689523);let b={borderWidth:f.px,borderTopWidth:f.px,borderRightWidth:f.px,borderBottomWidth:f.px,borderLeftWidth:f.px,borderRadius:f.px,radius:f.px,borderTopLeftRadius:f.px,borderTopRightRadius:f.px,borderBottomRightRadius:f.px,borderBottomLeftRadius:f.px,width:f.px,maxWidth:f.px,height:f.px,maxHeight:f.px,top:f.px,right:f.px,bottom:f.px,left:f.px,padding:f.px,paddingTop:f.px,paddingRight:f.px,paddingBottom:f.px,paddingLeft:f.px,margin:f.px,marginTop:f.px,marginRight:f.px,marginBottom:f.px,marginLeft:f.px,backgroundPositionX:f.px,backgroundPositionY:f.px,...g.transformValueTypes,zIndex:d.int,fillOpacity:e.alpha,strokeOpacity:e.alpha,numOctaves:d.int}}},313966:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({buildTransform:()=>g});var d=a.i(237979),e=a.i(940953),f=a.i(318684);let b={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},c=d.transformPropOrder.length;function g(a,g,h){let i="",j=!0;for(let k=0;k<c;k++){let c=d.transformPropOrder[k],l=a[c];if(void 0===l)continue;let m=!0;if(!(m="number"==typeof l?l===+!!c.startsWith("scale"):0===parseFloat(l))||h){let a=(0,e.getValueAsType)(l,f.numberValueTypes[c]);if(!m){j=!1;let d=b[c]||c;i+=`${d}(${a}) `}h&&(g[c]=a)}}return i=i.trim(),h?i=h(g,j?"":i):j&&(i="none"),i}}},999040:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({buildHTMLStyles:()=>i});var d=a.i(237979),e=a.i(863483),f=a.i(940953),g=a.i(318684),h=a.i(313966);function i(a,b,c){let{style:i,vars:j,transformOrigin:k}=a,l=!1,m=!1;for(let a in b){let c=b[a];if(d.transformProps.has(a)){l=!0;continue}if((0,e.isCSSVariableName)(a)){j[a]=c;continue}{let b=(0,f.getValueAsType)(c,g.numberValueTypes[a]);a.startsWith("origin")?(m=!0,k[a]=b):i[a]=b}}if(!b.transform&&(l||c?i.transform=(0,h.buildTransform)(b,a.transform,c):i.transform&&(i.transform="none")),m){let{originX:a="50%",originY:b="50%",originZ:c=0}=k;i.transformOrigin=`${a} ${b} ${c}`}}},406044:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createHtmlRenderState:()=>b});let b=()=>({style:{},transform:{},transformOrigin:{},vars:{}})}},990118:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({copyRawValuesOnly:()=>i,useHTMLProps:()=>j});var d=a.i(990148),e=a.i(722851),f=a.i(110789),g=a.i(999040),h=a.i(406044);function i(a,b,c){for(let e in b)(0,d.isMotionValue)(b[e])||(0,f.isForcedMotionValue)(e,c)||(a[e]=b[e])}function j(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return i(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,e.useMemo)(()=>{let c=(0,h.createHtmlRenderState)();return(0,g.buildHTMLStyles)(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c}},892125:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({buildSVGPath:()=>e});var d=a.i(128779);let b={offset:"stroke-dashoffset",array:"stroke-dasharray"},c={offset:"strokeDashoffset",array:"strokeDasharray"};function e(a,f,g=1,h=0,i=!0){a.pathLength=1;let j=i?b:c;a[j.offset]=d.px.transform(-h);let k=d.px.transform(f),l=d.px.transform(g);a[j.array]=`${k} ${l}`}}},620848:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({buildSVGAttrs:()=>f});var d=a.i(999040),e=a.i(892125);function f(a,{attrX:b,attrY:c,attrScale:f,pathLength:g,pathSpacing:h=1,pathOffset:i=0,...j},k,l,m){if((0,d.buildHTMLStyles)(a,j,l),k){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:n,style:o}=a;n.transform&&(o.transform=n.transform,delete n.transform),(o.transform||n.transformOrigin)&&(o.transformOrigin=n.transformOrigin??"50% 50%",delete n.transformOrigin),o.transform&&(o.transformBox=m?.transformBox??"fill-box",delete n.transformBox),void 0!==b&&(n.x=b),void 0!==c&&(n.y=c),void 0!==f&&(n.scale=f),void 0!==g&&(0,e.buildSVGPath)(n,g,h,i,!1)}},546239:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSvgRenderState:()=>b});var d=a.i(406044);let b=()=>({...(0,d.createHtmlRenderState)(),attrs:{}})}},408590:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isSVGTag:()=>b});let b=a=>"string"==typeof a&&"svg"===a.toLowerCase()}},755431:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({useSVGProps:()=>i});var d=a.i(722851),e=a.i(990118),f=a.i(620848),g=a.i(546239),h=a.i(408590);function i(a,b,c,i){let j=(0,d.useMemo)(()=>{let c=(0,g.createSvgRenderState)();return(0,f.buildSVGAttrs)(c,b,(0,h.isSVGTag)(i),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};(0,e.copyRawValuesOnly)(b,a.style,a),j.style={...b,...j.style}}return j}},375591:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isValidMotionProp:()=>d});let b=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function d(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||b.has(a)}}},531044:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({filterProps:()=>f,loadExternalIsValidProp:()=>e});var d=a.i(375591);let b=a=>!(0,d.isValidMotionProp)(a);function e(a){a&&(b=b=>b.startsWith("on")?!(0,d.isValidMotionProp)(b):a(b))}try{e((()=>{let a=Error("Cannot find module '@emotion/is-prop-valid'");throw a.code="MODULE_NOT_FOUND",a})().default)}catch{}function f(a,c,e){let f={};for(let g in a)("values"!==g||"object"!=typeof a.values)&&(b(g)||!0===e&&(0,d.isValidMotionProp)(g)||!c&&!(0,d.isValidMotionProp)(g)||a.draggable&&g.startsWith("onDrag"))&&(f[g]=a[g]);return f}}},915963:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({lowercaseSVGElements:()=>b});let b=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"]}},989769:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isSVGComponent:()=>e});var d=a.i(915963);function e(a){if("string"!=typeof a||a.includes("-"));else if(d.lowercaseSVGElements.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}},554197:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createUseRender:()=>j});var d=a.i(990148),e=a.i(722851),f=a.i(990118),g=a.i(755431),h=a.i(531044),i=a.i(989769);function j(a=!1){return(b,c,j,{latestValues:k},l)=>{let m=((0,i.isSVGComponent)(b)?g.useSVGProps:f.useHTMLProps)(c,k,l,b),n=(0,h.filterProps)(c,"string"==typeof b,a),o=b!==e.Fragment?{...n,...m,ref:j}:{},{children:p}=c,q=(0,e.useMemo)(()=>(0,d.isMotionValue)(p)?p.get():p,[p]);return(0,e.createElement)(b,{...o,children:q})}}},481138:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({useConstant:()=>e});var d=a.i(722851);function e(a){let b=(0,d.useRef)(null);return null===b.current&&(b.current=a()),b.current}},689743:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({makeUseVisualState:()=>b});var d=a.i(722851),e=a.i(188221),f=a.i(104968),g=a.i(647216),h=a.i(481756),i=a.i(69840),j=a.i(481138),k=a.i(806838);let b=a=>(b,c)=>{let l=(0,d.useContext)(f.MotionContext),m=(0,d.useContext)(g.PresenceContext),n=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,f){return{latestValues:function(a,b,c,d){let f={},g=d(a,{});for(let a in g)f[a]=(0,k.resolveMotionValue)(g[a]);let{initial:j,animate:l}=a,m=(0,h.isControllingVariants)(a),n=(0,h.isVariantNode)(a);b&&n&&!m&&!1!==a.inherit&&(void 0===j&&(j=b.initial),void 0===l&&(l=b.animate));let o=!!c&&!1===c.initial,p=(o=o||!1===j)?l:j;if(p&&"boolean"!=typeof p&&!(0,e.isAnimationControls)(p)){let b=Array.isArray(p)?p:[p];for(let c=0;c<b.length;c++){let d=(0,i.resolveVariantFromProps)(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=o?b.length-1:0;b=b[a]}null!==b&&(f[a]=b)}for(let b in a)f[b]=a[b]}}}return f}(c,d,f,a),renderState:b()}})(a,b,l,m);return c?n():(0,j.useConstant)(n)}}},272424:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({scrapeMotionValuesFromProps:()=>f});var d=a.i(990148),e=a.i(110789);function f(a,b,c){let{style:f}=a,g={};for(let h in f)((0,d.isMotionValue)(f[h])||b.style&&(0,d.isMotionValue)(b.style[h])||(0,e.isForcedMotionValue)(h,a)||c?.getValue(h)?.liveStyle!==void 0)&&(g[h]=f[h]);return g}},662123:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({htmlMotionConfig:()=>b});var d=a.i(689743),e=a.i(272424),f=a.i(406044);let b={useVisualState:(0,d.makeUseVisualState)({scrapeMotionValuesFromProps:e.scrapeMotionValuesFromProps,createRenderState:f.createHtmlRenderState})}}},666018:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({scrapeMotionValuesFromProps:()=>g});var d=a.i(990148),e=a.i(237979),f=a.i(272424);function g(a,b,c){let g=(0,f.scrapeMotionValuesFromProps)(a,b,c);for(let c in a)((0,d.isMotionValue)(a[c])||(0,d.isMotionValue)(b[c]))&&(g[-1!==e.transformPropOrder.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return g}},870716:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({svgMotionConfig:()=>b});var d=a.i(689743),e=a.i(546239),f=a.i(666018);let b={useVisualState:(0,d.makeUseVisualState)({scrapeMotionValuesFromProps:f.scrapeMotionValuesFromProps,createRenderState:e.createSvgRenderState})}}},909244:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createMotionComponentFactory:()=>i});var d=a.i(785044),e=a.i(554197),f=a.i(989769),g=a.i(662123),h=a.i(870716);function i(a,b){return function(c,{forwardMotionProps:i}={forwardMotionProps:!1}){let j={...(0,f.isSVGComponent)(c)?h.svgMotionConfig:g.htmlMotionConfig,preloadedFeatures:a,useRender:(0,e.createUseRender)(i),createVisualElement:b,Component:c};return(0,d.createRendererMotionComponent)(j)}}},55866:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({auto:()=>b});let b={test:a=>"auto"===a,parse:a=>a}}},579918:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({testValueType:()=>b});let b=a=>b=>b.test(a)}},331873:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({dimensionValueTypes:()=>b,findDimensionValueType:()=>c});var d=a.i(55866),e=a.i(846550),f=a.i(128779),g=a.i(579918);let b=[e.number,f.px,f.percent,f.degrees,f.vw,f.vh,d.auto],c=a=>b.find((0,g.testValueType)(a))}},729292:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isNumericalString:()=>b});let b=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a)}},920735:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getVariableValue:()=>function a(b,c,h=1){(0,d.invariant)(h<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`);let[i,j]=g(b);if(!i)return;let k=window.getComputedStyle(c).getPropertyValue(i);if(k){let a=k.trim();return(0,e.isNumericalString)(a)?parseFloat(a):a}return(0,f.isCSSVariableToken)(j)?a(j,c,h+1):j},parseCSSVariable:()=>g});var d=a.i(761310),e=a.i(729292),f=a.i(863483);let b=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function g(a){let c=b.exec(a);if(!c)return[,];let[,d,e,f]=c;return[`--${d??e}`,f]}}},596549:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({isZeroValueString:()=>b});let b=a=>/^0[^.\s]+$/u.test(a)}},16847:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({isNone:()=>e});var d=a.i(596549);function e(a){return"number"==typeof a?0===a:null===a||"none"===a||"0"===a||(0,d.isZeroValueString)(a)}},38727:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({filter:()=>g});var d=a.i(504233),e=a.i(222277);let b=new Set(["brightness","contrast","saturate","opacity"]);function f(a){let[c,d]=a.slice(0,-1).split("(");if("drop-shadow"===c)return a;let[f]=d.match(e.floatRegex)||[];if(!f)return a;let g=d.replace(f,""),h=+!!b.has(c);return f!==d&&(h*=100),c+"("+h+g+")"}let c=/\b([a-z-]*)\(.*?\)/gu,g={...d.complex,getAnimatableNone:a=>{let b=a.match(c);return b?b.map(f).join(" "):a}}}},780040:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({defaultValueTypes:()=>b,getDefaultValueType:()=>c});var d=a.i(322e3),e=a.i(38727);let b={...a.i(318684).numberValueTypes,color:d.color,backgroundColor:d.color,outlineColor:d.color,fill:d.color,stroke:d.color,borderColor:d.color,borderTopColor:d.color,borderRightColor:d.color,borderBottomColor:d.color,borderLeftColor:d.color,filter:e.filter,WebkitFilter:e.filter},c=a=>b[a]}},714959:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({getAnimatableNone:()=>g});var d=a.i(504233),e=a.i(38727),f=a.i(780040);function g(a,b){let c=(0,f.getDefaultValueType)(a);return c!==e.filter&&(c=d.complex),c.getAnimatableNone?c.getAnimatableNone(b):void 0}},302041:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({makeNoneKeyframesAnimatable:()=>f});var d=a.i(504233),e=a.i(714959);let b=new Set(["auto","none","0"]);function f(a,c,f){let g,h=0;for(;h<a.length&&!g;){let c=a[h];"string"==typeof c&&!b.has(c)&&(0,d.analyseComplexValue)(c).values.length&&(g=a[h]),h++}if(g&&f)for(let b of c)a[b]=(0,e.getAnimatableNone)(f,g)}}},775276:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DOMKeyframesResolver:()=>b});var d=a.i(605667),e=a.i(331873),f=a.i(920735),g=a.i(863483),h=a.i(382386),i=a.i(16847),j=a.i(302041),k=a.i(479779);class b extends h.KeyframeResolver{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&(d=d.trim(),(0,g.isCSSVariableToken)(d))){let e=(0,f.getVariableValue)(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!d.positionalKeys.has(c)||2!==a.length)return;let[h,i]=a,j=(0,e.findDimensionValueType)(h),l=(0,e.findDimensionValueType)(i);if(j!==l)if((0,k.isNumOrPxType)(j)&&(0,k.isNumOrPxType)(l))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else k.positionalValues[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++)(null===a[b]||(0,i.isNone)(a[b]))&&c.push(b);c.length&&(0,j.makeNoneKeyframesAnimatable)(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=k.positionalValues[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=k.positionalValues[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}}},468726:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({findValueType:()=>c});var d=a.i(322e3),e=a.i(504233),f=a.i(331873),g=a.i(579918);let b=[...f.dimensionValueTypes,d.color,e.complex],c=a=>b.find((0,g.testValueType)(a))}},209396:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasReducedMotionListener:()=>c,prefersReducedMotion:()=>b});let b={current:null},c={current:!1}}},777483:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({initPrefersReducedMotion:()=>f});var d=a.i(910521),e=a.i(209396);function f(){if(e.hasReducedMotionListener.current=!0,d.isBrowser)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>e.prefersReducedMotion.current=a.matches;a.addListener(b),b()}else e.prefersReducedMotion.current=!1}},18663:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({visualElementStore:()=>b});let b=new WeakMap}},574196:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({updateMotionValuesFromProps:()=>f});var d=a.i(990148),e=a.i(121149);function f(a,b,c){for(let f in b){let g=b[f],h=c[f];if((0,d.isMotionValue)(g))a.addValue(f,g);else if((0,d.isMotionValue)(h))a.addValue(f,(0,e.motionValue)(g,{owner:a}));else if(h!==g)if(a.hasValue(f)){let b=a.getValue(f);!0===b.liveStyle?b.jump(g):b.hasAnimated||b.set(g)}else{let b=a.getStaticValue(f);a.addValue(f,(0,e.motionValue)(void 0!==b?b:g,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}},571515:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({VisualElement:()=>c});var d=a.i(382386),e=a.i(153512),f=a.i(259255),g=a.i(990148),h=a.i(237979),i=a.i(121149),j=a.i(468726),k=a.i(504233),l=a.i(714959),m=a.i(729292),n=a.i(596549),o=a.i(827277),p=a.i(140460),q=a.i(35838),r=a.i(777483),s=a.i(209396),t=a.i(18663),u=a.i(481756),v=a.i(574196),w=a.i(69840);let b=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class c{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:h,blockInitialAnimation:i,visualState:j},k={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=d.KeyframeResolver,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=e.time.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,f.frame.render(this.render,!1,!0))};let{latestValues:l,renderState:m}=j;this.latestValues=l,this.baseTarget={...l},this.initialValues=b.initial?{...l}:{},this.renderState=m,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=h,this.options=k,this.blockInitialAnimation=!!i,this.isControllingVariants=(0,u.isControllingVariants)(b),this.isVariantNode=(0,u.isVariantNode)(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:n,...o}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in o){let b=o[a];void 0!==l[a]&&(0,g.isMotionValue)(b)&&b.set(l[a],!1)}}mount(a){this.current=a,t.visualElementStore.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),s.hasReducedMotionListener.current||(0,r.initPrefersReducedMotion)(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||s.prefersReducedMotion.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),(0,f.cancelFrame)(this.notifyUpdate),(0,f.cancelFrame)(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=h.transformProps.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&f.frame.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0)}),g=b.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),g(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in p.featureDefinitions){let b=p.featureDefinitions[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,q.createBox)()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,c){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=c;for(let c=0;c<b.length;c++){let d=b[c];this.propEventSubscriptions[d]&&(this.propEventSubscriptions[d](),delete this.propEventSubscriptions[d]);let e=a["on"+d];e&&(this.propEventSubscriptions[d]=this.on(d,e))}this.prevMotionValues=(0,v.updateMotionValuesFromProps)(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=(0,i.motionValue)(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];return null!=c&&("string"==typeof c&&((0,m.isNumericalString)(c)||(0,n.isZeroValueString)(c))?c=parseFloat(c):!(0,j.findValueType)(c)&&k.complex.test(b)&&(c=(0,l.getAnimatableNone)(a,b)),this.setBaseTarget(a,(0,g.isMotionValue)(c)?c.get():c)),(0,g.isMotionValue)(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=(0,w.resolveVariantFromProps)(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||(0,g.isMotionValue)(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new o.SubscriptionManager),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}}}},537554:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DOMVisualElement:()=>b});var d=a.i(775276),e=a.i(990148),f=a.i(571515);class b extends f.VisualElement{constructor(){super(...arguments),this.KeyframeResolver=d.DOMKeyframesResolver}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;(0,e.isMotionValue)(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}}},334589:a=>{"use strict";var{g:b,__dirname:c}=a;function d(a,{style:b,vars:c},d,e){for(let f in Object.assign(a.style,b,e&&e.getProjectionStyles(d)),c)a.style.setProperty(f,c[f])}a.s({renderHTML:()=>d})},559949:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({HTMLVisualElement:()=>b,getComputedStyle:()=>l});var d=a.i(237979),e=a.i(769169),f=a.i(863483),g=a.i(723163),h=a.i(537554),i=a.i(999040),j=a.i(334589),k=a.i(272424);function l(a){return window.getComputedStyle(a)}class b extends h.DOMVisualElement{constructor(){super(...arguments),this.type="html",this.renderInstance=j.renderHTML}readValueFromInstance(a,b){if(d.transformProps.has(b))return this.projection?.isProjecting?(0,e.defaultTransformValue)(b):(0,e.readTransformValue)(a,b);{let c=l(a),d=((0,f.isCSSVariableName)(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return(0,g.measureViewportBox)(a,b)}build(a,b,c){(0,i.buildHTMLStyles)(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return(0,k.scrapeMotionValuesFromProps)(a,b,c)}}}},148781:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({camelCaseAttributes:()=>b});let b=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"])}},79100:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({renderSVG:()=>g});var d=a.i(820280),e=a.i(334589),f=a.i(148781);function g(a,b,c,g){for(let c in(0,e.renderHTML)(a,b,void 0,g),b.attrs)a.setAttribute(f.camelCaseAttributes.has(c)?c:(0,d.camelToDash)(c),b.attrs[c])}},640426:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({SVGVisualElement:()=>b});var d=a.i(237979),e=a.i(780040),f=a.i(35838),g=a.i(537554),h=a.i(820280),i=a.i(620848),j=a.i(148781),k=a.i(408590),l=a.i(79100),m=a.i(666018);class b extends g.DOMVisualElement{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=f.createBox}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(d.transformProps.has(b)){let a=(0,e.getDefaultValueType)(b);return a&&a.default||0}return b=j.camelCaseAttributes.has(b)?b:(0,h.camelToDash)(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return(0,m.scrapeMotionValuesFromProps)(a,b,c)}build(a,b,c){(0,i.buildSVGAttrs)(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){(0,l.renderSVG)(a,b,c,d)}mount(a){this.isSVGTag=(0,k.isSVGTag)(a.tagName),super.mount(a)}}}},726590:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createDomVisualElement:()=>b});var d=a.i(722851),e=a.i(559949),f=a.i(640426),g=a.i(989769);let b=(a,b)=>(0,g.isSVGComponent)(a)?new f.SVGVisualElement(b):new e.HTMLVisualElement(b,{allowProjection:a!==d.Fragment})}},712035:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createMotionComponent:()=>b});var d=a.i(814340),e=a.i(2392),f=a.i(240172),g=a.i(412926),h=a.i(909244),i=a.i(726590);let b=(0,h.createMotionComponentFactory)({...d.animations,...f.gestureAnimations,...e.drag,...g.layout},i.createDomVisualElement)}},843495:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({motion:()=>b});var d=a.i(749346),e=a.i(712035);let b=(0,d.createDOMMotionComponentProxy)(e.createMotionComponent)}},308591:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ConsultationsSidebar:()=>q});var d=a.i(674420),e=a.i(722851),f=a.i(647852),g=a.i(701090),h=a.i(767332),i=a.i(582126),j=a.i(72732),k=a.i(314928),l=a.i(70763),m=a.i(256051),n=a.i(381085),o=a.i(953676),p=a.i(843495);let b=a=>{let b=new Date(a),c=b.getFullYear(),d=b.toLocaleDateString("en-US",{month:"short"}),e=b.getDate().toString().padStart(2,"0"),f=b.getMinutes().toString().padStart(2,"0"),g=b.getHours()>=12?"PM":"AM",h=b.getHours()%12||12;return`${d} ${e}, ${c} at ${h.toString().padStart(2,"0")}:${f} ${g}`},c=a=>{let c=new Date(a),d=Math.floor((new Date().getTime()-c.getTime())/1e3);if(d<60)return"Just now";if(d<3600){let a=Math.floor(d/60);return`${a} minute${a>1?"s":""} ago`}if(d<86400){let a=Math.floor(d/3600);return`${a} hour${a>1?"s":""} ago`}{if(!(d<604800))return b(a);let c=Math.floor(d/86400);return`${c} day${c>1?"s":""} ago`}};function q({consultations:a,hasMore:q,onConsultationSelect:r,selectedConsultation:s,isDarkMode:t,doctorId:u,isMobile:v=!1,onClose:w,isLoading:x=!1}){let[y,z]=(0,e.useState)(a),[A,B]=(0,e.useState)(q),[C,D]=(0,e.useState)(!1),[E,F]=(0,e.useState)(1),[G,H]=(0,e.useState)(""),[I,J]=(0,e.useState)(""),[K,L]=(0,e.useState)(!1),[M,N]=(0,e.useState)("all"),[O,P]=(0,e.useState)("all"),[Q,R]=(0,e.useState)(""),[S,T]=(0,e.useState)("all"),U=(0,e.useRef)(null),V=(0,e.useRef)(null),W=(0,e.useRef)(null),[X,Y]=(0,e.useState)(!1),[Z,$]=(0,e.useState)(!1),[_,aa]=(0,e.useState)(!1);(0,e.useEffect)(()=>{L(!0)},[]),(0,e.useEffect)(()=>{z(a),B(q),F(1)},[a,q]),(0,e.useEffect)(()=>(W.current&&clearTimeout(W.current),W.current=setTimeout(()=>{J(G)},500),()=>{W.current&&clearTimeout(W.current)}),[G]),(0,e.useEffect)(()=>{F(1),z([]),B(!1),(async()=>{D(!0);try{let a=await (0,o.getConsultations)({page:1,pageSize:15,status:"all"===M?void 0:M,searchTerm:I||void 0});a.success&&(z(a.data.consultations),B(a.data.hasMore),F(1))}catch(a){console.error("Failed to search consultations:",a),B(!1)}finally{D(!1)}})()},[I,M]);let ab=(0,e.useCallback)(async()=>{if(C||!A)return void console.log("Skipping load more - isLoading:",C,"hasMore:",A);console.log("Loading more consultations - page:",E+1),D(!0);try{let a=await (0,o.getConsultations)({page:E+1,pageSize:15,status:"all"===M?void 0:M,searchTerm:I||void 0});a.success&&a.data.consultations.length>0?(z(b=>[...b,...a.data.consultations]),B(a.data.hasMore),F(a=>a+1),console.log("Loaded",a.data.consultations.length,"more consultations")):(B(!1),console.log("No more consultations to load"))}catch(a){console.error("Failed to load more consultations:",a),B(!1)}finally{D(!1)}},[E,A,C,M,I]);(0,e.useEffect)(()=>{if(!V.current||!A||C){U.current&&(U.current.disconnect(),U.current=null);return}return U.current&&U.current.disconnect(),U.current=new IntersectionObserver(a=>{a[0].isIntersecting&&!C&&A&&y.length>0?(console.log("Intersection observer triggered - loading more"),ab()):console.log("Intersection observer triggered but conditions not met:",{isIntersecting:a[0].isIntersecting,isLoading:C,hasMore:A,consultationsCount:y.length})},{threshold:.1}),U.current.observe(V.current),()=>{U.current&&(U.current.disconnect(),U.current=null)}},[ab,A,C,y.length]),(0,e.useEffect)(()=>{F(1)},[O,S]);let ac=(a=>{if("all"===O)return a;let b=new Date,c=new Date(b);return c.setDate(c.getDate()-1),a.filter(a=>{let d=new Date(a.created_at);switch(O){case"today":return d.toDateString()===b.toDateString();case"yesterday":return d.toDateString()===c.toDateString();case"custom":if(!Q)return!0;let e=new Date(Q);return d.toDateString()===e.toDateString();default:return!0}})})(y.filter(a=>{let b=G.toLowerCase(),c=!G||a.patient_number?.toString().includes(b)||a.patient_name?.toLowerCase().includes(b),d="all"===M||a.status===M,e="all"===S||a.consultation_type===S;return c&&d&&e})),ad=a=>{switch(a){case"pending":return(0,d.jsx)(i.Clock,{className:"w-4 h-4 text-orange-500"});case"generated":return(0,d.jsx)(k.FileText,{className:"w-4 h-4 text-blue-500"});case"approved":return(0,d.jsx)(j.CheckCircle,{className:"w-4 h-4 text-green-500"});default:return(0,d.jsx)(i.Clock,{className:"w-4 h-4 text-gray-400"})}},ae=a=>{let b="px-2 py-1 text-xs font-medium rounded-full";switch(a){case"pending":return`${b} bg-orange-200 text-orange-900 dark:bg-orange-900/30 dark:text-orange-400`;case"generated":return`${b} bg-blue-200 text-blue-900 dark:bg-blue-900/30 dark:text-blue-400`;case"approved":return`${b} bg-green-200 text-green-900 dark:bg-green-900/30 dark:text-green-400`;default:return`${b} bg-gray-200 text-gray-900 dark:bg-black dark:text-gray-400`}},af=a=>{let b="px-2 py-1 text-xs font-medium rounded-full";switch(a){case"outpatient":return`${b} bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400`;case"discharge":return`${b} bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400`;case"surgery":return`${b} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400`;case"radiology":return`${b} bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400`;case"dermatology":return`${b} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400`;case"cardiology_echo":return`${b} bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400`;case"ivf_cycle":return`${b} bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400`;case"pathology":return`${b} bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400`;default:return`${b} bg-gray-100 text-gray-800 dark:bg-black dark:text-gray-400`}};return(0,d.jsxs)("div",{className:"h-full flex flex-col transition-colors duration-300 bg-transparent",children:[(0,d.jsxs)("div",{className:"p-6 transition-colors duration-300 border-r-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h2",{className:`text-lg font-semibold ${t?"text-gray-100":"text-slate-800"}`,children:"Consultations"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{onClick:()=>r(null),className:`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${t?"bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg":"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl"}`,title:"Add New Consultation",children:(0,d.jsx)(g.Plus,{className:"w-4 h-4"})}),v&&w&&(0,d.jsx)("button",{onClick:w,className:`p-1 rounded-xl transition-all duration-300 ${t?"hover:bg-gray-800 text-gray-300":"hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105"}`,children:(0,d.jsx)(h.X,{className:"w-5 h-5"})})]})]}),(0,d.jsxs)("div",{className:"relative mb-4",children:[G!==I?(0,d.jsx)(n.Loader2,{className:`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin ${t?"text-gray-400":"text-gray-500"}`}):(0,d.jsx)(f.Search,{className:`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${t?"text-gray-400":"text-gray-500"}`}),(0,d.jsx)("input",{type:"text",placeholder:"Search by patient name...",value:G,onChange:a=>H(a.target.value),className:`w-full pl-10 pr-4 py-3 rounded-xl border transition-all duration-300 ${t?"bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500 focus:bg-white/90"} focus:outline-none focus:ring-2 focus:ring-indigo-500/20`})]}),(0,d.jsxs)("div",{className:"flex space-x-2 mb-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("button",{onClick:()=>{Y(!X),$(!1),aa(!1)},className:`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${t?"bg-black border-gray-700 text-gray-300 hover:bg-gray-800":"bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50"}`,children:[(0,d.jsx)("span",{children:"all"===M?"All":M.charAt(0).toUpperCase()+M.slice(1)}),(0,d.jsx)(m.ChevronDown,{className:"w-3 h-3"})]}),X&&(0,d.jsx)("div",{className:`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${t?"bg-black border-gray-700":"bg-white/90 backdrop-blur-xl border-white/30"}`,children:["all","pending","generated","approved"].map(a=>(0,d.jsx)("button",{onClick:()=>{N(a),Y(!1)},className:`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${M===a?t?"bg-teal-900/30 text-teal-400":"bg-teal-50 text-teal-700":t?"text-gray-300 hover:bg-gray-800":"text-amber-700 hover:bg-orange-50"}`,children:"all"===a?"All":a.charAt(0).toUpperCase()+a.slice(1)},a))})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("button",{onClick:()=>{$(!Z),Y(!1),aa(!1)},className:`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${t?"bg-black border-gray-700 text-gray-300 hover:bg-gray-800":"bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50"}`,children:[(0,d.jsx)(l.Calendar,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"all"===O?"All":O.charAt(0).toUpperCase()+O.slice(1)}),(0,d.jsx)(m.ChevronDown,{className:"w-3 h-3"})]}),Z&&(0,d.jsx)("div",{className:`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${t?"bg-black border-gray-700":"bg-white/90 backdrop-blur-xl border-white/30"}`,children:["all","today","yesterday","custom"].map(a=>(0,d.jsx)("button",{onClick:()=>{P(a),$(!1),"custom"!==a&&R("")},className:`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${O===a?t?"bg-teal-900/30 text-teal-400":"bg-teal-50 text-teal-700":t?"text-gray-300 hover:bg-gray-800":"text-amber-700 hover:bg-orange-50"}`,children:"all"===a?"All":a.charAt(0).toUpperCase()+a.slice(1)},a))})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("button",{onClick:()=>{aa(!_),Y(!1),$(!1)},className:`flex items-center space-x-1 px-3 py-1.5 text-xs rounded-lg border transition-colors ${t?"bg-black border-gray-700 text-gray-300 hover:bg-gray-800":"bg-white/80 border-white/30 text-slate-700 hover:bg-indigo-50"}`,children:[(0,d.jsx)(k.FileText,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"all"===S?"All":S.charAt(0).toUpperCase()+S.slice(1)}),(0,d.jsx)(m.ChevronDown,{className:"w-3 h-3"})]}),_&&(0,d.jsx)("div",{className:`absolute top-full left-0 mt-1 w-32 rounded-lg border shadow-lg z-10 ${t?"bg-black border-gray-700":"bg-white/90 backdrop-blur-xl border-white/30"}`,children:["all","outpatient","discharge","surgery","radiology","dermatology","cardiology_echo","ivf_cycle","pathology"].map(a=>(0,d.jsx)("button",{onClick:()=>{T(a),aa(!1)},className:`w-full text-left px-3 py-2 text-xs hover:bg-opacity-50 transition-colors ${S===a?t?"bg-teal-900/30 text-teal-400":"bg-teal-50 text-teal-700":t?"text-gray-300 hover:bg-gray-800":"text-amber-700 hover:bg-orange-50"}`,children:"all"===a?"All":"cardiology_echo"===a?"Cardiology Echo":"ivf_cycle"===a?"IVF Cycle":a.charAt(0).toUpperCase()+a.slice(1)},a))})]})]}),"custom"===O&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("input",{type:"date",value:Q,onChange:a=>R(a.target.value),className:`w-full px-3 py-2 text-xs rounded-lg border transition-colors ${t?"bg-black border-gray-700 text-gray-100 focus:border-indigo-500":"bg-white/80 border-white/30 text-slate-800 focus:border-indigo-500"} focus:outline-none focus:ring-2 focus:ring-indigo-500/20`})})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto",children:C||x?(0,d.jsxs)("div",{className:`p-4 text-center ${t?"text-gray-300":"text-slate-600"}`,children:[(0,d.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,d.jsxs)(p.motion.div,{className:"relative",animate:{x:[-20,20,-20]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center",children:(0,d.jsx)(p.motion.div,{className:"w-2 h-2 bg-white rounded-full",animate:{scale:[1,1.5,1],opacity:[1,.7,1]},transition:{duration:.5,repeat:1/0,ease:"easeInOut"}})}),(0,d.jsx)(p.motion.div,{className:"absolute top-1 left-1 w-6 h-6 bg-orange-300 rounded-full opacity-30",animate:{x:[-15,15,-15],scale:[.8,1.2,.8]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.1}})]})}),(0,d.jsx)(p.motion.p,{className:"text-sm",animate:{opacity:[.5,1,.5]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"},children:"Loading consultations..."})]}):0===ac.length?(0,d.jsxs)("div",{className:`p-4 text-center ${t?"text-gray-300":"text-slate-600"}`,children:[(0,d.jsx)(k.FileText,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,d.jsx)("p",{className:"text-sm",children:G?"No recordings found":"No recordings yet"})]}):(0,d.jsxs)("div",{className:"p-2",children:[ac.map(a=>(0,d.jsxs)(p.motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>r(a),className:`p-3 mb-2 rounded-xl cursor-pointer transition-all duration-300 ${s?.id===a.id?t?"bg-indigo-900/30 border border-indigo-500 shadow-lg":"bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 shadow-lg":t?"bg-gray-900/50 hover:bg-gray-800/70 border border-gray-700/50 hover:border-gray-600":"bg-white/70 hover:bg-white/90 backdrop-blur-sm border border-white/30 hover:border-white/50 hover:shadow-md"}`,children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h3",{className:`font-medium text-sm truncate ${t?"text-gray-100":"text-slate-800"}`,children:a.patient_name||`Patient #${a.patient_number||"N/A"}`}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,d.jsx)(l.Calendar,{className:`w-3 h-3 ${t?"text-gray-400":"text-gray-500"}`}),(0,d.jsx)("span",{className:`text-xs ${t?"text-gray-300":"text-amber-600"}`,children:K?c(a.created_at):b(a.created_at)})]})]}),ad(a.status)]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-1 mb-2",children:[(0,d.jsx)("span",{className:ae(a.status),children:a.status}),(0,d.jsx)("span",{className:af(a.consultation_type||"outpatient"),children:a.consultation_type||"outpatient"})]}),(a.doctor_notes||a.ai_generated_note)&&(0,d.jsx)("p",{className:`text-xs line-clamp-2 ${t?"text-gray-300":"text-amber-700"}`,children:a.doctor_notes||a.ai_generated_note?.substring(0,100)+"..."})]},a.id)),A&&y.length>0&&(0,d.jsx)("div",{ref:V,className:"flex justify-center py-4",children:C?(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(n.Loader2,{className:`w-4 h-4 animate-spin ${t?"text-gray-400":"text-gray-500"}`}),(0,d.jsx)("span",{className:`text-sm ${t?"text-gray-400":"text-gray-500"}`,children:"Loading more..."})]}):(0,d.jsx)("div",{className:`text-xs ${t?"text-gray-500":"text-gray-400"}`,children:"Scroll for more"})}),!A&&y.length>0&&(0,d.jsx)("div",{className:"flex justify-center py-4",children:(0,d.jsx)("div",{className:`text-xs ${t?"text-gray-500":"text-gray-400"}`,children:"No more consultations"})})]})})]})}}},449515:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],c=(0,d.default)("chevron-up",b)}},2944:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ChevronUp:()=>d.default});var d=a.i(449515)},55330:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PopChild:()=>h});var d=a.i(674420),e=a.i(949208),f=a.i(722851),g=a.i(602698);class b extends f.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,e.isHTMLElement)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:a,isPresent:c,anchorX:e}){let h=(0,f.useId)(),i=(0,f.useRef)(null),j=(0,f.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:k}=(0,f.useContext)(g.MotionConfigContext);return(0,f.useInsertionEffect)(()=>{let{width:a,height:b,top:d,left:f,right:g}=j.current;if(c||!i.current||!a||!b)return;let l="left"===e?`left: ${f}`:`right: ${g}`;i.current.dataset.motionPopId=h;let m=document.createElement("style");return k&&(m.nonce=k),document.head.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${h}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${b}px !important;
            ${l}px !important;
            top: ${d}px !important;
          }
        `),()=>{document.head.contains(m)&&document.head.removeChild(m)}},[c]),(0,d.jsx)(b,{isPresent:c,childRef:i,sizeRef:j,children:(0,f.cloneElement)(a,{ref:i})})}}},592581:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({PresenceChild:()=>b});var d=a.i(674420),e=a.i(722851),f=a.i(647216),g=a.i(481138),h=a.i(55330);let b=({children:a,initial:b,isPresent:c,onExitComplete:j,custom:k,presenceAffectsLayout:l,mode:m,anchorX:n})=>{let o=(0,g.useConstant)(i),p=(0,e.useId)(),q=!0,r=(0,e.useMemo)(()=>(q=!1,{id:p,initial:b,isPresent:c,custom:k,onExitComplete:a=>{for(let b of(o.set(a,!0),o.values()))if(!b)return;j&&j()},register:a=>(o.set(a,!1),()=>o.delete(a))}),[c,o,j]);return l&&q&&(r={...r}),(0,e.useMemo)(()=>{o.forEach((a,b)=>o.set(b,!1))},[c]),(0,e.useEffect)(()=>{c||o.size||!j||j()},[c]),"popLayout"===m&&(a=(0,d.jsx)(h.PopChild,{isPresent:c,anchorX:n,children:a})),(0,d.jsx)(f.PresenceContext.Provider,{value:r,children:a})};function i(){return new Map}}},93101:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getChildKey:()=>b,onlyElements:()=>e});var d=a.i(722851);let b=a=>a.key||"";function e(a){let b=[];return d.Children.forEach(a,a=>{(0,d.isValidElement)(a)&&b.push(a)}),b}}},862772:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AnimatePresence:()=>b});var d=a.i(674420),e=a.i(722851),f=a.i(14275),g=a.i(481138),h=a.i(31751),i=a.i(592581),j=a.i(502394),k=a.i(93101);let b=({children:a,custom:b,initial:c=!0,onExitComplete:l,presenceAffectsLayout:m=!0,mode:n="sync",propagate:o=!1,anchorX:p="left"})=>{let[q,r]=(0,j.usePresence)(o),s=(0,e.useMemo)(()=>(0,k.onlyElements)(a),[a]),t=o&&!q?[]:s.map(k.getChildKey),u=(0,e.useRef)(!0),v=(0,e.useRef)(s),w=(0,g.useConstant)(()=>new Map),[x,y]=(0,e.useState)(s),[z,A]=(0,e.useState)(s);(0,h.useIsomorphicLayoutEffect)(()=>{u.current=!1,v.current=s;for(let a=0;a<z.length;a++){let b=(0,k.getChildKey)(z[a]);t.includes(b)?w.delete(b):!0!==w.get(b)&&w.set(b,!1)}},[z,t.length,t.join("-")]);let B=[];if(s!==x){let a=[...s];for(let b=0;b<z.length;b++){let c=z[b],d=(0,k.getChildKey)(c);t.includes(d)||(a.splice(b,0,c),B.push(c))}return"wait"===n&&B.length&&(a=B),A((0,k.onlyElements)(a)),y(s),null}let{forceRender:C}=(0,e.useContext)(f.LayoutGroupContext);return(0,d.jsx)(d.Fragment,{children:z.map(a=>{let e=(0,k.getChildKey)(a),f=(!o||!!q)&&(s===z||t.includes(e));return(0,d.jsx)(i.PresenceChild,{isPresent:f,initial:(!u.current||!!c)&&void 0,custom:b,presenceAffectsLayout:m,mode:n,onExitComplete:f?void 0:()=>{if(!w.has(e))return;w.set(e,!0);let a=!0;w.forEach(b=>{b||(a=!1)}),a&&(C?.(),A(v.current),o&&r?.(),l&&l())},anchorX:p,children:a},e)})})}}},917472:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]],c=(0,d.default)("bold",b)}},717010:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Bold:()=>d.default});var d=a.i(917472)},120555:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]],c=(0,d.default)("italic",b)}},848130:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Italic:()=>d.default});var d=a.i(120555)},73393:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({ContentEditableEditor:()=>h});var d=a.i(674420),e=a.i(722851),f=a.i(717010),g=a.i(848130);let b=a=>a.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"),c=a=>a.replace(/<strong>(.*?)<\/strong>/g,"**$1**").replace(/<b>(.*?)<\/b>/g,"**$1**").replace(/<em>(.*?)<\/em>/g,"*$1*").replace(/<i>(.*?)<\/i>/g,"*$1*").replace(/<br\s*\/?>/g,"\n").replace(/<div>/g,"\n").replace(/<\/div>/g,"").replace(/<p>/g,"").replace(/<\/p>/g,"\n").trim();function h({content:a,onChange:h,isEditing:i,isDarkMode:j}){let k=(0,e.useRef)(null),l=b(a);(0,e.useEffect)(()=>{k.current&&!i&&(k.current.innerHTML=l)},[l,i]);let m=()=>{k.current&&i&&h(c(k.current.innerHTML))};return(0,d.jsxs)("div",{className:`min-h-[200px] ${i?"":j?"text-gray-200":"text-slate-800"}`,children:[i&&(0,d.jsxs)("div",{className:"flex gap-2 mb-2 border-b pb-2",children:[(0,d.jsx)("button",{onClick:()=>{document.execCommand("bold",!1),m()},className:`p-2 rounded transition-colors ${j?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-700"}`,title:"Bold",children:(0,d.jsx)(f.Bold,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>{document.execCommand("italic",!1),m()},className:`p-2 rounded transition-colors ${j?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-700"}`,title:"Italic",children:(0,d.jsx)(g.Italic,{className:"w-4 h-4"})})]}),(0,d.jsx)("div",{ref:k,contentEditable:i,onInput:m,dangerouslySetInnerHTML:{__html:l},className:`prose max-w-none focus:outline-none min-h-[100px] p-4 rounded-lg ${j?"prose-invert text-gray-200":"text-slate-800"} ${j?"bg-black border border-gray-700":"bg-white/70 backdrop-blur-sm border border-white/30"}`,style:{whiteSpace:"pre-wrap",wordWrap:"break-word"},suppressContentEditableWarning:!0})]})}}},596713:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({composeRefs:()=>f,useComposedRefs:()=>g});var d=a.i(722851);function e(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function f(...a){return b=>{let c=!1,d=a.map(a=>{let d=e(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():e(a[b],null)}}}}function g(...a){return(0,d.useCallback)(f(...a),a)}},324867:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Root:()=>h,Slot:()=>h,Slottable:()=>k,createSlot:()=>g,createSlottable:()=>j});var d=a.i(722851),e=a.i(596713),f=a.i(674420);function g(a){let b=function(a){let b=(0,d.forwardRef)((a,b)=>{let{children:c,...f}=a;if((0,d.isValidElement)(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,c.props);return c.type!==d.Fragment&&(j.ref=b?(0,e.composeRefs)(b,i):i),(0,d.cloneElement)(c,j)}return d.Children.count(c)>1?d.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=(0,d.forwardRef)((a,c)=>{let{children:e,...g}=a,h=d.Children.toArray(e),i=h.find(l);if(i){let a=i.props.children,e=h.map(b=>b!==i?b:d.Children.count(a)>1?d.Children.only(null):(0,d.isValidElement)(a)?a.props.children:null);return(0,f.jsx)(b,{...g,ref:c,children:(0,d.isValidElement)(a)?(0,d.cloneElement)(a,void 0,e):null})}return(0,f.jsx)(b,{...g,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}var h=g("Slot"),i=Symbol("radix.slottable");function j(a){let b=({children:a})=>(0,f.jsx)(f.Fragment,{children:a});return b.displayName=`${a}.Slottable`,b.__radixId=i,b}var k=j("Slottable");function l(a){return(0,d.isValidElement)(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}},379611:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({cva:()=>e,cx:()=>c});var d=a.i(985661);let b=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,c=d.clsx,e=(a,d)=>e=>{var f;if((null==d?void 0:d.variants)==null)return c(a,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:g,defaultVariants:h}=d,i=Object.keys(g).map(a=>{let c=null==e?void 0:e[a],d=null==h?void 0:h[a];if(null===c)return null;let f=b(c)||b(d);return g[a][f]}),j=e&&Object.entries(e).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return c(a,i,null==d||null==(f=d.compoundVariants)?void 0:f.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==e?void 0:e.class,null==e?void 0:e.className)}}},325074:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Button:()=>h,buttonVariants:()=>b});var d=a.i(674420),e=a.i(324867),f=a.i(379611),g=a.i(148530);let b=(0,f.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function h({className:a,variant:c,size:f,asChild:h=!1,...i}){let j=h?e.Slot:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(b({variant:c,size:f,className:a})),...i})}}},100254:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Input:()=>f});var d=a.i(674420),e=a.i(148530);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},896e3:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({Badge:()=>h,badgeVariants:()=>b});var d=a.i(674420),e=a.i(324867),f=a.i(379611),g=a.i(148530);let b=(0,f.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:c,asChild:f=!1,...h}){let i=f?e.Slot:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(b({variant:c}),a),...h})}}},919505:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({useAutosave:()=>b});var d=a.i(722851);let b=(a={})=>{let[b,c]=(0,d.useState)("idle"),[e,f]=(0,d.useState)({}),g=(0,d.useRef)(null),h=(0,d.useRef)(null),{onSuccess:i,onError:j,textDebounceMs:k=2e3,audioDelayMs:l=1e3}=a;(0,d.useEffect)(()=>()=>{g.current&&clearTimeout(g.current),h.current&&clearTimeout(h.current)},[]);let m=(0,d.useCallback)(async(a,b,d)=>{c("saving");try{let e=await b();return c("saved"),d&&d.field&&f(a=>({...a,[d.field]:d.value})),i?.(a),setTimeout(()=>{c(a=>"saved"===a?"idle":a)},2e3),e}catch(d){console.error(`Autosave failed for ${a}:`,d),c("error");let b=d instanceof Error?d:Error(String(d));throw j?.(a,b),b}},[i,j]),n=(0,d.useCallback)(async(a,b)=>{if(0!==a.length)return m("images",()=>b(a))},[m]),o=(0,d.useCallback)((a,b,c=!1)=>{if(h.current&&(clearTimeout(h.current),h.current=null),c)return m("audio",()=>b(a));let d=setTimeout(async()=>{try{await m("audio",()=>b(a))}catch(a){}h.current=null},l);h.current=d},[m,l]),p=(0,d.useCallback)((a,b,c)=>{if(g.current&&(clearTimeout(g.current),g.current=null),e[a]===b)return;let d=setTimeout(async()=>{try{await m("text",()=>c(a,b),{field:a,value:b})}catch(a){}g.current=null},k);g.current=d},[m,k,e]),q=(0,d.useCallback)(async a=>m("manual_retry",a),[m]);return{autoSaveStatus:b,setAutoSaveStatus:c,autoSaveImages:n,autoSaveAudio:o,autoSaveText:p,retryAutoSave:q,forceSave:(0,d.useCallback)(async a=>(g.current&&(clearTimeout(g.current),g.current=null),h.current&&(clearTimeout(h.current),h.current=null),m("manual_retry",a)),[m]),hasPendingSaves:null!==g.current||null!==h.current,lastSavedValues:e}}}},347093:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AutoSaveIndicator:()=>h});var d=a.i(674420),e=a.i(24353),f=a.i(381085),g=a.i(676261);function h({status:a,onRetry:b,className:c="",showText:h=!0}){return"idle"===a?null:(0,d.jsxs)("div",{className:`flex items-center text-xs ${c}`,children:["saving"===a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.Loader2,{className:"w-3 h-3 mr-1 animate-spin text-blue-500"}),h&&(0,d.jsx)("span",{className:"text-slate-600",children:"Saving..."})]}),"saved"===a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.Check,{className:"w-3 h-3 mr-1 text-green-500"}),h&&(0,d.jsx)("span",{className:"text-green-600",children:"Saved"})]}),"error"===a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-3 h-3 mr-1 text-red-500 cursor-pointer hover:scale-110 transition-transform",onClick:b,title:"Click to retry save",children:(0,d.jsx)(g.AlertCircle,{className:"w-full h-full"})}),h&&(0,d.jsx)("span",{className:"text-red-600 cursor-pointer",onClick:b,children:"Save failed - Click to retry"})]})]})}},522295:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({StreamlinedRecordingArea:()=>Q});var d=a.i(674420),e=a.i(722851),f=a.i(629551),g=a.i(954780),h=a.i(341239),i=a.i(246021),j=a.i(525128),k=a.i(115285),l=a.i(447888),m=a.i(86111),n=a.i(767332),o=a.i(256051),p=a.i(2944),q=a.i(24353),r=a.i(381085),s=a.i(491800),t=a.i(12372),u=a.i(701090),v=a.i(843495),w=a.i(862772),x=a.i(73393),y=a.i(325074),z=a.i(100254),A=a.i(896e3),B=a.i(826542),C=a.i(762294),D=a.i(734293),E=a.i(851627),F=a.i(954786),G=a.i(736124),H=a.i(994946),I=a.i(895032),J=a.i(195646),K=a.i(919505),L=a.i(347093),M=a.i(844183),N=a.i(365171);function O(a){return a.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>").replace(/<br><br>/g,"</p><p>").split("</p><p>").map(a=>a.trim()).filter(a=>a.length>0).map(a=>`<p>${a}</p>`).join("")||`<p>${a.replace(/\n/g,"<br>")}</p>`}function P(a){return a.replace(/<strong>(.*?)<\/strong>/g,"**$1**").replace(/<em>(.*?)<\/em>/g,"*$1*").replace(/<br\s*\/?>/g,"\n").replace(/<\/p><p>/g,"\n\n").replace(/<\/?p>/g,"").replace(/<[^>]*>/g,"").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'")}let b=[{id:"outpatient",name:"Outpatient Consultation",color:"teal"},{id:"discharge",name:"Discharge Summary",color:"purple"},{id:"surgery",name:"Operative Note",color:"red"},{id:"radiology",name:"Radiology Report",color:"blue"},{id:"dermatology",name:"Dermatology Note",color:"green"},{id:"cardiology_echo",name:"Echocardiogram Report",color:"pink"},{id:"ivf_cycle",name:"IVF Cycle Summary",color:"orange"},{id:"pathology",name:"Histopathology Report",color:"indigo"}],c=a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`};function Q(Q){let{selectedConsultation:R,isDarkMode:S,patientName:T,setPatientName:U,selectedTemplate:V,setSelectedTemplate:W,isRecording:X,isPaused:Y,recordingDuration:Z,audioBlob:$,images:_,isGenerating:aa,setIsGenerating:ab,summary:ac,setSummary:ad,isEditing:ae,setIsEditing:af,additionalNotes:ag,setAdditionalNotes:ah,startRecording:ai,pauseRecording:aj,stopRecording:ak,handleImageUpload:al,removeImage:am,clearAudio:an,doctorName:ao}=Q,[ap,aq]=(0,e.useState)(!1),[ar,as]=(0,e.useState)(!1),[at,au]=(0,e.useState)(null),[av,aw]=(0,e.useState)(null),ax=(0,e.useRef)(null),ay=(0,e.useRef)(null),[az,aA]=(0,e.useState)(!1),[aB,aC]=(0,e.useState)(!1),[aD,aE]=(0,e.useState)(!1),[aF,aG]=(0,e.useState)(""),[aH,aI]=(0,e.useState)(""),[aJ,aK]=(0,e.useState)(""),[aL,aM]=(0,e.useState)(!1),[aN,aO]=(0,e.useState)(null),{autoSaveStatus:aP,autoSaveImages:aQ,autoSaveAudio:aR,autoSaveText:aS,retryAutoSave:aT,forceSave:aU,hasPendingSaves:aV}=(0,K.useAutosave)({onSuccess:a=>{console.log(`Autosave successful: ${a}`)},onError:(a,b)=>{console.error(`Autosave failed: ${a}`,b)}}),[aW,aX]=(0,e.useState)(null),[aY,aZ]=(0,e.useState)(null),[a$,a_]=(0,e.useState)([]),[a0,a1]=(0,e.useState)(!1),[a2,a3]=(0,e.useState)(null),[a4,a5]=(0,e.useState)(null),a6=(0,e.useRef)(null),a7=(0,e.useRef)({}),a8=(0,e.useCallback)(async b=>{if(!R)return;let{addAdditionalImages:c}=await a.r(881628)(a.i);return c(R.id,b)},[R]),a9=(0,e.useCallback)(async b=>{if(!T.trim()||!V)throw Error("Patient name and template required");if(R){let c=new File([b],`additional_audio_${Date.now()}.webm`,{type:"audio/webm"}),{addAdditionalAudio:d}=await a.r(881628)(a.i);return d(R.id,c)}{let a=new File([b],"audio.webm",{type:"audio/webm"}),c=await (0,B.createConsultationWithFiles)(a,_.map(a=>a.file),[],"doctor",ag||void 0,V,T||void 0);return c&&c.success&&c.data&&Q.onConsultationUpdate(c.data),c}},[T,V,R,ag,_,Q]),ba=(0,e.useCallback)(async(b,c)=>{if(R){if("notes"===b){let{updateAdditionalNotes:b}=await a.r(881628)(a.i);return b(R.id,c)}else if("patient_name"===b){let{updatePatientName:b}=await a.r(881628)(a.i);return b(R.id,c)}}},[R]),bb=(0,e.useCallback)(async()=>{if(R)try{let{getConsultations:b}=await a.r(881628)(a.i),c=await b();if(c.success&&c.data){let a=c.data.consultations.find(a=>a.id===R.id);a&&Q.onConsultationUpdate&&Q.onConsultationUpdate(a)}}catch(a){console.error("Failed to refresh consultation data:",a)}},[R,Q]),bc=(0,e.useCallback)(async a=>{if(R&&a.length>0){let b=Array.from(a);try{let a=await aQ(b,async a=>{let b=await a8(a);return b&&"object"==typeof b&&"success"in b&&b.success&&await bb(),b});console.log("Image autosave completed:",a)}catch(b){console.error("Image autosave failed:",b),al(a)}}else al(a)},[R,aQ,a8,bb,al]);(0,e.useEffect)(()=>{aI(""),aO(a=>(a&&clearTimeout(a),null))},[R?.id]);let bd=(0,e.useRef)(null);(0,e.useEffect)(()=>{let a=null===bd.current,b=null!==$;a&&b&&T.trim()&&V&&aR($,async a=>{let b=await a9(a);return b&&"object"==typeof b&&"success"in b&&b.success&&(an(),await bb()),b}),bd.current=$},[$,T,V,aR,a9,an,bb]);let be=(0,e.useCallback)(a=>{ah(a),R&&a.trim()&&aS("notes",a,ba)},[ah,R,aS,ba]),bf=(0,e.useCallback)(a=>{U(a),R&&a.trim()&&aS("patient_name",a,ba)},[U,R,aS,ba]);(0,e.useEffect)(()=>()=>{aW&&clearTimeout(aW)},[aW]);let bg=(a,b=3e3)=>{if(aN&&clearTimeout(aN),aI(a),a){let a=setTimeout(()=>{aI(""),aO(null)},b);aO(a)}},bh=b.find(a=>a.id===V)||b[0],bi=R?.status==="approved",bj=!bi,bk=ac||R?.ai_generated_note||R?.edited_note,bl=a=>{let b=document.createElement("div");return b.innerHTML=a,(b.textContent||b.innerText||"").replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/#{1,6}\s/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/`([^`]+)`/g,"$1").replace(/^\s*[-*+]\s/gm,"").replace(/^\s*\d+\.\s/gm,"").replace(/\n{3,}/g,"\n\n").trim()},bm=async()=>{try{let a=bl(ac);await navigator.clipboard.writeText(a),aA(!0),setTimeout(()=>aA(!1),2e3)}catch(a){console.error("Failed to copy:",a)}},bn=async a=>{if(W(a),R)try{let b=await (0,F.updateConsultationType)(R.id,a);if(b.success){let b={...R,consultation_type:a};Q.onConsultationUpdate(b)}else console.error("Failed to update consultation type:",b.error),bg(`Failed to update template: ${b.error}`)}catch(a){console.error("Error updating consultation type:",a),bg("Failed to update template")}},bo=async()=>{if(!R||!aJ.trim())return void aI("No content to save");aM(!0),aI("");try{let a=await (0,G.saveEditedNote)(R.id,aJ);if(a.success){bg("Changes saved successfully!"),ad(O(aJ)),af(!1);let a={...R,edited_note:aJ};Q.onConsultationUpdate(a)}else bg(a.error||"Failed to save changes")}catch(a){console.error("Error saving edited note:",a),bg("Failed to save changes")}finally{aM(!1)}},bp=async b=>{if(R)try{aI("Deleting audio...");let c=await (0,I.deleteAdditionalAudio)(R.id,b);if(c.success){bg("Audio deleted successfully!");let{getConsultations:b}=await a.r(881628)(a.i),c=await b();if(c.success&&c.data){let a=c.data.consultations.find(a=>a.id===R.id);a&&Q.onConsultationUpdate(a)}}else bg(c.error||"Failed to delete audio")}catch(a){bg("Failed to delete audio")}finally{aZ(null)}},bq=async b=>{if(R)try{aI("Deleting image...");let c=await (0,J.deleteConsultationImage)(R.id,b);if(c.success){bg("Image deleted successfully!");let{getConsultations:b}=await a.r(881628)(a.i),c=await b();if(c.success&&c.data){let a=c.data.consultations.find(a=>a.id===R.id);a&&Q.onConsultationUpdate(a)}}else bg(c.error||"Failed to delete image")}catch(a){bg("Failed to delete image")}finally{aZ(null)}},br=(a,b)=>{aX(setTimeout(()=>{aZ({type:a,url:b})},500))},bs=()=>{aW&&(clearTimeout(aW),aX(null))},bt=async()=>{let b=!!R;if(b){if(!$&&0===_.length&&!ag.trim())return void aI("Please add new audio, images, or additional notes to update the consultation")}else if(!$||!T.trim())return void aI("Please provide patient name and record audio");aC(!0),aI("");try{if(b){if(_.length>0){let a=_.map(a=>a.file),b=await (0,E.addAdditionalImages)(R.id,a);if(!b.success)return void aI(b.error||"Failed to add additional images")}if($){let a=new File([$],`additional_audio_${Date.now()}.webm`,{type:"audio/webm"}),b=await (0,D.addAdditionalAudio)(R.id,a);if(!b.success)return void aI(b.error||"Failed to add additional audio")}if(ag.trim()!==(R.additional_notes||"").trim()){let{updateAdditionalNotes:b}=await a.r(881628)(a.i),c=await b(R.id,ag.trim());if(!c.success)return void aI(c.error||"Failed to update additional notes")}bg("Consultation updated successfully!");try{let{getConsultations:b}=await a.r(881628)(a.i),c=await b();if(c.success&&c.data){let a=c.data.consultations.find(a=>a.id===R.id);if(a)return Q.onConsultationUpdate(a),{success:!0,data:a}}return Q.onConsultationUpdate(R),{success:!0,data:R}}catch(a){return console.error("Failed to refetch consultations:",a),Q.onConsultationUpdate(R),{success:!0,data:R}}}{if(!$)return void aI("No audio recording available");let a=new File([$],`recording_${Date.now()}.webm`,{type:"audio/webm"}),b=_.map(a=>a.file),c=await (0,B.createConsultationWithFiles)(a,b,[],"doctor",ag||void 0,V,T||void 0);if(c.success&&c.data)return bg(`Consultation submitted! ${c.data.patient_name}`),Q.onConsultationUpdate(c.data),c;return aI("error"in c?c.error:"Failed to submit consultation"),c}}catch(a){return console.error("❌ Save error:",a),aI(`Network error: ${a instanceof Error?a.message:"Unknown error"}. Please try again.`),{success:!1,error:"Network error"}}finally{aC(!1)}},bu=async b=>{if(R)console.log("🎯 Using existing consultation:",R.id);else if(b)console.log("🎯 Using new consultation:",b.id);else{if(!$)return void aI("Please record audio or select a consultation first");let a=await bt();return a&&a.success&&a.data?bu(a.data):void 0}let c=R?.id||b?.id,d=R?.doctor_notes||b?.doctor_notes;if(!c)return void aI("No consultation ID available");if(ab(!0),aG(""),ad(""),R)try{await (0,H.clearEditedNote)(R.id)}catch(a){console.error("Failed to clear edited note:",a)}try{console.log("🎯 Starting generation with consultation ID:",c),console.log("🎯 Using template type:",V);let e=R||b;if(!e){aI("No consultation data available"),ab(!1);return}let f=await fetch("/api/generate-summary-stream",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({primary_audio_url:e.primary_audio_url,additional_audio_urls:Array.isArray(e.additional_audio_urls)?e.additional_audio_urls:[],image_urls:Array.isArray(e.image_urls)?e.image_urls:[],submitted_by:e.submitted_by||"doctor",consultation_type:V||"outpatient",doctor_notes:d||void 0,additional_notes:ag||void 0,patient_name:e.patient_name||void 0,doctor_name:ao||void 0,created_at:e.created_at||void 0})});if(!f.ok)throw Error(`HTTP ${f.status}: ${f.statusText}`);let g=f.body?.getReader();if(!g)throw Error("No reader available");let h=new TextDecoder,i="";for(aG("");;){let{done:a,value:b}=await g.read();if(a)break;for(let a of h.decode(b,{stream:!0}).split("\n"))if(a.startsWith("data: "))try{let b=JSON.parse(a.slice(6));if("chunk"===b.type&&b.text){let a=b.text.replace(/\\n/g,"\n").replace(/\n\n+/g,"\n\n");i+=a,aG(i)}}catch(a){}}let j=O(i);ad(j),aG(""),ab(!1),bg("Summary generated successfully!"),af(!1),aK("");try{let{saveStreamingSummary:b}=await a.r(881628)(a.i),d=await b(c,i);d.success?((0,M.trackConsultation)("generated",V),Q.onConsultationUpdate&&Q.onConsultationUpdate({...e,ai_generated_note:i,edited_note:null,status:"generated"})):(console.error("Failed to save streaming summary:",d.error),aI(`Summary generated but failed to save: ${d.error}`))}catch(a){console.error("Error saving streaming summary:",a),aI("Summary generated but failed to save. Please try regenerate.")}}catch(a){console.error("❌ Generate error:",a),aI(`Failed to generate summary: ${a instanceof Error?a.message:"Unknown error"}`),ab(!1)}},bv=async()=>{if(!R||!ac.trim())return void aI("Please generate a summary before approving");aE(!0),aI("");try{let a=await (0,C.approveConsultation)(R.id,ac);a.success?(bg("Consultation approved successfully!"),(0,M.trackConsultation)("approved",R.consultation_type),Q.onConsultationUpdate&&Q.onConsultationUpdate({...R,status:"approved",edited_note:ac})):aI(a.error||"Failed to approve consultation")}catch(a){aI("Failed to approve consultation")}finally{aE(!1)}},bw=async(a,b)=>{if(at===a){ay.current&&(ay.current.stop(),ay.current=null),au(null);return}ay.current&&ay.current.stop(),aw(a),au(null);try{ax.current||(ax.current=new(window.AudioContext||window.webkitAudioContext)),"suspended"===ax.current.state&&await ax.current.resume();let c=ax.current,d=`/api/audio-proxy?url=${encodeURIComponent(b)}`,e=await fetch(d);if(!e.ok)throw Error(`Audio fetch failed: ${e.statusText}`);let f=await e.arrayBuffer(),g=await c.decodeAudioData(f),h=c.createBufferSource();h.buffer=g,h.connect(c.destination),h.start(0),h.onended=()=>{au(null),ay.current=null},ay.current=h,au(a)}catch(b){console.error("Web Audio API Error:",b);let a=b instanceof Error?b.message:String(b);aI(`Unable to play audio: ${a}`),au(null)}finally{aw(null)}},bx=async a=>{let b=a7.current[a];if(b){if(/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&b.src&&!b.src.startsWith("blob:"))return void await bw(a,b.src);if(at===a)b.pause(),au(null);else{Object.values(a7.current).forEach(a=>a.pause());try{await b.play(),au(a)}catch(b){console.error("Audio playback error:",b),au(null);let a=b instanceof Error?b.message:String(b);aI(`Unable to play audio: ${a}`)}}}};return(0,d.jsxs)("div",{className:"space-y-8 max-w-5xl",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:hidden gap-4 pl-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("label",{className:`text-lg font-semibold ${S?"text-gray-200":"text-slate-800"}`,children:"Patient Name"}),R&&(0,d.jsx)("button",{onClick:()=>Q.onConsultationUpdate(null),className:`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 mr-8 ${S?"bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg":"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl"}`,title:"Add New Consultation",children:(0,d.jsx)(u.Plus,{className:"w-4 h-4"})})]}),(0,d.jsx)(z.Input,{value:T,onChange:a=>R?bf(a.target.value):U(a.target.value),placeholder:"Enter patient name",className:`h-10 px-4 py-2 rounded-xl border transition-colors ${S?"bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500"} focus:outline-none focus:ring-2 focus:ring-teal-500/20`})]}),(0,d.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{className:`block text-lg font-semibold mb-4 ${S?"text-gray-200":"text-slate-800"}`,children:"Template"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)(y.Button,{variant:"outline",onClick:()=>bj&&aq(!ap),disabled:!bj,className:`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${S?"bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500"} focus:outline-none focus:ring-2 focus:ring-indigo-500/20`,children:[(0,d.jsx)("span",{children:bh.name}),bj&&(0,d.jsx)(o.ChevronDown,{className:"h-4 w-4"})]}),(0,d.jsx)(w.AnimatePresence,{children:ap&&bj&&(0,d.jsx)(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${S?"bg-gray-950 border-gray-700":"bg-white/90 backdrop-blur-xl border-white/30"}`,children:b.map(a=>(0,d.jsx)("button",{onClick:()=>{bn(a.id),aq(!1)},className:`w-full px-3 py-2 text-left transition-colors ${V===a.id?S?"bg-indigo-900/30 text-indigo-400":"bg-indigo-50 text-indigo-700":S?"hover:bg-gray-800 text-gray-200":"hover:bg-indigo-50 text-slate-800"}`,children:a.name},a.id))})})]})]}),bk&&R&&!bi&&(0,d.jsx)(y.Button,{onClick:bv,disabled:aD||!ac.trim(),className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center",children:aD?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Approving..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.Check,{className:"w-4 h-4 mr-2"}),"Approve"]})})]})]}),(0,d.jsx)("div",{className:"hidden sm:block pl-3",children:(0,d.jsxs)("div",{className:"flex gap-6 items-end",children:[(0,d.jsxs)("div",{className:"w-[25%]",children:[(0,d.jsx)("label",{className:`block text-lg font-semibold mb-4 ${S?"text-gray-200":"text-slate-800"}`,children:"Patient Name"}),(0,d.jsx)(z.Input,{value:T,onChange:a=>R?bf(a.target.value):U(a.target.value),placeholder:"Enter patient name",className:`h-10 px-4 py-2 rounded-xl border transition-colors ${S?"bg-black border-gray-700 text-gray-100 placeholder-gray-400 focus:border-indigo-500":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400 focus:border-indigo-500"} focus:outline-none focus:ring-2 focus:ring-indigo-500/20`})]}),(0,d.jsxs)("div",{className:"w-[25%]",children:[(0,d.jsx)("label",{className:`block text-lg font-semibold mb-4 ${S?"text-gray-200":"text-slate-800"}`,children:"Template"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)(y.Button,{variant:"outline",onClick:()=>bj&&aq(!ap),disabled:!bj,className:`w-full justify-between h-10 px-4 py-2 rounded-xl border transition-colors ${S?"bg-black border-gray-700 text-gray-100 hover:bg-gray-800 focus:border-indigo-500":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 hover:bg-white/90 focus:border-indigo-500"} focus:outline-none focus:ring-2 focus:ring-indigo-500/20`,children:[(0,d.jsx)("span",{children:bh.name}),bj&&(0,d.jsx)(o.ChevronDown,{className:"h-4 w-4"})]}),(0,d.jsx)(w.AnimatePresence,{children:ap&&bj&&(0,d.jsx)(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:`absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-20 ${S?"bg-gray-950 border-gray-700":"bg-white/90 backdrop-blur-xl border-white/30"}`,children:b.map(a=>(0,d.jsx)("button",{onClick:()=>{bn(a.id),aq(!1)},className:`w-full px-3 py-2 text-left transition-colors ${V===a.id?S?"bg-indigo-900/30 text-indigo-400":"bg-indigo-50 text-indigo-700":S?"hover:bg-gray-800 text-gray-200":"hover:bg-indigo-50 text-slate-800"}`,children:a.name},a.id))})})]})]}),(0,d.jsxs)("div",{className:"flex items-end space-x-3 w-[50%]",children:[bk&&R&&!bi&&(0,d.jsx)(y.Button,{onClick:bv,disabled:aD||!ac.trim(),className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center",children:aD?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Approving..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.Check,{className:"w-4 h-4 mr-2"}),"Approve"]})}),(0,d.jsx)(w.AnimatePresence,{children:(R||$&&"saved"===aP)&&!bi&&(0,d.jsx)(v.motion.div,{initial:{opacity:0,scale:.8,x:-20},animate:{opacity:1,scale:1,x:0},exit:{opacity:0,scale:.8,x:-20},transition:{type:"spring",damping:20,stiffness:300},children:(0,d.jsx)(y.Button,{onClick:()=>bu(),disabled:aa,className:"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center",children:aa?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Wand2,{className:"w-4 h-4 mr-2"}),R&&(R.ai_generated_note||R.edited_note)?"Regenerate":"Generate"]})})})})]})]})}),(0,d.jsxs)("div",{className:"pl-3",children:[(0,d.jsx)("div",{className:"flex sm:hidden items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[X?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(y.Button,{onClick:aj,size:"sm",variant:"outline",className:"border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0",children:Y?(0,d.jsx)(h.Play,{className:"w-4 h-4"}):(0,d.jsx)(i.Pause,{className:"w-4 h-4"})}),(0,d.jsx)(y.Button,{onClick:ak,size:"sm",variant:"outline",className:"border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0",children:(0,d.jsx)(g.Square,{className:"w-4 h-4"})}),(0,d.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,d.jsx)(A.Badge,{variant:"secondary",className:"font-mono text-sm px-2 py-1",children:c(Z)}),Y&&(0,d.jsx)(A.Badge,{variant:"outline",className:"text-yellow-600 border-yellow-500 text-xs",children:"Paused"})]})]}):(0,d.jsx)(y.Button,{onClick:ai,disabled:bi,size:"sm",className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0",children:(0,d.jsx)(f.Mic,{className:"w-4 h-4"})}),(0,d.jsx)(y.Button,{onClick:a=>{a.preventDefault(),a.stopPropagation(),setTimeout(()=>{a6.current&&a6.current.click()},0)},disabled:bi,variant:"outline",size:"sm",className:`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${S?"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500":"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300"}`,title:"Upload Images",children:(0,d.jsx)(j.Upload,{className:"w-4 h-4"})})]})}),(0,d.jsx)("div",{className:"hidden sm:block",children:(0,d.jsx)("div",{className:"flex gap-4 items-center",children:(0,d.jsxs)("div",{className:"flex items-center gap-3 flex-1 max-w-xs",children:[X?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(y.Button,{onClick:aj,size:"sm",variant:"outline",className:"border-yellow-500 text-yellow-600 hover:bg-yellow-50 rounded-full w-10 h-10 p-0",children:Y?(0,d.jsx)(h.Play,{className:"w-4 h-4"}):(0,d.jsx)(i.Pause,{className:"w-4 h-4"})}),(0,d.jsx)(y.Button,{onClick:ak,size:"sm",variant:"outline",className:"border-red-500 text-red-600 hover:bg-red-50 rounded-full w-10 h-10 p-0",children:(0,d.jsx)(g.Square,{className:"w-4 h-4"})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 ml-3",children:[(0,d.jsx)(A.Badge,{variant:"secondary",className:"font-mono text-lg px-3 py-1",children:c(Z)}),Y&&(0,d.jsx)(A.Badge,{variant:"outline",className:"text-yellow-600 border-yellow-500",children:"Paused"})]})]}):(0,d.jsx)(y.Button,{onClick:ai,disabled:bi,size:"sm",className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md rounded-full w-10 h-10 p-0",children:(0,d.jsx)(f.Mic,{className:"w-4 h-4"})}),(0,d.jsx)(y.Button,{onClick:a=>{a.preventDefault(),a.stopPropagation(),setTimeout(()=>{a6.current&&a6.current.click()},0)},disabled:bi,variant:"outline",size:"sm",className:`w-10 h-10 p-0 rounded-full transition-all duration-300 transform hover:scale-110 ${S?"bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500":"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:border-indigo-300"}`,title:"Upload Images",children:(0,d.jsx)(j.Upload,{className:"w-4 h-4"})}),(0,d.jsx)(L.AutoSaveIndicator,{status:aP,onRetry:()=>aT(async()=>{if($&&T.trim()&&V)return a9($);throw Error("Nothing to save")}),showText:!1,className:"ml-2"})]})})})]}),aH&&(0,d.jsx)(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mt-4 p-3 rounded-md ${S?"bg-blue-950/50 border border-blue-800":"bg-blue-50 border border-blue-200"}`,children:(0,d.jsx)("p",{className:`text-sm ${S?"text-blue-200":"text-blue-800"}`,children:aH})}),($||R?.primary_audio_url||R?.additional_audio_urls&&Array.isArray(R.additional_audio_urls)&&R.additional_audio_urls.filter(a=>"string"==typeof a&&""!==a.trim()).length>0)&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-3 pl-0",children:[$&&(0,d.jsxs)("div",{className:`flex items-center gap-2 rounded-lg px-3 py-2 ${S?"bg-black":"bg-transparent"}`,children:[(0,d.jsx)(y.Button,{size:"sm",variant:"ghost",onClick:()=>bx("new"),className:"!h-10 !w-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700 text-white",style:{height:"40px",width:"40px"},children:"new"===at?(0,d.jsx)(i.Pause,{className:"w-4 h-4"}):(0,d.jsx)(h.Play,{className:"w-4 h-4"})}),(0,d.jsx)(y.Button,{size:"sm",variant:"ghost",onClick:()=>{au(null),an()},className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:(0,d.jsx)(s.Trash2,{className:"w-3 h-3"})}),(0,d.jsx)("audio",{ref:a=>{a&&(a7.current.new=a)},src:URL.createObjectURL($),onEnded:()=>au(null)})]}),R?.primary_audio_url&&(0,d.jsxs)("div",{className:`flex items-center gap-2 rounded-lg px-3 py-2 ${S?"bg-black":"bg-transparent"}`,children:[(0,d.jsx)(y.Button,{size:"sm",variant:"ghost",onClick:()=>bx("primary"),disabled:"primary"===av,className:"!h-10 !w-10 p-0 rounded-full bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50",style:{height:"40px",width:"40px"},children:"primary"===av?(0,d.jsx)(r.Loader2,{className:"w-4 h-4 animate-spin"}):"primary"===at?(0,d.jsx)(i.Pause,{className:"w-4 h-4"}):(0,d.jsx)(h.Play,{className:"w-4 h-4"})}),(0,d.jsx)("audio",{ref:a=>{a&&(a7.current.primary=a,a.load())},src:R.primary_audio_url,onEnded:()=>au(null),onLoadedData:()=>{console.log("Primary audio loaded:",R.primary_audio_url)}},`primary-audio-${R.primary_audio_url}`)]}),R?.additional_audio_urls&&Array.isArray(R.additional_audio_urls)&&R.additional_audio_urls.filter(a=>"string"==typeof a&&""!==a.trim()).map((a,b)=>(0,d.jsxs)("div",{className:`flex items-center gap-2 rounded-lg px-3 py-2 relative group ${S?"bg-black":"bg-transparent"}`,onMouseEnter:()=>R?.status!=="approved"&&aZ({type:"audio",url:a}),onMouseLeave:()=>aZ(null),onTouchStart:()=>R?.status!=="approved"&&br("audio",a),onTouchEnd:bs,onTouchCancel:bs,children:[(0,d.jsx)(y.Button,{size:"sm",variant:"ghost",onClick:()=>bx(`additional-${b}`),disabled:av===`additional-${b}`,className:"!h-10 !w-10 p-0 rounded-full bg-teal-600 hover:bg-teal-700 text-white disabled:opacity-50",style:{height:"40px",width:"40px"},children:av===`additional-${b}`?(0,d.jsx)(r.Loader2,{className:"w-4 h-4 animate-spin"}):at===`additional-${b}`?(0,d.jsx)(i.Pause,{className:"w-4 h-4"}):(0,d.jsx)(h.Play,{className:"w-4 h-4"})}),R?.status!=="approved"&&aY?.type==="audio"&&aY?.url===a&&(0,d.jsx)(y.Button,{size:"sm",onClick:()=>bp(a),className:"absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200",title:"Delete audio",children:(0,d.jsx)(s.Trash2,{className:"w-3 h-3"})}),(0,d.jsx)("audio",{ref:a=>{a&&(a7.current[`additional-${b}`]=a,a.load())},src:a,onEnded:()=>au(null),onLoadedData:()=>{console.log(`Additional audio ${b} loaded:`,a)}},`additional-audio-${a}`)]},`additional-${b}`))]}),R?.image_urls&&Array.isArray(R.image_urls)&&R.image_urls.length>0&&(0,d.jsx)("div",{className:`p-4 rounded-lg transition-colors ${S?"bg-black":"bg-transparent"}`,children:(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:Array.isArray(R?.image_urls)&&R.image_urls.filter(a=>"string"==typeof a&&""!==a.trim()).map((a,b)=>(0,d.jsxs)("div",{className:`w-20 h-20 rounded-lg overflow-hidden border-2 relative group ${S?"bg-black border-gray-700":"bg-white/70 backdrop-blur-sm border-white/30"}`,onMouseEnter:()=>R?.status!=="approved"&&aZ({type:"image",url:a}),onMouseLeave:()=>aZ(null),onTouchStart:()=>R?.status!=="approved"&&br("image",a),onTouchEnd:bs,onTouchCancel:bs,children:[(0,d.jsx)(N.default,{src:a,alt:`Image ${b+1}`,width:80,height:80,className:"w-full h-full object-cover"}),R?.status!=="approved"&&aY?.type==="image"&&aY?.url===a&&(0,d.jsx)(y.Button,{size:"sm",onClick:()=>bq(a),className:"absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white border-2 border-white shadow-lg z-10 transition-all duration-200",title:"Delete image",children:(0,d.jsx)(s.Trash2,{className:"w-3 h-3"})})]},`existing-${b}`))})}),(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(y.Button,{variant:"ghost",onClick:()=>as(!ar),className:`flex items-center gap-2 transition-colors ${S?"text-gray-300 hover:text-gray-200":"text-slate-600 hover:text-slate-700"}`,children:[(0,d.jsx)("span",{children:"Additional Notes"}),ar?(0,d.jsx)(p.ChevronUp,{className:"w-4 h-4"}):(0,d.jsx)(o.ChevronDown,{className:"w-4 h-4"})]}),(0,d.jsx)("div",{className:"sm:hidden",children:(R||$&&"saved"===aP)&&!bi&&(0,d.jsx)(y.Button,{onClick:()=>bu(),disabled:aa,className:"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-full px-6 py-2 font-medium w-32 justify-center",children:aa?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Creating..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.Wand2,{className:"w-4 h-4 mr-2"}),R&&(R.ai_generated_note||R.edited_note)?"Regenerate":"Generate"]})})})]}),(0,d.jsx)(w.AnimatePresence,{children:ar&&(0,d.jsx)(v.motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-3",children:(0,d.jsxs)("div",{className:`p-4 pr-4 sm:pr-20 rounded-lg transition-colors ${S?"bg-black":"bg-transparent"}`,children:[(0,d.jsx)("textarea",{value:ag,onChange:a=>be(a.target.value),placeholder:"Add any additional notes or observations...",rows:6,className:`w-full px-3 py-2 border rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors ${S?"bg-black border-gray-700 text-gray-100 placeholder-gray-400":"bg-white/70 backdrop-blur-sm border-white/30 text-slate-800 placeholder-slate-400"}`}),(0,d.jsx)("div",{className:"mt-2 flex justify-end",children:(0,d.jsx)(L.AutoSaveIndicator,{status:aP,onRetry:()=>aT(async()=>{if($&&T.trim()&&V)return a9($);throw Error("Nothing to save")}),showText:!1})})]})})})]}),(ac||aF||aa||bk)&&(0,d.jsxs)("div",{className:`py-6 pr-4 sm:pr-20 pl-2 rounded-lg transition-colors ${S?"bg-black":"bg-transparent"}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:`text-lg font-semibold ${S?"text-gray-100":"text-slate-800"}`,children:"Consultation Summary"}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(ac||bk)&&(0,d.jsxs)(d.Fragment,{children:[ae?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(y.Button,{variant:"ghost",size:"sm",onClick:bo,disabled:aL,className:`transition-colors ${S?"text-green-300 hover:text-green-200":"text-green-600 hover:text-green-700"}`,children:[aL?(0,d.jsx)(r.Loader2,{className:"w-4 h-4 mr-1 animate-spin"}):(0,d.jsx)(t.Save,{className:"w-4 h-4 mr-1"}),"Save"]}),(0,d.jsxs)(y.Button,{variant:"ghost",size:"sm",onClick:()=>{af(!1),aK("")},className:`transition-colors ${S?"text-gray-300 hover:text-gray-200":"text-slate-600 hover:text-slate-700"}`,children:[(0,d.jsx)(n.X,{className:"w-4 h-4 mr-1"}),"Cancel"]})]}):(0,d.jsxs)(y.Button,{variant:"ghost",size:"sm",onClick:()=>{let a=ac||R?.edited_note||R?.ai_generated_note||"";aK(a.includes("<")?P(a):a),af(!0)},className:`transition-colors ${S?"text-gray-300 hover:text-gray-200":"text-slate-600 hover:text-slate-700"}`,children:[(0,d.jsx)(l.Edit3,{className:"w-4 h-4 mr-1"}),"Edit"]}),(0,d.jsx)(y.Button,{variant:"ghost",size:"sm",onClick:bm,className:`transition-colors ${S?"text-gray-300 hover:text-gray-200":"text-slate-600 hover:text-slate-700"}`,children:az?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.Check,{className:"w-4 h-4 mr-1 text-green-500"}),"Copied"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.Copy,{className:"w-4 h-4 mr-1"}),"Copy"]})})]})})]}),aa&&(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(r.Loader2,{className:"w-8 h-8 animate-spin text-blue-600 mx-auto mb-3"}),(0,d.jsx)("p",{className:`text-sm ${S?"text-gray-300":"text-slate-600"}`,children:"Generating AI summary..."})]})}),aF&&(0,d.jsx)("div",{className:`rounded-lg p-4 border-l-4 border-blue-500 ${S?"bg-black":"bg-blue-50"}`,children:(0,d.jsx)("div",{className:"prose max-w-none",children:(0,d.jsxs)("div",{className:`whitespace-pre-wrap ${S?"text-gray-200":"text-gray-700"}`,children:[aF,(0,d.jsx)("span",{className:"inline-block w-2 h-5 bg-blue-500 animate-pulse ml-1"})]})})}),(ac||bk)&&!aa&&(0,d.jsx)(d.Fragment,{children:ae?(0,d.jsx)("div",{className:`rounded-lg border ${S?"bg-black border-gray-700":"bg-amber-50/50 border-orange-200"}`,children:(0,d.jsx)("textarea",{value:aJ,onChange:a=>aK(a.target.value),placeholder:"Edit your consultation summary in markdown...",rows:15,className:`w-full px-4 py-3 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 transition-colors font-mono text-sm ${S?"bg-black text-gray-100 placeholder-gray-400":"bg-amber-50/50 text-amber-800 placeholder-amber-600/60"}`})}):(0,d.jsx)(x.ContentEditableEditor,{content:R?.edited_note||R?.ai_generated_note||(ac?P(ac):"")||"",onChange:a=>{ad(O(a))},isEditing:!1,isDarkMode:S})})]}),(0,d.jsx)("input",{ref:a6,type:"file",accept:"image/*",multiple:!0,onChange:a=>a.target.files&&bc(a.target.files),className:"hidden"})]})}}},120756:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({RecordingMainArea:()=>h});var d=a.i(674420),e=a.i(722851),f=a.i(522295),g=a.i(843495);function h({selectedConsultation:a,isDarkMode:b,doctorId:c,doctorName:h,onConsultationUpdate:i}){let[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)(0),[p,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)("outpatient"),[x,y]=(0,e.useState)(""),[z,A]=(0,e.useState)([]),[B,C]=(0,e.useState)(!1),[D,E]=(0,e.useState)(""),[F,G]=(0,e.useState)(!1),H=(0,e.useRef)(null),I=(0,e.useRef)(null),J=(0,e.useRef)(null);(0,e.useEffect)(()=>{a?(u(a.patient_name||""),w(a.consultation_type||"outpatient"),y(a.additional_notes||""),E(a.edited_note||a.ai_generated_note||"")):(u(""),w("outpatient"),y(""),E("")),G(!1),q(null),s(null),A([]),k(!1),m(!1),o(0)},[a]),(0,e.useEffect)(()=>(j&&!l?J.current=setInterval(()=>{o(a=>a+1)},1e3):J.current&&(clearInterval(J.current),J.current=null),()=>{J.current&&clearInterval(J.current)}),[j,l]);let K=()=>["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/mpeg"].find(a=>MediaRecorder.isTypeSupported(a))||"audio/webm",L=async()=>{try{let a=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0}});I.current=a;let b=K(),c=new MediaRecorder(a,{mimeType:b});H.current=c;let d=[];c.ondataavailable=a=>{a.data.size>0&&d.push(a.data)},c.onstop=()=>{let c=new Blob(d,{type:b}),e=b.includes("webm")?"webm":b.includes("mp4")?"mp4":b.includes("mpeg")?"mp3":"webm",f=new File([c],`recording_${Date.now()}.${e}`,{type:b});q(c),s(f),k(!1),m(!1),a.getTracks().forEach(a=>a.stop())},c.start(),k(!0),m(!1),o(0)}catch(a){console.error("Recording error:",a),alert("Failed to start recording. Please check microphone permissions.")}},M=async a=>{try{let b=[];for(let c=0;c<a.length;c++){let d=a[c];if(!d.type.startsWith("image/")||d.size>0xa00000)continue;let e=URL.createObjectURL(d);b.push({id:`${Date.now()}-${c}`,file:d,preview:e})}A(a=>[...a,...b])}catch{console.error("Failed to process images")}};return(0,d.jsx)("div",{className:"h-full flex flex-col relative bg-transparent",children:(0,d.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,d.jsx)(g.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-5xl mx-auto",children:(0,d.jsx)(f.StreamlinedRecordingArea,{selectedConsultation:a,isDarkMode:b,doctorId:c,doctorName:h,onConsultationUpdate:b=>{i(b),b&&!a&&(q(null),s(null),A([]),C(!1))},patientName:t,setPatientName:u,selectedTemplate:v,setSelectedTemplate:w,isRecording:j,isPaused:l,recordingDuration:n,audioBlob:p,audioFile:r,images:z,setImages:A,isGenerating:B,setIsGenerating:C,summary:D,setSummary:E,isEditing:F,setIsEditing:G,additionalNotes:x,setAdditionalNotes:y,startRecording:L,pauseRecording:()=>{H.current&&j&&(l?(H.current.resume(),m(!1)):(H.current.pause(),m(!0)))},stopRecording:()=>{H.current&&j&&H.current.stop()},handleImageUpload:M,removeImage:a=>{A(b=>{let c=b.find(b=>b.id===a);return c&&c.preview&&URL.revokeObjectURL(c.preview),b.filter(b=>b.id!==a)})},clearAudio:()=>{q(null),s(null)}})})})})}},102803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],c=(0,d.default)("menu",b)}},62836:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Menu:()=>d.default});var d=a.i(102803)},184590:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],c=(0,d.default)("moon",b)}},674304:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Moon:()=>d.default});var d=a.i(184590)},423643:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],c=(0,d.default)("sun",b)}},138013:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Sun:()=>d.default});var d=a.i(423643)},159803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],c=(0,d.default)("user",b)}},254330:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({User:()=>d.default});var d=a.i(159803)},365020:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],c=(0,d.default)("info",b)}},891975:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Info:()=>d.default});var d=a.i(365020)},635363:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({NewRecordingInterface:()=>t});var d=a.i(674420),e=a.i(722851),f=a.i(816486),g=a.i(365171),h=a.i(308591),i=a.i(120756),j=a.i(843495),k=a.i(862772),l=a.i(62836),m=a.i(674304),n=a.i(138013),o=a.i(254330),p=a.i(265033),q=a.i(891975),r=a.i(558981),s=a.i(141126);function t({user:a,consultations:b,hasMore:c,doctorId:t}){let[u,v]=(0,e.useState)(!1),[w,x]=(0,e.useState)(!1),[y,z]=(0,e.useState)(null),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(!1);(0,e.useEffect)(()=>{let a=()=>{B(window.innerWidth<=640),window.innerWidth>640&&x(!1)};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]),(0,e.useEffect)(()=>{let a=localStorage.getItem("darkMode");a?v("true"===a):v(!1)},[]),(0,e.useEffect)(()=>{u?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode",u.toString())},[u]);let E=a=>{z(a),A&&x(!1)};return(0,d.jsxs)("div",{className:`min-h-screen transition-colors duration-300 overflow-x-hidden ${u?"bg-black text-gray-100":"bg-gradient-to-br from-indigo-50 via-white to-cyan-50 text-gray-900"}`,children:[!u&&(0,d.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,d.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"}),(0,d.jsx)("div",{className:"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"})]}),(0,d.jsx)("nav",{className:`fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20 transition-opacity duration-300 ${A&&w?"opacity-0 pointer-events-none":"opacity-100"}`,children:(0,d.jsxs)("div",{className:`flex items-center ${A?"space-x-4":"space-x-8"}`,children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative w-8 h-8",children:(0,d.jsx)(g.default,{src:"/celer-ai-logo.svg",alt:"Celer AI",width:32,height:32,className:"rounded-lg"})}),!A&&(0,d.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold",children:"Celer AI"})]}),(0,d.jsxs)("div",{className:`flex items-center ${A?"space-x-2":"space-x-3"}`,children:[A&&(0,d.jsx)("button",{onClick:()=>{x(!w)},className:"p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105",children:(0,d.jsx)(l.Menu,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>{v(!u)},className:"p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105",children:u?(0,d.jsx)(n.Sun,{className:"w-4 h-4"}):(0,d.jsx)(m.Moon,{className:"w-4 h-4"})}),A?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.default,{href:"/info",className:"p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105",title:"Analytics",children:(0,d.jsx)(q.Info,{className:"w-4 h-4"})}),(0,d.jsx)(f.default,{href:"/settings",className:"p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105",title:"Settings",children:(0,d.jsx)(p.Settings,{className:"w-4 h-4"})})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.default,{href:"/info",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Analytics"}),(0,d.jsx)(f.default,{href:"/settings",className:"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors",children:"Settings"})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("button",{onClick:()=>D(!C),className:"p-2 rounded-xl transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 text-slate-700 hover:scale-105",children:(0,d.jsx)(o.User,{className:"w-4 h-4"})}),(0,d.jsx)(k.AnimatePresence,{children:C&&(0,d.jsx)(j.motion.div,{initial:{opacity:0,scale:.95,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:-10},transition:{duration:.15},className:"absolute right-0 mt-2 w-48 rounded-xl shadow-2xl z-50 bg-white/95 backdrop-blur-xl border border-white/30",children:(0,d.jsxs)("div",{className:"py-1",children:[(0,d.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,d.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["Dr. ",a?.name||"Doctor"]}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:a?.email})]}),(0,d.jsx)("form",{action:s.logout,children:(0,d.jsxs)("button",{type:"submit",className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 transition-colors",onClick:()=>D(!1),children:[(0,d.jsx)(r.LogOut,{className:"w-4 h-4 mr-3"}),"Logout"]})})]})})})]})]})]})}),(0,d.jsxs)("div",{className:`flex h-screen pt-20 ${u?"bg-black":"bg-gradient-to-br from-indigo-50 via-white to-cyan-50"}`,children:[!A&&(0,d.jsx)("div",{className:"w-80 border-r-0",children:(0,d.jsx)(h.ConsultationsSidebar,{consultations:b,hasMore:c,onConsultationSelect:E,selectedConsultation:y,isDarkMode:u,doctorId:t})}),(0,d.jsx)(k.AnimatePresence,{children:A&&w&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 z-40",onClick:()=>x(!1)}),(0,d.jsx)(j.motion.div,{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"},transition:{type:"spring",damping:25,stiffness:200},className:`fixed left-0 top-0 bottom-0 w-[75%] z-40 transition-colors duration-300 ${u?"bg-gray-950":"bg-gradient-to-br from-indigo-50 via-white to-cyan-50"}`,children:(0,d.jsx)(h.ConsultationsSidebar,{consultations:b,hasMore:c,onConsultationSelect:E,selectedConsultation:y,isDarkMode:u,doctorId:t,isMobile:!0,onClose:()=>x(!1)})})]})}),(0,d.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,d.jsx)(i.RecordingMainArea,{selectedConsultation:y,isDarkMode:u,doctorId:t,doctorName:a?.name||void 0,onConsultationUpdate:a=>{z(a)}})})]})]})}},959293:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],c=(0,d.default)("star",b)}},315806:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Star:()=>d.default});var d=a.i(959293)},996137:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ReferredWelcomeModal:()=>j});var d=a.i(674420),e=a.i(767332),f=a.i(485878),g=a.i(605754),h=a.i(315806),i=a.i(533784);function j({isOpen:a,onClose:b,referralInfo:c}){return a&&c?.referred_by?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl max-w-sm sm:max-w-md w-full mx-2 sm:mx-4 overflow-hidden max-h-[95vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-teal-500 to-emerald-600 p-4 sm:p-6 text-white relative",children:[(0,d.jsx)("button",{onClick:b,className:"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors",children:(0,d.jsx)(e.X,{className:"w-5 h-5"})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4",children:(0,d.jsx)(g.Gift,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,d.jsx)("h2",{className:"text-xl sm:text-2xl font-bold mb-2",children:"Welcome to Celer AI!"}),(0,d.jsx)("p",{className:"text-sm sm:text-base text-emerald-100",children:"You're part of a trusted network"})]})]}),(0,d.jsxs)("div",{className:"p-4 sm:p-6",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 p-3 sm:p-4 rounded-lg border border-blue-200 mb-4 sm:mb-6",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(f.Users,{className:"w-4 h-4 sm:w-5 sm:h-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-xs sm:text-sm text-blue-700 font-medium",children:"Referred by"}),(0,d.jsxs)("p",{className:"text-base sm:text-lg font-bold text-blue-900",children:["Dr. ",c.referred_by.name]})]})]})}),(0,d.jsx)("div",{className:"text-center mb-4 sm:mb-6",children:(0,d.jsxs)("p",{className:"text-sm sm:text-base text-gray-700 leading-relaxed",children:["You're here because ",(0,d.jsxs)("span",{className:"font-semibold text-teal-600",children:["Dr. ",c.referred_by.name]})," trusts Celer AI. Happy Consulting! 🎉"]})}),(0,d.jsxs)("div",{className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6",children:[(0,d.jsx)("h3",{className:"text-sm sm:text-base font-semibold text-gray-900 text-center",children:"What's included:"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)(h.Star,{className:"w-4 h-4 text-yellow-500 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm text-gray-700",children:"50 AI-powered consultations monthly"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)(g.Gift,{className:"w-4 h-4 text-emerald-500 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm text-gray-700",children:"Advanced medical templates"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)(i.Crown,{className:"w-4 h-4 text-purple-500 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm text-gray-700",children:"Priority support from our team"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-amber-50 to-yellow-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4 sm:mb-6",children:[(0,d.jsx)("h4",{className:"text-sm sm:text-base font-semibold text-amber-800 mb-2",children:"Join the Referral Program!"}),(0,d.jsxs)("p",{className:"text-xs sm:text-sm text-amber-700 leading-relaxed",children:["You can also refer colleagues and earn 10% off your next bill for each successful referral. After 3 successful referrals, you'll become a ",(0,d.jsx)("span",{className:"font-semibold",children:"Celer AI Ambassador"}),"!"]})]}),(0,d.jsx)("button",{onClick:b,className:"w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm sm:text-base",children:"Start Consulting"})]})]})}):null}},114263:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({DashboardClient:()=>h});var d=a.i(674420),e=a.i(722851),f=a.i(996137),g=a.i(321167);function h({doctorId:a}){let[b,c]=(0,e.useState)(!1),[h,i]=(0,e.useState)(null);return(0,e.useEffect)(()=>{(async()=>{if(!localStorage.getItem(`welcome_seen_${a}`)){let b=await (0,g.getReferralInfo)(a);b.success&&b.data.referred_by&&(i(b.data),c(!0))}})()},[a]),(0,d.jsx)(f.ReferredWelcomeModal,{isOpen:b,onClose:()=>{c(!1),localStorage.setItem(`welcome_seen_${a}`,"true")},referralInfo:h})}},209614:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(528442);let b=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],c=(0,d.default)("download",b)}},897689:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Download:()=>d.default});var d=a.i(209614)},337582:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({PWAInstallPrompt:()=>k});var d=a.i(674420),e=a.i(722851),f=a.i(767332),g=a.i(897689),h=a.i(843495),i=a.i(862772),j=a.i(365171);function k(){let[a,b]=(0,e.useState)(null),[c,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)(!1);(0,e.useEffect)(()=>{let a=()=>{o(window.innerWidth<=768)},c=window.matchMedia("(display-mode: standalone)").matches,d=!0===window.navigator.standalone,e=window.matchMedia("(display-mode: standalone)").matches;m(c||d||e),a();let f=a=>{a.preventDefault(),b(a);let c=localStorage.getItem("pwa_install_dismissed"),d=localStorage.getItem("pwa_install_last_dismissed");(!c||d&&Date.now()-parseInt(d)>6048e5)&&setTimeout(()=>{k(!0)},3e3)},g=()=>{m(!0),k(!1),b(null),localStorage.setItem("pwa_installed","true")};return window.addEventListener("beforeinstallprompt",f),window.addEventListener("appinstalled",g),window.addEventListener("resize",a),()=>{window.removeEventListener("beforeinstallprompt",f),window.removeEventListener("appinstalled",g),window.removeEventListener("resize",a)}},[]);let p=async()=>{if(a)try{await a.prompt();let{outcome:c}=await a.userChoice;"accepted"===c?(console.log("PWA install accepted"),localStorage.setItem("pwa_install_accepted","true")):(console.log("PWA install dismissed"),localStorage.setItem("pwa_install_dismissed","true"),localStorage.setItem("pwa_install_last_dismissed",Date.now().toString())),k(!1),b(null)}catch(a){console.error("Error during PWA install:",a)}},q=()=>{k(!1),localStorage.setItem("pwa_install_dismissed","true"),localStorage.setItem("pwa_install_last_dismissed",Date.now().toString())};return!l&&n&&a&&c?(0,d.jsx)(i.AnimatePresence,{children:(0,d.jsx)(h.motion.div,{initial:{opacity:0,y:100,scale:.8},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:100,scale:.8},transition:{type:"spring",damping:25,stiffness:200},className:"fixed bottom-4 right-4 z-50 max-w-sm",children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl border border-gray-200 p-4 mx-4",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 rounded-lg overflow-hidden",children:(0,d.jsx)(j.default,{src:"/icons/icon-72x72.png",alt:"Celer AI",width:72,height:72,className:"w-full h-full object-cover",priority:!0})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-sm font-semibold text-gray-900",children:"Install Celer AI"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Add to home screen"})]})]}),(0,d.jsx)("button",{onClick:q,className:"text-gray-400 hover:text-gray-600 transition-colors p-1",children:(0,d.jsx)(f.X,{className:"w-4 h-4"})})]}),(0,d.jsx)("p",{className:"text-xs text-gray-600 mb-3 leading-relaxed",children:"Get faster access and a better experience with our app."}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("button",{onClick:p,className:"flex-1 bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1",children:[(0,d.jsx)(g.Download,{className:"w-3 h-3"}),(0,d.jsx)("span",{children:"Install"})]}),(0,d.jsx)("button",{onClick:q,className:"px-3 py-2 text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"Not now"})]})]})})}):null}}};

//# sourceMappingURL=_31ffe479._.js.map