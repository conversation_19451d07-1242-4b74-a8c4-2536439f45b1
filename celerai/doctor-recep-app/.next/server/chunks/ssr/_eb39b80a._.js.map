{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/shared/src/utils.ts", "turbopack:///[project]/node_modules/lucide-react/src/defaultAttributes.ts", "turbopack:///[project]/node_modules/lucide-react/src/Icon.ts", "turbopack:///[project]/node_modules/lucide-react/src/createLucideIcon.ts", "turbopack:///[project]/node_modules/next/dist/client/app-dir/link.js/proxy.cjs", "turbopack:///[project]/node_modules/lucide-react/src/icons/arrow-left.ts", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-page.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/stethoscope.ts", "turbopack:///[project]/src/app/terms/page.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n", "import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n", "import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 2v2', key: '1539x4' }],\n  ['path', { d: 'M5 2v2', key: '1yf1q8' }],\n  ['path', { d: 'M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1', key: 'rb5t3r' }],\n  ['path', { d: 'M8 15a6 6 0 0 0 12 0v-3', key: 'x18d4x' }],\n  ['circle', { cx: '20', cy: '10', r: '2', key: 'ts1r5v' }],\n];\n\n/**\n * @component @name Stethoscope\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMnYyIiAvPgogIDxwYXRoIGQ9Ik01IDJ2MiIgLz4KICA8cGF0aCBkPSJNNSAzSDRhMiAyIDAgMCAwLTIgMnY0YTYgNiAwIDAgMCAxMiAwVjVhMiAyIDAgMCAwLTItMmgtMSIgLz4KICA8cGF0aCBkPSJNOCAxNWE2IDYgMCAwIDAgMTIgMHYtMyIgLz4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjEwIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/stethoscope\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Stethoscope = createLucideIcon('stethoscope', __iconNode);\n\nexport default Stethoscope;\n", "import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { ArrowLeft, Stethoscope } from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Terms of Service - Celer AI',\n  description: 'Terms of Service for Celer AI medical documentation platform',\n}\n\nexport default function TermsOfService() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50\">\n      <div className=\"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-teal-600 hover:text-teal-700 mb-4\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Back to Home\n          </Link>\n          <div className=\"flex items-center space-x-3 mb-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-lg flex items-center justify-center\">\n              <Stethoscope className=\"w-5 h-5 text-white\" />\n            </div>\n            <h1 className=\"text-3xl font-bold text-slate-800\">Terms of Service</h1>\n          </div>\n          <p className=\"text-slate-600\">Last updated: June 17, 2025</p>\n        </div>\n\n        {/* Content */}\n        <div className=\"bg-white/80 backdrop-blur-sm shadow-lg rounded-lg border border-orange-200/50 p-8\">\n          <div className=\"prose prose-slate max-w-none text-black\">\n            \n            <h2>1. Acceptance of Terms</h2>\n            <p>\n              By accessing and using Celer AI (&quot;the Service&quot;), you accept and agree to be bound by the terms and provision of this agreement.\n              If you do not agree to abide by the above, please do not use this service.\n            </p>\n\n            <h2>2. Description of Service</h2>\n            <p>\n              Celer AI is an AI-powered medical documentation platform designed to assist healthcare providers in generating consultation summaries, \n              reports, and medical documentation. The Service is intended for use by licensed medical professionals only.\n            </p>\n\n            <h2>3. User Responsibilities</h2>\n            <p>You agree to:</p>\n            <ul>\n              <li>Provide accurate and complete information when using the Service</li>\n              <li>Use the Service only for lawful purposes and in accordance with applicable medical regulations</li>\n              <li>Maintain the confidentiality of your account credentials</li>\n              <li>Review and verify all AI-generated content before using it in medical practice</li>\n              <li>Comply with all applicable laws, regulations, and professional standards</li>\n            </ul>\n\n            <h2>4. Medical Disclaimer</h2>\n            <p>\n              <strong>IMPORTANT:</strong> Celer AI is a documentation tool only. It does not provide medical advice, diagnosis, or treatment recommendations. \n              All AI-generated content must be reviewed, verified, and approved by qualified medical professionals before use. \n              The Service is not a substitute for professional medical judgment, and users remain fully responsible for all medical decisions and patient care.\n            </p>\n\n            <h2>5. Limitation of Liability</h2>\n            <p>\n              TO THE MAXIMUM EXTENT PERMITTED BY LAW, CELER AI AND ITS AFFILIATES SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, \n              SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO LOSS OF PROFITS, DATA, USE, GOODWILL, \n              OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR USE OF THE SERVICE.\n            </p>\n\n            <h2>6. Data Privacy and Security</h2>\n            <p>\n              We take data privacy seriously and implement appropriate security measures to protect your information. \n              Please refer to our Privacy Policy for detailed information about how we collect, use, and protect your data.\n            </p>\n\n            <h2>7. Intellectual Property</h2>\n            <p>\n              The Service and its original content, features, and functionality are owned by Celer AI and are protected by \n              international copyright, trademark, patent, trade secret, and other intellectual property laws.\n            </p>\n\n            <h2>8. Subscription and Payment Terms</h2>\n            <p>\n              Subscription fees are charged in advance on a monthly basis. All fees are non-refundable except as required by law. \n              We reserve the right to change our pricing with 30 days&apos; notice.\n            </p>\n\n            <h2>9. Termination</h2>\n            <p>\n              We may terminate or suspend your account and access to the Service immediately, without prior notice or liability, \n              for any reason whatsoever, including without limitation if you breach the Terms.\n            </p>\n\n            <h2>10. Governing Law</h2>\n            <p>\n              These Terms shall be interpreted and governed by the laws of India. Any disputes arising from these Terms \n              shall be subject to the exclusive jurisdiction of the courts in India.\n            </p>\n\n            <h2>11. Changes to Terms</h2>\n            <p>\n              We reserve the right to modify these Terms at any time. We will notify users of any material changes via email \n              or through the Service. Continued use of the Service after such modifications constitutes acceptance of the updated Terms.\n            </p>\n\n            <h2>12. Contact Information</h2>\n            <p>\n              If you have any questions about these Terms of Service, please contact us at:\n            </p>\n            <ul>\n              <li>Email: <EMAIL></li>\n              <li>Phone: +91 **********</li>\n              <li>WhatsApp: +91 **********</li>\n            </ul>\n\n            <div className=\"mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg\">\n              <p className=\"text-sm text-amber-800 mb-0\">\n                <strong>Professional Use Only:</strong> This service is intended exclusively for licensed healthcare professionals. \n                By using Celer AI, you confirm that you are a qualified medical practitioner authorized to provide medical care.\n              </p>\n            </div>\n\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "module", "exports", "require", "AppPageRouteModule", "tree", "pages", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": "qUA0BQM,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,oTClBpB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAc,AAAC,CAAf,AAAe,CAAA,AAAf,CAAA,AAAe,AAC1B,CADW,AAAe,AAC1B,CADW,AAAe,AAC1B,AAAO,CAAP,AADW,CACX,AADW,CACX,AADW,CACX,AADW,CAAA,CAAA,CACJ,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA,CAQ/C,EAAiC,AAAnB,GACzB,CAAA,CAAA,AAAO,CADqC,AAC5C,AAAO,CAAP,AAAO,CAAP,AAAO,CAAA,AADI,CACJ,AADkB,CAClB,AADkB,CAClB,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,EAAO,CAAI,CAAA,CAAA,CAAA,CAClD,CAAA,CAAA,CAAK,CAAA,CAAG,AAAH,AAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,AAAG,WAAY,CAAA,GAS9B,CAAA,CAAe,AAAf,AAAkC,CAAA,AAAlC,CAAkC,AAAlC,CAAkC,AAAlC,CAAkC,AACvC,AADK,CAAkC,AAAlC,CAAkE,AAAlE,CAAkE,AAAlE,CAAA,AAAkE,AACvE,CADuE,AACvE,AADK,CACL,AADK,AACO,AAD2D,CACvE,AADK,AAAkE,CACvE,AAAwB,AADnB,CACL,AAAwB,CAAxB,AAAwB,CAAxB,AAEE,AAFsB,CAAA,AAAxB,CAAwB,AAAxB,CAA8B,AAA9B,CAA8B,AAA9B,GAEE,CAAA,CAAA,AAAU,CAAV,CAAA,CAAA,CAAA,CAAA,CAAA,AAAU,CAAO,AAAjB,CAAkB,CAAA,CAAE,WAAA,EAAgB,CAAA,CAAA,CAAA,AAAU,CAAV,CAAA,CAAA,CAAA,CAAA,AAAU,CAAV,AAAgB,CAAhB,AAAiB,CAAA,AAC/D,CAAA,CAQa,EAAe,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AACrE,CADqE,AACrE,CADqE,AACrE,AACG,AAFQ,CACX,AAD0B,CAC1B,AAD0B,CAC1B,CAAA,CAAA,CACG,CAAO,CAAC,CAAA,CAAW,AAAX,CAAA,CAAkB,AAAlB,CAAA,CAAA,CAAA,CAAA,CAAA,AAA4B,AAEjC,CAFK,AAA4B,AAEzB,CAFyB,AAEzB,CAFyB,AAEzB,CAFyB,AAEzB,AACyB,CAHA,AAEzB,AAER,CAFQ,AAER,CAFA,AAAQ,AAER,CAFQ,AAER,CAAA,AAFQ,AACP,CADgB,AAChB,CAAqB,AADL,AAChB,CAAqB,AAArB,CAAqB,AAArB,CAAA,AAAqB,AAArB,CAAA,AAAqB,CAArB,AAAqB,CAArB,CAAqB,CACtB,EAAM,CAAA,CAAA,CAAN,AAAM,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,AAAM,CAAN,AAAM,CAAN,AAAM,AAGhC,CAHgC,AAGhC,AAH0B,CAAM,AAGhC,AAHgC,CAGhC,AAHgC,CAGhC,AAHgC,CAAA,AAG3B,CAH2B,EAGxB,CAAA,CACR,IAAK,CAAA,CAAA,CAQG,CAAA,CAAA,AAAc,AAAC,CAAf,AAAe,CAAf,AAAe,CAAf,AAAe,CAAA,AAC1B,AADW,CAAA,AAA8C,AACzD,CADW,AAA8C,AACzD,CADW,AAA8C,AACzD,CADyD,AACzD,AADW,CAA8C,AACzD,AADW,CAA8C,AACzD,AADW,CACX,AADW,CACX,AAAW,CAAX,IAAmB,EACjB,CAAI,CAAA,CADoB,AACpB,CADoB,AACpB,CAAA,AAAK,CAAL,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,AAAS,CAAT,AAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAT,AAAS,CAAT,CAAA,CAAA,AAA4B,CAAnB,MAA4B,CAAA,CAAA,CAAlB,EAC1C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAGb,CAAA,EAHa,oEC7Db,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CACb,AADa,CACb,AADa,CAAA,AACb,CADa,AACb,CADa,AACb,CADa,AACN,CADM,AACN,CAAA,AADM,CACN,AADM,CACN,AADM,CAAA,AACN,CADM,AACN,CADM,AACN,CADM,AACN,CAAA,AADM,CACN,AADM,CACN,AADM,CACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACT,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA,CAAA,CACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAClB,CAAA,oHCcA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,AAAO,CAAA,AAAP,CAAO,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACX,CACE,OACE,CAAA,CAAA,AAAQ,CAAR,AAAQ,CAAA,AAAR,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MACR,CAAA,CAAA,AAAO,CAAA,AAAP,CAAO,aACP,CAAA,CAAA,AAAc,CAAd,AAAc,CAAd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,aACd,CAAA,CAAA,AACA,CADA,AACA,CAAA,AADA,CACA,AADA,CACA,AADA,CAAA,AACA,CADA,AACA,CAAA,AADA,CACA,AADA,CACY,AADZ,CAAA,CAAA,CAAA,AACY,CADZ,AACY,CADZ,AACY,CADZ,CAAA,QAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IACA,CAAA,CAAA,AACA,CADA,CAAA,CAAA,AACG,CADH,AACG,CADH,AACG,CAAA,AAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,KACE,CAAA,CAAA,AACA,CADA,EACA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,AADG,CAAA,AACH,AADG,CACH,AADG,CACH,AADG,CACH,AADG,CACI,AADJ,CACI,CAAA,CAAA,AACP,CADO,AACP,CADO,AACP,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,AACR,CADQ,AACR,CADQ,AACR,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,AACR,CADQ,CAAA,CAAA,QACR,CAAa,CAAA,CAA6C,AAA7C,CAA6C,AAA7C,CAAmD,AAAnD,CAAA,AAAuB,CAAvB,AAAuB,CAAvB,AAAuB,CAAvB,AAAuB,CAAvB,AAAuB,CAAvB,AAAuB,CAAvB,AAA8B,CAA9B,AAA8B,CAA9B,AAAyC,CAAzC,AAAyC,CAAU,AAAV,AAAzC,CAAyC,AAAU,AAAnD,CAAyC,AAAU,AAAnD,CAAyC,AAAzC,AAAmD,CAAV,AAAU,CAAV,AAAlB,AAA4B,CAAA,AAA5B,AAAkB,CAAiB,AAAjB,CAAA,AAAqB,CAAA,CAAA,AAAI,AAArB,CAAiB,AAAI,AAArB,CAAqB,AAC/E,CAD+E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAC/E,CAAW,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,AAAV,GACxB,CAAI,CAAA,CAAA,CAAC,CAAY,CAD0B,AAC1B,CAD0B,AAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,AAAD,CAAA,CAAA,SAAC,EAAY,CAAI,CAAA,CAAA,CAAA,AAAK,CAAE,AAAF,AAAL,CAAK,AAAE,CAAA,AAAF,CAAE,AAAF,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAe,MAAO,CAAA,CAC/D,GAAG,CAAA,AACL,AADK,CACL,AADK,CAEL,AAFK,IAGA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,AAAI,CAAJ,AAAK,CAAL,AAAM,CAAK,CAAA,CAAA,AAAK,CAAA,AAAM,AAAX,CAAK,CAAA,CAAA,AAAM,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAc,EAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,AACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,AAAY,CAAZ,AAAY,CAAZ,AAAY,AAAW,CAAX,AAAY,AAAxB,CAAY,AAAY,AAAxB,CAAY,AAAY,AAAhB,AAAwB,CAApB,AAAY,AAC5C,CADgC,AAAY,CAAZ,AAAY,AAAZ,CAAY,AAAZ,CAAA,AAAY,CAAQ,mHC1CpD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAAA,CAAA,AAAkB,CAAlB,CAAA,CAAA,CAAA,CAAA,AACxB,CADwB,AACxB,CADwB,AACxB,CAAA,AADiE,CAAA,AACjE,AAAM,CAD2D,AACjE,AAAkB,CAD+C,AAC/C,AADd,AACc,CAD+C,AAC/C,AADK,CAA0C,AAA1C,AACL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAuC,CAAC,CAAA,UAAE,CAAW,CAAA,CAAA,AAAG,CAAH,AAAG,CAAH,AAAG,CAAH,AAAG,CAAA,AAAS,AAAZ,CAAA,AAAY,AAAT,AAAS,CAAA,AAAT,CAAS,CAAA,CAAA,CAAA,EAAA,EACjF,aAAA,EAAA,EAAc,OAAM,CAAA,KAClB,CAAA,CAAA,CAAA,QACA,CAAA,CAAA,AACA,CADA,AACA,CADA,AACA,CADA,AACA,CADA,AACA,CADA,AACA,CADA,AACA,CAAA,CAAA,CAAW,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,EAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,AAAE,CAAF,AAAE,CAAF,AAC3C,CAD2C,AAC3C,CAD2C,CAAC,CAAC,IAC7C,CAAU,CAAA,CAAA,CAAQ,AAAR,CAAA,AAAQ,CAAR,AACV,CADU,AACV,CADU,AACV,CADU,AACV,AAEF,CAHoB,AAClB,CAAA,CAAA,AAEC,CAFD,AAEC,CACJ,AAHG,AAEC,CAAA,CAAA,CAMA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAFG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAc,YAAA,EAAa,CAAA,CAAA,CAAA,AAE9B,CAF8B,AAGvC,CAHuC,AAGvC,CAHuC,CAAA,CAAQ,CAAA,GAEtC,+CC3BT,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,qIAFhD,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,2PCCzC,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AADyB,CACzB,AADyB,CACvB,AAAF,AADyB,EACpB,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC/C,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC3C,CAaM,EAAY,CAAA,EAAA,EAAA,CAAA,CAAZ,AAAY,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oTClB3D,IAAA,EAAmC,EAAA,CAA1BC,AAA0B,CAAA,QAAiG,EAAA,EAAA,CAAA,CAAA,GAAzG,KACgC,EAAwB,AADW,CAA3D,CACgD,CAAA,CAAA,QAWnF,EAAA,EAAA,CAAA,CAAA,GAAyE,CAXU,IAanF,EAAc,EAAA,CAAA,CAAA,IAAA,GAGd,EAAsB,EAAA,CAAbC,AAAa,CAAA,GAAT,EAAEC,GAEyD,EAAwB,AAF5E,EAE4E,CAAA,AAF1E,CAE0E,QAOhG,EAAiC,EAAA,CAAA,CAAA,IAP+D,gBAchG,GAPiC,CAOjC,EAAA,CAAc,GAAA,KAA4C,KAAA,CAAA,QAAA,CAA8C,EAAC,GAAvB,IAAuB,CAAA,QAAjD,GAAiD,CAEzG,EAAA,CACA,KAAO,IAAA,CAAMG,EAAAA,KAAc,CAAA,GAAIL,CAAAA,EAAmB,cAAA,kBADU,GACV,IAChDM,KACEC,EACAG,GAAAA,CADMF,AACA,CAFI,AAEJ,EAAA,OADUC,AAEhBE,EACA,CAAA,CAAA,IAHwB,AAGxB,EADU,AACV,EAA2C,6BAAA,OAC3CC,MAAAA,CAAAA,IAAY,EAAA,wEAAA,OACZC,IAAAA,CAAAA,EAAU,EAAA,EAAA,wEAAA,OACVC,OAAU,CAAA,CAAE,GAAA,EAAA,2EAAA,GACd,aAAA,CAAA,IAAA,EAAA,qCAAA,IACAC,CACEC,CAAAA,KAAYf,GADJ,2BACIA,yjHC7CT,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AADyB,CACzB,AADyB,CACzB,AAAE,AADuB,EACpB,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACvC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAA4D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACzF,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACxD,CAAC,QAAU,CAAA,CAAA,AAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAI,CAAA,CAAA,IAAA,CAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC1D,CAaM,EAAc,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,AAAd,CAAc,CAAA,CAAA,CAAA,CAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,kMCrB9D,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAEO,IAAM,EAAqB,CAChC,MAAO,8BACP,YAAa,8DACf,EAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,SAFX,mEAIC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAApB,CAAqC,kBAGxC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8GACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,cAAtB,WAEH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,wBAEpD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,0BAAiB,mCAIhC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6FACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,2BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,+MAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,8BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,uPAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,6BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,kBACH,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,qEACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mGACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,6DACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mFACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gFAGN,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,0BACJ,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,UAAO,eAAmB,6XAK7B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,+BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,8SAMH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,iCACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,0NAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,6BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,iNAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,sCACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,yLAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,mBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,wMAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,sBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,qLAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,yBACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,8OAKH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,4BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,kFAGH,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,+BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,0BACJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UAAG,gCAGN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mEACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,wCACX,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,UAAO,2BAA+B,8MAUvD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}