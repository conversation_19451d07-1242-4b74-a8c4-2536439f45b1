{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from '@/lib/types'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/referrals.ts"], "sourcesContent": ["'use server'\n\nimport { createClient } from '@/lib/supabase/server'\nimport { ReferralInfo, ApiResponse } from '@/lib/types'\nimport { revalidatePath } from 'next/cache'\n\nexport async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {\n  try {\n    const supabase = await createClient()\n\n    // Get doctor's referral information\n    const { data: doctor, error: doctorError } = await supabase\n      .from('doctors')\n      .select(`\n        referral_code,\n        total_referrals,\n        successful_referrals,\n        referral_discount_earned,\n        referred_by\n      `)\n      .eq('id', doctorId)\n      .single()\n\n    if (doctorError || !doctor) {\n      return { success: false, error: 'Failed to fetch referral information' }\n    }\n\n    // Get pending referrals count\n    const { count: pendingCount } = await supabase\n      .from('referral_analytics')\n      .select('*', { count: 'exact', head: true })\n      .eq('referrer_id', doctorId)\n      .eq('status', 'pending')\n\n    // Get recent referrals\n    const { data: recentReferrals, error: referralsError } = await supabase\n      .from('referral_analytics')\n      .select(`\n        id,\n        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),\n        signup_date,\n        conversion_date,\n        status\n      `)\n      .eq('referrer_id', doctorId)\n      .order('created_at', { ascending: false })\n      .limit(10)\n\n    if (referralsError) {\n      return { success: false, error: 'Failed to fetch recent referrals' }\n    }\n\n    // Get referrer info separately if exists\n    let referredBy = null\n    if (doctor.referred_by) {\n      const { data: referrer } = await supabase\n        .from('doctors')\n        .select('name, referral_code')\n        .eq('id', doctor.referred_by)\n        .single()\n      \n      if (referrer) {\n        referredBy = {\n          name: referrer.name,\n          referral_code: referrer.referral_code || ''\n        }\n      }\n    }\n\n    const referralInfo: ReferralInfo = {\n      referral_code: doctor.referral_code || '',\n      total_referrals: doctor.total_referrals || 0,\n      successful_referrals: doctor.successful_referrals || 0,\n      pending_referrals: pendingCount || 0,\n      discount_earned: doctor.referral_discount_earned || 0,\n      referred_by: referredBy,\n      recent_referrals: (recentReferrals || []).map(ref => ({\n        id: ref.id,\n        name: ref.referred_doctor?.name || 'Unknown',\n        email: ref.referred_doctor?.email || 'Unknown',\n        signup_date: ref.signup_date,\n        conversion_date: ref.conversion_date,\n        status: ref.status as 'pending' | 'converted' | 'expired'\n      }))\n    }\n\n    return { success: true, data: referralInfo }\n  } catch (error) {\n    console.error('Error fetching referral info:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('referral_code')\n      .eq('id', doctorId)\n      .single()\n\n    if (error || !doctor?.referral_code) {\n      return { success: false, error: 'Failed to fetch referral code' }\n    }\n\n    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'\n    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`\n\n    return { success: true, data: referralLink }\n  } catch (error) {\n    console.error('Error generating referral link:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Find the referrer\n    const { data: referrer, error: referrerError } = await supabase\n      .from('doctors')\n      .select('id, name')\n      .eq('referral_code', referralCode)\n      .single()\n\n    if (referrerError || !referrer) {\n      return { success: false, error: 'Invalid referral code' }\n    }\n\n    // Update the new doctor with referrer information\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ referred_by: referrer.id })\n      .eq('id', newDoctorId)\n\n    if (updateError) {\n      return { success: false, error: 'Failed to process referral signup' }\n    }\n\n    // Create referral analytics record\n    const { error: analyticsError } = await supabase\n      .from('referral_analytics')\n      .insert({\n        referrer_id: referrer.id,\n        referred_doctor_id: newDoctorId,\n        referral_code: referralCode,\n        status: 'pending'\n      })\n\n    if (analyticsError) {\n      console.error('Failed to create analytics record:', analyticsError)\n      // Don't fail the signup for this\n    }\n\n    // Update referrer's total referrals count\n    const { data: currentReferrer } = await supabase\n      .from('doctors')\n      .select('total_referrals')\n      .eq('id', referrer.id)\n      .single()\n\n    if (currentReferrer) {\n      const { error: countError } = await supabase\n        .from('doctors')\n        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })\n        .eq('id', referrer.id)\n\n      if (countError) {\n        console.error('Failed to update referral count:', countError)\n      }\n    }\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Error processing referral signup:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const supabase = await createClient()\n\n    // Call the database function to handle conversion\n    const { data, error } = await supabase.rpc('handle_referral_conversion', {\n      referred_doctor_uuid: doctorId\n    })\n\n    if (error) {\n      console.error('Error marking referral conversion:', error)\n      return { success: false, error: 'Failed to process referral conversion' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/admin/dashboard')\n\n    return { success: true, data: data || false }\n  } catch (error) {\n    console.error('Error marking referral conversion:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function getAdminReferralStats(): Promise<ApiResponse<{\n  total_referrals: number;\n  successful_conversions: number;\n  pending_referrals: number;\n  total_discount_earned: number;\n  top_referrers: Array<{\n    id: string;\n    name: string;\n    referral_code: string;\n    successful_referrals: number;\n    discount_earned: number;\n  }>;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    // Get overall stats\n    const { data: totalStats, error: statsError } = await supabase\n      .from('referral_analytics')\n      .select('status, discount_earned')\n\n    if (statsError) {\n      return { success: false, error: 'Failed to fetch referral statistics' }\n    }\n\n    const totalReferrals = totalStats?.length || 0\n    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0\n    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0\n    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0\n\n    // Get top referrers\n    const { data: topReferrers, error: referrersError } = await supabase\n      .from('doctors')\n      .select('id, name, referral_code, successful_referrals, referral_discount_earned')\n      .gt('successful_referrals', 0)\n      .order('successful_referrals', { ascending: false })\n      .limit(10)\n\n    if (referrersError) {\n      return { success: false, error: 'Failed to fetch top referrers' }\n    }\n\n    return {\n      success: true,\n      data: {\n        total_referrals: totalReferrals,\n        successful_conversions: successfulConversions,\n        pending_referrals: pendingReferrals,\n        total_discount_earned: totalDiscountEarned,\n        top_referrers: (topReferrers || []).map(r => ({\n          id: r.id,\n          name: r.name,\n          referral_code: r.referral_code || '',\n          successful_referrals: r.successful_referrals || 0,\n          discount_earned: r.referral_discount_earned || 0\n        }))\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching admin referral stats:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function validateReferralCode(referralCode: string): Promise<ApiResponse<{\n  valid: boolean;\n  referrer_name?: string;\n}>> {\n  try {\n    const supabase = await createClient()\n\n    const { data: referrer, error } = await supabase\n      .from('doctors')\n      .select('name, approved')\n      .eq('referral_code', referralCode)\n      .eq('approved', true)\n      .single()\n\n    if (error || !referrer) {\n      return { \n        success: true, \n        data: { valid: false } \n      }\n    }\n\n    return {\n      success: true,\n      data: {\n        valid: true,\n        referrer_name: referrer.name\n      }\n    }\n  } catch (error) {\n    console.error('Error validating referral code:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}"], "names": [], "mappings": ";;;;;;;;;;AAEA;AAEA;;;;;;AAEO,eAAe,gBAAgB,QAAgB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oCAAoC;QACpC,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,8BAA8B;QAC9B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,sBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,eAAe,UAClB,EAAE,CAAC,UAAU;QAEhB,uBAAuB;QACvB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,eAAe,UAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,gBAAgB;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,yCAAyC;QACzC,IAAI,aAAa;QACjB,IAAI,OAAO,WAAW,EAAE;YACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,WACL,MAAM,CAAC,uBACP,EAAE,CAAC,MAAM,OAAO,WAAW,EAC3B,MAAM;YAET,IAAI,UAAU;gBACZ,aAAa;oBACX,MAAM,SAAS,IAAI;oBACnB,eAAe,SAAS,aAAa,IAAI;gBAC3C;YACF;QACF;QAEA,MAAM,eAA6B;YACjC,eAAe,OAAO,aAAa,IAAI;YACvC,iBAAiB,OAAO,eAAe,IAAI;YAC3C,sBAAsB,OAAO,oBAAoB,IAAI;YACrD,mBAAmB,gBAAgB;YACnC,iBAAiB,OAAO,wBAAwB,IAAI;YACpD,aAAa;YACb,kBAAkB,CAAC,mBAAmB,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;oBACpD,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,eAAe,EAAE,QAAQ;oBACnC,OAAO,IAAI,eAAe,EAAE,SAAS;oBACrC,aAAa,IAAI,WAAW;oBAC5B,iBAAiB,IAAI,eAAe;oBACpC,QAAQ,IAAI,MAAM;gBACpB,CAAC;QACH;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,qBAAqB,QAAgB;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,SAAS,CAAC,QAAQ,eAAe;YACnC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QACnD,MAAM,eAAe,GAAG,QAAQ,YAAY,EAAE,OAAO,aAAa,EAAE;QAEpE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,sBAAsB,YAAoB,EAAE,WAAmB;IACnF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oBAAoB;QACpB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,iBAAiB,cACpB,MAAM;QAET,IAAI,iBAAiB,CAAC,UAAU;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAwB;QAC1D;QAEA,kDAAkD;QAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,WACL,MAAM,CAAC;YAAE,aAAa,SAAS,EAAE;QAAC,GAClC,EAAE,CAAC,MAAM;QAEZ,IAAI,aAAa;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE;QAEA,mCAAmC;QACnC,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,sBACL,MAAM,CAAC;YACN,aAAa,SAAS,EAAE;YACxB,oBAAoB;YACpB,eAAe;YACf,QAAQ;QACV;QAEF,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,iCAAiC;QACnC;QAEA,0CAA0C;QAC1C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,WACL,MAAM,CAAC,mBACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,iBAAiB;YACnB,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC;gBAAE,iBAAiB,CAAC,gBAAgB,eAAe,IAAI,CAAC,IAAI;YAAE,GACrE,EAAE,CAAC,MAAM,SAAS,EAAE;YAEvB,IAAI,YAAY;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAK;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,uBAAuB,QAAgB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,kDAAkD;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,8BAA8B;YACvE,sBAAsB;QACxB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAwC;QAC1E;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,MAAM,QAAQ;QAAM;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe;IAapB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,oBAAoB;QACpB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,sBACL,MAAM,CAAC;QAEV,IAAI,YAAY;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,YAAY,UAAU;QAC7C,MAAM,wBAAwB,YAAY,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;QAC1F,MAAM,mBAAmB,YAAY,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;QACnF,MAAM,sBAAsB,YAAY,OAAO,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,eAAe,IAAI,CAAC,GAAG,MAAM;QAEjG,oBAAoB;QACpB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,WACL,MAAM,CAAC,2EACP,EAAE,CAAC,wBAAwB,GAC3B,KAAK,CAAC,wBAAwB;YAAE,WAAW;QAAM,GACjD,KAAK,CAAC;QAET,IAAI,gBAAgB;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,iBAAiB;gBACjB,wBAAwB;gBACxB,mBAAmB;gBACnB,uBAAuB;gBACvB,eAAe,CAAC,gBAAgB,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC5C,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,eAAe,EAAE,aAAa,IAAI;wBAClC,sBAAsB,EAAE,oBAAoB,IAAI;wBAChD,iBAAiB,EAAE,wBAAwB,IAAI;oBACjD,CAAC;YACH;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEO,eAAe,qBAAqB,YAAoB;IAI7D,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,WACL,MAAM,CAAC,kBACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,YAAY,MACf,MAAM;QAET,IAAI,SAAS,CAAC,UAAU;YACtB,OAAO;gBACL,SAAS;gBACT,MAAM;oBAAE,OAAO;gBAAM;YACvB;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,eAAe,SAAS,IAAI;YAC9B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;;;IAxSsB;IAuFA;IAwBA;IAiEA;IAwBA;IAgEA;;AAxQA,+OAAA;AAuFA,+OAAA;AAwBA,+OAAA;AAiEA,+OAAA;AAwBA,+OAAA;AAgEA,+OAAA", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/auth/session.ts"], "sourcesContent": ["import 'server-only'\nimport { SignJWT, jwtVerify } from 'jose'\nimport { cookies } from 'next/headers'\nimport { SessionPayload } from '@/lib/types' // UNCOMMENT THIS LINE\n// TODO: Remove this line (type SessionPayload = any)\n// type SessionPayload = any // REMOVE THIS LINE\n\nconst secretKey = process.env.SESSION_SECRET\nconst encodedKey = new TextEncoder().encode(secretKey)\n\nexport async function encrypt(payload: SessionPayload) {\n  // Fix: Cast payload to Record<string, unknown> for SignJWT\n  return new SignJWT(payload as unknown as Record<string, unknown>) // Keep this cast\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('7d')\n    .sign(encodedKey)\n}\n\nexport async function decrypt(session: string | undefined = '') {\n  try {\n    if (!session) {\n      return null\n    }\n\n    const { payload } = await jwtVerify(session, encodedKey, {\n      algorithms: ['HS256'],\n    })\n    // Fix: cast to unknown first, then to SessionPayload for type safety\n    return payload as unknown as SessionPayload // Keep this cast\n  } catch {\n    console.log('Failed to verify session')\n    return null\n  }\n}\n\nexport async function createSession(userId: string) {\n  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n  const session = await encrypt({ userId, expiresAt })\n  const cookieStore = await cookies()\n\n  console.log('DEBUG: Creating session for user:', userId)\n  console.log('DEBUG: Session expires at:', expiresAt)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expiresAt,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session cookie set successfully')\n}\n\nexport async function updateSession() {\n  const cookieStore = await cookies()\n  const session = cookieStore.get('session')?.value\n  const payload = await decrypt(session)\n\n  console.log('DEBUG: Updating session - session exists:', !!session)\n  console.log('DEBUG: Updating session - payload valid:', !!payload)\n\n  if (!session || !payload) {\n    console.log('DEBUG: Cannot update session - missing session or payload')\n    return null\n  }\n\n  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expires,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session updated successfully')\n}\n\nexport async function refreshSession(userId: string) {\n  // Delete old session and create new one\n  console.log('DEBUG: Refreshing session for user:', userId)\n  await deleteSession()\n  await createSession(userId)\n  console.log('DEBUG: Session refresh completed')\n}\n\nexport async function deleteSession() {\n  const cookieStore = await cookies()\n  console.log('DEBUG: Deleting session cookie')\n  cookieStore.delete('session')\n  console.log('DEBUG: Session cookie deleted')\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAEA,qDAAqD;AACrD,gDAAgD;AAEhD,MAAM,YAAY,QAAQ,GAAG,CAAC,cAAc;AAC5C,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;AAErC,eAAe,QAAQ,OAAuB;IACnD,2DAA2D;IAC3D,OAAO,IAAI,qJAAA,CAAA,UAAO,CAAC,SAA+C,iBAAiB;KAChF,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,MAClB,IAAI,CAAC;AACV;AAEO,eAAe,QAAQ,UAA8B,EAAE;IAC5D,IAAI;QACF,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;YACvD,YAAY;gBAAC;aAAQ;QACvB;QACA,qEAAqE;QACrE,OAAO,SAAqC,iBAAiB;IAC/D,EAAE,OAAM;QACN,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;AACF;AAEO,eAAe,cAAc,MAAc;IAChD,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAC3D,MAAM,UAAU,MAAM,QAAQ;QAAE;QAAQ;IAAU;IAClD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,QAAQ,GAAG,CAAC,qCAAqC;IACjD,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,UAAU,YAAY,GAAG,CAAC,YAAY;IAC5C,MAAM,UAAU,MAAM,QAAQ;IAE9B,QAAQ,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,IAAI,CAAC,WAAW,CAAC,SAAS;QACxB,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;IAEzD,YAAY,GAAG,CAAC,WAAW,SAAS;QAClC,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe,eAAe,MAAc;IACjD,wCAAwC;IACxC,QAAQ,GAAG,CAAC,uCAAuC;IACnD,MAAM;IACN,MAAM,cAAc;IACpB,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,QAAQ,GAAG,CAAC;IACZ,YAAY,MAAM,CAAC;IACnB,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\nexport const SignupFormSchema = z.object({\n  name: z\n    .string()\n    .min(2, { message: 'Name must be at least 2 characters long.' })\n    .trim(),\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z\n    .string()\n    .min(8, { message: 'Password must be at least 8 characters long' })\n    .regex(/[a-zA-Z]/, { message: 'Password must contain at least one letter.' })\n    .regex(/[0-9]/, { message: 'Password must contain at least one number.' })\n    .regex(/[^a-zA-Z0-9]/, {\n      message: 'Password must contain at least one special character.',\n    })\n    .trim(),\n  clinic_name: z\n    .string()\n    .min(2, { message: 'Hospital name must be at least 2 characters long.' })\n    .optional(),\n  phone: z\n    .string()\n    .regex(/^\\d{10}$/, { message: 'Please enter exactly 10 digits (excluding +91).' })\n    .optional(),\n})\n\nexport const LoginFormSchema = z.object({\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z.string().min(1, { message: 'Password is required.' }).trim(),\n})\n\nexport const AdminLoginFormSchema = z.object({\n  email: z.string().email({ message: 'Please enter a valid email.' }).trim(),\n  password: z.string().min(1, { message: 'Password is required.' }).trim(),\n})\n\nexport const ConsultationCreateSchema = z.object({\n  primary_audio_url: z.string().url({ message: 'Valid audio URL is required.' }),\n  additional_audio_urls: z.array(z.string().url()).optional().default([]),\n  image_urls: z.array(z.string().url()).optional().default([]),\n  submitted_by: z.enum(['doctor', 'receptionist']),\n  total_file_size_bytes: z.number().min(0).optional(),\n})\n\nexport const ConsultationUpdateSchema = z.object({\n  edited_note: z.string().min(1, { message: 'Note content is required.' }),\n})\n\n\n\nexport const ProfileUpdateSchema = z.object({\n  name: z\n    .string()\n    .min(2, { message: 'Name must be at least 2 characters long.' })\n    .trim(),\n  clinic_name: z\n    .string()\n    .min(2, { message: 'Hospital name must be at least 2 characters long.' })\n    .optional(),\n  phone: z\n    .string()\n    .regex(/^\\d{10}$/, { message: 'Please enter exactly 10 digits (excluding +91).' })\n    .optional(),\n\n})\n\nexport type SignupFormData = z.infer<typeof SignupFormSchema>\nexport type LoginFormData = z.infer<typeof LoginFormSchema>\nexport type ConsultationCreateData = z.infer<typeof ConsultationCreateSchema>\nexport type ConsultationUpdateData = z.infer<typeof ConsultationUpdateSchema>\n\nexport type ProfileUpdateData = z.infer<typeof ProfileUpdateSchema>\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;;AAEO,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,IAAI;IACP,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA8C,GAChE,KAAK,CAAC,YAAY;QAAE,SAAS;IAA6C,GAC1E,KAAK,CAAC,SAAS;QAAE,SAAS;IAA6C,GACvE,KAAK,CAAC,gBAAgB;QACrB,SAAS;IACX,GACC,IAAI;IACP,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,QAAQ;IACX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,YAAY;QAAE,SAAS;IAAkD,GAC/E,QAAQ;AACb;AAEO,MAAM,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB,GAAG,IAAI;AACxE;AAEO,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAA8B,GAAG,IAAI;IACxE,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB,GAAG,IAAI;AACxE;AAEO,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;QAAE,SAAS;IAA+B;IAC5E,uBAAuB,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,OAAO,CAAC,EAAE;IACtE,YAAY,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,OAAO,CAAC,EAAE;IAC3D,cAAc,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAe;IAC/C,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;AACnD;AAEO,MAAM,2BAA2B,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;AACxE;AAIO,MAAM,sBAAsB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,IAAI;IACP,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoD,GACtE,QAAQ;IACX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,YAAY;QAAE,SAAS;IAAkD,GAC/E,QAAQ;AAEb", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/auth.ts"], "sourcesContent": ["'use server'\n\nimport { redirect } from 'next/navigation'\nimport bcrypt from 'bcryptjs'\nimport { createClient } from '@/lib/supabase/server'\nimport { createSession, deleteSession, refreshSession } from '@/lib/auth/session'\nimport { SignupFormSchema, LoginFormSchema } from '@/lib/validations'\nimport { FormState } from '@/lib/types'\nimport { processReferralSignup } from '@/lib/actions/referrals'\n\nexport async function signup(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = SignupFormSchema.safeParse({\n    name: formData.get('name'),\n    email: formData.get('email'),\n    password: formData.get('password'),\n    clinic_name: formData.get('clinic_name'),\n    phone: formData.get('phone'),\n  })\n\n  // Get referral code from form\n  const referralCode = formData.get('referral_code') as string\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.', // A generic message for validation failure\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  // Prepare data for insertion into database\n  const { name, email, password, clinic_name, phone } = validatedFields.data\n  const hashedPassword = await bcrypt.hash(password, 10)\n\n  try {\n    const supabase = await createClient()\n\n    // Check if user already exists (email or phone)\n    const { data: existingUser } = await supabase\n      .from('doctors')\n      .select('id')\n      .eq('email', email)\n      .single()\n\n    if (existingUser) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An account with this email already exists.',\n      }\n    }\n\n    // Check if phone number already exists (if phone is provided)\n    if (phone) {\n      const { data: existingPhone } = await supabase\n        .from('doctors')\n        .select('id')\n        .eq('phone', phone)\n        .single()\n\n      if (existingPhone) {\n        return {\n          success: false,\n          message: 'An account with this phone number already exists.',\n        }\n      }\n    }\n\n    // Insert the user into the database with approval required and new default quota\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .insert({\n        name,\n        email,\n        password_hash: hashedPassword,\n        clinic_name,\n        phone,\n        approved: false, // New doctors require approval\n        monthly_quota: 50, // Set default quota to 50\n      })\n      .select('id, approved')\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    if (!user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    // Process referral if referral code was provided\n    if (referralCode) {\n      await processReferralSignup(referralCode, user.id)\n    }\n\n    // Don't create session for unapproved users\n    if (!user.approved) {\n      return {\n        success: true, // This is a success state, but with an informative message\n        message: 'Account created successfully! Please wait for admin approval before you can log in.',\n      }\n    }\n\n    // Create user session (only for approved users)\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Signup error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means signup was successful and session created (if approved)\n  // or a success message was returned for pending approval.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function login(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = LoginFormSchema.safeParse({\n    email: formData.get('email'),\n    password: formData.get('password'),\n  })\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.',\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  const { email, password } = validatedFields.data\n\n  try {\n    const supabase = await createClient()\n\n    // Get user from database\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, password_hash, approved')\n      .eq('email', email)\n      .single()\n\n    if (error || !user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Check if user is approved\n    if (!user.approved) {\n      return {\n        success: false, // This is an error state, preventing login\n        message: 'Your account is pending admin approval. Please wait for approval before logging in.',\n      }\n    }\n\n    // Create user session\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Login error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means login was successful and session created.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function logout() {\n  await deleteSession()\n  redirect('/login')\n}\n\nexport async function changePassword(doctorId: string, formData: FormData): Promise<FormState> {\n  const currentPassword = formData.get('currentPassword') as string\n  const newPassword = formData.get('newPassword') as string\n  const confirmPassword = formData.get('confirmPassword') as string\n\n  // Basic validation\n  if (!currentPassword || !newPassword || !confirmPassword) {\n    return {\n      success: false,\n      message: 'All fields are required.',\n    }\n  }\n\n  if (newPassword !== confirmPassword) {\n    return {\n      success: false,\n      message: 'New passwords do not match.',\n    }\n  }\n\n  if (newPassword.length < 8) {\n    return {\n      success: false,\n      message: 'New password must be at least 8 characters long.',\n    }\n  }\n\n  try {\n    const supabase = await createClient()\n\n    // Get current password hash\n    const { data: doctor, error: fetchError } = await supabase\n      .from('doctors')\n      .select('password_hash')\n      .eq('id', doctorId)\n      .single()\n\n    if (fetchError || !doctor) {\n      return {\n        success: false,\n        message: 'Doctor not found.',\n      }\n    }\n\n    // Verify current password\n    const isValidPassword = await bcrypt.compare(currentPassword, doctor.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false,\n        message: 'Current password is incorrect.',\n      }\n    }\n\n    // Hash new password\n    const hashedNewPassword = await bcrypt.hash(newPassword, 10)\n\n    // Update password\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ password_hash: hashedNewPassword })\n      .eq('id', doctorId)\n\n    if (updateError) {\n      console.error('Password update error:', updateError)\n      return {\n        success: false,\n        message: 'Failed to update password.',\n      }\n    }\n\n    // Refresh session with updated credentials\n    await refreshSession(doctorId)\n\n    return {\n      success: true,\n      message: 'Password changed successfully!',\n    }\n  } catch (error) {\n    console.error('Password change error:', error)\n    return {\n      success: false,\n      message: 'An unexpected error occurred.',\n    }\n  }\n}"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AAEO,eAAe,OAAO,KAAgB,EAAE,QAAkB;IAC/D,uBAAuB;IACvB,MAAM,kBAAkB,yHAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC;QACjD,MAAM,SAAS,GAAG,CAAC;QACnB,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;QACvB,aAAa,SAAS,GAAG,CAAC;QAC1B,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,8BAA8B;IAC9B,MAAM,eAAe,SAAS,GAAG,CAAC;IAElC,+CAA+C;IAC/C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,sDAAsD;YACtD,SAAS;YACT,SAAS;YACT,gFAAgF;YAChF,aAAa,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;QAC1D;IACF;IAEA,2CAA2C;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,gBAAgB,IAAI;IAC1E,MAAM,iBAAiB,MAAM,iIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;IAEnD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,gDAAgD;QAChD,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,SAAS,OACZ,MAAM;QAET,IAAI,cAAc;YAChB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,8DAA8D;QAC9D,IAAI,OAAO;YACT,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,SAAS,OACZ,MAAM;YAET,IAAI,eAAe;gBACjB,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF;QAEA,iFAAiF;QACjF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC;YACN;YACA;YACA,eAAe;YACf;YACA;YACA,UAAU;YACV,eAAe;QACjB,GACC,MAAM,CAAC,gBACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,iDAAiD;QACjD,IAAI,cAAc;YAChB,MAAM,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,KAAK,EAAE;QACnD;QAEA,4CAA4C;QAC5C,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,gDAAgD;QAChD,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,8FAA8F;IAC9F,0DAA0D;IAC1D,4FAA4F;IAC5F,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACT,4FAA4F;IAC5F,OAAO;QAAE,SAAS;QAAM,SAAS;IAAiB;AACpD;AAEO,eAAe,MAAM,KAAgB,EAAE,QAAkB;IAC9D,uBAAuB;IACvB,MAAM,kBAAkB,yHAAA,CAAA,kBAAe,CAAC,SAAS,CAAC;QAChD,OAAO,SAAS,GAAG,CAAC;QACpB,UAAU,SAAS,GAAG,CAAC;IACzB;IAEA,+CAA+C;IAC/C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,sDAAsD;YACtD,SAAS;YACT,SAAS;YACT,gFAAgF;YAChF,aAAa,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;QAC1D;IACF;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IAEhD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,yBAAyB;QACzB,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,WACL,MAAM,CAAC,+BACP,EAAE,CAAC,SAAS,OACZ,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,iIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,aAAa;QAEzE,IAAI,CAAC,iBAAiB;YACpB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,4BAA4B;QAC5B,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,gFAAgF;IAChF,4FAA4F;IAC5F,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACT,4FAA4F;IAC5F,OAAO;QAAE,SAAS;QAAM,SAAS;IAAiB;AACpD;AAEO,eAAe;IACpB,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAClB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;AAEO,eAAe,eAAe,QAAgB,EAAE,QAAkB;IACvE,MAAM,kBAAkB,SAAS,GAAG,CAAC;IACrC,MAAM,cAAc,SAAS,GAAG,CAAC;IACjC,MAAM,kBAAkB,SAAS,GAAG,CAAC;IAErC,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,iBAAiB;QACxD,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,gBAAgB,iBAAiB;QACnC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;QAElC,4BAA4B;QAC5B,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAC/C,IAAI,CAAC,WACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,cAAc,CAAC,QAAQ;YACzB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,iIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,iBAAiB,OAAO,aAAa;QAElF,IAAI,CAAC,iBAAiB;YACpB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,oBAAoB;QACpB,MAAM,oBAAoB,MAAM,iIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,aAAa;QAEzD,kBAAkB;QAClB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,WACL,MAAM,CAAC;YAAE,eAAe;QAAkB,GAC1C,EAAE,CAAC,MAAM;QAEZ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF;QAEA,2CAA2C;QAC3C,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAErB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;;;IA5RsB;IA0HA;IAwEA;IAKA;;AAvMA,+OAAA;AA0HA,+OAAA;AAwEA,+OAAA;AAKA,+OAAA", "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/.next-internal/server/app/signup/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getAdminReferralStats as '00ed563c8fb8c346efaec610c0d2a954999d90622b'} from 'ACTIONS_MODULE0'\nexport {generateReferralLink as '400d27483b0ad440768404c34690724d71663b6083'} from 'ACTIONS_MODULE0'\nexport {validateReferralCode as '4072b74acb2fae150f3d07efd9bac13f2f423a1a31'} from 'ACTIONS_MODULE0'\nexport {markReferralConversion as '407564c58b76eea97b9678f27ef8f98ef2a04ae6eb'} from 'ACTIONS_MODULE0'\nexport {getReferralInfo as '40757964b8c9b072995f44631743e71306c1784480'} from 'ACTIONS_MODULE0'\nexport {processReferralSignup as '606bc295d7adf166de394559341675608634558f4e'} from 'ACTIONS_MODULE0'\nexport {signup as '607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AAMA", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/auth/signup-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SignupForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignupForm() from the server but SignupForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/signup-form.tsx <module evaluation>\",\n    \"SignupForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qEACA", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/auth/signup-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SignupForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignupForm() from the server but SignupForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/signup-form.tsx\",\n    \"SignupForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,iDACA", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/app/signup/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { SignupForm } from '@/components/auth/signup-form'\nimport { <PERSON><PERSON><PERSON>, Wand2, ArrowRight, Users, Shield } from 'lucide-react'\nimport { validateReferralCode } from '@/lib/actions/referrals'\n\nexport const metadata: Metadata = {\n  title: 'Sign Up - Celer AI',\n  description: 'Create your Celer AI account',\n}\n\nexport default async function SignupPage({ \n  searchParams \n}: { \n  searchParams: Promise<{ ref?: string }> \n}) {\n  const resolvedSearchParams = await searchParams\n  const referralCode = resolvedSearchParams.ref\n  let referrerInfo: { valid: boolean; referrer_name?: string } = { valid: false }\n  \n  if (referralCode) {\n    const result = await validateReferralCode(referralCode)\n    if (result.success) {\n      referrerInfo = result.data\n    }\n  }\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden\">\n      {/* Floating Navigation */}\n      <nav className=\"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20\">\n        <div className=\"flex items-center space-x-8\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"relative w-8 h-8\">\n              <Image\n                src=\"/celer-ai-logo.svg\"\n                alt=\"Celer AI\"\n                width={32}\n                height={32}\n                className=\"rounded-lg\"\n              />\n            </div>\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold\">\n              Celer AI\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <Link\n              href=\"/login\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Sign In\n            </Link>\n            <Link\n              href=\"/\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Home\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse\"></div>\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-2000\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-6\">\n              <Sparkles className=\"w-4 h-4 text-indigo-600 animate-pulse\" />\n              <span className=\"text-indigo-700 text-sm font-medium\">Join the Magic</span>\n              <Wand2 className=\"w-4 h-4 text-purple-600\" />\n            </div>\n\n            <h2 className=\"text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4\">\n              {referrerInfo.valid ? (\n                <>\n                  Join\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse\">\n                    {referrerInfo.referrer_name}&apos;s\n                  </span>\n                  <span className=\"block\">Network</span>\n                </>\n              ) : (\n                <>\n                  Start Your\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse\">\n                     Journey\n                  </span>\n                </>\n              )}\n            </h2>\n\n            <p className=\"text-lg text-slate-600 mb-6\">\n              {referrerInfo.valid\n                ? `You're here because ${referrerInfo.referrer_name} trusts Celer AI. Happy Consulting!`\n                : 'Create magical medical reports in 30 seconds'\n              }\n            </p>\n\n            <div className=\"flex justify-center items-center space-x-6 text-slate-500 text-sm\">\n              <div className=\"flex items-center space-x-1\">\n                <Users className=\"w-4 h-4 text-indigo-600\" />\n                <span>Trusted by doctors</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Shield className=\"w-4 h-4 text-emerald-500\" />\n                <span>Secure & Private</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Signup Form Card */}\n          <div className=\"relative\">\n            <div className=\"absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse\"></div>\n            <div className=\"relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl p-8 border border-white/20\">\n              {referrerInfo.valid && (\n                <div className=\"mb-6 p-4 bg-gradient-to-r from-emerald-50 to-cyan-50 border border-emerald-200 rounded-xl\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-emerald-400 rounded-full animate-ping\"></div>\n                    <div className=\"w-2 h-2 bg-emerald-400 rounded-full\"></div>\n                    <p className=\"text-sm text-emerald-700 font-medium\">\n                      Referred by Dr. {referrerInfo.referrer_name}\n                    </p>\n                  </div>\n                </div>\n              )}\n              <SignupForm referralCode={referralCode} />\n            </div>\n          </div>\n\n          {/* Sign in link */}\n          <div className=\"text-center\">\n            <div className=\"bg-white/60 backdrop-blur-xl rounded-xl p-4 border border-white/30\">\n              <p className=\"text-sm text-slate-600\">\n                Already have an account?{' '}\n                <Link\n                  href=\"/login\"\n                  className=\"font-medium text-indigo-600 hover:text-purple-600 transition-colors duration-200 inline-flex items-center space-x-1\"\n                >\n                  <span>Sign in here</span>\n                  <ArrowRight className=\"w-3 h-3\" />\n                </Link>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe,WAAW,EACvC,YAAY,EAGb;IACC,MAAM,uBAAuB,MAAM;IACnC,MAAM,eAAe,qBAAqB,GAAG;IAC7C,IAAI,eAA2D;QAAE,OAAO;IAAM;IAE9E,IAAI,cAAc;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1C,IAAI,OAAO,OAAO,EAAE;YAClB,eAAe,OAAO,IAAI;QAC5B;IACF;IACA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAK,WAAU;8CAA8H;;;;;;;;;;;;sCAIhJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAAsC;;;;;;sDACtD,8OAAC,+MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,aAAa,KAAK,iBACjB;;4CAAE;0DAEA,8OAAC;gDAAK,WAAU;;oDACb,aAAa,aAAa;oDAAC;;;;;;;0DAE9B,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;;qEAG1B;;4CAAE;0DAEA,8OAAC;gDAAK,WAAU;0DAAgH;;;;;;;;;;;;;8CAOtI,8OAAC;oCAAE,WAAU;8CACV,aAAa,KAAK,GACf,CAAC,oBAAoB,EAAE,aAAa,aAAa,CAAC,mCAAmC,CAAC,GACtF;;;;;;8CAIN,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;wCACZ,aAAa,KAAK,kBACjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;4DAAuC;4DACjC,aAAa,aAAa;;;;;;;;;;;;;;;;;;sDAKnD,8OAAC,4IAAA,CAAA,aAAU;4CAAC,cAAc;;;;;;;;;;;;;;;;;;sCAK9B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAyB;wCACX;sDACzB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;8DAAK;;;;;;8DACN,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}