module.exports = {

"[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
"use turbopack no side effects";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/actions/data:b6153b [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"608aaaf92844312537830e8c8d0c49debb89bda21b":"saveStreamingSummary"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "saveStreamingSummary": (()=>saveStreamingSummary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var saveStreamingSummary = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("608aaaf92844312537830e8c8d0c49debb89bda21b", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "saveStreamingSummary"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/data:654ad4 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40d596bef0460f198d588bbf8dede026bc85cca740":"createConsultation"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "createConsultation": (()=>createConsultation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var createConsultation = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40d596bef0460f198d588bbf8dede026bc85cca740", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "createConsultation"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/data:b1e14a [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd":"generateSummaryStream"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "generateSummaryStream": (()=>generateSummaryStream)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateSummaryStream = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f7dd3bdf6242116733f7ef527b1e8f307d343dacd", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateSummaryStream"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/data:fb1f18 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"6016d1147dadc5ab75f7387f44ab799f7cd595708b":"updateConsultationImages"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "updateConsultationImages": (()=>updateConsultationImages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var updateConsultationImages = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("6016d1147dadc5ab75f7387f44ab799f7cd595708b", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updateConsultationImages"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/data:72301d [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7":"updateAdditionalNotes"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "updateAdditionalNotes": (()=>updateAdditionalNotes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var updateAdditionalNotes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updateAdditionalNotes"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/data:73b1a1 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"609aacf458083825c0e86748f13c86d5773450588b":"updatePatientName"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "updatePatientName": (()=>updatePatientName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var updatePatientName = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("609aacf458083825c0e86748f13c86d5773450588b", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updatePatientName"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vY29uc3VsdGF0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHNlcnZlcidcblxuaW1wb3J0IHsgcmV2YWxpZGF0ZVBhdGggfSBmcm9tICduZXh0L2NhY2hlJ1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJ1xuaW1wb3J0IHsgdmVyaWZ5U2Vzc2lvbiB9IGZyb20gJ0AvbGliL2F1dGgvZGFsJ1xuaW1wb3J0IHsgQ29uc3VsdGF0aW9uQ3JlYXRlU2NoZW1hLCBDb25zdWx0YXRpb25VcGRhdGVTY2hlbWEgfSBmcm9tICdAL2xpYi92YWxpZGF0aW9ucydcbmltcG9ydCB7IEFwaVJlc3BvbnNlLCBDb25zdWx0YXRpb24sIERhdGFiYXNlIH0gZnJvbSAnQC9saWIvdHlwZXMnXG5pbXBvcnQgeyB1cGxvYWRGaWxlLCB1cGxvYWRNdWx0aXBsZUZpbGVzLCBjYWxjdWxhdGVUb3RhbEZpbGVTaXplLCB2YWxpZGF0ZVRvdGFsU2l6ZSB9IGZyb20gJ0AvbGliL3N0b3JhZ2UnXG5cbi8vIEhlbHBlciB0byBwYXJzZSBhIEpzb24gfCBudWxsIGFycmF5IGZpZWxkIGZyb20gU3VwYWJhc2VcbmZ1bmN0aW9uIHBhcnNlSnNvblN0cmluZ0FycmF5KGZpZWxkOiB1bmtub3duKTogc3RyaW5nW10ge1xuICBpZiAoIWZpZWxkKSByZXR1cm4gW11cbiAgaWYgKEFycmF5LmlzQXJyYXkoZmllbGQpKSByZXR1cm4gZmllbGQgYXMgc3RyaW5nW11cbiAgaWYgKHR5cGVvZiBmaWVsZCA9PT0gJ3N0cmluZycpIHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2UoZmllbGQpXG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gW11cbiAgICB9XG4gIH1cbiAgcmV0dXJuIFtdXG59XG5cbi8vIE5ldyBmdW5jdGlvbiB0byBoYW5kbGUgZmlsZSB1cGxvYWRzIGFuZCBjb25zdWx0YXRpb24gY3JlYXRpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVDb25zdWx0YXRpb25XaXRoRmlsZXMoXG4gIGF1ZGlvRmlsZTogRmlsZSxcbiAgaW1hZ2VGaWxlczogRmlsZVtdLFxuICBhZGRpdGlvbmFsQXVkaW9GaWxlczogRmlsZVtdLFxuICBzdWJtaXR0ZWRCeTogJ2RvY3RvcicgfCAncmVjZXB0aW9uaXN0JyxcbiAgZG9jdG9yTm90ZXM/OiBzdHJpbmcsXG4gIGNvbnN1bHRhdGlvblR5cGU6ICdvdXRwYXRpZW50JyB8ICdkaXNjaGFyZ2UnIHwgJ3N1cmdlcnknIHwgJ3JhZGlvbG9neScgfCAnZGVybWF0b2xvZ3knIHwgJ2NhcmRpb2xvZ3lfZWNobycgfCAnaXZmX2N5Y2xlJyB8ICdwYXRob2xvZ3knID0gJ291dHBhdGllbnQnLFxuICBwYXRpZW50TmFtZT86IHN0cmluZ1xuKTogUHJvbWlzZTxBcGlSZXNwb25zZTxDb25zdWx0YXRpb24+PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IHZlcmlmeVNlc3Npb24oKVxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVW5hdXRob3JpemVkJyB9XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgdG90YWwgZmlsZSBzaXplXG4gICAgY29uc3QgYWxsRmlsZXMgPSBbYXVkaW9GaWxlLCAuLi5pbWFnZUZpbGVzLCAuLi5hZGRpdGlvbmFsQXVkaW9GaWxlc11cbiAgICBjb25zdCB0b3RhbFNpemVWYWxpZGF0aW9uID0gdmFsaWRhdGVUb3RhbFNpemUoYWxsRmlsZXMpXG4gICAgaWYgKCF0b3RhbFNpemVWYWxpZGF0aW9uLnZhbGlkKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IHRvdGFsU2l6ZVZhbGlkYXRpb24uZXJyb3IhIH1cbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBjb25zdWx0YXRpb24gSUQgZm9yIGZpbGUgb3JnYW5pemF0aW9uXG4gICAgY29uc3QgY29uc3VsdGF0aW9uSWQgPSBjcnlwdG8ucmFuZG9tVVVJRCgpXG5cbiAgICAvLyBVcGxvYWQgcHJpbWFyeSBhdWRpbyBmaWxlXG4gICAgY29uc3QgYXVkaW9VcGxvYWRSZXN1bHQgPSBhd2FpdCB1cGxvYWRGaWxlKGF1ZGlvRmlsZSwgc2Vzc2lvbi51c2VySWQsIGNvbnN1bHRhdGlvbklkLCAnYXVkaW8nKVxuICAgIGlmICghYXVkaW9VcGxvYWRSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBgQXVkaW8gdXBsb2FkIGZhaWxlZDogJHthdWRpb1VwbG9hZFJlc3VsdC5lcnJvcn1gIH1cbiAgICB9XG5cbiAgICAvLyBVcGxvYWQgYWRkaXRpb25hbCBhdWRpbyBmaWxlc1xuICAgIGxldCBhZGRpdGlvbmFsQXVkaW9VcmxzOiBzdHJpbmdbXSA9IFtdXG4gICAgaWYgKGFkZGl0aW9uYWxBdWRpb0ZpbGVzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IGFkZGl0aW9uYWxBdWRpb1Jlc3VsdCA9IGF3YWl0IHVwbG9hZE11bHRpcGxlRmlsZXMoYWRkaXRpb25hbEF1ZGlvRmlsZXMsIHNlc3Npb24udXNlcklkLCBjb25zdWx0YXRpb25JZCwgJ2F1ZGlvJylcbiAgICAgIGlmICghYWRkaXRpb25hbEF1ZGlvUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBgQWRkaXRpb25hbCBhdWRpbyB1cGxvYWQgZmFpbGVkOiAke2FkZGl0aW9uYWxBdWRpb1Jlc3VsdC5lcnJvcnM/LmpvaW4oJywgJyl9YCB9XG4gICAgICB9XG4gICAgICBhZGRpdGlvbmFsQXVkaW9VcmxzID0gYWRkaXRpb25hbEF1ZGlvUmVzdWx0LnVybHMgfHwgW11cbiAgICB9XG5cbiAgICAvLyBVcGxvYWQgaW1hZ2UgZmlsZXNcbiAgICBsZXQgaW1hZ2VVcmxzOiBzdHJpbmdbXSA9IFtdXG4gICAgaWYgKGltYWdlRmlsZXMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgaW1hZ2VVcGxvYWRSZXN1bHQgPSBhd2FpdCB1cGxvYWRNdWx0aXBsZUZpbGVzKGltYWdlRmlsZXMsIHNlc3Npb24udXNlcklkLCBjb25zdWx0YXRpb25JZCwgJ2ltYWdlJylcbiAgICAgIGlmICghaW1hZ2VVcGxvYWRSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGBJbWFnZSB1cGxvYWQgZmFpbGVkOiAke2ltYWdlVXBsb2FkUmVzdWx0LmVycm9ycz8uam9pbignLCAnKX1gIH1cbiAgICAgIH1cbiAgICAgIGltYWdlVXJscyA9IGltYWdlVXBsb2FkUmVzdWx0LnVybHMgfHwgW11cbiAgICB9XG5cbiAgICBjb25zdCB0b3RhbEZpbGVTaXplID0gY2FsY3VsYXRlVG90YWxGaWxlU2l6ZShhbGxGaWxlcylcblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIEluc2VydCBjb25zdWx0YXRpb24gd2l0aCBwcmUtZ2VuZXJhdGVkIElEXG4gICAgY29uc3QgeyBkYXRhOiBjb25zdWx0YXRpb24sIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIGlkOiBjb25zdWx0YXRpb25JZCxcbiAgICAgICAgZG9jdG9yX2lkOiBzZXNzaW9uLnVzZXJJZCxcbiAgICAgICAgcHJpbWFyeV9hdWRpb191cmw6IGF1ZGlvVXBsb2FkUmVzdWx0LnVybCEsXG4gICAgICAgIGFkZGl0aW9uYWxfYXVkaW9fdXJsczogYWRkaXRpb25hbEF1ZGlvVXJscyxcbiAgICAgICAgaW1hZ2VfdXJsczogaW1hZ2VVcmxzLFxuICAgICAgICBzdWJtaXR0ZWRfYnk6IHN1Ym1pdHRlZEJ5LFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgdG90YWxfZmlsZV9zaXplX2J5dGVzOiB0b3RhbEZpbGVTaXplLFxuICAgICAgICBkb2N0b3Jfbm90ZXM6IGRvY3Rvck5vdGVzIHx8IG51bGwsXG4gICAgICAgIGNvbnN1bHRhdGlvbl90eXBlOiBjb25zdWx0YXRpb25UeXBlLFxuICAgICAgICBwYXRpZW50X25hbWU6IHBhdGllbnROYW1lIHx8IG51bGwsXG4gICAgICB9IGFzIERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NvbnN1bHRhdGlvbnMnXVsnSW5zZXJ0J10pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgY29uc3VsdGF0aW9uJyB9XG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuICAgIHJldmFsaWRhdGVQYXRoKCcvbW9iaWxlJylcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IGNvbnN1bHRhdGlvbiBhcyBDb25zdWx0YXRpb24gfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0NyZWF0ZSBjb25zdWx0YXRpb24gd2l0aCBmaWxlcyBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyB9XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNhdmVTdHJlYW1pbmdTdW1tYXJ5KGNvbnN1bHRhdGlvbklkOiBzdHJpbmcsIHN1bW1hcnk6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8c3RyaW5nPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb24gd2l0aCBnZW5lcmF0ZWQgc3VtbWFyeVxuICAgIGNvbnN0IHsgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnVwZGF0ZSh7XG4gICAgICAgIGFpX2dlbmVyYXRlZF9ub3RlOiBzdW1tYXJ5LFxuICAgICAgICBzdGF0dXM6ICdnZW5lcmF0ZWQnLFxuICAgICAgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgICAgLmVxKCdkb2N0b3JfaWQnLCBzZXNzaW9uLnVzZXJJZCkgLy8gRW5zdXJlIHVzZXIgY2FuIG9ubHkgdXBkYXRlIHRoZWlyIG93biBjb25zdWx0YXRpb25zXG5cbiAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwZGF0ZSBlcnJvcjonLCB1cGRhdGVFcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBzYXZlIGdlbmVyYXRlZCBzdW1tYXJ5JyB9XG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogc3VtbWFyeSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignU2F2ZSBzdHJlYW1pbmcgc3VtbWFyeSBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyB9XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNvbnN1bHRhdGlvbihmb3JtRGF0YTogRm9ybURhdGEpOiBQcm9taXNlPEFwaVJlc3BvbnNlPENvbnN1bHRhdGlvbj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgdmVyaWZ5U2Vzc2lvbigpXG4gICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH1cbiAgICB9XG5cbiAgICAvLyBFeHRyYWN0IFVSTHMgZnJvbSBmb3JtIGRhdGFcbiAgICBjb25zdCBwcmltYXJ5QXVkaW9VcmwgPSBmb3JtRGF0YS5nZXQoJ3ByaW1hcnlfYXVkaW9fdXJsJykgYXMgc3RyaW5nXG4gICAgY29uc3QgYWRkaXRpb25hbEF1ZGlvVXJsc1N0cmluZyA9IGZvcm1EYXRhLmdldCgnYWRkaXRpb25hbF9hdWRpb191cmxzJykgYXMgc3RyaW5nXG4gICAgY29uc3QgaW1hZ2VVcmxzU3RyaW5nID0gZm9ybURhdGEuZ2V0KCdpbWFnZV91cmxzJykgYXMgc3RyaW5nXG4gICAgY29uc3QgdG90YWxGaWxlU2l6ZVN0cmluZyA9IGZvcm1EYXRhLmdldCgndG90YWxfZmlsZV9zaXplX2J5dGVzJykgYXMgc3RyaW5nXG5cbiAgICBsZXQgYWRkaXRpb25hbEF1ZGlvVXJsczogc3RyaW5nW10gPSBbXVxuICAgIGxldCBpbWFnZVVybHM6IHN0cmluZ1tdID0gW11cbiAgICBsZXQgdG90YWxGaWxlU2l6ZSA9IDBcblxuICAgIC8vIFBhcnNlIGFkZGl0aW9uYWwgYXVkaW8gVVJMc1xuICAgIGlmIChhZGRpdGlvbmFsQXVkaW9VcmxzU3RyaW5nKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhZGRpdGlvbmFsQXVkaW9VcmxzID0gSlNPTi5wYXJzZShhZGRpdGlvbmFsQXVkaW9VcmxzU3RyaW5nKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBhZGRpdGlvbmFsX2F1ZGlvX3VybHM6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUGFyc2UgaW1hZ2UgVVJMc1xuICAgIGlmIChpbWFnZVVybHNTdHJpbmcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGltYWdlVXJscyA9IEpTT04ucGFyc2UoaW1hZ2VVcmxzU3RyaW5nKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBpbWFnZV91cmxzOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFBhcnNlIHRvdGFsIGZpbGUgc2l6ZVxuICAgIGlmICh0b3RhbEZpbGVTaXplU3RyaW5nKSB7XG4gICAgICB0b3RhbEZpbGVTaXplID0gcGFyc2VJbnQodG90YWxGaWxlU2l6ZVN0cmluZywgMTApIHx8IDBcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBmb3JtIGRhdGFcbiAgICBjb25zdCB2YWxpZGF0ZWRGaWVsZHMgPSBDb25zdWx0YXRpb25DcmVhdGVTY2hlbWEuc2FmZVBhcnNlKHtcbiAgICAgIHByaW1hcnlfYXVkaW9fdXJsOiBwcmltYXJ5QXVkaW9VcmwsXG4gICAgICBhZGRpdGlvbmFsX2F1ZGlvX3VybHM6IGFkZGl0aW9uYWxBdWRpb1VybHMsXG4gICAgICBpbWFnZV91cmxzOiBpbWFnZVVybHMsXG4gICAgICBzdWJtaXR0ZWRfYnk6IGZvcm1EYXRhLmdldCgnc3VibWl0dGVkX2J5JyksXG4gICAgICB0b3RhbF9maWxlX3NpemVfYnl0ZXM6IHRvdGFsRmlsZVNpemUsXG4gICAgfSlcblxuICAgIGlmICghdmFsaWRhdGVkRmllbGRzLnN1Y2Nlc3MpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcjogJ0ludmFsaWQgZm9ybSBkYXRhOiAnICsgSlNPTi5zdHJpbmdpZnkodmFsaWRhdGVkRmllbGRzLmVycm9yLmZsYXR0ZW4oKS5maWVsZEVycm9ycylcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCB7IHByaW1hcnlfYXVkaW9fdXJsLCBhZGRpdGlvbmFsX2F1ZGlvX3VybHMsIGltYWdlX3VybHMsIHN1Ym1pdHRlZF9ieSwgdG90YWxfZmlsZV9zaXplX2J5dGVzIH0gPSB2YWxpZGF0ZWRGaWVsZHMuZGF0YVxuXG4gICAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKVxuXG4gICAgLy8gSW5zZXJ0IGNvbnN1bHRhdGlvblxuICAgIGNvbnN0IHsgZGF0YTogY29uc3VsdGF0aW9uLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICBkb2N0b3JfaWQ6IHNlc3Npb24udXNlcklkLFxuICAgICAgICBwcmltYXJ5X2F1ZGlvX3VybCxcbiAgICAgICAgYWRkaXRpb25hbF9hdWRpb191cmxzOiBhZGRpdGlvbmFsX2F1ZGlvX3VybHMgfHwgW10sXG4gICAgICAgIGltYWdlX3VybHM6IGltYWdlX3VybHMgfHwgW10sXG4gICAgICAgIHN1Ym1pdHRlZF9ieSxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHRvdGFsX2ZpbGVfc2l6ZV9ieXRlczogdG90YWxfZmlsZV9zaXplX2J5dGVzIHx8IDAsXG4gICAgICB9IGFzIERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NvbnN1bHRhdGlvbnMnXVsnSW5zZXJ0J10pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgY29uc3VsdGF0aW9uJyB9XG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuICAgIHJldmFsaWRhdGVQYXRoKCcvbW9iaWxlJylcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IGNvbnN1bHRhdGlvbiBhcyBDb25zdWx0YXRpb24gfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0NyZWF0ZSBjb25zdWx0YXRpb24gZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG5cbi8vIE9QVElNSVpFRDogUGFnaW5hdGVkIGNvbnN1bHRhdGlvbnMgd2l0aCBGVFMgc2VhcmNoIHN1cHBvcnRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDb25zdWx0YXRpb25zKHtcbiAgcGFnZSA9IDEsXG4gIHBhZ2VTaXplID0gMTUsIC8vIFN0YXJ0IHdpdGggc21hbGwgaW5pdGlhbCBsb2FkXG4gIHN0YXR1cyxcbiAgc2VhcmNoVGVybVxufToge1xuICBwYWdlPzogbnVtYmVyXG4gIHBhZ2VTaXplPzogbnVtYmVyXG4gIHN0YXR1cz86ICdwZW5kaW5nJyB8ICdnZW5lcmF0ZWQnIHwgJ2FwcHJvdmVkJ1xuICBzZWFyY2hUZXJtPzogc3RyaW5nXG59ID0ge30pOiBQcm9taXNlPEFwaVJlc3BvbnNlPHtcbiAgY29uc3VsdGF0aW9uczogQ29uc3VsdGF0aW9uW11cbiAgaGFzTW9yZTogYm9vbGVhblxuICB0b3RhbENvdW50PzogbnVtYmVyXG59Pj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcbiAgICBjb25zdCBmcm9tID0gKHBhZ2UgLSAxKSAqIHBhZ2VTaXplXG4gICAgY29uc3QgdG8gPSBmcm9tICsgcGFnZVNpemUgLSAxXG5cbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnNlbGVjdCgnaWQsIGRvY3Rvcl9pZCwgc3VibWl0dGVkX2J5LCBwcmltYXJ5X2F1ZGlvX3VybCwgYWRkaXRpb25hbF9hdWRpb191cmxzLCBpbWFnZV91cmxzLCBhaV9nZW5lcmF0ZWRfbm90ZSwgZWRpdGVkX25vdGUsIHN0YXR1cywgcGF0aWVudF9udW1iZXIsIHBhdGllbnRfbmFtZSwgdG90YWxfZmlsZV9zaXplX2J5dGVzLCBmaWxlX3JldGVudGlvbl91bnRpbCwgY3JlYXRlZF9hdCwgdXBkYXRlZF9hdCwgY29uc3VsdGF0aW9uX3R5cGUsIGRvY3Rvcl9ub3RlcywgYWRkaXRpb25hbF9ub3RlcycsIHsgY291bnQ6ICdleGFjdCcgfSlcbiAgICAgIC5lcSgnZG9jdG9yX2lkJywgc2Vzc2lvbi51c2VySWQpXG4gICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgIC5yYW5nZShmcm9tLCB0bylcblxuICAgIC8vIEFwcGx5IHN0YXR1cyBmaWx0ZXJcbiAgICBpZiAoc3RhdHVzKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdzdGF0dXMnLCBzdGF0dXMpXG4gICAgfVxuXG4gICAgLy8gQXBwbHkgRlRTIHNlYXJjaCBpZiBwcm92aWRlZFxuICAgIGlmIChzZWFyY2hUZXJtICYmIHNlYXJjaFRlcm0udHJpbSgpKSB7XG4gICAgICAvLyBDb252ZXJ0IHNlYXJjaCB0ZXJtIHRvIEZUUyBxdWVyeSBmb3JtYXQgKHNwYWNlLXNlcGFyYXRlZCB3b3JkcyBqb2luZWQgd2l0aCAmKVxuICAgICAgY29uc3QgZnRzUXVlcnkgPSBzZWFyY2hUZXJtLnRyaW0oKS5zcGxpdCgvXFxzKy8pLmpvaW4oJyAmICcpXG4gICAgICBxdWVyeSA9IHF1ZXJ5LnRleHRTZWFyY2goJ2Z0cycsIGZ0c1F1ZXJ5KVxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YTogY29uc3VsdGF0aW9ucywgZXJyb3IsIGNvdW50IH0gPSBhd2FpdCBxdWVyeVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBlcnJvcjonLCBlcnJvcilcblxuICAgICAgLy8gSGFuZGxlIFwiUmVxdWVzdGVkIHJhbmdlIG5vdCBzYXRpc2ZpYWJsZVwiIGVycm9yIGdyYWNlZnVsbHlcbiAgICAgIGlmIChlcnJvci5jb2RlID09PSAnUEdSU1QxMDMnKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBjb25zdWx0YXRpb25zOiBbXSxcbiAgICAgICAgICAgIGhhc01vcmU6IGZhbHNlLFxuICAgICAgICAgICAgdG90YWxDb3VudDogY291bnQgfHwgMFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggY29uc3VsdGF0aW9ucycgfVxuICAgIH1cblxuICAgIC8vIE1hcCB0byBDb25zdWx0YXRpb25bXVxuICAgIGNvbnN0IHR5cGVkQ29uc3VsdGF0aW9ucyA9IChjb25zdWx0YXRpb25zIHx8IFtdKS5tYXAoKHJvdzogRGF0YWJhc2VbJ3B1YmxpYyddWydUYWJsZXMnXVsnY29uc3VsdGF0aW9ucyddWydSb3cnXSkgPT4ge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaWQ6IHJvdy5pZCxcbiAgICAgICAgZG9jdG9yX2lkOiByb3cuZG9jdG9yX2lkISxcbiAgICAgICAgc3VibWl0dGVkX2J5OiByb3cuc3VibWl0dGVkX2J5IGFzICdkb2N0b3InIHwgJ3JlY2VwdGlvbmlzdCcsXG4gICAgICAgIHByaW1hcnlfYXVkaW9fdXJsOiByb3cucHJpbWFyeV9hdWRpb191cmwsXG4gICAgICAgIGFkZGl0aW9uYWxfYXVkaW9fdXJsczogcGFyc2VKc29uU3RyaW5nQXJyYXkocm93LmFkZGl0aW9uYWxfYXVkaW9fdXJscyksXG4gICAgICAgIGltYWdlX3VybHM6IHBhcnNlSnNvblN0cmluZ0FycmF5KHJvdy5pbWFnZV91cmxzKSxcbiAgICAgICAgYWlfZ2VuZXJhdGVkX25vdGU6IHJvdy5haV9nZW5lcmF0ZWRfbm90ZSA/PyB1bmRlZmluZWQsXG4gICAgICAgIGVkaXRlZF9ub3RlOiByb3cuZWRpdGVkX25vdGUgPz8gdW5kZWZpbmVkLFxuICAgICAgICBzdGF0dXM6IHJvdy5zdGF0dXMgYXMgJ3BlbmRpbmcnIHwgJ2dlbmVyYXRlZCcgfCAnYXBwcm92ZWQnLFxuICAgICAgICBwYXRpZW50X251bWJlcjogcm93LnBhdGllbnRfbnVtYmVyID8/IHVuZGVmaW5lZCxcbiAgICAgICAgcGF0aWVudF9uYW1lOiByb3cucGF0aWVudF9uYW1lID8/IHVuZGVmaW5lZCxcbiAgICAgICAgdG90YWxfZmlsZV9zaXplX2J5dGVzOiByb3cudG90YWxfZmlsZV9zaXplX2J5dGVzID8/IDAsXG4gICAgICAgIGZpbGVfcmV0ZW50aW9uX3VudGlsOiByb3cuZmlsZV9yZXRlbnRpb25fdW50aWwsXG4gICAgICAgIGNyZWF0ZWRfYXQ6IHJvdy5jcmVhdGVkX2F0LFxuICAgICAgICB1cGRhdGVkX2F0OiByb3cudXBkYXRlZF9hdCxcbiAgICAgICAgY29uc3VsdGF0aW9uX3R5cGU6IHJvdy5jb25zdWx0YXRpb25fdHlwZSA/PyAnb3V0cGF0aWVudCcsXG4gICAgICAgIGRvY3Rvcl9ub3Rlczogcm93LmRvY3Rvcl9ub3RlcyA/PyB1bmRlZmluZWQsXG4gICAgICAgIGFkZGl0aW9uYWxfbm90ZXM6IHJvdy5hZGRpdGlvbmFsX25vdGVzID8/IHVuZGVmaW5lZCxcbiAgICAgIH0gYXMgQ29uc3VsdGF0aW9uXG4gICAgfSlcblxuICAgIGNvbnN0IGhhc01vcmUgPSAoY291bnQgfHwgMCkgPiB0byArIDFcblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YToge1xuICAgICAgICBjb25zdWx0YXRpb25zOiB0eXBlZENvbnN1bHRhdGlvbnMsXG4gICAgICAgIGhhc01vcmUsXG4gICAgICAgIHRvdGFsQ291bnQ6IGNvdW50IHx8IDBcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignR2V0IGNvbnN1bHRhdGlvbnMgZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG5cblxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVTdW1tYXJ5U3RyZWFtKFxuICBjb25zdWx0YXRpb25JZDogc3RyaW5nLFxuICBhZGRpdGlvbmFsSW1hZ2VzPzogc3RyaW5nW10sXG4gIG9uQ2h1bms/OiAoY2h1bms6IHN0cmluZykgPT4gdm9pZCxcbiAgb25Db21wbGV0ZT86IChzdW1tYXJ5OiBzdHJpbmcpID0+IHZvaWQsXG4gIG9uRXJyb3I/OiAoZXJyb3I6IHN0cmluZykgPT4gdm9pZCxcbiAgY29uc3VsdGF0aW9uVHlwZT86IHN0cmluZyxcbiAgZG9jdG9yTm90ZXM/OiBzdHJpbmcsXG4gIGFkZGl0aW9uYWxOb3Rlcz86IHN0cmluZ1xuKTogUHJvbWlzZTxBcGlSZXNwb25zZTxzdHJpbmc+PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IHZlcmlmeVNlc3Npb24oKVxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgb25FcnJvcj8uKCdVbmF1dGhvcml6ZWQnKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVW5hdXRob3JpemVkJyB9XG4gICAgfVxuXG4gICAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKVxuXG4gICAgLy8gR2V0IGNvbnN1bHRhdGlvbiBhbmQgZG9jdG9yIGRhdGFcbiAgICBjb25zdCB7IGRhdGE6IGNvbnN1bHRhdGlvbiwgZXJyb3I6IGNvbnN1bHRhdGlvbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnNlbGVjdCgnaWQsIGRvY3Rvcl9pZCwgc3VibWl0dGVkX2J5LCBwcmltYXJ5X2F1ZGlvX3VybCwgYWRkaXRpb25hbF9hdWRpb191cmxzLCBpbWFnZV91cmxzLCBhaV9nZW5lcmF0ZWRfbm90ZSwgZWRpdGVkX25vdGUsIHN0YXR1cywgcGF0aWVudF9udW1iZXIsIHBhdGllbnRfbmFtZSwgY3JlYXRlZF9hdCwgdXBkYXRlZF9hdCwgY29uc3VsdGF0aW9uX3R5cGUsIGRvY3Rvcl9ub3RlcywgZG9jdG9ycyhuYW1lKScpXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoY29uc3VsdGF0aW9uRXJyb3IgfHwgIWNvbnN1bHRhdGlvbikge1xuICAgICAgb25FcnJvcj8uKCdDb25zdWx0YXRpb24gbm90IGZvdW5kJylcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0NvbnN1bHRhdGlvbiBub3QgZm91bmQnIH1cbiAgICB9XG5cbiAgICAvLyBTdHJpY3RseSB0eXBlIGNvbnN1bHRhdGlvblxuICAgIHR5cGUgQ29uc3VsdGF0aW9uUm93ID0gRGF0YWJhc2VbJ3B1YmxpYyddWydUYWJsZXMnXVsnY29uc3VsdGF0aW9ucyddWydSb3cnXSAmIHsgZG9jdG9yczogeyBuYW1lOiBzdHJpbmcgfSB9XG4gICAgY29uc3QgdHlwZWRDb25zdWx0YXRpb24gPSBjb25zdWx0YXRpb24gYXMgQ29uc3VsdGF0aW9uUm93XG5cbiAgICAvLyBQYXJzZSBhZGRpdGlvbmFsX2F1ZGlvX3VybHMgYW5kIGltYWdlX3VybHMgZnJvbSBKU09OIGlmIG5lZWRlZFxuICAgIGNvbnN0IGFkZGl0aW9uYWxBdWRpb1VybHMgPSBwYXJzZUpzb25TdHJpbmdBcnJheSh0eXBlZENvbnN1bHRhdGlvbi5hZGRpdGlvbmFsX2F1ZGlvX3VybHMpXG4gICAgY29uc3QgaW1hZ2VVcmxzID0gcGFyc2VKc29uU3RyaW5nQXJyYXkodHlwZWRDb25zdWx0YXRpb24uaW1hZ2VfdXJscylcbiAgICBjb25zdCBhbGxJbWFnZVVybHMgPSBbLi4uaW1hZ2VVcmxzLCAuLi4oYWRkaXRpb25hbEltYWdlcyB8fCBbXSldXG5cbiAgICAvLyBVc2UgdGhlIFNBTUUgcHJvZHVjdGlvbi1ncmFkZSBzdHJlYW1pbmcgYXBwcm9hY2ggYXMgdGhlIENyZWF0ZSBidXR0b25zXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9hcGkvZ2VuZXJhdGUtc3VtbWFyeS1zdHJlYW1gLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIHByaW1hcnlfYXVkaW9fdXJsOiB0eXBlZENvbnN1bHRhdGlvbi5wcmltYXJ5X2F1ZGlvX3VybCxcbiAgICAgICAgYWRkaXRpb25hbF9hdWRpb191cmxzOiBBcnJheS5pc0FycmF5KGFkZGl0aW9uYWxBdWRpb1VybHMpID8gYWRkaXRpb25hbEF1ZGlvVXJscyA6IFtdLFxuICAgICAgICBpbWFnZV91cmxzOiBBcnJheS5pc0FycmF5KGFsbEltYWdlVXJscykgPyBhbGxJbWFnZVVybHMgOiBbXSxcbiAgICAgICAgc3VibWl0dGVkX2J5OiB0eXBlZENvbnN1bHRhdGlvbi5zdWJtaXR0ZWRfYnkgfHwgJ2RvY3RvcicsXG4gICAgICAgIGNvbnN1bHRhdGlvbl90eXBlOiBjb25zdWx0YXRpb25UeXBlIHx8IHR5cGVkQ29uc3VsdGF0aW9uLmNvbnN1bHRhdGlvbl90eXBlIHx8ICdvdXRwYXRpZW50JyxcbiAgICAgICAgZG9jdG9yX25vdGVzOiBkb2N0b3JOb3RlcyB8fCB0eXBlZENvbnN1bHRhdGlvbi5kb2N0b3Jfbm90ZXMgfHwgdW5kZWZpbmVkLFxuICAgICAgICBhZGRpdGlvbmFsX25vdGVzOiBhZGRpdGlvbmFsTm90ZXMgfHwgdW5kZWZpbmVkLFxuICAgICAgICBwYXRpZW50X25hbWU6IHR5cGVkQ29uc3VsdGF0aW9uLnBhdGllbnRfbmFtZSB8fCB1bmRlZmluZWQsXG4gICAgICAgIGRvY3Rvcl9uYW1lOiB0eXBlZENvbnN1bHRhdGlvbi5kb2N0b3JzLm5hbWUgfHwgdW5kZWZpbmVkLFxuICAgICAgICBjcmVhdGVkX2F0OiB0eXBlZENvbnN1bHRhdGlvbi5jcmVhdGVkX2F0IHx8IHVuZGVmaW5lZCxcbiAgICAgIH0pLFxuICAgIH0pXG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvck1zZyA9IGBIVFRQICR7cmVzcG9uc2Uuc3RhdHVzfTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWBcbiAgICAgIG9uRXJyb3I/LihlcnJvck1zZylcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNc2cgfVxuICAgIH1cblxuICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHk/LmdldFJlYWRlcigpXG4gICAgaWYgKCFyZWFkZXIpIHtcbiAgICAgIG9uRXJyb3I/LignTm8gcmVhZGVyIGF2YWlsYWJsZScpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdObyByZWFkZXIgYXZhaWxhYmxlJyB9XG4gICAgfVxuXG4gICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpXG4gICAgbGV0IGZ1bGxTdW1tYXJ5ID0gJydcblxuICAgIHRyeSB7XG4gICAgICB3aGlsZSAodHJ1ZSkge1xuICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpXG4gICAgICAgIGlmIChkb25lKSBicmVha1xuXG4gICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUsIHsgc3RyZWFtOiB0cnVlIH0pXG4gICAgICAgIGNvbnN0IGxpbmVzID0gY2h1bmsuc3BsaXQoJ1xcbicpXG5cbiAgICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7XG4gICAgICAgICAgaWYgKGxpbmUuc3RhcnRzV2l0aCgnZGF0YTogJykpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGxpbmUuc2xpY2UoNikpXG4gICAgICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICdjaHVuaycgJiYgZGF0YS50ZXh0KSB7XG4gICAgICAgICAgICAgICAgLy8gRm9ybWF0IHRleHQgd2l0aCBwcm9wZXIgbGluZSBicmVha3MgKHNhbWUgYXMgcHJvZHVjdGlvbilcbiAgICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRUZXh0ID0gZGF0YS50ZXh0LnJlcGxhY2UoL1xcXFxuL2csICdcXG4nKS5yZXBsYWNlKC9cXG5cXG4rL2csICdcXG5cXG4nKVxuICAgICAgICAgICAgICAgIGZ1bGxTdW1tYXJ5ICs9IGZvcm1hdHRlZFRleHRcbiAgICAgICAgICAgICAgICAvLyBDYWxsIG9uQ2h1bmsgd2l0aCB0aGUgbmV3IHRleHQgY2h1bmtcbiAgICAgICAgICAgICAgICBvbkNodW5rPy4oZm9ybWF0dGVkVGV4dClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoX2UpIHtcbiAgICAgICAgICAgICAgLy8gSWdub3JlIHBhcnNlIGVycm9ycyAoc2FtZSBhcyBwcm9kdWN0aW9uKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDYWxsIG9uQ29tcGxldGUgd2l0aCBmaW5hbCBzdW1tYXJ5XG4gICAgICBvbkNvbXBsZXRlPy4oZnVsbFN1bW1hcnkpXG5cbiAgICAgIC8vIFNhdmUgdGhlIHN0cmVhbWVkIHN1bW1hcnkgdXNpbmcgdGhlIHNhbWUgYXBwcm9hY2ggYXMgcHJvZHVjdGlvblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2F2ZVJlc3VsdCA9IGF3YWl0IHNhdmVTdHJlYW1pbmdTdW1tYXJ5KGNvbnN1bHRhdGlvbklkLCBmdWxsU3VtbWFyeSlcbiAgICAgICAgaWYgKCFzYXZlUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBzdHJlYW1pbmcgc3VtbWFyeTonLCBzYXZlUmVzdWx0LmVycm9yKVxuICAgICAgICAgIG9uRXJyb3I/LihgU3VtbWFyeSBnZW5lcmF0ZWQgYnV0IGZhaWxlZCB0byBzYXZlOiAke3NhdmVSZXN1bHQuZXJyb3J9YClcbiAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGBTdW1tYXJ5IGdlbmVyYXRlZCBidXQgZmFpbGVkIHRvIHNhdmU6ICR7c2F2ZVJlc3VsdC5lcnJvcn1gIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoc2F2ZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBzdHJlYW1pbmcgc3VtbWFyeTonLCBzYXZlRXJyb3IpXG4gICAgICAgIG9uRXJyb3I/LignU3VtbWFyeSBnZW5lcmF0ZWQgYnV0IGZhaWxlZCB0byBzYXZlLiBQbGVhc2UgdHJ5IHJlZ2VuZXJhdGUuJylcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnU3VtbWFyeSBnZW5lcmF0ZWQgYnV0IGZhaWxlZCB0byBzYXZlLiBQbGVhc2UgdHJ5IHJlZ2VuZXJhdGUuJyB9XG4gICAgICB9XG5cbiAgICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IGZ1bGxTdW1tYXJ5IH1cblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgR2VuZXJhdGUgZXJyb3I6JywgZXJyb3IpXG4gICAgICBjb25zdCBlcnJvck1zZyA9IGBGYWlsZWQgdG8gZ2VuZXJhdGUgc3VtbWFyeTogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ31gXG4gICAgICBvbkVycm9yPy4oZXJyb3JNc2cpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTXNnIH1cbiAgICB9XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdHZW5lcmF0ZSBzdHJlYW1pbmcgc3VtbWFyeSBlcnJvcjonLCBlcnJvcilcbiAgICBvbkVycm9yPy4oJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYXBwcm92ZUNvbnN1bHRhdGlvbihcbiAgY29uc3VsdGF0aW9uSWQ6IHN0cmluZyxcbiAgZWRpdGVkTm90ZTogc3RyaW5nXG4pOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IHZlcmlmeVNlc3Npb24oKVxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVW5hdXRob3JpemVkJyB9XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZWRpdGVkIG5vdGVcbiAgICBjb25zdCB2YWxpZGF0ZWRGaWVsZHMgPSBDb25zdWx0YXRpb25VcGRhdGVTY2hlbWEuc2FmZVBhcnNlKHtcbiAgICAgIGVkaXRlZF9ub3RlOiBlZGl0ZWROb3RlLFxuICAgIH0pXG5cbiAgICBpZiAoIXZhbGlkYXRlZEZpZWxkcy5zdWNjZXNzKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIG5vdGUgY29udGVudCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb25cbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnVwZGF0ZSh7XG4gICAgICAgIGVkaXRlZF9ub3RlOiBlZGl0ZWROb3RlLFxuICAgICAgICBzdGF0dXM6ICdhcHByb3ZlZCcsXG4gICAgICB9IGFzIERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NvbnN1bHRhdGlvbnMnXVsnVXBkYXRlJ10pXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVcGRhdGUgZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gYXBwcm92ZSBjb25zdWx0YXRpb24nIH1cbiAgICB9XG5cbiAgICByZXZhbGlkYXRlUGF0aCgnL2Rhc2hib2FyZCcpXG5cbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB0cnVlIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdBcHByb3ZlIGNvbnN1bHRhdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyB9XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUNvbnN1bHRhdGlvbkltYWdlcyhjb25zdWx0YXRpb25JZDogc3RyaW5nLCBpbWFnZVVybHM6IHN0cmluZ1tdKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb24gd2l0aCBuZXcgaW1hZ2UgVVJMc1xuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY29uc3VsdGF0aW9ucycpXG4gICAgICAudXBkYXRlKHtcbiAgICAgICAgaW1hZ2VfdXJsczogaW1hZ2VVcmxzLFxuICAgICAgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgICAgLmVxKCdkb2N0b3JfaWQnLCBzZXNzaW9uLnVzZXJJZClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGNvbnN1bHRhdGlvbiBpbWFnZXMgZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIGNvbnN1bHRhdGlvbiBpbWFnZXMnIH1cbiAgICB9XG5cbiAgICByZXZhbGlkYXRlUGF0aCgnL2Rhc2hib2FyZCcpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogdHJ1ZSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignVXBkYXRlIGNvbnN1bHRhdGlvbiBpbWFnZXMgZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhZGRBZGRpdGlvbmFsQXVkaW8oY29uc3VsdGF0aW9uSWQ6IHN0cmluZywgYXVkaW9GaWxlOiBGaWxlKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG5cbiAgICAvLyBGZXRjaCB0aGUgY29uc3VsdGF0aW9uXG4gICAgY29uc3QgeyBkYXRhOiBjb25zdWx0YXRpb24sIGVycm9yOiBmZXRjaEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnNlbGVjdCgnYWRkaXRpb25hbF9hdWRpb191cmxzLCBzdGF0dXMnKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgICAgLmVxKCdkb2N0b3JfaWQnLCBzZXNzaW9uLnVzZXJJZClcbiAgICAgIC5zaW5nbGUoKVxuICAgIGlmIChmZXRjaEVycm9yIHx8ICFjb25zdWx0YXRpb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0NvbnN1bHRhdGlvbiBub3QgZm91bmQnIH1cbiAgICB9XG4gICAgaWYgKGNvbnN1bHRhdGlvbi5zdGF0dXMgPT09ICdhcHByb3ZlZCcpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYENhbm5vdCBhZGQgYXVkaW8gdG8gYXBwcm92ZWQgY29uc3VsdGF0aW9ucy4gQ3VycmVudCBzdGF0dXM6ICR7Y29uc3VsdGF0aW9uLnN0YXR1c31gIH1cbiAgICB9XG5cbiAgICAvLyBVcGxvYWQgdGhlIGFkZGl0aW9uYWwgYXVkaW8gZmlsZVxuICAgIGNvbnN0IHVwbG9hZFJlc3VsdCA9IGF3YWl0IHVwbG9hZEZpbGUoYXVkaW9GaWxlLCBzZXNzaW9uLnVzZXJJZCwgY29uc3VsdGF0aW9uSWQsICdhdWRpbycpXG4gICAgaWYgKCF1cGxvYWRSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBgQXVkaW8gdXBsb2FkIGZhaWxlZDogJHt1cGxvYWRSZXN1bHQuZXJyb3J9YCB9XG4gICAgfVxuXG4gICAgY29uc3QgYWRkaXRpb25hbEF1ZGlvVXJscyA9IHBhcnNlSnNvblN0cmluZ0FycmF5KGNvbnN1bHRhdGlvbi5hZGRpdGlvbmFsX2F1ZGlvX3VybHMpXG4gICAgYWRkaXRpb25hbEF1ZGlvVXJscy5wdXNoKHVwbG9hZFJlc3VsdC51cmwhKVxuXG4gICAgLy8gVXBkYXRlIHRoZSBjb25zdWx0YXRpb25cbiAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoeyBhZGRpdGlvbmFsX2F1ZGlvX3VybHM6IGFkZGl0aW9uYWxBdWRpb1VybHMgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIGFkZCBhZGRpdGlvbmFsIGF1ZGlvJyB9XG4gICAgfVxuICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICByZXZhbGlkYXRlUGF0aCgnL3JlY29yZCcpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogdHJ1ZSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQWRkIGFkZGl0aW9uYWwgYXVkaW8gZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhZGRBZGRpdGlvbmFsSW1hZ2VzKGNvbnN1bHRhdGlvbklkOiBzdHJpbmcsIGltYWdlRmlsZXM6IEZpbGVbXSk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgdmVyaWZ5U2Vzc2lvbigpXG4gICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH1cbiAgICB9XG4gICAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKVxuXG4gICAgLy8gRmV0Y2ggdGhlIGNvbnN1bHRhdGlvblxuICAgIGNvbnN0IHsgZGF0YTogY29uc3VsdGF0aW9uLCBlcnJvcjogZmV0Y2hFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC5zZWxlY3QoJ2ltYWdlX3VybHMsIHN0YXR1cycpXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuICAgICAgLnNpbmdsZSgpXG4gICAgaWYgKGZldGNoRXJyb3IgfHwgIWNvbnN1bHRhdGlvbikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQ29uc3VsdGF0aW9uIG5vdCBmb3VuZCcgfVxuICAgIH1cbiAgICBpZiAoY29uc3VsdGF0aW9uLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJykge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBgQ2Fubm90IGFkZCBpbWFnZXMgdG8gYXBwcm92ZWQgY29uc3VsdGF0aW9ucy4gQ3VycmVudCBzdGF0dXM6ICR7Y29uc3VsdGF0aW9uLnN0YXR1c31gIH1cbiAgICB9XG5cbiAgICAvLyBVcGxvYWQgYWxsIGltYWdlIGZpbGVzXG4gICAgY29uc3QgdXBsb2FkUmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgaW1hZ2VGaWxlcy5tYXAoZmlsZSA9PiB1cGxvYWRGaWxlKGZpbGUsIHNlc3Npb24udXNlcklkLCBjb25zdWx0YXRpb25JZCwgJ2ltYWdlJykpXG4gICAgKVxuXG4gICAgLy8gQ2hlY2sgaWYgYWxsIHVwbG9hZHMgc3VjY2VlZGVkXG4gICAgY29uc3QgZmFpbGVkVXBsb2FkcyA9IHVwbG9hZFJlc3VsdHMuZmlsdGVyKHJlc3VsdCA9PiAhcmVzdWx0LnN1Y2Nlc3MpXG4gICAgaWYgKGZhaWxlZFVwbG9hZHMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgZXJyb3JzID0gZmFpbGVkVXBsb2Fkcy5tYXAoZiA9PiBmLmVycm9yKS5qb2luKCcsICcpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGBJbWFnZSB1cGxvYWQgZmFpbGVkOiAke2Vycm9yc31gIH1cbiAgICB9XG5cbiAgICAvLyBHZXQgZXhpc3RpbmcgaW1hZ2UgVVJMcyBhbmQgYWRkIG5ldyBvbmVzXG4gICAgY29uc3QgZXhpc3RpbmdJbWFnZVVybHMgPSBwYXJzZUpzb25TdHJpbmdBcnJheShjb25zdWx0YXRpb24uaW1hZ2VfdXJscylcbiAgICBjb25zdCBuZXdJbWFnZVVybHMgPSB1cGxvYWRSZXN1bHRzLm1hcChyZXN1bHQgPT4gcmVzdWx0LnVybCEpLmZpbHRlcih1cmwgPT4gdXJsKVxuICAgIGNvbnN0IGFsbEltYWdlVXJscyA9IFsuLi5leGlzdGluZ0ltYWdlVXJscywgLi4ubmV3SW1hZ2VVcmxzXVxuXG4gICAgLy8gVXBkYXRlIHRoZSBjb25zdWx0YXRpb25cbiAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoeyBpbWFnZV91cmxzOiBhbGxJbWFnZVVybHMgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIGFkZCBhZGRpdGlvbmFsIGltYWdlcycgfVxuICAgIH1cblxuICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICByZXZhbGlkYXRlUGF0aCgnL3JlY29yZCcpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogdHJ1ZSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQWRkIGFkZGl0aW9uYWwgaW1hZ2VzIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlQ29uc3VsdGF0aW9uVHlwZShjb25zdWx0YXRpb25JZDogc3RyaW5nLCBjb25zdWx0YXRpb25UeXBlOiAnb3V0cGF0aWVudCcgfCAnZGlzY2hhcmdlJyB8ICdzdXJnZXJ5JyB8ICdyYWRpb2xvZ3knIHwgJ2Rlcm1hdG9sb2d5JyB8ICdjYXJkaW9sb2d5X2VjaG8nIHwgJ2l2Zl9jeWNsZScgfCAncGF0aG9sb2d5Jyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgdmVyaWZ5U2Vzc2lvbigpXG4gICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH1cbiAgICB9XG5cbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG5cbiAgICAvLyBVcGRhdGUgY29uc3VsdGF0aW9uIHR5cGVcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NvbnN1bHRhdGlvbnMnKVxuICAgICAgLnVwZGF0ZSh7XG4gICAgICAgIGNvbnN1bHRhdGlvbl90eXBlOiBjb25zdWx0YXRpb25UeXBlLFxuICAgICAgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgICAgLmVxKCdkb2N0b3JfaWQnLCBzZXNzaW9uLnVzZXJJZClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGNvbnN1bHRhdGlvbiB0eXBlIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBjb25zdWx0YXRpb24gdHlwZScgfVxuICAgIH1cblxuICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB0cnVlIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdVcGRhdGUgY29uc3VsdGF0aW9uIHR5cGUgZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzYXZlRWRpdGVkTm90ZShjb25zdWx0YXRpb25JZDogc3RyaW5nLCBlZGl0ZWROb3RlOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IHZlcmlmeVNlc3Npb24oKVxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVW5hdXRob3JpemVkJyB9XG4gICAgfVxuXG4gICAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKVxuXG4gICAgLy8gVXBkYXRlIGNvbnN1bHRhdGlvbiB3aXRoIGVkaXRlZCBub3RlXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBlZGl0ZWRfbm90ZTogZWRpdGVkTm90ZSxcbiAgICAgIH0gYXMgRGF0YWJhc2VbJ3B1YmxpYyddWydUYWJsZXMnXVsnY29uc3VsdGF0aW9ucyddWydVcGRhdGUnXSlcbiAgICAgIC5lcSgnaWQnLCBjb25zdWx0YXRpb25JZClcbiAgICAgIC5lcSgnZG9jdG9yX2lkJywgc2Vzc2lvbi51c2VySWQpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1NhdmUgZWRpdGVkIG5vdGUgZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gc2F2ZSBlZGl0ZWQgbm90ZScgfVxuICAgIH1cblxuICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB0cnVlIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdTYXZlIGVkaXRlZCBub3RlIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlQWRkaXRpb25hbE5vdGVzKGNvbnN1bHRhdGlvbklkOiBzdHJpbmcsIGFkZGl0aW9uYWxOb3Rlczogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb24gd2l0aCBhZGRpdGlvbmFsIG5vdGVzXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBhZGRpdGlvbmFsX25vdGVzOiBhZGRpdGlvbmFsTm90ZXMgfHwgbnVsbCxcbiAgICAgIH0gYXMgRGF0YWJhc2VbJ3B1YmxpYyddWydUYWJsZXMnXVsnY29uc3VsdGF0aW9ucyddWydVcGRhdGUnXSlcbiAgICAgIC5lcSgnaWQnLCBjb25zdWx0YXRpb25JZClcbiAgICAgIC5lcSgnZG9jdG9yX2lkJywgc2Vzc2lvbi51c2VySWQpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwZGF0ZSBhZGRpdGlvbmFsIG5vdGVzIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBhZGRpdGlvbmFsIG5vdGVzJyB9XG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHRydWUgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1VwZGF0ZSBhZGRpdGlvbmFsIG5vdGVzIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlUGF0aWVudE5hbWUoY29uc3VsdGF0aW9uSWQ6IHN0cmluZywgcGF0aWVudE5hbWU6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgdmVyaWZ5U2Vzc2lvbigpXG4gICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH1cbiAgICB9XG5cbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG5cbiAgICAvLyBVcGRhdGUgY29uc3VsdGF0aW9uIHdpdGggcGF0aWVudCBuYW1lXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBwYXRpZW50X25hbWU6IHBhdGllbnROYW1lIHx8IG51bGwsXG4gICAgICB9IGFzIERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NvbnN1bHRhdGlvbnMnXVsnVXBkYXRlJ10pXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVcGRhdGUgcGF0aWVudCBuYW1lIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBwYXRpZW50IG5hbWUnIH1cbiAgICB9XG5cbiAgICByZXZhbGlkYXRlUGF0aCgnL2Rhc2hib2FyZCcpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogdHJ1ZSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignVXBkYXRlIHBhdGllbnQgbmFtZSBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyB9XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNsZWFyRWRpdGVkTm90ZShjb25zdWx0YXRpb25JZDogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIENsZWFyIGVkaXRlZCBub3RlIGZyb20gY29uc3VsdGF0aW9uXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBlZGl0ZWRfbm90ZTogbnVsbCxcbiAgICAgIH0gYXMgRGF0YWJhc2VbJ3B1YmxpYyddWydUYWJsZXMnXVsnY29uc3VsdGF0aW9ucyddWydVcGRhdGUnXSlcbiAgICAgIC5lcSgnaWQnLCBjb25zdWx0YXRpb25JZClcbiAgICAgIC5lcSgnZG9jdG9yX2lkJywgc2Vzc2lvbi51c2VySWQpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NsZWFyIGVkaXRlZCBub3RlIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRmFpbGVkIHRvIGNsZWFyIGVkaXRlZCBub3RlJyB9XG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHRydWUgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0NsZWFyIGVkaXRlZCBub3RlIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG4vLyBEZWxldGUgYWRkaXRpb25hbCBhdWRpbyBmcm9tIGNvbnN1bHRhdGlvblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRlbGV0ZUFkZGl0aW9uYWxBdWRpbyhcbiAgY29uc3VsdGF0aW9uSWQ6IHN0cmluZyxcbiAgYXVkaW9Vcmw6IHN0cmluZ1xuKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIEdldCBjdXJyZW50IGNvbnN1bHRhdGlvblxuICAgIGNvbnN0IHsgZGF0YTogY29uc3VsdGF0aW9uLCBlcnJvcjogZmV0Y2hFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC5zZWxlY3QoJ2FkZGl0aW9uYWxfYXVkaW9fdXJscywgc3RhdHVzJylcbiAgICAgIC5lcSgnaWQnLCBjb25zdWx0YXRpb25JZClcbiAgICAgIC5lcSgnZG9jdG9yX2lkJywgc2Vzc2lvbi51c2VySWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChmZXRjaEVycm9yIHx8ICFjb25zdWx0YXRpb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0NvbnN1bHRhdGlvbiBub3QgZm91bmQnIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBjb25zdWx0YXRpb24gY2FuIGJlIG1vZGlmaWVkXG4gICAgaWYgKGNvbnN1bHRhdGlvbi5zdGF0dXMgPT09ICdhcHByb3ZlZCcpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0Nhbm5vdCBkZWxldGUgZmlsZXMgZnJvbSBhcHByb3ZlZCBjb25zdWx0YXRpb25zJyB9XG4gICAgfVxuXG4gICAgLy8gUGFyc2UgYW5kIGZpbHRlciBvdXQgdGhlIGF1ZGlvIFVSTFxuICAgIGNvbnN0IGFkZGl0aW9uYWxBdWRpb1VybHMgPSBwYXJzZUpzb25TdHJpbmdBcnJheShjb25zdWx0YXRpb24uYWRkaXRpb25hbF9hdWRpb191cmxzKVxuICAgIGNvbnN0IHVwZGF0ZWRBdWRpb1VybHMgPSBhZGRpdGlvbmFsQXVkaW9VcmxzLmZpbHRlcih1cmwgPT4gdXJsICE9PSBhdWRpb1VybClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb25cbiAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBhZGRpdGlvbmFsX2F1ZGlvX3VybHM6IHVwZGF0ZWRBdWRpb1VybHMsXG4gICAgICB9IGFzIERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NvbnN1bHRhdGlvbnMnXVsnVXBkYXRlJ10pXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuXG4gICAgaWYgKHVwZGF0ZUVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEZWxldGUgYWRkaXRpb25hbCBhdWRpbyBlcnJvcjonLCB1cGRhdGVFcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgYWRkaXRpb25hbCBhdWRpbycgfVxuICAgIH1cblxuICAgIC8vIERlbGV0ZSBmaWxlIGZyb20gc3RvcmFnZVxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRlbGV0ZUZpbGUgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvc3RvcmFnZScpXG4gICAgICBjb25zdCBmaWxlUGF0aCA9IGF1ZGlvVXJsLnNwbGl0KCcvJykucG9wKCkgfHwgJydcbiAgICAgIGF3YWl0IGRlbGV0ZUZpbGUoZmlsZVBhdGgsICdhdWRpbycpXG4gICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTdG9yYWdlIGRlbGV0aW9uIGVycm9yOicsIHN0b3JhZ2VFcnJvcilcbiAgICAgIC8vIERvbid0IGZhaWwgdGhlIG9wZXJhdGlvbiBpZiBzdG9yYWdlIGRlbGV0aW9uIGZhaWxzXG4gICAgfVxuXG4gICAgcmV2YWxpZGF0ZVBhdGgoJy9kYXNoYm9hcmQnKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHRydWUgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0RlbGV0ZSBhZGRpdGlvbmFsIGF1ZGlvIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG4vLyBEZWxldGUgaW1hZ2UgZnJvbSBjb25zdWx0YXRpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkZWxldGVDb25zdWx0YXRpb25JbWFnZShcbiAgY29uc3VsdGF0aW9uSWQ6IHN0cmluZyxcbiAgaW1hZ2VVcmw6IHN0cmluZ1xuKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcblxuICAgIC8vIEdldCBjdXJyZW50IGNvbnN1bHRhdGlvblxuICAgIGNvbnN0IHsgZGF0YTogY29uc3VsdGF0aW9uLCBlcnJvcjogZmV0Y2hFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC5zZWxlY3QoJ2ltYWdlX3VybHMsIHN0YXR1cycpXG4gICAgICAuZXEoJ2lkJywgY29uc3VsdGF0aW9uSWQpXG4gICAgICAuZXEoJ2RvY3Rvcl9pZCcsIHNlc3Npb24udXNlcklkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZmV0Y2hFcnJvciB8fCAhY29uc3VsdGF0aW9uKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdDb25zdWx0YXRpb24gbm90IGZvdW5kJyB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgY29uc3VsdGF0aW9uIGNhbiBiZSBtb2RpZmllZFxuICAgIGlmIChjb25zdWx0YXRpb24uc3RhdHVzID09PSAnYXBwcm92ZWQnKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdDYW5ub3QgZGVsZXRlIGZpbGVzIGZyb20gYXBwcm92ZWQgY29uc3VsdGF0aW9ucycgfVxuICAgIH1cblxuICAgIC8vIFBhcnNlIGFuZCBmaWx0ZXIgb3V0IHRoZSBpbWFnZSBVUkxcbiAgICBjb25zdCBpbWFnZVVybHMgPSBwYXJzZUpzb25TdHJpbmdBcnJheShjb25zdWx0YXRpb24uaW1hZ2VfdXJscylcbiAgICBjb25zdCB1cGRhdGVkSW1hZ2VVcmxzID0gaW1hZ2VVcmxzLmZpbHRlcih1cmwgPT4gdXJsICE9PSBpbWFnZVVybClcblxuICAgIC8vIFVwZGF0ZSBjb25zdWx0YXRpb25cbiAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjb25zdWx0YXRpb25zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBpbWFnZV91cmxzOiB1cGRhdGVkSW1hZ2VVcmxzLFxuICAgICAgfSBhcyBEYXRhYmFzZVsncHVibGljJ11bJ1RhYmxlcyddWydjb25zdWx0YXRpb25zJ11bJ1VwZGF0ZSddKVxuICAgICAgLmVxKCdpZCcsIGNvbnN1bHRhdGlvbklkKVxuICAgICAgLmVxKCdkb2N0b3JfaWQnLCBzZXNzaW9uLnVzZXJJZClcblxuICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRGVsZXRlIGNvbnN1bHRhdGlvbiBpbWFnZSBlcnJvcjonLCB1cGRhdGVFcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgaW1hZ2UnIH1cbiAgICB9XG5cbiAgICAvLyBEZWxldGUgZmlsZSBmcm9tIHN0b3JhZ2VcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkZWxldGVGaWxlIH0gPSBhd2FpdCBpbXBvcnQoJ0AvbGliL3N0b3JhZ2UnKVxuICAgICAgY29uc3QgZmlsZVBhdGggPSBpbWFnZVVybC5zcGxpdCgnLycpLnBvcCgpIHx8ICcnXG4gICAgICBhd2FpdCBkZWxldGVGaWxlKGZpbGVQYXRoLCAnaW1hZ2UnKVxuICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignU3RvcmFnZSBkZWxldGlvbiBlcnJvcjonLCBzdG9yYWdlRXJyb3IpXG4gICAgICAvLyBEb24ndCBmYWlsIHRoZSBvcGVyYXRpb24gaWYgc3RvcmFnZSBkZWxldGlvbiBmYWlsc1xuICAgIH1cblxuICAgIHJldmFsaWRhdGVQYXRoKCcvZGFzaGJvYXJkJylcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB0cnVlIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdEZWxldGUgY29uc3VsdGF0aW9uIGltYWdlIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnIH1cbiAgfVxufVxuXG4vLyBHZXQgY29uc3VsdGF0aW9uIHN0YXRpc3RpY3MgdXNpbmcgb3B0aW1pemVkIGRhdGFiYXNlIGZ1bmN0aW9uXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q29uc3VsdGF0aW9uU3RhdHMoKTogUHJvbWlzZTxBcGlSZXNwb25zZTx7XG4gIHRvdGFsX2NvbnN1bHRhdGlvbnM6IG51bWJlclxuICBwZW5kaW5nX2NvbnN1bHRhdGlvbnM6IG51bWJlclxuICBhcHByb3ZlZF9jb25zdWx0YXRpb25zOiBudW1iZXJcbiAgdG9kYXlfY29uc3VsdGF0aW9uczogbnVtYmVyXG59Pj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCB2ZXJpZnlTZXNzaW9uKClcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfVxuICAgIH1cblxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlQ2xpZW50KClcbiAgICBcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5ycGMoJ2dldF9jb25zdWx0YXRpb25fc3RhdHMnLCB7XG4gICAgICBkb2N0b3JfdXVpZDogc2Vzc2lvbi51c2VySWRcbiAgICB9KVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBjb25zdWx0YXRpb24gc3RhdHMnIH1cbiAgICB9XG5cbiAgICBjb25zdCBzdGF0cyA9IGRhdGE/LlswXSB8fCB7XG4gICAgICB0b3RhbF9jb25zdWx0YXRpb25zOiAwLFxuICAgICAgcGVuZGluZ19jb25zdWx0YXRpb25zOiAwLFxuICAgICAgYXBwcm92ZWRfY29uc3VsdGF0aW9uczogMCxcbiAgICAgIHRvZGF5X2NvbnN1bHRhdGlvbnM6IDBcbiAgICB9XG5cbiAgICByZXR1cm4geyBcbiAgICAgIHN1Y2Nlc3M6IHRydWUsIFxuICAgICAgZGF0YToge1xuICAgICAgICB0b3RhbF9jb25zdWx0YXRpb25zOiBOdW1iZXIoc3RhdHMudG90YWxfY29uc3VsdGF0aW9ucyksXG4gICAgICAgIHBlbmRpbmdfY29uc3VsdGF0aW9uczogTnVtYmVyKHN0YXRzLnBlbmRpbmdfY29uc3VsdGF0aW9ucyksXG4gICAgICAgIGFwcHJvdmVkX2NvbnN1bHRhdGlvbnM6IE51bWJlcihzdGF0cy5hcHByb3ZlZF9jb25zdWx0YXRpb25zKSxcbiAgICAgICAgdG9kYXlfY29uc3VsdGF0aW9uczogTnVtYmVyKHN0YXRzLnRvZGF5X2NvbnN1bHRhdGlvbnMpXG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0dldCBjb25zdWx0YXRpb24gc3RhdHMgZXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjJTQWl2QnNCIn0=
}}),
"[project]/src/lib/actions/data:8dbed9 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c":"getConsultationStats"},"src/lib/actions/consultations.ts",""] */ __turbopack_context__.s({
    "getConsultationStats": (()=>getConsultationStats)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var getConsultationStats = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("0021892c2090f634723ac0a0d1cd1d8c637aae5b9c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getConsultationStats"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addAdditionalAudio": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d3d376__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["addAdditionalAudio"]),
    "addAdditionalImages": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$7da740__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["addAdditionalImages"]),
    "approveConsultation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d87154__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["approveConsultation"]),
    "clearEditedNote": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$ee4577__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["clearEditedNote"]),
    "createConsultation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$654ad4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["createConsultation"]),
    "createConsultationWithFiles": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d5fe61__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["createConsultationWithFiles"]),
    "deleteAdditionalAudio": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$9ffef8__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["deleteAdditionalAudio"]),
    "deleteConsultationImage": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$bc8c0d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["deleteConsultationImage"]),
    "generateSummaryStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b1e14a__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateSummaryStream"]),
    "getConsultationStats": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$8dbed9__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getConsultationStats"]),
    "getConsultations": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$759865__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getConsultations"]),
    "saveEditedNote": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b57dd0__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["saveEditedNote"]),
    "saveStreamingSummary": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b6153b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["saveStreamingSummary"]),
    "updateAdditionalNotes": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$72301d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updateAdditionalNotes"]),
    "updateConsultationImages": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$fb1f18__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updateConsultationImages"]),
    "updateConsultationType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$2326c4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updateConsultationType"]),
    "updatePatientName": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$73b1a1__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updatePatientName"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d5fe61__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:d5fe61 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b6153b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:b6153b [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$654ad4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:654ad4 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$759865__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:759865 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b1e14a__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:b1e14a [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d87154__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:d87154 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$fb1f18__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:fb1f18 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$d3d376__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:d3d376 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$7da740__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:7da740 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$2326c4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:2326c4 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$b57dd0__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:b57dd0 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$72301d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:72301d [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$73b1a1__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:73b1a1 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$ee4577__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:ee4577 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$9ffef8__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:9ffef8 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$bc8c0d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:bc8c0d [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$data$3a$8dbed9__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/data:8dbed9 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addAdditionalAudio": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["addAdditionalAudio"]),
    "addAdditionalImages": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["addAdditionalImages"]),
    "approveConsultation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["approveConsultation"]),
    "clearEditedNote": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["clearEditedNote"]),
    "createConsultation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createConsultation"]),
    "createConsultationWithFiles": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createConsultationWithFiles"]),
    "deleteAdditionalAudio": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["deleteAdditionalAudio"]),
    "deleteConsultationImage": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["deleteConsultationImage"]),
    "generateSummaryStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateSummaryStream"]),
    "getConsultationStats": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getConsultationStats"]),
    "getConsultations": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getConsultations"]),
    "saveEditedNote": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["saveEditedNote"]),
    "saveStreamingSummary": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["saveStreamingSummary"]),
    "updateAdditionalNotes": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateAdditionalNotes"]),
    "updateConsultationImages": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateConsultationImages"]),
    "updateConsultationType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateConsultationType"]),
    "updatePatientName": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updatePatientName"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_lib_actions_022b6a00._.js.map