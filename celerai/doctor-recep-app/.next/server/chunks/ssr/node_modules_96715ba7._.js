module.exports={445237:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";e._=function(a){return a&&a.__esModule?a:{default:a}}},641093:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"HTTPAccessErrorFallback",{enumerable:!0,get:function(){return f}});let b=a.r(445237),c=a.r(129629);b._(a.r(465421));let g={fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},h={display:"inline-block"},i={display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},j={fontSize:14,fontWeight:400,lineHeight:"49px",margin:0};function f(a){let{status:b,message:d}=a;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("title",{children:b+": "+d}),(0,c.jsx)("div",{style:g,children:(0,c.jsxs)("div",{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,c.jsx)("h1",{className:"next-error-h1",style:i,children:b}),(0,c.jsx)("div",{style:h,children:(0,c.jsx)("h2",{style:j,children:d})})]})})]})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}},106157:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return f}});let b=a.r(129629),c=a.r(641093);function f(){return(0,b.jsx)(c.HTTPAccessErrorFallback,{status:404,message:"This page could not be found."})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),d.exports=e.default)}}};

//# sourceMappingURL=node_modules_96715ba7._.js.map