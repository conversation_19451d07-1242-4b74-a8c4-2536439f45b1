module.exports={329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},137496:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createSession:()=>i,decrypt:()=>h,deleteSession:()=>l,encrypt:()=>g,refreshSession:()=>k,updateSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify session"),null}}async function i(a){let b=new Date(Date.now()+6048e5),c=await g({userId:a,expiresAt:b}),d=await (0,f.cookies)();console.log("DEBUG: Creating session for user:",a),console.log("DEBUG: Session expires at:",b),d.set("session",c,{httpOnly:!0,secure:!1,expires:b,sameSite:"lax",path:"/"}),console.log("DEBUG: Session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("session")?.value,c=await h(b);if(console.log("DEBUG: Updating session - session exists:",!!b),console.log("DEBUG: Updating session - payload valid:",!!c),!b||!c)return console.log("DEBUG: Cannot update session - missing session or payload"),null;let d=new Date(Date.now()+6048e5);a.set("session",b,{httpOnly:!0,secure:!1,expires:d,sameSite:"lax",path:"/"}),console.log("DEBUG: Session updated successfully")}async function k(a){console.log("DEBUG: Refreshing session for user:",a),await l(),await i(a),console.log("DEBUG: Session refresh completed")}async function l(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting session cookie"),a.delete("session"),console.log("DEBUG: Session cookie deleted")}}},76803:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkSession:()=>c,getDoctorQuota:()=>k,getUser:()=>i,getUserById:()=>j,verifySession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(137496),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId||(0,f.redirect)("/login"),{isAuth:!0,userId:c.userId}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("session")?.value,c=await (0,g.decrypt)(b);return c?.userId?{isAuth:!0,userId:c.userId}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("*").eq("id",a.userId).single();if(d)return console.error("Failed to fetch user:",d.message||d),null;if(!c)return null;return{...c,password_hash:c.password_hash}}catch(a){return console.error("Failed to fetch user:",a instanceof Error?a.message:a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash").eq("id",a).single();if(d)return console.error("Failed to fetch user by ID:",d.message||d),null;if(!c)return null;return{...c}}catch(a){return console.error("Failed to fetch user by ID:",a instanceof Error?a.message:a),null}}),k=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("doctors").select("monthly_quota, quota_used, quota_reset_at").eq("id",a).single();if(d)return console.error("Failed to fetch quota:",d.message||d),null;let e=c.monthly_quota-c.quota_used,f=Math.round(c.quota_used/c.monthly_quota*100),g=new Date(c.quota_reset_at),i=Math.ceil((g.getTime()-Date.now())/864e5);return{monthly_quota:c.monthly_quota,quota_used:c.quota_used,quota_remaining:e,quota_percentage:f,quota_reset_at:c.quota_reset_at,days_until_reset:Math.max(0,i)}}catch(a){return console.error("Failed to fetch quota:",a instanceof Error?a.message:a),null}})}},680493:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createContactRequest:()=>g,getContactRequests:()=>h,getContactRequestsCount:()=>k,getPendingContactRequests:()=>i,hasActiveContactRequest:()=>l,updateContactRequestStatus:()=>j});var d=a.i(930768);a.i(996945);var e=a.i(729149),f=a.i(719358);async function g(a,b,c){try{console.log("Creating contact request for doctorId:",a);let d=await (0,e.createClient)();console.log("Fetching doctor info...");let{data:g,error:h}=await d.from("doctors").select("name, email, clinic_name, phone, quota_used, monthly_quota").eq("id",a).single();if(console.log("Doctor fetch result:",{doctor:g,doctorError:h}),h||!g)return console.error("Doctor not found:",{doctorId:a,doctorError:h}),{success:!1,error:`Doctor not found: ${h?.message||"No doctor data"}`};let i={doctor_id:a,doctor_name:g.name,doctor_email:g.email,clinic_name:g.clinic_name||"",phone_number:g.phone||"",current_quota_used:g.quota_used||0,monthly_quota:g.monthly_quota||0,request_type:"general_contact",message:b||"Contact request from dashboard",subject:c||"general"};console.log("Creating contact request with data:",i);let{data:j,error:k}=await d.from("contact_requests").insert(i).select("id").single();if(console.log("Insert result:",{data:j,error:k}),k)return console.error("Failed to create contact request:",k),{success:!1,error:`Database error: ${k.message}`};return(0,f.revalidatePath)("/admin/dashboard"),(0,f.revalidatePath)("/admin"),console.log("Contact request created successfully with ID:",j.id),{success:!0,data:j.id}}catch(a){return console.error("Unexpected error creating contact request:",a),{success:!1,error:`Unexpected error: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("*").order("created_at",{ascending:!1});if(c)return console.error("Database error fetching contact requests:",c),{success:!1,error:"Failed to fetch contact requests"};return{success:!0,data:b||[]}}catch(a){return console.error("Error fetching contact requests:",a),{success:!1,error:"An unexpected error occurred"}}}async function i(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("*").eq("status","pending").order("created_at",{ascending:!1});if(c)return{success:!1,error:"Failed to fetch pending contact requests"};return{success:!0,data:b||[]}}catch(a){return console.error("Error fetching pending contact requests:",a),{success:!1,error:"An unexpected error occurred"}}}async function j(a,b){try{let c=await (0,e.createClient)(),{error:d}=await c.from("contact_requests").update({status:b}).eq("id",a);if(d)return{success:!1,error:"Failed to update contact request status"};return(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Error updating contact request status:",a),{success:!1,error:"An unexpected error occurred"}}}async function k(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("status");if(c)return{success:!1,error:"Failed to fetch contact requests count"};let d={total:b?.length||0,pending:b?.filter(a=>"pending"===a.status).length||0,contacted:b?.filter(a=>"contacted"===a.status).length||0,resolved:b?.filter(a=>"resolved"===a.status).length||0};return{success:!0,data:d}}catch(a){return console.error("Error fetching contact requests count:",a),{success:!1,error:"An unexpected error occurred"}}}async function l(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.from("contact_requests").select("id").eq("doctor_id",a).eq("status","pending").single();if(d&&"PGRST116"!==d.code)return{success:!1,error:"Failed to check contact request status"};return{success:!0,data:!!c}}catch(a){return console.error("Error checking contact request status:",a),{success:!1,error:"An unexpected error occurred"}}}(0,a.i(377991).ensureServerEntryExports)([g,h,i,j,k,l]),(0,d.registerServerReference)(g,"70fff2cc329b28db6323e452c9272d2de14164c462",null),(0,d.registerServerReference)(h,"0009c0c46815af213900a610cc4a7e2d00a036b8b7",null),(0,d.registerServerReference)(i,"00e1bc255712e823e34fa44afca19fb096ce32734c",null),(0,d.registerServerReference)(j,"60bd2570fa48d9714b2922cc0537d755b4c2180d2e",null),(0,d.registerServerReference)(k,"00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9",null),(0,d.registerServerReference)(l,"405d7138432cf8f813249d6a1a52fcfca61c62a7ed",null)},378044:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(230122),a.i(680493)},396621:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(230122),a.i(680493),a.i(378044)},330401:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"008a705899f288d02ab102cb7194752ef57be976ca":()=>d.logout,"70fff2cc329b28db6323e452c9272d2de14164c462":()=>e.createContactRequest});var d=a.i(230122),e=a.i(680493);a.i(378044)},205244:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"008a705899f288d02ab102cb7194752ef57be976ca":()=>d["008a705899f288d02ab102cb7194752ef57be976ca"],"70fff2cc329b28db6323e452c9272d2de14164c462":()=>d["70fff2cc329b28db6323e452c9272d2de14164c462"]}),a.i(396621);var d=a.i(330401)},973770:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({TemplatesInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call TemplatesInterface() from the server but TemplatesInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/templates/templates-interface.tsx <module evaluation>","TemplatesInterface")}},71604:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({TemplatesInterface:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call TemplatesInterface() from the server but TemplatesInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/templates/templates-interface.tsx","TemplatesInterface")}},146475:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(973770);var d=a.i(71604);a.n(d)},75956:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>g,metadata:()=>b});var d=a.i(129629),e=a.i(76803),f=a.i(146475);let b={title:"Templates - Celer AI",description:"Create and manage custom consultation templates"};async function g(){let a=await (0,e.verifySession)(),b=await (0,e.getUser)();return(0,d.jsx)(f.TemplatesInterface,{user:b,doctorId:a.userId})}}},686230:a=>{var{g:b,__dirname:c}=a;a.n(a.i(75956))},986260:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(686230),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["templates",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/templates/page.tsx"]}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/templates/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/templates/page",pathname:"/templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},430422:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(686230),a.i(660874),a.i(715847),a.i(781045),a.i(986260)},39845:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(986260)},703530:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(430422);var d=a.i(39845)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__65230092._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__4aa02480._.js.map