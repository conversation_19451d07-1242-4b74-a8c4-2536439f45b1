{"version": 3, "sources": ["turbopack:///[project]/node_modules/lucide-react/src/icons/mic.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/file-check.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/chevron-down.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/timer.ts", "turbopack:///[project]/node_modules/lucide-react/src/icons/headphones.ts", "turbopack:///[project]/src/app/page.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z', key: '131961' }],\n  ['path', { d: 'M19 10v2a7 7 0 0 1-14 0v-2', key: '1vc78b' }],\n  ['line', { x1: '12', x2: '12', y1: '19', y2: '22', key: 'x3vr5v' }],\n];\n\n/**\n * @component @name Mic\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMmEzIDMgMCAwIDAtMyAzdjdhMyAzIDAgMCAwIDYgMFY1YTMgMyAwIDAgMC0zLTNaIiAvPgogIDxwYXRoIGQ9Ik0xOSAxMHYyYTcgNyAwIDAgMS0xNCAwdi0yIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTkiIHkyPSIyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mic\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mic = createLucideIcon('mic', __iconNode);\n\nexport default Mic;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'm9 15 2 2 4-4', key: '1grp1n' }],\n];\n\n/**\n * @component @name FileCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Im05IDE1IDIgMiA0LTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileCheck = createLucideIcon('file-check', __iconNode);\n\nexport default FileCheck;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '10', x2: '14', y1: '2', y2: '2', key: '14vaq8' }],\n  ['line', { x1: '12', x2: '15', y1: '14', y2: '11', key: '17fdiu' }],\n  ['circle', { cx: '12', cy: '14', r: '8', key: '1e1u0o' }],\n];\n\n/**\n * @component @name Timer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTAiIHgyPSIxNCIgeTE9IjIiIHkyPSIyIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjE1IiB5MT0iMTQiIHkyPSIxMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE0IiByPSI4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/timer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Timer = createLucideIcon('timer', __iconNode);\n\nexport default Timer;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3',\n      key: '1xhozi',\n    },\n  ],\n];\n\n/**\n * @component @name Headphones\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxNGgzYTIgMiAwIDAgMSAyIDJ2M2EyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtN2E5IDkgMCAwIDEgMTggMHY3YTIgMiAwIDAgMS0yIDJoLTFhMiAyIDAgMCAxLTItMnYtM2EyIDIgMCAwIDEgMi0yaDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/headphones\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Headphones = createLucideIcon('headphones', __iconNode);\n\nexport default Headphones;\n", "import Link from 'next/link'\nimport Image from 'next/image'\nimport { <PERSON><PERSON>, ArrowR<PERSON>, Clock, Sparkles, Wand2, FileCheck, Zap, ChevronDown, Timer, Headphones } from 'lucide-react'\nimport { checkSession } from '@/lib/auth/dal'\nimport { redirect } from 'next/navigation'\n\nexport default async function Home() {\n  // Check if user is already logged in and redirect to dashboard\n  const session = await checkSession()\n  if (session) {\n    redirect('/dashboard')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden\">\n      {/* Floating Navigation */}\n      <nav className=\"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20\">\n        <div className=\"flex items-center space-x-8\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"relative w-8 h-8\">\n              <Image\n                src=\"/celer-ai-logo.svg\"\n                alt=\"Celer AI\"\n                width={32}\n                height={32}\n                className=\"rounded-lg\"\n              />\n            </div>\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold\">\n              Celer AI\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <Link\n              href=\"/blog\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Blog\n            </Link>\n            <Link\n              href=\"/guide\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Guides\n            </Link>\n            <Link\n              href=\"/login\"\n              className=\"text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors\"\n            >\n              Sign In\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200\"\n            >\n              Start Free\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <main className=\"relative\">\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse\"></div>\n          <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-1000\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6 pt-32 pb-16\">\n          <div className=\"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]\">\n            \n            {/* Left Column */}\n            <div className=\"space-y-8\">\n              <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2\">\n                <Sparkles className=\"w-4 h-4 text-indigo-600 animate-pulse\" />\n                <span className=\"text-indigo-700 text-sm font-medium\">AI Magic for Medical Docs</span>\n                <Wand2 className=\"w-4 h-4 text-purple-600\" />\n              </div>\n\n              <div className=\"space-y-4\">\n                <h1 className=\"text-6xl md:text-7xl font-black text-slate-900 leading-none\">\n                  Speak.\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse\">\n                    Generate.\n                  </span>\n                  <span className=\"block text-slate-700\">\n                    Done.\n                  </span>\n                </h1>\n                \n                <div className=\"flex items-center space-x-3 text-lg text-slate-600\">\n                  <Timer className=\"w-5 h-5 text-emerald-500\" />\n                  <span>Medical reports in 30 seconds</span>\n                  <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-ping\"></div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-r from-slate-50 to-indigo-50 rounded-2xl p-6 border border-slate-200\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\">\n                      <Mic className=\"w-6 h-6 text-white animate-pulse\" />\n                    </div>\n                    <div>\n                      <p className=\"font-semibold text-slate-800\">Try saying:</p>\n                      <p className=\"text-slate-600 italic\">&ldquo;Patient has fever, cough, prescribed antibiotics...&rdquo;</p>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-slate-400\" />\n                </div>\n              </div>\n\n              <div>\n                <Link\n                  href=\"/signup\"\n                  className=\"group relative bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\"\n                >\n                  <Zap className=\"w-5 h-5\" />\n                  <span>Start Creating Magic</span>\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-6 pt-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-emerald-600\">30s</div>\n                  <div className=\"text-sm text-slate-600\">Average Time</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">90%</div>\n                  <div className=\"text-sm text-slate-600\">Accuracy</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column - Video */}\n            <div className=\"relative -mt-35\">\n              <div className=\"relative\">\n                <div className=\"absolute -inset-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl blur-lg opacity-30 animate-pulse\"></div>\n                <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden\">\n                  <div style={{position: 'relative', paddingBottom: '52.708333333333336%', height: 0}}>\n                    <iframe\n                      src=\"https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7\"\n                      className=\"absolute top-0 left-0 w-full h-full border-0\"\n                      allowFullScreen\n                      title=\"Celer AI Demo\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"absolute -top-6 -left-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <Headphones className=\"w-4 h-4 text-indigo-600\" />\n                  <span className=\"text-sm font-medium text-slate-700\">Voice Input</span>\n                </div>\n              </div>\n\n              <div className=\"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-3 border border-slate-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <FileCheck className=\"w-4 h-4 text-emerald-600\" />\n                  <span className=\"text-sm font-medium text-slate-700\">Perfect Report</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <ChevronDown className=\"w-6 h-6 text-slate-400\" />\n        </div>\n      </main>\n\n      {/* Time Section - Side Layout Like Hero */}\n      <section className=\"py-32 relative overflow-hidden\">\n        {/* Same background style as hero but darker */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-slate-900\"></div>\n        \n        {/* Floating elements like hero */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-emerald-200/20 to-cyan-200/20 rounded-full blur-xl animate-pulse\"></div>\n          <div className=\"absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full blur-xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-cyan-200/10 to-blue-200/10 rounded-full blur-xl animate-pulse delay-2000\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6\">\n          <div className=\"grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]\">\n            \n            {/* Left Column - Problem & Solution */}\n            <div className=\"space-y-8 text-white\">\n              {/* Time Badge - Same style as hero */}\n              <div className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-50/10 to-cyan-50/10 border border-emerald-200/20 rounded-full px-4 py-2\">\n                <Clock className=\"w-4 h-4 text-emerald-400 animate-spin\" />\n                <span className=\"text-emerald-300 text-sm font-medium\">Reclaim Your Time</span>\n                <Timer className=\"w-4 h-4 text-cyan-400\" />\n              </div>\n\n              {/* Headline - Same style as hero */}\n              <div className=\"space-y-4\">\n                <h2 className=\"text-6xl md:text-7xl font-black text-white leading-none\">\n                  Get Your\n                  <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 via-cyan-400 to-purple-400 animate-pulse\">\n                    Life Back\n                  </span>\n                </h2>\n                \n                <div className=\"flex items-center space-x-3 text-lg text-slate-300\">\n                  <Timer className=\"w-5 h-5 text-emerald-400\" />\n                  <span>Save 2+ hours daily with voice documentation</span>\n                  <div className=\"w-2 h-2 bg-emerald-400 rounded-full animate-ping\"></div>\n                </div>\n              </div>\n\n              {/* Time Comparison - Same style as hero demo box */}\n              <div className=\"bg-gradient-to-r from-slate-50/5 to-emerald-50/5 rounded-2xl p-6 border border-white/10\">\n                <div className=\"grid grid-cols-2 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-red-400 text-4xl font-black mb-2\">15-20</div>\n                    <div className=\"text-slate-300 text-sm\">minutes per patient</div>\n                    <div className=\"text-red-400 text-xs mt-2\">😰 Stress & paperwork</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-emerald-400 text-4xl font-black mb-2\">30</div>\n                    <div className=\"text-slate-300 text-sm\">seconds total</div>\n                    <div className=\"text-emerald-400 text-xs mt-2\">😌 Perfect & effortless</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* CTA - Same style as hero */}\n              <div>\n                <Link\n                  href=\"/signup\"\n                  className=\"group relative bg-gradient-to-r from-emerald-600 to-cyan-600 hover:from-emerald-700 hover:to-cyan-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\"\n                >\n                  <Timer className=\"w-5 h-5\" />\n                  <span>Start Saving Time Now</span>\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </div>\n\n              {/* Trust indicators - Same style as hero */}\n              <div className=\"grid grid-cols-2 gap-6 pt-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-emerald-400\">2+ hrs</div>\n                  <div className=\"text-sm text-slate-300\">Daily Savings</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-cyan-400\">90%</div>\n                  <div className=\"text-sm text-slate-300\">Accuracy</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column - Life Activities */}\n            <div className=\"relative\">\n              {/* Container with same style as video frame */}\n              <div className=\"relative\">\n                <div className=\"absolute -inset-4 bg-gradient-to-r from-emerald-500 via-cyan-500 to-purple-500 rounded-3xl blur-lg opacity-30 animate-pulse\"></div>\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-white/20\">\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-white mb-8 text-center\">\n                      What will you do with 2 extra hours daily?\n                    </h3>\n                    \n                    <div className=\"grid grid-cols-2 gap-6\">\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">👨‍👩‍👧‍👦</div>\n                        <div className=\"text-white font-medium\">Family Time</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Quality moments</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">🏃‍♂️</div>\n                        <div className=\"text-white font-medium\">Exercise</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Stay healthy</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">📚</div>\n                        <div className=\"text-white font-medium\">Learning</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Grow skills</div>\n                      </div>\n                      <div className=\"bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 text-center hover:bg-white/10 transition-colors\">\n                        <div className=\"text-4xl mb-3\">😴</div>\n                        <div className=\"text-white font-medium\">Rest</div>\n                        <div className=\"text-slate-300 text-sm mt-1\">Recharge</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Floating elements like hero */}\n              <div className=\"absolute -top-6 -left-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20\">\n                <div className=\"flex items-center space-x-2\">\n                  <Clock className=\"w-4 h-4 text-emerald-400\" />\n                  <span className=\"text-sm font-medium text-white\">2+ Hours Saved</span>\n                </div>\n              </div>\n\n              <div className=\"absolute -bottom-6 -right-6 bg-white/10 backdrop-blur-xl rounded-xl shadow-lg p-3 border border-white/20\">\n                <div className=\"flex items-center space-x-2\">\n                  <Timer className=\"w-4 h-4 text-cyan-400\" />\n                  <span className=\"text-sm font-medium text-white\">Every Day</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Minimal Footer */}\n      <footer className=\"bg-slate-50 border-t border-slate-200\">\n        <div className=\"max-w-7xl mx-auto px-6 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-3 mb-4 md:mb-0\">\n              <div className=\"relative w-8 h-8\">\n                <Image\n                  src=\"/celer-ai-logo.svg\"\n                  alt=\"Celer AI\"\n                  width={32}\n                  height={32}\n                  className=\"rounded-lg\"\n                />\n              </div>\n              <span className=\"font-semibold text-slate-800\">Celer AI</span>\n              <span className=\"text-slate-500 text-sm\">• Built for Indian doctors</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-6 text-sm text-slate-500\">\n              <Link href=\"/privacy\" className=\"hover:text-slate-700 transition-colors\">Privacy</Link>\n              <Link href=\"/terms\" className=\"hover:text-slate-700 transition-colors\">Terms</Link>\n              <a href=\"mailto:<EMAIL>\" className=\"hover:text-slate-700 transition-colors\">Support</a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": "wHAGO,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AADyB,CAAA,AACzB,CADyB,AACzB,AAAE,EAAG,CAAwD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACrF,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC3D,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AAAR,CAAU,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAI,CAAM,CAAA,CAAA,CAAA,CAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACpE,CAaM,EAAM,CAAN,AAAM,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,6LCjBvC,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AADyB,CACzB,AADyB,CACzB,AAAE,AADuB,EACpB,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC3F,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACxD,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAChD,CAaM,EAAY,CAAA,EAAA,EAAA,CAAA,CAAZ,AAAY,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mMCjB9C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAzB,AAAiC,CAAV,AAAU,CAAV,AAAY,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAa7E,EAAc,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAd,AAAc,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mMCbxD,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AADiC,CACjC,AAAQ,AADyB,CACvB,AADuB,CACvB,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAI,CAAM,CAAA,CAAA,CAAA,CAAA,EAAA,CAAI,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAChE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAE,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAI,CAAM,CAAA,CAAA,CAAA,CAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAClE,CAAC,QAAU,CAAA,CAAA,AAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAI,CAAA,CAAA,IAAA,CAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC1D,CAaM,EAAQ,CAAA,EAAR,AAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+LCjB3C,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAFgC,AAEhC,CAFgC,AAGhC,CAHgC,AAI9B,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACF,CAaM,EAAa,CAAA,EAAA,EAAA,CAAA,CAAA,CAAb,AAAa,CAAA,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAA,CAAiB,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,kLCxB5D,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,QAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QAEe,eAAe,IAO5B,OALgB,AACZ,MADkB,CAAA,EAAA,AACT,EADS,YAAA,AAAW,KAE/B,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,IAFW,UAMpB,CAAA,EAAA,EAAA,IAAA,EAAC,EAJD,IAIC,CAAI,UAAU,iGAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qJACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,qBACJ,EAFD,EAEK,WACJ,MAAO,GACP,OAAQ,GACR,UAAU,iBAGd,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uIAA8H,gBAIhJ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,QACL,UAAU,KAFX,gFAGA,SAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,SACL,UAAU,IAFX,iFAGA,WAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,SACL,UAAU,IAFX,iFAGA,YAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,UACL,UAAU,GAFX,6MAGA,uBAQP,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,qBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+HACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0IAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEAGb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4IACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,yBACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,+CAAsC,8BACtD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,WAGH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,wEAA8D,SAE1E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,yHAAgH,cAGhI,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gCAAuB,aAKzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,SACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,kCACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2DAInB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+FACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6GACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,sBAAd,iBAEH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,wCAA+B,gBAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gEAGzC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,eAArB,iBAIL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,UACL,UAAU,GAFX,oRAIC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,YACf,CAAA,EAAA,EAAA,GAAA,EAAC,AADA,OACA,UAAK,yBACN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,eAArB,iDAIL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+CAAsC,QACrD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,oBAE1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CAAqC,QACpD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,sBAM9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+HACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,MAAO,CAAC,SAAU,WAAY,cAAe,sBAAuB,OAAQ,CAAC,WAChF,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,IAAI,uGACJ,UAAU,+CACV,eAAe,CAAA,CAAA,EACf,MAAM,yBAMd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6FACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,eAArB,aACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8CAAqC,qBAIzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAApB,aACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8CAAqC,gCAO/D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gFACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,YAAtB,oBAKL,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,2CAEjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mFAGf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8HACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yIACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6IAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEAGb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qJACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,sBACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gDAAuC,sBACvD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,SAIH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,oEAA0D,WAEtE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,0HAAiH,iBAKnI,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,SACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,iDACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2DAKnB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDAAwC,UACvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,wBACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCAA4B,6BAE7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAA4C,OAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,kBACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yCAAgC,oCAMrD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,UACL,UAAU,GAFX,kRAIC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GAAA,AADC,EACA,OAAA,UAAK,0BACN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,eAArB,iDAKL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+CAAsC,WACrD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,qBAE1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,QAClD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,sBAM9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gIACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+GACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0DAAiD,+CAI/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+HACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,gBAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,gBACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,uBAE/C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+HACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,UAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,aACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,oBAE/C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+HACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,aACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,mBAE/C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+HACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,SACxC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,2BAQvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gHACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,SACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,0CAAiC,wBAIrD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oHACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,MACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,0CAAiC,8BAS7D,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,iDAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,qBACJ,EAFD,EAEK,WACJ,MAAO,GACP,OAAQ,GACR,UAAU,iBAGd,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wCAA+B,aAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,kCAAyB,kCAG3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,EAA/B,gDAAwE,YACzE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,SAAS,UAAU,IAA7B,8CAAsE,UACvE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,6BAA6B,UAAU,kDAAyC,wBAOtG", "ignoreList": [0, 1, 2, 3, 4]}