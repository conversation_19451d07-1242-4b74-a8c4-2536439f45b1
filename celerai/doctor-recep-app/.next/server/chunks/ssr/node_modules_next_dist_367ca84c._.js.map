{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/lib/scheduler.ts", "turbopack:///[project]/node_modules/next/src/client/components/hooks-server-context.ts", "turbopack:///[project]/node_modules/next/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/node_modules/next/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/node_modules/next/src/lib/metadata/metadata-constants.tsx", "turbopack:///[project]/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "nextTick", "setImmediate", "r", "DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err", "StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "code", "error", "isHangingPromiseRejectionError", "makeHangingPromise", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "NODE_ENV", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "createPrerenderInterruptedError", "controller", "abort", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console"], "mappings": "wIA4CgBA,cAAc,CAAA,kBAAdA,GAbHC,iBAAiB,CAAA,kBAAjBA,GAtBAC,kBAAkB,CAAA,kBAAlBA,GAgDGC,6BAA6B,CAAA,kBAA7BA,uEAhDT,IAAMD,EAAqB,AAAWE,IAO3CC,QAAQC,OAAO,GAAGC,IAAI,CAAC,KAInBC,QAAQG,QAAQ,CAACP,EAErB,EACF,EAQaH,EAA+BG,AAAX,IAI7BQ,aAAaR,EAEjB,EAOO,SAASJ,IACd,OAAO,IAAIK,QAAc,AAACC,GAAYL,EAAkBK,GAC1D,CAWO,SAASH,IAIZ,OAAO,IAAIE,QAAQ,AAACQ,GAAMD,aAAaC,GAE3C,4HC7DaC,kBAAkB,CAAA,kBAAlBA,GAQGC,oBAAoB,CAAA,kBAApBA,uEAVhB,IAAMC,EAAqB,sBAEpB,OAAMF,UAA2BG,MAGtCC,YAA4BC,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5BC,MAAAA,CAAoCJ,CAIpC,CACF,CAEO,SAASD,EAAqBM,CAAY,QAC/C,AACiB,UAAf,OAAOA,GACPA,AAAQ,QACR,CAAE,CAAA,WAAYA,GACQ,AADN,UAEhB,AADA,OAAOA,EAAID,MAAM,EAKZC,EAAID,MAAM,GAAKJ,CACxB,kVCnBaM,qBAAqB,CAAA,kBAArBA,GAIGC,uBAAuB,CAAA,kBAAvBA,uEANhB,IAAMC,EAA0B,yBAEzB,OAAMF,UAA8BL,wBAApC,KAAA,IAAA,GAAA,IAAA,CACWQ,IAAAA,CAAOD,EACzB,CAEO,SAASD,EACdG,CAAc,QAEd,AAAqB,UAAjB,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAAMD,AAJsD,GAAI,CAItD,GAAKD,CACxB,kVCdgBG,8BAA8B,CAAA,kBAA9BA,GAgCAC,kBAAkB,CAAA,kBAAlBA,uEAhCT,SAASD,EACdN,CAAY,QAEZ,AAAmB,UAAf,OAAOA,GAA4B,AAARA,QAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAID,CAJwD,KAIlD,GAAKS,CACxB,CAEA,IAAMA,EAA4B,2BAElC,OAAMC,UAAqCb,MAGzCC,YAA4Ba,CAAkB,CAAE,CAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,qJAAqJ,CAAC,EAAA,IAAA,CAFnRA,UAAAA,CAAAA,EAAAA,IAAAA,CAFZX,MAAAA,CAASS,CAMzB,CACF,CAGA,IAAMG,EAAyB,IAAIC,QAS5B,SAASL,EACdM,CAAmB,CACnBH,CAAkB,EAElB,GAAIG,EAAOC,OAAO,CAChB,CADkB,MACX9B,QAAQ+B,MAAM,CAAC,IAAIN,EAA6BC,GAClD,EACL,IAAMM,EAAiB,IAAIhC,QAAW,CAACiC,EAAGF,KACxC,IAAMG,EAAiBH,EAAOI,IAAI,CAChC,KACA,IAAIV,EAA6BC,IAE/BU,EAAmBT,EAAuBU,GAAG,CAACR,GAClD,GAAIO,EACFA,EAAiBE,IAAI,CAACJ,OACjB,CACL,CAHoB,GAGdK,EAAY,CAACL,EAAe,CAClCP,EAAuBa,GAAG,CAACX,EAAQU,GACnCV,EAAOY,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUI,MAAM,CAAED,IAAK,AACzCH,CAAS,CAACG,EAAE,EAEhB,EACA,CAAEE,MAAM,CAAK,EAEjB,CACF,GAKA,OADAZ,EAAea,KAAK,CAACC,GACdd,CACT,CACF,CAEA,SAASc,IAAgB,4HCrEZC,sBAAsB,CAAA,kBAAtBA,GAEAC,oBAAoB,CAAA,kBAApBA,GADAC,sBAAsB,CAAA,kBAAtBA,uEADN,IAAMF,EAAyB,6BACzBE,EAAyB,6BACzBD,EAAuB,gFCkBnC,uEAoVeE,QAAQ,CAAA,kBAARA,GA3CAC,2CAA2C,CAAA,kBAA3CA,GAlCAC,kCAAkC,CAAA,kBAAlCA,GAuKAC,mBAAmB,CAAA,kBAAnBA,GA4GAC,qBAAqB,CAAA,kBAArBA,GAtGAC,oBAAoB,CAAA,kBAApBA,GAhXAC,0BAA0B,CAAA,kBAA1BA,GAWAC,4BAA4B,CAAA,kBAA5BA,GAmbAC,6BAA6B,CAAA,kBAA7BA,GAjBAC,0BAA0B,CAAA,kBAA1BA,GAlDAC,wBAAwB,CAAA,kBAAxBA,GAtWAC,qBAAqB,CAAA,kBAArBA,GAgSAC,iBAAiB,CAAA,kBAAjBA,GAwCAC,2BAA2B,CAAA,kBAA3BA,GA3TAC,yBAAyB,CAAA,kBAAzBA,GAuPAC,oBAAoB,CAAA,kBAApBA,GAgSAC,wBAAwB,CAAA,kBAAxBA,GAvcAC,gCAAgC,CAAA,kBAAhCA,GA6ZAC,yBAAyB,CAAA,kBAAzBA,GApYAC,+BAA+B,CAAA,kBAA/BA,GAzCAC,0BAA0B,CAAA,kBAA1BA,GAiHAC,qCAAqC,CAAA,kBAArCA,GAmDHC,sCAAsC,CAAA,kBAAtCA,GA+NGC,qBAAqB,CAAA,kBAArBA,kFA9hBE,CAAA,CAAA,IAAA,qCAEiB,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,OACE,CAAA,CAAA,IAAA,QAK5B,CAAA,CAAA,IAAA,QAC4B,CAAA,CAAA,IAAA,IAE7BC,EAAiD,YAAnC,OAAOC,EAAAA,OAAK,CAACC,iBAAiB,CA2C3C,SAASpB,EACdqB,CAA2C,EAE3C,MAAO,wBACLA,EACAC,gBAAiB,EAAE,CACnBC,2BAAuBC,EACvBC,0BAA2B,IAC7B,CACF,CAEO,SAASxB,IACd,MAAO,CACLyB,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASzB,EACd0B,CAAmC,MAE5BA,EAAP,OAAA,AAAuC,OAAhCA,EAAAA,EAAcT,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCS,EAAkC7D,UAAU,AACrD,CASO,SAASsC,EACdwB,CAAgB,CAChBC,CAAuE,CACvE/D,CAAkB,EAElB,KAAI+D,GAEuB,UAAvBA,EAAcC,IAAI,EACK,kBACvB,CADAD,EAAcC,IAAI,AAHlBD,GAAe,CAefD,EAAMG,YAAY,GAAIH,EAAMI,WAAW,EAAE,AAE7C,GAAIJ,EAAMK,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAI5E,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEuE,EAAMM,KAAK,CAAC,8EAA8E,EAAEpE,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAI+D,GACF,GAA2B,SADV,QAC2B,CAAxCA,EAAcC,IAAI,CACpBzB,EACEuB,EAAMM,KAAK,CACXpE,EACA+D,EAAcM,eAAe,OAE1B,GAA2B,qBAAvBN,EAAcC,IAAI,CAAyB,CACpDD,EAAcO,UAAU,CAAG,EAG3B,IAAMhF,EAAM,OAAA,cAEX,CAFW,IAAIP,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE+E,EAAMM,KAAK,CAAC,iDAAiD,EAAEpE,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHA8D,EAAMS,uBAAuB,CAAGvE,EAChC8D,EAAMU,iBAAiB,CAAGlF,EAAImF,KAAK,CAE7BnF,CACR,CAMA,EAEJ,CAUO,GAlBI,IACLb,EAiBUmE,EACdkB,CAAgB,CAChB9D,CAAkB,CAnBNtB,CAqBZ,EArBe,CAACgG,CAqBVC,EAAiBC,EAAAA,GArBC,KAAK,UAGzB,EAkBuC,CAACC,IApBxCd,IAoBgD,GAC/CY,GAA0C,OApB3CZ,UAoB4D,CAAzCY,EAAeX,CApBpBA,GAoBwB,CApBpB,CAsBtBzB,EAAqBuB,EAAMM,AAtBA,KAsBK,CAAEpE,EAAY2E,EAAeN,eAAe,CAC9E,CAQO,SAAS5B,EACdzC,CAAkB,CAClB8D,CAAgB,CAChBa,CAAoC,EAGpC,IAAMrF,EAAM,OAAA,cAEX,CAFW,IAAIP,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE+E,EAAMM,KAAK,CAAC,mDAAmD,EAAEpE,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAOA,OALA2E,EAAeL,UAAU,CAAG,EAE5BR,EAAMS,uBAAuB,CAAGvE,EAChC8D,EAAMU,iBAAiB,CAAGlF,EAAImF,KAAK,CAE7BnF,CACR,CASO,SAASqD,EACdmC,CAAiB,CACjBf,CAAmC,EAE/BA,GAEuB,UAAvBA,EAFe,AAEDC,IAAI,EACK,kBACvB,CADAD,EAAcC,IAAI,GAQlBD,AAAuB,gBAATC,IAAI,EACK,qBAAvBD,EAAcC,IAAI,AAAK,GACvB,CACAD,EAAcO,UAAU,EAAG,CASjC,CAKA,SAASS,EACPX,CAAa,CACbpE,CAAkB,CAClB2E,CAAoC,EAIpC,IAAMhF,EAAQsF,EAFC,CAAC,MAAM,EAAEb,EAAM,mBAEgBY,8CAFiD,EAAEhF,EAAW,CAAC,CAAC,EAI9G2E,EAAeO,UAAU,CAACC,KAAK,CAACxF,GAEhC,IAAM0E,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBjB,YADG,GACY,CAACxC,IAAI,CAAC,CAGnC6D,MAAOJ,EAAgBlB,sBAAsB,CACzC,AAAIjE,QAAQuF,KAAK,MACjBnB,aACJtD,CACF,EAEJ,CAEO,SAAS0B,EACd0C,CAAa,CACbpE,CAAkB,CAClBoF,CAAqB,CACrBT,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBd,KADD,oBAC0B,GAC3Cc,EAAgBhB,qBAAqB,CAAGrD,EACxCqE,EAAgBd,yBAAyB,CAAG6B,GAGhDL,EAAoCX,EAAOpE,EAAY2E,EACzD,CAEO,SAAS9B,EACdwC,CAA0B,EAI1BA,EAAaC,cAAc,EAAG,CAChC,CAYO,SAAS7D,EACd2C,CAAa,CACbpE,CAAkB,CAClBoF,CAAqB,CACrBT,CAAoC,EAGpC,IAAgC,IADRA,AACpBY,EADmCL,UAAU,CAAC/E,MAAM,CACpCC,OAAO,CAAY,CAMrC,IAAMiE,EAAkBM,EAAeN,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBd,KADD,oBAC0B,GAC3Cc,EAAgBhB,qBAAqB,CAAGrD,EACxCqE,EAAgBd,yBAAyB,CAAG6B,GACV,IAA9BT,EAAea,AAAqB,UAAX,GAG3BnB,EAAgBoB,iBAAiB,CAAG,EAAA,GAI1CV,EAAoCX,EAAOpE,EAAY2E,EACzD,CACA,MAAMM,EACJ,CAAC,MAAM,EAAEb,EAAM,iEAAiE,EAAEpE,EAAW,CAAC,CAAC,CAEnG,CAGO,IAAM8C,EACXD,EASK,SAASrB,EAAS,QAAEwD,CAAM,OAAEZ,CAAK,CAAiB,EACvD,IAAMO,EAAiBC,EAAAA,oBAAoB,CAACC,QAAQ,GAKpDtC,EAAqB6B,EAAOY,EAH1BL,GAAkBA,AAAwB,GAGRN,iBAHDL,IAAI,CACjCW,EAAeN,eAAe,CAC9B,KAER,CAEO,SAAS9B,EACd6B,CAAa,CACbpE,CAAkB,CAClBqE,CAA4C,EAE5CqB,IACIrB,GACFA,EAAgBjB,YADG,GACY,CAACxC,IAAI,CAAC,CAGnC6D,MAAOJ,EAAgBlB,sBAAsB,CACzC,AAAIjE,QAAQuF,KAAK,MACjBnB,aACJtD,CACF,GAGFiD,EAAAA,OAAK,CAACC,iBAAiB,CAACyC,EAAqBvB,EAAOpE,GACtD,CAEA,SAAS2F,EAAqBvB,CAAa,CAAEpE,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAEoE,EAAM,iEAAiE,EAAEpE,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKWoC,EAAkB9C,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACA,AAAgC,UAChC,OADQA,EAAYsG,OAAO,EAEpBC,EAAyBvG,EAAYsG,AAXoC,CAAC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASC,EAAwBb,CAAc,EAC7C,OACEA,EAAOc,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnFd,EAAOc,QAAQ,CACb,gEAGN,CAEA,GAAID,CAAgE,MAAxCF,CAA+C,CAA1B,MAAO,QACtD,MAAM,OAAA,cAEL,CAFK,AAAIzG,MACR,0FADI,oBAAA,OAAA,mBAAA,eAAA,EAEN,GAGF,IAAM6G,EAA6B,6BAEnC,SAASd,EAAgCW,CAAe,EACtD,IAAMjG,EAAQ,OAAA,cAAkB,CAAlB,AAAIT,MAAM0G,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADEjG,EAAcN,MAAM,CAAG0G,EAClBpG,CACT,CAMO,SAAS0C,EACd1C,CAAc,EAEd,MACE,AAAiB,iBAAVA,GACG,OAAVA,GACCA,EAAcN,MAAM,GAAK0G,GAC1B,SAAUpG,GACV,YAAaA,GACbA,aAAiBT,KAErB,CAEO,SAASyC,EACdyB,CAAqC,EAErC,OAAOA,EAAgBnC,MAAM,CAAG,CAClC,CAEO,SAASY,EACdmE,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAc5C,eAAe,CAACxC,IAAI,IAAIqF,EAAc7C,eAAe,EAC5D4C,EAAc5C,eAAe,AACtC,CAEO,SAASlB,EACdkB,CAAqC,EAErC,OAAOA,EACJ8C,MAAM,CACJC,AAAD,GAC0B,AAAxB,iBAAOA,EAAO1B,KAAK,EAAiB0B,EAAO1B,KAAK,CAACxD,MAAM,CAAG,GAE7DmF,GAAG,CAAC,CAAC,YAAEpG,CAAU,OAAEyE,CAAK,CAAE,IACzBA,EAAQA,EACL4B,KAAK,CAAC,MACP,AAGCC,KAAK,CAAC,GACNJ,MAAM,CAAC,AAACK,KAEHA,EAAKT,QAAQ,CAAC,uBAAuB,AAKrCS,EAAKT,QAAQ,CAAC,MAXoD,aAWjC,AAKjCS,EAAKT,QAAQ,CAAC,YAAY,CAM/BU,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAExG,EAAW;AAAG,EAAEyE,EAAAA,CAAO,EAEjE,CAEA,SAASiB,IACP,GAAI,CAAC1C,EACH,MAAM,KADU,EACV,cAEL,CAFK,AAAI9D,MACR,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEJ,CAMO,SAAS+C,EAA2B+C,CAAc,EACvDU,IACA,IAAMR,EAAa,IAAIuB,gBAEvB,GAAI,CACFxD,EAAAA,OAAK,CAACC,iBAAiB,CAAC8B,EAC1B,CAAE,MAAO0B,EAAY,CACnBxB,EAAWC,KAAK,CAACuB,EACnB,CACA,OAAOxB,EAAW/E,MACpB,AAD0B,CAQnB,SAAS6B,EACd+B,CAAmC,EAEnC,IAAMmB,EAAa,IAAIuB,gBAkBvB,OAhBI1C,EAAc4C,WAAW,CAI3B5C,CAJ6B,CAIf4C,WAAW,CAACC,UAAU,GAAGpI,IAAI,CAAC,KAC1C0G,EAAWC,KAAK,EAClB,GAOAhH,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC,IAAM+G,EAAWC,KAAK,IAGpCD,EAAW/E,MAAM,AAC1B,CAEO,SAASyB,EACd5B,CAAkB,CAClB2E,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBjB,YADG,GACY,CAACxC,IAAI,CAAC,CACnC6D,MAAOJ,EAAgBlB,sBAAsB,CACzC,AAAIjE,QAAQuF,KAAK,MACjBnB,aACJtD,CACF,EAEJ,CAEO,SAAS+C,EAAsB/C,CAAkB,EACtD,IAAM6G,EAAYC,EAAAA,gBAAgB,CAACjC,QAAQ,GAE3C,GACEgC,GACAA,EAAUE,kBAAkB,EAC5BF,EAAUG,mBAAmB,EAC7BH,EAAUG,mBAAmB,CAACC,IAAI,CAAG,EACrC,CAGA,IAAMlD,EAAgBa,EAAAA,oBAAoB,CAACC,QAAQ,GAC/Cd,IAEyB,WAFV,EAEuB,CAApCA,EAAcC,IAAI,CAIpBf,EAAAA,OAAK,CAACiE,GAAG,CAACrH,GAAAA,EAAAA,kBAAAA,AAAkB,EAACkE,EAAcoD,YAAY,CAAEnH,IACzB,iBAAiB,CAAxC+D,EAAcC,IAAI,CAE3BzB,EACEsE,EAAUzC,KAAK,CACfpE,EACA+D,EAAcM,eAAe,EAEtBN,AAAuB,oBAAoB,GAA7BC,IAAI,EAC3BvB,EAAiCzC,EAAY6G,EAAW9C,GAG9D,CACF,CAEA,IAAMqD,EAAmB,mCACnBC,EAAmB,AAAIC,OAC3B,CAAC,UAAU,EAAEjG,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCkG,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAE/F,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCiG,EAAiB,AAAIF,OAAO,CAAC,UAAU,EAAEhG,EAAAA,oBAAoB,CAAC,QAAQ,CAAC,EAEtE,SAASoB,EACd0B,CAAa,CACbqD,CAAsB,CACtBC,CAAyC,CACzC1B,CAAmC,CACnCC,CAAmC,EAEnC,IAAIuB,EAAeG,IAAI,CAACF,IAGjB,GAAIJ,EAAiBM,IAAI,CAACF,GAHQ,AAGS,CAChDC,EAAkBjE,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAI8D,EAAiBI,IAAI,CAACF,GAAiB,CAChDC,EAAkBhE,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAI0D,EAAiBO,IAAI,CAACF,GAAiB,CAChDC,EAAkBlE,mBAAmB,EAAG,EACxC,MACF,MAAO,GACLwC,EAAczC,yBAAyB,EACvC0C,EAAc1C,yBAAyB,CACvC,CACAmE,EAAkB/D,oBAAoB,EAAG,EACzC,MACF,KAAO,CAEL,IAAMhE,EAAQiI,AAMlB,SAASA,AACPhC,CAAe,CACf6B,CAAsB,EAEtB,IAAM9H,EAAQ,OAAA,GAVgCiG,WAUd,CAAlB,AAAI1G,MAAM0G,GAAV,oBAAA,OAAA,mBAAA,eAAA,EAAiB,GAE/B,OADAjG,EAAM8E,KAAK,CAAG,UAAYmB,EAAU6B,EAC7B9H,CACT,EAdoB,CAAC,OAAO,EAAEyE,EAAM,+UAA+U,CAAC,CAC3TqD,GACrDC,EAAkB9D,aAAa,CAAChD,IAAI,CAACjB,GACrC,MACF,EACF,CAWO,SAAS6C,EACd4B,CAAa,CACbsD,CAAyC,CACzC1B,CAAmC,CACnCC,CAAmC,MAE/B4B,EACAC,EACAC,EAeJ,GAdI/B,EAAczC,yBAAyB,EAAE,AAC3CsE,EAAY7B,EAAczC,yBAAyB,CACnDuE,EAAiB9B,EAAc3C,qBAAqB,CACpD0E,EAAiD,KAApC/B,EAAcP,iBAAiB,EACnCQ,EAAc1C,yBAAyB,EAAE,AAClDsE,EAAY5B,EAAc1C,yBAAyB,CACnDuE,EAAiB7B,EAAc5C,qBAAqB,CACpD0E,EAAa9B,AAAoC,OAAtBR,iBAAiB,GAE5CoC,EAAY,KACZC,OAAiBxE,EACjByE,GAAa,GAGXL,EAAkB/D,oBAAoB,EAAIkE,EAO5C,MANI,AAACE,GADkD,AAIrDC,QAAQrI,CAHO,IAGF,CAACkI,GAGV,IAAItI,EAAAA,qBAAqB,CAGjC,IAAMqE,EAAgB8D,EAAkB9D,aAAa,CACrD,GAAIA,EAAc3C,MAAM,CAAE,CACxB,IAAK,IAAID,EAAI,EAAGA,EAAI4C,EAAc3C,MAAM,CAAED,IAAK,AAC7CgH,QAAQrI,KAAK,CAACiE,CAAa,CAAC5C,EAAE,CAGhC,OAAM,IAAIzB,EAAAA,qBAAqB,AACjC,CAEA,GAAI,CAACmI,EAAkBlE,mBAAmB,EAAE,AAC1C,GAAIkE,EAAkBjE,kBAAkB,CAAE,CACxC,GAAIoE,EAEF,MADAG,GADa,KACLrI,KAAK,CAACkI,GACR,OAAA,cAEL,CAFK,IAAItI,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE6E,EAAM,oEAAoE,EAAE0D,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAIvI,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE6E,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAO,GAAIsD,EAAkBhE,kBAAkB,CAAE,CAC/C,GAAImE,EAEF,MADAG,GADa,KACLrI,KAAK,CAACkI,GACR,OAAA,cAEL,CAFK,IAAItI,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE6E,EAAM,oEAAoE,EAAE0D,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAIvI,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE6E,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GACF,CAEJ", "ignoreList": [0, 1, 2, 3, 4, 5]}