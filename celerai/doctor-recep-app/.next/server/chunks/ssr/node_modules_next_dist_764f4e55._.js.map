{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/router-reducer-types.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/app-router-headers.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/is-thenable.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/use-action-queue.ts", "turbopack:///[project]/node_modules/next/dist/src/client/app-call-server.ts", "turbopack:///[project]/node_modules/next/dist/src/client/app-find-source-map-url.ts", "turbopack:///[project]/node_modules/next/dist/src/client/flight-data-helpers.ts", "turbopack:///[project]/node_modules/next/dist/src/client/app-build-id.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/hash.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/set-cache-busting-search-param.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/fetch-server-response.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/unresolved-thenable.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/navigation-untracked.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-status-code.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-error.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/is-next-router-error.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/create-href-from-url.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/nav-failure-handler.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/error-boundary.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/match-segments.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/router/utils/handle-smooth-scroll.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/reducers/get-segment-value.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/segment.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/redirect.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/not-found.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/forbidden.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/unauthorized.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.browser.ts", "turbopack:///[project]/node_modules/next/dist/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/node_modules/next/dist/src/server/lib/router-utils/is-postpone.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/hooks-server-context.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/node_modules/next/dist/src/lib/metadata/metadata-constants.tsx", "turbopack:///[project]/node_modules/next/dist/src/lib/scheduler.ts", "turbopack:///[project]/node_modules/next/dist/src/server/app-render/dynamic-rendering.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.server.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/unstable-rethrow.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/navigation.react-server.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/navigation.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/redirect-boundary.tsx", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/utils/warn-once.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/create-router-cache-key.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/layout-router.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/render-from-template-context.tsx", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/invariant-error.ts", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/utils/reflect-utils.ts", "turbopack:///[project]/node_modules/next/dist/src/client/request/search-params.browser.prod.ts", "turbopack:///[project]/node_modules/next/dist/src/client/request/search-params.browser.ts", "turbopack:///[project]/node_modules/next/dist/src/client/request/params.browser.prod.ts", "turbopack:///[project]/node_modules/next/dist/src/client/request/params.browser.ts", "turbopack:///[project]/node_modules/next/dist/src/server/web/spec-extension/adapters/reflect.ts", "turbopack:///[project]/node_modules/next/dist/src/server/create-deduped-by-callsite-server-error-logger.ts", "turbopack:///[project]/node_modules/next/dist/src/server/request/utils.ts", "turbopack:///[project]/node_modules/next/dist/src/server/request/search-params.ts", "turbopack:///[project]/node_modules/next/dist/src/server/request/params.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/client-page.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/client-segment.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/browser-resolved-metadata.tsx", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-metadata.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/server-inserted-metadata.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/async-metadata.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_PREFETCH = 'prefetch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  navigatedAt,\n  previousTree,\n  serverResponse,\n}: {\n  navigatedAt: number\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  prefetchCache?: AppRouterState['prefetchCache']\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n  allowAliasing: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  tree: FlightRouterState | undefined\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  navigatedAt: number\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\nexport interface PrefetchAction {\n  type: typeof ACTION_PREFETCH\n  url: URL\n  kind: PrefetchKind\n}\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\nexport type PrefetchCacheEntry = {\n  treeAtTimeOfPrefetch: FlightRouterState\n  data: Promise<FetchServerResponseResult>\n  kind: PrefetchKind\n  prefetchTime: number\n  staleTime: number\n  lastUsedTime: number | null\n  key: string\n  status: PrefetchCacheEntryStatus\n  url: URL\n}\n\nexport enum PrefetchCacheEntryStatus {\n  fresh = 'fresh',\n  reusable = 'reusable',\n  expired = 'expired',\n  stale = 'stale',\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   * Prefetched data is stored separately in `prefetchCache`, that is applied during ACTION_NAVIGATE.\n   */\n  cache: CacheNode\n  /**\n   * Cache that holds prefetched Flight responses keyed by url.\n   */\n  prefetchCache: Map<string, PrefetchCacheEntry>\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState = Promise<AppRouterState> | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | PrefetchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n", "export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n", "import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n", "import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n", "const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n", "import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map(getFlightDataPartsFromPath)\n}\n", "// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n", "// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n", "'use client'\nimport { hexHash } from '../../../shared/lib/hash'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = hexHash(\n    [\n      headers[NEXT_ROUTER_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n      headers[NEXT_ROUTER_STATE_TREE_HEADER],\n      headers[NEXT_URL],\n    ].join(',')\n  )\n\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n  const pairs = rawQuery.split('&').filter(Boolean)\n  pairs.push(`${NEXT_RSC_UNION_QUERY}=${uniqueCacheKey}`)\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n", "'use client'\n\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nexport function urlToUrlWithoutFlightMarker(url: string): URL {\n  const urlWithoutFlightParameters = new URL(url, location.origin)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(url).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n      JSON.stringify(flightRouterState)\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(res.url)\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeader = res.headers.get(NEXT_ROUTER_STALE_TIME_HEADER)\n    const staleTime =\n      staleTimeHeader !== null ? parseInt(staleTimeHeader, 10) : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await require('../react-dev-overlay/app/hot-reloader-client').waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\nexport function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n) {\n  const fetchUrl = new URL(url)\n\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n  setCacheBustingSearchParam(fetchUrl, headers)\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  return fetch(fetchUrl, {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  })\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n", "/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n", "import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n", "import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n", "'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\n\nconst workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n", "/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function handleSmoothScroll(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n  const htmlElement = document.documentElement\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n", "import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n", "const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n", "import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n", "'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n", "import type { Segment } from '../../../server/app-render/types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "import type { FlightRouterState } from '../../../../server/app-render/types'\nimport { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes'\n\nexport function hasInterceptionRouteInCurrentTree([\n  segment,\n  parallelRoutes,\n]: FlightRouterState): boolean {\n  // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n  if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n    return true\n  }\n\n  // If segment is not an array, apply the existing string-based check\n  if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n    return true\n  }\n\n  // Iterate through parallelRoutes if they exist\n  if (parallelRoutes) {\n    for (const key in parallelRoutes) {\n      if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n", "'use client'\n\nimport type {\n  <PERSON>ache<PERSON>ode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n      navigatedAt: -1,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n", "import type { SearchParams } from '../../server/request/search-params'\n\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  return promise\n}\n", "export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n", "import type { Params } from '../../server/request/params'\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeUntrackedExoticParams(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n", "export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n", "export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n", "import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n", "import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  if (prerenderStore.type === 'prerender') {\n    // We are in a dynamicIO (PPR or otherwise) prerender\n    return makeAbortingExoticSearchParams(workStore.route, prerenderStore)\n  }\n\n  // The remaining cases are prerender-ppr and prerender-legacy\n  // We are in a legacy static generation and need to interrupt the prerender\n  // when search params are accessed.\n  return makeErroringExoticSearchParams(workStore, prerenderStore)\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeAbortingExoticSearchParams(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            const error = createSearchAccessError(route, expression)\n            abortAndThrowOnSynchronousRequestDataAccess(\n              route,\n              expression,\n              error,\n              prerenderStore\n            )\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        const error = createSearchAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      const error = createSearchAccessError(route, expression)\n      abortAndThrowOnSynchronousRequestDataAccess(\n        route,\n        expression,\n        error,\n        prerenderStore\n      )\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n", "'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n", "import { use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport function BrowserResolvedMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { metadata, error } = use(promise)\n  // If there's metadata error on client, discard the browser metadata\n  // and let metadata outlet deal with the error. This will avoid the duplication metadata.\n  if (error) return null\n  return metadata\n}\n", "module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedMetadata\n", "import { use, useContext } from 'react'\nimport {\n  type MetadataResolver,\n  ServerInsertedMetadataContext,\n} from '../../../shared/lib/server-inserted-metadata.shared-runtime'\nimport type { StreamingMetadataResolvedState } from './types'\n\n// Receives a metadata resolver setter from the context, and will pass the metadata resolving promise to\n// the context where we gonna use it to resolve the metadata, and render as string to append in <body>.\nconst useServerInsertedMetadata = (metadataResolver: MetadataResolver) => {\n  const setMetadataResolver = useContext(ServerInsertedMetadataContext)\n\n  if (setMetadataResolver) {\n    setMetadataResolver(metadataResolver)\n  }\n}\n\nexport function ServerInsertMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  // Apply use() to the metadata promise to suspend the rendering in SSR.\n  const { metadata } = use(promise)\n  // Insert metadata into the HTML stream through the `useServerInsertedMetadata`\n  useServerInsertedMetadata(() => metadata)\n\n  return null\n}\n", "'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport const AsyncMetadata =\n  typeof window === 'undefined'\n    ? (\n        require('./server-inserted-metadata') as typeof import('./server-inserted-metadata')\n      ).ServerInsertMetadata\n    : (\n        require('./browser-resolved-metadata') as typeof import('./browser-resolved-metadata')\n      ).BrowserResolvedMetadata\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n", "'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["ACTION_REFRESH", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "ACTION_PREFETCH", "ACTION_HMR_REFRESH", "ACTION_SERVER_ACTION", "PrefetchKind", "PrefetchCacheEntryStatus", "RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER", "isThenable", "promise", "then", "React", "use", "dispatch", "dispatchAppRouterAction", "action", "Error", "useActionQueue", "actionQueue", "state", "setState", "useState", "process", "env", "NODE_ENV", "startTransition", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "type", "basePath", "__NEXT_ROUTER_BASEPATH", "pathname", "findSourceMapURL", "filename", "undefined", "getFlightDataPartsFromPath", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "getNextFlightSegmentPath", "flightSegmentPath", "normalizeFlightData", "flightData", "map", "globalBuildId", "setAppBuildId", "buildId", "getAppBuildId", "djb2Hash", "str", "hash", "i", "char", "charCodeAt", "hexHash", "toString", "setCacheBustingSearchParam", "url", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "join", "existingSearch", "search", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "pairs", "split", "filter", "Boolean", "push", "createFromReadableStream", "NEXT_RUNTIME", "require", "urlToUrlWithoutFlightMarker", "urlWithoutFlightParameters", "URL", "location", "origin", "searchParams", "delete", "__NEXT_CONFIG_OUTPUT", "endsWith", "doMpaNavigation", "canonicalUrl", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "window", "addEventListener", "abort", "fetchServerResponse", "options", "flightRouterState", "nextUrl", "prefetchKind", "encodeURIComponent", "JSON", "stringify", "AUTO", "isHmrRefresh", "res", "fetchPriority", "TEMPORARY", "createFetch", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "staleTimeHeader", "parseInt", "isFlightResponse", "ok", "body", "TURBOPACK", "flightStream", "createUnclosingPrefetchStream", "response", "createFromNextReadableStream", "b", "f", "S", "err", "aborted", "console", "error", "fetchUrl", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetch", "credentials", "priority", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue", "unresolvedThenable", "useContext", "PathnameContext", "hasFallbackRouteParams", "workAsyncStorage", "workStore", "getStore", "fallbackRouteParams", "size", "useUntrackedPathname", "HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "digest", "prefix", "httpStatus", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "errorCode", "destination", "at", "statusCode", "isNaN", "isNextRouterError", "createHrefFromUrl", "includeHash", "useEffect", "handleHardNavError", "next", "__pendingUrl", "href", "useNavFailureHandler", "__NEXT_APP_NAV_FAIL_HANDLING", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "isRevalidate", "isStaticGeneration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "previousPathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "GlobalError", "html", "id", "div", "style", "h2", "hostname", "p", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "existingSegment", "handleSmoothScroll", "fn", "onlyHashChange", "htmlElement", "document", "documentElement", "existing", "scroll<PERSON>eh<PERSON>or", "dontForceLayout", "getClientRects", "getSegmentValue", "Array", "isArray", "isGroupSegment", "isParallelRouteSegment", "addSearchParamsIfPageSegment", "isPageSegment", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "DEFAULT_SEGMENT_KEY", "actionAsyncStorage", "getRedirectError", "TemporaryRedirect", "redirect", "isAction", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "DIGEST", "notFound", "forbidden", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "unauthorized", "BAILOUT_TO_CSR", "BailoutToCSRError", "reason", "isBailoutToCSRError", "unstable_rethrow", "cause", "isHangingPromiseRejectionError", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "expression", "abortListenersBySignal", "WeakMap", "makeHangingPromise", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "listeners", "set", "once", "catch", "ignoreReject", "REACT_POSTPONE_TYPE", "Symbol", "for", "isPostpone", "$$typeof", "DYNAMIC_ERROR_CODE", "DynamicServerError", "description", "isDynamicServerError", "NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "code", "isStaticGenBailoutError", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "scheduleOnNextTick", "cb", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r", "workUnitAsyncStorage", "hasPostpone", "unstable_postpone", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "syncDynamicErrorWithStack", "createDynamicValidationState", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "getFirstDynamicReason", "trackingState", "markCurrentScopeAsDynamic", "workUnitStore", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "trackFallbackParamAccessed", "prerenderStore", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "_store", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "abortOnSynchronousPlatformIOAccess", "errorWithStack", "trackSynchronousPlatformIOAccessInDev", "requestStore", "prerenderPhase", "abortAndThrowOnSynchronousRequestDataAccess", "prerenderSignal", "validating", "syncDynamicLogged", "trackSynchronousRequestDataAccessInDev", "Postpone", "assertPostpone", "createPostponeReason", "isDynamicPostpone", "message", "isDynamicPostponeReason", "NEXT_PRERENDER_INTERRUPTED", "isPrerenderInterruptedError", "accessedDynamicData", "consumeDynamicAccess", "serverDynamic", "clientDynamic", "formatDynamicAPIAccesses", "access", "line", "createPostponedAbortSignal", "x", "createHangingInputAbortSignal", "cacheSignal", "inputReady", "annotateDynamicAccess", "useDynamicRouteParams", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "trackAllowedDynamicAccess", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "throwIfDisallowedDynamic", "syncError", "syncExpression", "syncLogged", "ReadonlyURLSearchParamsError", "ReadonlyURLSearchParams", "URLSearchParams", "append", "sort", "bailoutToClientRendering", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathParamsContext", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useSearchParams", "readonlySearchParams", "usePathname", "useRouter", "router", "useParams", "getSelectedLayoutSegmentPath", "parallelRouteKey", "first", "node", "parallelRoutes", "segmentValue", "useSelectedLayoutSegments", "context", "parentTree", "useSelectedLayoutSegment", "selectedLayoutSegments", "selectedLayoutSegment", "HandleRedirect", "redirectType", "RedirectErrorBoundary", "RedirectBoundary", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "componentDidCatch", "missingSlots", "triggeredStatus", "errorComponents", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createRouterCache<PERSON>ey", "withoutSearchParameters", "ensureLeadingSlash", "path", "normalizeAppPath", "reduce", "index", "segments", "normalizeRscURL", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "find", "m", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "concat", "splitInterceptingRoute", "hasInterceptionRouteInCurrentTree", "key", "Suspense", "useDeferredValue", "ReactDOM", "GlobalLayoutRouterContext", "TemplateContext", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "isLast", "hasOwnProperty", "subTree", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "focusAndScrollRef", "apply", "segmentPaths", "some", "scrollRefSegmentPath", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "clientHeight", "scrollTop", "focus", "ScrollAndFocusHandler", "InnerLayoutRouter", "cacheNode", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "resolvedRsc", "lazyData", "refetchTree", "includeNextUrl", "navigatedAt", "Date", "now", "serverResponse", "previousTree", "subtree", "Provider", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "templateStyles", "templateScripts", "template", "parentParallelRoutes", "segmentMap", "Map", "parentTreeSegment", "treeSegment", "cache<PERSON>ey", "stateKey", "newLazyCacheNode", "prefetchHead", "RenderFromTemplateContext", "InvariantError", "isDefinitelyAValidIdentifier", "describeStringPropertyAccess", "target", "prop", "describeHasCheckingStringProperty", "stringifiedProp", "wellKnownProperties", "CachedSearchParams", "makeUntrackedExoticSearchParams", "underlyingSearchParams", "cachedSearchParams", "keys", "for<PERSON>ach", "createRenderSearchParamsFromClient", "makeUntrackedExoticSearchParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "makeUntrackedExoticParams", "underlyingParams", "cachedParams", "createRenderParamsFromClient", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "ReflectAdapter", "receiver", "Reflect", "deleteProperty", "errorRef", "current", "cache", "logErrorOrWarn", "__NEXT_DYNAMIC_IO", "warn", "flushCurrentErrorIfNew", "createDedupedByCallsiteServerErrorLoggerDev", "getMessage", "logDedupedError", "args", "afterTaskAsyncStorage", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "invalidUsageError", "isRequestAPICallableInsideAfter", "afterTaskStore", "rootTaskSpawnPhase", "createSearchParamsFromClient", "createPrerenderSearchParams", "createRenderSearchParams", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "makeAbortingExoticSearchParams", "makeErroringExoticSearchParams", "isPrefetchRequest", "CachedSearchParamsForUseCache", "proxiedPromise", "Proxy", "hasOwn", "createSearchAccessError", "ownKeys", "makeErroringExoticSearchParamsForUseCache", "defineProperty", "writable", "enumerable", "configurable", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "proxiedProperties", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "add", "newValue", "syncIODev", "missingProperties", "warnForIncompleteEnumeration", "warnForSyncAccess", "createIncompleteEnumerationError", "describeListOfPropertyNames", "properties", "createParamsFromClient", "createPrerenderParams", "createRenderParams", "createServerParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForRoute", "createPrerenderParamsForClientSegment", "fallbackP<PERSON><PERSON>", "hasSomeFallbackParams", "makeAbortingExoticParams", "makeErroringExoticParams", "createParamsAccessError", "augmentedUnderlying", "ClientPageRoot", "params", "promises", "clientSearchParams", "clientParams", "ClientSegmentRoot", "slots", "BrowserResolvedMetadata", "metadata", "module", "exports", "vendored", "ServerInsertedMetadata", "ServerInsertedMetadataContext", "useServerInsertedMetadata", "metadataResolver", "setMetadataResolver", "ServerInsertMetadata", "AsyncMetadata", "MetadataOutlet", "AsyncMetadataOutlet", "NameSpace", "MetadataBoundary", "ViewportBoundary", "OutletBoundary"], "mappings": "6RAOO,IAAMA,EAAiB,UACjBC,AAD0B,EACR,WAClBC,AAD4B,EACX,UACjBC,AAD0B,EACJ,eAAc,AACpCC,EAAkB,WAAU,AAC5BC,EAAqB,cAAa,AAClCC,EAAuB,gBAAe,AAsI5C,IAAKC,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,+CAAAA,OAIX,AA4DWC,EAAAA,SAAAA,CAAAA,eAAAA,WAAAA,yDAAAA,OAKX,ihBCxNM,IAAMC,EAAa,MAAc,AAC3BC,EAAgB,cAAsB,AAItCC,EAAgC,yBAAiC,AACjEC,EAA8B,uBAA+B,AAK7DC,EACX,+BAAuC,AAC5BC,EAA0B,mBAA2B,AACrDC,EAA+B,4BAAoC,AACnEC,EAAW,WACXC,AAD8B,EACJ,mBAA2B,AAErDC,EAAiB,CAC5BT,EACAE,EACAC,EACAE,EACAD,EACD,CAAS,AAEGM,EAAuB,OAAe,AAEtCC,EAAgC,sBAA8B,AAC9DC,EAA2B,qBAA6B,AACxDC,EAA6B,0BAAkC,AAC/DC,EAA8B,2BAAmC,AACjEC,EAA2B,qBAA6B,iDC1B9D,SAASC,EACdC,CAAuB,EAEvB,OACc,OAAZA,GACmB,UAAnB,OAAOA,GACP,SAAUA,GACV,AAAwB,mBAAjBA,EAAQC,IAEnB,AAFuB,CAPvB,EAAA,CAAA,CAAA,+HCLA,IAAA,EAAkC,CAA3BC,CAA2B,CAAA,CAAA,MAAlBC,EAChB,CADmB,CACQ,EAA8B,CAAhDJ,AAAgD,CAAA,GAD9B,KAC8B,AAWzD,CAXmB,EADe,CAY9BK,EAA4C,GAXrB,EAapB,SAASC,EAAwBC,CAAsB,EAC5D,GAAIF,AAAa,MAAM,GACrB,MAAM,OAAA,cAEL,CAFSG,AAAJ,MACJ,2EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEFH,EAASE,EACX,CAEO,SAASE,EACdC,CAAiC,EAEjC,GAAM,CAACC,EAAOC,EAAS,CAAA,EAAGT,OAAAA,CAAMU,QAAQ,CAAeH,EAAYC,KAAK,EA0BxE,OAJEN,EAAW,AAACE,GACVG,EAAYL,QAAQ,CAACE,EAAQK,GAGjC,CAAA,EAAA,EAAOZ,UAAAA,EAAWW,GAAAA,CAAAA,EAAAA,EAASP,GAAAA,EAAIO,GAASA,CAC1C,2ECtDA,IAAA,EAAuC,EAAA,CAA9BM,AAA8B,CAAA,QACvC,EAAqC,EAAA,CAA5BpC,AAA4B,CADb,AACa,QADL,AAEhC,EAAwC,EAAA,CAA/ByB,AAA+B,CAAA,EAA+B,AAFhC,EACgD,CAA1D,GAGtB,KAH8B,SACL,CAEVY,EAAWC,CAAgB,CAAEC,CAAiB,EAClE,AAHsC,OAG/B,IAAIC,QAAQ,CAACC,EAASC,UAC3BN,eAAAA,EAAgB,UACdX,uBAAAA,EAAwB,CACtBkB,KAAAA,EAAM3C,oBAAAA,UACNsC,aACAC,UACAE,SACAC,CACF,EACF,EACF,EACF,iFCbO,IAAMK,EA0BPE,UAAS,OAzBbhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cC2BpB,ED1BD,OC0BUe,EACdC,AD3BaJ,CC2BiB,MAkBnBW,EAbX,GAAM,CAACL,EAAMC,EAAUC,ADhCOP,ECgCDQ,EAAc,CACzCL,EAAeM,CDjC6B,ICiCxB,CAAC,CAACL,GAElBM,EAAcP,EAAeM,KAAK,CAAC,EAAG,CAACL,GAE7C,MAAO,CAILO,cAAeD,EAAYD,KAAK,CAAC,EAAG,CAAC,eACrCC,EAGAE,QAA4C,AAAnCF,OAAAA,EAAAA,CAAW,CAACA,EAAYG,MAAM,CAAG,EAAA,AAAE,EAAnCH,EAAuC,QAChDL,EACAC,gBACAC,gBACAC,EACAM,aApB2B,IAoBbX,EAAeU,MAAM,AACrC,CACF,CAEO,GAJqCT,MAI5BW,EACdC,CAAoC,EAIpC,OAAOA,EAAkBP,KAAK,CAAC,EACjC,CAEO,SAASQ,EACdC,CAAsB,QAItB,AAA0B,UAAtB,AAAgC,OAAzBA,EACFA,EAGFA,EAAWC,GAAG,CAACjB,EACxB,kMChEA,IAAIkB,EAAwB,GAErB,SAASC,EAAcC,CAAe,EAC3CF,EAAgBE,CAClB,CAEO,SAASC,IACd,OAAOH,CACT,mDCdO,SAASI,EAASC,CAAW,EAClC,IAAIC,EAAO,KACX,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAIZ,MAAM,CAAEc,IAE9BD,AAFmC,EAE1BA,CAAAA,IAAQ,CAAA,CAAKA,EADTD,EAAII,GACYD,OADF,CAACD,GACS,EAEvC,OAAOD,IAAS,CAClB,CAEO,SAASI,EAAQL,CAAW,EACjC,OAAOD,EAASC,GAAKM,QAAQ,CAAC,IAAItB,KAAK,CAAC,EAAG,EAC7C,4HCjBA,IAAA,EAAwB,EAA0B,CAAzCqB,AAAyC,CAAA,CAAA,KAAlC,EAChB,EAEEvE,EAAAA,CADAD,AACAC,CAHsB,AAGtBA,GAAmC,EACnCF,GAsBK,IAAM2E,EAA6B,CACxCC,EACAC,KAEA,IA5B2B,AA4BrBC,EA3BN5E,AA2BM4E,CAAAA,EAAAA,EAAiBL,CA1BM,EAC7BpE,IAyBuBoE,EACrB,CACEI,CAAO,AA3BH,CA2BG,CA1BXrE,CA0BYP,mBA1BQ,QA0BRA,AAzBP,CAyBmC,EAAI,IACxC4E,CAAO,CAAA,EAAC3E,aA1BgB,sBA0BhBA,CAAoC,EAAI,IAChD2E,CAAO,CAAA,EAAC7E,6BAAAA,CAA8B,CACtC6E,CAAO,CAAA,EAACxE,QAAAA,CAAS,CAClB,CAAC0E,IAAI,CAAC,MAcHC,EAAiBJ,EAAIK,MAAM,CAI3BG,EAAQF,CAHGF,EAAeG,UAAU,CAAC,KACvCH,EAAe5B,KAAK,CAAC,GACrB4B,CAAAA,EACmBK,KAAK,CAAC,KAAKC,MAAM,CAACC,SACzCH,EAAMI,IAAI,CAAA,EAAIhF,oBAAAA,CAAqB,IAAGsE,GACtCF,EAAIK,MAAM,CAAGG,EAAM5B,MAAM,CAAI,IAAG4B,EAAML,IAAI,CAAC,KAAS,EACtD,EAAC,0KCvCD,IAAA,EAEE/E,EAA6B,CAC7BQ,AAFAP,CAEAO,QAQF,EAA2B,EAAuB,CAAzCwB,AAAyC,CAAA,CAAA,EATnB,EACT,EACpB3B,CAQF,CADmB,CACc,EAA+B,AAXnC,CAWpBqC,AAAuD,CAAA,AAV9D1C,CAEQ,EACRF,AAMyB,KAE3B,EAA6B,EAAwB,CAA5CF,AAA4C,AARzC,CAQyC,CAPnDU,AAMuB,AAAuC,GACX,IACrD,CAFiC,CAK1B,CAJc,CAIa,CAFhCsD,AAEgC,CAAA,KAJL,GAK7B,EAA8B,AAZL,EACvBzD,AAWgD,CAAzC+D,AAAyC,CAAA,AADhB,CACgB,GAH7B,IAIrB,EAA2C,EADrB,AACqB,AAFpC,CAEES,AAAkC,CAAA,MADb,AAhC9B,CAqByB,EACvBjE,AAtBI,CAiCuE,eAA1C,QAXT,AAWiB,EAVzCD,AAvBMgF,CAAwB,CAAE,CAGpB,EAFZ,AAEY,CAFX,AAEW,CAFV7D,AAEU,OA4DP,CA9DKC,GAAG,CAAC6D,IAIVC,AA0DUC,EAA4BhB,CAAW,EACrD,CAzC6B,EAlBjB,AAJc,CA+DpBiB,EAA6B,AA7D/BF,GAqBC,CAwCkCG,IAAIlB,AA7D/B,EA6DoCmB,SAASC,MAAM,EAE/D,AA1C4B,GAyC5BH,CACIjE,CADuBqE,OACfpE,GAAG,CAACC,CADuB,CAACoE,MAChB,AADsB,CAAA,EAAC1F,EAClB,kBADkBA,EAClB,AAEY,GAFE,QAEvCoB,QAAQC,GAAG,CAACsE,oBAAoB,EAChCN,EAA2BpD,QAAQ,CAAC2D,QAAQ,CAAC,QAC7C,CACA,GAAM,CAAE3D,UAAQ,CAAE,CAAGoD,EACfrC,EAASf,EAAS2D,QAAQ,CAAC,cAAgB,GAAK,EAEtDP,EAA2BpD,QAAQ,CAAGA,EAASW,KAAK,CAAC,EAAG,CAACI,EAC3D,CAEF,OAAOqC,CACT,CAEA,SAASQ,EAAgBzB,CAAW,EAClC,MAAO,CACLf,WAAY+B,EAA4BhB,GAAKF,QAAQ,GACrD4B,kBAAc1D,EACd2D,oBAAoB,EACpBC,aAAa,EACbC,WAAW,EACXC,UAAW,CAAC,CACd,CACF,CAEA,IAAIC,EAAkB,IAAIC,gBAsBnB,eAAeI,EACpBpC,CAAQ,CACRqC,CAAmC,EAEnC,GAAM,mBAAEC,CAAiB,SAAEC,CAAO,cAAEC,CAAY,CAAE,CAAGH,EAE/CpC,EAA0B,CAE9B,CAAA,EAAC/E,UAAAA,CAAW,CAAE,IAEd,CAAA,EAACE,6BAAAA,CAA8B,CAAEqH,MAAhCrH,aACCsH,KAAKC,SAAS,CAACL,GAEnB,EAQIE,IAAAA,EAAiBxH,YAAAA,CAAa4H,IAAI,EAAE,CACtC3C,CAAO,CAAC5E,EAAAA,2BAAAA,CAA4B,CAAG,GAAA,EAOrCkH,IACFtC,CAAO,CAAA,EAACxE,CADG,OACHA,CAAS,CAAG8G,CAAAA,EAGtB,GAAI,KAoCqBO,EA/BvB,IAAMC,EAAgBP,EAClBA,IAAAA,EAAiBxH,YAAAA,CAAagI,SAAS,CACrC,OACA,MACF,OAGuC,UAAU,CAA/ChG,QAAQC,GAAG,CAACsE,oBAAoB,GAK9BvB,CADJA,EAAM,IAAIkB,IAAIlB,EAAAA,EACNnC,QAAQ,CAAC2D,QAAQ,CAAC,KACxBxB,CAD8B,CAC1BnC,QAAQ,EAAI,YAEhBmC,EAAInC,QAAQ,EAAI,QAKtB,IAAMiF,EAAM,MAAMG,EAChBjD,EACAC,EACA8C,EACAhB,EAAgBmB,MAAM,EAGlBC,EAAcnC,EAA4B8B,EAAI9C,GAAG,EACjD0B,EAAeoB,EAAIM,UAAU,CAAGD,OAAcnF,EAE9CqF,EAAcP,EAAI7C,OAAO,CAACqD,GAAG,CAAC,iBAAmB,GACjDC,EAAe,CAAC,CAAA,CAAA,AAAiB,OAAhBT,EAAAA,EAAI7C,OAAO,CAACqD,GAAG,CAAC,OAAA,CAAA,CAAA,KAAA,EAAhBR,EAAyBU,QAAQ,CAAA,EAAC/H,QAAAA,CAAAA,EACnDoG,EAAY,CAAC,CAACiB,EAAI7C,OAAO,CAACqD,GAAG,CAAA,EAACxH,wBAAAA,EAC9B2H,EAAkBX,EAAI7C,OAAO,CAACqD,GAAG,CAACzH,EAAAA,6BAAAA,EAClCiG,EACgB,OAApB2B,EAA2BC,SAASD,EAAiB,IAAM,CAAC,EAC1DE,EAAmBN,EAAY9C,UAAU,CAAA,EAAC7E,uBAAAA,EAY9C,GAV6B,AACc,GADA,OACU,CAA/CsB,QAAQC,GAAG,CAACsE,oBAAoB,EAC7BoC,IACHA,EAAmBN,EAAY9C,UADV,AACoB,CAAC,aAAA,EAO5C,CAACoD,GAAoB,CAACb,EAAIc,EAAE,EAAI,CAACd,EAAIe,IAAI,CAM3C,CAN6C,MAEzC7D,EAAIP,IAAI,EAAE,CACZ0D,EAAY1D,IAAI,CAAGO,EAAIP,IAAI,AAAJA,EAGlBgC,EAAgB0B,EAAYrD,QAAQ,IAY7C,IAAMiE,EAAelC,EACjBmC,AA+ER,SAASA,AACPgB,CAAgD,EAahD,IAAMC,EAASD,EAAqBE,SAAS,GAC7C,OAAO,IAAIC,eAAe,CACxB,MAAMC,KAAKC,CAAU,EACnB,MAAO,CAAM,CACX,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAG,MAAMN,EAAOO,IAAI,GACzC,GAAI,CAACF,EAAM,CAGTD,EAAWI,OAAO,CAACF,GACnB,QACF,CAGA,MACF,CACF,CACF,EACF,EA9GsCzC,EAAIe,IAAI,EACtCf,EAAIe,IAAI,CACNI,EAAW,MAAOC,EACtBH,GAGF,GAAIzE,CAAAA,EAAAA,EAAAA,aAAAA,MAAoB2E,EAASE,CAAC,CAChC,CADkC,MAC3B1C,EAAgBqB,EAAI9C,GAAG,EAGhC,MAAO,CACLf,WAAYD,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBiF,EAASG,CAAC,EAC1C1C,aAAcA,EACdC,mBAAoB4B,EACpB3B,YAAaqC,EAASI,CAAC,WACvBxC,EACAC,WACF,CACF,CAAE,MAAOwC,EAAK,CAWZ,OAVKvC,AAAD,EAAiBmB,MAAM,CAACqB,OAAO,EAAE,AACnCC,QAAQC,KAAK,CACV,mCAAkCzE,EAAI,wCACvCsE,GAOG,CACLrF,WAAYe,EAAIF,QAAQ,GACxB4B,kBAAc1D,EACd2D,oBAAoB,EACpBC,aAAa,EACbC,WAAW,EACXC,UAAW,CAAC,CACd,CACF,CACF,CAEO,SAASmB,EACdjD,CAAQ,CACRC,CAAuB,CACvB8C,CAA6C,CAC7CG,CAAoB,EAEpB,IAAMwB,EAAW,IAAIxD,IAAIlB,cAKzBD,0BAAAA,EAA2B2E,EAAUzE,GAU9B4E,MAAMH,EAAU,CAErBI,YAAa,sBACb7E,EACA8E,SAAUhC,QAAiB/E,SAC3BkF,CACF,EACF,CAEO,SAASgB,EACdH,CAAwC,EAExC,OAAOlD,EAAyBkD,EAAc,cAC5C3G,UAAAA,kBACAU,EAAAA,gBAAAA,AACF,EACF,CA1MsB,aAAa,AAA/B,OAAOmE,SAKTA,OAAOC,gBAAgB,CAAC,WAAY,KAClCH,EAAgBI,KAAK,EACvB,GAIAF,OAAOC,gBAAgB,CAAC,WAAY,KAClCH,EAAkB,IAAIC,eACxB,sDC5GF,EAAA,CAAA,CAAA,4BAAO,IAAM0D,EAAqB,CAChCtJ,KAAM,KAAO,CACf,EAAsB,mFCLtB,IAAA,EAAkC,EAAA,CAAzBuJ,AAAyB,CAAA,QAClC,CADmB,CACa,EAAA,CAAvBC,AAAuB,CAAA,GADL,KAsCpB,GAtC2B,GACV,GAA8D,AAqCtEO,KArCgB,KA0C9B,CAlCF,AAkCMN,SAlCGA,EACP,GAAsB,YAiCQ,CAjC1B,OAAO5D,OAAwB,CAEjC,GAAM,kBAAE6D,CAAgB,CAAE,CACxB/E,EAAQ,CAAA,CAAA,IAAA,GAEJgF,EAAYD,EAAiBE,QAAQ,GAC3C,GAAI,CAACD,EAAW,OAAO,EAEvB,GAAM,qBAAEE,CAAmB,CAAE,CAAGF,SAC5B,CAACE,GAAoD,GAAG,CAAhCA,EAAoBC,IAGlD,AAHsD,AAAe,CAKrE,OAAO,CACT,IAyBE,CAAA,EAAA,EAAOP,UAAAA,EAAAA,EAAWC,eAAAA,EANT,IAOX,sOCnDO,IAAMQ,EAAwB,CACnCC,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAEMC,AAFL,EAEqB,IAAIC,IAAIC,OAAOC,MAAM,CAACP,IAE/BQ,EAAiC,2BAA0B,AAajE,SAASC,EACdpC,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,YAAYA,CAAAA,CAAI,EACM,AAAxB,UACA,OADOA,EAAMqC,MAAM,CAEnB,OAAO,EAET,GAAM,CAACC,EAAQC,EAAW,CAAGvC,EAAMqC,MAAM,CAACrG,KAAK,CAAC,KAEhD,OACEsG,IAAWH,GACXJ,EAAcS,GAAG,CAACC,OAAOF,GAE7B,CAEO,SAASG,EACd1C,CAA8B,EAG9B,OAAOyC,OAAOF,AADKvC,EAAMqC,MAAM,CAACrG,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAAS2G,EACdC,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,mFC5DO,IAAKC,EAAAA,SAAAA,CAAAA,SAAAA,WAAAA,gHAAAA,OAIX,yHCJD,IAAA,EAAmC,EAAwB,CAAlDA,AAAkD,CAAA,QAEpD,EAFoD,EAE9CC,EAAsB,GAFR,QAAQ,KAEe,AAE3C,IAAKC,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,6BAAAA,OAGX,AAaM,SAASC,EAAgBhD,CAAc,EAC5C,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMqC,MAAM,CAEnB,OAAO,EAGT,IAAMA,EAASrC,EAAMqC,MAAM,CAACrG,KAAK,CAAC,KAC5B,CAACiH,EAAWhK,EAAK,CAAGoJ,EACpBa,EAAcb,EAAOtI,KAAK,CAAC,EAAG,CAAC,GAAG2B,IAAI,CAAC,KAGvC0H,EAAaX,OAFJJ,AAEWO,EAFJO,EAAE,CAAC,CAAC,IAI1B,OACEF,IAAcH,IACJ,YAAT7J,GAA+B,IAA/BA,KAAsBA,CAAS,CAAK,EACd,UAAvB,OAAOiK,GACP,CAACG,MAAMD,IACPA,KAAAA,EAAcP,kBAAAA,AAElB,kFC5CA,IAAA,EAGO,EAAA,CAFLT,AAEK,CAAA,QACP,EAAoD,EAAkB,CAA7DY,AAA6D,CAAA,GAAA,CADlB,EAQ7C,IAVoB,IAGH,CAORM,EACdtD,CATK,AASS,EAEd,EAVkD,IAUlD,CAAA,EAAA,EAAOgD,eAAAA,EAAgBhD,IAAAA,CAAAA,EAAAA,EAAUoC,yBAAAA,EAA0BpC,EAC7D,kDCfO,SAASuD,EACdhI,CAA8C,CAC9CiI,CAA2B,EAE3B,OAFAA,KAAAA,IAAAA,IAAAA,GAAuB,CAAA,EAEhBjI,EAAInC,QAAQ,CAAGmC,EAAIK,MAAM,EAAI4H,CAAAA,CAAcjI,EAAIP,IAAI,CAAG,EAAA,CAAC,AAChE,2ICLiC,EAAA,CAAA,CAAA,QACjC,IAAA,EAAkC,EAAA,CAAzBuI,AAAyB,CAAA,IAAuC,IAElE,QAFmB,CAEVG,EAAmB1D,CAAc,IAFf,IAGhC,EACEA,GACA,AAAkB,oBAAXxC,UACPA,OAAOmG,IAAI,CAACC,YAAY,EAAA,CAAA,EAAA,EACxBL,iBAAAA,EAAkB,IAAI9G,IAAIe,OAAOd,QAAQ,CAACmH,IAAI,KAAA,CAAA,EAAA,EAC5CN,iBAAAA,EAAkB/F,OAAOmG,IAAI,CAACC,YAAY,GAC5C,CACA7D,QAAQC,KAAK,CACV,oEACDA,GAEFxC,OAAOd,QAAQ,CAACmH,IAAI,CAAGrG,OAAOmG,IAAI,CAACC,YAAY,CAACvI,QAAQ,IACjD,EAGX,CAEO,SAASyI,IAyBhB,2JC5CA,EAAuC,CAAhClM,CAAgC,CAAA,CAAA,QAAP,AAChC,EAAqC,EAAwB,CAApD8J,AAAoD,CAAA,EADtB,MAEvC,EAAkC,EAAwB,AADG,CACpD4B,AAAiD,CAAA,KAD7B,GAEM,CADuB,CACA,CAAA,CAAA,CAFrB,GACX,IAG1B,CAF0D,GADxB,AAG5BjC,EACJ,AAAkB,oBAAX7D,OAEDlB,EAAQ,CAAA,CAAA,IAAA,GACR+E,gBAAgB,MAClB9H,EAEAyK,EAAS,CACbhE,MAAO,CAELiE,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA6BA,SAASC,EAAe,CAAyB,EAAzB,GAAA,OAAE7E,CAAK,CAAkB,CAAzB,EACtB,GAAIqB,EAAkB,CACpB,IAAMyD,EAAQzD,EAAiBE,QAAQ,GACvC,GAAIuD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOC,YAAY,AAAZA,GAAgBD,CAAAA,CAAJ,OAAIA,KAAAA,EAAAA,EAAOE,kBAAAA,AAAkB,EAElD,CAFoD,KACpDjF,QAAQC,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,CAEO,MAAMiF,UAA6BrN,EAAAA,OAAAA,CAAMsN,SAAS,CASvD,OAAOC,yBAAyBnF,CAAY,CAAE,CAC5C,GAAA,CAAA,EAAA,EAAIsD,iBAAAA,EAAkBtD,GAGpB,KAH4B,CAGtBA,EAGR,MAAO,OAAEA,CAAM,CACjB,CAEA,OAAOoF,yBACLC,CAAgC,CAChCjN,CAAgC,CACE,CAClC,GAAM,OAAE4H,CAAK,CAAE,CAAG5H,SAsBlB,AAAIiN,EAAMjM,QAAQ,GAAKhB,EAAMkN,gBAAgB,EAAIlN,EAAM4H,KAAK,CACnD,CADqD,AAE1DA,MAAO,KACPsF,iBAAkBD,EAAMjM,QAC1B,AADkC,EAG7B,CACL4G,MAAO5H,EAAM4H,KAAK,CAClBsF,iBAAkBD,EAAMjM,QAAQ,AAClC,CACF,CAOAmM,QAA0B,QACxB,AAAI,IAAI,CAACnN,KAAK,CAAC4H,KAAK,CAClB,CADoB,AAElB,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,cACE,EAAA,GAAA,EAAC6E,EAAAA,CAAe7E,IADlB,EACyB,IAAI,CAAC5H,KAAK,CAAC4H,KAAK,GACtC,IAAI,CAACqF,KAAK,CAACG,WAAW,CACtB,IAAI,CAACH,KAAK,CAACI,YAAY,MACxB,GAAA,EAACC,IAAI,CAACL,KAAK,CAACM,cAAc,CAAA,CACxB3F,MAAO,IAAI,CAAC5H,KAAK,CAAC4H,KAAK,CACvB4F,MAAO,IAAI,CAACA,KAAK,MAMlB,IAAI,CAACP,KAAK,CAACQ,QAAQ,AAC5B,CA1EAC,YAAYT,CAAgC,CAAE,CAC5C,KAAK,CAACA,GAAAA,IAAAA,CAoDRO,KAAAA,CAAQ,KACN,IAAI,CAACvN,QAAQ,CAAC,CAAE2H,MAAO,IAAK,EAC9B,EArDE,IAAI,CAAC5H,KAAK,CAAG,CAAE4H,MAAO,KAAMsF,iBAAkB,IAAI,CAACD,KAAK,CAACjM,QAAQ,AAAC,CACpE,CAwEF,CAKO,SAAS2M,EAAY,CAAyB,EAAzB,GAAA,OAAE/F,CAAK,CAAkB,CAAzB,EACpBqC,EAA6BrC,MAAAA,EAAAA,KAAAA,EAAAA,EAAOqC,MAAM,CAChD,MAAA,CAAA,EAAA,EACE,IAAA,EAAC2D,CADH,MACGA,CAAKC,GAAG,8BACP,EAAA,GAAA,EAACpM,OAAAA,CAAAA,QACD,IAAA,EAACuF,OAAAA,gBACC,GAAA,EAACyF,EAAAA,CAAe7E,MAAOA,SACvB,GAAA,EAACkG,MAAAA,CAAIC,MAAOnC,EAAOhE,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACkG,CAAD,KAACA,gBACC,IAAA,EAACE,KAAAA,CAAGD,MAAOnC,EAAOQ,IAAI,WAAE,wBACAnC,EAAS,SAAW,SAAS,8CACvB7E,OAAOd,QAAQ,CAAC2J,QAAQ,CAAC,YAAU,IAC9DhE,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAAA,CAAAA,EAAAA,EAAS,EAATA,CAAS,EAACiE,IAAAA,CAAEH,GAAZ9D,GAAmB2B,EAAOQ,IAAI,UAAI,WAAUnC,IAAgB,eAMzE,OAIe0D,EAWR,SAASQ,CAXU,CAWI,CAO7B,EAP6B,GAAA,gBAC5BZ,CAAc,aACdH,CAAW,cACXC,CAAY,UACZI,CAAQ,CAGT,CAP6B,EAYtBzM,EAAAA,CAAAA,EAAAA,EAAWsI,oBAAAA,WACjB,AAAIiE,EACF,CAAA,EAAA,EACE,GAAA,EAACV,EAAAA,CACC7L,CAHc,QAGJA,EACVuM,eAAgBA,EAChBH,YAAaA,EACbC,aAAcA,WAEbI,IAKP,CAAA,EAAA,EAAO,GAAA,EAAA,EAAA,QAAA,CAAA,UAAGA,GACZ,eADS,+DCzNF,IAAMW,EAAe,CAC1BC,EACAvM,IAGA,AAA+B,AAA3B,UAAqC,OAA9BuM,EACT,AAAuB,UAAU,AAA7B,OAAOvM,GAEFuM,IAAoBvM,EAK/B,AAAuB,UAAnB,AAA6B,OAAtBA,GAGJuM,CAAe,CAAC,EAAE,GAAKvM,CAAO,CAAC,EAAE,EAAIuM,CAAe,CAAC,EAAE,GAAKvM,CAAO,CAAC,EAAE,kDCdxE,SAASwM,EACdC,CAAc,CACd/I,CAAqE,EAIrE,GAJAA,KAAAA,QAAAA,EAAmE,EAAC,EAIhEA,EAAQgJ,cAAc,CAAE,YAC1BD,IAGF,IAAME,EAAcC,SAASC,eAAe,CACtCC,EAAWH,EAAYV,KAAK,CAACc,cAAc,CACjDJ,EAAYV,KAAK,CAACc,cAAc,CAAG,OAC9BrJ,AAAD,EAASsJ,eAAe,EAAE,AAI5BL,EAAYM,cAAc,GAE5BR,IACAE,EAAYV,KAAK,CAACc,cAAc,CAAGD,CACrC,CArBA,EAAA,CAAA,CAAA,4ECFO,SAASI,EAAgBlN,CAAgB,EAC9C,OAAOmN,MAAMC,OAAO,CAACpN,GAAWA,CAAO,CAAC,EAAE,CAAGA,CAC/C,+ECFO,SAASqN,EAAerN,CAAe,EAE5C,MAAsB,MAAfA,CAAO,CAAC,EAAE,EAAYA,EAAQ6C,QAAQ,CAAC,IAChD,CAEO,SAASyK,EAAuBtN,CAAe,EACpD,OAAOA,EAAQ4B,UAAU,CAAC,MAAQ5B,AAAY,eAChD,CAEO,SAASuN,EACdvN,CAAgB,CAChB0C,CAA2D,EAI3D,GAFsB1C,CAElBwN,CAF0B3I,QAAQ,CAAC4I,GAEpB,CACjB,IAAMC,EAAmB3J,KAAKC,SAAS,CAACtB,GACxC,MAA4B,OAArBgL,EACHD,EAAmB,IAAMC,EACzBD,CACN,CAEA,OAAOzN,CACT,8IAEO,IAAMyN,EAAmB,WAAU,AAC7BE,EAAsB,cAAa,wNC5BhD,IAAA,EAAmC,EAAwB,CAAA,AAAlDhF,CAAkD,QAC3D,EAIEC,AALyD,EAKtC,CAHnBC,AAIK,CAAA,GANoB,GAQ3B,IAAM+E,CANQ,AAGO,AALc,CASf,CALlB9E,KAEuB,OAGvB,GALe,EACfF,EAIOtF,OAEDlB,EAAQ,CAAA,CAAA,IAAA,IACRwL,kBAAkB,CACpBvO,OAEC,SAASwO,EACdxM,CAAW,CACXtC,CAAkB,CAClBmK,CAAqE,EAArEA,KAAAA,IAAAA,IAAAA,EAAAA,EAAiCP,kBAAAA,CAAmBmF,iBAAAA,AAAiB,EAErE,IAAMhI,EAAQ,OAAA,cAA8B,CAA9B,AAAI/H,MAAAA,EAAM6K,mBAAAA,EAAV,oBAAA,OAAA,mBAAA,gBAAA,CAA6B,GAE3C,OADA9C,EAAMqC,MAAM,CAAA,EAAMS,mBAAAA,CAAoB,IAAG7J,EAAK,IAAGsC,EAAI,IAAG6H,EAAW,IAC5DpD,CACT,CAcO,SAASiI,EAEd1M,CAAW,CACXtC,CAAmB,IAFnB,EAIS6O,CAIT,OAJA7O,MAAAA,CAAAA,GAAAA,EAAS6O,CAAAA,IAJkB,EAIlBA,CAAAA,EAAAA,AAA4B,GAA5BA,IAAAA,EAAAA,EAAoBvG,QAAQ,EAAA,CAAA,CAAA,KAAA,EAA5BuG,EAAgCI,QAAQ,EAAA,EAC7CnF,YAAAA,CAAa5G,IAAI,CAAA,EACjB4G,YAAAA,CAAaoF,OAAAA,AAAO,EAElBJ,EAAiBxM,EAAKtC,EAAAA,EAAM4J,kBAAAA,CAAmBmF,iBAAiB,CACxE,CAaO,SAASI,EAEd7M,CAAW,CACXtC,CAAyC,EAEzC,MAFAA,KAAAA,AAFA,IAEAA,IAAAA,EAAAA,EAAqB8J,YAAAA,CAAaoF,EAFP,KAEOA,AAAO,EAEnCJ,EAAiBxM,EAAKtC,EAAAA,EAAM4J,kBAAAA,CAAmBwF,iBAAiB,CACxE,CAUO,SAASC,EAAwBtI,CAAc,QACpD,AAAI,CAAA,EAAA,CAAA,CAACgD,eAAAA,EAAgBhD,GAIdA,EAAMqC,GAJgB,GAIV,CAACrG,KAAK,CAAC,KAAKjC,KAAK,CAAC,EAAG,CAAC,GAAG2B,IAAI,CAAC,KAJb,IAKtC,CAEO,SAAS6M,EAAyBvI,CAAoB,EAC3D,GAAI,CAAA,CAAA,EAAA,EAACgD,eAAAA,EAAgBhD,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAI/H,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAO+H,EAAMqC,MAAM,CAACrG,KAAK,CAAC,IAAK,EAAE,CAAC,EACpC,AADsC,CAG/B,SAASwM,EAA+BxI,CAAoB,EACjE,GAAI,CAAA,CAAA,EAAA,EAACgD,eAAAA,EAAgBhD,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAI/H,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOwK,OAAOzC,EAAMqC,MAAM,CAACrG,KAAK,CAAC,KAAKmH,EAAE,CAAC,CAAC,GAC5C,0EC9EA,IAAMsF,EAAU,GAnBgB,AAmBhB,CAjBT,CAAA,CAAA,CAAA,QAiBWtG,eAjBkC,eAiBlCA,CAA+B,OAE1C,SAASuG,IAEd,IAAM1I,EAAQ,OAAA,cAAiB,CAAjB,AAAI/H,MAAMwQ,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgB,EAG9B,OAFEzI,EAAkCqC,MAAM,CAAGoG,EAEvCzI,CACR,mDCPO,SAAS2I,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI1Q,MACP,+GADG,oBAAA,OAAA,kBAAA,iBAAA,CAEN,EAOJ,wBAbgB,AAlBgB,CAEzB,CAAA,CAAA,CAAA,QAgBWkK,eAhBkC,eAgBlCA,GAA+B,8CCG1C,SAAS0G,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI5Q,MACP,+GADG,oBAAA,OAAA,mBAAA,eAAA,EAEN,EAOJ,2BAhCgC,AAmBhB,CAjBT,CAAA,CAAA,CAAA,QAiBWkK,eAjBkC,eAiBlCA,GAA+B,uGCnBjD,IAAM2G,EAAiB,kCAGhB,OAAMC,UAA0B9Q,MAGrC6N,YAA4BkD,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZ3G,MAAAA,CAASyG,CAIzB,CACF,CAGO,SAASG,EAAoBpJ,CAAY,QAC3B,AAAnB,UAAI,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIwC,CAJwD,KAIlD,GAAKyG,CACxB,6EChBO,SAASI,EAAiBlJ,CAAc,EAC7C,GAAA,CAAA,EAAA,EAAIsD,iBAAAA,EAAkBtD,IAAAA,CAAAA,EAAAA,EAAUiJ,mBAAAA,EAAoBjJ,GAClD,KAD0D,CACpDA,EAGJA,aAAiB/H,OAAS,UAAW+H,GACvCkJ,EAAiBlJ,EAD6B,AACvBmJ,KAAK,CAEhC,IAXA,IAAA,EAAoC,EAAA,CAAA,AAA3BF,CAA2B,OACpC,EAAkC,EAAwB,CAAA,AAAjD3F,CAAiD,CADwB,IAAtD,IAC8B,IADtB,GACV,QAAQ,iCCD3B,SAAS8F,EACdvJ,CAAY,QAEZ,AAAmB,UAAf,OAAOA,GAAoBA,AAAQ,QAAQ,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIwC,CAJwD,KAIlD,GAAKgH,CACxB,sEAEA,IAAMA,EAA4B,2BAElC,OAAMC,UAAqCrR,MAGzC6N,YAA4ByD,CAAkB,CAAE,CAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,qJAAqJ,CAAC,EAAA,IAAA,CAFnRA,UAAAA,CAAAA,EAAAA,IAAAA,CAFZlH,MAAAA,CAASgH,CAMzB,CACF,CAGA,IAAMG,EAAyB,IAAIC,QAS5B,SAASC,EACdjL,CAAmB,CACnB8K,CAAkB,EAElB,GAAI9K,EAAOqB,OAAO,CAChB,CADkB,MACXhH,QAAQE,MAAM,CAAC,IAAIsQ,EAA6BC,GAClD,EACL,IAAMI,EAAiB,IAAI7Q,QAAW,CAAC8Q,EAAG5Q,KACxC,IAAM6Q,EAAiB7Q,EAAO8Q,IAAI,CAChC,KACA,IAAIR,EAA6BC,IAE/BQ,EAAmBP,EAAuB3K,GAAG,CAACJ,GAClD,GAAIsL,EACFA,EAAiB5N,IAAI,CAAC0N,OACjB,CACL,CAHoB,GAGdG,EAAY,CAACH,EAAe,CAClCL,EAAuBS,GAAG,CAACxL,EAAQuL,GACnCvL,EAAOhB,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIxC,EAAI,EAAGA,EAAI+O,EAAU7P,MAAM,CAAEc,IAAK,AACzC+O,CAAS,CAAC/O,EAAE,EAEhB,EACA,CAAEiP,MAAM,CAAK,EAEjB,CACF,GAKA,OADAP,EAAeQ,KAAK,CAACC,GACdT,CACT,CACF,CAEA,SAASS,IAAgB,4ECrEzB,IAAMC,EAA8BC,OAAOC,GAAG,CAAC,kBAExC,SAASC,EAAWxK,CAAU,EACnC,MACmB,UAAjB,OAAOA,GACPA,AAAU,UACVA,EAAMyK,QAAQ,GAAKJ,CAEvB,8GCRA,IAAMK,EAAqB,sBAEpB,OAAMC,UAA2B1S,MAGtC6N,YAA4B8E,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5BvI,MAAAA,CAAoCqI,CAIpC,CACF,CAEO,SAASG,EAAqBhL,CAAY,QAC/C,AACiB,UAAf,OAAOA,GACC,AAARA,QACA,CAAE,CAAA,WAAYA,GAAE,AACM,UAAtB,AACA,OADOA,EAAIwC,MAAM,EAKZxC,EAAIwC,MAAM,GAAKqI,CACxB,qHCrBA,IAAMI,EAA0B,yBAEzB,OAAMC,UAA8B9S,wBAApC,KAAA,IAAA,GAAA,IAAA,CACW+S,IAAAA,CAAOF,EACzB,CAEO,SAASG,EACdjL,CAAc,QAEd,AAAqB,UAAjB,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAJ4D,AAItDgL,GAJ0D,CAItD,GAAKF,CACxB,gJCdO,IAAMI,EAAyB,6BAA4B,AACrDC,EAAyB,6BAA4B,AACrDC,EAAuB,2BAA0B,kDCO9D,EAAA,CAAA,CAAA,6GAAO,IAAMC,EAAqB,AAAWC,IAO3CxS,QAAQC,OAAO,GAAGpB,IAAI,CAAC,KAInBY,QAAQgT,QAAQ,CAACD,EAErB,EACF,EAAC,AAQYE,EAAoB,AAAWF,IAIxCG,aAAaH,EAEjB,EAAC,AAOM,SAASI,IACd,OAAO,IAAI5S,QAAc,AAACC,GAAYyS,EAAkBzS,GAC1D,CAWO,SAAS4S,IAIZ,OAAO,IAAI7S,QAAQ,AAAC8S,GAAMH,aAAaG,GAE3C,i1BChCA,IAAA,EAAyB,CAAlBhU,CAAkB,CAAA,CAAA,QAEzB,AAFkB,EAEiB,EAAA,CAAA,AAA1B+S,CAA0B,EAFV,KAGzB,EAAsC,EAAA,CAA7BI,AAA6B,CAD2C,AAC3C,IADX,IAE3B,EAAqC,EAFF,AAEE,CAA5Bc,AAA4B,CAAA,IAAoC,EAD3C,AAA2D,EAEzF,EAAiC,EAAA,CAAxBxK,AAAwB,CAAA,AAFK,KACT,EAE7B,CAD4E,CACzC,EAA4B,CAAtDqI,AAAsD,CAAA,AAF1B,EACZ,MAEzB,EAEEyB,AAJ+B,EAK/BC,CAAAA,AAFAF,CAEAE,AAJ6D,GAApC,EAGH,EACtBA,AAAoB,CAEtB,EAAmC,CANA,CAMqB,CAA/CC,AAA+C,CAAA,CADjD,MACiD,AAJhC,CAMxB,CALEF,GAKIW,EAAiD,GAF5B,QAAQ,CAEf,OAAA,EAAOlU,MAHmB,CAGnBA,CAAMmU,iBAAiB,CA2C3C,SAASC,EACdC,CAA2C,EAE3C,MAAO,wBACLA,EACAC,gBAAiB,EAAE,CACnBC,2BAAuB5S,EACvB6S,0BAA2B,IAC7B,CACF,CAEO,SAASC,IACd,MAAO,CACLC,qBAAqB,EACrBC,oBAAoB,EACpBC,mBAAoB,GACpBC,sBAAsB,EACtBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASC,EACdC,CAAmC,MAE5BA,EAAP,OAAuC,AAAvC,OAAOA,EAAAA,EAAcV,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCU,EAAkCrD,UAAU,AACrD,CASO,SAASsD,EACd/H,CAAgB,CAChBgI,CAAuE,CACvEvD,CAAkB,EAElB,KAAIuD,GAEAA,AAAuB,YAAT7T,IAAI,EACK,kBACvB,CADA6T,EAAc7T,IAHd6T,AAGkB,GAHH,CAefhI,EAAMiI,YAAY,GAAIjI,EAAMkI,WAAW,EAE3C,AAF6C,GAEzClI,EAAMmI,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAA,EAAIlC,qBAAAA,CACR,CAAC,MAAM,EAAEjG,EAAMoI,KAAK,CAAC,8EAA8E,EAAE3D,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIuD,GACF,GAA2B,SADV,QAC2B,CAAxCA,EAAc7T,IAAI,CACpBkU,EACErI,EAAMoI,KAAK,CACX3D,EACAuD,EAAcM,eAAe,OAE1B,GAA2B,qBAAvBN,EAAc7T,IAAI,CAAyB,CACpD6T,EAAcO,UAAU,CAAG,EAG3B,IAAMxN,EAAM,OAAA,cAEX,CAFW,IAAA,EAAI8K,kBAAAA,CACd,CAAC,MAAM,EAAE7F,EAAMoI,KAAK,CAAC,iDAAiD,EAAE3D,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHAzE,EAAMwI,uBAAuB,CAAG/D,EAChCzE,EAAMyI,iBAAiB,CAAG1N,EAAI2N,KAAK,CAE7B3N,CACR,CAMA,EAEJ,CAUO,GAlBI,IACLtH,EAiBUkV,EACd3I,CAAgB,CAChByE,CAAkB,CAnBN/Q,CAqBZ,EArBe,CAACC,CAqBViV,EAAAA,EAAiB7B,GArBC,KAAK,UAGzB,EAkBmBA,CAAqBtK,IApBxCuL,IAoBgD,GAC/CY,GAA0C,OApB3CZ,UAoB4D,CAAzCY,EAAezU,CApBpBA,GAoBwB,CApBpB,CAsBtBkU,EAAqBrI,EAtBM,AAsBAoI,KAAK,CAAE3D,EAAYmE,EAAeN,eAAe,CAC9E,CAQO,SAASO,EACdpE,CAAkB,CAClBzE,CAAgB,CAChB4I,CAAoC,EAGpC,IAAM7N,EAAM,OAAA,cAEX,CAFW,IAAA,EAAI8K,kBAAAA,CACd,CAAC,MAAM,EAAE7F,EAAMoI,KAAK,CAAC,mDAAmD,EAAE3D,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAOA,OALAmE,EAAeL,UAAU,CAAG,EAE5BvI,EAAMwI,uBAAuB,CAAG/D,EAChCzE,EAAMyI,iBAAiB,CAAG1N,EAAI2N,KAAK,CAE7B3N,CACR,CASO,SAAS+N,EACdC,CAAiB,CACjBf,CAAmC,EAE/BA,GAEAA,AAAuB,YAFR,AAED7T,IAAI,EAClB6T,AAAuB,kBACvB,GADc7T,IAAI,GAQK,cAAvB6T,EAAc7T,IAAI,EACK,qBAAvB6T,EAAc7T,IAAS,AAAL,GAClB,CACA6T,EAAcO,UAAU,EAAG,CASjC,CAKA,SAASS,EACPZ,CAAa,CACb3D,CAAkB,CAClBmE,CAAoC,EAIpC,IAAM1N,EAAQ+N,EAFC,CAAC,MAAM,EAAEb,EAAM,mBAEgBlE,8CAFiD,EAAEO,EAAW,CAAC,CAAC,EAI9GmE,EAAe9M,UAAU,CAAClD,KAAK,CAACsC,GAEhC,IAAMoN,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBlB,YADG,GACY,CAAC/P,IAAI,CAAC,CAGnCqR,MAAOJ,EAAgBnB,sBAAsB,CACzC,AAAIhU,QAAQuV,KAAK,MACjBjU,aACJgQ,CACF,EAEJ,CAEO,SAASyE,EACdd,CAAa,CACb3D,CAAkB,CAClB0E,CAAqB,CACrBP,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBhB,KADD,oBAC0B,GAC3CgB,EAAgBjB,qBAAqB,CAAG5C,EACxC6D,EAAgBhB,yBAAyB,CAAG6B,GAGhDH,EAAoCZ,EAAO3D,EAAYmE,EACzD,CAEO,SAASQ,EACdC,CAA0B,EAI1BA,EAAaC,cAAc,CAAG,EAChC,CAYO,SAASC,EACdnB,CAAa,CACb3D,CAAkB,CAClB0E,CAAqB,CACrBP,CAAoC,EAGpC,IAAgC,IAA5BY,AADoBZ,EAAe9M,UAAU,CAACnC,MAAM,CACpCqB,OAAO,CAAY,CAMrC,IAAMsN,EAAkBM,EAAeN,eAAe,CAClDA,GACEA,AAA8C,MAAM,GAApChB,KADD,oBAC0B,GAC3CgB,EAAgBjB,qBAAqB,CAAG5C,EACxC6D,EAAgBhB,yBAAyB,CAAG6B,GACV,IAA9BP,EAAea,AAAqB,UAAX,GAG3BnB,EAAgBoB,iBAAiB,EAAG,CAAA,GAI1CV,EAAoCZ,EAAO3D,EAAYmE,EACzD,CACA,MAAMK,EACJ,CAAC,MAAM,EAAEb,EAAM,iEAAiE,EAAE3D,EAAW,CAAC,CAAC,CAEnG,CAGO,IAAMkF,EACXP,EASK,SAASQ,EAAS,QAAE1F,CAAM,CAAEkE,OAAK,CAAiB,EACvD,IAAMQ,CAV+B,CAU/BA,EAAiB7B,oBAAAA,CAAqBtK,QAAQ,GAKpD4L,EAAqBD,EAAOlE,EAH1B0E,GAA0C,GAGRN,eAHhBM,EAAezU,IAAI,CACjCyU,EAAeN,eAAe,CAC9B,KAER,CAEO,SAASD,EACdD,CAAa,CACb3D,CAAkB,CAClB6D,CAA4C,EAE5CuB,IACIvB,GACFA,EAAgBlB,YADG,GACY,CAAC/P,IAAI,CAAC,CAGnCqR,MAAOJ,EAAgBnB,sBAAsB,CACzC,AAAIhU,QAAQuV,KAAK,MACjBjU,aACJgQ,CACF,KAGF3R,OAAAA,CAAMmU,iBAAiB,CAAC6C,EAAqB1B,EAAO3D,GACtD,CAEA,SAASqF,EAAqB1B,CAAa,CAAE3D,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAE2D,EAAM,iEAAiE,EAAE3D,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKWsF,EAAkBhP,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACgC,UAChC,AADA,OAAQA,EAAYiP,OAAO,EAEpBC,EAAyBlP,EAAYiP,AAXoC,CAAC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASC,EAAwB/F,CAAc,EAC7C,OACEA,EAAOjK,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnFiK,EAAOjK,QAAQ,CACb,gEAGN,CAEA,GAAIgQ,CAAgE,MAAxCH,CAA+C,CAA1B,MAAO,QACtD,MAAM,OAAA,cAEL,CAFK,AAAI3W,MACR,0FADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAM+W,EAA6B,6BAEnC,SAASjB,EAAgCe,CAAe,EACtD,IAAM9O,EAAQ,OAAA,cAAkB,CAAd/H,AAAJ,MAAU6W,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADE9O,EAAcqC,MAAM,CAAG2M,EAClBhP,CACT,CAMO,SAASiP,EACdjP,CAAc,EAEd,MACE,AAAiB,iBAAVA,GACG,OAAVA,GACCA,EAAcqC,MAAM,GAAK2M,GAC1B,SAAUhP,GACV,YAAaA,GACbA,aAAiB/H,KAErB,CAEO,SAASiX,EACdhD,CAAqC,EAErC,OAAOA,EAAgB/R,MAAM,CAAG,CAClC,CAEO,SAASgV,EACdC,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAclD,eAAe,CAAC/P,IAAI,IAAIkT,EAAcnD,eAAe,EAC5DkD,EAAclD,eAAe,AACtC,CAEO,SAASoD,EACdpD,CAAqC,EAErC,OAAOA,EACJjQ,MAAM,CACL,AAACsT,GACyB,UAAxB,OAAOA,EAAO/B,KAAK,EAAiB+B,EAAO/B,KAAK,CAACrT,MAAM,CAAG,GAE7DM,GAAG,CAAC,CAAC,YAAE8O,CAAU,OAAEiE,CAAK,CAAE,IACzBA,EAAQA,EACLxR,KAAK,CAAC,MACP,AAGCjC,KAAK,CAAC,GACNkC,MAAM,CAAC,AAACuT,KAEHA,EAAKzQ,QAAQ,CAAC,uBAKdyQ,AALqC,EAKhCzQ,QAAQ,CAAC,MAXoD,aAgBlEyQ,AALiC,EAK5BzQ,QAAQ,CAAC,YAAY,CAM/BrD,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAE6N,EAAW;AAAG,EAAEiE,EAAAA,CAAO,EAEjE,CAEA,SAASmB,IACP,GAAI,CAAC7C,EACH,MAAM,KADU,EACV,cAEL,CAFK,AAAI7T,MACR,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEJ,CAMO,SAASwX,EAA2BzG,CAAc,EACvD2F,IACA,IAAM/N,EAAa,IAAIrD,gBAEvB,GAAI,CACF3F,EAAAA,OAAAA,CAAMmU,iBAAiB,CAAC/C,EAC1B,CAAE,MAAO0G,EAAY,CACnB9O,EAAWlD,KAAK,CAACgS,EACnB,CACA,OAAO9O,EAAWnC,MAAM,AAC1B,CAOO,SAASkR,EACd7C,CAAmC,EAEnC,IAAMlM,EAAa,IAAIrD,gBAkBvB,OAhBIuP,EAAc8C,WAAW,CAI3B9C,CAJ6B,CAIf8C,WAAW,CAACC,UAAU,GAAGlY,IAAI,CAAC,KAC1CiJ,EAAWlD,KAAK,EAClB,QAOA2N,kBAAAA,EAAmB,IAAMzK,EAAWlD,KAAK,IAGpCkD,EAAWnC,MAAM,AAC1B,CAEO,SAASqR,EACdvG,CAAkB,CAClBmE,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBlB,YADG,GACY,CAAC/P,IAAI,CAAC,CACnCqR,MAAOJ,EAAgBnB,sBAAsB,CACzC,AAAIhU,QAAQuV,KAAK,CACjBjU,kBACJgQ,CACF,EAEJ,CAEO,SAASwG,EAAsBxG,CAAkB,EACtD,IAAMjI,EAAAA,EAAYD,gBAAAA,CAAiBE,QAAQ,GAE3C,GACED,GACAA,EAAU0D,kBAAkB,EAC5B1D,EAAUE,mBAAmB,EAC7BF,EAAUE,mBAAmB,CAACC,IAAI,CAAG,EACrC,CAGA,IAAMqL,EAAAA,EAAgBjB,oBAAAA,CAAqBtK,QAAQ,GAC/CuL,IAEyB,WAFV,EAEuB,CAApCA,EAAc7T,IAAI,GAIpBrB,OAAAA,CAAMC,GAAG,CAAA,CAAA,EAAA,EAAC6R,kBAAAA,EAAmBoD,EAAckD,YAAY,CAAEzG,IAChDuD,AAAuB,iBAAiB,GAA1B7T,IAAI,CAE3BkU,EACE7L,EAAU4L,KAAK,CACf3D,EACAuD,EAAcM,eAAe,EAEC,oBAAoB,CAA3CN,EAAc7T,IAAI,EAC3B0U,EAAiCpE,EAAYjI,EAAWwL,GAG9D,CACF,CAEA,IAAMmD,EAAmB,mCACnBC,EAAmB,AAAIC,OAC3B,CAAC,UAAU,EAAA,EAAEjF,sBAAAA,CAAuB,QAAQ,CAAC,EAEzCkF,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAA,EAAEhF,sBAAAA,CAAuB,QAAQ,CAAC,EAEzCkF,EAAiB,AAAIF,OAAO,CAAC,UAAU,EAAA,EAAE/E,oBAAAA,CAAqB,QAAQ,CAAC,EAEtE,KAFwCA,IAE/BkF,EACdpD,CAAa,CACbqD,CAAsB,CACtBC,CAAyC,CACzCpB,CAAmC,CACnCC,CAAmC,EAEnC,IAAIgB,EAAeI,IAAI,CAACF,IAGjB,GAAIL,EAAiBO,IAAI,CAACF,GAAiB,AAHT,CAIvCC,EAAkBjE,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAI6D,EAAiBK,IAAI,CAACF,GAAiB,CAChDC,EAAkBhE,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAIyD,EAAiBQ,IAAI,CAACF,GAAiB,CAChDC,EAAkBlE,mBAAmB,CAAG,GACxC,MACF,MAAO,GACL8C,EAAchD,yBAAyB,EACvCiD,EAAcjD,yBAAyB,CACvC,CACAoE,EAAkB/D,oBAAoB,EAAG,EACzC,MACF,KAAO,CAEL,IAAMzM,EAAQ0Q,AAMlB,SAASA,AACP5B,CAAe,CACfyB,CAAsB,EAEtB,IAAMvQ,EAAQ,OAAA,GAVgC8O,WAUd,CAAlB,AAAI7W,MAAM6W,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADA9O,EAAMwN,KAAK,CAAG,UAAYsB,EAAUyB,EAC7BvQ,CACT,EAdoB,CAAC,OAAO,EAAEkN,EAAM,+UAA+U,CAAC,CAC3TqD,GACrDC,EAAkB9D,aAAa,CAACvQ,IAAI,CAAC6D,GACrC,MACF,EACF,CAWO,SAAS2Q,EACdzD,CAAa,CACbsD,CAAyC,CACzCpB,CAAmC,CACnCC,CAAmC,MAE/BuB,EACAC,EACAC,EAeJ,GAdI1B,EAAchD,yBAAyB,EAAE,AAC3CwE,EAAYxB,EAAchD,yBAAyB,CACnDyE,EAAiBzB,EAAcjD,qBAAqB,CACpD2E,GAAiD,IAApC1B,EAAcZ,iBAAiB,EACnCa,EAAcjD,yBAAyB,EAAE,AAClDwE,EAAYvB,EAAcjD,yBAAyB,CACnDyE,EAAiBxB,EAAclD,qBAAqB,CACpD2E,GAAiD,IAApCzB,EAAcb,iBAAiB,GAE5CoC,EAAY,KACZC,EAAiBtX,OACjBuX,EAAa,IAGXN,EAAkB/D,oBAAoB,EAAImE,EAO5C,MANI,AAACE,GADkD,AAIrD/Q,QAAQC,CAHO,IAGF,CAAC4Q,GAGV,IAAA,EAAI7F,qBAAAA,CAGZ,IAAM2B,EAAgB8D,EAAkB9D,aAAa,CACrD,GAAIA,EAAcvS,MAAM,CAAE,CACxB,IAAK,IAAIc,EAAI,EAAGA,EAAIyR,EAAcvS,MAAM,CAAEc,IAAK,AAC7C8E,QAAQC,KAAK,CAAC0M,CAAa,CAACzR,EAAE,CAGhC,OAAM,IAAA,EAAI8P,qBAAAA,AACZ,CAEA,GAAI,CAACyF,EAAkBlE,mBAAmB,EACxC,AAD0C,GACtCkE,EAAkBjE,kBAAkB,CAAE,CACxC,GAAIqE,EAEF,MADA7Q,GADa,KACLC,KAAK,CAAC4Q,GACR,OAAA,cAEL,CAFK,IAAA,EAAI7F,qBAAAA,CACR,CAAC,OAAO,EAAEmC,EAAM,oEAAoE,EAAE2D,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAA,EAAI9F,qBAAAA,CACR,CAAC,OAAO,EAAEmC,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAO,GAAIsD,EAAkBhE,kBAAkB,CAAE,CAC/C,GAAIoE,EAEF,MADA7Q,GADa,KACLC,KAAK,CAAC4Q,GACR,OAAA,cAEL,CAFK,IAAA,EAAI7F,qBAAAA,CACR,CAAC,OAAO,EAAEmC,EAAM,oEAAoE,EAAE2D,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,eAAA,EAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAA,EAAI9F,qBAAAA,CACR,CAAC,OAAO,EAAEmC,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GACF,CAEJ,6EC3sBO,SAAShE,EAAiBlJ,CAAc,EAC7C,GAAA,CAAA,EAAA,EACEsD,iBAAAA,EAAkBtD,IAAAA,CAClBiJ,EAAAA,EAAAA,mBAAAA,EAAoBjJ,IAAAA,CAAAA,EAAAA,EACpB6K,oBAAAA,EAAqB7K,IAAAA,CAAAA,EAAAA,EACrB6O,iBAAAA,EAAkB7O,IAAAA,CAAAA,EAAAA,EAClBwK,UAAAA,EAAWxK,IAAAA,CAAAA,EAAAA,EACXoJ,8BAAAA,EAA+BpJ,GAE/B,KADA,CACMA,EAGJA,aAAiB/H,OAAS,UAAW+H,GACvCkJ,EAAiBlJ,EAD6B,AACvBmJ,KAAK,CAEhC,IAtBA,IAAA,EAAuC,CAAQ,CAAA,CAAtCC,AAAsC,CAAA,QAC/C,EAA2B,EAAA,CAAlBoB,AAAkB,CAAA,CAA2C,CADe,MAErF,CADmB,CACiB,EAAA,CAA3BvB,AAA2B,CAAA,CAFG,EACZ,IAE3B,EAAkC,EAAwB,CAAjD3F,AAAiD,CAAA,CADwB,IAAtD,GAE5B,CAD0D,CACxB,EAAA,CAFE,AAE3BuL,AAAyB,CAAA,EADR,MACmD,AAC7E,EAFkC,AAEG,EAAwB,CAAA,AAApDhE,CAAoD,EADnC,QAAQ,GAC2B,MAAhC,QAAQ,6BCErC,EAAA,CAAA,CAAA,0BAAO,IAAM3B,EACO,aAAlB,OAAO1L,OAEDlB,EAAQ,CAAA,CAAA,IAAA,IACR4M,gBAAgB,CAEhB5M,EAAQ,CAAA,CAAA,IAAA,IACR4M,gBAAgB,CAAA,kDCdV,EAAA,CAAA,CAAA,iCA4B8B,EAAY,CAAA,CAAA,OAC3B,CAD2B,CACT,CAAA,CAAA,MACT,EAAA,CAAA,CAAA,QACC,EAAA,CAAA,CAAA,QACM,EAAA,CAAA,CAAA,QACZ,EAAoB,CAAA,CAAA,IAAA,GAhCrD,OAAM6H,UAAqC9Y,MACzC6N,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAMkL,UAAgCC,gBAEpCC,QAAS,CACP,MAAM,IAAIH,CACZ,CAEAlU,QAAS,CACP,MAAM,IAAIkU,CACZ,CAEA9G,KAAM,CACJ,MAAM,IAAI8G,CACZ,CAEAI,MAAO,CACL,MAAM,IAAIJ,CACZ,CACF,iOC1BA,IAAA,EAAkC,EAAA,CAAA,AAAzBhI,CAAyB,OAClC,EAAiC,EAAA,CAD+C,AACvE1H,AAAwB,CAAA,GADP,IAGnB,IAH2B,IACT,CAET+P,EAFsE,AAE7CpI,CAAc,EACrD,EAH+B,EAGzB1H,EAAAA,EAAYD,gBAAAA,CAAiBE,QAAQ,GAE3C,GAAID,CAAAA,OAAAA,GAAAA,EAAAA,AAAW0L,WAAAA,AAAW,EAAE,EAExB1L,MAAAA,EAAAA,KAAAA,EAAAA,EAAW0D,kBAAAA,AAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,IAAA,EAAI+D,iBAAAA,CAAkBC,GAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAA4B,EACvE,iMCNA,IAAA,EAA2C,EAAA,CAAlC9H,AAAkC,CAAA,QAC3C,CADmB,CAGE,CAHAmQ,CAKd,CAHLC,AAGK,CAAA,IAAA,AALqB,IAM5B,EAEiB,CACfG,CAAiB,AATiB,CAOlCD,AAEiB,CAAA,CAPD,CAQX,CAPLD,GAHyC,EAW3C,EAAgC,EAAA,CAAvBnK,AAAuB,CAFb,AAEa,IAJX,EACnBjG,EAG2E,AAC7E,CAP2D,CAOb,EAAQ,CAA7CwG,AAA6C,CAAA,AAD9B,IAC8B,GAJrC,CAGe,GACgD,IAAvD,EAAEE,GAC3B,IAAA,EAAwC,EAA2B,CAHN,AAGpDmJ,AAA0D,CAAA,QAsFjEW,EACK,CAAA,CAAA,OADgB,CApFvB,EAFgC,EAE1B5B,EACc,CAoFb,GAvFiC,SAGtC,OAAOvS,OAEDlB,EAAQ,CAAA,CAAA,IAAA,CAkF6C,GAjFrDyT,qBAAqB,MACvBxW,EAuBC,SAASqY,IACd,IAAMhV,EAAAA,CAAAA,EAAAA,EAAesE,UAAAA,EAAAA,EAAWsQ,mBAAAA,EAK1BK,EAAAA,CAAAA,EAAAA,EAAuBR,OAAAA,EAAQ,IAC9BzU,AAAL,EAMO,EANH,EAMG,EAAIoU,MANQ,iBAMRA,CAAwBpU,GAH1B,KAIR,CAACA,EAAa,EAEjB,GAAsB,aAAlB,OAAOY,OAAwB,CAEjC,GAAM,0BAAE4T,CAAwB,CAAE,CAChC9U,EAAQ,CAAA,CAAA,IAAA,GAEV8U,EAAyB,oBAC3B,CAEA,OAAOS,CACT,CAoBO,SAASC,IAKd,OAJA/B,OAAAA,EAAAA,EAAwB,CAAxBA,gBAIA,CAAA,EAAA,EAAO7O,UAAAA,EAAAA,EAAWC,eAAAA,CACpB,CA2BO,SAAS4Q,IACd,IAAMC,EAAAA,CAAS9Q,EAAAA,EAAAA,UAAAA,EAAAA,EAAWoQ,gBAAAA,EAC1B,GAAIU,AAAW,MAAM,GACnB,MAAM,OAAA,cAAwD,CAAxD,AAAI/Z,MAAM,+CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAuD,GAG/D,OAAO+Z,CACT,CAoBO,SAASC,IAGd,OAFAlC,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,cAEA,CAAA,EAAA,EAAO7O,UAAAA,EAAAA,EAAWuQ,iBAAAA,CACpB,CAiEO,SAASe,EACdL,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3BpC,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,8BAEA,IAAM0C,EAAAA,CAAUvR,EAAAA,EAAAA,UAAAA,EAAAA,EAAWqQ,mBAAAA,SAEtBkB,AAAL,EAEOP,AAtET,EAoEM,KAAU,EApEPA,EACPvY,CAAuB,CACvBwY,CAAwB,CACxBC,CAAY,CACZpY,CAA0B,MAEtBqY,EACJ,GAJAD,KAAAA,IAAAA,IAAAA,EAAQ,EAAA,EACRpY,KAAAA,QAAAA,EAAwB,EAAA,AAAE,EAGtBoY,EAEFC,EAAO1Y,CAAI,CAAC,CAFH,CAEK,CAACwY,EAAiB,KAC3B,KAGEG,EADP,IAAMA,EAAiB3Y,CAAI,CAAC,EAAE,CAC9B0Y,EAAOC,AAAuB,OAAvBA,EAAAA,EAAezM,QAAAA,AAAQ,EAAvByM,EAA2BrQ,OAAOC,MAAM,CAACoQ,EAAe,CAAC,EAAE,AACpE,CAEA,GAAI,CAACD,EAAM,OAAOrY,EAClB,IAAME,EAAUmY,CAAI,CAAC,EAAE,CAEnBE,EAAAA,CAAAA,EAAAA,EAAenL,eAAAA,EAAgBlN,SAEnC,AAAI,CAACqY,GAAgBA,EAAazW,UAAU,CAAA,EAAC6L,gBAAAA,EACpC3N,CADuD,EAIhEA,EAAYmC,IAAI,CAACoW,GAEVL,EACLG,EACAF,EACA,GACAnY,GAEJ,EAqCsCyY,EAAQC,UAAU,CAAEP,GAFnC,IAGvB,CAqBO,SAASQ,EACdR,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3BpC,OAAAA,EAAAA,EAAwB,CAAxBA,6BAEA,IAAM6C,EAAyBJ,EAA0BL,GAEzD,GAAI,CAACS,GAA0BA,AAAkC,GAAG,GAAdzY,MAAM,CAC1D,OAAO,KAGT,IAAM0Y,EACiB,aAArBV,EACIS,CAAsB,CAAC,EAAE,CACzBA,CAAsB,CAACA,EAAuBzY,MAAM,CAAG,EAAE,CAI/D,OAAO0Y,IAAAA,EAA0BhL,mBAAAA,CAC7B,KACAgL,CACN,wRC/QA,EAAwC,CAAjCjb,CAAiC,CAAA,CAAA,MAAxB6L,EAEwB,EAAA,CAAA,CAAA,GAFf,QAAQ,QAAO,OAGxC,EAAmC6E,EAA+B,CAAzDC,AAAyD,CAAA,OAClE,EAAsC,AADoB,CAAoB,CAChC,AAAkB,CAAA,AAAvDxF,CAAuD,IADE,EAQlE,IARiC,CACZ,CADcuF,CACZtF,EAOd8P,EAPuD,AAOxC,CAQvB,EARuB,GAAA,KAPc,KAQpC7K,CAAQ,CACRrC,OAAK,CACLmN,cAAY,CAKb,CARuB,EAShBf,EAAAA,CAAAA,EAAAA,EAASD,SAAAA,IAaf,WAXAtO,SAAAA,EAAU,EAFKsO,KAGbna,OAAAA,CAAMc,eAAe,CAAC,KAChBqa,IAAAA,EAAiBhQ,YAAAA,CAAa5G,IAAI,CACpC6V,CADsC,CAC/B7V,IAAI,CAAC8L,EAAU,CAAC,GAEvB+J,EAAO7J,OAAO,CAACF,EAAU,CAAC,GAE5BrC,GACF,EACF,EAAG,CAACqC,EAAU8K,EAAcnN,EAAOoM,EAAO,EAEnC,IACT,CAEO,MAAMgB,UAAAA,EAA8Bpb,OAAAA,CAAMsN,SAAS,CASxD,OAAOC,yBAAyBnF,CAAU,CAAE,CAC1C,GAAA,CAAA,EAAA,EAAIgD,eAAAA,EAAgBhD,GAGlB,KAH0B,CAGnB,CAAEiI,SAFGK,CAEO/M,AAFP+M,EAAAA,EAAAA,uBAAAA,EAAwBtI,GAEZ+S,aADlBA,CAAAA,EAAAA,EAAexK,wBAAAA,EAAyBvI,EACT,CAGvC,OAAMA,CACR,CAGAuF,QAA0B,CACxB,GAAM,CAAE0C,UAAQ,cAAE8K,CAAY,CAAE,CAAG,IAAI,CAAC3a,KAAK,QAC5B,AAAjB,OAAI6P,GAAqB8K,AAAiB,MAAM,GAC9C,CAAA,EAAA,EACE,GAAA,EAACD,EAAAA,CACC7K,SAAUA,EACV8K,aAAcA,EACdnN,MAAO,IAAM,IAAI,CAACvN,QAAQ,CAAC,CAAE4P,SAAU,IAAK,KAK3C,IAAI,CAAC5C,KAAK,CAACQ,QAAQ,AAC5B,CA7BAC,YAAYT,CAA4B,CAAE,CACxC,KAAK,CAACA,GACN,IAAI,CAACjN,KAAK,CAAG,CAAE6P,SAAU,KAAM8K,aAAc,IAAK,CACpD,CA2BF,CAEO,SAASE,EAAiB,CAA2C,EAA3C,GAAA,UAAEpN,CAAQ,CAAiC,CAA3C,EACzBmM,EAAAA,CAAAA,EAAAA,EAASD,SAAAA,IACf,MAAA,CAAA,EAAA,EACE,GAAA,EAACiB,EAAAA,AADH,CACyBhB,OAAQA,WAASnM,GAE5C,0EC9EA,IAAIqN,EAAW,AAACtJ,IAAe,8GCa/B,EAAyC,CAAlChS,CAAkC,CAAA,CAAA,MAAzBsJ,EAChB,EAAqC,EAAyB,CAArDQ,AAAqD,CAAA,EADpC,MAE1B,EAEEgB,AAJgC,EAKhCC,CAJ4D,AAI5DA,AAFAhB,CAEAgB,IALuC,CACZ,GAOJ,CAJI,CAIiC,CAH5DA,AAG4D,CAAA,CAPzB,OAQrC,AANuB,EACrBD,EAKF,CAJoC,AAG0B,CAC3B,CAHjCN,CAGiC,CAAA,AAA1B+Q,CAA0B,OAqBnC,OAAMC,GArBqB,IAA+D,CAH/D,EAwBrBA,CArB6B,CAqBWxb,IAvBvC,GAuBuCA,CAAMsN,SAAS,CAY3DmO,WAnC6B,QAmCH,CAqB1B,CAEA,OAAOlO,yBAAyBnF,CAAU,CAAE,CAC1C,GAAA,CAAA,EAAA,EAAIoC,yBAAAA,EAA0BpC,GAE5B,KAFoC,CAE7B,CACLuT,gBAFIhR,CAEaA,AAFbA,EAAAA,EAAaG,2BAAAA,EAA4B1C,EAG/C,CAGF,OAAMA,CACR,CAEA,OAAOoF,yBACLC,CAA2C,CAC3CjN,CAA8B,CACE,QAOhC,AAAIiN,EAAMjM,QAAQ,GAAKhB,EAAMkN,gBAAgB,EAAIlN,EAAMmb,eAAe,CAC7D,CACLA,AAFoE,qBAEnDha,EACjB+L,iBAAkBD,EAAMjM,QAAQ,AAClC,EAEK,CACLma,gBAAiBnb,EAAMmb,eAAe,CACtCjO,iBAAkBD,EAAMjM,QAAQ,AAClC,CACF,CAEAmM,QAAS,CACP,GAAM,CAAEmD,UAAQ,WAAEC,CAAS,cAAEE,CAAY,UAAEhD,CAAQ,CAAE,CAAG,IAAI,CAACR,KAAK,CAC5D,iBAAEkO,CAAe,CAAE,CAAG,IAAI,CAACnb,KAAK,CAChCob,EAAkB,CACtB,CAAA,EAAC7R,qBAAAA,CAAsBC,SAAS,CAAC,CAAE8G,EACnC,CAAA,EAAC/G,qBAAAA,CAAsBE,SAAS,CAAC,CAAE8G,EACnC,CAAA,EAAChH,qBAAAA,CAAsBG,YAAY,CAAC,CAAE+G,CACxC,EAEA,GAAI0K,EAAiB,CACnB,IAAME,EACJF,IAAAA,EAAoB5R,qBAAAA,CAAsBC,SAAS,EAAI8G,EACnDgL,EACJH,IAAAA,EAAoB5R,qBAAAA,CAAsBE,SAAS,EAAI8G,EACnDgL,EACJJ,IAAAA,EAAoB5R,qBAAAA,CAAsBG,YAAY,EAAI+G,CAAtClH,QAGtB,AAAM8R,GAAcC,CAAhB,CAAED,CAA6BE,EAInC,CACE,EAAA,EAAA,IAAA,EAAA,CAL8C,CAK9C,EALkD,MAKlD,CAAA,gBACE,GAAA,EAACC,OADH,AACGA,CAAKC,KAAK,SAASC,QAAQ,YAC3Bvb,GAMAib,CAAe,CAACD,EAAgB,CANxB/a,GAAG,AANPqN,CAMQpN,AASnB,CAEA,OAX2B,AAWpBoN,CACT,CArGAC,YAAYT,CAA2C,CAAE,CACvD,KAAK,CAACA,GACN,IAAI,CAACjN,AAuF2B,KAvFtB,CAAG,CACXmb,UAsF8B,WAAA,AAtFbha,EACjB+L,iBAAkBD,EAAMjM,QAAQ,AAClC,CACF,CAgGF,CAEO,SAAS2a,EAA2B,CAKT,EALS,CAd/B,EAc+B,IAd/B,EAACH,IAeXlL,CAAQ,GAfGkL,QAgBXjL,CAAS,cACTE,CAAY,UACZhD,CAAQ,CACwB,CALS,EAUnCzM,EAAAA,CAAAA,EAAAA,EAAWsI,oBAAAA,IACX4R,EAAAA,CAAepS,EAAAA,EAAAA,UAAAA,EAAAA,EAAWiS,kBAAAA,SACJzK,AAE5B,GAFwCC,CAEpCqL,EAFiDnL,EAGnD,AACE,GAAA,EAAA,GAAA,EAJ4D,AAI3DuK,EAAAA,CACCha,CAHgB,QAGNA,EACVsP,SAAUA,EACVC,UAAWA,EACXE,aAAcA,EACdyK,aAAcA,WAEbzN,IAKP,CAAA,EAAA,EAAO,GAAA,EAAA,EAAA,QAAA,CAAA,UAAGA,GACZ,eADS,sEClLT,IAAA,EAAiC,EAA6B,CAAA,AAArD8B,CAAqD,QAEvD,KAFuD,EAArC,EAETsM,EACd/Z,CAAgB,CAChBga,CAAwC,CAJT,OAQ/B,CAJAA,KAAAA,IAAAA,IAAAA,GAAmC,CAAA,EAI/B7M,MAAMC,OAAO,CAACpN,IACNA,CAAO,CAAC,EAAE,CAAC,CADK,GACFA,CAAO,CAAC,EAAE,CAAC,IAAGA,CAAO,CAAC,EAAE,CAK9Cga,GAA2Bha,EAAQ4B,UAAU,CAAA,EAAC6L,gBAAAA,EAChD,CADmE,CAC5DA,gBAAAA,CAGFzN,CACT,kDChBO,SAASia,EAAmBC,CAAY,EAC7C,OAAOA,EAAKtY,UAAU,CAAC,KAAOsY,EAAQ,IAAGA,CAC3C,CAFA,EAAA,CAAA,CAAA,gICJA,IAAA,EAAmC,EAAA,CAAA,AAA1BD,CAA0B,IAAsC,IACzE,EAA+B,CAAe,CAAA,CAArC5M,AAAqC,CAAA,GADnB,KAsBpB,GAtB4B,EACZ,IAqBP8M,EAAiBnH,CAAa,CArBf,CAsB7B,MAAA,CAAA,EAAA,EAAOiH,kBAAAA,EACLjH,EAAMlR,KAAK,CAAC,KAAKsY,MAAM,CAAC,CAAClb,EAAUc,EAASqa,EAAOC,IAEjD,AAAI,CAACta,GAKL,CAAA,EAAA,EAAIqN,CALU,aAKVA,EAAerN,IAKA,KAAK,CALK,AAKzBA,CAAO,CAAC,EAAE,EAMXA,CAAY,YAAsB,UAAZA,CAAY,CAAM,EACzCqa,IAAUC,EAASra,MAAM,CAAG,EAhBrBf,CAiBP,CAIQA,EAAS,IAAGc,EACrB,IAEP,CAMO,SAASua,EAAgBlZ,CAAW,EACzC,OAAOA,EAAI4M,OAAO,CAChB,cAEA,KAEJ,yBAHkC,6IC5DlC,IAAA,EAAiC,CAAa,CAAA,CAAA,AAArCkM,CAAqC,QAGvC,IAAMK,EAA6B,CACxC,AAJuB,QAAQ,GAK/B,MACA,OACA,QACD,CAAS,AAEH,SAASC,EAA2BP,CAAY,EAErD,OAKU7a,SAJR6a,EACGpY,KAAK,CAAC,KACN4Y,IAAI,CAAE1a,AAAD,GACJwa,EAA2BE,IAAI,CAAC,AAACC,GAAM3a,EAAQ4B,UAAU,CAAC+Y,IAGlE,CAEO,SAASC,EAAoCV,CAAY,EAC9D,IAAIW,EACFC,EACAC,EAEF,IAAK,IAAM/a,KAAWka,EAAKpY,KAAK,CAAC,KAE/B,AAFqC,GACrCgZ,CACIA,CADKN,EAA2BE,IAAI,CAAC,AAACC,GAAM3a,EAAQ4B,UAAU,CAAC+Y,IACvD,CACT,CAACE,EAAmBE,EAAiB,CAAGb,EAAKpY,KAAK,CAACgZ,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACC,EACpC,MAAM,OAAA,GADgD,WAGrD,CAFShd,AAAJ,MACH,+BAA8Bmc,EAAK,qFADhC,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAKF,OAFAW,EAAAA,CAAAA,EAAAA,EAAoBV,gBAAAA,EAAiBU,GAE7BC,GACN,IAAK,MAGDC,EADEF,AAAsB,CAL0B,IAKrB,GACT,IAAGE,EAEJF,EAAoB,IAAME,EAE/C,KACF,KAAK,OAEH,GAA0B,KAAK,CAA3BF,EACF,AAdmG,MAc7F,OAAA,cAEL,CAFK,AAAI9c,MACP,+BAA8Bmc,EAAK,gEADhC,oBAAA,OAAA,mBAAA,eAAA,EAEN,GAEFa,EAAmBF,EAChB/Y,KAAK,CAAC,KACNjC,KAAK,CAAC,EAAG,CAAC,GACVmb,MAAM,CAACD,GACPvZ,IAAI,CAAC,KACR,KACF,KAAK,QAEHuZ,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAME,EAAyBJ,EAAkB/Y,KAAK,CAAC,KACvD,GAAImZ,EAAuBhb,MAAM,EAAI,EACnC,CADsC,KAChC,OAAA,cAEL,CAFK,AAAIlC,MACP,+BAA8Bmc,EAAK,mEADhC,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGFa,EAAmBE,EAChBpb,KAAK,CAAC,EAAG,CAAC,GACVmb,MAAM,CAACD,GACPvZ,IAAI,CAAC,KACR,KACF,SACE,MAAM,OAAA,cAAyC,CAAzC,AAAIzD,MAAM,gCAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAwC,EAClD,CAEA,MAAO,mBAAE8c,mBAAmBE,CAAiB,CAC/C,8FCrFO,SAASG,EAAkC,CAG9B,EAH8B,GAAA,CAChDlb,EACAoY,EACkB,CAH8B,EAKhD,GAAIjL,MAAMC,OAAO,CAACpN,KAA4B,OAAfA,CAAO,CAAC,EAAE,EAA4B,OAAfA,CAAO,CAAC,EAAE,AAAK,CAAG,EAKjD,CALqD,SAKxE,OAAOA,GAAY,CAAYya,EAAAA,EAAAA,0BAAAA,EAA2Bza,GAJ5D,OAIsE,AAJ/D,EAST,GAAIoY,GACF,IAAK,IAAM+C,KADO,AACA/C,EAChB,GAAI8C,EAAkC9C,CAAc,CAAC+C,EAAI,EACvD,CAD0D,CAD5B,KAEvB,CAEX,CAGF,OAAO,CACT,IA1BA,IAAA,EAA2C,EAAA,CAAlCV,AAAkC,CAAA,yBAAR,MAAiE,EAAzD,+DCW3C,EAGO,EAAA,CAFLxe,AAEK,CAAA,MAAuC,EAE9C,EAGEuC,CAHKd,CAGU,AAEf2d,CAAAA,CADAD,AACAC,IATmB,EAKnBrU,AAIgB,EADR,AAIV,EAAgC,AAH9BqU,CAGKC,CAAyB,AAVzB,CAUyB,CAAA,AADzB,EANK,EACV3d,GAAG,CAKS,AAEd,CANEa,CAQA+c,CAHmB,CAInBC,CAFAnE,AAEAmE,CAAAA,EAAe,GADU,EACzBA,CAEF,CANgC,CAMI,AAD7B,EAC6B,CAAA,AAA3B/X,CAA2B,IAJf,EACnB8X,CAG0E,CAC5E,EAAmC,EAAuB,CAAA,AAAjDxU,CAAiD,IAD9B,IAE5B,CAD0D,CAC5B,EAAkB,AAFZ,CAE3BsF,AAAuC,CAAA,GADrB,KAE3B,EAA6B,CAFM,CACb,AACyB,CAAtCC,AAAsC,CAAA,MADjB,CAH6B,CAK3D,EAAmC,CADd,CACc,CAAA,AAA1BE,CAA0B,KADN,GAE7B,EAAiC,EAAqB,CAA7CuM,AAA6C,CAAA,GAD3B,CAA4D,CACjC,GACtD,EAA2C,CAFR,CAEQ,CAAlCc,AAAkC,CAAA,CADlB,OAEzB,CAFiC,CAEI,EAAA,CAD6C,AACzEE,AAA4B,CAAA,QACrC,EAD+E,AACrC,CAFP,CAEe,CAAA,AAAzCmB,CAAyC,KADrB,AADc,GAG3C,EAAwC,EAAoB,CAAnDrd,AAAmD,AAFvB,CAEuB,QAoD5D,GApD4D,CAoDtDke,EAAAA,EACJT,EAtDwC,IACV,CAqD9BA,CACAS,MAtDsC,GAD4E,mDAuDtD,CAmBxDI,EAAiB,CACrB,SACA,SACA,OACA,QACA,MACA,QACA,IACA,IACD,CA2BD,SAASS,EAAuBP,CAAoB,CAAEQ,CAAsB,EAC1E,IAAML,EAAOH,EAAQI,qBAAqB,GAC1C,OAAOD,EAAKM,GAAG,EAAI,GAAKN,EAAKM,GAAG,EAAID,CACtC,CA0BA,MAAMM,UAAAA,EAAmCzf,OAAAA,CAAMsN,SAAS,CA4GtDoS,mBA5GuC1f,AA4GnB,CAClB,IAAI,CAAC2f,qBAAqB,EAC5B,CAEAC,oBAAqB,CAEf,IAAI,CAACnS,KAAK,CAACoS,iBAAiB,CAACC,KAAK,EACpC,AADsC,IAClC,CAACH,qBAAqB,EAE9B,CAEAhS,QAAS,CACP,OAAO,IAAI,CAACF,KAAK,CAACQ,QAAQ,AAC5B,mBAzHF,KAAA,IAAA,GAAA,IAAA,CACE0R,qBAAAA,CAAwB,KAEtB,GAAM,CAAEE,mBAAiB,aAAEzd,CAAW,CAAE,CAAG,IAAI,CAACqL,KAAK,CAErD,GAAIoS,EAAkBC,KAAK,CAAE,CAI3B,GAC4C,IAA1CD,EAAkBE,YAAY,CAACxd,MAAM,EACrC,CAACsd,EAAkBE,YAAY,CAACC,IAAI,CAAC,AAACC,GACpC7d,EAAY4c,KAAK,CAAC,CAAC1c,EAASqa,IAAAA,CAAAA,EAAAA,EAC1B/N,YAAAA,EAAatM,EAAS2d,CAAoB,CAACtD,EAAM,IAIrD,CADA,MAIF,IAAIuD,EAEiC,KAC/BZ,EAAeO,EAAkBP,YAAY,CAanD,GAXIA,IACFY,EA5CR,AA4CkBb,QADM,CA3CfA,AAAuBC,CAAoB,MAQhDpQ,QANF,AAAqB,OAAO,CAAxBoQ,EACKpQ,SAAS1H,IAAI,CAKpB0H,AAAwBoQ,OAAxBpQ,EAAAA,SAASqQ,cAAc,CAACD,EAAAA,CAAAA,CAAxBpQ,EAEAA,SAASsQ,iBAAiB,CAACF,EAAa,CAAC,EAAE,AAE/C,EAgCyCA,EAAAA,EAK/B,AAACY,IACHA,EA1GF,AAAJ,AAAsB,GAyGJ,KACF5B,KA1GmB,OAAxB1Y,OAA+B,KAMnC4Y,CADLH,IAA6DC,EA4DiC,SA5DjCA,AAAW,EAqG9C,IAAI,GAIxB,CAAE4B,CAAAA,EAxG0B3B,WAwGP4B,OAAAA,CAAM,CAC7B,EADiC,KAMnC,KAAO,CAAED,CAAAA,aAAmBE,WAAAA,CAAU,EA9F5C,AA8FkD1B,SA9FvBC,AAAlBD,CAAsC,EAI7C,GAAI,CAAC,SAAU,QAAQ,CAACvX,QAAQ,CAACyX,iBAAiBD,GAASE,QAAQ,EAOjE,CAPoE,MAO7D,EAKT,IAAMC,EAAOH,EAAQI,qBAAqB,GAC1C,OAAON,EAAeO,KAAK,CAAC,AAACC,GAAwB,IAAfH,CAAI,CAACG,EAAK,CAClD,EA4EoEiB,IAAU,CAUtE,GAAmC,MAAM,CAArCA,EAAQG,kBAAkB,CAC5B,OAEFH,EAAUA,EAAQG,kBAAkB,AACtC,CAGAR,EAAkBC,KAAK,EAAG,EAC1BD,EAAkBP,YAAY,CAAG,KACjCO,EAAkBE,YAAY,CAAG,EAAE,MAEnCjR,kBAAAA,EACE,KAEE,GAAIwQ,EAAc,YACdY,EAAwBI,cAAc,GAM1C,IAAMrR,EAAcC,SAASC,eAAe,CACtCgQ,EAAiBlQ,EAAYsR,YAAY,EAG3CrB,EAAuBgB,EAAwBf,KAQnDlQ,EAAYuR,SAAS,CAR+C,AAQ5C,EAGpB,AAACtB,EAAuBgB,EAAwBf,IAEhDe,EAAwBI,WAFyC,GAE3B,GAE5C,EACA,CAEEhR,gBAAiB,GACjBN,eAAgB6Q,EAAkB7Q,cAAc,AAClD,GAIF6Q,EAAkB7Q,cAAc,EAAG,EAGnCkR,EAAQO,KAAK,EACf,CACF,EAgBF,CAEA,SAASC,EAAsB,CAM9B,EAN8B,GAAA,CAC7Bte,aAAW,UACX6L,CAAQ,CAIT,CAN8B,EAOvB4M,EAAAA,CAAAA,EAAAA,EAAUvR,UAAAA,EAAAA,EAAWuU,yBAAAA,EAC3B,GAAI,CAAChD,EACH,MAAM,CADM,MACN,cAAuD,CAAvD,AAAIxa,MAAM,8CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAsD,GAG9D,MAAA,CAAA,EAAA,EACE,GAAA,EAACof,EADH,AACGA,CACCrd,YAAaA,EACbyd,kBAAmBhF,EAAQgF,iBAAiB,UAE3C5R,GAGP,CAKA,SAAS0S,EAAkB,CAU1B,EAV0B,GAAA,MACzB5e,CAAI,aACJK,CAAW,WACXwe,CAAS,CACTjd,KAAG,CAMJ,CAV0B,EAWnBkX,EAAUvR,CAAAA,EAAAA,EAAAA,UAAAA,EAAAA,EAAWuU,yBAAAA,EAC3B,GAAI,CAAChD,EACH,MAAM,CADM,MACN,cAAuD,CAAvD,AAAIxa,MAAM,8CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAsD,GAG9D,GAAM,CAAE0B,KAAM8e,CAAQ,CAAE,CAAGhG,EASrBiG,EACsB,OAA1BF,EAAUG,WAAW,CAAYH,EAAUG,WAAW,CAAGH,EAAUI,GAAG,CAKlEA,EAAAA,CAAAA,EAAAA,EAAWrD,gBAAAA,EAAiBiD,EAAUI,GAAG,CAAEF,GAM3CG,EACW,UAAf,OAAOD,GAA4B,OAARA,GAAoC,YAApB,OAAOA,EAAIjhB,IAAI,CAAK,CAAA,EAAA,EAC3DE,GAAAA,EAAI+gB,GACJA,EAEN,GAAI,CAACC,EAAa,CAMhB,IAAIC,EAAWN,EAAUM,QAAQ,CACjC,GAAIA,AAAa,SAAM,CAKrB,IAAMC,EAAcpD,AA/U1B,SAASA,EACPC,CAAgD,CAChDC,CAAiC,EAEjC,GAAID,EAAmB,CACrB,GAAM,CAAC1b,EAASiY,EAAiB,CAAGyD,EAC9BE,EAAsC,IAA7BF,EAAkBzb,MAAM,CAEvC,GAAA,CAAA,EAAA,EAAIqM,YAAAA,EAAaqP,CAAc,CAAC,EAAE,CAAE3b,IAC9B2b,CAAc,CAAC,EAAE,CAACE,CADsB,aACR,CAAC5D,GAAmB,CACtD,GAAI2D,EAAQ,CACV,IAAME,EAAUL,OACdpc,EACAsc,CAAc,CAAC,EAAE,CAAC1D,EAAiB,EAErC,MAAO,CACL0D,CAAc,CAAC,EAAE,CACjB,CACE,GAAGA,CAAc,CAAC,EAAE,CACpB,CAAC1D,EAAiB,CAAE,CAClB6D,CAAO,CAAC,EAAE,CACVA,CAAO,CAAC,EAAE,CACVA,CAAO,CAAC,EAAE,CACV,UACD,AACH,EACD,AACH,CAEA,MAAO,CACLH,CAAc,CAAC,EAAE,CACjB,CACE,GAAGA,CAAc,CAAC,EAAE,CACpB,CAAC1D,EAAiB,CAAEwD,EAClBC,EAAkB7b,KAAK,CAAC,GACxB8b,CAAc,CAAC,EAAE,CAAC1D,EAAiB,CAEvC,EAEJ,AADG,CAGP,CAEA,OAAO0D,CACT,EAmSyC,CAAC,MAAO7b,EAAY,CAAEye,GACnDO,EAAiB5D,CAAAA,EAAAA,EAAAA,iCAAAA,EAAkCqD,GACnDQ,EAAcC,KAAKC,GAAG,GAC5BX,EAAUM,QAAQ,CAAGA,EAAAA,CAAWnb,EAAAA,EAAAA,mBAAAA,EAC9B,IAAIlB,IAAIlB,EAAKmB,SAASC,MAAM,EAC5B,CACEkB,kBAAmBkb,EACnBjb,QAASkb,EAAiBvG,EAAQ3U,OAAO,CAAG,IAC9C,GACAnG,IAAI,CAAC,AAACyhB,IACN1gB,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,UACdX,uBAAAA,EAAwB,CACtBkB,KAAM9C,EAAAA,mBAAAA,CACNkjB,aAAcZ,iBACdW,cACAH,CACF,EACF,GAEOG,SAITvhB,GAAAA,EAAIihB,EACN,MAGAjhB,GAAAA,EAAAA,EAAIoJ,kBAAAA,CACN,CAmBA,MAdE,CAcKqY,AAdL,EAAA,EAAA,GAAA,EAAA,EAAC/H,QAD2E,WAC3EA,CAAoBgI,QAAQ,CAAA,CAC3BzY,MAAO,CACL4R,WAAY/Y,EACZ6f,gBAAiBhB,EACjBiB,kBAAmBzf,EAGnBuB,IAAKA,CACP,WAECsd,GAKP,CAMA,SAASa,EAAgB,CAMxB,EANwB,IAenBE,EAfmB,SACvBD,CAAO,UACP9T,CAAQ,CAIT,CANwB,EA2BvB,GALE+T,CAKEA,CAViB,UAAnB,OAAOD,GACK,OAAZA,GACiC,YAAjC,AACA,OADQA,EAAgBhiB,IAAI,CAGRE,CAAAA,EAAAA,EAAAA,GAAAA,EADM8hB,AACFE,GAEJF,EAGC,CACrB,IAAMG,EAAaF,CAAiB,CAAC,EAAE,CACjCG,EAAgBH,CAAiB,CAAC,EAAE,CACpCI,EAAiBJ,CAAiB,CAAC,EAAE,CAC3C,MAAA,CAAA,AACE,EAAA,EAAA,GAAA,EAAA,EADF,AACGtE,QAAAA,CAAAA,CACC2E,SAAAA,CAAAA,EAAAA,EACE,IAAA,EAAA,CADFA,CACE,QAAA,CAAA,WACGF,EACAC,EACAF,aAHH,CAODjU,GAGP,CAEA,MAAA,CAAA,EAAA,EAAO,GAAA,EAAA,EAAA,AAAP,QAAO,CAAA,UAAGA,GACZ,CAMe,SAASqU,EAAkB,CAsBzC,EA7BQ,AAOiC,GAAA,mBACxCC,CAAiB,OACjBna,CAAK,aACLwF,CAAW,cACXC,CAAY,gBACZ2U,CAAc,iBACdC,CAAe,UACfC,CAAQ,UACR5R,CAAQ,WACRC,CAAS,CACTE,cAAY,CAYb,CAtByC,EAuBlC4J,EAAAA,CAAUvR,EAAAA,EAAAA,UAAAA,EAAAA,EAAWqQ,mBAAAA,EAC3B,GAAI,CAACkB,EACH,MAAM,CADM,MACN,cAA2D,CAA3D,AAAIxa,MAAM,kDAAV,oBAAA,OAAA,kBAAA,eAAA,EAA0D,GAGlE,GAAM,CAAEya,YAAU,iBAAE8G,CAAe,mBAAEC,CAAiB,KAAEle,CAAG,CAAE,CAAGkX,EAI1D8H,EAAuBf,EAAgBlH,cAAc,CACvDkI,EAAaD,EAAqB1b,GAAG,CAACsb,GAGrCK,IACHA,EAAa,IAAIC,EADF,EAEfF,EAAqBtQ,GAAG,CAACkQ,EAAmBK,IAK9C,IAAME,EAAoBhI,CAAU,CAAC,EAAE,CACjC/Y,EAAO+Y,CAAU,CAAC,EAAE,CAACyH,EAAkB,CACvCQ,EAAchhB,CAAI,CAAC,EAAE,CAErBK,EACkB,OAElB,AACA,AAHJyf,EAII,CAACU,EAAkB,CACnBV,EAAkBvE,MAAM,CAAC,CAACwF,EAAmBP,EAAkB,EAY/DS,EAAAA,CAAAA,EAAAA,EAAW3G,QAd0B,YAc1BA,EAAqB0G,GAChCE,EAAW5G,CAAAA,EAAAA,EAAAA,SAhB2D,WAgB3DA,EAAqB0G,GAAa,GAG/CnC,EAAYgC,CAHyC,CAG9B3b,GAAG,CAAC+b,GAC/B,QAAkBrhB,GAJ0D,CAIxEif,EAAyB,CAG3B,IAAMsC,EAAkC,CACtChC,SAAU,KACVF,IAAK,KACLD,YAAa,KACb9e,KAAM,KACNkhB,aAAc,KACdzI,eAAgB,IAAImI,IACpBd,QAAS,KACTV,YAAa,CAAC,CAChB,EAGAT,EAAYsC,EACZN,EAAWvQ,GAAG,CAAC2Q,EAAUE,EAC3B,CAoBA,IAAMlB,EAAoBJ,EAAgBG,OAAO,CAEjD,MAAA,CAAA,EAAA,EACE,IAAA,EAACjE,CADH,CACGA,eAAAA,CAAgB6D,QAAQ,CAAA,CAEvBzY,MAAAA,CAAAA,EAAAA,EACE,GAAA,EAACwX,EAAAA,AADHxX,CACyB9G,YAAaA,WAClC,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,AAACuM,aAAAA,CAAAA,CACCZ,eAAgB3F,EAChBwF,YAAaA,EACbC,aAAcA,WAEd,CAAA,EAAA,EAAA,GAAA,EAACiU,EAAAA,AAAD,CAAiBC,QAASC,WACxB,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,AAAC7F,0BAAAA,CAAAA,CACCrL,SAAUA,EACVC,UAAWA,EACXE,aAAcA,WAEd,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,AAACoK,gBAAAA,CAAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAACsF,EAAAA,AAAD,CACEhd,IAAKA,EACL5B,KAAMA,EACN6e,UAAWA,EACXxe,YAAaA,wBAS1BogB,EACAC,EACAC,IA9BIO,EAiCX,0FCtnBA,EAAmD,CAA5CjjB,CAA4C,CAAA,CAAA,MAAnCsJ,EAChB,EAAgC,EAAA,CAAvBwU,AAAuB,CAAA,EADN,MAGX,EAH6B,IACpB,CAA4D,EAE5DsF,CAH2B,GAIjD,CAH8B,GAGxBnV,EAAAA,CAAAA,EAAAA,EAAW3E,UAAAA,EAAAA,EAAWwU,eAAAA,EAC5B,MAAA,CAAA,EAAA,EAAO,GAAA,EAAA,EAAA,AAAP,QAAO,CAAA,UAAG7P,GACZ,eADS,+DCPF,OAAMoV,UAAuBhjB,MAClC6N,YAAYgJ,CAAe,CAAElR,CAAsB,CAAE,CACnD,KAAK,CACF,eAAakR,CAAAA,CAAQ/R,QAAQ,CAAC,KAAO+R,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DlR,GAEF,IAAI,CAACiW,IAAI,CAAG,gBACd,CACF,gKCHA,IAAMqH,EAA+B,6BAE9B,SAASC,EAA6BC,CAAc,CAAEC,CAAY,SACvE,AAAIH,EAA6BzK,IAAI,CAAC4K,GAC5B,IADmC,AAC/BD,EAAO,IAAGC,EAAK,IAErB,IAAID,EAAO,IAAGnd,KAAKC,SAAS,CAACmd,GAAM,IAC7C,CAEO,SAASC,EACdF,CAAc,CACdC,CAAY,EAEZ,IAAME,EAAkBtd,KAAKC,SAAS,CAACmd,GACvC,MAAQ,gBAAgBD,EAAO,KAAIG,EAAgB,QAASA,EAAgB,OAAMH,EAAO,eAC3F,CAEO,IAAMI,EAAsB,IAAIxZ,IAAI,CACzC,iBACA,gBACA,uBACA,WACA,UACA,iBAIA,OACA,QACA,UAIA,SAGA,cAIA,SACA,WACA,aACD,EAAC,+FC9CF,IAAA,EAAoC,EAAA,CAA3BwZ,AAA2B,CAAA,KAAsC,GAG1E,IAAMC,EAAqB,IAAIhS,AAHH,QAKrB,AAL6B,SAKpBiS,EACdC,CAAoC,EAEpC,IAAMC,EAAqBH,EAAmB5c,GAAG,CAAC8c,GAClD,GAAIC,EACF,OAAOA,EAMT,IAAMlkB,EAAUoB,GAPQ,KAOAC,OAAO,CAAC4iB,GAYhC,OAXAF,EAAmBxR,GAAG,CAAC0R,EAAwBjkB,GAE/CuK,OAAO4Z,IAAI,CAACF,GAAwBG,OAAO,CAAET,AAAD,IAC1C,EAAIG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAIxB3jB,CAAe,CAJgB,AAIf2jB,EAAK,CAAGM,CAAsB,CAACN,EAAAA,AAAK,CAE1D,GAEO3jB,CACT,mGC/BO,IAAMqkB,EAMLzf,EAAQ,CAAA,AAFR0f,CAEQ,IAAA,IACRN,iBAH8C,MAHpDnjB,QAAQC,AAM6B,CAAA,EAN1B,CAACC,QAAQ,KAAK,gBACrB,AACE6D,QAAQ,iDCFhB,IAAA,EAAoC,EAAA,CAA3Bkf,AAA2B,CAAA,KAAsC,GAG1E,IAAMS,EAAe,IAHO,AAGHxS,QAHW,AAK7B,SAASyS,EACdC,CAAwB,EAExB,IAAMC,EAAeH,EAAapd,GAAG,CAACsd,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZ1kB,EAAUoB,QAAQC,OAAO,CAACojB,GAYhC,OAXAF,EAAahS,GAAG,CAACkS,EAAkBzkB,GAEnCuK,OAAO4Z,IAAI,CAACM,GAAkBL,OAAO,CAAC,AAACT,IACrC,EAAIG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAIxB3jB,CAAe,CAJgB,AAIf2jB,EAAK,CAAGc,CAAgB,CAACd,EAAAA,AAAK,CAEpD,GAEO3jB,CACT,8FC3BO,IAAM2kB,EAKL/f,EAAQ,CAAA,CAAA,IAAA,IACR4f,aAHkD,IAFxD3jB,QAK+B,AALvBC,CAKuB,EALpB,CAACC,QAAQ,KAAK,gBACrB,AAAC6D,QAAQ,wBACNggB,aCHF,OAAMC,EACX,OAAO1d,IACLuc,CAAS,CACTC,CAAqB,CACrBmB,CAAiB,CACZ,CACL,IAAM1b,EAAQ2b,QAAQ5d,GAAG,CAACuc,EAAQC,EAAMmB,SACxC,AAAqB,YAAjB,AAA6B,OAAtB1b,EACFA,EAAMgJ,IAAI,CAACsR,GAGbta,CACT,CAEA,OAAOmJ,IACLmR,CAAS,CACTC,CAAqB,CACrBva,CAAU,CACV0b,CAAa,CACJ,CACT,OAAOC,QAAQxS,GAAG,CAACmR,EAAQC,EAAMva,EAAO0b,EAC1C,CAEA,OAAOha,IAAsB4Y,CAAS,CAAEC,CAAqB,CAAW,CACtE,OAAOoB,QAAQja,GAAG,CAAC4Y,EAAQC,EAC7B,CAEA,OAAOqB,eACLtB,CAAS,CACTC,CAAqB,CACZ,CACT,OAAOoB,QAAQC,cAAc,CAACtB,EAAQC,EACxC,CACF,6GCjCA,IAAA,EAA8B,EAAA,CAAA,CAAA,EAAlBzjB,MAEZ,IAAM+kB,CAFiB,CAEqB,CAAEC,MAFhB,EAEyB,IAAK,EAGtDC,EACmB,YAAvB,OAAA,EAAOjlB,KAAW,CAAK,AAAVilB,EACTjlB,KAAW,CAALilB,AACN,AAAClW,GAA+BA,EAKhCmW,EAEF/c,QAAQid,IAAI,CA0BT,EA5BgBzkB,OA4BP2kB,CA5Be1kB,CA6B7B2kB,CAAoC,CA7BJ,CAACJ,AA+BjC,OAAO,SAASK,AAAgB,CA/BkB,EA+BfC,CA9BjCtd,AA8B2C,EAkBzC+c,EAjBcK,IA/BRnd,CA+BsBqd,GAmBhC,CAlDe,AAmDjB,CA9C+BR,EAE7B,AAACxH,CAyCkBvG,GAxCjB,GAAI,CACFgO,EAAeH,EAASC,OAAO,CACjC,QAAU,CACRD,EAASC,OAAO,CAAG,IACrB,CACF,6BAP0E,uNCpB5E,IAAA,EAAsC,EAAA,CAA7B7R,AAA6B,CAAA,QACtC,EAAsC,EAAA,CAAA,AAA7BuS,CAA6B,MADR,AAA2D,EAIlF,MAJ+B,GAItBC,CAHuE,CAIrFrQ,CAJ4B,AAIf,CACb3D,CAAkB,EAElB,IAPoC,EAO9B,OAAA,cAEL,CAFK,IAAA,EAAIwB,qBAAAA,CACR,CAAC,MAAM,EAAEmC,EAAM,iDAAiD,EAAE3D,EAAW,0HAA0H,CAAC,EADpM,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,CAEO,SAASiU,EACdtQ,CAAa,CACb3D,CAAkB,EAElB,MAAM,OAAA,cAEL,CAFK,IAAA,EAAIwB,qBAAAA,CACR,CAAC,MAAM,EAAEmC,EAAM,4EAA4E,EAAE3D,EAAW,0HAA0H,CAAC,EAD/N,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,CAEO,SAASkU,EACdnc,CAAoB,EAEpB,IAAMtB,EAAQ,OAAA,cAEb,CAFa,AAAI/H,MAChB,CAAC,MAAM,EAAEqJ,EAAU4L,KAAK,CAAC,oVAAoV,CAAC,EADlW,oBAAA,OAAA,mBAAA,gBAAA,CAEd,EAIA,OAFA5L,EAAUoc,iBAAiB,GAAK1d,EAE1BA,CACR,CAEO,SAAS2d,IACd,IAAMC,EAAAA,EAAiBN,qBAAAA,CAAsB/b,QAAQ,GACrD,MAAOqc,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBC,kBAAAA,AAAkB,IAAK,QAChD,kRCnCA,IAAA,EAA+B,EAAA,CAAtBtB,AAAsB,CAAA,EAAwC,MACvE,EACElO,EACAV,CAHqB,AAErBU,AACAV,CAAAA,EAD2C,EAC3CA,GAH6B,CAU/B,EAMO,EAAA,CALL9B,AAKK,CAAA,EAb2B,EAChCsB,IAaF,EAA+B,EAAkC,CAAxD8N,AAAwD,CAAA,EADV,GALjC,GAOtB,EAAmC,AAdb,EACpBrN,AAa6D,CAFxD,AACgB,AACdlE,AAAsD,CAAA,EADE,KAAlC,CAE/B,EAASwT,EAAmD,CAAA,AAAnDA,CAAmD,AADG,EACX,CADzB,KAE3B,EAD4D,AAG1D5B,CAJiC,CAIjCA,CADAH,AACAG,CAAAA,CAjB+B,CAiBE,CAhBjCxL,CAiBA0L,IAEF,EACEgC,EACAC,CADAD,AACAC,CAAAA,QAkCK,CAtCc,CAjBE,EAoBgC,AAnBrDhP,CAc4B,CAM5BgP,AAR6G,CAG7GnC,EAEK,AAqCSwC,EACdnC,CAAoC,CACpCra,CAAoB,EAEpB,IAAMwL,EAtC8B,AAsC9BA,EAAgBjB,MArCjB,UAAS,EArBwB,EA0DhBA,CAAqBtK,GAzCA,EAhBtC,GAyD8C,GACnD,GAAIuL,EADkBjB,AAEpB,OAAQiB,EAAc7T,IADL,AACS,EACxB,IAAK,IA5D6B,QA6DlC,IAAK,gBACL,IAAK,mBACH,OAAO8kB,EAA4Bzc,EAAWwL,EAGlD,CAEF,OAAOkR,EAAyBrC,EAAwBra,EAC1D,CAhDkC,EAAqB,CAAA,CAAA,MAAA,EAmDhD,IAAM2c,EACXC,EAEK,SAASA,EACdvC,CAAoC,CACpCra,CAAoB,EAEpB,IAAMwL,EAAAA,EAAgBjB,YANe,QAMfA,CAAqBtK,QAAQ,GACnD,GAAIuL,EACF,OAAQA,EAAc7T,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAO8kB,EAA4Bzc,EAAWwL,EAGlD,CAEF,OAAOkR,EAAyBrC,EAAwBra,EAC1D,CAEO,SAAS6c,EACd7c,CAAoB,EAEpB,GAAIA,EAAU0L,WAAW,CAGvB,CAHyB,MAGlBlU,QAAQC,OAAO,CAAC,CAAC,GAG1B,IAAM2U,EAAAA,EAAiB7B,oBAAAA,CAAqBtK,QAAQ,UACpD,AAAImM,GAA0C,AAAxBA,aAAqC,GAAtBzU,IAAI,CAIvC,CAAA,EAAA,EAAOyQ,kBAAAA,EAAmBgE,EAAesC,YAAY,CAAE,kBAKlDlX,QAAQC,OAAO,CAAC,CAAC,EAC1B,CAEA,SAASglB,EACPzc,CAAoB,CACpBoM,CAA8B,SAE9B,AAAIpM,EAAU0L,WAAW,CAGhBlU,CAHkB,OAGVC,OAAO,CAAC,CAAC,GAGE,aAAa,CAArC2U,EAAezU,IAAI,CAEdmlB,AAwCX,SAASA,AACPlR,CAAa,CACbQ,CAAoC,EAEpC,IAAMkO,EAAqBH,EAAmB5c,GAAG,CAAC6O,GAClD,GAAIkO,EACF,OAAOA,EAGT,IAAMlkB,EAAAA,AAAUgS,GAAAA,AAJQ,EAIRA,kBAAAA,EACdgE,EAAesC,YAAY,CAC3B,kBAGIwO,EAAiB,IAAIC,MAAM/mB,EAAS,CACxCmH,IAAIuc,CAAM,CAAEC,CAAI,CAAEmB,CAAQ,EACxB,GAAIva,OAAOyc,MAAM,CAAChnB,EAAS2jB,GAIzB,IAJgC,GAIhC,EAAOkB,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,GAG1C,OAAQnB,GACN,CAJOkB,GAIF,OAIH,WADAzM,qBAAAA,EADE,AACoBvG,wDAAYmE,GAC3B6O,EAAAA,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,EAE1C,KAAK,SAIH,OADA1M,EAAAA,EAAAA,qBAAAA,EAAsBvG,AADpB,yDACgCmE,GAClC,EAAO6O,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,EAG1C,SACE,GAAoB,UAAhB,OAAOnB,GAAqB,CAACG,EAAAA,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,GAAO,CAC9D,IAAM9R,EAAAA,CAAAA,EAAAA,EAAa4R,4BAAAA,EACjB,eACAE,GAEIrb,EAAQ2e,EAAwBzR,EAAO3D,QAC7C8E,2CAAAA,EACEnB,EACA3D,EACAvJ,EACA0N,EAEJ,CACA,OAAA,EAAO6O,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,EAE5C,CACF,EACAha,IAAI4Y,CAAM,CAAEC,CAAI,EAKd,GAAoB,UAAhB,OAAOA,EAAmB,CAC5B,IAAM9R,EAAAA,CAAAA,EAAAA,EAAa+R,iCAAAA,EACjB,eACAD,GAEIrb,EAAQ2e,EAAwBzR,EAAO3D,QAC7C8E,2CAAAA,EACEnB,EACA3D,EACAvJ,EACA0N,EAEJ,CACA,OAAO6O,EAAAA,cAAAA,CAAe/Z,GAAG,CAAC4Y,EAAQC,EACpC,EACAuD,UACE,IAAMrV,EACJ,+DACIvJ,EAAQ2e,EAAwBzR,EAAO3D,QAC7C8E,qCAAAA,MAAAA,EACEnB,EACA3D,EACAvJ,EACA0N,EAEJ,CACF,GAGA,OADA+N,EAAmBxR,GAAG,CAACyD,EAAgB8Q,GAChCA,CACT,EAlI0Cld,EAAU4L,KAAK,CAAEQ,GAMlD2Q,AA8HT,SAASA,AACP/c,CAAoB,CACpBoM,CAAwD,EAExD,IAAMkO,EAAqBH,EAAmB5c,GAAG,CAACyC,GAClD,GAAIsa,EACF,OAAOA,EAOT,IAAMlkB,EAAUoB,GARQ,KAQAC,OAAO,CAJA,AAIC4iB,CAJA,GAM1B6C,EAAiB,IAAIC,MAAM/mB,EAAS,CACxCmH,IAAIuc,CAAM,CAAEC,CAAI,CAAEmB,CAAQ,EACxB,GAAIva,OAAOyc,MAAM,CAAChnB,EAAS2jB,GAIzB,IAJgC,GAIhC,EAAOkB,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,GAG1C,OAAQnB,GACN,IAAK,OAAQ,CACX,IAAM9R,EACJ,wDACEjI,EAAU2L,kBAAkB,EAAE,IAChCuQ,qDAAAA,EACElc,EAAU4L,KAAK,CACf3D,GAEOmE,AAAwB,iBAAiB,GAA1BzU,IAAI,MAE5BkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,KAIhCO,EAAAA,gCAAAA,EACEpE,EACAjI,EACAoM,GAGJ,MACF,CACA,IAAK,SAAU,CACb,IAAMnE,EACJ,yDACEjI,EAAU2L,kBAAkB,EAAE,IAChCuQ,qDAAAA,EACElc,EAAU4L,KAAK,CACf3D,GAE+B,iBAAiB,CAAzCmE,EAAezU,IAAI,MAE5BkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,OAIhCO,gCAAAA,EACEpE,EACAjI,EACAoM,GAGJ,MACF,CACA,QACE,GAAoB,UAAhB,OAAO2N,GAAqB,CAAA,EAACG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,GAAO,CAC9D,IAAM9R,EAAAA,CAAAA,EAAAA,EAAa4R,4BAAAA,EACjB,eACAE,GAEE/Z,EAAU2L,kBAAkB,CAC9BuQ,CAAAA,AADgC,EAChCA,EAAAA,qDAAAA,EACElc,EAAU4L,KAAK,CACf3D,GAE+B,iBAAiB,CAAzCmE,EAAezU,IAAI,MAE5BkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,GAIhCO,EAAAA,EAAAA,gCAAAA,EACEpE,EACAjI,EACAoM,EAGN,CACA,OAAA,EAAO6O,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,EAE5C,CACF,EACAha,IAAI4Y,CAAM,CAAEC,CAAI,EAKd,GAAoB,UAAhB,OAAOA,EAAmB,CAC5B,IAAM9R,EAAa+R,CAAAA,EAAAA,EAAAA,iCAAAA,EACjB,eACAD,GAsBF,OApBI/Z,EAAU2L,kBAAkB,EAAE,IAChCuQ,qDAAAA,EACElc,EAAU4L,KAAK,CACf3D,GAE+B,iBAAiB,CAAzCmE,EAAezU,IAAI,MAE5BkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,GAHjBD,YAGgC,OAIhCQ,gCAAAA,EACEpE,EACAjI,EACAoM,IAGG,CACT,CACA,OAAO6O,EAAAA,cAAAA,CAAe/Z,GAAG,CAAC4Y,EAAQC,EACpC,EACAuD,UACE,IAAMrV,EACJ,+DACEjI,EAAU2L,kBAAkB,EAAE,IAChCuQ,qDAAAA,EACElc,EAAU4L,KAAK,CACf3D,GAE+B,iBAAiB,CAAzCmE,EAAezU,IAAI,MAE5BkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,EAIhCO,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCpE,EAAYjI,EAAWoM,EAE5D,CACF,GAGA,OADA+N,EAAmBxR,GAAG,CAAC3I,EAAWkd,GAC3BA,CACT,EAlSwCld,EAAWoM,EACnD,CAEA,SAASsQ,EACPrC,CAAoC,CACpCra,CAAoB,SAEpB,AAAIA,EAAU0L,WAAW,CAGhBlU,CAHkB,OAGVC,OAAO,CAAC,CAAC,GAWf2iB,AAwUb,SAASA,AACPC,CAAoC,CACpC7W,CAAgB,EAEhB,IAAM8W,EAAqBH,EAAmB5c,GAAG,CAAC8c,GAClD,GAAIC,EACF,OAAOA,EAMT,IAAMlkB,EAAUoB,GAPQ,KAOAC,OAAO,CAAC4iB,GAwBhC,OAvBAF,EAAmBxR,GAAG,CAAC0R,EAAwBjkB,GAE/CuK,OAAO4Z,IAAI,CAACF,GAAwBG,OAAO,CAAC,AAACT,IACvC,EAACG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,IAC3BpZ,GADkC,IAC3B6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCxc,MACE,IAAMiO,EAAAA,EAAgBjB,oBAAAA,CAAqBtK,QAAQ,GAEnD,SADAqM,EAAAA,+BAAAA,EAAgC9I,EAAOgI,GAChC6O,CAAsB,CAACN,EAAK,AACrC,EACApR,IAAInJ,CAAK,EACPmB,OAAO6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCva,QACAie,UAAU,EACVC,WAAY,EACd,EACF,EACAA,YAAY,EACZC,cAAc,CAChB,EAEJ,GAEOvnB,CACT,EA7W6CikB,EAAwBra,EAGrE,CAGA,IAAMma,EAAqB,IAAIhS,QAEzB8U,EAAgC,IAAI9U,QA4QnC,SAASoV,EACdvd,CAAoB,EAEpB,IAAMsa,EAAqB2C,EAA8B1f,GAAG,CAACyC,GAC7D,GAAIsa,EACF,OAAOA,EAGT,IAAMlkB,EAAUoB,GAJQ,KAIAC,OAAO,CAAC,CAAC,GAE3BylB,EAAiB,IAAIC,MAAM/mB,EAAS,KACxCmH,CAAIuc,EAAQC,EAAMmB,EAAR,AAAM,GACVva,GADoB,IACbyc,MAAM,CAAChnB,EAAS2jB,IAST,GATgB,OAShC,EACCA,KADMA,GACG,SAATA,CAAmB,EAAA,EAACG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAAI,GACjD,CACAoC,oCAAAA,EAAqCnc,GAPrC,EAAOib,cAAAA,CAAe1d,GAAG,CAACuc,EAAQC,EAAMmB,QAY5Cha,CAAI4Y,EAAQC,IAAI,AAAN,CAMU,UAAhB,EACCA,KADMA,GACG,SAATA,CAAmB,EAAA,EAACG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAAI,EAEjDoC,CADA,CACAA,oCAAAA,EAAqCnc,GAGvC,EAAOib,cAAAA,CAAe/Z,GAAG,CAAC4Y,EAAQC,IAEpCuD,eACEnB,oCAAAA,EAAqCnc,EACvC,CACF,GAGA,OADAid,EAA8BtU,GAAG,CAAC3I,EAAWkd,GACtCA,CACT,CA4NA,IAAMoB,EAAAA,CAAAA,EAAAA,EAAoB1C,2CAAAA,EACxByB,GAGIgB,EAAAA,CAAAA,EAAAA,EACJzC,2CAAAA,EAA4C2C,AAc9C,SAASA,AACP3S,CAAyB,CACzB3D,CAAkB,CAClBmW,CAAgC,EAEhC,IAAMpd,EAAS4K,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAON,CAPUjV,AAAJ,MACL,CAAA,EAAGqK,EAAO,KAAK,EAAEiH,EAIf,SAJ0B,EAAE,wLAI5B,EAAGuW,AAKT,SAASA,AAA4BC,CAAyB,EAC5D,OAAQA,EAAW5lB,MAAM,EACvB,KAAK,EACH,MAAM,OAAA,cAEL,CAFK,IAAA,EAAI8gB,cAAAA,CACR,uFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAE8E,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC/B,MAAK,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACxD,SAAS,CACP,IAAInV,EAAc,GAClB,IAAK,IAAI3P,EAAI,EAAGA,EAAI8kB,EAAW5lB,MAAM,CAAG,EAAGc,IAAK,AAC9C2P,GAAe,CAAC,EAAE,EAAEmV,CAAU,CAAC9kB,EAAE,CAAC,IAAI,CAAC,CAGzC,OAAO2P,AADPA,EAAe,CAAC,QAAQ,EAAEmV,CAAU,CAACA,EAAW5lB,MAAM,CAAG,EAAE,CAAC,EAAE,CAAC,AAEjE,CACF,CACF,EAxBqCulB,GAAmB,gEAAE,CAAC,AAJxB,EAD1B,CAEH,AAIA,CAAC,AAJA,kBAFE,OAAA,iBAAA,gBAAA,CAOP,EACF,CAFqE,CAAC,CAxBtE,AAoBuE,CAAC,GAClE,CAAC,IArBEf,EACPzR,CAAyB,CACzB3D,CAAkB,EAElB,IAAMjH,EAAS4K,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,CAgB8D,CAAC,GAClE,CAAC,QAbJ,CAJUjV,AAAJ,MACL,CAAA,EAAGqK,EAAO,KAAK,EAAEiH,EAAW,oCAgBwC,CAAC,GACnE,wFAjB4B,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,gBAAA,CAIP,EACF,CAHuE,CAAC,GAClE,CAAC,8DAA8D,CAAC,6KCltBvC,EAAA,CAAA,CAAA,EAAwC,MAAvE,IACA,EACE8E,EACAV,CAAAA,AADAU,AAFOkO,CAGP5O,EAD2C,EAC3CA,IAKF,EAMO,EAAA,CALL9B,AAKK,AAdgB,CAchB,EAX2B,EAChCsB,GAJ6B,CAe/B,EAA+B,EAAkC,CAAxD8N,AAAwD,CAAA,EADV,GALjC,GAOtB,EAZsB,AAcpBO,EAbA/M,AAcK,CAJgB,AAErB0M,AAEK,AALA,CAKA,EAJ0D,KAAlC,CAGV,AAErB,EAAmC,EAA4B,CAAA,AAAtDzR,CAAsD,EADxD,MAEP,EAASwT,EAAmD,CAJ9B,AAIrBA,AAAmD,CADG,AACH,CAH1D1B,CAGkD,CADzB,IADkB,CAoCtC,AAlDiC,EAgBoB,CADzB,KAd5B,CAiDSwE,EACd7D,CAAwB,CACxB7a,CAAoB,MA6HpBA,EA3HA,IAAMwL,EAAAA,CA2Hc,CA3HEjB,IAtCuF,QAfvE,QAqDhBA,CAAqBtK,QAAQ,GACnD,GAAIuL,EACF,OAAQA,EAAc7T,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOgnB,EAAsB9D,EAAkB7a,EAAWwL,EAG9D,CAEF,OAAOoT,EAAqC5e,IAAlB6a,EAC5B,CAjDkC,EAAqB,CAAA,CAAA,MAAA,EAqDhD,IAAMgE,EAAgCC,EAGtC,SAASC,EACdlE,CAAwB,CACxB7a,CAAoB,QAEpB,IAAMwL,EAAAA,EAAgBjB,GAPuD,iBAOvDA,CAAqBtK,QAAQ,GACnD,GAAIuL,EACF,OAAQA,EAAc7T,IAAI,AADT,EAEf,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOgnB,EAAsB9D,EAAkB7a,EAAWwL,EAG9D,CAEF,OAAOoT,EAAqC5e,EAiGnC4a,EAjGiBC,EAC5B,CAEO,SAASiE,EACdjE,CAAwB,CACxB7a,CAAoB,OA4Fe6a,CA1FnC,IAAMrP,EAAgBjB,EAAAA,oBAAAA,CAAqBtK,QAAQ,GACnD,GAAIuL,EACF,OAAQA,EAAc7T,IAAI,AADT,EAEf,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOgnB,EAAsB9D,EAAkB7a,EAAWwL,EAG9D,CAEF,OAAOoT,EAAqC5e,IAAlB6a,EAC5B,CAEO,SAASmE,EACdnE,CAAwB,CACxB7a,CAAoB,EAEpB,IAAMoM,EAAAA,EAAiB7B,oBAAAA,CAAqBtK,QAAQ,GACpD,GAAImM,GAAkBA,AAAwB,gBAATzU,IAAI,CAAkB,CACzD,IAAMsnB,EAAiBjf,EAAUE,mBAAmB,CACpD,GAAI+e,GACF,IAAK,IAAIlL,KAAO8G,AADE,EAEhB,GAAIoE,EAAe/d,GAAG,CAAC6S,GAIrB,GAJ2B,AADG,GAK9B,CAAA,EAAA,EAAO3L,kBAAAA,EAAmBgE,EAAesC,YAAY,CAAE,WAE3D,CAEJ,CAIA,OAAOlX,QAAQC,OAAO,CAACojB,EACzB,CAEA,SAAS8D,EACP9D,CAAwB,CACxB7a,CAAoB,CACpBoM,CAA8B,EAE9B,IAAM6S,EAAiBjf,EAAUE,mBAAmB,CACpD,GAAI+e,EAAgB,CAClB,IAAIC,GAAwB,EAC5B,IAAK,IAAMnL,KAAO8G,EAChB,GAAIoE,EAAe/d,GAAG,CAAC6S,GAAM,CAC3BmL,EAFgC,AAER,GACxB,KACF,CAGF,GAAIA,QAE0B,AAA5B,aAAyC,CAArC9S,CAFqB,CAENzU,IAAI,CAEdwnB,AAwCf,SAASA,AACPtE,CAAwB,CACxBjP,CAAa,CACbQ,CAAoC,EAEpC,IAAM0O,EAAeH,EAAapd,GAAG,CAACsd,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZ1kB,EAAUgS,CAAAA,EAAAA,EAAAA,kBAAAA,EACdgE,EAAesC,YAAY,CAC3B,YAiCF,OA/BAiM,EAAahS,GAAG,CAACkS,EAAkBzkB,GAEnCuK,OAAO4Z,IAAI,CAACM,GAAkBL,OAAO,CAAC,AAACT,IACrC,EAAIG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,IAI1BpZ,GAJiC,IAI1B6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCxc,MACE,IAAM0K,EAAAA,CAAAA,EAAAA,EAAa4R,4BAAAA,EAA6B,SAAUE,GACpDrb,EAAQ2gB,EAAwBzT,EAAO3D,QAC7C8E,2CAAAA,EACEnB,EACA3D,EACAvJ,EACA0N,EAEJ,EACAzD,IAAIuV,CAAQ,EACVvd,OAAO6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCva,MAAO0e,EACPT,UAAU,EACVC,YAAY,CACd,EACF,EACAA,YAAY,EACZC,aAAc,EAChB,EAEJ,GAEOvnB,CACT,EArFUykB,EACA7a,EAAU4L,KAAK,CACfQ,GAOGgT,AA8Eb,SAASA,AACPvE,CAAwB,CACxBoE,CAAmC,CACnCjf,CAAoB,CACpBoM,CAAwD,EAExD,IAAM0O,EAAeH,EAAapd,GAAG,CAACsd,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZwE,EAAsB,CAAE,GAAGzE,CAAgB,AAAC,EAK5CzkB,EAAUoB,QAAQC,OAAO,CAAC6nB,GA6EhC,OA5EA3E,EAAahS,GAAG,CAACkS,EAAkBzkB,GAEnCuK,OAAO4Z,IAAI,CAACM,GAAkBL,OAAO,CAAC,AAACT,IACjCG,EAAAA,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAItBkF,EAJ6B,AAId/d,GAAG,CAAC6Y,IACrBpZ,GAD4B,IACrB6c,cAAc,CAAC8B,EAAqBvF,EAAM,CAC/Cxc,MACE,IAAM0K,EAAAA,CAAAA,EAAAA,EAAa4R,4BAAAA,EAA6B,SAAUE,GAO9B,iBAAiB,CAAzC3N,EAAezU,IAAI,IAErBkU,EAAAA,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,OAIhCO,gCAAAA,EACEpE,EACAjI,EACAoM,EAGN,EACAsR,WAAY,EACd,GACA/c,OAAO6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCxc,MACE,IAAM0K,EAAAA,CAAAA,EAAAA,EAAa4R,4BAAAA,EAA6B,SAAUE,EAOtD3N,CAAwB,iBAAiB,GAA1BzU,IAAI,MAErBkU,oBAAAA,EACE7L,EAAU4L,KAAK,CACf3D,EACAmE,EAAeN,eAAe,OAIhCO,gCAAAA,EACEpE,EACAjI,EACAoM,EAGN,EACAzD,IAAIuV,CAAQ,EACVvd,OAAO6c,cAAc,CAACpnB,EAAS2jB,EAAM,CACnCva,MAAO0e,EACPT,UAAU,EACVC,YAAY,CACd,EACF,EACAA,YAAY,EACZC,cAAc,CAChB,IAEEvnB,CAAe,CAAC2jB,EAAK,CAAGc,CAAgB,CAACd,EAAK,CAGtD,GAEO3jB,CACT,EA3KQykB,EACAoE,EACAjf,EACAoM,EAGN,CAGA,OAAOwO,EAA0BC,EACnC,CAiBA,IAAMF,EAAe,IAAIxS,QAkJzB,SAASyS,EAA0BC,CAAwB,EACzD,IAAMC,EAAeH,EAAapd,GAAG,CAACsd,GACtC,GAAIC,EACF,OAAOA,EAMT,GAPkB,CAOZ1kB,EAAUoB,QAAQC,OAAO,CAACojB,GAYhC,OAXAF,EAAahS,GAAG,CAACkS,EAAkBzkB,GAEnCuK,OAAO4Z,IAAI,CAACM,GAAkBL,OAAO,CAAC,AAACT,IACrC,EAAIG,mBAAAA,CAAoBhZ,GAAG,CAAC6Y,KAIxB3jB,CAAe,CAAC2jB,AAJe,EAIV,CAAGc,CAAgB,CAACd,EAAAA,AAAK,CAEpD,GAEO3jB,CACT,CAsFA,IAAMkoB,EAAAA,CAAAA,EAAAA,EAAoB1C,2CAAAA,EACxByD,GAGIhB,EAAAA,CAAAA,EAAAA,EACJzC,2CAAAA,EAA4C2C,AAc9C,SAASA,AACP3S,CAAyB,CACzB3D,CAAkB,CAClBmW,CAAgC,EAEhC,IAAMpd,EAAS4K,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAON,CAPM,AAAIjV,MACT,CAAA,EAAGqK,EAAO,KAAK,EAAEiH,EAIf,SAJ0B,EAAE,oKAI5B,EAKN,AALSuW,SAKAA,AAA4BC,CAAyB,EAC5D,OAAQA,EAAW5lB,MAAM,EACvB,KAAK,EACH,MAAM,OAAA,cAEL,CAFK,IAAA,EAAI8gB,cAAAA,CACR,uFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAE8E,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC/B,MAAK,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACxD,SAAS,CACP,IAAInV,EAAc,GAClB,IAAK,IAAI3P,EAAI,EAAGA,EAAI8kB,EAAW5lB,MAAM,CAAG,EAAGc,IAAK,AAC9C2P,GAAe,CAAC,EAAE,EAAEmV,CAAU,CAAC9kB,EAAE,CAAC,IAAI,CAAC,CAGzC,OAAO2P,AADPA,EAAe,CAAC,QAAQ,EAAEmV,CAAU,CAACA,EAAW5lB,MAAM,CAAG,EAAE,CAAC,EAAE,CAAC,AAEjE,CACF,CACF,EAxBqCulB,GAAmB,gEAAE,CAJvB,AAIwB,EALlD,CAEH,AAIA,CAJC,AAIA,kBANE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAOP,CAJI,AAG+D,CAH9D,AAG+D,AAEtE,GA1BA,SAASiB,EACPzT,CAAyB,CACzB3D,CAAkB,EAElB,IAAMjH,EAAS4K,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,AAgB8D,CAAC,GAClE,CAAC,EAjBE,cAIN,CAJM,AAAIjV,MACT,CAAA,EAAGqK,EAAO,KAAK,EAAEiH,EAAW,cAgB0B,CAAC,GACrD,wGAjB4B,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAIP,CADI,CAAC,AAEP,8DAFqE,CAAC,kCChdtE,EAA+B,EAAkC,CAAA,AAAxD0R,CAAwD,QAY1D,KAZgB,GAA0C,CAYjD4F,EAAe,CAW9B,CAvB8B,CAYA,GAAA,WAC7B3b,CAAS,cACTtI,CAAY,QACZkkB,CAAM,CAENC,UAAQ,CAMT,CAX8B,EAY7B,GAAsB,aAAlB,OAAOvjB,OAAwB,CACjC,IAGIwjB,EACAC,EAJE,OATqD,WASnD5f,CAAgB,CAAE,CACxB/E,EAAQ,CAAA,CAAA,IAAA,GAMJwI,EAAQzD,EAAiBE,QAAQ,GACvC,GAAI,CAACuD,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAA,EAAImW,cAAAA,CACR,4EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,8BAAE6C,CAA4B,CAAE,CACpCxhB,EAAQ,CAAA,CAAA,IAAA,IACV0kB,EAAqBlD,EAA6BlhB,EAAckI,GAEhE,GAAM,wBAAEkb,CAAsB,CAAE,CAC9B1jB,EAAQ,CAAA,CAAA,IAAA,IAGV,OAFA2kB,AAEA,EAFejB,EAAuBc,EAAQhc,GAE9C,CAAA,CAAA,CAAA,EAAO,GAAA,EAACI,EAAAA,CAAU4b,OAAQG,EAAcrkB,aAAcokB,GACxD,CAAO,CACL,GAAM,CAAEjF,oCAAkC,CAAE,CAC1Czf,EAAQ,CAAA,CAAA,IAAA,GACJ0kB,EAAqBjF,EAAmCnf,GACxD,8BAAEyf,CAA4B,CAAE,CACpC/f,EAAQ,CAAA,CAAA,IAAA,IACJ2kB,EAAe5E,EAA6ByE,GAElD,MAAA,CAAA,EAAA,EAAO,GAAA,EAAC5b,EAAR,AAAQA,CAAU4b,OAAQG,EAAcrkB,aAAcokB,GACxD,CACF,mGC3DA,EAA+B,EAAkC,CAAxD/F,AAAwD,CAAA,QAY1D,KAZgB,GAA0C,CAYjDiG,EAAkB,CAWjC,CAvB8B,CAYG,GAAA,WAChChc,CAAS,OACTic,CAAK,QACLL,CAAM,SAENppB,CAAO,CAMR,CAXiC,EAYhC,GAAsB,aAAlB,OAAO8F,OAAwB,CACjC,IAGIyjB,EAHE,UATqD,QASnD5f,CAAgB,CAAE,CACxB/E,EAAQ,CAAA,CAAA,IAAA,GAKJwI,EAAQzD,EAAiBE,QAAQ,GACvC,GAAI,CAACuD,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAA,EAAImW,cAAAA,CACR,sGADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,wBAAE+E,CAAsB,CAAE,CAC9B1jB,EAAQ,CAAA,CAAA,IAAA,IAGV,OAFA2kB,AAEA,EAFejB,EAAuBc,EAAQhc,GAE9C,CAAA,CAAA,CAAA,EAAO,GAAA,EAACI,EAAAA,CAAW,GAAGic,CAAK,CAAEL,OAAQG,GACvC,CAAO,CACL,GAAM,8BAAE5E,CAA4B,CAAE,CACpC/f,EAAQ,CAAA,CAAA,IAAA,IACJ2kB,EAAe5E,EAA6ByE,GAClD,MAAA,CAAA,EAAA,EAAO,GAAA,EAAC5b,EAAR,AAAQA,CAAW,GAAGic,CAAK,CAAEL,OAAQG,GACvC,CACF,uFCnDA,IAAA,EAA2B,EAAA,CAAlBppB,AAAkB,CAAA,EAAf,MAGL,EAHa,OAGJupB,CAHW,CAGa,CAIvC,EAJuC,GAAA,SACtC1pB,CAAO,CAGR,CAJuC,EAKhC,UAAE2pB,CAAQ,OAAErhB,CAAK,CAAE,CAAA,CAAA,EAAA,EAAGnI,GAAAA,EAAIH,UAGhC,AAAIsI,EAAc,KAAP,AACJqhB,CACT,kECbAC,EAAOC,OAAO,CAAGjlB,EAAQ,CAAA,CAAA,IAAA,IAAyBklB,QAAQ,CACxD,QACD,CAACC,sBAAsB,mFCFxB,IAAA,EAAuC,EAAA,CAAA,AAA9B5pB,CAA8B,EAA3B,EAAEqJ,IACd,EAGO,EAAA,CADLwgB,AACK,CAJiB,AAIjB,QAJyB,AAShC,IAAMC,EAA4B,AAACC,EATI,EAUrC,IAAMC,EAAAA,CAAAA,EAAAA,CAPuB,CAOD3gB,SANsC,CAMtCA,EAAWwgB,EAAAA,6BAAAA,EAEnCG,GACFA,EAAoBD,EAExB,EAEO,SAASE,EAAqB,CALV,AAS1B,EAJoC,GAAA,SACnCpqB,CAAO,CAGR,CAJoC,EAM7B,UAAE2pB,CAAQ,CAAE,CAAA,CAAA,EAAA,EAAGxpB,GAAAA,EAAIH,GAIzB,OAFAiqB,EAA0B,IAAMN,GAEzB,IACT,2HC1BA,EAAqC,EAAA,CAA5B/L,AAA4B,CAAA,OAApB,CAGV,CAHYzd,GAGNkqB,AAHS,EAIF,MAJU,OAI5B,CAJmC,MAI5BvkB,OAEDlB,EAAQ,CAAA,CAAA,IAAA,GACRwlB,oBAAoB,CAEpBxlB,EAAQ,CAAA,CAAA,IAAA,IACR8kB,uBAAuB,CAE/B,AAF+B,SAEtBY,EAAe,CAIvB,EAJuB,GAAA,CACtBtqB,SAAO,CAGR,CAJuB,EAKhB,OAAEsI,CAAK,QAAEqC,CAAM,CAAE,CAAA,CAAA,EAAA,EAAGxK,GAAAA,EAAIH,GAC9B,GAAIsI,EAMF,KANS,CACLqC,IAGArC,EAAcqC,EAHN,IAGY,CAAGA,CAAAA,EAErBrC,EAER,OAAO,IACT,CAEO,SAASiiB,EAAoB,CAInC,EAJmC,GAAA,SAClCvqB,CAAO,CAGR,CAJmC,EAKlC,MAAA,CAAA,EAAA,EACE,GAAA,EAAA,EADF,AACG4d,QAAAA,CAAAA,CAAS2E,SAAU,cAClB,CAAA,EAAA,EAAA,GAAA,EAAC+H,EAAAA,AAAD,CAAgBtqB,QAASA,KAG/B,8HCvCA,IAAA,EAEEyT,EACAC,CAFAF,AAEAE,CAAAA,KADsB,EACtBA,AAAoB,CAKtB,IAAM8W,EAAY,CAJX,AAKL,CAAA,EAAChX,GARqB,EACtBC,iBAOCD,CAAuB,CAAE,SAAU,CAInC,EAJmC,GAAA,CALW,SAM7CrF,CAAQ,CAGT,CAJmC,EAKlC,OAAOA,CACT,EACA,CAACsF,EAAAA,sBAAAA,CAAuB,CAAE,SAAU,CAInC,EAJmC,GAAA,UAClCtF,CAAQ,CAGT,CAJmC,EAKlC,OAAOA,CACT,EACA,CAAA,EAACuF,oBAAAA,CAAqB,CAAE,SAAU,CAIjC,EAJiC,GAAA,CAChCvF,UAAQ,CAGT,CAJiC,EAKhC,OAAOA,CACT,CACF,EAEasc,EAGXD,CAAS,CAAA,EAAChX,aAFV,AACA,SACUA,CAAuBnR,KAAK,CAAC,GAAoC,CAAA,AAEhEqoB,EAGXF,CAAS,CAAA,EAAC/W,aAFV,AACA,SACUA,CAAuBpR,KAAK,CAAC,GAAoC,CAEhEsoB,AAFgE,CANf,CAW5DH,CAAS,CAAA,EAAC9W,WAFV,AACA,IAXgF,KAYtEA,CAAqBrR,KAAK,CAAC,GAAkC,CAAA,GANX,oBADoB,iBAMpB,oBADoB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]}