module.exports = {

"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://tzjelqzwdgidsjqhmvkr.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6amVscXp3ZGdpZHNqcWhtdmtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTAsImV4cCI6MjA2MzkwNDY5MH0.MVkBlC_QI1IT7licw9CzLoS9yyo-QGXR7EFyXYZDyZc"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/src/lib/auth/session.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSession": (()=>createSession),
    "decrypt": (()=>decrypt),
    "deleteSession": (()=>deleteSession),
    "encrypt": (()=>encrypt),
    "refreshSession": (()=>refreshSession),
    "updateSession": (()=>updateSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$server$2d$only$2f$empty$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/server-only/empty.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/sign.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
;
;
;
// TODO: Remove this line (type SessionPayload = any)
// type SessionPayload = any // REMOVE THIS LINE
const secretKey = process.env.SESSION_SECRET;
const encodedKey = new TextEncoder().encode(secretKey);
async function encrypt(payload) {
    // Fix: Cast payload to Record<string, unknown> for SignJWT
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SignJWT"](payload) // Keep this cast
    .setProtectedHeader({
        alg: 'HS256'
    }).setIssuedAt().setExpirationTime('7d').sign(encodedKey);
}
async function decrypt(session = '') {
    try {
        if (!session) {
            return null;
        }
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jwtVerify"])(session, encodedKey, {
            algorithms: [
                'HS256'
            ]
        });
        // Fix: cast to unknown first, then to SessionPayload for type safety
        return payload; // Keep this cast
    } catch  {
        console.log('Failed to verify session');
        return null;
    }
}
async function createSession(userId) {
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    const session = await encrypt({
        userId,
        expiresAt
    });
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    console.log('DEBUG: Creating session for user:', userId);
    console.log('DEBUG: Session expires at:', expiresAt);
    cookieStore.set('session', session, {
        httpOnly: true,
        secure: false,
        expires: expiresAt,
        sameSite: 'lax',
        path: '/'
    });
    console.log('DEBUG: Session cookie set successfully');
}
async function updateSession() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const session = cookieStore.get('session')?.value;
    const payload = await decrypt(session);
    console.log('DEBUG: Updating session - session exists:', !!session);
    console.log('DEBUG: Updating session - payload valid:', !!payload);
    if (!session || !payload) {
        console.log('DEBUG: Cannot update session - missing session or payload');
        return null;
    }
    const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    cookieStore.set('session', session, {
        httpOnly: true,
        secure: false,
        expires: expires,
        sameSite: 'lax',
        path: '/'
    });
    console.log('DEBUG: Session updated successfully');
}
async function refreshSession(userId) {
    // Delete old session and create new one
    console.log('DEBUG: Refreshing session for user:', userId);
    await deleteSession();
    await createSession(userId);
    console.log('DEBUG: Session refresh completed');
}
async function deleteSession() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    console.log('DEBUG: Deleting session cookie');
    cookieStore.delete('session');
    console.log('DEBUG: Session cookie deleted');
}
}}),
"[project]/src/lib/auth/dal.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkSession": (()=>checkSession),
    "getDoctorQuota": (()=>getDoctorQuota),
    "getUser": (()=>getUser),
    "getUserById": (()=>getUserById),
    "verifySession": (()=>verifySession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$server$2d$only$2f$empty$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/server-only/empty.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/session.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
const verifySession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const cookie = cookieStore.get('session')?.value;
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decrypt"])(cookie);
    if (!session?.userId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login');
    }
    return {
        isAuth: true,
        userId: session.userId
    };
});
const checkSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async ()=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const cookie = cookieStore.get('session')?.value;
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decrypt"])(cookie);
    if (!session?.userId) {
        return null;
    }
    return {
        isAuth: true,
        userId: session.userId
    };
});
const getUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async ()=>{
    const session = await verifySession();
    if (!session) return null;
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: user, error } = await supabase.from('doctors').select('*').eq('id', session.userId).single();
        if (error) {
            console.error('Failed to fetch user:', error.message || error);
            return null;
        }
        if (!user) return null;
        // Return user without template_config conversion since it's removed
        return {
            ...user,
            password_hash: user.password_hash
        };
    } catch (error) {
        console.error('Failed to fetch user:', error instanceof Error ? error.message : error);
        return null;
    }
});
const getUserById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (userId)=>{
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: user, error } = await supabase.from('doctors').select('id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash').eq('id', userId).single();
        if (error) {
            console.error('Failed to fetch user by ID:', error.message || error);
            return null;
        }
        if (!user) return null;
        // Return user without template_config conversion since it's removed
        return {
            ...user
        };
    } catch (error) {
        console.error('Failed to fetch user by ID:', error instanceof Error ? error.message : error);
        return null;
    }
});
const getDoctorQuota = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (userId)=>{
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: doctor, error } = await supabase.from('doctors').select('monthly_quota, quota_used, quota_reset_at').eq('id', userId).single();
        if (error) {
            console.error('Failed to fetch quota:', error.message || error);
            return null;
        }
        const quotaRemaining = doctor.monthly_quota - doctor.quota_used;
        const quotaPercentage = Math.round(doctor.quota_used / doctor.monthly_quota * 100);
        const resetDate = new Date(doctor.quota_reset_at);
        const daysUntilReset = Math.ceil((resetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
        return {
            monthly_quota: doctor.monthly_quota,
            quota_used: doctor.quota_used,
            quota_remaining: quotaRemaining,
            quota_percentage: quotaPercentage,
            quota_reset_at: doctor.quota_reset_at,
            days_until_reset: Math.max(0, daysUntilReset)
        };
    } catch (error) {
        console.error('Failed to fetch quota:', error instanceof Error ? error.message : error);
        return null;
    }
});
}}),
"[project]/src/lib/validations.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminLoginFormSchema": (()=>AdminLoginFormSchema),
    "ConsultationCreateSchema": (()=>ConsultationCreateSchema),
    "ConsultationUpdateSchema": (()=>ConsultationUpdateSchema),
    "LoginFormSchema": (()=>LoginFormSchema),
    "ProfileUpdateSchema": (()=>ProfileUpdateSchema),
    "SignupFormSchema": (()=>SignupFormSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
const SignupFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, {
        message: 'Name must be at least 2 characters long.'
    }).trim(),
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email({
        message: 'Please enter a valid email.'
    }).trim(),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(8, {
        message: 'Password must be at least 8 characters long'
    }).regex(/[a-zA-Z]/, {
        message: 'Password must contain at least one letter.'
    }).regex(/[0-9]/, {
        message: 'Password must contain at least one number.'
    }).regex(/[^a-zA-Z0-9]/, {
        message: 'Password must contain at least one special character.'
    }).trim(),
    clinic_name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, {
        message: 'Hospital name must be at least 2 characters long.'
    }).optional(),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^\d{10}$/, {
        message: 'Please enter exactly 10 digits (excluding +91).'
    }).optional()
});
const LoginFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email({
        message: 'Please enter a valid email.'
    }).trim(),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, {
        message: 'Password is required.'
    }).trim()
});
const AdminLoginFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email({
        message: 'Please enter a valid email.'
    }).trim(),
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, {
        message: 'Password is required.'
    }).trim()
});
const ConsultationCreateSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    primary_audio_url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url({
        message: 'Valid audio URL is required.'
    }),
    additional_audio_urls: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url()).optional().default([]),
    image_urls: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url()).optional().default([]),
    submitted_by: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'doctor',
        'receptionist'
    ]),
    total_file_size_bytes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).optional()
});
const ConsultationUpdateSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    edited_note: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, {
        message: 'Note content is required.'
    })
});
const ProfileUpdateSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, {
        message: 'Name must be at least 2 characters long.'
    }).trim(),
    clinic_name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(2, {
        message: 'Hospital name must be at least 2 characters long.'
    }).optional(),
    phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^\d{10}$/, {
        message: 'Please enter exactly 10 digits (excluding +91).'
    }).optional()
});
}}),
"[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@aws-sdk/client-s3", () => require("@aws-sdk/client-s3"));

module.exports = mod;
}}),
"[project]/src/lib/storage.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STORAGE_CONFIG": (()=>STORAGE_CONFIG),
    "calculateTotalFileSize": (()=>calculateTotalFileSize),
    "deleteFile": (()=>deleteFile),
    "downloadFile": (()=>downloadFile),
    "extractFilePathFromUrl": (()=>extractFilePathFromUrl),
    "generateStoragePath": (()=>generateStoragePath),
    "uploadFile": (()=>uploadFile),
    "uploadMultipleFiles": (()=>uploadMultipleFiles),
    "validateFile": (()=>validateFile),
    "validateTotalSize": (()=>validateTotalSize)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@aws-sdk/client-s3 [external] (@aws-sdk/client-s3, cjs)");
;
const STORAGE_CONFIG = {
    // R2 Configuration
    BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
    PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',
    // Folder prefixes (replaces separate buckets)
    AUDIO_PREFIX: 'consultation-audio',
    IMAGE_PREFIX: 'consultation-images',
    // File limits
    MAX_FILE_SIZE: 100 * 1024 * 1024,
    MAX_TOTAL_SIZE: 200 * 1024 * 1024,
    ALLOWED_AUDIO_TYPES: [
        'audio/webm',
        'audio/mp3',
        'audio/wav',
        'audio/m4a',
        'audio/mpeg',
        'audio/mp4',
        'audio/ogg'
    ],
    ALLOWED_IMAGE_TYPES: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/heic'
    ],
    RETENTION_DAYS: 30
};
// R2 Client configuration
const createR2Client = ()=>{
    return new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["S3Client"]({
        region: 'auto',
        endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,
        credentials: {
            accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',
            secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************'
        }
    });
};
function validateFile(file, type) {
    // Check file size
    if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
        return {
            valid: false,
            error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit`
        };
    }
    // Check file type
    const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES;
    if (!allowedTypes.includes(file.type)) {
        return {
            valid: false,
            error: `File type ${file.type} is not allowed`
        };
    }
    return {
        valid: true
    };
}
function generateStoragePath(doctorId, consultationId, fileName, type) {
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX;
    return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`;
}
async function uploadFile(file, doctorId, consultationId, type) {
    try {
        // Validate file
        const validation = validateFile(file, type);
        if (!validation.valid) {
            return {
                success: false,
                error: validation.error
            };
        }
        const r2Client = createR2Client();
        const filePath = generateStoragePath(doctorId, consultationId, file.name, type);
        // Convert file to buffer
        const fileBuffer = await file.arrayBuffer();
        // Upload to R2 - preserve exact Content-Type from file
        const uploadCommand = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["PutObjectCommand"]({
            Bucket: STORAGE_CONFIG.BUCKET_NAME,
            Key: filePath,
            Body: new Uint8Array(fileBuffer),
            ContentType: file.type,
            CacheControl: 'public, max-age=3600'
        });
        await r2Client.send(uploadCommand);
        // Generate public URL
        const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`;
        return {
            success: true,
            url: publicUrl
        };
    } catch (error) {
        console.error('R2 upload error:', error);
        return {
            success: false,
            error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}
async function uploadMultipleFiles(files, doctorId, consultationId, type) {
    const results = await Promise.all(files.map((file)=>uploadFile(file, doctorId, consultationId, type)));
    const successful = results.filter((r)=>r.success);
    const failed = results.filter((r)=>!r.success);
    if (failed.length > 0) {
        return {
            success: false,
            errors: failed.map((f)=>f.error || 'Unknown error')
        };
    }
    return {
        success: true,
        urls: successful.map((s)=>s.url).filter(Boolean)
    };
}
async function deleteFile(filePath, _type) {
    try {
        const r2Client = createR2Client();
        const deleteCommand = new __TURBOPACK__imported__module__$5b$externals$5d2f40$aws$2d$sdk$2f$client$2d$s3__$5b$external$5d$__$2840$aws$2d$sdk$2f$client$2d$s3$2c$__cjs$29$__["DeleteObjectCommand"]({
            Bucket: STORAGE_CONFIG.BUCKET_NAME,
            Key: filePath
        });
        await r2Client.send(deleteCommand);
        return {
            success: true
        };
    } catch (error) {
        console.error('R2 delete error:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Delete failed'
        };
    }
}
function extractFilePathFromUrl(url, type) {
    try {
        const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX;
        const prefixPath = `/${prefix}/`;
        const index = url.indexOf(prefixPath);
        if (index === -1) return null;
        return url.substring(url.indexOf(prefix));
    } catch  {
        return null;
    }
}
async function downloadFile(filePath, _type) {
    try {
        // For public files, we can fetch directly from the custom domain
        const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`;
        const response = await fetch(publicUrl);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.blob();
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('R2 download error:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Download failed'
        };
    }
}
function calculateTotalFileSize(files) {
    return files.reduce((total, file)=>total + file.size, 0);
}
function validateTotalSize(files) {
    const totalSize = calculateTotalFileSize(files);
    if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {
        return {
            valid: false,
            error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`
        };
    }
    return {
        valid: true
    };
}
}}),
"[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c":"getConsultationStats","40181945d99bd1b1c806279418d8c4e384a1b0bd15":"getConsultations","40cad8ac11d66cae6a36deaf0347461ff74e2f07d8":"clearEditedNote","40d596bef0460f198d588bbf8dede026bc85cca740":"createConsultation","6016d1147dadc5ab75f7387f44ab799f7cd595708b":"updateConsultationImages","60648db8471ac7da6666e2d2e8b2cf27a97b9b4688":"approveConsultation","6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7":"updateAdditionalNotes","608aaaf92844312537830e8c8d0c49debb89bda21b":"saveStreamingSummary","6090ac31595b581fab8f32262309efcdcab1c7eb47":"saveEditedNote","60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb":"updateConsultationType","609aacf458083825c0e86748f13c86d5773450588b":"updatePatientName","60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e":"addAdditionalImages","60b3bf115b7639c2355ff16ee9fa53425dad65720c":"addAdditionalAudio","60c855f485dc14093b0fee942b2204ae7dca69142e":"deleteConsultationImage","60fe749e3c9b3a6222eaf37afc1421459616909387":"deleteAdditionalAudio","7f7dd3bdf6242116733f7ef527b1e8f307d343dacd":"generateSummaryStream","7fc832fd06db7232f37f9b1b44631bd2956c9b940c":"createConsultationWithFiles"},"",""] */ __turbopack_context__.s({
    "addAdditionalAudio": (()=>addAdditionalAudio),
    "addAdditionalImages": (()=>addAdditionalImages),
    "approveConsultation": (()=>approveConsultation),
    "clearEditedNote": (()=>clearEditedNote),
    "createConsultation": (()=>createConsultation),
    "createConsultationWithFiles": (()=>createConsultationWithFiles),
    "deleteAdditionalAudio": (()=>deleteAdditionalAudio),
    "deleteConsultationImage": (()=>deleteConsultationImage),
    "generateSummaryStream": (()=>generateSummaryStream),
    "getConsultationStats": (()=>getConsultationStats),
    "getConsultations": (()=>getConsultations),
    "saveEditedNote": (()=>saveEditedNote),
    "saveStreamingSummary": (()=>saveStreamingSummary),
    "updateAdditionalNotes": (()=>updateAdditionalNotes),
    "updateConsultationImages": (()=>updateConsultationImages),
    "updateConsultationType": (()=>updateConsultationType),
    "updatePatientName": (()=>updatePatientName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/dal.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/storage.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Helper to parse a Json | null array field from Supabase
function parseJsonStringArray(field) {
    if (!field) return [];
    if (Array.isArray(field)) return field;
    if (typeof field === 'string') {
        try {
            return JSON.parse(field);
        } catch  {
            return [];
        }
    }
    return [];
}
async function createConsultationWithFiles(audioFile, imageFiles, additionalAudioFiles, submittedBy, doctorNotes, consultationType = 'outpatient', patientName) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        // Validate total file size
        const allFiles = [
            audioFile,
            ...imageFiles,
            ...additionalAudioFiles
        ];
        const totalSizeValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateTotalSize"])(allFiles);
        if (!totalSizeValidation.valid) {
            return {
                success: false,
                error: totalSizeValidation.error
            };
        }
        // Generate consultation ID for file organization
        const consultationId = crypto.randomUUID();
        // Upload primary audio file
        const audioUploadResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadFile"])(audioFile, session.userId, consultationId, 'audio');
        if (!audioUploadResult.success) {
            return {
                success: false,
                error: `Audio upload failed: ${audioUploadResult.error}`
            };
        }
        // Upload additional audio files
        let additionalAudioUrls = [];
        if (additionalAudioFiles.length > 0) {
            const additionalAudioResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadMultipleFiles"])(additionalAudioFiles, session.userId, consultationId, 'audio');
            if (!additionalAudioResult.success) {
                return {
                    success: false,
                    error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}`
                };
            }
            additionalAudioUrls = additionalAudioResult.urls || [];
        }
        // Upload image files
        let imageUrls = [];
        if (imageFiles.length > 0) {
            const imageUploadResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadMultipleFiles"])(imageFiles, session.userId, consultationId, 'image');
            if (!imageUploadResult.success) {
                return {
                    success: false,
                    error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}`
                };
            }
            imageUrls = imageUploadResult.urls || [];
        }
        const totalFileSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["calculateTotalFileSize"])(allFiles);
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Insert consultation with pre-generated ID
        const { data: consultation, error } = await supabase.from('consultations').insert({
            id: consultationId,
            doctor_id: session.userId,
            primary_audio_url: audioUploadResult.url,
            additional_audio_urls: additionalAudioUrls,
            image_urls: imageUrls,
            submitted_by: submittedBy,
            status: 'pending',
            total_file_size_bytes: totalFileSize,
            doctor_notes: doctorNotes || null,
            consultation_type: consultationType,
            patient_name: patientName || null
        }).select().single();
        if (error) {
            console.error('Database error:', error);
            return {
                success: false,
                error: 'Failed to create consultation'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/mobile');
        return {
            success: true,
            data: consultation
        };
    } catch (error) {
        console.error('Create consultation with files error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function saveStreamingSummary(consultationId, summary) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation with generated summary
        const { error: updateError } = await supabase.from('consultations').update({
            ai_generated_note: summary,
            status: 'generated'
        }).eq('id', consultationId).eq('doctor_id', session.userId) // Ensure user can only update their own consultations
        ;
        if (updateError) {
            console.error('Update error:', updateError);
            return {
                success: false,
                error: 'Failed to save generated summary'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: summary
        };
    } catch (error) {
        console.error('Save streaming summary error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function createConsultation(formData) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        // Extract URLs from form data
        const primaryAudioUrl = formData.get('primary_audio_url');
        const additionalAudioUrlsString = formData.get('additional_audio_urls');
        const imageUrlsString = formData.get('image_urls');
        const totalFileSizeString = formData.get('total_file_size_bytes');
        let additionalAudioUrls = [];
        let imageUrls = [];
        let totalFileSize = 0;
        // Parse additional audio URLs
        if (additionalAudioUrlsString) {
            try {
                additionalAudioUrls = JSON.parse(additionalAudioUrlsString);
            } catch (error) {
                console.error('Error parsing additional_audio_urls:', error);
            }
        }
        // Parse image URLs
        if (imageUrlsString) {
            try {
                imageUrls = JSON.parse(imageUrlsString);
            } catch (error) {
                console.error('Error parsing image_urls:', error);
            }
        }
        // Parse total file size
        if (totalFileSizeString) {
            totalFileSize = parseInt(totalFileSizeString, 10) || 0;
        }
        // Validate form data
        const validatedFields = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConsultationCreateSchema"].safeParse({
            primary_audio_url: primaryAudioUrl,
            additional_audio_urls: additionalAudioUrls,
            image_urls: imageUrls,
            submitted_by: formData.get('submitted_by'),
            total_file_size_bytes: totalFileSize
        });
        if (!validatedFields.success) {
            return {
                success: false,
                error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)
            };
        }
        const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data;
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Insert consultation
        const { data: consultation, error } = await supabase.from('consultations').insert({
            doctor_id: session.userId,
            primary_audio_url,
            additional_audio_urls: additional_audio_urls || [],
            image_urls: image_urls || [],
            submitted_by,
            status: 'pending',
            total_file_size_bytes: total_file_size_bytes || 0
        }).select().single();
        if (error) {
            console.error('Database error:', error);
            return {
                success: false,
                error: 'Failed to create consultation'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/mobile');
        return {
            success: true,
            data: consultation
        };
    } catch (error) {
        console.error('Create consultation error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function getConsultations({ page = 1, pageSize = 15, status, searchTerm } = {}) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        let query = supabase.from('consultations').select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', {
            count: 'exact'
        }).eq('doctor_id', session.userId).order('created_at', {
            ascending: false
        }).range(from, to);
        // Apply status filter
        if (status) {
            query = query.eq('status', status);
        }
        // Apply FTS search if provided
        if (searchTerm && searchTerm.trim()) {
            // Convert search term to FTS query format (space-separated words joined with &)
            const ftsQuery = searchTerm.trim().split(/\s+/).join(' & ');
            query = query.textSearch('fts', ftsQuery);
        }
        const { data: consultations, error, count } = await query;
        if (error) {
            console.error('Database error:', error);
            // Handle "Requested range not satisfiable" error gracefully
            if (error.code === 'PGRST103') {
                return {
                    success: true,
                    data: {
                        consultations: [],
                        hasMore: false,
                        totalCount: count || 0
                    }
                };
            }
            return {
                success: false,
                error: 'Failed to fetch consultations'
            };
        }
        // Map to Consultation[]
        const typedConsultations = (consultations || []).map((row)=>{
            return {
                id: row.id,
                doctor_id: row.doctor_id,
                submitted_by: row.submitted_by,
                primary_audio_url: row.primary_audio_url,
                additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),
                image_urls: parseJsonStringArray(row.image_urls),
                ai_generated_note: row.ai_generated_note ?? undefined,
                edited_note: row.edited_note ?? undefined,
                status: row.status,
                patient_number: row.patient_number ?? undefined,
                patient_name: row.patient_name ?? undefined,
                total_file_size_bytes: row.total_file_size_bytes ?? 0,
                file_retention_until: row.file_retention_until,
                created_at: row.created_at,
                updated_at: row.updated_at,
                consultation_type: row.consultation_type ?? 'outpatient',
                doctor_notes: row.doctor_notes ?? undefined,
                additional_notes: row.additional_notes ?? undefined
            };
        });
        const hasMore = (count || 0) > to + 1;
        return {
            success: true,
            data: {
                consultations: typedConsultations,
                hasMore,
                totalCount: count || 0
            }
        };
    } catch (error) {
        console.error('Get consultations error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function generateSummaryStream(consultationId, additionalImages, onChunk, onComplete, onError, consultationType, doctorNotes, additionalNotes) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            onError?.('Unauthorized');
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get consultation and doctor data
        const { data: consultation, error: consultationError } = await supabase.from('consultations').select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)').eq('id', consultationId).eq('doctor_id', session.userId).single();
        if (consultationError || !consultation) {
            onError?.('Consultation not found');
            return {
                success: false,
                error: 'Consultation not found'
            };
        }
        const typedConsultation = consultation;
        // Parse additional_audio_urls and image_urls from JSON if needed
        const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls);
        const imageUrls = parseJsonStringArray(typedConsultation.image_urls);
        const allImageUrls = [
            ...imageUrls,
            ...additionalImages || []
        ];
        // Use the SAME production-grade streaming approach as the Create buttons
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3005")}/api/generate-summary-stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                primary_audio_url: typedConsultation.primary_audio_url,
                additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],
                image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],
                submitted_by: typedConsultation.submitted_by || 'doctor',
                consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',
                doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,
                additional_notes: additionalNotes || undefined,
                patient_name: typedConsultation.patient_name || undefined,
                doctor_name: typedConsultation.doctors.name || undefined,
                created_at: typedConsultation.created_at || undefined
            })
        });
        if (!response.ok) {
            const errorMsg = `HTTP ${response.status}: ${response.statusText}`;
            onError?.(errorMsg);
            return {
                success: false,
                error: errorMsg
            };
        }
        const reader = response.body?.getReader();
        if (!reader) {
            onError?.('No reader available');
            return {
                success: false,
                error: 'No reader available'
            };
        }
        const decoder = new TextDecoder();
        let fullSummary = '';
        try {
            while(true){
                const { done, value } = await reader.read();
                if (done) break;
                const chunk = decoder.decode(value, {
                    stream: true
                });
                const lines = chunk.split('\n');
                for (const line of lines){
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            if (data.type === 'chunk' && data.text) {
                                // Format text with proper line breaks (same as production)
                                const formattedText = data.text.replace(/\\n/g, '\n').replace(/\n\n+/g, '\n\n');
                                fullSummary += formattedText;
                                // Call onChunk with the new text chunk
                                onChunk?.(formattedText);
                            }
                        } catch (_e) {
                        // Ignore parse errors (same as production)
                        }
                    }
                }
            }
            // Call onComplete with final summary
            onComplete?.(fullSummary);
            // Save the streamed summary using the same approach as production
            try {
                const saveResult = await saveStreamingSummary(consultationId, fullSummary);
                if (!saveResult.success) {
                    console.error('Failed to save streaming summary:', saveResult.error);
                    onError?.(`Summary generated but failed to save: ${saveResult.error}`);
                    return {
                        success: false,
                        error: `Summary generated but failed to save: ${saveResult.error}`
                    };
                }
            } catch (saveError) {
                console.error('Error saving streaming summary:', saveError);
                onError?.('Summary generated but failed to save. Please try regenerate.');
                return {
                    success: false,
                    error: 'Summary generated but failed to save. Please try regenerate.'
                };
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
            return {
                success: true,
                data: fullSummary
            };
        } catch (error) {
            console.error('❌ Generate error:', error);
            const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`;
            onError?.(errorMsg);
            return {
                success: false,
                error: errorMsg
            };
        }
    } catch (error) {
        console.error('Generate streaming summary error:', error);
        onError?.('An unexpected error occurred');
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function approveConsultation(consultationId, editedNote) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        // Validate edited note
        const validatedFields = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConsultationUpdateSchema"].safeParse({
            edited_note: editedNote
        });
        if (!validatedFields.success) {
            return {
                success: false,
                error: 'Invalid note content'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation
        const { error } = await supabase.from('consultations').update({
            edited_note: editedNote,
            status: 'approved'
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Update error:', error);
            return {
                success: false,
                error: 'Failed to approve consultation'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Approve consultation error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function updateConsultationImages(consultationId, imageUrls) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation with new image URLs
        const { error } = await supabase.from('consultations').update({
            image_urls: imageUrls
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Update consultation images error:', error);
            return {
                success: false,
                error: 'Failed to update consultation images'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Update consultation images error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function addAdditionalAudio(consultationId, audioFile) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the consultation
        const { data: consultation, error: fetchError } = await supabase.from('consultations').select('additional_audio_urls, status').eq('id', consultationId).eq('doctor_id', session.userId).single();
        if (fetchError || !consultation) {
            return {
                success: false,
                error: 'Consultation not found'
            };
        }
        if (consultation.status === 'approved') {
            return {
                success: false,
                error: `Cannot add audio to approved consultations. Current status: ${consultation.status}`
            };
        }
        // Upload the additional audio file
        const uploadResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadFile"])(audioFile, session.userId, consultationId, 'audio');
        if (!uploadResult.success) {
            return {
                success: false,
                error: `Audio upload failed: ${uploadResult.error}`
            };
        }
        const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls);
        additionalAudioUrls.push(uploadResult.url);
        // Update the consultation
        const { error: updateError } = await supabase.from('consultations').update({
            additional_audio_urls: additionalAudioUrls
        }).eq('id', consultationId);
        if (updateError) {
            return {
                success: false,
                error: 'Failed to add additional audio'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/record');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Add additional audio error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function addAdditionalImages(consultationId, imageFiles) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the consultation
        const { data: consultation, error: fetchError } = await supabase.from('consultations').select('image_urls, status').eq('id', consultationId).eq('doctor_id', session.userId).single();
        if (fetchError || !consultation) {
            return {
                success: false,
                error: 'Consultation not found'
            };
        }
        if (consultation.status === 'approved') {
            return {
                success: false,
                error: `Cannot add images to approved consultations. Current status: ${consultation.status}`
            };
        }
        // Upload all image files
        const uploadResults = await Promise.all(imageFiles.map((file)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uploadFile"])(file, session.userId, consultationId, 'image')));
        // Check if all uploads succeeded
        const failedUploads = uploadResults.filter((result)=>!result.success);
        if (failedUploads.length > 0) {
            const errors = failedUploads.map((f)=>f.error).join(', ');
            return {
                success: false,
                error: `Image upload failed: ${errors}`
            };
        }
        // Get existing image URLs and add new ones
        const existingImageUrls = parseJsonStringArray(consultation.image_urls);
        const newImageUrls = uploadResults.map((result)=>result.url).filter((url)=>url);
        const allImageUrls = [
            ...existingImageUrls,
            ...newImageUrls
        ];
        // Update the consultation
        const { error: updateError } = await supabase.from('consultations').update({
            image_urls: allImageUrls
        }).eq('id', consultationId);
        if (updateError) {
            return {
                success: false,
                error: 'Failed to add additional images'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/record');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Add additional images error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function updateConsultationType(consultationId, consultationType) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation type
        const { error } = await supabase.from('consultations').update({
            consultation_type: consultationType
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Update consultation type error:', error);
            return {
                success: false,
                error: 'Failed to update consultation type'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Update consultation type error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function saveEditedNote(consultationId, editedNote) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation with edited note
        const { error } = await supabase.from('consultations').update({
            edited_note: editedNote
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Save edited note error:', error);
            return {
                success: false,
                error: 'Failed to save edited note'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Save edited note error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function updateAdditionalNotes(consultationId, additionalNotes) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation with additional notes
        const { error } = await supabase.from('consultations').update({
            additional_notes: additionalNotes || null
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Update additional notes error:', error);
            return {
                success: false,
                error: 'Failed to update additional notes'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Update additional notes error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function updatePatientName(consultationId, patientName) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Update consultation with patient name
        const { error } = await supabase.from('consultations').update({
            patient_name: patientName || null
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Update patient name error:', error);
            return {
                success: false,
                error: 'Failed to update patient name'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Update patient name error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function clearEditedNote(consultationId) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Clear edited note from consultation
        const { error } = await supabase.from('consultations').update({
            edited_note: null
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (error) {
            console.error('Clear edited note error:', error);
            return {
                success: false,
                error: 'Failed to clear edited note'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Clear edited note error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function deleteAdditionalAudio(consultationId, audioUrl) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get current consultation
        const { data: consultation, error: fetchError } = await supabase.from('consultations').select('additional_audio_urls, status').eq('id', consultationId).eq('doctor_id', session.userId).single();
        if (fetchError || !consultation) {
            return {
                success: false,
                error: 'Consultation not found'
            };
        }
        // Check if consultation can be modified
        if (consultation.status === 'approved') {
            return {
                success: false,
                error: 'Cannot delete files from approved consultations'
            };
        }
        // Parse and filter out the audio URL
        const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls);
        const updatedAudioUrls = additionalAudioUrls.filter((url)=>url !== audioUrl);
        // Update consultation
        const { error: updateError } = await supabase.from('consultations').update({
            additional_audio_urls: updatedAudioUrls
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (updateError) {
            console.error('Delete additional audio error:', updateError);
            return {
                success: false,
                error: 'Failed to delete additional audio'
            };
        }
        // Delete file from storage
        try {
            const { deleteFile } = await __turbopack_context__.r("[project]/src/lib/storage.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const filePath = audioUrl.split('/').pop() || '';
            await deleteFile(filePath, 'audio');
        } catch (storageError) {
            console.error('Storage deletion error:', storageError);
        // Don't fail the operation if storage deletion fails
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Delete additional audio error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function deleteConsultationImage(consultationId, imageUrl) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get current consultation
        const { data: consultation, error: fetchError } = await supabase.from('consultations').select('image_urls, status').eq('id', consultationId).eq('doctor_id', session.userId).single();
        if (fetchError || !consultation) {
            return {
                success: false,
                error: 'Consultation not found'
            };
        }
        // Check if consultation can be modified
        if (consultation.status === 'approved') {
            return {
                success: false,
                error: 'Cannot delete files from approved consultations'
            };
        }
        // Parse and filter out the image URL
        const imageUrls = parseJsonStringArray(consultation.image_urls);
        const updatedImageUrls = imageUrls.filter((url)=>url !== imageUrl);
        // Update consultation
        const { error: updateError } = await supabase.from('consultations').update({
            image_urls: updatedImageUrls
        }).eq('id', consultationId).eq('doctor_id', session.userId);
        if (updateError) {
            console.error('Delete consultation image error:', updateError);
            return {
                success: false,
                error: 'Failed to delete image'
            };
        }
        // Delete file from storage
        try {
            const { deleteFile } = await __turbopack_context__.r("[project]/src/lib/storage.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const filePath = imageUrl.split('/').pop() || '';
            await deleteFile(filePath, 'image');
        } catch (storageError) {
            console.error('Storage deletion error:', storageError);
        // Don't fail the operation if storage deletion fails
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Delete consultation image error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function getConsultationStats() {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
        if (!session) {
            return {
                success: false,
                error: 'Unauthorized'
            };
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data, error } = await supabase.rpc('get_consultation_stats', {
            doctor_uuid: session.userId
        });
        if (error) {
            console.error('Database error:', error);
            return {
                success: false,
                error: 'Failed to fetch consultation stats'
            };
        }
        const stats = data?.[0] || {
            total_consultations: 0,
            pending_consultations: 0,
            approved_consultations: 0,
            today_consultations: 0
        };
        return {
            success: true,
            data: {
                total_consultations: Number(stats.total_consultations),
                pending_consultations: Number(stats.pending_consultations),
                approved_consultations: Number(stats.approved_consultations),
                today_consultations: Number(stats.today_consultations)
            }
        };
    } catch (error) {
        console.error('Get consultation stats error:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    createConsultationWithFiles,
    saveStreamingSummary,
    createConsultation,
    getConsultations,
    generateSummaryStream,
    approveConsultation,
    updateConsultationImages,
    addAdditionalAudio,
    addAdditionalImages,
    updateConsultationType,
    saveEditedNote,
    updateAdditionalNotes,
    updatePatientName,
    clearEditedNote,
    deleteAdditionalAudio,
    deleteConsultationImage,
    getConsultationStats
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createConsultationWithFiles, "7fc832fd06db7232f37f9b1b44631bd2956c9b940c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(saveStreamingSummary, "608aaaf92844312537830e8c8d0c49debb89bda21b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createConsultation, "40d596bef0460f198d588bbf8dede026bc85cca740", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getConsultations, "40181945d99bd1b1c806279418d8c4e384a1b0bd15", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateSummaryStream, "7f7dd3bdf6242116733f7ef527b1e8f307d343dacd", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(approveConsultation, "60648db8471ac7da6666e2d2e8b2cf27a97b9b4688", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateConsultationImages, "6016d1147dadc5ab75f7387f44ab799f7cd595708b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(addAdditionalAudio, "60b3bf115b7639c2355ff16ee9fa53425dad65720c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(addAdditionalImages, "60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateConsultationType, "60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(saveEditedNote, "6090ac31595b581fab8f32262309efcdcab1c7eb47", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateAdditionalNotes, "6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePatientName, "609aacf458083825c0e86748f13c86d5773450588b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(clearEditedNote, "40cad8ac11d66cae6a36deaf0347461ff74e2f07d8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteAdditionalAudio, "60fe749e3c9b3a6222eaf37afc1421459616909387", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteConsultationImage, "60c855f485dc14093b0fee942b2204ae7dca69142e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getConsultationStats, "0021892c2090f634723ac0a0d1cd1d8c637aae5b9c", null);
}}),
"[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00ed563c8fb8c346efaec610c0d2a954999d90622b":"getAdminReferralStats","400d27483b0ad440768404c34690724d71663b6083":"generateReferralLink","4072b74acb2fae150f3d07efd9bac13f2f423a1a31":"validateReferralCode","407564c58b76eea97b9678f27ef8f98ef2a04ae6eb":"markReferralConversion","40757964b8c9b072995f44631743e71306c1784480":"getReferralInfo","606bc295d7adf166de394559341675608634558f4e":"processReferralSignup"},"",""] */ __turbopack_context__.s({
    "generateReferralLink": (()=>generateReferralLink),
    "getAdminReferralStats": (()=>getAdminReferralStats),
    "getReferralInfo": (()=>getReferralInfo),
    "markReferralConversion": (()=>markReferralConversion),
    "processReferralSignup": (()=>processReferralSignup),
    "validateReferralCode": (()=>validateReferralCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function getReferralInfo(doctorId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get doctor's referral information
        const { data: doctor, error: doctorError } = await supabase.from('doctors').select(`
        referral_code,
        total_referrals,
        successful_referrals,
        referral_discount_earned,
        referred_by
      `).eq('id', doctorId).single();
        if (doctorError || !doctor) {
            return {
                success: false,
                error: 'Failed to fetch referral information'
            };
        }
        // Get pending referrals count
        const { count: pendingCount } = await supabase.from('referral_analytics').select('*', {
            count: 'exact',
            head: true
        }).eq('referrer_id', doctorId).eq('status', 'pending');
        // Get recent referrals
        const { data: recentReferrals, error: referralsError } = await supabase.from('referral_analytics').select(`
        id,
        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),
        signup_date,
        conversion_date,
        status
      `).eq('referrer_id', doctorId).order('created_at', {
            ascending: false
        }).limit(10);
        if (referralsError) {
            return {
                success: false,
                error: 'Failed to fetch recent referrals'
            };
        }
        // Get referrer info separately if exists
        let referredBy = null;
        if (doctor.referred_by) {
            const { data: referrer } = await supabase.from('doctors').select('name, referral_code').eq('id', doctor.referred_by).single();
            if (referrer) {
                referredBy = {
                    name: referrer.name,
                    referral_code: referrer.referral_code || ''
                };
            }
        }
        const referralInfo = {
            referral_code: doctor.referral_code || '',
            total_referrals: doctor.total_referrals || 0,
            successful_referrals: doctor.successful_referrals || 0,
            pending_referrals: pendingCount || 0,
            discount_earned: doctor.referral_discount_earned || 0,
            referred_by: referredBy,
            recent_referrals: (recentReferrals || []).map((ref)=>({
                    id: ref.id,
                    name: ref.referred_doctor?.name || 'Unknown',
                    email: ref.referred_doctor?.email || 'Unknown',
                    signup_date: ref.signup_date,
                    conversion_date: ref.conversion_date,
                    status: ref.status
                }))
        };
        return {
            success: true,
            data: referralInfo
        };
    } catch (error) {
        console.error('Error fetching referral info:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function generateReferralLink(doctorId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: doctor, error } = await supabase.from('doctors').select('referral_code').eq('id', doctorId).single();
        if (error || !doctor?.referral_code) {
            return {
                success: false,
                error: 'Failed to fetch referral code'
            };
        }
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app';
        const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`;
        return {
            success: true,
            data: referralLink
        };
    } catch (error) {
        console.error('Error generating referral link:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function processReferralSignup(referralCode, newDoctorId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Find the referrer
        const { data: referrer, error: referrerError } = await supabase.from('doctors').select('id, name').eq('referral_code', referralCode).single();
        if (referrerError || !referrer) {
            return {
                success: false,
                error: 'Invalid referral code'
            };
        }
        // Update the new doctor with referrer information
        const { error: updateError } = await supabase.from('doctors').update({
            referred_by: referrer.id
        }).eq('id', newDoctorId);
        if (updateError) {
            return {
                success: false,
                error: 'Failed to process referral signup'
            };
        }
        // Create referral analytics record
        const { error: analyticsError } = await supabase.from('referral_analytics').insert({
            referrer_id: referrer.id,
            referred_doctor_id: newDoctorId,
            referral_code: referralCode,
            status: 'pending'
        });
        if (analyticsError) {
            console.error('Failed to create analytics record:', analyticsError);
        // Don't fail the signup for this
        }
        // Update referrer's total referrals count
        const { data: currentReferrer } = await supabase.from('doctors').select('total_referrals').eq('id', referrer.id).single();
        if (currentReferrer) {
            const { error: countError } = await supabase.from('doctors').update({
                total_referrals: (currentReferrer.total_referrals || 0) + 1
            }).eq('id', referrer.id);
            if (countError) {
                console.error('Failed to update referral count:', countError);
            }
        }
        return {
            success: true,
            data: true
        };
    } catch (error) {
        console.error('Error processing referral signup:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function markReferralConversion(doctorId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Call the database function to handle conversion
        const { data, error } = await supabase.rpc('handle_referral_conversion', {
            referred_doctor_uuid: doctorId
        });
        if (error) {
            console.error('Error marking referral conversion:', error);
            return {
                success: false,
                error: 'Failed to process referral conversion'
            };
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/dashboard');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])('/admin/dashboard');
        return {
            success: true,
            data: data || false
        };
    } catch (error) {
        console.error('Error marking referral conversion:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function getAdminReferralStats() {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get overall stats
        const { data: totalStats, error: statsError } = await supabase.from('referral_analytics').select('status, discount_earned');
        if (statsError) {
            return {
                success: false,
                error: 'Failed to fetch referral statistics'
            };
        }
        const totalReferrals = totalStats?.length || 0;
        const successfulConversions = totalStats?.filter((s)=>s.status === 'converted').length || 0;
        const pendingReferrals = totalStats?.filter((s)=>s.status === 'pending').length || 0;
        const totalDiscountEarned = totalStats?.reduce((sum, s)=>sum + (s.discount_earned || 0), 0) || 0;
        // Get top referrers
        const { data: topReferrers, error: referrersError } = await supabase.from('doctors').select('id, name, referral_code, successful_referrals, referral_discount_earned').gt('successful_referrals', 0).order('successful_referrals', {
            ascending: false
        }).limit(10);
        if (referrersError) {
            return {
                success: false,
                error: 'Failed to fetch top referrers'
            };
        }
        return {
            success: true,
            data: {
                total_referrals: totalReferrals,
                successful_conversions: successfulConversions,
                pending_referrals: pendingReferrals,
                total_discount_earned: totalDiscountEarned,
                top_referrers: (topReferrers || []).map((r)=>({
                        id: r.id,
                        name: r.name,
                        referral_code: r.referral_code || '',
                        successful_referrals: r.successful_referrals || 0,
                        discount_earned: r.referral_discount_earned || 0
                    }))
            }
        };
    } catch (error) {
        console.error('Error fetching admin referral stats:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
async function validateReferralCode(referralCode) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: referrer, error } = await supabase.from('doctors').select('name, approved').eq('referral_code', referralCode).eq('approved', true).single();
        if (error || !referrer) {
            return {
                success: true,
                data: {
                    valid: false
                }
            };
        }
        return {
            success: true,
            data: {
                valid: true,
                referrer_name: referrer.name
            }
        };
    } catch (error) {
        console.error('Error validating referral code:', error);
        return {
            success: false,
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getReferralInfo,
    generateReferralLink,
    processReferralSignup,
    markReferralConversion,
    getAdminReferralStats,
    validateReferralCode
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getReferralInfo, "40757964b8c9b072995f44631743e71306c1784480", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateReferralLink, "400d27483b0ad440768404c34690724d71663b6083", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(processReferralSignup, "606bc295d7adf166de394559341675608634558f4e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(markReferralConversion, "407564c58b76eea97b9678f27ef8f98ef2a04ae6eb", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getAdminReferralStats, "00ed563c8fb8c346efaec610c0d2a954999d90622b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(validateReferralCode, "4072b74acb2fae150f3d07efd9bac13f2f423a1a31", null);
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"008a705899f288d02ab102cb7194752ef57be976ca":"logout","6064c1bc78024112540d1fec516d46f480c1310eb6":"login","607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d":"signup","608ed2498189cfd7c9da6c6017df4df3d3e1da70c2":"changePassword"},"",""] */ __turbopack_context__.s({
    "changePassword": (()=>changePassword),
    "login": (()=>login),
    "logout": (()=>logout),
    "signup": (()=>signup)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/session.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
async function signup(state, formData) {
    // Validate form fields
    const validatedFields = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SignupFormSchema"].safeParse({
        name: formData.get('name'),
        email: formData.get('email'),
        password: formData.get('password'),
        clinic_name: formData.get('clinic_name'),
        phone: formData.get('phone')
    });
    // Get referral code from form
    const referralCode = formData.get('referral_code');
    // If any form fields are invalid, return early
    if (!validatedFields.success) {
        return {
            // Provide required 'success' and 'message' properties
            success: false,
            message: 'Invalid form fields.',
            // Use 'fieldErrors' as defined in FormState, and pass the specific field errors
            fieldErrors: validatedFields.error.flatten().fieldErrors
        };
    }
    // Prepare data for insertion into database
    const { name, email, password, clinic_name, phone } = validatedFields.data;
    const hashedPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].hash(password, 10);
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Check if user already exists (email or phone)
        const { data: existingUser } = await supabase.from('doctors').select('id').eq('email', email).single();
        if (existingUser) {
            return {
                success: false,
                message: 'An account with this email already exists.'
            };
        }
        // Check if phone number already exists (if phone is provided)
        if (phone) {
            const { data: existingPhone } = await supabase.from('doctors').select('id').eq('phone', phone).single();
            if (existingPhone) {
                return {
                    success: false,
                    message: 'An account with this phone number already exists.'
                };
            }
        }
        // Insert the user into the database with approval required and new default quota
        const { data: user, error } = await supabase.from('doctors').insert({
            name,
            email,
            password_hash: hashedPassword,
            clinic_name,
            phone,
            approved: false,
            monthly_quota: 50
        }).select('id, approved').single();
        if (error) {
            console.error('Database error:', error);
            return {
                success: false,
                message: 'An error occurred while creating your account.'
            };
        }
        if (!user) {
            return {
                success: false,
                message: 'An error occurred while creating your account.'
            };
        }
        // Process referral if referral code was provided
        if (referralCode) {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["processReferralSignup"])(referralCode, user.id);
        }
        // Don't create session for unapproved users
        if (!user.approved) {
            return {
                success: true,
                message: 'Account created successfully! Please wait for admin approval before you can log in.'
            };
        }
        // Create user session (only for approved users)
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createSession"])(user.id);
    } catch (error) {
        console.error('Signup error:', error);
        return {
            success: false,
            message: 'An unexpected error occurred.'
        };
    }
    // If execution reaches here, it means signup was successful and session created (if approved)
    // or a success message was returned for pending approval.
    // The redirect will handle navigation, but a FormState must be returned for useActionState.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/dashboard');
    // This return is theoretically unreachable but satisfies the Promise<FormState> return type
    return {
        success: true,
        message: 'Redirecting...'
    };
}
async function login(state, formData) {
    // Validate form fields
    const validatedFields = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LoginFormSchema"].safeParse({
        email: formData.get('email'),
        password: formData.get('password')
    });
    // If any form fields are invalid, return early
    if (!validatedFields.success) {
        return {
            // Provide required 'success' and 'message' properties
            success: false,
            message: 'Invalid form fields.',
            // Use 'fieldErrors' as defined in FormState, and pass the specific field errors
            fieldErrors: validatedFields.error.flatten().fieldErrors
        };
    }
    const { email, password } = validatedFields.data;
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get user from database
        const { data: user, error } = await supabase.from('doctors').select('id, password_hash, approved').eq('email', email).single();
        if (error || !user) {
            return {
                success: false,
                message: 'Invalid email or password.'
            };
        }
        // Verify password
        const isValidPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].compare(password, user.password_hash);
        if (!isValidPassword) {
            return {
                success: false,
                message: 'Invalid email or password.'
            };
        }
        // Check if user is approved
        if (!user.approved) {
            return {
                success: false,
                message: 'Your account is pending admin approval. Please wait for approval before logging in.'
            };
        }
        // Create user session
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createSession"])(user.id);
    } catch (error) {
        console.error('Login error:', error);
        return {
            success: false,
            message: 'An unexpected error occurred.'
        };
    }
    // If execution reaches here, it means login was successful and session created.
    // The redirect will handle navigation, but a FormState must be returned for useActionState.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/dashboard');
    // This return is theoretically unreachable but satisfies the Promise<FormState> return type
    return {
        success: true,
        message: 'Redirecting...'
    };
}
async function logout() {
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteSession"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/login');
}
async function changePassword(doctorId, formData) {
    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const confirmPassword = formData.get('confirmPassword');
    // Basic validation
    if (!currentPassword || !newPassword || !confirmPassword) {
        return {
            success: false,
            message: 'All fields are required.'
        };
    }
    if (newPassword !== confirmPassword) {
        return {
            success: false,
            message: 'New passwords do not match.'
        };
    }
    if (newPassword.length < 8) {
        return {
            success: false,
            message: 'New password must be at least 8 characters long.'
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get current password hash
        const { data: doctor, error: fetchError } = await supabase.from('doctors').select('password_hash').eq('id', doctorId).single();
        if (fetchError || !doctor) {
            return {
                success: false,
                message: 'Doctor not found.'
            };
        }
        // Verify current password
        const isValidPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].compare(currentPassword, doctor.password_hash);
        if (!isValidPassword) {
            return {
                success: false,
                message: 'Current password is incorrect.'
            };
        }
        // Hash new password
        const hashedNewPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].hash(newPassword, 10);
        // Update password
        const { error: updateError } = await supabase.from('doctors').update({
            password_hash: hashedNewPassword
        }).eq('id', doctorId);
        if (updateError) {
            console.error('Password update error:', updateError);
            return {
                success: false,
                message: 'Failed to update password.'
            };
        }
        // Refresh session with updated credentials
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$session$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["refreshSession"])(doctorId);
        return {
            success: true,
            message: 'Password changed successfully!'
        };
    } catch (error) {
        console.error('Password change error:', error);
        return {
            success: false,
            message: 'An unexpected error occurred.'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signup,
    login,
    logout,
    changePassword
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signup, "607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(login, "6064c1bc78024112540d1fec516d46f480c1310eb6", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(logout, "008a705899f288d02ab102cb7194752ef57be976ca", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(changePassword, "608ed2498189cfd7c9da6c6017df4df3d3e1da70c2", null);
}}),
"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0021892c2090f634723ac0a0d1cd1d8c637aae5b9c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getConsultationStats"]),
    "008a705899f288d02ab102cb7194752ef57be976ca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logout"]),
    "40181945d99bd1b1c806279418d8c4e384a1b0bd15": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getConsultations"]),
    "40757964b8c9b072995f44631743e71306c1784480": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getReferralInfo"]),
    "40cad8ac11d66cae6a36deaf0347461ff74e2f07d8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearEditedNote"]),
    "40d596bef0460f198d588bbf8dede026bc85cca740": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createConsultation"]),
    "6016d1147dadc5ab75f7387f44ab799f7cd595708b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateConsultationImages"]),
    "60648db8471ac7da6666e2d2e8b2cf27a97b9b4688": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["approveConsultation"]),
    "6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateAdditionalNotes"]),
    "608aaaf92844312537830e8c8d0c49debb89bda21b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["saveStreamingSummary"]),
    "6090ac31595b581fab8f32262309efcdcab1c7eb47": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["saveEditedNote"]),
    "60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateConsultationType"]),
    "609aacf458083825c0e86748f13c86d5773450588b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updatePatientName"]),
    "60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addAdditionalImages"]),
    "60b3bf115b7639c2355ff16ee9fa53425dad65720c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addAdditionalAudio"]),
    "60c855f485dc14093b0fee942b2204ae7dca69142e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteConsultationImage"]),
    "60fe749e3c9b3a6222eaf37afc1421459616909387": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteAdditionalAudio"]),
    "7f7dd3bdf6242116733f7ef527b1e8f307d343dacd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateSummaryStream"]),
    "7fc832fd06db7232f37f9b1b44631bd2956c9b940c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createConsultationWithFiles"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0021892c2090f634723ac0a0d1cd1d8c637aae5b9c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["0021892c2090f634723ac0a0d1cd1d8c637aae5b9c"]),
    "008a705899f288d02ab102cb7194752ef57be976ca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["008a705899f288d02ab102cb7194752ef57be976ca"]),
    "40181945d99bd1b1c806279418d8c4e384a1b0bd15": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40181945d99bd1b1c806279418d8c4e384a1b0bd15"]),
    "40757964b8c9b072995f44631743e71306c1784480": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40757964b8c9b072995f44631743e71306c1784480"]),
    "40cad8ac11d66cae6a36deaf0347461ff74e2f07d8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40cad8ac11d66cae6a36deaf0347461ff74e2f07d8"]),
    "40d596bef0460f198d588bbf8dede026bc85cca740": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40d596bef0460f198d588bbf8dede026bc85cca740"]),
    "6016d1147dadc5ab75f7387f44ab799f7cd595708b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6016d1147dadc5ab75f7387f44ab799f7cd595708b"]),
    "60648db8471ac7da6666e2d2e8b2cf27a97b9b4688": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60648db8471ac7da6666e2d2e8b2cf27a97b9b4688"]),
    "6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7"]),
    "608aaaf92844312537830e8c8d0c49debb89bda21b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["608aaaf92844312537830e8c8d0c49debb89bda21b"]),
    "6090ac31595b581fab8f32262309efcdcab1c7eb47": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6090ac31595b581fab8f32262309efcdcab1c7eb47"]),
    "60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb"]),
    "609aacf458083825c0e86748f13c86d5773450588b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["609aacf458083825c0e86748f13c86d5773450588b"]),
    "60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e"]),
    "60b3bf115b7639c2355ff16ee9fa53425dad65720c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60b3bf115b7639c2355ff16ee9fa53425dad65720c"]),
    "60c855f485dc14093b0fee942b2204ae7dca69142e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60c855f485dc14093b0fee942b2204ae7dca69142e"]),
    "60fe749e3c9b3a6222eaf37afc1421459616909387": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60fe749e3c9b3a6222eaf37afc1421459616909387"]),
    "7f7dd3bdf6242116733f7ef527b1e8f307d343dacd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f7dd3bdf6242116733f7ef527b1e8f307d343dacd"]),
    "7fc832fd06db7232f37f9b1b44631bd2956c9b940c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7fc832fd06db7232f37f9b1b44631bd2956c9b940c"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$referrals$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$lib$2f$actions$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/lib/actions/referrals.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/lib/actions/auth.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/global-error.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/components/recording/new-recording-interface.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NewRecordingInterface": (()=>NewRecordingInterface)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const NewRecordingInterface = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call NewRecordingInterface() from the server but NewRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/recording/new-recording-interface.tsx <module evaluation>", "NewRecordingInterface");
}}),
"[project]/src/components/recording/new-recording-interface.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NewRecordingInterface": (()=>NewRecordingInterface)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const NewRecordingInterface = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call NewRecordingInterface() from the server but NewRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/recording/new-recording-interface.tsx", "NewRecordingInterface");
}}),
"[project]/src/components/recording/new-recording-interface.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$recording$2f$new$2d$recording$2d$interface$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/recording/new-recording-interface.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$recording$2f$new$2d$recording$2d$interface$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/recording/new-recording-interface.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$recording$2f$new$2d$recording$2d$interface$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/data/dashboard-data.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DashboardData": (()=>DashboardData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/dal.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/actions/consultations.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$recording$2f$new$2d$recording$2d$interface$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/recording/new-recording-interface.tsx [app-rsc] (ecmascript)");
;
;
;
;
async function DashboardData({ doctorId }) {
    // This runs inside Suspense boundary, not blocking initial page render
    // OPTIMIZED: Load only initial page (15 items) for fast initial render
    const [user, consultationsResult] = await Promise.all([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUser"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$actions$2f$consultations$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getConsultations"])({
            page: 1,
            pageSize: 15
        })
    ]);
    const consultations = consultationsResult.success ? consultationsResult.data.consultations || [] : [];
    const hasMore = consultationsResult.success ? consultationsResult.data.hasMore : false;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$recording$2f$new$2d$recording$2d$interface$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NewRecordingInterface"], {
        user: user,
        consultations: consultations,
        hasMore: hasMore,
        doctorId: doctorId
    }, void 0, false, {
        fileName: "[project]/src/components/data/dashboard-data.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/shared/dashboard-client.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DashboardClient": (()=>DashboardClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const DashboardClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardClient() from the server but DashboardClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/shared/dashboard-client.tsx <module evaluation>", "DashboardClient");
}}),
"[project]/src/components/shared/dashboard-client.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DashboardClient": (()=>DashboardClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const DashboardClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardClient() from the server but DashboardClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/shared/dashboard-client.tsx", "DashboardClient");
}}),
"[project]/src/components/shared/dashboard-client.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$dashboard$2d$client$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/shared/dashboard-client.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$dashboard$2d$client$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/shared/dashboard-client.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$dashboard$2d$client$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/pwa/pwa-install-prompt.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PWAInstallPrompt": (()=>PWAInstallPrompt)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PWAInstallPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PWAInstallPrompt() from the server but PWAInstallPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/pwa/pwa-install-prompt.tsx <module evaluation>", "PWAInstallPrompt");
}}),
"[project]/src/components/pwa/pwa-install-prompt.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PWAInstallPrompt": (()=>PWAInstallPrompt)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PWAInstallPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PWAInstallPrompt() from the server but PWAInstallPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/pwa/pwa-install-prompt.tsx", "PWAInstallPrompt");
}}),
"[project]/src/components/pwa/pwa-install-prompt.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pwa$2f$pwa$2d$install$2d$prompt$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pwa/pwa-install-prompt.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pwa$2f$pwa$2d$install$2d$prompt$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/pwa/pwa-install-prompt.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pwa$2f$pwa$2d$install$2d$prompt$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/ui/skeleton-loaders.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminDashboardSkeleton": (()=>AdminDashboardSkeleton),
    "ConsultationsListSkeleton": (()=>ConsultationsListSkeleton),
    "DashboardSkeleton": (()=>DashboardSkeleton),
    "DashboardStatsSkeleton": (()=>DashboardStatsSkeleton),
    "InfoPageSkeleton": (()=>InfoPageSkeleton),
    "QuotaCardSkeleton": (()=>QuotaCardSkeleton),
    "ReferralStatsSkeleton": (()=>ReferralStatsSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AdminDashboardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "AdminDashboardSkeleton");
const ConsultationsListSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "ConsultationsListSkeleton");
const DashboardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "DashboardSkeleton");
const DashboardStatsSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "DashboardStatsSkeleton");
const InfoPageSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "InfoPageSkeleton");
const QuotaCardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "QuotaCardSkeleton");
const ReferralStatsSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>", "ReferralStatsSkeleton");
}}),
"[project]/src/components/ui/skeleton-loaders.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminDashboardSkeleton": (()=>AdminDashboardSkeleton),
    "ConsultationsListSkeleton": (()=>ConsultationsListSkeleton),
    "DashboardSkeleton": (()=>DashboardSkeleton),
    "DashboardStatsSkeleton": (()=>DashboardStatsSkeleton),
    "InfoPageSkeleton": (()=>InfoPageSkeleton),
    "QuotaCardSkeleton": (()=>QuotaCardSkeleton),
    "ReferralStatsSkeleton": (()=>ReferralStatsSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AdminDashboardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "AdminDashboardSkeleton");
const ConsultationsListSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "ConsultationsListSkeleton");
const DashboardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "DashboardSkeleton");
const DashboardStatsSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "DashboardStatsSkeleton");
const InfoPageSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "InfoPageSkeleton");
const QuotaCardSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "QuotaCardSkeleton");
const ReferralStatsSkeleton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/skeleton-loaders.tsx", "ReferralStatsSkeleton");
}}),
"[project]/src/components/ui/skeleton-loaders.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2d$loaders$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/ui/skeleton-loaders.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2d$loaders$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/ui/skeleton-loaders.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2d$loaders$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/dashboard/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/dal.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$data$2f$dashboard$2d$data$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/data/dashboard-data.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$dashboard$2d$client$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/dashboard-client.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pwa$2f$pwa$2d$install$2d$prompt$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pwa/pwa-install-prompt.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2d$loaders$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/skeleton-loaders.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const metadata = {
    title: 'Dashboard - Celer AI',
    description: 'Create new patient consultations with AI-powered summaries'
};
async function DashboardPage() {
    // OPTIMIZED: Only verify session (fast), then stream the rest
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$dal$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifySession"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2d$loaders$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DashboardSkeleton"], {}, void 0, false, {
                    fileName: "[project]/src/app/dashboard/page.tsx",
                    lineNumber: 21,
                    columnNumber: 27
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$data$2f$dashboard$2d$data$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DashboardData"], {
                    doctorId: session.userId
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/page.tsx",
                    lineNumber: 22,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$dashboard$2d$client$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DashboardClient"], {
                doctorId: session.userId
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pwa$2f$pwa$2d$install$2d$prompt$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PWAInstallPrompt"], {}, void 0, false, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),
"[project]/src/app/dashboard/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/dashboard/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1822eaca._.js.map