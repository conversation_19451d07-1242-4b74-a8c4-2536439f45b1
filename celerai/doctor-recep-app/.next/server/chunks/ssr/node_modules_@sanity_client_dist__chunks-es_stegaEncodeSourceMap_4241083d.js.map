{"version": 3, "sources": ["turbopack:///[project]/node_modules/@sanity/client/src/csm/studioPath.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/jsonPath.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/resolveMapping.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/isArray.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/walkMap.ts", "turbopack:///[project]/node_modules/@sanity/client/src/stega/encodeIntoResult.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/draftUtils.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/createEditUrl.ts", "turbopack:///[project]/node_modules/@sanity/client/src/csm/resolveEditInfo.ts", "turbopack:///[project]/node_modules/@sanity/client/src/stega/filterDefault.ts", "turbopack:///[project]/node_modules/@sanity/client/src/stega/stegaEncodeSourceMap.ts"], "sourcesContent": ["/** @alpha */\nexport type KeyedSegment = {_key: string}\n\n/** @alpha */\nexport type IndexTuple = [number | '', number | '']\n\n/** @alpha */\nexport type PathSegment = string | number | KeyedSegment | IndexTuple\n\n/** @alpha */\nexport type Path = PathSegment[]\n\nconst rePropName =\n  /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g\n/** @internal */\nexport const reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/\nconst reIndexTuple = /^\\d*:\\d*$/\n\n/** @internal */\nexport function isIndexSegment(segment: PathSegment): segment is number {\n  return typeof segment === 'number' || (typeof segment === 'string' && /^\\[\\d+\\]$/.test(segment))\n}\n\n/** @internal */\nexport function isKeySegment(segment: PathSegment): segment is KeyedSegment {\n  if (typeof segment === 'string') {\n    return reKeySegment.test(segment.trim())\n  }\n\n  return typeof segment === 'object' && '_key' in segment\n}\n\n/** @internal */\nexport function isIndexTuple(segment: PathSegment): segment is IndexTuple {\n  if (typeof segment === 'string' && reIndexTuple.test(segment)) {\n    return true\n  }\n\n  if (!Array.isArray(segment) || segment.length !== 2) {\n    return false\n  }\n\n  const [from, to] = segment\n  return (typeof from === 'number' || from === '') && (typeof to === 'number' || to === '')\n}\n\n/** @internal */\nexport function get<Result = unknown, Fallback = unknown>(\n  obj: unknown,\n  path: Path | string,\n  defaultVal?: Fallback,\n): Result | typeof defaultVal {\n  const select = typeof path === 'string' ? fromString(path) : path\n  if (!Array.isArray(select)) {\n    throw new Error('Path must be an array or a string')\n  }\n\n  let acc: unknown | undefined = obj\n  for (let i = 0; i < select.length; i++) {\n    const segment = select[i]\n    if (isIndexSegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc[segment]\n    }\n\n    if (isKeySegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc.find((item) => item._key === segment._key)\n    }\n\n    if (typeof segment === 'string') {\n      acc =\n        typeof acc === 'object' && acc !== null\n          ? ((acc as Record<string, unknown>)[segment] as Result)\n          : undefined\n    }\n\n    if (typeof acc === 'undefined') {\n      return defaultVal\n    }\n  }\n\n  return acc as Result\n}\n\n/** @alpha */\nexport function toString(path: Path): string {\n  if (!Array.isArray(path)) {\n    throw new Error('Path is not an array')\n  }\n\n  return path.reduce<string>((target, segment, i) => {\n    const segmentType = typeof segment\n    if (segmentType === 'number') {\n      return `${target}[${segment}]`\n    }\n\n    if (segmentType === 'string') {\n      const separator = i === 0 ? '' : '.'\n      return `${target}${separator}${segment}`\n    }\n\n    if (isKeySegment(segment) && segment._key) {\n      return `${target}[_key==\"${segment._key}\"]`\n    }\n\n    if (Array.isArray(segment)) {\n      const [from, to] = segment\n      return `${target}[${from}:${to}]`\n    }\n\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``)\n  }, '')\n}\n\n/** @alpha */\nexport function fromString(path: string): Path {\n  if (typeof path !== 'string') {\n    throw new Error('Path is not a string')\n  }\n\n  const segments = path.match(rePropName)\n  if (!segments) {\n    throw new Error('Invalid path string')\n  }\n\n  return segments.map(parsePathSegment)\n}\n\nfunction parsePathSegment(segment: string): PathSegment {\n  if (isIndexSegment(segment)) {\n    return parseIndexSegment(segment)\n  }\n\n  if (isKeySegment(segment)) {\n    return parseKeySegment(segment)\n  }\n\n  if (isIndexTuple(segment)) {\n    return parseIndexTupleSegment(segment)\n  }\n\n  return segment\n}\n\nfunction parseIndexSegment(segment: string): PathSegment {\n  return Number(segment.replace(/[^\\d]/g, ''))\n}\n\nfunction parseKeySegment(segment: string): KeyedSegment {\n  const segments = segment.match(reKeySegment)\n  return {_key: segments![1]}\n}\n\nfunction parseIndexTupleSegment(segment: string): IndexTuple {\n  const [from, to] = segment.split(':').map((seg) => (seg === '' ? seg : Number(seg)))\n  return [from, to]\n}\n", "import * as studioPath from './studioPath'\nimport type {\n  ContentSourceMapParsedPath,\n  ContentSourceMapParsedPathKeyedSegment,\n  ContentSourceMapPaths,\n  Path,\n} from './types'\n\nconst ESCAPE: Record<string, string> = {\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t',\n  \"'\": \"\\\\'\",\n  '\\\\': '\\\\\\\\',\n}\n\nconst UNESCAPE: Record<string, string> = {\n  '\\\\f': '\\f',\n  '\\\\n': '\\n',\n  '\\\\r': '\\r',\n  '\\\\t': '\\t',\n  \"\\\\'\": \"'\",\n  '\\\\\\\\': '\\\\',\n}\n\n/**\n * @internal\n */\nexport function jsonPath(path: ContentSourceMapParsedPath): ContentSourceMapPaths[number] {\n  return `$${path\n    .map((segment) => {\n      if (typeof segment === 'string') {\n        const escapedKey = segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `['${escapedKey}']`\n      }\n\n      if (typeof segment === 'number') {\n        return `[${segment}]`\n      }\n\n      if (segment._key !== '') {\n        const escapedKey = segment._key.replace(/['\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `[?(@._key=='${escapedKey}')]`\n      }\n\n      return `[${segment._index}]`\n    })\n    .join('')}`\n}\n\n/**\n * @internal\n */\nexport function parseJsonPath(path: ContentSourceMapPaths[number]): ContentSourceMapParsedPath {\n  const parsed: ContentSourceMapParsedPath = []\n\n  const parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g\n  let match: RegExpExecArray | null\n\n  while ((match = parseRe.exec(path)) !== null) {\n    if (match[1] !== undefined) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push(key)\n      continue\n    }\n\n    if (match[2] !== undefined) {\n      parsed.push(parseInt(match[2], 10))\n      continue\n    }\n\n    if (match[3] !== undefined) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push({\n        _key,\n        _index: -1,\n      })\n      continue\n    }\n  }\n\n  return parsed\n}\n\n/**\n * @internal\n */\nexport function jsonPathToStudioPath(path: ContentSourceMapParsedPath): Path {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._key !== '') {\n      return {_key: segment._key}\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\n/**\n * @internal\n */\nexport function studioPathToJsonPath(path: Path | string): ContentSourceMapParsedPath {\n  const parsedPath = typeof path === 'string' ? studioPath.fromString(path) : path\n\n  return parsedPath.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (Array.isArray(segment)) {\n      throw new Error(`IndexTuple segments aren't supported:${JSON.stringify(segment)}`)\n    }\n\n    if (isContentSourceMapParsedPathKeyedSegment(segment)) {\n      return segment\n    }\n\n    if (segment._key) {\n      return {_key: segment._key, _index: -1}\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\nfunction isContentSourceMapParsedPathKeyedSegment(\n  segment: studioPath.PathSegment | ContentSourceMapParsedPath[number],\n): segment is ContentSourceMapParsedPathKeyedSegment {\n  return typeof segment === 'object' && '_key' in segment && '_index' in segment\n}\n\n/**\n * @internal\n */\nexport function jsonPathToMappingPath(path: ContentSourceMapParsedPath): (string | number)[] {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n", "import {jsonPath, jsonPathToMappingPath} from './jsonPath'\nimport type {ContentSourceMap, ContentSourceMapMapping, ContentSourceMapParsedPath} from './types'\n\n/**\n * @internal\n */\nexport function resolveMapping(\n  resultPath: ContentSourceMapParsedPath,\n  csm?: ContentSourceMap,\n):\n  | {\n      mapping: ContentSourceMapMapping\n      matchedPath: string\n      pathSuffix: string\n    }\n  | undefined {\n  if (!csm?.mappings) {\n    return undefined\n  }\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath))\n\n  if (csm.mappings[resultMappingPath] !== undefined) {\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: '',\n    }\n  }\n\n  const mappings = Object.entries(csm.mappings)\n    .filter(([key]) => resultMappingPath.startsWith(key))\n    .sort(([key1], [key2]) => key2.length - key1.length)\n\n  if (mappings.length == 0) {\n    return undefined\n  }\n\n  const [matchedPath, mapping] = mappings[0]\n  const pathSuffix = resultMappingPath.substring(matchedPath.length)\n  return {mapping, matchedPath, pathSuffix}\n}\n", "/** @internal */\nexport function isArray(value: unknown): value is Array<unknown> {\n  return value !== null && Array.isArray(value)\n}\n", "import {isRecord} from '../util/isRecord'\nimport {isArray} from './isArray'\nimport type {ContentSourceMapParsedPath, WalkMapFn} from './types'\n\n/**\n * generic way to walk a nested object or array and apply a mapping function to each value\n * @internal\n */\nexport function walkMap(\n  value: unknown,\n  mappingFn: WalkMapFn,\n  path: ContentSourceMapParsedPath = [],\n): unknown {\n  if (isArray(value)) {\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v['_key']\n        if (typeof _key === 'string') {\n          return walkMap(v, mappingFn, path.concat({_key, _index: idx}))\n        }\n      }\n\n      return walkMap(v, mappingFn, path.concat(idx))\n    })\n  }\n\n  if (isRecord(value)) {\n    // Handle Portable Text in a faster way\n    if (value._type === 'block' || value._type === 'span') {\n      const result = {...value}\n      if (value._type === 'block') {\n        result.children = walkMap(value.children, mappingFn, path.concat('children'))\n      } else if (value._type === 'span') {\n        result.text = walkMap(value.text, mappingFn, path.concat('text'))\n      }\n      return result\n    }\n\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))]),\n    )\n  }\n\n  return mappingFn(value, path)\n}\n", "import type {ContentSourceMap} from '@sanity/client/csm'\n\nimport {parseJsonPath} from '../csm/jsonPath'\nimport {resolveMapping} from '../csm/resolveMapping'\nimport {walkMap} from '../csm/walkMap'\nimport type {Encoder} from './types'\n\n/**\n * @internal\n */\nexport function encodeIntoResult<Result>(\n  result: Result,\n  csm: ContentSourceMap,\n  encoder: Encoder,\n): Result {\n  return walkMap(result, (value, path) => {\n    // Only map strings, we could extend this in the future to support other types like integers...\n    if (typeof value !== 'string') {\n      return value\n    }\n\n    const resolveMappingResult = resolveMapping(path, csm)\n    if (!resolveMappingResult) {\n      return value\n    }\n\n    const {mapping, matchedPath} = resolveMappingResult\n    if (mapping.type !== 'value') {\n      return value\n    }\n\n    if (mapping.source.type !== 'documentValue') {\n      return value\n    }\n\n    const sourceDocument = csm.documents[mapping.source.document!]\n    const sourcePath = csm.paths[mapping.source.path]\n\n    const matchPathSegments = parseJsonPath(matchedPath)\n    const sourcePathSegments = parseJsonPath(sourcePath)\n    const fullSourceSegments = sourcePathSegments.concat(path.slice(matchPathSegments.length))\n\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value,\n    })\n  }) as Result\n}\n", "// nominal/opaque type hack\ntype Opaque<T, K> = T & {__opaqueId__: K}\n\n/** @internal */\nexport type DraftId = Opaque<string, 'draftId'>\n\n/** @internal */\nexport type PublishedId = Opaque<string, 'publishedId'>\n\n/** @internal */\nexport const DRAFTS_FOLDER = 'drafts'\n\n/** @internal */\nexport const VERSION_FOLDER = 'versions'\n\nconst PATH_SEPARATOR = '.'\nconst DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`\nconst VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`\n\n/** @internal */\nexport function isDraftId(id: string): id is DraftId {\n  return id.startsWith(DRAFTS_PREFIX)\n}\n\n/** @internal */\nexport function isVersionId(id: string): boolean {\n  return id.startsWith(VERSION_PREFIX)\n}\n\n/** @internal */\nexport function isPublishedId(id: string): id is PublishedId {\n  return !isDraftId(id) && !isVersionId(id)\n}\n\n/** @internal */\nexport function getDraftId(id: string): DraftId {\n  if (isVersionId(id)) {\n    const publishedId = getPublishedId(id)\n    return (DRAFTS_PREFIX + publishedId) as DraftId\n  }\n\n  return isDraftId(id) ? id : ((DRAFTS_PREFIX + id) as DraftId)\n}\n\n/**  @internal */\nexport function getVersionId(id: string, version: string): string {\n  if (version === 'drafts' || version === 'published') {\n    throw new Error('Version can not be \"published\" or \"drafts\"')\n  }\n\n  return `${VERSION_PREFIX}${version}${PATH_SEPARATOR}${getPublishedId(id)}`\n}\n\n/**\n *  @internal\n *  Given an id, returns the versionId if it exists.\n *  e.g. `versions.summer-drop.foo` = `summer-drop`\n *  e.g. `drafts.foo` = `undefined`\n *  e.g. `foo` = `undefined`\n */\nexport function getVersionFromId(id: string): string | undefined {\n  if (!isVersionId(id)) return undefined\n  // eslint-disable-next-line unused-imports/no-unused-vars\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR)\n\n  return versionId\n}\n\n/** @internal */\nexport function getPublishedId(id: string): PublishedId {\n  if (isVersionId(id)) {\n    // make sure to only remove the versions prefix and the bundle name\n    return id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) as PublishedId as PublishedId\n  }\n\n  if (isDraftId(id)) {\n    return id.slice(DRAFTS_PREFIX.length) as PublishedId\n  }\n\n  return id as PublishedId\n}\n", "import {getPublishedId, getVersionFromId, isPublishedId, isVersionId} from './draftUtils'\nimport {jsonPathToStudioPath} from './jsonPath'\nimport * as studioPath from './studioPath'\nimport type {CreateEditUrlOptions, EditIntentUrl, StudioBaseUrl} from './types'\n\n/** @internal */\nexport function createEditUrl(options: CreateEditUrlOptions): `${StudioBaseUrl}${EditIntentUrl}` {\n  const {\n    baseUrl,\n    workspace: _workspace = 'default',\n    tool: _tool = 'default',\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset,\n  } = options\n\n  if (!baseUrl) {\n    throw new Error('baseUrl is required')\n  }\n  if (!path) {\n    throw new Error('path is required')\n  }\n  if (!_id) {\n    throw new Error('id is required')\n  }\n  if (baseUrl !== '/' && baseUrl.endsWith('/')) {\n    throw new Error('baseUrl must not end with a slash')\n  }\n\n  const workspace = _workspace === 'default' ? undefined : _workspace\n  const tool = _tool === 'default' ? undefined : _tool\n  const id = getPublishedId(_id)\n  const stringifiedPath = Array.isArray(path)\n    ? studioPath.toString(jsonPathToStudioPath(path))\n    : path\n\n  // eslint-disable-next-line no-warning-comments\n  // @TODO Using searchParams as a temporary workaround until `@sanity/overlays` can decode state from the path reliably\n  const searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath,\n  })\n  if (workspace) {\n    searchParams.set('workspace', workspace)\n  }\n  if (tool) {\n    searchParams.set('tool', tool)\n  }\n  if (projectId) {\n    searchParams.set('projectId', projectId)\n  }\n  if (dataset) {\n    searchParams.set('dataset', dataset)\n  }\n  if (isPublishedId(_id)) {\n    searchParams.set('perspective', 'published')\n  } else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id)!\n    searchParams.set('perspective', versionId)\n  }\n\n  const segments = [baseUrl === '/' ? '' : baseUrl]\n  if (workspace) {\n    segments.push(workspace)\n  }\n  const routerParams = [\n    'mode=presentation',\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`,\n  ]\n  if (tool) {\n    routerParams.push(`tool=${tool}`)\n  }\n  segments.push('intent', 'edit', `${routerParams.join(';')}?${searchParams}`)\n  return segments.join('/') as unknown as `${StudioBaseUrl}${EditIntentUrl}`\n}\n", "import {parseJsonPath} from './jsonPath'\nimport {resolveMapping} from './resolveMapping'\nimport type {\n  CreateEditUrlOptions,\n  ResolveEditInfoOptions,\n  StudioBaseRoute,\n  StudioBaseUrl,\n  StudioUrl,\n} from './types'\n\n/** @internal */\nexport function resolveEditInfo(options: ResolveEditInfoOptions): CreateEditUrlOptions | undefined {\n  const {resultSourceMap: csm, resultPath} = options\n  const {mapping, pathSuffix} = resolveMapping(resultPath, csm) || {}\n\n  if (!mapping) {\n    // console.warn('no mapping for path', { path: resultPath, sourceMap: csm })\n    return undefined\n  }\n\n  if (mapping.source.type === 'literal') {\n    return undefined\n  }\n\n  if (mapping.source.type === 'unknown') {\n    return undefined\n  }\n\n  const sourceDoc = csm.documents[mapping.source.document]\n  const sourcePath = csm.paths[mapping.source.path]\n\n  if (sourceDoc && sourcePath) {\n    const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n      typeof options.studioUrl === 'function' ? options.studioUrl(sourceDoc) : options.studioUrl,\n    )\n    if (!baseUrl) return undefined\n    const {_id, _type, _projectId, _dataset} = sourceDoc\n    return {\n      baseUrl,\n      workspace,\n      tool,\n      id: _id,\n      type: _type,\n      path: parseJsonPath(sourcePath + pathSuffix),\n      projectId: _projectId,\n      dataset: _dataset,\n    } satisfies CreateEditUrlOptions\n  }\n\n  return undefined\n}\n\n/** @internal */\nexport function resolveStudioBaseRoute(studioUrl: StudioUrl): StudioBaseRoute {\n  let baseUrl: StudioBaseUrl = typeof studioUrl === 'string' ? studioUrl : studioUrl.baseUrl\n  if (baseUrl !== '/') {\n    baseUrl = baseUrl.replace(/\\/$/, '')\n  }\n  if (typeof studioUrl === 'string') {\n    return {baseUrl}\n  }\n  return {...studioUrl, baseUrl}\n}\n", "import type {ContentSourceMapParsedPath, FilterDefault} from './types'\n\nexport const filterDefault: FilterDefault = ({sourcePath, resultPath, value}) => {\n  // Skips encoding on URL or Date strings, similar to the `skip: 'auto'` parameter in vercelStegaCombine()\n  if (isValidDate(value) || isValidURL(value)) {\n    return false\n  }\n\n  const endPath = sourcePath.at(-1)\n  // Never encode slugs\n  if (sourcePath.at(-2) === 'slug' && endPath === 'current') {\n    return false\n  }\n\n  // Skip underscored keys, and strings that end with `Id`, needs better heuristics but it works for now\n  if (typeof endPath === 'string' && (endPath.startsWith('_') || endPath.endsWith('Id'))) {\n    return false\n  }\n\n  // Don't encode into anything that is suggested it'll render for SEO in meta tags\n  if (\n    sourcePath.some(\n      (path) => path === 'meta' || path === 'metadata' || path === 'openGraph' || path === 'seo',\n    )\n  ) {\n    return false\n  }\n\n  // If the sourcePath or resultPath contains something that sounds like a type, like iconType, we skip encoding, as it's most\n  // of the time used for logic that breaks if it contains stega characters\n  if (hasTypeLike(sourcePath) || hasTypeLike(resultPath)) {\n    return false\n  }\n\n  // Finally, we ignore a bunch of paths that are typically used for page building\n  if (typeof endPath === 'string' && denylist.has(endPath)) {\n    return false\n  }\n\n  return true\n}\n\nconst denylist = new Set([\n  'color',\n  'colour',\n  'currency',\n  'email',\n  'format',\n  'gid',\n  'hex',\n  'href',\n  'hsl',\n  'hsla',\n  'icon',\n  'id',\n  'index',\n  'key',\n  'language',\n  'layout',\n  'link',\n  'linkAction',\n  'locale',\n  'lqip',\n  'page',\n  'path',\n  'ref',\n  'rgb',\n  'rgba',\n  'route',\n  'secret',\n  'slug',\n  'status',\n  'tag',\n  'template',\n  'theme',\n  'type',\n  'textTheme',\n  'unit',\n  'url',\n  'username',\n  'variant',\n  'website',\n])\n\nfunction isValidDate(dateString: string) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? Boolean(Date.parse(dateString)) : false\n}\n\nfunction isValidURL(url: string) {\n  try {\n    new URL(url, url.startsWith('/') ? 'https://acme.com' : undefined)\n  } catch {\n    return false\n  }\n  return true\n}\n\nfunction hasTypeLike(path: ContentSourceMapParsedPath): boolean {\n  return path.some((segment) => typeof segment === 'string' && segment.match(/type/i) !== null)\n}\n", "import {vercelStegaCombine} from '@vercel/stega'\n\nimport {createEditUrl} from '../csm/createEditUrl'\nimport {jsonPathToStudioPath} from '../csm/jsonPath'\nimport {resolveStudioBaseRoute} from '../csm/resolveEditInfo'\nimport {reKeySegment, toString as studioPathToString} from '../csm/studioPath'\nimport {encodeIntoResult} from './encodeIntoResult'\nimport {filterDefault} from './filterDefault'\nimport {\n  type ContentSourceMap,\n  type ContentSourceMapParsedPath,\n  type InitializedStegaConfig,\n} from './types'\n\nconst TRUNCATE_LENGTH = 20\n\n/**\n * Uses `@vercel/stega` to embed edit info JSON into strings in your query result.\n * The JSON payloads are added using invisible characters so they don't show up visually.\n * The edit info is generated from the Content Source Map (CSM) that is returned from Sanity for the query.\n * @public\n */\nexport function stegaEncodeSourceMap<Result = unknown>(\n  result: Result,\n  resultSourceMap: ContentSourceMap | undefined,\n  config: InitializedStegaConfig,\n): Result {\n  const {filter, logger, enabled} = config\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\"\n    logger?.error?.(`[@sanity/client]: ${msg}`, {result, resultSourceMap, config})\n    throw new TypeError(msg)\n  }\n\n  if (!resultSourceMap) {\n    logger?.error?.('[@sanity/client]: Missing Content Source Map from response body', {\n      result,\n      resultSourceMap,\n      config,\n    })\n    return result\n  }\n\n  if (!config.studioUrl) {\n    const msg = 'config.studioUrl must be defined'\n    logger?.error?.(`[@sanity/client]: ${msg}`, {result, resultSourceMap, config})\n    throw new TypeError(msg)\n  }\n\n  const report: Record<'encoded' | 'skipped', {path: string; length: number; value: string}[]> = {\n    encoded: [],\n    skipped: [],\n  }\n\n  const resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({sourcePath, sourceDocument, resultPath, value}) => {\n      // Allow userland to control when to opt-out of encoding\n      if (\n        (typeof filter === 'function'\n          ? filter({sourcePath, resultPath, filterDefault, sourceDocument, value})\n          : filterDefault({sourcePath, resultPath, filterDefault, sourceDocument, value})) === false\n      ) {\n        if (logger) {\n          report.skipped.push({\n            path: prettyPathForLogging(sourcePath),\n            value: `${value.slice(0, TRUNCATE_LENGTH)}${\n              value.length > TRUNCATE_LENGTH ? '...' : ''\n            }`,\n            length: value.length,\n          })\n        }\n        return value\n      }\n\n      if (logger) {\n        report.encoded.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? '...' : ''}`,\n          length: value.length,\n        })\n      }\n\n      const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n        typeof config.studioUrl === 'function'\n          ? config.studioUrl(sourceDocument)\n          : config.studioUrl!,\n      )\n      if (!baseUrl) return value\n      const {_id: id, _type: type, _projectId: projectId, _dataset: dataset} = sourceDocument\n\n      return vercelStegaCombine(\n        value,\n        {\n          origin: 'sanity.io',\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...(!config.omitCrossDatasetReferenceData && {dataset, projectId}),\n          }),\n        },\n        // We use custom logic to determine if we should skip encoding\n        false,\n      )\n    },\n  )\n\n  if (logger) {\n    const isSkipping = report.skipped.length\n    const isEncoding = report.encoded.length\n    if (isSkipping || isEncoding) {\n      ;(logger?.groupCollapsed || logger.log)?.('[@sanity/client]: Encoding source map into result')\n      logger.log?.(\n        `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`,\n      )\n    }\n    if (report.encoded.length > 0) {\n      logger?.log?.(`[@sanity/client]: Table of encoded paths`)\n      ;(logger?.table || logger.log)?.(report.encoded)\n    }\n    if (report.skipped.length > 0) {\n      const skipped = new Set<string>()\n      for (const {path} of report.skipped) {\n        skipped.add(path.replace(reKeySegment, '0').replace(/\\[\\d+\\]/g, '[]'))\n      }\n      logger?.log?.(`[@sanity/client]: List of skipped paths`, [...skipped.values()])\n    }\n\n    if (isSkipping || isEncoding) {\n      logger?.groupEnd?.()\n    }\n  }\n\n  return resultWithStega\n}\n\nfunction prettyPathForLogging(path: ContentSourceMapParsedPath): string {\n  return studioPathToString(jsonPathToStudioPath(path))\n}\n"], "names": ["studioPath.toString", "vercelStegaCombine", "studioPathToString"], "mappings": "wKAeO,IAAM,EAAe,2BA6ErB,SAAS,EAAS,CAAA,EAAoB,AACvC,GAAA,CAAC,MAAM,OAAA,CAAQ,GACX,CADe,KACf,AAAI,MAAM,sBAAsB,EAGxC,OAAO,EAAK,MAAA,CAAe,CAAC,EAAQ,EAAS,KAC3C,CADiD,GAC3C,EAAc,OAAO,EAC3B,GAAoB,WAAhB,EACK,MAAA,CAAA,EAAG,EAAM,CAAA,EAAI,CAAJ,CAAW,CAAA,CAAA,CAG7B,EAH6B,CAGzB,AAAgB,aAEX,MAAA,CAAA,EAAG,EAAM,EADQ,EACR,EADE,EAAU,GAAK,GACL,CAAA,EAAG,EAAO,CAAA,CAGpC,GAHoC,AAGpC,CAnFiB,UAAnB,OAmFe,AAnFR,EACF,EAAa,GAkFI,CAlFJ,CAAK,EAAQ,IAAA,CAAK,CAAC,EAGf,UAAnB,OAAO,GAAwB,UAAU,GA+EjB,EAAQ,IAAA,CACnC,MAAO,CAAA,EAAG,EAAM,IAAA,IAAA,EAAW,EAAQ,IAAI,CAAA,EAAA,CAAA,CAGrC,GAAA,MAAM,OAAA,CAAQ,GAAU,CACpB,GAAA,AADiB,CAChB,EAAM,EAAE,CAAI,EACnB,MAAO,CAAA,EAAG,EAAM,CAAA,EAAI,CAAJ,CAAQ,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAGhC,MAAM,AAAI,MAAM,CAAA,2BAAA,EAA8B,KAAK,SAAA,CAAU,GAAQ,EAAA,CAAI,CAAL,AAAK,CAAJ,CACpE,EAAE,CACP,CC/GA,IAAM,EAAiC,CACrC,KAAM,MACN,KAAM,MACN,KAAM,MACN,IAAM,MACN,IAAK,MACL,KAAM,MACR,EAEM,EAAmC,CACvC,MAAO,KACP,MAAO,CAAA;AAAA,CAAA,CACP,MAAO,KACP,MAAO,IACP,MAAO,IACP,OAAQ,IACV,EAkCO,SAAS,EAAc,CAAA,EACtB,AADuF,IAIzF,EAHE,EAAqC,EAAA,CAErC,EAAU,oDAGhB,KAAA,AAAwC,QAAhC,EAAQ,EAAQ,IAAA,CAAK,EAAI,CAAA,EAAa,CACxC,GAAa,KAAA,IAAb,CAAA,CAAM,CAAC,CAAA,CAAiB,CACpB,IAAA,EAAM,CAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,oBAAqB,AAAC,GAC1C,CAAA,CAAS,CAAC,CAClB,EAED,EAAO,IAAA,CAAK,GAAG,AACf,QAAA,CAGE,GAAa,KAAA,IAAb,CAAA,CAAM,CAAC,CAAA,CAAiB,CAC1B,EAAO,IAAA,CAAK,SAAS,CAAA,CAAM,CAAC,CAAA,CAAG,EAAE,CAAC,EAClC,QAAA,CAGE,GAAA,AAAa,KAAA,KAAb,CAAM,CAAC,CAAA,CAAiB,CACpB,IAAA,EAAO,CAAA,CAAM,CAAC,CAAA,CAAE,OAAA,CAAQ,WAAY,AAAC,GAClC,CAAA,CAAS,CAAC,CAClB,EAED,EAAO,IAAA,CAAK,CACV,OACA,OAAQ,CAAA,CAAA,CACT,EACD,QAAA,CACF,CAGK,OAAA,CACT,CAKO,SAAS,EAAqB,CAAA,EAAwC,AACpE,OAAA,EAAK,GAAA,CAAI,AAAC,IAKf,GAJuB,KADI,KACvB,OAAO,GAIY,UAAnB,OAAO,EACF,OAAA,EAGT,GAAqB,KAAjB,EAAQ,IAAA,CACH,MAAA,CAAC,KAAM,EAAQ,IAAA,AAAI,EAG5B,GAAuB,CAAA,IAAnB,EAAQ,MAAA,CACV,OAAO,EAAQ,MAGjB,AAHiB,OAGX,AAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,GAAQ,CAAE,CAAA,CAC7D,CAD0D,AAE7D,CAF8D,AI1G9C,SAAA,EACd,CAAA,CACA,CAAA,CACA,CAAA,EACQ,AACR,OAAO,ADPF,SAAS,EACd,CAAA,CACA,CAAA,CACA,EAAmC,CAAA,CAAA,EAC1B,AACT,GDXO,AAAU,CCWb,SDXqB,MAAM,OAAA,CCWnB,ADX2B,GCYrC,EADe,ADX2B,KCYnC,EAAM,GAAA,CAAI,CAAC,EAAG,KACf,GAAA,AADuB,CACvB,EAAA,EAAA,QAAA,EAAS,CAAC,EAAG,CACf,IAAM,EAAO,EAAE,IAAA,CACf,GAAoB,UAAhB,OAAO,EACF,OAAA,EAAQ,EAAG,EAAW,EAAK,MAAA,CAAO,MAAC,EAAM,OAAQ,CAAG,CAAC,CAAC,CAAA,CAIjE,OAAO,EAAQ,EAAG,EAAW,EAAK,MAAA,CAAO,GAAG,AAAC,CAAA,AAC9C,EAGC,GAAA,CAAA,EAAA,EAAA,QAAA,EAAS,GAAQ,CAEnB,CAFgB,EAEI,UAAhB,EAAM,KAAA,EAAqC,SAAhB,EAAM,KAAA,CAAkB,CAC/C,IAAA,EAAS,CAAC,GAAG,CAAA,AAAK,EACpB,MAAgB,UAAhB,EAAM,KAAA,CACR,EAAO,QAAA,CAAW,EAAQ,EAAM,QAAA,CAAU,EAAW,EAAK,MAAA,CAAO,UAAU,CAAC,EACnD,SAAhB,CAAgB,CAAV,KAAA,GACf,EAAO,IAAA,CAAO,EAAQ,EAAM,IAAA,CAAM,EAAW,EAAK,MAAA,CAAO,MAAM,EAAC,CAAA,CAE3D,CAAA,CAGT,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,GAAO,EAAF,CAAE,CAAI,CAAC,CAAC,EAAG,CAAC,CAAA,GAAM,CAAC,EAAG,EAAQ,EAAG,EAAW,EAAK,MAAA,CAAO,CAAC,CAAC,CAAC,CAAC,EAClF,CAGK,OAAA,EAAU,EAAO,EAC1B,EAD8B,AC5Bb,EAAQ,CAAC,EAAO,KAE7B,GAAqB,CAFiB,SAElC,OAAO,EACF,OAAA,EAGH,IAAA,EAAuB,AHfjB,SAAA,AACd,CAAA,CACA,CAAA,EAOY,MACZ,GAAI,CAAC,GAAK,SACR,OAEF,IAAM,GDUiB,ECVkC,AD8IlD,EApIgB,AAoIX,EApI4E,CAoI5E,CAAI,AAAC,IC9IkD,ADmJjE,CCnJkE,ED+I9D,AAAmB,EC/IC,GD8IG,MC9IM,MD+ItB,GAIY,UAAnB,OAAO,EACF,OAAA,EAGT,GAAI,AAAmB,CAAA,MAAX,MAAA,CACV,OAAO,EAAQ,MAAA,AAGjB,OAAM,AAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,SAAA,CAAU,GAAQ,CAAE,CAAA,CAC7D,CAD0D,CAAC,AAhJrD,CAAA,CAAA,EAAI,EACR,GAAA,CAAI,AAAC,GACmB,UAAnB,OAAO,EAIF,CAAA,EAAA,EAHY,EAAQ,OAAA,CAAQ,iBAAkB,AAAC,GAC7C,CAAA,CAAO,EACf,CACqB,CAAA,CAFD,CAEC,CAAA,CAGD,UAAnB,OAAO,EACF,CAAA,CAAA,EAAI,EAAO,CAAA,CAAA,CAGC,EAHD,GAGhB,EAAQ,IAAA,CAIH,CAAA,YAAA,EAHY,EAAQ,IAAA,CAAK,OAAA,CAAQ,SAAU,AAAC,GAC1C,CAAA,CAAO,EACf,CAC+B,CAAA,CAFX,EAEW,CAAA,CAG3B,CAAA,CAAA,EAAI,EAAQ,MAAM,CAAA,CAAA,CAC1B,EACA,IAAA,CAAK,EAAE,CAAC,CAAA,CAAA,EC/BP,GAAoC,KAAA,IAApC,EAAI,QAAA,CAAS,EAAiB,CACzB,MAAA,CACL,OAF8B,CAErB,EAAI,QAAA,CAAS,EAAiB,CACvC,YAAa,EAD0B,AAEvC,WAAY,EACd,EAGI,IAAA,EAAW,OAAO,OAAA,CAAQ,EAAI,QAAQ,EACzC,MAAA,CAAO,CAAC,CAAC,EAAG,CAAA,EAAM,EAAkB,UAAA,CAAW,GAAG,CAClD,AADmD,IACnD,CAAK,CAAC,CAAC,EAAI,CAAG,CAAH,AAAI,EAAI,EAAA,CAAM,EAAK,MAAA,CAAS,EAAK,MAAM,EAErD,GAAuB,GAAnB,EAAS,MAAA,CACX,OAGI,GAAA,CAAC,EAAa,EAAO,CAAI,CAAA,CAAS,CAAC,CAAd,AAAc,CACnC,EAAa,EAAkB,SAAA,CAAU,EAAY,MAAM,EAC1D,MAAA,SAAC,cAAS,aAAa,CAAU,CAC1C,EGnBgD,EAAM,GAAG,AACrD,GAAI,CAAC,EACI,OAAA,EAGH,GAAA,SAAC,CAAA,aAAS,CAAA,CAAA,CAAe,EAK/B,GAJI,AAAiB,YAAT,IAAA,EAIgB,kBAAxB,EAAQ,MAAA,CAAO,IAAA,CACV,OAAA,EAGH,IAAA,EAAiB,EAAI,SAAA,CAAU,EAAQ,MAAA,CAAO,QAAS,CAAA,CACvD,EAAa,EAAI,KAAA,CAAM,EAAQ,MAAA,CAAO,IAAI,CAAA,CAE1C,EAAoB,EAAc,GAIxC,OAAO,CAJ4C,CAIpC,CACb,CAHI,UADqB,CAIb,CAJ2B,GACK,MAAA,CAAO,AADF,EACO,KAAA,CAAM,EAAkB,MAAM,CAAC,EAIvF,iBACA,WAAY,QACZ,CAAA,CACD,CAAA,CACF,CACH,CCvCa,IAMP,EANO,AAMS,GAAG,OAIlB,MAVsB,AAMS,GAAG,AAIzB,EAAU,CAAA,EAA2B,AAC5C,EARI,KAQJ,EAAG,AAL2C,EAAA,EACjD,MAIM,AARkB,CAQP,EACvB,CAGO,QAVD,CAUU,CAJoB,CAIR,CAAA,EAAqB,AACxC,OAAA,EAAG,UAAA,CATW,AASA,GATG,SAU1B,CGzBO,CHwB8B,GGxBxB,AHe2B,EGfI,CAAC,AHeF,YGfG,CAAA,CHeW,EAAA,SGfC,CAAA,OAAY,CAAA,CAAA,IAEpE,CAF+E,EAE3E,AAgFN,SAAS,AAAY,CAAA,EACZ,AADgC,MAChC,uBAAqB,IAAA,CAAK,IAAc,CAAA,CAAQ,IAAZ,CAAiB,KAAA,CAAM,EACpE,EAlFkB,IAoFlB,AApF4B,CAAL,CAiFuD,IAAK,CAAA,EAG1E,AAAW,CAAA,EAAa,AAC3B,GAAA,CACF,IAAI,IAAI,EAAK,EAAI,UAAA,CAAW,GAAG,EAAI,mBAAqB,KAAA,CAAS,CAAA,CAAA,KAC3D,CACC,MAAA,CAAA,CAAA,CAEF,MAAA,CAAA,CACT,EA3FuC,GAC5B,EADiC,IACjC,CAAA,EAGH,IAAA,EAAU,EAAW,EAAA,CAAG,CAAA,CAAE,EA2BhC,MAzBI,CAAA,CAAsB,SAAtB,EAAW,EAAA,CAAG,CAAA,CAAE,GAA4B,YAAZ,GAKb,UAAnB,EAAmB,KAAZ,IAAyB,EAAQ,UAAA,CAAW,GAAG,GAAK,EAAQ,QAAA,CAAS,KAAI,CAAA,EAMlF,EAAW,IAAA,CACT,AAAC,GAAkB,AAAT,YAA4B,aAAT,GAAgC,cAAT,GAAiC,QAAT,IAQ5E,EAAY,IAAe,EAAY,IAKvC,AAAmB,AALG,MAA2B,WAK1C,GAAwB,EAAS,GAAA,CAAI,EAAO,CAAA,AAKzD,EAEM,EAAA,IAAe,IAAI,CAAnB,AACJ,QACA,KAFI,IAGJ,WACA,QACA,SACA,MACA,MACA,OACA,MACA,OACA,OACA,KACA,QACA,MACA,WACA,SACA,OACA,aACA,SACA,OACA,OACA,OACA,MACA,MACA,OACA,QACA,SACA,OACA,SACA,MACA,WACA,QACA,OACA,YACA,OACA,MACA,WACA,UACA,UACD,EAeD,SAAS,EAAY,CAAA,EAA2C,AACvD,OAAA,EAAK,IAAA,CAAK,AAAC,GAA+B,AAAnB,iBAAO,GAAmD,IAAI,GAA/B,EAAQ,KAAA,CAAM,OAAO,EACpF,CC7EgB,SAAA,EACd,CAAA,CACA,CAAA,CACA,CAAA,EACQ,AACR,GAAM,CAAC,QAAA,QAAQ,CAAA,SAAQ,CAAA,CAAW,CAAA,EAClC,GAAI,CAAC,EAAS,CACZ,IAAM,EAAM,iEACZ,OAAA,GAAQ,QAAQ,CAAA,kBAAA,EAAqB,EAAG,CAAA,CAAI,QAAC,kBAAQ,EAAiB,QAAA,CAAO,EACvE,AAAI,UAAU,EAAG,CAAA,AAGzB,GAAI,CAAC,EACH,OAAA,GAAQ,QAAQ,kEAAmE,QACjF,kBACA,SACA,CACD,CAAA,EACM,EAGL,GAAA,CAAC,EAAO,SAAA,CAAW,CACrB,IAAM,EAAM,kCACZ,OAAA,GAAQ,QAAQ,CAAA,kBAAA,EAAqB,EAAG,CAAA,CAAI,QAAC,kBAAQ,EAAiB,QAAA,CAAO,EACvE,AAAI,UAAU,EAAG,CAAA,AAGzB,IAAM,EAAyF,CAC7F,QAAS,CAAC,CAAA,CACV,QAAS,CAAA,CAAA,EAGL,EAAkB,EACtB,EACA,EACA,CAAC,YAAC,CAAA,gBAAY,CAAA,YAAgB,CAAA,OAAY,CAAA,CAAA,KAAW,WAGhD,GAAA,CAAkB,YAAlB,OAAO,EACJ,EAAO,YAAC,aAAY,gBAAY,iBAAe,EAAgB,OAAA,CAAM,EACrE,EAAc,YAAC,aAAY,QAA2C,CAAM,EAAA,CAAA,GAAO,CAAA,EAEnF,OAAA,GACF,EAAO,OAAA,CAAQ,IAAA,CAAK,CAClB,KA4EHE,CA5ES,CA4EU,EA5EW,IAC3B,MADqC,AAC9B,CAAA,EAAG,EAAM,IA2EqB,CA3ErB,CAAM,EA2EmB,CA3EhB,AA2EiB,GA3ED,EACvC,EAAM,MAAA,EADgC,CAAC,AACxB,CAAkB,MAAQ,EAC3C,CAAA,CAAA,CACA,OAAQ,EAAM,MAAA,AACf,CAAA,EAEI,EAGL,GACF,EAAO,OAAA,CAAQ,IAAA,CAAK,CAClB,MAAM,GAAqB,IAC3B,MADqC,AAC9B,CAAA,EAAG,EAAM,KAAA,CAAM,GAAG,GAAgB,EAAG,EAAM,MAAA,CAjEpC,CAiE0B,CAAC,AAAkB,CAAkB,MAAQ,EAAE,CAAA,CAAA,CACvF,OAAQ,EAAM,MAAA,CACf,EAGH,GAAM,CAAC,SAAA,WAAS,CAAA,CAAW,MAAA,CAAQ,EF7BnC,AAAY,CE6BuB,MF9BnC,AACY,EADkC,UAArB,OADQ,AACD,EE+BF,OFhCG,EAAuC,GEgCtE,OAAO,EAAO,SAAA,CACV,EAAO,SAAA,CAAU,GACjB,EAAO,SADwB,AACxB,EFjC4C,EAAY,EAAU,OAAA,IAEjF,EAAU,EAAQ,OAAA,CAAQ,MAAO,GAAE,CAAA,CAEZ,UAArB,OAAO,EACF,SAAC,CAAA,EAEH,CAAC,GAAG,CAAA,SAAW,CAAO,GE4BrB,GAAA,CAAC,EAAgB,MAAA,CAAA,EACf,GAAA,CAAC,IAAK,CAAA,CAAI,MAAO,CAAA,CAAM,WAAY,CAAA,CAAW,SAAU,CAAA,CAAA,CAAW,EAElE,MAAAD,CAAAA,EAAAA,EAAAA,CAAAA,EACL,EACA,CACE,OAAQ,YACR,KAAM,AH1FT,SAAS,AAAc,CAAA,EAAmE,AACzF,GAAA,SACJ,CAAA,CACA,UAAW,EAAa,SAAA,CACxB,KAAM,EAAQ,SAAA,CACd,GAAI,CAAA,MACJ,CAAA,MACA,CAAA,WACA,CAAA,SACA,CAAA,CAAA,CACE,EAEJ,GAAI,CAAC,EACG,MAAA,AAAI,MAAM,qBAAqB,EAEvC,GAAI,CAAC,EACG,MAAA,AAAI,MAAM,kBAAkB,EAEpC,GAAI,CAAC,EACG,MAAA,AAAI,MAAM,gBAAgB,EAElC,GAAgB,MAAZ,GAAmB,EAAQ,QAAA,CAAS,GAAG,EACnC,MAAA,AAAI,MAAM,mCAAmC,EAGrD,IAAM,EAA2B,YAAf,EAA2B,KAAA,EAAY,EACnD,EAAiB,YAAV,EAAsB,KAAA,EAAY,EACzC,EDqCF,GCrCO,EDuCF,EAAG,KAFI,AAEJ,CAAM,CAFA,IAEgB,KAAA,CAAM,CAAC,EAAT,AAAW,IAAA,CAAK,KAG5C,KACK,AC3CiB,ED2Cd,CC3CiB,CDuCiC,CAGhD,EAAE,AACJ,CAAM,EAAc,MAAM,IC1ChC,AD6CC,EC7CiB,MAAM,OAAA,CAAQ,GAClCD,CADsC,CAClB,EAAqB,IACzC,AAD6C,CAAC,CAK5C,EAAe,IAAI,gBAAgB,SACvC,KACA,OACA,EACA,KAAM,CAAA,CACP,EACG,GAAA,GACF,EAAa,GAAA,CAAI,YAAa,GAE5B,GACF,EAAa,CAH0B,EAG1B,CAAI,OAAQ,GAEvB,CAF2B,EAG7B,EAAa,GAAA,CAAI,YAAa,GAE5B,GACF,EAAa,CAH0B,EAG1B,CAAI,UAAW,GDzBvB,AAAC,ICyB6B,EDzBX,CC2BtB,CAAc,ED3BA,CC4B2B,AADxB,CD3BD,GC6BT,ED7Bc,AC6BF,CD7Be,EAAE,AC6Bd,AAAG,CACrB,IAAA,EDDH,ACCe,SDDN,AAAiB,CAAA,EAAgC,AAC3D,GAAA,CAAC,EAAY,EAAE,CAAG,CAAA,MAEhB,GAAA,CAAC,EAAgB,EAAW,GAAG,EAAY,CAAI,EAAG,KAAA,CAhDnC,AAgDyC,CAAb,IAE1C,OAAA,CACT,CAH8E,CCFvC,GAAG,AACzB,EAAA,GAAA,CAAI,cAAe,GAAS,MAH5B,AAG4B,EAH5B,GAAA,CAAI,cAAe,WAAW,EAM7C,IAAM,EAAW,CAAC,AAAY,QAAM,GAAK,EAAO,CAC5C,GACF,CAF8C,CAErC,IAAA,CAAK,GAEhB,IAAM,EAFmB,AAEJ,CACnB,oBACA,CAAA,GAAA,EAAM,EAAE,CAAA,CACR,CAAA,KAAA,EAAQ,EAAI,CAAA,CAAA,AACZ,CAAA,KAAA,EAAQ,mBAAmB,GAAgB,CAAA,CAC7C,CACI,OAAA,EAFwC,CAAC,AAG3C,EAAa,IAAA,CAAK,CAAA,KAAA,EAAQ,EAAI,CAAE,CAAF,CAEhC,EAAS,IAAA,CAAK,SAAU,OAAQ,CAAA,EAAG,EAAa,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,EAAI,EAAY,CAAE,EACpE,EAAS,IAAA,CAAK,AADoD,GACjD,CAC1B,EGgB8B,SAClB,YACA,OACA,KACA,EACA,OACA,KAAM,EACN,GAAI,CAAC,EAAO,6BAAA,EAAiC,SAAC,YAAS,CAAS,CACjE,AADiE,CACjE,CACH,EAEA,CAFA,AAEA,EACF,GAIJ,GAAI,EAAQ,CACV,IAAM,EAAa,EAAO,OAAA,CAAQ,MAAA,CAC5B,EAAa,EAAO,OAAA,CAAQ,MAAA,CAC9B,GAAA,CAAA,GAAc,CAAA,CAVd,AAUc,GAAA,CACd,CADc,EACN,gBAAkB,EAAO,GAAA,IAAO,mDAAmD,EAC7F,EAAO,GAAA,GACL,CAAA,iCAAA,EAAoC,EAAO,OAAA,CAAQ,MAAM,CAAA,WAAA,EAAc,EAAO,OAAA,CAAQ,MAAM,CAAA,EAAA,CAAA,CAG5F,EAAO,OAAA,CAAQ,MAAA,CAAS,IAC1B,CAD0B,EAClB,MAAM,0CAA0C,GAAA,AACtD,GAAQ,OAAS,EAAO,GAAA,IAAO,EAAO,QAAO,CAAA,CAE7C,EAAO,OAAA,CAAQ,MAAA,CAAS,EAAG,CACvB,IAAA,EAAA,IAAc,IACT,AADqB,AAA1B,IACK,GAAA,MAAC,AADN,CACM,CAAA,GAAS,EAAO,OAAA,CAClB,EAAA,GAAA,CAAI,EAAK,OAAA,CAAQ,EAAc,GAAG,EAAE,OAAA,CAAQ,WAAY,IAAI,CAAC,EAEvE,GAAQ,MAAM,0CAA2C,CAAC,GAAG,EAAQ,MAAA,CAAA,CAAQ,CAAC,CAAA,CAG5E,CAAA,GAAc,CAAA,CAAA,EAChB,GAAQ,WAAW,CAAA,CAIhB,OAAA,CACT", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}