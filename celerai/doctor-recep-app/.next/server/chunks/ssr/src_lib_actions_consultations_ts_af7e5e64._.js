module.exports={755284:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({addAdditionalAudio:()=>r,addAdditionalImages:()=>s,approveConsultation:()=>p,clearEditedNote:()=>x,createConsultation:()=>m,createConsultationWithFiles:()=>k,deleteAdditionalAudio:()=>y,deleteConsultationImage:()=>z,generateSummaryStream:()=>o,getConsultationStats:()=>A,getConsultations:()=>n,saveEditedNote:()=>u,saveStreamingSummary:()=>l,updateAdditionalNotes:()=>v,updateConsultationImages:()=>q,updateConsultationType:()=>t,updatePatientName:()=>w});var d=a.i(930768);a.i(996945);var e=a.i(719358),f=a.i(729149),g=a.i(76803),h=a.i(635512),i=a.i(870110);function j(a){if(!a)return[];if(Array.isArray(a))return a;if("string"==typeof a)try{return JSON.parse(a)}catch{}return[]}async function k(a,b,c,d,h,j="outpatient",l){try{let k=await (0,g.verifySession)();if(!k)return{success:!1,error:"Unauthorized"};let m=[a,...b,...c],n=(0,i.validateTotalSize)(m);if(!n.valid)return{success:!1,error:n.error};let o=crypto.randomUUID(),p=await (0,i.uploadFile)(a,k.userId,o,"audio");if(!p.success)return{success:!1,error:`Audio upload failed: ${p.error}`};let q=[];if(c.length>0){let a=await (0,i.uploadMultipleFiles)(c,k.userId,o,"audio");if(!a.success)return{success:!1,error:`Additional audio upload failed: ${a.errors?.join(", ")}`};q=a.urls||[]}let r=[];if(b.length>0){let a=await (0,i.uploadMultipleFiles)(b,k.userId,o,"image");if(!a.success)return{success:!1,error:`Image upload failed: ${a.errors?.join(", ")}`};r=a.urls||[]}let s=(0,i.calculateTotalFileSize)(m),t=await (0,f.createClient)(),{data:u,error:v}=await t.from("consultations").insert({id:o,doctor_id:k.userId,primary_audio_url:p.url,additional_audio_urls:q,image_urls:r,submitted_by:d,status:"pending",total_file_size_bytes:s,doctor_notes:h||null,consultation_type:j,patient_name:l||null}).select().single();if(v)return console.error("Database error:",v),{success:!1,error:"Failed to create consultation"};return(0,e.revalidatePath)("/dashboard"),(0,e.revalidatePath)("/mobile"),{success:!0,data:u}}catch(a){return console.error("Create consultation with files error:",a),{success:!1,error:"An unexpected error occurred"}}}async function l(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({ai_generated_note:b,status:"generated"}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Update error:",h),{success:!1,error:"Failed to save generated summary"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:b}}catch(a){return console.error("Save streaming summary error:",a),{success:!1,error:"An unexpected error occurred"}}}async function m(a){try{let b=await (0,g.verifySession)();if(!b)return{success:!1,error:"Unauthorized"};let c=a.get("primary_audio_url"),d=a.get("additional_audio_urls"),i=a.get("image_urls"),j=a.get("total_file_size_bytes"),k=[],l=[],m=0;if(d)try{k=JSON.parse(d)}catch(a){console.error("Error parsing additional_audio_urls:",a)}if(i)try{l=JSON.parse(i)}catch(a){console.error("Error parsing image_urls:",a)}j&&(m=parseInt(j,10)||0);let n=h.ConsultationCreateSchema.safeParse({primary_audio_url:c,additional_audio_urls:k,image_urls:l,submitted_by:a.get("submitted_by"),total_file_size_bytes:m});if(!n.success)return{success:!1,error:"Invalid form data: "+JSON.stringify(n.error.flatten().fieldErrors)};let{primary_audio_url:o,additional_audio_urls:p,image_urls:q,submitted_by:r,total_file_size_bytes:s}=n.data,t=await (0,f.createClient)(),{data:u,error:v}=await t.from("consultations").insert({doctor_id:b.userId,primary_audio_url:o,additional_audio_urls:p||[],image_urls:q||[],submitted_by:r,status:"pending",total_file_size_bytes:s||0}).select().single();if(v)return console.error("Database error:",v),{success:!1,error:"Failed to create consultation"};return(0,e.revalidatePath)("/dashboard"),(0,e.revalidatePath)("/mobile"),{success:!0,data:u}}catch(a){return console.error("Create consultation error:",a),{success:!1,error:"An unexpected error occurred"}}}async function n({page:a=1,pageSize:b=15,status:c,searchTerm:d}={}){try{let e=await (0,g.verifySession)();if(!e)return{success:!1,error:"Unauthorized"};let h=await (0,f.createClient)(),i=(a-1)*b,k=i+b-1,l=h.from("consultations").select("id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes",{count:"exact"}).eq("doctor_id",e.userId).order("created_at",{ascending:!1}).range(i,k);if(c&&(l=l.eq("status",c)),d&&d.trim()){let a=d.trim().split(/\s+/).join(" & ");l=l.textSearch("fts",a)}let{data:m,error:n,count:o}=await l;if(n){if(console.error("Database error:",n),"PGRST103"===n.code)return{success:!0,data:{consultations:[],hasMore:!1,totalCount:o||0}};return{success:!1,error:"Failed to fetch consultations"}}let p=(m||[]).map(a=>({id:a.id,doctor_id:a.doctor_id,submitted_by:a.submitted_by,primary_audio_url:a.primary_audio_url,additional_audio_urls:j(a.additional_audio_urls),image_urls:j(a.image_urls),ai_generated_note:a.ai_generated_note??void 0,edited_note:a.edited_note??void 0,status:a.status,patient_number:a.patient_number??void 0,patient_name:a.patient_name??void 0,total_file_size_bytes:a.total_file_size_bytes??0,file_retention_until:a.file_retention_until,created_at:a.created_at,updated_at:a.updated_at,consultation_type:a.consultation_type??"outpatient",doctor_notes:a.doctor_notes??void 0,additional_notes:a.additional_notes??void 0})),q=(o||0)>k+1;return{success:!0,data:{consultations:p,hasMore:q,totalCount:o||0}}}catch(a){return console.error("Get consultations error:",a),{success:!1,error:"An unexpected error occurred"}}}async function o(a,b,c,d,h,i,k,m){try{let n=await (0,g.verifySession)();if(!n)return h?.("Unauthorized"),{success:!1,error:"Unauthorized"};let o=await (0,f.createClient)(),{data:p,error:q}=await o.from("consultations").select("id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)").eq("id",a).eq("doctor_id",n.userId).single();if(q||!p)return h?.("Consultation not found"),{success:!1,error:"Consultation not found"};let r=j(p.additional_audio_urls),s=[...j(p.image_urls),...b||[]],t=await fetch("http://localhost:3005/api/generate-summary-stream",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({primary_audio_url:p.primary_audio_url,additional_audio_urls:Array.isArray(r)?r:[],image_urls:Array.isArray(s)?s:[],submitted_by:p.submitted_by||"doctor",consultation_type:i||p.consultation_type||"outpatient",doctor_notes:k||p.doctor_notes||void 0,additional_notes:m||void 0,patient_name:p.patient_name||void 0,doctor_name:p.doctors.name||void 0,created_at:p.created_at||void 0})});if(!t.ok){let a=`HTTP ${t.status}: ${t.statusText}`;return h?.(a),{success:!1,error:a}}let u=t.body?.getReader();if(!u)return h?.("No reader available"),{success:!1,error:"No reader available"};let v=new TextDecoder,w="";try{for(;;){let{done:a,value:b}=await u.read();if(a)break;for(let a of v.decode(b,{stream:!0}).split("\n"))if(a.startsWith("data: "))try{let b=JSON.parse(a.slice(6));if("chunk"===b.type&&b.text){let a=b.text.replace(/\\n/g,"\n").replace(/\n\n+/g,"\n\n");w+=a,c?.(a)}}catch(a){}}d?.(w);try{let b=await l(a,w);if(!b.success)return console.error("Failed to save streaming summary:",b.error),h?.(`Summary generated but failed to save: ${b.error}`),{success:!1,error:`Summary generated but failed to save: ${b.error}`}}catch(a){return console.error("Error saving streaming summary:",a),h?.("Summary generated but failed to save. Please try regenerate."),{success:!1,error:"Summary generated but failed to save. Please try regenerate."}}return(0,e.revalidatePath)("/dashboard"),{success:!0,data:w}}catch(b){console.error("❌ Generate error:",b);let a=`Failed to generate summary: ${b instanceof Error?b.message:"Unknown error"}`;return h?.(a),{success:!1,error:a}}}catch(a){return console.error("Generate streaming summary error:",a),h?.("An unexpected error occurred"),{success:!1,error:"An unexpected error occurred"}}}async function p(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};if(!h.ConsultationUpdateSchema.safeParse({edited_note:b}).success)return{success:!1,error:"Invalid note content"};let d=await (0,f.createClient)(),{error:i}=await d.from("consultations").update({edited_note:b,status:"approved"}).eq("id",a).eq("doctor_id",c.userId);if(i)return console.error("Update error:",i),{success:!1,error:"Failed to approve consultation"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Approve consultation error:",a),{success:!1,error:"An unexpected error occurred"}}}async function q(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({image_urls:b}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Update consultation images error:",h),{success:!1,error:"Failed to update consultation images"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Update consultation images error:",a),{success:!1,error:"An unexpected error occurred"}}}async function r(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{data:h,error:k}=await d.from("consultations").select("additional_audio_urls, status").eq("id",a).eq("doctor_id",c.userId).single();if(k||!h)return{success:!1,error:"Consultation not found"};if("approved"===h.status)return{success:!1,error:`Cannot add audio to approved consultations. Current status: ${h.status}`};let l=await (0,i.uploadFile)(b,c.userId,a,"audio");if(!l.success)return{success:!1,error:`Audio upload failed: ${l.error}`};let m=j(h.additional_audio_urls);m.push(l.url);let{error:n}=await d.from("consultations").update({additional_audio_urls:m}).eq("id",a);if(n)return{success:!1,error:"Failed to add additional audio"};return(0,e.revalidatePath)("/dashboard"),(0,e.revalidatePath)("/record"),{success:!0,data:!0}}catch(a){return console.error("Add additional audio error:",a),{success:!1,error:"An unexpected error occurred"}}}async function s(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{data:h,error:k}=await d.from("consultations").select("image_urls, status").eq("id",a).eq("doctor_id",c.userId).single();if(k||!h)return{success:!1,error:"Consultation not found"};if("approved"===h.status)return{success:!1,error:`Cannot add images to approved consultations. Current status: ${h.status}`};let l=await Promise.all(b.map(b=>(0,i.uploadFile)(b,c.userId,a,"image"))),m=l.filter(a=>!a.success);if(m.length>0){let a=m.map(a=>a.error).join(", ");return{success:!1,error:`Image upload failed: ${a}`}}let n=j(h.image_urls),o=l.map(a=>a.url).filter(a=>a),p=[...n,...o],{error:q}=await d.from("consultations").update({image_urls:p}).eq("id",a);if(q)return{success:!1,error:"Failed to add additional images"};return(0,e.revalidatePath)("/dashboard"),(0,e.revalidatePath)("/record"),{success:!0,data:!0}}catch(a){return console.error("Add additional images error:",a),{success:!1,error:"An unexpected error occurred"}}}async function t(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({consultation_type:b}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Update consultation type error:",h),{success:!1,error:"Failed to update consultation type"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Update consultation type error:",a),{success:!1,error:"An unexpected error occurred"}}}async function u(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({edited_note:b}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Save edited note error:",h),{success:!1,error:"Failed to save edited note"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Save edited note error:",a),{success:!1,error:"An unexpected error occurred"}}}async function v(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({additional_notes:b||null}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Update additional notes error:",h),{success:!1,error:"Failed to update additional notes"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Update additional notes error:",a),{success:!1,error:"An unexpected error occurred"}}}async function w(a,b){try{let c=await (0,g.verifySession)();if(!c)return{success:!1,error:"Unauthorized"};let d=await (0,f.createClient)(),{error:h}=await d.from("consultations").update({patient_name:b||null}).eq("id",a).eq("doctor_id",c.userId);if(h)return console.error("Update patient name error:",h),{success:!1,error:"Failed to update patient name"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Update patient name error:",a),{success:!1,error:"An unexpected error occurred"}}}async function x(a){try{let b=await (0,g.verifySession)();if(!b)return{success:!1,error:"Unauthorized"};let c=await (0,f.createClient)(),{error:d}=await c.from("consultations").update({edited_note:null}).eq("id",a).eq("doctor_id",b.userId);if(d)return console.error("Clear edited note error:",d),{success:!1,error:"Failed to clear edited note"};return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Clear edited note error:",a),{success:!1,error:"An unexpected error occurred"}}}async function y(b,c){try{let d=await (0,g.verifySession)();if(!d)return{success:!1,error:"Unauthorized"};let h=await (0,f.createClient)(),{data:i,error:k}=await h.from("consultations").select("additional_audio_urls, status").eq("id",b).eq("doctor_id",d.userId).single();if(k||!i)return{success:!1,error:"Consultation not found"};if("approved"===i.status)return{success:!1,error:"Cannot delete files from approved consultations"};let l=j(i.additional_audio_urls).filter(a=>a!==c),{error:m}=await h.from("consultations").update({additional_audio_urls:l}).eq("id",b).eq("doctor_id",d.userId);if(m)return console.error("Delete additional audio error:",m),{success:!1,error:"Failed to delete additional audio"};try{let{deleteFile:b}=await a.r(482970)(a.i),d=c.split("/").pop()||"";await b(d,"audio")}catch(a){console.error("Storage deletion error:",a)}return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Delete additional audio error:",a),{success:!1,error:"An unexpected error occurred"}}}async function z(b,c){try{let d=await (0,g.verifySession)();if(!d)return{success:!1,error:"Unauthorized"};let h=await (0,f.createClient)(),{data:i,error:k}=await h.from("consultations").select("image_urls, status").eq("id",b).eq("doctor_id",d.userId).single();if(k||!i)return{success:!1,error:"Consultation not found"};if("approved"===i.status)return{success:!1,error:"Cannot delete files from approved consultations"};let l=j(i.image_urls).filter(a=>a!==c),{error:m}=await h.from("consultations").update({image_urls:l}).eq("id",b).eq("doctor_id",d.userId);if(m)return console.error("Delete consultation image error:",m),{success:!1,error:"Failed to delete image"};try{let{deleteFile:b}=await a.r(482970)(a.i),d=c.split("/").pop()||"";await b(d,"image")}catch(a){console.error("Storage deletion error:",a)}return(0,e.revalidatePath)("/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Delete consultation image error:",a),{success:!1,error:"An unexpected error occurred"}}}async function A(){try{let a=await (0,g.verifySession)();if(!a)return{success:!1,error:"Unauthorized"};let b=await (0,f.createClient)(),{data:c,error:d}=await b.rpc("get_consultation_stats",{doctor_uuid:a.userId});if(d)return console.error("Database error:",d),{success:!1,error:"Failed to fetch consultation stats"};let e=c?.[0]||{total_consultations:0,pending_consultations:0,approved_consultations:0,today_consultations:0};return{success:!0,data:{total_consultations:Number(e.total_consultations),pending_consultations:Number(e.pending_consultations),approved_consultations:Number(e.approved_consultations),today_consultations:Number(e.today_consultations)}}}catch(a){return console.error("Get consultation stats error:",a),{success:!1,error:"An unexpected error occurred"}}}(0,a.i(377991).ensureServerEntryExports)([k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A]),(0,d.registerServerReference)(k,"7fc832fd06db7232f37f9b1b44631bd2956c9b940c",null),(0,d.registerServerReference)(l,"608aaaf92844312537830e8c8d0c49debb89bda21b",null),(0,d.registerServerReference)(m,"40d596bef0460f198d588bbf8dede026bc85cca740",null),(0,d.registerServerReference)(n,"40181945d99bd1b1c806279418d8c4e384a1b0bd15",null),(0,d.registerServerReference)(o,"7f7dd3bdf6242116733f7ef527b1e8f307d343dacd",null),(0,d.registerServerReference)(p,"60648db8471ac7da6666e2d2e8b2cf27a97b9b4688",null),(0,d.registerServerReference)(q,"6016d1147dadc5ab75f7387f44ab799f7cd595708b",null),(0,d.registerServerReference)(r,"60b3bf115b7639c2355ff16ee9fa53425dad65720c",null),(0,d.registerServerReference)(s,"60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e",null),(0,d.registerServerReference)(t,"60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb",null),(0,d.registerServerReference)(u,"6090ac31595b581fab8f32262309efcdcab1c7eb47",null),(0,d.registerServerReference)(v,"6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7",null),(0,d.registerServerReference)(w,"609aacf458083825c0e86748f13c86d5773450588b",null),(0,d.registerServerReference)(x,"40cad8ac11d66cae6a36deaf0347461ff74e2f07d8",null),(0,d.registerServerReference)(y,"60fe749e3c9b3a6222eaf37afc1421459616909387",null),(0,d.registerServerReference)(z,"60c855f485dc14093b0fee942b2204ae7dca69142e",null),(0,d.registerServerReference)(A,"0021892c2090f634723ac0a0d1cd1d8c637aae5b9c",null)}};

//# sourceMappingURL=src_lib_actions_consultations_ts_af7e5e64._.js.map