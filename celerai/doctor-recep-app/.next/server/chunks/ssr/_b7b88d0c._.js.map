{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts", "turbopack:///[project]/src/lib/actions/data:114051 <text/javascript>", "turbopack:///[project]/src/components/auth/login-form.tsx"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n", "/* __next_internal_action_entry_do_not_use__ [{\"6064c1bc78024112540d1fec516d46f480c1310eb6\":\"login\"},\"src/lib/actions/auth.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var login=/*#__PURE__*/createServerReference(\"6064c1bc78024112540d1fec516d46f480c1310eb6\",callServer,void 0,findSourceMapURL,\"login\");", "'use client'\n\nimport { useActionState, useEffect } from 'react'\nimport { login } from '@/lib/actions/auth'\nimport { FormState } from '@/lib/types'\nimport { trackAuth } from '@/lib/analytics'\n\n// CORRECTED initialState to conform to FormState interface\nconst initialState: FormState = {\n  success: false,\n  message: '',\n  // fieldErrors is optional, so it can be omitted or set to an empty object if needed\n  // fieldErrors: {},\n}\n\nexport function LoginForm() {\n  const [state, formAction, isPending] = useActionState(login, initialState)\n\n  // Track successful login (when redirecting to dashboard)\n  useEffect(() => {\n    if (state?.success && state?.message?.includes('Redirecting')) {\n      trackAuth('login_successful')\n    }\n  }, [state?.success, state?.message])\n\n  // Handle form submission to track login attempt\n  const handleSubmit = (formData: FormData) => {\n    // Track login attempt\n    trackAuth('login_attempted')\n\n    // Call the original form action\n    formAction(formData)\n  }\n\n  return (\n    <form action={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"<EMAIL>\"\n          />\n          {state?.fieldErrors?.email && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.email[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Password\n          </label>\n          <input\n            id=\"password\"\n            name=\"password\"\n            type=\"password\"\n            autoComplete=\"current-password\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Enter your password\"\n          />\n          {state?.fieldErrors?.password && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.password[0]}</span>\n            </p>\n          )}\n        </div>\n      </div>\n\n      {state?.message && (\n        <div className=\"rounded-xl p-4 border bg-gradient-to-r from-red-50 to-pink-50 border-red-200\">\n          <p className=\"text-sm font-medium flex items-center space-x-2 text-red-800\">\n            <span className=\"w-2 h-2 rounded-full bg-red-400\"></span>\n            <span>{state.message}</span>\n          </p>\n        </div>\n      )}\n\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isPending}\n          className=\"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2\"\n        >\n          {isPending ? (\n            <>\n              <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n              <span>Signing you in...</span>\n            </>\n          ) : (\n            <>\n              <span>Continue the Magic</span>\n              <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n              </svg>\n            </>\n          )}\n        </button>\n      </div>\n    </form>\n  )\n}"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "wIAGSA,UAAU,CAAA,kBAAVA,EAAAA,UAAU,EASNC,qBAAqB,CAAA,kBAArBA,GARJC,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,8EADE,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,IAQpBD,EAGC,AAFX,CAAA,CAEW,AAFV,CAEU,AAFTE,CAES,OAFDC,AAKXH,GALc,CAACI,KAIXC,OAJuB,CAIf,EAFRA,EAGiB,MAHT,WAEiC,iCCjBoF,EAAA,CAAA,CAAA,eAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAmB,CAAA,EAAA,EAAA,CAAb,WAAW,SAAE,AAAoB,EAAE,cAAtB,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAC,AAAnC,oBAAkB,+ECE9X,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAGA,IAAM,EAA0B,CAC9B,SAAS,EACT,QAAS,EAGX,EAEO,SAAS,IACd,GAAM,CAAC,EAAO,EAAY,EAAU,CAAG,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,EAAA,KAAK,CAAE,SAG7D,GAAA,CAHuC,CAGvC,SAAQ,AAAR,EAAU,KACJ,CAJgD,EAIzC,SAAW,GAAO,MAD/B,GACwC,SAAS,gBAAgB,AAC7D,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,mBAEd,EAAG,CAAC,GAAO,CAFP,OAEgB,GAAO,QAAQ,EAYjC,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,OATa,AAAC,CASN,GAPd,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,mBAGV,EAAW,EACb,EAG8B,CAP5B,SAOsC,sBACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,yDAAgD,kBAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,QACL,aAAa,QACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,uBAEb,GAAO,aAAa,OACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,KAAK,CAAC,EAAE,SAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,WAAW,UAAU,yDAAgD,aAGpF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,WACL,aAAa,mBACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,wBAEb,GAAO,aAAa,UACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,QAAQ,CAAC,EAAE,YAM3C,GAAO,SACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wFACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,yEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,OAAO,QAK1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,2WAET,EACC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8EACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,yBAGR,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,uBACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDAAyD,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAChH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wCAQrF", "ignoreList": [0]}