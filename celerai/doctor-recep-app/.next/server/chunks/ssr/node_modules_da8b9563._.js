module.exports={447268:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";var f={};function g(a,b){!b.unsigned&&--a;let c=b.unsigned?0:-Math.pow(2,a),d=Math.pow(2,a)-1,e=b.moduloBitLength?Math.pow(2,b.moduloBitLength):Math.pow(2,a),f=b.moduloBitLength?Math.pow(2,b.moduloBitLength-1):Math.pow(2,a-1);return function(a,g){g||(g={});let h=+a;if(g.enforceRange){if(!Number.isFinite(h))throw TypeError("Argument is not a finite number");if((h=(h<0?-1:1)*Math.floor(Math.abs(h)))<c||h>d)throw TypeError("Argument is not in byte range");return h}if(!isNaN(h)&&g.clamp){var i;return(h=(i=h)%1==.5&&(1&i)==0?Math.floor(i):Math.round(i))<c&&(h=c),h>d&&(h=d),h}if(!Number.isFinite(h)||0===h)return 0;if(h=(h<0?-1:1)*Math.floor(Math.abs(h))%e,!b.unsigned&&h>=f)return h-e;if(b.unsigned){if(h<0)h+=e;else if(-0===h)return 0}return h}}d.exports=f,f.void=function(){},f.boolean=function(a){return!!a},f.byte=g(8,{unsigned:!1}),f.octet=g(8,{unsigned:!0}),f.short=g(16,{unsigned:!1}),f["unsigned short"]=g(16,{unsigned:!0}),f.long=g(32,{unsigned:!1}),f["unsigned long"]=g(32,{unsigned:!0}),f["long long"]=g(32,{unsigned:!1,moduloBitLength:64}),f["unsigned long long"]=g(32,{unsigned:!0,moduloBitLength:64}),f.double=function(a){let b=+a;if(!Number.isFinite(b))throw TypeError("Argument is not a finite floating-point value");return b},f["unrestricted double"]=function(a){let b=+a;if(isNaN(b))throw TypeError("Argument is NaN");return b},f.float=f.double,f["unrestricted float"]=f["unrestricted double"],f.DOMString=function(a,b){return(b||(b={}),b.treatNullAsEmptyString&&null===a)?"":String(a)},f.ByteString=function(a,b){let c,d=String(a);for(let a=0;void 0!==(c=d.codePointAt(a));++a)if(c>255)throw TypeError("Argument is not a valid bytestring");return d},f.USVString=function(a){let b=String(a),c=b.length,d=[];for(let a=0;a<c;++a){let e=b.charCodeAt(a);if(e<55296||e>57343)d.push(String.fromCodePoint(e));else if(56320<=e&&e<=57343)d.push(String.fromCodePoint(65533));else if(a===c-1)d.push(String.fromCodePoint(65533));else{let c=b.charCodeAt(a+1);if(56320<=c&&c<=57343){let b=1023&e,f=1023&c;d.push(String.fromCodePoint(65536+1024*b+f)),++a}else d.push(String.fromCodePoint(65533))}}return d.join("")},f.Date=function(a,b){if(!(a instanceof Date))throw TypeError("Argument is not a Date object");if(!isNaN(a))return a},f.RegExp=function(a,b){return a instanceof RegExp||(a=new RegExp(a)),a}},113265:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";d.exports.mixin=function(a,b){let c=Object.getOwnPropertyNames(b);for(let d=0;d<c.length;++d)Object.defineProperty(a,c[d],Object.getOwnPropertyDescriptor(b,c[d]))},d.exports.wrapperSymbol=Symbol("wrapper"),d.exports.implSymbol=Symbol("impl"),d.exports.wrapperForImpl=function(a){return a[d.exports.wrapperSymbol]},d.exports.implForWrapper=function(a){return a[d.exports.implSymbol]}},56396:function(a){"use strict";var{g:b,__dirname:c,m:d,e:e}=a,f=a.r(172481),g=a.r(251720),h={TRANSITIONAL:0,NONTRANSITIONAL:1};function i(a){return a.split("\0").map(function(a){return a.normalize("NFC")}).join("\0")}function j(a){for(var b=0,c=g.length-1;b<=c;){var d=Math.floor((b+c)/2),e=g[d];if(e[0][0]<=a&&e[0][1]>=a)return e;e[0][0]>a?c=d-1:b=d+1}return null}var k=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;function l(a){return a.replace(k,"_").length}var m=/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08E4-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C03\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D01-\u0D03\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u18A9\u1920-\u192B\u1930-\u193B\u19B0-\u19C0\u19C8\u19C9\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF8\u1CF9\u1DC0-\u1DF5\u1DFC-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C4\uA8E0-\uA8F1\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2D]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD804[\uDC00-\uDC02\uDC38-\uDC46\uDC7F-\uDC82\uDCB0-\uDCBA\uDD00-\uDD02\uDD27-\uDD34\uDD73\uDD80-\uDD82\uDDB3-\uDDC0\uDE2C-\uDE37\uDEDF-\uDEEA\uDF01-\uDF03\uDF3C\uDF3E-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF57\uDF62\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDCB0-\uDCC3\uDDAF-\uDDB5\uDDB8-\uDDC0\uDE30-\uDE40\uDEAB-\uDEB7]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF51-\uDF7E\uDF8F-\uDF92]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD83A[\uDCD0-\uDCD6]|\uDB40[\uDD00-\uDDEF]/;function n(a,b,c){var d=function(a,b,c){for(var d=!1,e="",f=l(a),g=0;g<f;++g){var i=a.codePointAt(g),k=j(i);switch(k[1]){case"disallowed":d=!0,e+=String.fromCodePoint(i);break;case"ignored":break;case"mapped":e+=String.fromCodePoint.apply(String,k[2]);break;case"deviation":c===h.TRANSITIONAL?e+=String.fromCodePoint.apply(String,k[2]):e+=String.fromCodePoint(i);break;case"valid":e+=String.fromCodePoint(i);break;case"disallowed_STD3_mapped":b?(d=!0,e+=String.fromCodePoint(i)):e+=String.fromCodePoint.apply(String,k[2]);break;case"disallowed_STD3_valid":b&&(d=!0),e+=String.fromCodePoint(i)}}return{string:e,error:d}}(a,b,c);d.string=i(d.string);for(var e=d.string.split("."),g=0;g<e.length;++g)try{var k=function(a,b){"xn--"===a.substr(0,4)&&(a=f.toUnicode(a),h.NONTRANSITIONAL);var c=!1;(i(a)!==a||"-"===a[3]&&"-"===a[4]||"-"===a[0]||"-"===a[a.length-1]||-1!==a.indexOf(".")||0===a.search(m))&&(c=!0);for(var d=l(a),e=0;e<d;++e){var g=j(a.codePointAt(e));if(n===h.TRANSITIONAL&&"valid"!==g[1]||n===h.NONTRANSITIONAL&&"valid"!==g[1]&&"deviation"!==g[1]){c=!0;break}}return{label:a,error:c}}(e[g]);e[g]=k.label,d.error=d.error||k.error}catch(a){d.error=!0}return{string:e.join("."),error:d.error}}d.exports.toASCII=function(a,b,c,d){var e=n(a,b,c),g=e.string.split(".");if(g=g.map(function(a){try{return f.toASCII(a)}catch(b){return e.error=!0,a}}),d){var h=g.slice(0,g.length-1).join(".").length;(h.length>253||0===h.length)&&(e.error=!0);for(var i=0;i<g.length;++i)if(g.length>63||0===g.length){e.error=!0;break}}return e.error?null:g.join(".")},d.exports.toUnicode=function(a,b){var c=n(a,b,h.NONTRANSITIONAL);return{domain:c.string,error:c.error}},d.exports.PROCESSING_OPTIONS=h},852477:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b=a.r(172481),c=a.r(56396),e={ftp:21,file:null,gopher:70,http:80,https:443,ws:80,wss:443},y=Symbol("failure");function f(a){return b.ucs2.decode(a).length}function g(a,b){let c=a[b];return isNaN(c)?void 0:String.fromCodePoint(c)}function h(a){return a>=48&&a<=57}function i(a){return a>=65&&a<=90||a>=97&&a<=122}function j(a){return h(a)||a>=65&&a<=70||a>=97&&a<=102}function k(a){return"."===a||"%2e"===a.toLowerCase()}function l(a){return 2===a.length&&i(a.codePointAt(0))&&(":"===a[1]||"|"===a[1])}function m(a){return void 0!==e[a]}function n(a){return m(a.scheme)}function o(a){let b=a.toString(16).toUpperCase();return 1===b.length&&(b="0"+b),"%"+b}function p(a){return a<=31||a>126}let z=new Set([32,34,35,60,62,63,96,123,125]);function q(a){return p(a)||z.has(a)}let A=new Set([47,58,59,61,64,91,92,93,94,124]);function r(a){return q(a)||A.has(a)}function s(a,b){let c=String.fromCodePoint(a);if(b(a)){let a=new Buffer(c),b="";for(let c=0;c<a.length;++c)b+=o(a[c]);return b}return c}function t(a,d){if("["===a[0])return"]"!==a[a.length-1]?y:function(a){let c=[0,0,0,0,0,0,0,0],d=0,e=null,f=0;if(58===(a=b.ucs2.decode(a))[f]){if(58!==a[f+1])return y;f+=2,e=++d}for(;f<a.length;){if(8===d)return y;if(58===a[f]){if(null!==e)return y;++f,e=++d;continue}let b=0,i=0;for(;i<4&&j(a[f]);)b=16*b+parseInt(g(a,f),16),++f,++i;if(46===a[f]){if(0===i||(f-=i,d>6))return y;let b=0;for(;void 0!==a[f];){let e=null;if(b>0)if(46!==a[f]||!(b<4))return y;else++f;if(!h(a[f]))return y;for(;h(a[f]);){let b=parseInt(g(a,f));if(null===e)e=b;else{if(0===e)return y;e=10*e+b}if(e>255)return y;++f}c[d]=256*c[d]+e,(2==++b||4===b)&&++d}if(4!==b)return y;break}if(58===a[f]){if(void 0===a[++f])return y}else if(void 0!==a[f])return y;c[d]=b,++d}if(null!==e){let a=d-e;for(d=7;0!==d&&a>0;){let b=c[e+a-1];c[e+a-1]=c[d],c[d]=b,--d,--a}}else if(null===e&&8!==d)return y;return c}(a.substring(1,a.length-1));if(!d){var e=a;if(-1!==e.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|\?|@|\[|\\|\]/))return y;let c="",d=b.ucs2.decode(e);for(let a=0;a<d.length;++a)c+=s(d[a],p);return c}let f=function(a){let b=new Buffer(a),c=[];for(let a=0;a<b.length;++a)37!==b[a]?c.push(b[a]):37===b[a]&&j(b[a+1])&&j(b[a+2])?(c.push(parseInt(b.slice(a+1,a+3).toString(),16)),a+=2):c.push(b[a]);return new Buffer(c).toString()}(a),i=c.toASCII(f,!1,c.PROCESSING_OPTIONS.NONTRANSITIONAL,!1);if(null===i||-1!==i.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|%|\/|:|\?|@|\[|\\|\]/))return y;let k=function(a){let b=a.split(".");if(""===b[b.length-1]&&b.length>1&&b.pop(),b.length>4)return a;let c=[];for(let d of b){if(""===d)return a;let b=function(a){let b=10;return(a.length>=2&&"0"===a.charAt(0)&&"x"===a.charAt(1).toLowerCase()?(a=a.substring(2),b=16):a.length>=2&&"0"===a.charAt(0)&&(a=a.substring(1),b=8),""===a)?0:(10===b?/[^0-9]/:16===b?/[^0-9A-Fa-f]/:/[^0-7]/).test(a)?y:parseInt(a,b)}(d);if(b===y)return a;c.push(b)}for(let a=0;a<c.length-1;++a)if(c[a]>255)return y;if(c[c.length-1]>=Math.pow(256,5-c.length))return y;let d=c.pop(),e=0;for(let a of c)d+=a*Math.pow(256,3-e),++e;return d}(i);return"number"==typeof k||k===y?k:i}function u(a){if("number"==typeof a){let b="",c=a;for(let a=1;a<=4;++a)b=String(c%256)+b,4!==a&&(b="."+b),c=Math.floor(c/256);return b}return a instanceof Array?"["+function(a){let b="",c=function(a){let b=null,c=1,d=null,e=0;for(let f=0;f<a.length;++f)0!==a[f]?(e>c&&(b=d,c=e),d=null,e=0):(null===d&&(d=f),++e);return e>c&&(b=d,c=e),{idx:b,len:c}}(a).idx,d=!1;for(let e=0;e<=7;++e)if(!d||0!==a[e]){if(d&&(d=!1),c===e){b+=0===e?"::":":",d=!0;continue}b+=a[e].toString(16),7!==e&&(b+=":")}return b}(a)+"]":a}function v(a){let b=a.path;if(0!==b.length){var c;"file"===a.scheme&&1===b.length&&(c=b[0],/^[A-Za-z]:$/.test(c))||b.pop()}}function w(a){return""!==a.username||""!==a.password}function x(a,c,d,e,f){if(this.pointer=0,this.input=a,this.base=c||null,this.encodingOverride=d||"utf-8",this.stateOverride=f,this.url=e,this.failure=!1,this.parseError=!1,!this.url){this.url={scheme:"",username:"",password:"",host:null,port:null,path:[],query:null,fragment:null,cannotBeABaseURL:!1};let a=this.input.replace(/^[\u0000-\u001F\u0020]+|[\u0000-\u001F\u0020]+$/g,"");a!==this.input&&(this.parseError=!0),this.input=a}let g=this.input.replace(/\u0009|\u000A|\u000D/g,"");for(g!==this.input&&(this.parseError=!0),this.input=g,this.state=f||"scheme start",this.buffer="",this.atFlag=!1,this.arrFlag=!1,this.passwordTokenSeenFlag=!1,this.input=b.ucs2.decode(this.input);this.pointer<=this.input.length;++this.pointer){let a=this.input[this.pointer],b=isNaN(a)?void 0:String.fromCodePoint(a),c=this["parse "+this.state](a,b);if(c){if(c===y){this.failure=!0;break}}else break}}x.prototype["parse scheme start"]=function(a,b){if(i(a))this.buffer+=b.toLowerCase(),this.state="scheme";else{if(this.stateOverride)return this.parseError=!0,y;this.state="no scheme",--this.pointer}return!0},x.prototype["parse scheme"]=function(a,b){if(i(a)||h(a)||43===a||45===a||46===a)this.buffer+=b.toLowerCase();else if(58===a){if(this.stateOverride&&(n(this.url)&&!m(this.buffer)||!n(this.url)&&m(this.buffer)||(w(this.url)||null!==this.url.port)&&"file"===this.buffer||"file"===this.url.scheme&&(""===this.url.host||null===this.url.host))||(this.url.scheme=this.buffer,this.buffer="",this.stateOverride))return!1;"file"===this.url.scheme?((47!==this.input[this.pointer+1]||47!==this.input[this.pointer+2])&&(this.parseError=!0),this.state="file"):n(this.url)&&null!==this.base&&this.base.scheme===this.url.scheme?this.state="special relative or authority":n(this.url)?this.state="special authority slashes":47===this.input[this.pointer+1]?(this.state="path or authority",++this.pointer):(this.url.cannotBeABaseURL=!0,this.url.path.push(""),this.state="cannot-be-a-base-URL path")}else{if(this.stateOverride)return this.parseError=!0,y;this.buffer="",this.state="no scheme",this.pointer=-1}return!0},x.prototype["parse no scheme"]=function(a){return null===this.base||this.base.cannotBeABaseURL&&35!==a?y:(this.base.cannotBeABaseURL&&35===a?(this.url.scheme=this.base.scheme,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.url.cannotBeABaseURL=!0,this.state="fragment"):("file"===this.base.scheme?this.state="file":this.state="relative",--this.pointer),!0)},x.prototype["parse special relative or authority"]=function(a){return 47===a&&47===this.input[this.pointer+1]?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="relative",--this.pointer),!0},x.prototype["parse path or authority"]=function(a){return 47===a?this.state="authority":(this.state="path",--this.pointer),!0},x.prototype["parse relative"]=function(a){return this.url.scheme=this.base.scheme,isNaN(a)?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query):47===a?this.state="relative slash":63===a?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query="",this.state="query"):35===a?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment"):n(this.url)&&92===a?(this.parseError=!0,this.state="relative slash"):(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(0,this.base.path.length-1),this.state="path",--this.pointer),!0},x.prototype["parse relative slash"]=function(a){return n(this.url)&&(47===a||92===a)?(92===a&&(this.parseError=!0),this.state="special authority ignore slashes"):47===a?this.state="authority":(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.state="path",--this.pointer),!0},x.prototype["parse special authority slashes"]=function(a){return 47===a&&47===this.input[this.pointer+1]?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="special authority ignore slashes",--this.pointer),!0},x.prototype["parse special authority ignore slashes"]=function(a){return 47!==a&&92!==a?(this.state="authority",--this.pointer):this.parseError=!0,!0},x.prototype["parse authority"]=function(a,b){if(64===a){this.parseError=!0,this.atFlag&&(this.buffer="%40"+this.buffer),this.atFlag=!0;let a=f(this.buffer);for(let b=0;b<a;++b){let a=this.buffer.codePointAt(b);if(58===a&&!this.passwordTokenSeenFlag){this.passwordTokenSeenFlag=!0;continue}let c=s(a,r);this.passwordTokenSeenFlag?this.url.password+=c:this.url.username+=c}this.buffer=""}else if(isNaN(a)||47===a||63===a||35===a||n(this.url)&&92===a){if(this.atFlag&&""===this.buffer)return this.parseError=!0,y;this.pointer-=f(this.buffer)+1,this.buffer="",this.state="host"}else this.buffer+=b;return!0},x.prototype["parse hostname"]=x.prototype["parse host"]=function(a,b){if(this.stateOverride&&"file"===this.url.scheme)--this.pointer,this.state="file host";else if(58!==a||this.arrFlag)if(isNaN(a)||47===a||63===a||35===a||n(this.url)&&92===a){if(--this.pointer,n(this.url)&&""===this.buffer)return this.parseError=!0,y;if(this.stateOverride&&""===this.buffer&&(w(this.url)||null!==this.url.port))return this.parseError=!0,!1;let a=t(this.buffer,n(this.url));if(a===y)return y;if(this.url.host=a,this.buffer="",this.state="path start",this.stateOverride)return!1}else 91===a?this.arrFlag=!0:93===a&&(this.arrFlag=!1),this.buffer+=b;else{if(""===this.buffer)return this.parseError=!0,y;let a=t(this.buffer,n(this.url));if(a===y)return y;if(this.url.host=a,this.buffer="",this.state="port","hostname"===this.stateOverride)return!1}return!0},x.prototype["parse port"]=function(a,b){if(h(a))this.buffer+=b;else{if(!(isNaN(a)||47===a||63===a||35===a||n(this.url)&&92===a)&&!this.stateOverride)return this.parseError=!0,y;if(""!==this.buffer){let a=parseInt(this.buffer);if(a>65535)return this.parseError=!0,y;this.url.port=a===e[this.url.scheme]?null:a,this.buffer=""}if(this.stateOverride)return!1;this.state="path start",--this.pointer}return!0};let B=new Set([47,92,63,35]);x.prototype["parse file"]=function(a){if(this.url.scheme="file",47===a||92===a)92===a&&(this.parseError=!0),this.state="file slash";else if(null!==this.base&&"file"===this.base.scheme)if(isNaN(a))this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query;else if(63===a)this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query="",this.state="query";else if(35===a)this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment";else{var b;this.input.length-this.pointer-1!=0&&(b=this.input[this.pointer+1],i(a)&&(58===b||124===b))&&(!(this.input.length-this.pointer-1>=2)||B.has(this.input[this.pointer+2]))?this.parseError=!0:(this.url.host=this.base.host,this.url.path=this.base.path.slice(),v(this.url)),this.state="path",--this.pointer}else this.state="path",--this.pointer;return!0},x.prototype["parse file slash"]=function(a){if(47===a||92===a)92===a&&(this.parseError=!0),this.state="file host";else{if(null!==this.base&&"file"===this.base.scheme){var b;2===(b=this.base.path[0]).length&&i(b.codePointAt(0))&&":"===b[1]?this.url.path.push(this.base.path[0]):this.url.host=this.base.host}this.state="path",--this.pointer}return!0},x.prototype["parse file host"]=function(a,b){if(isNaN(a)||47===a||92===a||63===a||35===a)if(--this.pointer,!this.stateOverride&&l(this.buffer))this.parseError=!0,this.state="path";else if(""===this.buffer){if(this.url.host="",this.stateOverride)return!1;this.state="path start"}else{let a=t(this.buffer,n(this.url));if(a===y)return y;if("localhost"===a&&(a=""),this.url.host=a,this.stateOverride)return!1;this.buffer="",this.state="path start"}else this.buffer+=b;return!0},x.prototype["parse path start"]=function(a){return n(this.url)?(92===a&&(this.parseError=!0),this.state="path",47!==a&&92!==a&&--this.pointer):this.stateOverride||63!==a?this.stateOverride||35!==a?void 0!==a&&(this.state="path",47!==a&&--this.pointer):(this.url.fragment="",this.state="fragment"):(this.url.query="",this.state="query"),!0},x.prototype["parse path"]=function(a){if(isNaN(a)||47===a||n(this.url)&&92===a||!this.stateOverride&&(63===a||35===a)){var b;if((n(this.url)&&92===a&&(this.parseError=!0),".."===(b=(b=this.buffer).toLowerCase())||"%2e."===b||".%2e"===b||"%2e%2e"===b)?(v(this.url),47===a||n(this.url)&&92===a||this.url.path.push("")):k(this.buffer)&&47!==a&&!(n(this.url)&&92===a)?this.url.path.push(""):k(this.buffer)||("file"===this.url.scheme&&0===this.url.path.length&&l(this.buffer)&&(""!==this.url.host&&null!==this.url.host&&(this.parseError=!0,this.url.host=""),this.buffer=this.buffer[0]+":"),this.url.path.push(this.buffer)),this.buffer="","file"===this.url.scheme&&(void 0===a||63===a||35===a))for(;this.url.path.length>1&&""===this.url.path[0];)this.parseError=!0,this.url.path.shift();63===a&&(this.url.query="",this.state="query"),35===a&&(this.url.fragment="",this.state="fragment")}else 37!==a||j(this.input[this.pointer+1])&&j(this.input[this.pointer+2])||(this.parseError=!0),this.buffer+=s(a,q);return!0},x.prototype["parse cannot-be-a-base-URL path"]=function(a){return 63===a?(this.url.query="",this.state="query"):35===a?(this.url.fragment="",this.state="fragment"):(isNaN(a)||37===a||(this.parseError=!0),37!==a||j(this.input[this.pointer+1])&&j(this.input[this.pointer+2])||(this.parseError=!0),isNaN(a)||(this.url.path[0]=this.url.path[0]+s(a,p))),!0},x.prototype["parse query"]=function(a,b){if(isNaN(a)||!this.stateOverride&&35===a){n(this.url)&&"ws"!==this.url.scheme&&"wss"!==this.url.scheme||(this.encodingOverride="utf-8");let b=new Buffer(this.buffer);for(let a=0;a<b.length;++a)b[a]<33||b[a]>126||34===b[a]||35===b[a]||60===b[a]||62===b[a]?this.url.query+=o(b[a]):this.url.query+=String.fromCodePoint(b[a]);this.buffer="",35===a&&(this.url.fragment="",this.state="fragment")}else 37!==a||j(this.input[this.pointer+1])&&j(this.input[this.pointer+2])||(this.parseError=!0),this.buffer+=b;return!0},x.prototype["parse fragment"]=function(a){return isNaN(a)||(0===a?this.parseError=!0:(37!==a||j(this.input[this.pointer+1])&&j(this.input[this.pointer+2])||(this.parseError=!0),this.url.fragment+=s(a,p))),!0},d.exports.serializeURL=function(a,b){let c=a.scheme+":";if(null!==a.host?(c+="//",(""!==a.username||""!==a.password)&&(c+=a.username,""!==a.password&&(c+=":"+a.password),c+="@"),c+=u(a.host),null!==a.port&&(c+=":"+a.port)):null===a.host&&"file"===a.scheme&&(c+="//"),a.cannotBeABaseURL)c+=a.path[0];else for(let b of a.path)c+="/"+b;return null!==a.query&&(c+="?"+a.query),b||null===a.fragment||(c+="#"+a.fragment),c},d.exports.serializeURLOrigin=function(a){switch(a.scheme){case"blob":try{return d.exports.serializeURLOrigin(d.exports.parseURL(a.path[0]))}catch(a){return"null"}case"ftp":case"gopher":case"http":case"https":case"ws":case"wss":var b;let c;return c=(b={scheme:a.scheme,host:a.host,port:a.port}).scheme+"://"+u(b.host),null!==b.port&&(c+=":"+b.port),c;case"file":return"file://";default:return"null"}},d.exports.basicURLParse=function(a,b){void 0===b&&(b={});let c=new x(a,b.baseURL,b.encodingOverride,b.url,b.stateOverride);return c.failure?"failure":c.url},d.exports.setTheUsername=function(a,c){a.username="";let d=b.ucs2.decode(c);for(let b=0;b<d.length;++b)a.username+=s(d[b],r)},d.exports.setThePassword=function(a,c){a.password="";let d=b.ucs2.decode(c);for(let b=0;b<d.length;++b)a.password+=s(d[b],r)},d.exports.serializeHost=u,d.exports.cannotHaveAUsernamePasswordPort=function(a){return null===a.host||""===a.host||a.cannotBeABaseURL||"file"===a.scheme},d.exports.serializeInteger=function(a){return String(a)},d.exports.parseURL=function(a,b){return void 0===b&&(b={}),d.exports.basicURLParse(a,{baseURL:b.baseURL,encodingOverride:b.encodingOverride})}}},970070:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b=a.r(852477);e.implementation=class{constructor(a){let c=a[0],d=a[1],e=null;if(void 0!==d&&"failure"===(e=b.basicURLParse(d)))throw TypeError("Invalid base URL");let f=b.basicURLParse(c,{baseURL:e});if("failure"===f)throw TypeError("Invalid URL");this._url=f}get href(){return b.serializeURL(this._url)}set href(a){let c=b.basicURLParse(a);if("failure"===c)throw TypeError("Invalid URL");this._url=c}get origin(){return b.serializeURLOrigin(this._url)}get protocol(){return this._url.scheme+":"}set protocol(a){b.basicURLParse(a+":",{url:this._url,stateOverride:"scheme start"})}get username(){return this._url.username}set username(a){b.cannotHaveAUsernamePasswordPort(this._url)||b.setTheUsername(this._url,a)}get password(){return this._url.password}set password(a){b.cannotHaveAUsernamePasswordPort(this._url)||b.setThePassword(this._url,a)}get host(){let a=this._url;return null===a.host?"":null===a.port?b.serializeHost(a.host):b.serializeHost(a.host)+":"+b.serializeInteger(a.port)}set host(a){this._url.cannotBeABaseURL||b.basicURLParse(a,{url:this._url,stateOverride:"host"})}get hostname(){return null===this._url.host?"":b.serializeHost(this._url.host)}set hostname(a){this._url.cannotBeABaseURL||b.basicURLParse(a,{url:this._url,stateOverride:"hostname"})}get port(){return null===this._url.port?"":b.serializeInteger(this._url.port)}set port(a){b.cannotHaveAUsernamePasswordPort(this._url)||(""===a?this._url.port=null:b.basicURLParse(a,{url:this._url,stateOverride:"port"}))}get pathname(){return this._url.cannotBeABaseURL?this._url.path[0]:0===this._url.path.length?"":"/"+this._url.path.join("/")}set pathname(a){this._url.cannotBeABaseURL||(this._url.path=[],b.basicURLParse(a,{url:this._url,stateOverride:"path start"}))}get search(){return null===this._url.query||""===this._url.query?"":"?"+this._url.query}set search(a){let c=this._url;if(""===a){c.query=null;return}let d="?"===a[0]?a.substring(1):a;c.query="",b.basicURLParse(d,{url:c,stateOverride:"query"})}get hash(){return null===this._url.fragment||""===this._url.fragment?"":"#"+this._url.fragment}set hash(a){if(""===a){this._url.fragment=null;return}let c="#"===a[0]?a.substring(1):a;this._url.fragment="",b.basicURLParse(c,{url:this._url,stateOverride:"fragment"})}toJSON(){return this.href}}}},909882:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";let b=a.r(447268),c=a.r(113265),e=a.r(970070),g=c.implSymbol;function f(a){if(!this||this[g]||!(this instanceof f))throw TypeError("Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");if(arguments.length<1)throw TypeError("Failed to construct 'URL': 1 argument required, but only "+arguments.length+" present.");let c=[];for(let a=0;a<arguments.length&&a<2;++a)c[a]=arguments[a];c[0]=b.USVString(c[0]),void 0!==c[1]&&(c[1]=b.USVString(c[1])),d.exports.setup(this,c)}f.prototype.toJSON=function(){if(!this||!d.exports.is(this))throw TypeError("Illegal invocation");let a=[];for(let b=0;b<arguments.length&&b<0;++b)a[b]=arguments[b];return this[g].toJSON.apply(this[g],a)},Object.defineProperty(f.prototype,"href",{get(){return this[g].href},set(a){a=b.USVString(a),this[g].href=a},enumerable:!0,configurable:!0}),f.prototype.toString=function(){if(!this||!d.exports.is(this))throw TypeError("Illegal invocation");return this.href},Object.defineProperty(f.prototype,"origin",{get(){return this[g].origin},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"protocol",{get(){return this[g].protocol},set(a){a=b.USVString(a),this[g].protocol=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"username",{get(){return this[g].username},set(a){a=b.USVString(a),this[g].username=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"password",{get(){return this[g].password},set(a){a=b.USVString(a),this[g].password=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"host",{get(){return this[g].host},set(a){a=b.USVString(a),this[g].host=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"hostname",{get(){return this[g].hostname},set(a){a=b.USVString(a),this[g].hostname=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"port",{get(){return this[g].port},set(a){a=b.USVString(a),this[g].port=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"pathname",{get(){return this[g].pathname},set(a){a=b.USVString(a),this[g].pathname=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"search",{get(){return this[g].search},set(a){a=b.USVString(a),this[g].search=a},enumerable:!0,configurable:!0}),Object.defineProperty(f.prototype,"hash",{get(){return this[g].hash},set(a){a=b.USVString(a),this[g].hash=a},enumerable:!0,configurable:!0}),d.exports={is:a=>!!a&&a[g]instanceof e.implementation,create(a,b){let c=Object.create(f.prototype);return this.setup(c,a,b),c},setup(a,b,d){d||(d={}),d.wrapper=a,a[g]=new e.implementation(b,d),a[g][c.wrapperSymbol]=a},interface:f,expose:{Window:{URL:f},Worker:{URL:f}}}}},820593:function(a){var{g:b,__dirname:c,m:d,e:e}=a;"use strict";e.URL=a.r(909882).interface,e.serializeURL=a.r(852477).serializeURL,e.serializeURLOrigin=a.r(852477).serializeURLOrigin,e.basicURLParse=a.r(852477).basicURLParse,e.setTheUsername=a.r(852477).setTheUsername,e.setThePassword=a.r(852477).setThePassword,e.serializeHost=a.r(852477).serializeHost,e.serializeInteger=a.r(852477).serializeInteger,e.parseURL=a.r(852477).parseURL},540080:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";function f(a){return a&&"object"==typeof a&&"default"in a?a.default:a}Object.defineProperty(e,"__esModule",{value:!0});var g=f(a.r(109651)),h=f(a.r(62445)),i=f(a.r(771485)),j=f(a.r(820593)),k=f(a.r(348388)),l=f(a.r(794045));let c=g.Readable,E=Symbol("buffer"),F=Symbol("type");class G{constructor(){this[F]="";let a=arguments[0],b=arguments[1],c=[];if(a){let b=Number(a.length);for(let d=0;d<b;d++){let b,e=a[d];(b=e instanceof Buffer?e:ArrayBuffer.isView(e)?Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer?Buffer.from(e):e instanceof G?e[E]:Buffer.from("string"==typeof e?e:String(e))).length,c.push(b)}}this[E]=Buffer.concat(c);let d=b&&void 0!==b.type&&String(b.type).toLowerCase();d&&!/[^\u0020-\u007E]/.test(d)&&(this[F]=d)}get size(){return this[E].length}get type(){return this[F]}text(){return Promise.resolve(this[E].toString())}arrayBuffer(){let a=this[E];return Promise.resolve(a.buffer.slice(a.byteOffset,a.byteOffset+a.byteLength))}stream(){let a=new c;return a._read=function(){},a.push(this[E]),a.push(null),a}toString(){return"[object Blob]"}slice(){let a,b,c=this.size,d=arguments[0],e=arguments[1];a=void 0===d?0:d<0?Math.max(c+d,0):Math.min(d,c);let f=Math.max((void 0===e?c:e<0?Math.max(c+e,0):Math.min(e,c))-a,0),g=this[E].slice(a,a+f),h=new G([],{type:arguments[2]});return h[E]=g,h}}function m(a,b,c){Error.call(this,a),this.message=a,this.type=b,c&&(this.code=this.errno=c.code),Error.captureStackTrace(this,this.constructor)}Object.defineProperties(G.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(G.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),m.prototype=Object.create(Error.prototype),m.prototype.constructor=m,m.prototype.name="FetchError";let H=Symbol("Body internals"),I=g.PassThrough;function n(a){var b=this,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=c.size,e=c.timeout;null==a?a=null:p(a)?a=Buffer.from(a.toString()):q(a)||Buffer.isBuffer(a)||("[object ArrayBuffer]"===Object.prototype.toString.call(a)?a=Buffer.from(a):ArrayBuffer.isView(a)?a=Buffer.from(a.buffer,a.byteOffset,a.byteLength):a instanceof g||(a=Buffer.from(String(a)))),this[H]={body:a,disturbed:!1,error:null},this.size=void 0===d?0:d,this.timeout=void 0===e?0:e,a instanceof g&&a.on("error",function(a){let c="AbortError"===a.name?a:new m(`Invalid response body while trying to fetch ${b.url}: ${a.message}`,"system",a);b[H].error=c})}function o(){var a=this;if(this[H].disturbed)return n.Promise.reject(TypeError(`body used already for: ${this.url}`));if(this[H].disturbed=!0,this[H].error)return n.Promise.reject(this[H].error);let b=this.body;if(null===b)return n.Promise.resolve(Buffer.alloc(0));if(q(b)&&(b=b.stream()),Buffer.isBuffer(b))return n.Promise.resolve(b);if(!(b instanceof g))return n.Promise.resolve(Buffer.alloc(0));let c=[],d=0,e=!1;return new n.Promise(function(f,g){let h;a.timeout&&(h=setTimeout(function(){e=!0,g(new m(`Response timeout while trying to fetch ${a.url} (over ${a.timeout}ms)`,"body-timeout"))},a.timeout)),b.on("error",function(b){"AbortError"===b.name?(e=!0,g(b)):g(new m(`Invalid response body while trying to fetch ${a.url}: ${b.message}`,"system",b))}),b.on("data",function(b){if(!e&&null!==b){if(a.size&&d+b.length>a.size){e=!0,g(new m(`content size at ${a.url} over limit: ${a.size}`,"max-size"));return}d+=b.length,c.push(b)}}),b.on("end",function(){if(!e){clearTimeout(h);try{f(Buffer.concat(c,d))}catch(b){g(new m(`Could not create Buffer from response body for ${a.url}: ${b.message}`,"system",b))}}})})}function p(a){return"object"==typeof a&&"function"==typeof a.append&&"function"==typeof a.delete&&"function"==typeof a.get&&"function"==typeof a.getAll&&"function"==typeof a.has&&"function"==typeof a.set&&("URLSearchParams"===a.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(a)||"function"==typeof a.sort)}function q(a){return"object"==typeof a&&"function"==typeof a.arrayBuffer&&"string"==typeof a.type&&"function"==typeof a.stream&&"function"==typeof a.constructor&&"string"==typeof a.constructor.name&&/^(Blob|File)$/.test(a.constructor.name)&&/^(Blob|File)$/.test(a[Symbol.toStringTag])}function r(a){let b,c,d=a.body;if(a.bodyUsed)throw Error("cannot clone body after it is used");return d instanceof g&&"function"!=typeof d.getBoundary&&(b=new I,c=new I,d.pipe(b),d.pipe(c),a[H].body=b,d=c),d}function s(a){if(null===a)return null;if("string"==typeof a)return"text/plain;charset=UTF-8";if(p(a))return"application/x-www-form-urlencoded;charset=UTF-8";if(q(a))return a.type||null;if(Buffer.isBuffer(a))return null;else if("[object ArrayBuffer]"===Object.prototype.toString.call(a))return null;else if(ArrayBuffer.isView(a))return null;else if("function"==typeof a.getBoundary)return`multipart/form-data;boundary=${a.getBoundary()}`;else if(a instanceof g)return null;else return"text/plain;charset=UTF-8"}function t(a){let b=a.body;return null===b?0:q(b)?b.size:Buffer.isBuffer(b)?b.length:b&&"function"==typeof b.getLengthSync?b._lengthRetrievers&&0==b._lengthRetrievers.length||b.hasKnownLength&&b.hasKnownLength()?b.getLengthSync():null:null}n.prototype={get body(){return this[H].body},get bodyUsed(){return this[H].disturbed},arrayBuffer(){return o.call(this).then(function(a){return a.buffer.slice(a.byteOffset,a.byteOffset+a.byteLength)})},blob(){let a=this.headers&&this.headers.get("content-type")||"";return o.call(this).then(function(b){return Object.assign(new G([],{type:a.toLowerCase()}),{[E]:b})})},json(){var a=this;return o.call(this).then(function(b){try{return JSON.parse(b.toString())}catch(b){return n.Promise.reject(new m(`invalid json response body at ${a.url} reason: ${b.message}`,"invalid-json"))}})},text(){return o.call(this).then(function(a){return a.toString()})},buffer(){return o.call(this)},textConverted(){var a=this;return o.call(this).then(function(b){var c=0,d=a.headers;throw Error("The package `encoding` must be installed to use the textConverted() function")})}},Object.defineProperties(n.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),n.mixIn=function(a){for(let b of Object.getOwnPropertyNames(n.prototype))if(!(b in a)){let c=Object.getOwnPropertyDescriptor(n.prototype,b);Object.defineProperty(a,b,c)}},n.Promise=b.Promise;let J=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,K=/[^\t\x20-\x7e\x80-\xff]/;function u(a){if(a=`${a}`,J.test(a)||""===a)throw TypeError(`${a} is not a legal HTTP header name`)}function v(a){if(a=`${a}`,K.test(a))throw TypeError(`${a} is not a legal HTTP header value`)}function w(a,b){for(let c in b=b.toLowerCase(),a)if(c.toLowerCase()===b)return c}let L=Symbol("map");class M{constructor(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[L]=Object.create(null),a instanceof M){let b=a.raw();for(let a of Object.keys(b))for(let c of b[a])this.append(a,c);return}if(null==a);else if("object"==typeof a){let b=a[Symbol.iterator];if(null!=b){if("function"!=typeof b)throw TypeError("Header pairs must be iterable");let c=[];for(let b of a){if("object"!=typeof b||"function"!=typeof b[Symbol.iterator])throw TypeError("Each header pair must be iterable");c.push(Array.from(b))}for(let a of c){if(2!==a.length)throw TypeError("Each header pair must be a name/value tuple");this.append(a[0],a[1])}}else for(let b of Object.keys(a)){let c=a[b];this.append(b,c)}}else throw TypeError("Provided initializer must be an object")}get(a){u(a=`${a}`);let b=w(this[L],a);return void 0===b?null:this[L][b].join(", ")}forEach(a){let b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,c=x(this),d=0;for(;d<c.length;){var e=c[d];let f=e[0],g=e[1];a.call(b,g,f,this),c=x(this),d++}}set(a,b){a=`${a}`,b=`${b}`,u(a),v(b);let c=w(this[L],a);this[L][void 0!==c?c:a]=[b]}append(a,b){a=`${a}`,b=`${b}`,u(a),v(b);let c=w(this[L],a);void 0!==c?this[L][c].push(b):this[L][a]=[b]}has(a){return u(a=`${a}`),void 0!==w(this[L],a)}delete(a){u(a=`${a}`);let b=w(this[L],a);void 0!==b&&delete this[L][b]}raw(){return this[L]}keys(){return y(this,"key")}values(){return y(this,"value")}[Symbol.iterator](){return y(this,"key+value")}}function x(a){let b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(a[L]).sort().map("key"===b?function(a){return a.toLowerCase()}:"value"===b?function(b){return a[L][b].join(", ")}:function(b){return[b.toLowerCase(),a[L][b].join(", ")]})}M.prototype.entries=M.prototype[Symbol.iterator],Object.defineProperty(M.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(M.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});let N=Symbol("internal");function y(a,b){let c=Object.create(O);return c[N]={target:a,kind:b,index:0},c}let O=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==O)throw TypeError("Value of `this` is not a HeadersIterator");var a=this[N];let b=a.target,c=a.kind,d=a.index,e=x(b,c);return d>=e.length?{value:void 0,done:!0}:(this[N].index=d+1,{value:e[d],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(O,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});let P=Symbol("Response internals"),Q=h.STATUS_CODES;class R{constructor(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n.call(this,a,b);let c=b.status||200,d=new M(b.headers);if(null!=a&&!d.has("Content-Type")){let b=s(a);b&&d.append("Content-Type",b)}this[P]={url:b.url,status:c,statusText:b.statusText||Q[c],headers:d,counter:b.counter}}get url(){return this[P].url||""}get status(){return this[P].status}get ok(){return this[P].status>=200&&this[P].status<300}get redirected(){return this[P].counter>0}get statusText(){return this[P].statusText}get headers(){return this[P].headers}clone(){return new R(r(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}n.mixIn(R.prototype),Object.defineProperties(R.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(R.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});let S=Symbol("Request internals"),T=i.URL||j.URL,U=i.parse,V=i.format;function z(a){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(a)&&(a=new T(a).toString()),U(a)}let W="destroy"in g.Readable.prototype;function A(a){return"object"==typeof a&&"object"==typeof a[S]}class X{constructor(a){let b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};A(a)?b=z(a.url):(b=a&&a.href?z(a.href):z(`${a}`),a={});let d=c.method||a.method||"GET";if(d=d.toUpperCase(),(null!=c.body||A(a)&&null!==a.body)&&("GET"===d||"HEAD"===d))throw TypeError("Request with GET/HEAD method cannot have body");let e=null!=c.body?c.body:A(a)&&null!==a.body?r(a):null;n.call(this,e,{timeout:c.timeout||a.timeout||0,size:c.size||a.size||0});let f=new M(c.headers||a.headers||{});if(null!=e&&!f.has("Content-Type")){let a=s(e);a&&f.append("Content-Type",a)}let g=A(a)?a.signal:null;if("signal"in c&&(g=c.signal),null!=g&&!function(a){let b=a&&"object"==typeof a&&Object.getPrototypeOf(a);return!!(b&&"AbortSignal"===b.constructor.name)}(g))throw TypeError("Expected signal to be an instanceof AbortSignal");this[S]={method:d,redirect:c.redirect||a.redirect||"follow",headers:f,parsedURL:b,signal:g},this.follow=void 0!==c.follow?c.follow:void 0!==a.follow?a.follow:20,this.compress=void 0!==c.compress?c.compress:void 0===a.compress||a.compress,this.counter=c.counter||a.counter||0,this.agent=c.agent||a.agent}get method(){return this[S].method}get url(){return V(this[S].parsedURL)}get headers(){return this[S].headers}get redirect(){return this[S].redirect}get signal(){return this[S].signal}clone(){return new X(this)}}function B(a){Error.call(this,a),this.type="aborted",this.message=a,Error.captureStackTrace(this,this.constructor)}n.mixIn(X.prototype),Object.defineProperty(X.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(X.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),B.prototype=Object.create(Error.prototype),B.prototype.constructor=B,B.prototype.name="AbortError";let Y=i.URL||j.URL,Z=g.PassThrough,$=function(a,b){let c=new Y(b).hostname,d=new Y(a).hostname;return c===d||"."===c[c.length-d.length-1]&&c.endsWith(d)};function C(a,b){if(!C.Promise)throw Error("native promise missing, set fetch.Promise to your favorite alternative");return n.Promise=C.Promise,new C.Promise(function(c,d){var e,f;let i,j,n=new X(a,b),o=function(a){let b=a[S].parsedURL,c=new M(a[S].headers);if(c.has("Accept")||c.set("Accept","*/*"),!b.protocol||!b.hostname)throw TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(b.protocol))throw TypeError("Only HTTP(S) protocols are supported");if(a.signal&&a.body instanceof g.Readable&&!W)throw Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let d=null;if(null==a.body&&/^(POST|PUT)$/i.test(a.method)&&(d="0"),null!=a.body){let b=t(a);"number"==typeof b&&(d=String(b))}d&&c.set("Content-Length",d),c.has("User-Agent")||c.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),a.compress&&!c.has("Accept-Encoding")&&c.set("Accept-Encoding","gzip,deflate");let e=a.agent;return"function"==typeof e&&(e=e(b)),c.has("Connection")||e||c.set("Connection","close"),Object.assign({},b,{method:a.method,headers:function(a){let b=Object.assign({__proto__:null},a[L]),c=w(a[L],"Host");return void 0!==c&&(b[c]=b[c][0]),b}(c),agent:e})}(n),p=("https:"===o.protocol?k:h).request,r=n.signal,s=null,u=function(){let a=new B("The user aborted a request.");d(a),n.body&&n.body instanceof g.Readable&&D(n.body,a),s&&s.body&&s.body.emit("error",a)};if(r&&r.aborted)return void u();let v=function(){u(),y()},x=p(o);function y(){x.abort(),r&&r.removeEventListener("abort",v),clearTimeout(i)}r&&r.addEventListener("abort",v),n.timeout&&x.once("socket",function(a){i=setTimeout(function(){d(new m(`network timeout at: ${n.url}`,"request-timeout")),y()},n.timeout)}),x.on("error",function(a){d(new m(`request to ${n.url} failed, reason: ${a.message}`,"system",a)),s&&s.body&&D(s.body,a),y()}),e=x,f=function(a){(!r||!r.aborted)&&s&&s.body&&D(s.body,a)},e.on("socket",function(a){j=a}),e.on("response",function(a){let b=a.headers;"chunked"!==b["transfer-encoding"]||b["content-length"]||a.once("close",function(a){if(j&&j.listenerCount("data")>0&&!a){let a=Error("Premature close");a.code="ERR_STREAM_PREMATURE_CLOSE",f(a)}})}),14>parseInt(process.version.substring(1))&&x.on("socket",function(a){a.addListener("close",function(b){let c=a.listenerCount("data")>0;if(s&&c&&!b&&!(r&&r.aborted)){let a=Error("Premature close");a.code="ERR_STREAM_PREMATURE_CLOSE",s.body.emit("error",a)}})}),x.on("response",function(a){clearTimeout(i);let b=function(a){let b=new M;for(let c of Object.keys(a))if(!J.test(c))if(Array.isArray(a[c]))for(let d of a[c])K.test(d)||(void 0===b[L][c]?b[L][c]=[d]:b[L][c].push(d));else K.test(a[c])||(b[L][c]=[a[c]]);return b}(a.headers);if(C.isRedirect(a.statusCode)){let f=b.get("Location"),g=null;try{g=null===f?null:new Y(f,n.url).toString()}catch(a){if("manual"!==n.redirect){d(new m(`uri requested responds with an invalid redirect URL: ${f}`,"invalid-redirect")),y();return}}switch(n.redirect){case"error":d(new m(`uri requested responds with a redirect, redirect mode is set to error: ${n.url}`,"no-redirect")),y();return;case"manual":if(null!==g)try{b.set("Location",g)}catch(a){d(a)}break;case"follow":var e;if(null===g)break;if(n.counter>=n.follow){d(new m(`maximum redirect reached at: ${n.url}`,"max-redirect")),y();return}let h={headers:new M(n.headers),follow:n.follow,counter:n.counter+1,agent:n.agent,compress:n.compress,method:n.method,body:n.body,signal:n.signal,timeout:n.timeout,size:n.size};if(!$(n.url,g)||(e=n.url,new Y(g).protocol!==new Y(e).protocol))for(let a of["authorization","www-authenticate","cookie","cookie2"])h.headers.delete(a);if(303!==a.statusCode&&n.body&&null===t(n)){d(new m("Cannot follow redirect with body being a readable stream","unsupported-redirect")),y();return}(303===a.statusCode||(301===a.statusCode||302===a.statusCode)&&"POST"===n.method)&&(h.method="GET",h.body=void 0,h.headers.delete("content-length")),c(C(new X(g,h))),y();return}}a.once("end",function(){r&&r.removeEventListener("abort",v)});let f=a.pipe(new Z),g={url:n.url,status:a.statusCode,statusText:a.statusMessage,headers:b,size:n.size,timeout:n.timeout,counter:n.counter},h=b.get("Content-Encoding");if(!n.compress||"HEAD"===n.method||null===h||204===a.statusCode||304===a.statusCode)return void c(s=new R(f,g));let j={flush:l.Z_SYNC_FLUSH,finishFlush:l.Z_SYNC_FLUSH};if("gzip"==h||"x-gzip"==h)return void c(s=new R(f=f.pipe(l.createGunzip(j)),g));if("deflate"==h||"x-deflate"==h){let b=a.pipe(new Z);b.once("data",function(a){c(s=new R(f=(15&a[0])==8?f.pipe(l.createInflate()):f.pipe(l.createInflateRaw()),g))}),b.on("end",function(){s||c(s=new R(f,g))});return}if("br"==h&&"function"==typeof l.createBrotliDecompress)return void c(s=new R(f=f.pipe(l.createBrotliDecompress()),g));c(s=new R(f,g))});let z=n.body;null===z?x.end():q(z)?z.stream().pipe(x):Buffer.isBuffer(z)?(x.write(z),x.end()):z.pipe(x)})}function D(a,b){a.destroy?a.destroy(b):(a.emit("error",b),a.end())}C.isRedirect=function(a){return 301===a||302===a||303===a||307===a||308===a},C.Promise=b.Promise,d.exports=e=C,Object.defineProperty(e,"__esModule",{value:!0}),e.default=e,e.Headers=M,e.Request=X,e.Response=R,e.FetchError=m}}};

//# sourceMappingURL=node_modules_da8b9563._.js.map