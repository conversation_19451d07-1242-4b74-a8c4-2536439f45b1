module.exports={283620:function(a){var{g:b,__dirname:c,m:d,e:e}=a;{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var f={callServer:function(){return b.callServer},createServerReference:function(){return d},findSourceMapURL:function(){return c.findSourceMapURL}};for(var g in f)Object.defineProperty(e,g,{enumerable:!0,get:f[g]});let b=a.r(740515),c=a.r(249754),d=a.r(97477).createServerReference}},308949:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({login:()=>e});var d=a.i(283620),e=(0,d.createServerReference)("6064c1bc78024112540d1fec516d46f480c1310eb6",d.callServer,void 0,d.findSourceMapURL,"login")},603343:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({LoginForm:()=>h});var d=a.i(674420),e=a.i(722851),f=a.i(308949),g=a.i(844183);let b={success:!1,message:""};function h(){let[a,c,h]=(0,e.useActionState)(f.login,b);return(0,e.useEffect)(()=>{a?.success&&a?.message?.includes("Redirecting")&&(0,g.trackAuth)("login_successful")},[a?.success,a?.message]),(0,d.jsxs)("form",{action:a=>{(0,g.trackAuth)("login_attempted"),c(a)},className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-slate-700 mb-2",children:"Email Address"}),(0,d.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"<EMAIL>"}),a?.fieldErrors?.email&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:a.fieldErrors.email[0]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-700 mb-2",children:"Password"}),(0,d.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900",placeholder:"Enter your password"}),a?.fieldErrors?.password&&(0,d.jsxs)("p",{className:"mt-2 text-sm text-red-600 flex items-center space-x-1",children:[(0,d.jsx)("span",{className:"w-1 h-1 bg-red-600 rounded-full"}),(0,d.jsx)("span",{children:a.fieldErrors.password[0]})]})]})]}),a?.message&&(0,d.jsx)("div",{className:"rounded-xl p-4 border bg-gradient-to-r from-red-50 to-pink-50 border-red-200",children:(0,d.jsxs)("p",{className:"text-sm font-medium flex items-center space-x-2 text-red-800",children:[(0,d.jsx)("span",{className:"w-2 h-2 rounded-full bg-red-400"}),(0,d.jsx)("span",{children:a.message})]})}),(0,d.jsx)("div",{children:(0,d.jsx)("button",{type:"submit",disabled:h,className:"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2",children:h?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,d.jsx)("span",{children:"Signing you in..."})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{children:"Continue the Magic"}),(0,d.jsx)("svg",{className:"w-4 h-4 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})})})]})}}}};

//# sourceMappingURL=_b7b88d0c._.js.map