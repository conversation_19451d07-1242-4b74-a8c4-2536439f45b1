module.exports={771485:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("url",()=>require("url"))},62445:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("http",()=>require("http"))},348388:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("https",()=>require("https"))},109651:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("stream",()=>require("stream"))},794045:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("zlib",()=>require("zlib"))},329295:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.x("crypto",()=>require("crypto"))},269596:a=>{var{g:b,__dirname:c}=a;a.n(a.i(520884))},942505:a=>{var{g:b,__dirname:c}=a;a.n(a.i(106157))},745431:a=>{var{g:b,__dirname:c}=a;a.n(a.i(541884))},801327:a=>{var{g:b,__dirname:c}=a;a.n(a.i(906079))},97892:a=>{var{g:b,__dirname:c}=a;a.n(a.i(919184))},660874:function(a){var{g:b,__dirname:c,m:d,e:e}=a;d.exports=a.r(929549)},715847:a=>{var{g:b,__dirname:c}=a;a.n(a.i(103439))},781045:a=>{var{g:b,__dirname:c}=a;a.n(a.i(909856))},913994:a=>{var{g:b,__dirname:c}=a;a.n(a.i(330020))},520304:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({hasA11yProp:()=>f,mergeClasses:()=>e,toCamelCase:()=>c,toKebabCase:()=>b,toPascalCase:()=>d});let b=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase()),d=a=>{let b=c(a);return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim(),f=a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0}}},91640:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>d});var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}},252747:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(91640),f=a.i(520304);let b=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...e.default,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:(0,f.mergeClasses)("lucide",h),...!i&&!(0,f.hasA11yProp)(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]]))}},274453:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({default:()=>b});var d=a.i(465421),e=a.i(520304),f=a.i(252747);let b=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},h)=>(0,d.createElement)(f.default,{ref:h,iconNode:b,className:(0,e.mergeClasses)(`lucide-${(0,e.toKebabCase)((0,e.toPascalCase)(a))}`,`lucide-${a}`,c),...g}));return c.displayName=(0,e.toPascalCase)(a),c}}},834277:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],c=(0,d.default)("zap",b)}},533023:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Zap:()=>d.default});var d=a.i(834277)},680493:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({createContactRequest:()=>g,getContactRequests:()=>h,getContactRequestsCount:()=>k,getPendingContactRequests:()=>i,hasActiveContactRequest:()=>l,updateContactRequestStatus:()=>j});var d=a.i(930768);a.i(996945);var e=a.i(729149),f=a.i(719358);async function g(a,b,c){try{console.log("Creating contact request for doctorId:",a);let d=await (0,e.createClient)();console.log("Fetching doctor info...");let{data:g,error:h}=await d.from("doctors").select("name, email, clinic_name, phone, quota_used, monthly_quota").eq("id",a).single();if(console.log("Doctor fetch result:",{doctor:g,doctorError:h}),h||!g)return console.error("Doctor not found:",{doctorId:a,doctorError:h}),{success:!1,error:`Doctor not found: ${h?.message||"No doctor data"}`};let i={doctor_id:a,doctor_name:g.name,doctor_email:g.email,clinic_name:g.clinic_name||"",phone_number:g.phone||"",current_quota_used:g.quota_used||0,monthly_quota:g.monthly_quota||0,request_type:"general_contact",message:b||"Contact request from dashboard",subject:c||"general"};console.log("Creating contact request with data:",i);let{data:j,error:k}=await d.from("contact_requests").insert(i).select("id").single();if(console.log("Insert result:",{data:j,error:k}),k)return console.error("Failed to create contact request:",k),{success:!1,error:`Database error: ${k.message}`};return(0,f.revalidatePath)("/admin/dashboard"),(0,f.revalidatePath)("/admin"),console.log("Contact request created successfully with ID:",j.id),{success:!0,data:j.id}}catch(a){return console.error("Unexpected error creating contact request:",a),{success:!1,error:`Unexpected error: ${a instanceof Error?a.message:"Unknown error"}`}}}async function h(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("*").order("created_at",{ascending:!1});if(c)return console.error("Database error fetching contact requests:",c),{success:!1,error:"Failed to fetch contact requests"};return{success:!0,data:b||[]}}catch(a){return console.error("Error fetching contact requests:",a),{success:!1,error:"An unexpected error occurred"}}}async function i(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("*").eq("status","pending").order("created_at",{ascending:!1});if(c)return{success:!1,error:"Failed to fetch pending contact requests"};return{success:!0,data:b||[]}}catch(a){return console.error("Error fetching pending contact requests:",a),{success:!1,error:"An unexpected error occurred"}}}async function j(a,b){try{let c=await (0,e.createClient)(),{error:d}=await c.from("contact_requests").update({status:b}).eq("id",a);if(d)return{success:!1,error:"Failed to update contact request status"};return(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Error updating contact request status:",a),{success:!1,error:"An unexpected error occurred"}}}async function k(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("contact_requests").select("status");if(c)return{success:!1,error:"Failed to fetch contact requests count"};let d={total:b?.length||0,pending:b?.filter(a=>"pending"===a.status).length||0,contacted:b?.filter(a=>"contacted"===a.status).length||0,resolved:b?.filter(a=>"resolved"===a.status).length||0};return{success:!0,data:d}}catch(a){return console.error("Error fetching contact requests count:",a),{success:!1,error:"An unexpected error occurred"}}}async function l(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.from("contact_requests").select("id").eq("doctor_id",a).eq("status","pending").single();if(d&&"PGRST116"!==d.code)return{success:!1,error:"Failed to check contact request status"};return{success:!0,data:!!c}}catch(a){return console.error("Error checking contact request status:",a),{success:!1,error:"An unexpected error occurred"}}}(0,a.i(377991).ensureServerEntryExports)([g,h,i,j,k,l]),(0,d.registerServerReference)(g,"70fff2cc329b28db6323e452c9272d2de14164c462",null),(0,d.registerServerReference)(h,"0009c0c46815af213900a610cc4a7e2d00a036b8b7",null),(0,d.registerServerReference)(i,"00e1bc255712e823e34fa44afca19fb096ce32734c",null),(0,d.registerServerReference)(j,"60bd2570fa48d9714b2922cc0537d755b4c2180d2e",null),(0,d.registerServerReference)(k,"00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9",null),(0,d.registerServerReference)(l,"405d7138432cf8f813249d6a1a52fcfca61c62a7ed",null)},540809:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],c=(0,d.default)("users",b)}},755819:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({Users:()=>d.default});var d=a.i(540809)},403155:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({createAdminSession:()=>i,decryptAdminSession:()=>h,deleteAdminSession:()=>k,deleteSession:()=>k,encryptAdminSession:()=>g,updateAdminSession:()=>j}),a.i(750661);var d=a.i(749724),e=a.i(290753),f=a.i(409950);let b=process.env.SESSION_SECRET,c=new TextEncoder().encode(b);async function g(a){return new d.SignJWT(a).setProtectedHeader({alg:"HS256"}).setIssuedAt().setExpirationTime("7d").sign(c)}async function h(a=""){try{if(!a)return null;let{payload:b}=await (0,e.jwtVerify)(a,c,{algorithms:["HS256"]});return b}catch{return console.log("Failed to verify admin session"),null}}async function i(a,b="admin"){let c=new Date(Date.now()+6048e5),d=await g({adminId:a,role:b,expiresAt:c}),e=await (0,f.cookies)();console.log("DEBUG: Creating admin session for admin:",a,"role:",b),console.log("DEBUG: Admin session expires at:",c),e.set("admin_session",d,{httpOnly:!0,secure:!1,expires:c,sameSite:"lax",path:"/"}),console.log("DEBUG: Admin session cookie set successfully")}async function j(){let a=await (0,f.cookies)(),b=a.get("admin_session")?.value,c=await h(b);if(!b||!c)return null;let d=new Date(Date.now()+6048e5);a.set("admin_session",b,{httpOnly:!0,secure:!0,expires:d,sameSite:"lax",path:"/"})}async function k(){let a=await (0,f.cookies)();console.log("DEBUG: Deleting admin session cookie"),a.delete("admin_session"),console.log("DEBUG: Admin session cookie deleted")}}},534304:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({adminLogin:()=>i,adminLogout:()=>j,createAdminUser:()=>k});var d=a.i(930768);a.i(996945);var e=a.i(437713),f=a.i(729149),g=a.i(403155),h=a.i(635512);async function i(a,b){let c=h.AdminLoginFormSchema.safeParse({email:b.get("email"),password:b.get("password")});if(!c.success)return{success:!1,message:"Invalid form fields."};let{email:d,password:i}=c.data;try{let a=await (0,f.createClient)(),{data:b}=await a.from("admins").select("id, password_hash, role").eq("email",d).single();if(!b||!await e.default.compare(i,b.password_hash))return{success:!1,message:"Invalid email or password."};return await (0,g.createAdminSession)(b.id,b.role),{success:!0,message:"Login successful! Redirecting..."}}catch(a){return console.error("Admin Login Error:",a),{success:!1,message:"An unexpected error occurred."}}}async function j(){return await (0,g.deleteSession)(),{success:!0}}async function k(a,b,c,d="admin"){try{let g=await (0,f.createClient)(),{data:h}=await g.from("admins").select("id").eq("email",a).single();if(h)return{success:!1,error:"An admin with this email already exists."};let i=await e.default.hash(b,10),{data:j,error:k}=await g.from("admins").insert({name:c,email:a,password_hash:i,role:d}).select("id").single();if(k)return console.error("Database error:",k),{success:!1,error:"An error occurred while creating the admin account."};if(!j)return{success:!1,error:"An error occurred while creating the admin account."};return{success:!0,adminId:j.id}}catch(a){return console.error("Create admin error:",a),{success:!1,error:"An unexpected error occurred."}}}(0,a.i(377991).ensureServerEntryExports)([i,j,k]),(0,d.registerServerReference)(i,"60e96432ee93dbc1f2cacf69805b78047ca4cf9f56",null),(0,d.registerServerReference)(j,"0052d311627b1e498ec699db5ec34cf21bfb47393c",null),(0,d.registerServerReference)(k,"787a2e1247c0a22d20605e42975e36fd0fe310203e",null)},480888:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardSkeleton:()=>b,ConsultationsListSkeleton:()=>c,DashboardSkeleton:()=>e,DashboardStatsSkeleton:()=>f,InfoPageSkeleton:()=>g,QuotaCardSkeleton:()=>h,ReferralStatsSkeleton:()=>i});var d=a.i(77624);let b=(0,d.registerClientReference)(function(){throw Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","AdminDashboardSkeleton"),c=(0,d.registerClientReference)(function(){throw Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","ConsultationsListSkeleton"),e=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","DashboardSkeleton"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","DashboardStatsSkeleton"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","InfoPageSkeleton"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","QuotaCardSkeleton"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>","ReferralStatsSkeleton")}},473352:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardSkeleton:()=>b,ConsultationsListSkeleton:()=>c,DashboardSkeleton:()=>e,DashboardStatsSkeleton:()=>f,InfoPageSkeleton:()=>g,QuotaCardSkeleton:()=>h,ReferralStatsSkeleton:()=>i});var d=a.i(77624);let b=(0,d.registerClientReference)(function(){throw Error("Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","AdminDashboardSkeleton"),c=(0,d.registerClientReference)(function(){throw Error("Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","ConsultationsListSkeleton"),e=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","DashboardSkeleton"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","DashboardStatsSkeleton"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","InfoPageSkeleton"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","QuotaCardSkeleton"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/skeleton-loaders.tsx","ReferralStatsSkeleton")}},968123:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(480888);var d=a.i(473352);a.n(d)},352125:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],c=(0,d.default)("file-text",b)}},122146:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({FileText:()=>d.default});var d=a.i(352125)},624142:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({checkAdminAccess:()=>k,checkAdminSession:()=>c,getAdmin:()=>i,getAdminById:()=>j,getAdminForPage:()=>l,verifyAdminSession:()=>b}),a.i(750661);var d=a.i(465421),e=a.i(409950);a.i(622427);var f=a.i(766719),g=a.i(403155),h=a.i(729149);let b=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("admin_session")?.value,c=await (0,g.decryptAdminSession)(b);return c?.adminId||(0,f.redirect)("/admin/login"),{isAuth:!0,adminId:c.adminId,role:c.role}}),c=(0,d.cache)(async()=>{let a=await (0,e.cookies)(),b=a.get("admin_session")?.value,c=await (0,g.decryptAdminSession)(b);return c?.adminId?{isAuth:!0,adminId:c.adminId,role:c.role}:null}),i=(0,d.cache)(async()=>{let a=await b();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("admins").select("id, email, name, role, created_at, updated_at").eq("id",a.adminId).single();if(d)return console.error("Failed to fetch admin:",d),null;return c}catch(a){return console.error("Failed to fetch admin:",a),null}}),j=(0,d.cache)(async a=>{try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("admins").select("id, email, name, role, created_at, updated_at").eq("id",a).single();if(d)return console.error("Failed to fetch admin by ID:",d),null;return c}catch(a){return console.error("Failed to fetch admin by ID:",a),null}}),k=(0,d.cache)(async()=>{try{let a=await (0,e.cookies)(),b=a.get("admin_session")?.value,c=await (0,g.decryptAdminSession)(b);return!!c?.adminId}catch{return!1}}),l=(0,d.cache)(async()=>{let a=await c();if(!a)return null;try{let b=await (0,h.createClient)(),{data:c,error:d}=await b.from("admins").select("id, email, name, role, created_at, updated_at").eq("id",a.adminId).single();if(d)return console.error("Failed to fetch admin:",d),null;return c}catch(a){return console.error("Failed to fetch admin:",a),null}})}},183850:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({getAdminDashboardStats:()=>b,getAllDoctorsWithStats:()=>c,performAdminAction:()=>j,resetDoctorQuota:()=>k});var d=a.i(930768);a.i(996945);var e=a.i(719358),f=a.i(465421),g=a.i(729149),h=a.i(624142),i=a.i(377991);let b=(0,f.cache)(async()=>{try{if(!await (0,h.verifyAdminSession)())return{success:!1,error:"Unauthorized"};let a=await (0,g.createClient)(),{data:b,error:c}=await a.from("admin_dashboard_summary").select("*").single();if(c)return console.error("Database error:",c),{success:!1,error:"Failed to fetch dashboard stats"};let d={total_doctors:b.total_doctors||0,pending_approvals:b.pending_approvals||0,approved_doctors:b.approved_doctors||0,total_consultations:b.total_consultations||0,total_ai_generations:b.total_ai_generations||0,quota_usage_percentage:b.quota_usage_percentage||0};return{success:!0,data:d}}catch(a){return console.error("Get admin dashboard stats error:",a),{success:!1,error:"Failed to fetch dashboard stats"}}}),c=(0,f.cache)(async()=>{try{if(!await (0,h.verifyAdminSession)())return{success:!1,error:"Not authenticated. Please log in again."};let a=await (0,g.createClient)(),{data:b,error:c}=await a.from("admin_doctors_with_stats").select("*").order("created_at",{ascending:!1});if(c)return console.error("Database error:",c),{success:!1,error:"Failed to fetch doctors"};let d=b.map(a=>({...a,phone:a.phone??void 0,clinic_name:a.clinic_name??void 0,approved_by:a.approved_by??void 0,approved_at:a.approved_at??void 0,referral_code:a.referral_code??void 0,referred_by:a.referred_by??void 0,billing_status:a.billing_status??void 0,trial_ends_at:a.trial_ends_at??void 0,last_activity:a.last_activity??void 0}));return{success:!0,data:d}}catch(a){return console.error("Get doctors with stats error:",a),{success:!1,error:"Failed to fetch doctors with stats"}}});async function j(a){try{let b=await (0,h.verifyAdminSession)();if(!b)return{success:!1,error:"Unauthorized"};let c=await (0,g.createClient)();switch(a.action){case"approve":let{error:d}=await c.from("doctors").update({approved:!0,approved_by:b.adminId,approved_at:new Date().toISOString()}).eq("id",a.doctor_id);if(d)return{success:!1,error:"Failed to approve doctor"};break;case"reject":let{error:f}=await c.from("doctors").delete().eq("id",a.doctor_id);if(f)return{success:!1,error:"Failed to reject doctor"};break;case"update_quota":if(!a.data?.quota)return{success:!1,error:"Quota value is required"};let{error:i}=await c.from("doctors").update({monthly_quota:a.data.quota}).eq("id",a.doctor_id);if(i)return{success:!1,error:"Failed to update quota"};await c.from("usage_logs").insert({doctor_id:a.doctor_id,action_type:"quota_update",quota_after:a.data.quota,metadata:{admin_id:b.adminId,reason:a.data.reason||"Admin update"}});break;case"disable":let{error:j}=await c.from("doctors").update({approved:!1}).eq("id",a.doctor_id);if(j)return{success:!1,error:"Failed to disable doctor"};break;case"enable":let{error:k}=await c.from("doctors").update({approved:!0,approved_by:b.adminId,approved_at:new Date().toISOString()}).eq("id",a.doctor_id);if(k)return{success:!1,error:"Failed to enable doctor"};break;default:return{success:!1,error:"Invalid action"}}return(0,e.revalidatePath)("/admin/dashboard"),(0,e.revalidatePath)("/admin/doctors"),{success:!0,data:!0}}catch(a){return console.error("Perform admin action error:",a),{success:!1,error:"Failed to perform action"}}}async function k(a){try{let b=await (0,h.verifyAdminSession)();if(!b)return{success:!1,error:"Unauthorized"};let c=await (0,g.createClient)(),{data:d}=await c.from("doctors").select("quota_used").eq("id",a).single();if(!d)return{success:!1,error:"Doctor not found"};let{error:f}=await c.from("doctors").update({quota_used:0,quota_reset_at:new Date(new Date().getFullYear(),new Date().getMonth()+1,1).toISOString()}).eq("id",a);if(f)return{success:!1,error:"Failed to reset quota"};return await c.from("usage_logs").insert({doctor_id:a,action_type:"quota_reset",quota_before:d.quota_used,quota_after:0,metadata:{admin_id:b.adminId,reason:"Manual admin reset"}}),(0,e.revalidatePath)("/admin/dashboard"),(0,e.revalidatePath)("/admin/doctors"),{success:!0,data:!0}}catch(a){return console.error("Reset doctor quota error:",a),{success:!1,error:"Failed to reset quota"}}}(0,i.ensureServerEntryExports)([b,c,j,k]),(0,d.registerServerReference)(b,"7f3119b62c44ba233c6e17d3cb4ad82993a8fa6c9e",null),(0,d.registerServerReference)(c,"7f61b7244e7d1d9f901f583830c357ad547270ea16",null),(0,d.registerServerReference)(j,"400ef906d85e45dc697968c1d9fb5f164b7102286a",null),(0,d.registerServerReference)(k,"4068a3d4eaa9d307836531ef9e56a76d4f4a38edea",null)}},837536:a=>{var{g:b,__dirname:c}=a;a.n(a.i(938175))},33395:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__next_app__:()=>l,pages:()=>c,routeModule:()=>m,tree:()=>b});var d=a.i(269596),e=a.i(942505),f=a.i(745431),g=a.i(801327),h=a.i(97892),i=a.i(837536),j=a.i(660874),k=a.i(715847);a.i(781045);let b=["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{metadata:{},page:[()=>i,"[project]/src/app/admin/dashboard/page.tsx"]}]},{metadata:{}}]},{metadata:{}}]},{layout:[()=>d,"[project]/src/app/layout.tsx"],"not-found":[()=>e,"[project]/node_modules/next/dist/client/components/not-found-error.js"],forbidden:[()=>f,"[project]/node_modules/next/dist/client/components/forbidden-error.js"],unauthorized:[()=>g,"[project]/node_modules/next/dist/client/components/unauthorized-error.js"],"global-error":[()=>h,"[project]/src/app/global-error.tsx"]}],c=["[project]/src/app/admin/dashboard/page.tsx"],l={require:a.r,loadChunk:a.l},m=new j.AppPageRouteModule({definition:{kind:k.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})}},375608:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(269596),a.i(942505),a.i(745431),a.i(801327),a.i(97892),a.i(837536),a.i(660874),a.i(715847),a.i(781045),a.i(33395)},716572:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>e.ClientPageRoot,ClientSegmentRoot:()=>e.ClientSegmentRoot,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>e.HTTPAccessFallbackBoundary,LayoutRouter:()=>e.LayoutRouter,MetadataBoundary:()=>e.MetadataBoundary,OutletBoundary:()=>e.OutletBoundary,Postpone:()=>e.Postpone,RenderFromTemplateContext:()=>e.RenderFromTemplateContext,ViewportBoundary:()=>e.ViewportBoundary,__next_app__:()=>f.__next_app__,actionAsyncStorage:()=>e.actionAsyncStorage,collectSegmentData:()=>e.collectSegmentData,createMetadataComponents:()=>e.createMetadataComponents,createPrerenderParamsForClientSegment:()=>e.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>e.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>e.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>e.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>e.createTemporaryReferenceSet,decodeAction:()=>e.decodeAction,decodeFormState:()=>e.decodeFormState,decodeReply:()=>e.decodeReply,pages:()=>f.pages,patchFetch:()=>e.patchFetch,preconnect:()=>e.preconnect,preloadFont:()=>e.preloadFont,preloadStyle:()=>e.preloadStyle,prerender:()=>e.prerender,renderToReadableStream:()=>e.renderToReadableStream,routeModule:()=>f.routeModule,serverHooks:()=>e.serverHooks,taintObjectReference:()=>e.taintObjectReference,tree:()=>f.tree,workAsyncStorage:()=>e.workAsyncStorage,workUnitAsyncStorage:()=>e.workUnitAsyncStorage});var d=a.i(97892),e=a.i(913994),f=a.i(33395)},305077:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({ClientPageRoot:()=>d.ClientPageRoot,ClientSegmentRoot:()=>d.ClientSegmentRoot,GlobalError:()=>d.GlobalError,HTTPAccessFallbackBoundary:()=>d.HTTPAccessFallbackBoundary,LayoutRouter:()=>d.LayoutRouter,MetadataBoundary:()=>d.MetadataBoundary,OutletBoundary:()=>d.OutletBoundary,Postpone:()=>d.Postpone,RenderFromTemplateContext:()=>d.RenderFromTemplateContext,ViewportBoundary:()=>d.ViewportBoundary,__next_app__:()=>d.__next_app__,actionAsyncStorage:()=>d.actionAsyncStorage,collectSegmentData:()=>d.collectSegmentData,createMetadataComponents:()=>d.createMetadataComponents,createPrerenderParamsForClientSegment:()=>d.createPrerenderParamsForClientSegment,createPrerenderSearchParamsForClientPage:()=>d.createPrerenderSearchParamsForClientPage,createServerParamsForServerSegment:()=>d.createServerParamsForServerSegment,createServerSearchParamsForServerPage:()=>d.createServerSearchParamsForServerPage,createTemporaryReferenceSet:()=>d.createTemporaryReferenceSet,decodeAction:()=>d.decodeAction,decodeFormState:()=>d.decodeFormState,decodeReply:()=>d.decodeReply,pages:()=>d.pages,patchFetch:()=>d.patchFetch,preconnect:()=>d.preconnect,preloadFont:()=>d.preloadFont,preloadStyle:()=>d.preloadStyle,prerender:()=>d.prerender,renderToReadableStream:()=>d.renderToReadableStream,routeModule:()=>d.routeModule,serverHooks:()=>d.serverHooks,taintObjectReference:()=>d.taintObjectReference,tree:()=>d.tree,workAsyncStorage:()=>d.workAsyncStorage,workUnitAsyncStorage:()=>d.workUnitAsyncStorage}),a.i(375608);var d=a.i(716572)},458888:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardHeader:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AdminDashboardHeader() from the server but AdminDashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/admin-dashboard-header.tsx <module evaluation>","AdminDashboardHeader")}},338719:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({AdminDashboardHeader:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call AdminDashboardHeader() from the server but AdminDashboardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/admin-dashboard-header.tsx","AdminDashboardHeader")}},681463:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(458888);var d=a.i(338719);a.n(d)},725141:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],c=(0,d.default)("user-check",b)}},280517:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({UserCheck:()=>d.default});var d=a.i(725141)},816154:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],c=(0,d.default)("user-x",b)}},459014:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({UserX:()=>d.default});var d=a.i(816154)},129262:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({__iconNode:()=>b,default:()=>c});var d=a.i(274453);let b=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],c=(0,d.default)("trending-up",b)}},517513:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({TrendingUp:()=>d.default});var d=a.i(129262)},303334:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AdminDashboardStats:()=>k});var d=a.i(129629),e=a.i(755819),f=a.i(280517),g=a.i(459014),h=a.i(122146),i=a.i(533023),j=a.i(517513);function k({stats:a}){let b=[{name:"Total Doctors",value:a.total_doctors,icon:e.Users,color:"text-blue-600",bgColor:"bg-blue-100"},{name:"Pending Approvals",value:a.pending_approvals,icon:g.UserX,color:"text-yellow-600",bgColor:"bg-yellow-100"},{name:"Approved Doctors",value:a.approved_doctors,icon:f.UserCheck,color:"text-green-600",bgColor:"bg-green-100"},{name:"Total Consultations",value:a.total_consultations,icon:h.FileText,color:"text-purple-600",bgColor:"bg-purple-100"},{name:"AI Generations",value:a.total_ai_generations,icon:i.Zap,color:"text-indigo-600",bgColor:"bg-indigo-100"},{name:"Quota Usage",value:`${a.quota_usage_percentage}%`,icon:j.TrendingUp,color:a.quota_usage_percentage>80?"text-red-600":"text-emerald-600",bgColor:a.quota_usage_percentage>80?"bg-red-100":"bg-emerald-100"}];return(0,d.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",children:b.map(a=>(0,d.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,d.jsx)("div",{className:"p-5",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:`w-8 h-8 rounded-md ${a.bgColor} flex items-center justify-center`,children:(0,d.jsx)(a.icon,{className:`w-5 h-5 ${a.color}`})})}),(0,d.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,d.jsxs)("dl",{children:[(0,d.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:a.name}),(0,d.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:a.value})]})})]})})},a.name))})}},319830:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DoctorsTable:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call DoctorsTable() from the server but DoctorsTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/doctors-table.tsx <module evaluation>","DoctorsTable")}},601018:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({DoctorsTable:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call DoctorsTable() from the server but DoctorsTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/doctors-table.tsx","DoctorsTable")}},628667:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(319830);var d=a.i(601018);a.n(d)},239274:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({BillingManagement:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call BillingManagement() from the server but BillingManagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/billing-management.tsx <module evaluation>","BillingManagement")}},198875:a=>{"use strict";var{g:b,__dirname:c}=a;{a.s({BillingManagement:()=>b});let b=(0,a.i(77624).registerClientReference)(function(){throw Error("Attempted to call BillingManagement() from the server but BillingManagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/admin/billing-management.tsx","BillingManagement")}},79031:a=>{"use strict";var{g:b,__dirname:c}=a;a.i(239274);var d=a.i(198875);a.n(d)},11958:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({AdminData:()=>i});var d=a.i(129629),e=a.i(183850),f=a.i(303334),g=a.i(628667),h=a.i(79031);async function i({activeTab:a}){let[b,c]=await Promise.all([(0,e.getAdminDashboardStats)(),(0,e.getAllDoctorsWithStats)()]),i=b.success?b.data:null,j=c.success?c.data:[];return i?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(f.AdminDashboardStats,{stats:i}),"doctors"===a&&(0,d.jsx)(g.DoctorsTable,{doctors:j}),"billing"===a&&(0,d.jsx)(h.BillingManagement,{})]}):(0,d.jsx)("div",{className:"p-6 text-center",children:(0,d.jsx)("p",{className:"text-red-600",children:"Failed to load dashboard data"})})}},938175:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({default:()=>k});var d=a.i(129629),e=a.i(465421),f=a.i(624142),g=a.i(681463),h=a.i(11958),i=a.i(968123);a.i(622427);var j=a.i(766719);async function k({searchParams:a}){let b=await (0,f.getAdmin)();b||(0,j.redirect)("/admin/login");let c=(await a).tab||"doctors";return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.AdminDashboardHeader,{admin:b}),(0,d.jsxs)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,d.jsx)("a",{href:"/admin/dashboard?tab=doctors",className:`py-2 px-1 border-b-2 font-medium text-sm ${"doctors"===c?"border-teal-500 text-teal-600":"border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300"}`,children:"Doctor Management"}),(0,d.jsx)("a",{href:"/admin/dashboard?tab=billing",className:`py-2 px-1 border-b-2 font-medium text-sm ${"billing"===c?"border-teal-500 text-teal-600":"border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300"}`,children:"Billing & Referrals"})]})})}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(i.AdminDashboardSkeleton,{}),children:(0,d.jsx)(h.AdminData,{activeTab:c})})]})]})}},578927:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({applyReferralDiscount:()=>l,createBillingTransaction:()=>j,getAllBillingTransactions:()=>h,getBillingPlans:()=>g,getBillingStats:()=>n,getDoctorsBillingInfo:()=>i,markPaymentPaid:()=>k,updateDoctorBillingStatus:()=>m});var d=a.i(930768);a.i(996945);var e=a.i(729149),f=a.i(719358);async function g(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("billing_plans").select("*").eq("active",!0).order("monthly_price",{ascending:!0});if(c)return{success:!1,error:"Failed to fetch billing plans"};return{success:!0,data:b||[]}}catch(a){return console.error("Error fetching billing plans:",a),{success:!1,error:"An unexpected error occurred"}}}async function h(a=50,b=0){try{let c=await (0,e.createClient)(),{count:d}=await c.from("billing_transactions").select("*",{count:"exact",head:!0}),{data:f,error:g}=await c.from("billing_transactions").select(`
        *,
        doctor:doctors!billing_transactions_doctor_id_fkey(name, email, clinic_name),
        plan:billing_plans!billing_transactions_plan_id_fkey(name, monthly_price)
      `).order("created_at",{ascending:!1}).range(b,b+a-1);if(g)return{success:!1,error:"Failed to fetch billing transactions"};return{success:!0,data:{transactions:f||[],total_count:d||0}}}catch(a){return console.error("Error fetching billing transactions:",a),{success:!1,error:"An unexpected error occurred"}}}async function i(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("doctors").select(`
        id,
        name,
        email,
        clinic_name,
        billing_status,
        trial_ends_at,
        last_payment_date,
        next_billing_date,
        available_discount_amount,
        successful_referrals,
        referral_discount_earned,
        referred_by,
        current_plan:billing_plans!doctors_current_plan_id_fkey(name, monthly_price)
      `).eq("approved",!0).order("created_at",{ascending:!1});if(c)return{success:!1,error:"Failed to fetch doctors billing info"};let d=await Promise.all((b||[]).map(async b=>{let{data:c}=await a.from("billing_transactions").select("payment_status, final_amount").eq("doctor_id",b.id),d=c?.filter(a=>"paid"===a.payment_status).reduce((a,b)=>a+b.final_amount,0)||0,e=c?.filter(a=>"pending"===a.payment_status).reduce((a,b)=>a+b.final_amount,0)||0,f=null;if(b.referred_by){let{data:c}=await a.from("doctors").select("name").eq("id",b.referred_by).single();f=c?.name||null}return{id:b.id,name:b.name,email:b.email,clinic_name:b.clinic_name,billing_status:b.billing_status,trial_ends_at:b.trial_ends_at,last_payment_date:b.last_payment_date,next_billing_date:b.next_billing_date,available_discount_amount:b.available_discount_amount||0,current_plan:Array.isArray(b.current_plan)?b.current_plan[0]:b.current_plan,total_paid:d,pending_payments:e,referral_info:{successful_referrals:b.successful_referrals||0,discount_earned:b.referral_discount_earned||0,referred_by:f}}}));return{success:!0,data:d}}catch(a){return console.error("Error fetching doctors billing info:",a),{success:!1,error:"An unexpected error occurred"}}}async function j(a,b,c,d=31,g){try{let h=await (0,e.createClient)(),i=new Date,j=new Date(i);j.setDate(j.getDate()+d);let{data:k,error:l}=await h.from("billing_transactions").insert({doctor_id:a,plan_id:b,amount:c,final_amount:c,billing_period_start:i.toISOString(),billing_period_end:j.toISOString(),notes:g||null}).select("id").single();if(l)return{success:!1,error:"Failed to create billing transaction"};return(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:k.id}}catch(a){return console.error("Error creating billing transaction:",a),{success:!1,error:"An unexpected error occurred"}}}async function k(a,b,c){try{console.log("Marking payment as paid:",{transactionId:a,paymentMethod:b,paymentReference:c});let d=await (0,e.createClient)(),{data:g,error:h}=await d.from("billing_transactions").select("id, payment_status, doctor_id").eq("id",a).single();if(h)return console.error("Error fetching transaction:",h),{success:!1,error:`Transaction not found: ${h.message}`};if(!g)return{success:!1,error:"Transaction not found"};if("pending"!==g.payment_status)return{success:!1,error:`Cannot mark payment as paid. Current status: ${g.payment_status}`};let{error:i}=await d.rpc("complete_payment",{transaction_id:a});if(i)return console.error("Database error completing payment:",i),{success:!1,error:`Failed to complete payment: ${i.message}`};if(b||c){let{error:e}=await d.from("billing_transactions").update({payment_method:b||null,payment_reference:c||null,updated_at:new Date().toISOString()}).eq("id",a);e&&console.error("Error updating payment details:",e)}return console.log("Payment completed successfully with referral handling"),(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:!0}}catch(b){console.error("Unexpected error marking payment as paid:",b);let a=b instanceof Error?b.message:"Unknown error occurred";return{success:!1,error:`Unexpected error: ${a}`}}}async function l(a,b){try{let c=await (0,e.createClient)(),{data:d,error:g}=await c.rpc("apply_referral_discount",{transaction_id:a,discount_amount:b});if(g)return{success:!1,error:"Failed to apply referral discount"};return(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:d||!1}}catch(a){return console.error("Error applying referral discount:",a),{success:!1,error:"An unexpected error occurred"}}}async function m(a,b){try{let c=await (0,e.createClient)(),{error:d}=await c.from("doctors").update({billing_status:b}).eq("id",a);if(d)return{success:!1,error:"Failed to update billing status"};return(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:!0}}catch(a){return console.error("Error updating billing status:",a),{success:!1,error:"An unexpected error occurred"}}}async function n(){try{let a=await (0,e.createClient)(),{data:b}=await a.from("billing_transactions").select("final_amount, payment_status, payment_date").eq("payment_status","paid"),c=b?.reduce((a,b)=>a+b.final_amount,0)||0,d=new Date;d.setDate(1);let f=b?.filter(a=>a.payment_date&&new Date(a.payment_date)>=d).reduce((a,b)=>a+b.final_amount,0)||0,{data:g}=await a.from("billing_transactions").select("final_amount").eq("payment_status","pending"),h=g?.reduce((a,b)=>a+b.final_amount,0)||0,{count:i}=await a.from("doctors").select("*",{count:"exact",head:!0}).eq("billing_status","active"),{count:j}=await a.from("doctors").select("*",{count:"exact",head:!0}).eq("billing_status","trial"),{data:k}=await a.from("referral_discounts").select("discount_amount").eq("status","applied"),l=k?.reduce((a,b)=>a+b.discount_amount,0)||0;return{success:!0,data:{total_revenue:c,monthly_revenue:f,pending_payments:h,active_subscriptions:i||0,trial_users:j||0,referral_discounts_given:l}}}catch(a){return console.error("Error fetching billing stats:",a),{success:!1,error:"An unexpected error occurred"}}}(0,a.i(377991).ensureServerEntryExports)([g,h,i,j,k,l,m,n]),(0,d.registerServerReference)(g,"00eac522dfe7cabe2a6e71d6d21856221a9f52a549",null),(0,d.registerServerReference)(h,"601593b7e410bcaa66088bb5ce3de5cefb59afd238",null),(0,d.registerServerReference)(i,"00de095d837551bbb86629fce0ed19e4828a33db55",null),(0,d.registerServerReference)(j,"7c5e8dd2e45b0074066c6bf877fc2898170b991828",null),(0,d.registerServerReference)(k,"70b9ce8de127fd1018b818463398d90f2be7ba0dd1",null),(0,d.registerServerReference)(l,"6040083e50b05645892484999e301bb58cb15b820a",null),(0,d.registerServerReference)(m,"6065ee37f3b111f19406a7230c27536a11098d16b8",null),(0,d.registerServerReference)(n,"005135c7e18ac53e83714924ee812a087cbd5961ac",null)},515107:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(534304),a.i(680493),a.i(183850),a.i(578927)},967460:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({}),a.i(534304),a.i(680493),a.i(183850),a.i(578927),a.i(515107)},119220:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"0009c0c46815af213900a610cc4a7e2d00a036b8b7":()=>e.getContactRequests,"005135c7e18ac53e83714924ee812a087cbd5961ac":()=>g.getBillingStats,"0052d311627b1e498ec699db5ec34cf21bfb47393c":()=>d.adminLogout,"00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9":()=>e.getContactRequestsCount,"00de095d837551bbb86629fce0ed19e4828a33db55":()=>g.getDoctorsBillingInfo,"00eac522dfe7cabe2a6e71d6d21856221a9f52a549":()=>g.getBillingPlans,"400ef906d85e45dc697968c1d9fb5f164b7102286a":()=>f.performAdminAction,"4068a3d4eaa9d307836531ef9e56a76d4f4a38edea":()=>f.resetDoctorQuota,"601593b7e410bcaa66088bb5ce3de5cefb59afd238":()=>g.getAllBillingTransactions,"60bd2570fa48d9714b2922cc0537d755b4c2180d2e":()=>e.updateContactRequestStatus,"70b9ce8de127fd1018b818463398d90f2be7ba0dd1":()=>g.markPaymentPaid,"7c5e8dd2e45b0074066c6bf877fc2898170b991828":()=>g.createBillingTransaction,"7f3119b62c44ba233c6e17d3cb4ad82993a8fa6c9e":()=>f.getAdminDashboardStats,"7f61b7244e7d1d9f901f583830c357ad547270ea16":()=>f.getAllDoctorsWithStats});var d=a.i(534304),e=a.i(680493),f=a.i(183850),g=a.i(578927);a.i(515107)},942068:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({"0009c0c46815af213900a610cc4a7e2d00a036b8b7":()=>d["0009c0c46815af213900a610cc4a7e2d00a036b8b7"],"005135c7e18ac53e83714924ee812a087cbd5961ac":()=>d["005135c7e18ac53e83714924ee812a087cbd5961ac"],"0052d311627b1e498ec699db5ec34cf21bfb47393c":()=>d["0052d311627b1e498ec699db5ec34cf21bfb47393c"],"00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9":()=>d["00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9"],"00de095d837551bbb86629fce0ed19e4828a33db55":()=>d["00de095d837551bbb86629fce0ed19e4828a33db55"],"00eac522dfe7cabe2a6e71d6d21856221a9f52a549":()=>d["00eac522dfe7cabe2a6e71d6d21856221a9f52a549"],"400ef906d85e45dc697968c1d9fb5f164b7102286a":()=>d["400ef906d85e45dc697968c1d9fb5f164b7102286a"],"4068a3d4eaa9d307836531ef9e56a76d4f4a38edea":()=>d["4068a3d4eaa9d307836531ef9e56a76d4f4a38edea"],"601593b7e410bcaa66088bb5ce3de5cefb59afd238":()=>d["601593b7e410bcaa66088bb5ce3de5cefb59afd238"],"60bd2570fa48d9714b2922cc0537d755b4c2180d2e":()=>d["60bd2570fa48d9714b2922cc0537d755b4c2180d2e"],"70b9ce8de127fd1018b818463398d90f2be7ba0dd1":()=>d["70b9ce8de127fd1018b818463398d90f2be7ba0dd1"],"7c5e8dd2e45b0074066c6bf877fc2898170b991828":()=>d["7c5e8dd2e45b0074066c6bf877fc2898170b991828"],"7f3119b62c44ba233c6e17d3cb4ad82993a8fa6c9e":()=>d["7f3119b62c44ba233c6e17d3cb4ad82993a8fa6c9e"],"7f61b7244e7d1d9f901f583830c357ad547270ea16":()=>d["7f61b7244e7d1d9f901f583830c357ad547270ea16"]}),a.i(967460);var d=a.i(119220)},477303:a=>{var{g:b,__dirname:c}=a;a.v(a=>Promise.resolve().then(()=>a(537111)))},737753:a=>{var{g:b,__dirname:c}=a;a.v(b=>Promise.all(["server/chunks/ssr/[root-of-the-server]__65230092._.js","server/chunks/ssr/node_modules_ws_58f5cae3._.js"].map(b=>a.l(b))).then(()=>b(628329)))}};

//# sourceMappingURL=%5Broot-of-the-server%5D__f772b30e._.js.map