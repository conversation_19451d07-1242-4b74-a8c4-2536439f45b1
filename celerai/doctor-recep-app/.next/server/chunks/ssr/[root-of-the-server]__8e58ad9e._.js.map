{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/src/lib/auth/session.ts", "turbopack:///[project]/src/lib/auth/dal.ts", "turbopack:///[project]/src/lib/storage.ts", "turbopack:///[project]/src/components/ui/skeleton-loaders.tsx/proxy.mjs", "turbopack:///[project]/.next-internal/server/app/dashboard/page/actions.js (server actions loader)", "turbopack:///[project]/src/components/recording/new-recording-interface.tsx/proxy.mjs", "turbopack:///[project]/src/components/data/dashboard-data.tsx", "turbopack:///[project]/src/components/shared/dashboard-client.tsx/proxy.mjs", "turbopack:///[project]/src/components/pwa/pwa-install-prompt.tsx/proxy.mjs", "turbopack:///[project]/src/app/dashboard/page.tsx", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "import 'server-only'\nimport { SignJWT, jwtVerify } from 'jose'\nimport { cookies } from 'next/headers'\nimport { SessionPayload } from '@/lib/types' // UNCOMMENT THIS LINE\n// TODO: Remove this line (type SessionPayload = any)\n// type SessionPayload = any // REMOVE THIS LINE\n\nconst secretKey = process.env.SESSION_SECRET\nconst encodedKey = new TextEncoder().encode(secretKey)\n\nexport async function encrypt(payload: SessionPayload) {\n  // Fix: Cast payload to Record<string, unknown> for SignJWT\n  return new SignJWT(payload as unknown as Record<string, unknown>) // Keep this cast\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('7d')\n    .sign(encodedKey)\n}\n\nexport async function decrypt(session: string | undefined = '') {\n  try {\n    if (!session) {\n      return null\n    }\n\n    const { payload } = await jwtVerify(session, encodedKey, {\n      algorithms: ['HS256'],\n    })\n    // Fix: cast to unknown first, then to SessionPayload for type safety\n    return payload as unknown as SessionPayload // Keep this cast\n  } catch {\n    console.log('Failed to verify session')\n    return null\n  }\n}\n\nexport async function createSession(userId: string) {\n  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n  const session = await encrypt({ userId, expiresAt })\n  const cookieStore = await cookies()\n\n  console.log('DEBUG: Creating session for user:', userId)\n  console.log('DEBUG: Session expires at:', expiresAt)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expiresAt,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session cookie set successfully')\n}\n\nexport async function updateSession() {\n  const cookieStore = await cookies()\n  const session = cookieStore.get('session')?.value\n  const payload = await decrypt(session)\n\n  console.log('DEBUG: Updating session - session exists:', !!session)\n  console.log('DEBUG: Updating session - payload valid:', !!payload)\n\n  if (!session || !payload) {\n    console.log('DEBUG: Cannot update session - missing session or payload')\n    return null\n  }\n\n  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n\n  cookieStore.set('session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expires,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Session updated successfully')\n}\n\nexport async function refreshSession(userId: string) {\n  // Delete old session and create new one\n  console.log('DEBUG: Refreshing session for user:', userId)\n  await deleteSession()\n  await createSession(userId)\n  console.log('DEBUG: Session refresh completed')\n}\n\nexport async function deleteSession() {\n  const cookieStore = await cookies()\n  console.log('DEBUG: Deleting session cookie')\n  cookieStore.delete('session')\n  console.log('DEBUG: Session cookie deleted')\n}\n", "import 'server-only'\nimport { cache } from 'react'\nimport { cookies } from 'next/headers'\nimport { redirect } from 'next/navigation'\nimport { decrypt } from './session'\nimport { createClient } from '@/lib/supabase/server'\nimport { <PERSON> } from '@/lib/types'\n\n\n\nexport const verifySession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    redirect('/login')\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const checkSession = cache(async () => {\n  const cookieStore = await cookies()\n  const cookie = cookieStore.get('session')?.value\n  const session = await decrypt(cookie)\n\n  if (!session?.userId) {\n    return null\n  }\n\n  return { isAuth: true, userId: session.userId }\n})\n\nexport const getUser = cache(async (): Promise<Doctor | null> => {\n  const session = await verifySession()\n  if (!session) return null\n\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('*')\n      .eq('id', session.userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n      password_hash: user.password_hash\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\nexport const getUserById = cache(async (userId: string): Promise<Doctor | null> => {\n  try {\n    const supabase = await createClient()\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, email, name, phone, clinic_name, monthly_quota, quota_used, quota_reset_at, approved, approved_by, approved_at, created_at, updated_at, password_hash')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch user by ID:', error.message || error)\n      return null\n    }\n\n    if (!user) return null\n    // Return user without template_config conversion since it's removed\n    return {\n      ...user,\n    } as unknown as Doctor\n  } catch (error) {\n    console.error('Failed to fetch user by ID:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n\n// Get quota information for a doctor\nexport const getDoctorQuota = cache(async (userId: string) => {\n  try {\n    const supabase = await createClient()\n    const { data: doctor, error } = await supabase\n      .from('doctors')\n      .select('monthly_quota, quota_used, quota_reset_at')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Failed to fetch quota:', error.message || error)\n      return null\n    }\n\n    const quotaRemaining = doctor.monthly_quota - doctor.quota_used\n    const quotaPercentage = Math.round((doctor.quota_used / doctor.monthly_quota) * 100)\n    const resetDate = new Date(doctor.quota_reset_at)\n    const daysUntilReset = Math.ceil((resetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))\n\n    return {\n      monthly_quota: doctor.monthly_quota,\n      quota_used: doctor.quota_used,\n      quota_remaining: quotaRemaining,\n      quota_percentage: quotaPercentage,\n      quota_reset_at: doctor.quota_reset_at,\n      days_until_reset: Math.max(0, daysUntilReset),\n    }\n  } catch (error) {\n    console.error('Failed to fetch quota:', error instanceof Error ? error.message : error)\n    return null\n  }\n})\n", "import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'\n\n// Storage configuration - Migrated to Cloudflare R2\nexport const STORAGE_CONFIG = {\n  // R2 Configuration\n  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',\n  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',\n\n  // Folder prefixes (replaces separate buckets)\n  AUDIO_PREFIX: 'consultation-audio',\n  IMAGE_PREFIX: 'consultation-images',\n\n  // File limits\n  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file\n  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation\n  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],\n  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],\n  RETENTION_DAYS: 30\n}\n\n// R2 Client configuration\nconst createR2Client = () => {\n  return new S3Client({\n    region: 'auto',\n    endpoint: `https://${process.env.R2_ACCOUNT_ID || '57014886c6cd87ebacf23a94e56a6e0c'}.r2.cloudflarestorage.com`,\n    credentials: {\n      accessKeyId: process.env.R2_ACCESS_KEY_ID || '4dff08f96bf2f040b48bf3973813f7f0',\n      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '****************************************************************',\n    },\n  })\n}\n\n// File validation\nexport function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {\n  // Check file size\n  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {\n    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }\n  }\n\n  // Check file type\n  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES\n  if (!allowedTypes.includes(file.type)) {\n    return { valid: false, error: `File type ${file.type} is not allowed` }\n  }\n\n  return { valid: true }\n}\n\n// Generate storage path with folder prefix for R2\nexport function generateStoragePath(\n  doctorId: string,\n  consultationId: string,\n  fileName: string,\n  type: 'audio' | 'image'\n): string {\n  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')\n  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`\n}\n\n// Upload file to Cloudflare R2\nexport async function uploadFile(\n  file: File,\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; url?: string; error?: string }> {\n  try {\n    // Validate file\n    const validation = validateFile(file, type)\n    if (!validation.valid) {\n      return { success: false, error: validation.error }\n    }\n\n    const r2Client = createR2Client()\n    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)\n\n    // Convert file to buffer\n    const fileBuffer = await file.arrayBuffer()\n\n    // Upload to R2 - preserve exact Content-Type from file\n    const uploadCommand = new PutObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n      Body: new Uint8Array(fileBuffer),\n      ContentType: file.type, // Use original file.type to preserve codec info\n      CacheControl: 'public, max-age=3600',\n    })\n\n    await r2Client.send(uploadCommand)\n\n    // Generate public URL\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    return { success: true, url: publicUrl }\n  } catch (error) {\n    console.error('R2 upload error:', error)\n    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }\n  }\n}\n\n// Upload multiple files\nexport async function uploadMultipleFiles(\n  files: File[],\n  doctorId: string,\n  consultationId: string,\n  type: 'audio' | 'image'\n): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {\n  const results = await Promise.all(\n    files.map(file => uploadFile(file, doctorId, consultationId, type))\n  )\n\n  const successful = results.filter(r => r.success)\n  const failed = results.filter(r => !r.success)\n\n  if (failed.length > 0) {\n    return {\n      success: false,\n      errors: failed.map(f => f.error || 'Unknown error')\n    }\n  }\n\n  return {\n    success: true,\n    urls: successful.map(s => s.url!).filter(Boolean)\n  }\n}\n\n// Delete file from R2 storage\nexport async function deleteFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const r2Client = createR2Client()\n\n    const deleteCommand = new DeleteObjectCommand({\n      Bucket: STORAGE_CONFIG.BUCKET_NAME,\n      Key: filePath,\n    })\n\n    await r2Client.send(deleteCommand)\n\n    return { success: true }\n  } catch (error) {\n    console.error('R2 delete error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }\n  }\n}\n\n// Extract file path from R2 URL\nexport function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {\n  try {\n    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX\n    const prefixPath = `/${prefix}/`\n    const index = url.indexOf(prefixPath)\n\n    if (index === -1) return null\n\n    return url.substring(url.indexOf(prefix))\n  } catch {\n    return null\n  }\n}\n\n// Download file from R2 storage\nexport async function downloadFile(\n  filePath: string,\n  _type: 'audio' | 'image'\n): Promise<{ success: boolean; data?: Blob; error?: string }> {\n  try {\n    // For public files, we can fetch directly from the custom domain\n    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`\n\n    const response = await fetch(publicUrl)\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.blob()\n    return { success: true, data }\n  } catch (error) {\n    console.error('R2 download error:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }\n  }\n}\n\n// Calculate total file size\nexport function calculateTotalFileSize(files: File[]): number {\n  return files.reduce((total, file) => total + file.size, 0)\n}\n\n// Validate total consultation file size\nexport function validateTotalSize(files: File[]): { valid: boolean; error?: string } {\n  const totalSize = calculateTotalFileSize(files)\n  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {\n    return {\n      valid: false,\n      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`\n    }\n  }\n  return { valid: true }\n}\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminDashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminDashboardSkeleton() from the server but AdminDashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"AdminDashboardSkeleton\",\n);\nexport const ConsultationsListSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConsultationsListSkeleton() from the server but ConsultationsListSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"ConsultationsListSkeleton\",\n);\nexport const DashboardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"DashboardSkeleton\",\n);\nexport const DashboardStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardStatsSkeleton() from the server but DashboardStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"DashboardStatsSkeleton\",\n);\nexport const InfoPageSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfoPageSkeleton() from the server but InfoPageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"InfoPageSkeleton\",\n);\nexport const QuotaCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuotaCardSkeleton() from the server but QuotaCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"QuotaCardSkeleton\",\n);\nexport const ReferralStatsSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReferralStatsSkeleton() from the server but ReferralStatsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/skeleton-loaders.tsx <module evaluation>\",\n    \"ReferralStatsSkeleton\",\n);\n", "export {getConsultationStats as '0021892c2090f634723ac0a0d1cd1d8c637aae5b9c'} from 'ACTIONS_MODULE0'\nexport {getConsultations as '40181945d99bd1b1c806279418d8c4e384a1b0bd15'} from 'ACTIONS_MODULE0'\nexport {clearEditedNote as '40cad8ac11d66cae6a36deaf0347461ff74e2f07d8'} from 'ACTIONS_MODULE0'\nexport {createConsultation as '40d596bef0460f198d588bbf8dede026bc85cca740'} from 'ACTIONS_MODULE0'\nexport {updateConsultationImages as '6016d1147dadc5ab75f7387f44ab799f7cd595708b'} from 'ACTIONS_MODULE0'\nexport {approveConsultation as '60648db8471ac7da6666e2d2e8b2cf27a97b9b4688'} from 'ACTIONS_MODULE0'\nexport {updateAdditionalNotes as '6083efdc7ec9351b8f136d9a9b2f202431b35f7bc7'} from 'ACTIONS_MODULE0'\nexport {saveStreamingSummary as '608aaaf92844312537830e8c8d0c49debb89bda21b'} from 'ACTIONS_MODULE0'\nexport {saveEditedNote as '6090ac31595b581fab8f32262309efcdcab1c7eb47'} from 'ACTIONS_MODULE0'\nexport {updateConsultationType as '60918679bc7b17099a1e6b5713c5ee4e5bc650d7eb'} from 'ACTIONS_MODULE0'\nexport {updatePatientName as '609aacf458083825c0e86748f13c86d5773450588b'} from 'ACTIONS_MODULE0'\nexport {addAdditionalImages as '60a1a8aa9821dcefb87fffe6a0f6d57a9fc430d50e'} from 'ACTIONS_MODULE0'\nexport {addAdditionalAudio as '60b3bf115b7639c2355ff16ee9fa53425dad65720c'} from 'ACTIONS_MODULE0'\nexport {deleteConsultationImage as '60c855f485dc14093b0fee942b2204ae7dca69142e'} from 'ACTIONS_MODULE0'\nexport {deleteAdditionalAudio as '60fe749e3c9b3a6222eaf37afc1421459616909387'} from 'ACTIONS_MODULE0'\nexport {generateSummaryStream as '7f7dd3bdf6242116733f7ef527b1e8f307d343dacd'} from 'ACTIONS_MODULE0'\nexport {createConsultationWithFiles as '7fc832fd06db7232f37f9b1b44631bd2956c9b940c'} from 'ACTIONS_MODULE0'\nexport {logout as '008a705899f288d02ab102cb7194752ef57be976ca'} from 'ACTIONS_MODULE1'\nexport {getReferralInfo as '40757964b8c9b072995f44631743e71306c1784480'} from 'ACTIONS_MODULE2'\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NewRecordingInterface = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewRecordingInterface() from the server but NewRecordingInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/recording/new-recording-interface.tsx <module evaluation>\",\n    \"NewRecordingInterface\",\n);\n", "import { getUser } from '@/lib/auth/dal'\nimport { getConsultations } from '@/lib/actions/consultations'\nimport { NewRecordingInterface } from '@/components/recording/new-recording-interface'\n\ninterface DashboardDataProps {\n  doctorId: string\n}\n\nexport async function DashboardData({ doctorId }: DashboardDataProps) {\n  // This runs inside Suspense boundary, not blocking initial page render\n  // OPTIMIZED: Load only initial page (15 items) for fast initial render\n  const [user, consultationsResult] = await Promise.all([\n    getUser(),\n    getConsultations({ page: 1, pageSize: 15 }), // Small initial load\n  ])\n\n  const consultations = consultationsResult.success ? consultationsResult.data.consultations || [] : []\n  const hasMore = consultationsResult.success ? consultationsResult.data.hasMore : false\n\n  return (\n    <NewRecordingInterface\n      user={user}\n      consultations={consultations}\n      hasMore={hasMore}\n      doctorId={doctorId}\n    />\n  )\n}\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardClient() from the server but DashboardClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/dashboard-client.tsx <module evaluation>\",\n    \"DashboardClient\",\n);\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PWAInstallPrompt = registerClientReference(\n    function() { throw new Error(\"Attempted to call PWAInstallPrompt() from the server but PWAInstallPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pwa/pwa-install-prompt.tsx <module evaluation>\",\n    \"PWAInstallPrompt\",\n);\n", "import { Metadata } from 'next'\nimport { Suspense } from 'react'\nimport { verifySession } from '@/lib/auth/dal'\nimport { DashboardData } from '@/components/data/dashboard-data'\nimport { DashboardClient } from '@/components/shared/dashboard-client'\nimport { PWAInstallPrompt } from '@/components/pwa/pwa-install-prompt'\nimport { DashboardSkeleton } from '@/components/ui/skeleton-loaders'\n\nexport const metadata: Metadata = {\n  title: 'Dashboard - Celer AI',\n  description: 'Create new patient consultations with AI-powered summaries',\n}\n\nexport default async function DashboardPage() {\n  // OPTIMIZED: Only verify session (fast), then stream the rest\n  const session = await verifySession()\n\n  return (\n    <>\n      {/* STREAMING: Data loads progressively while user sees immediate structure */}\n      <Suspense fallback={<DashboardSkeleton />}>\n        <DashboardData doctorId={session.userId} />\n      </Suspense>\n\n      {/* Client-side components for modals */}\n      <DashboardClient doctorId={session.userId} />\n\n      {/* PWA Install Prompt */}\n      <PWAInstallPrompt />\n    </>\n  )\n}\n", "import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "module", "exports", "require", "AppPageRouteModule", "tree", "pages", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": "saA0BQM,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,yxBC1BjC,EAAA,CAAA,CAAA,QACA,IAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAKA,IAAM,EAAY,QAAQ,GAAG,CAAC,cAAc,CACtC,EAAa,IAAI,cAAc,MAAM,CAAC,GAErC,eAAe,EAAQ,CAAuB,EAEnD,OAAO,IAAI,EAAA,OAAO,CAAC,GAChB,MAD+D,YAC7C,CAAC,CAAE,GAD2D,CACtD,EADlB,KAC0B,GAClC,WAAW,GACX,iBAAiB,CAAC,MAClB,IAAI,CAAC,EACV,CAEO,eAAe,EAAQ,EAA8B,EAAE,EAC5D,GAAI,CACF,GAAI,CAAC,EACH,OADY,AACL,KAGT,GAAM,SAAE,CAAO,CAAE,CAAG,MAAM,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,EAAS,EAAY,CACvD,WAAY,CAAC,QAAQ,AACvB,CAF0B,EAI1B,OAAO,CACT,CAAE,KAAM,CAEN,CAH4C,MAE5C,QAAQ,GAAG,AAFkD,CAEjD,4BACL,IACT,CACF,CAEO,eAAe,EAAc,CAAc,EAChD,IAAM,EAAY,IAAI,KAAK,KAAK,GAAG,GAAK,IAAI,IACtC,CAD2C,CACjC,IADsC,EAChC,EAAQ,CAD6B,OAC3B,YAAQ,CAAU,GAC5C,EAAc,MAAM,GAAA,EAAA,OAAA,AAAM,IAEhC,QAAQ,GAAG,CAAC,cAFc,sBAEuB,GACjD,QAAQ,GAAG,CAAC,6BAA8B,GAE1C,EAAY,GAAG,CAAC,UAAW,EAAS,CAClC,UAAU,EACV,QAAQ,EACR,QAAS,EACT,SAAU,MACV,KAAM,GACR,GAEA,QAAQ,GAAG,CAAC,yCACd,CAEO,eAAe,IACpB,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,IAC1B,EAAU,EAAY,GAAG,CAAC,YAAY,MACtC,AAFoB,EAEV,MAAM,EAAQ,GAK9B,GAHA,QAAQ,GAAG,CAAC,4CAA6C,CAAC,CAAC,GAC3D,QAAQ,GAAG,CAAC,2CAA4C,CAAC,CAAC,GAEtD,CAAC,GAAW,CAAC,EAEf,OAFwB,AACxB,QAAQ,GAAG,CAAC,6DACL,KAGT,IAAM,EAAU,IAAI,KAAK,KAAK,GAAG,GAAK,IAAI,IAE1C,CAF+C,CAEnC,GAAG,CAFqC,AAEpC,KAFyC,KAE9B,EAAS,CAClC,UAAU,EACV,OAAQ,GACR,QAAS,EACT,SAAU,MACV,KAAM,GACR,GAEA,QAAQ,GAAG,CAAC,sCACd,CAEO,eAAe,EAAe,CAAc,EAEjD,QAAQ,GAAG,CAAC,sCAAuC,GACnD,MAAM,IACN,MAAM,EAAc,GACpB,QAAQ,GAAG,CAAC,mCACd,CAEO,eAAe,IACpB,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,IAChC,QAAQ,GAAG,CAAC,cADc,oBAE1B,EAAY,MAAM,CAAC,WACnB,QAAQ,GAAG,CAAC,gCACd,sJC9FA,EAAA,CAAA,CAAA,QACA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,CAAA,CAAA,QAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAKO,IAAM,EAAgB,CAAA,EAAA,EAAA,KAAA,AAAI,EAAE,UACjC,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,GADC,IACD,AAAM,IAC1B,EAAS,EAAY,GAAG,CAAC,YAAY,MADjB,AAEpB,EAAU,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,EAAE,GAM9B,OAJI,AAAC,GAAS,QAAQ,AACpB,CAAA,EAAA,EAAA,EAHoB,MAGpB,AAAO,EAAE,UAGJ,CAAE,OAAQ,GAAM,MAHrB,CAG6B,EAAQ,MAAM,AAAC,CAChD,GAEa,EAAe,CAAA,EAAA,EAAA,KAAI,AAAJ,EAAM,UAChC,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,GADA,IACA,AAAM,IAC1B,EAAS,EAAY,GAAG,CAAC,YAAY,MADjB,AAEpB,EAAU,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,EAAE,UAEzB,AAAL,GAAc,CAAV,MAIG,CAJe,AAIb,OANa,CAML,EAAM,OAAQ,EAAQ,MAAM,AAAC,EAHrC,IAIX,GAEa,EAAU,CAAA,EAAA,EAAA,KAAI,AAAJ,EAAM,UAC3B,IAAM,EAAU,MAAM,IACtB,GAAI,CAAC,AAFgB,EAEP,OAAO,KAErB,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,EAAA,YAAA,AAAW,IAC5B,CAAE,KAAM,CAAI,OAAE,CAAK,CAAE,CAAG,IADP,EACa,EACjC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,KAAM,EAAQ,MAAM,EACvB,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,wBAAyB,EAAM,OAAO,EAAI,GACjD,KAGT,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,GAAG,CAAI,CACP,cAAe,EAAK,aAAa,AACnC,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,wBAAyB,aAAiB,MAAQ,EAAM,OAAO,CAAG,GACzE,IACT,CACF,GAEa,EAAc,CAAA,EAAA,EAAA,KAAA,AAAI,EAAE,MAAO,IACtC,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,CAFA,CAEA,YAAW,AAAX,IACjB,CAAE,KAAM,CAAI,OAAE,CAAK,CAAE,CAAG,IADP,EACa,EACjC,IAAI,CAAC,WACL,MAAM,CAAC,6JACP,EAAE,CAAC,KAAM,GACT,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,8BAA+B,EAAM,OAAO,EAAI,GACvD,KAGT,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,GAAG,CACL,AADS,CAEX,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,aAAiB,MAAQ,EAAM,OAAO,CAAG,GAC/E,IACT,CACF,GAGa,EAAiB,GAAA,EAAA,KAAA,AAAI,EAAE,MAAO,IACzC,GAAI,CACF,IAAM,EAAW,MAAM,CAAA,EAAA,CAFG,CAEH,YAAW,AAAX,IACjB,CAAE,KAAM,CAAM,OAAE,CAAK,CAAE,CAAG,IADT,EACe,EACnC,IAAI,CAAC,WACL,MAAM,CAAC,6CACP,EAAE,CAAC,KAAM,GACT,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,yBAA0B,EAAM,OAAO,EAAI,GAClD,KAGT,IAAM,EAAiB,EAAO,aAAa,CAAG,EAAO,UAAU,CACzD,EAAkB,KAAK,KAAK,CAAE,EAAO,UAAU,CAAG,EAAO,aAAa,CAAI,KAC1E,EAAY,IAAI,KAAK,EAAO,cAAc,EAC1C,EAAiB,KAAK,IAAI,CAAC,AAAC,GAAU,OAAO,GAAK,KAAK,GAAG,EAAA,CAAE,CAAK,GAAD,IAEtE,AAF8E,KAAK,CAE5E,CACL,GAHsF,EAAE,SAGzE,EAAO,aAAa,CACnC,WAAY,EAAO,UAAU,CAC7B,gBAAiB,EACjB,iBAAkB,EAClB,eAAgB,EAAO,cAAc,CACrC,iBAAkB,KAAK,GAAG,CAAC,EAAG,EAChC,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,yBAA0B,aAAiB,MAAQ,EAAM,OAAO,CAAG,GAC1E,IACT,CACF,ghBCxHA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,QAGO,IAAM,EAAiB,CAE5B,YAAa,QAAQ,GAAG,CAAC,cAAc,EAAI,kBAC3C,WAAY,QAAQ,GAAG,CAAC,aAAa,EAAI,8BAGzC,aAAc,qBACd,aAAc,sBAGd,cAAe,MAAM,IACrB,GAD4B,YACZ,MAAM,IACtB,GAD6B,iBACR,CAAC,aAAc,YAAa,YAAa,YAAa,aAAc,YAAa,YAAY,CAClH,oBAAqB,CAAC,aAAc,YAAa,YAAa,aAAc,aAAa,CACzF,eAAgB,EAClB,EAGM,EAAiB,IACd,IAAI,EAAA,QAAQ,CAAC,CAClB,OAAQ,OACR,SAAU,CAAC,GAFF,KAEU,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAI,mCAAmC,yBAAyB,CAAC,CAC/G,YAAa,CACX,YAAa,QAAQ,GAAG,CAAC,gBAAgB,EAAI,mCAC7C,gBAAiB,QAAQ,GAAG,CAAC,oBAAoB,EAAI,kEACvD,CACF,GAIK,SAAS,EAAa,CAAU,CAAE,CAAuB,SAE9D,AAAI,EAAK,IAAI,CAAG,EAAe,aAAa,CACnC,CADqC,AACnC,OAAO,EAAO,MAAO,CAAC,kBAAkB,EAAE,EAAe,aAAa,CAAG,KAAO,KAAK,QAAQ,CAAC,AAAC,EAKrG,CADyB,UAAT,EAAmB,EAAe,mBAAmB,CAAG,EAAe,mBAAA,AAAmB,EAC7F,QAAQ,CAAC,EAAK,IAAI,EAI7B,CAJgC,AAI9B,OAAO,CAAK,EAHZ,CAAE,OAAO,EAAO,MAAO,CAAC,UAAU,EAAE,EAAK,IAAI,CAAC,eAAe,CAAC,AAAC,CAI1E,CAGO,SAAS,EACd,CAAgB,CAChB,CAAsB,CACtB,CAAgB,CAChB,CAAuB,EAEvB,IAAM,EAAoB,EAAS,OAAO,CAAC,kBAAmB,KACxD,EAAkB,UAAT,EAAmB,EAAe,YAAY,CAAG,EAAe,YAAY,CAC3F,MAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAS,CAAC,EAAE,EAAe,CAAC,EAAE,EAAA,CAAmB,AACvE,CAGO,eAAe,EACpB,CAAU,CACV,CAAgB,CAChB,CAAsB,CACtB,CAAuB,EAEvB,GAAI,CAEF,IAAM,EAAa,EAAa,EAAM,GACtC,GAAI,CAAC,EAAW,KAAK,CACnB,CADqB,KACd,CAAE,SAAS,EAAO,MAAO,EAAW,KAAK,AAAC,EAGnD,IAAM,EAAW,IACX,EAAW,EAAoB,EAAU,EAAgB,EAAK,IAAI,CAAE,GAGpE,EAAa,MAAM,EAAK,WAAW,GAGnC,EAAgB,IAAI,EAAA,gBAAgB,CAAC,CACzC,OAAQ,EAAe,UADC,CACU,CAClC,IAAK,EACL,KAAM,IAAI,WAAW,GACrB,YAAa,EAAK,IAAI,CACtB,aAAc,sBAChB,EAEA,OAAM,EAAS,IAAI,CAAC,GAGpB,IAAM,EAAY,CAAA,EAAG,EAAe,UAAU,CAAC,CAAC,EAAE,EAAA,CAAU,CAE5D,MAAO,CAAE,SAAS,EAAM,IAAK,CAAU,CACzC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mBAAoB,GAC3B,CAAE,SAAS,EAAO,MAAO,CAAC,eAAe,EAAE,aAAiB,MAAQ,EAAM,OAAO,CAAG,gBAAA,CAAiB,AAAC,CAC/G,CACF,CAGO,eAAe,EACpB,CAAa,CACb,CAAgB,CAChB,CAAsB,CACtB,CAAuB,EAEvB,IAAM,EAAU,MAAM,QAAQ,GAAG,CAC/B,EAAM,GAAG,CAAC,GAAQ,EAAW,EAAM,EAAU,EAAgB,KAGzD,EAAa,EAAQ,MAAM,CAAC,GAAK,EAAE,OAAO,EAC1C,EAAS,EAAQ,MAAM,CAAC,GAAK,CAAC,EAAE,OAAO,SAE7C,AAAI,EAAO,MAAM,CAAG,EACX,CADc,AAEnB,SAAS,EACT,OAAQ,EAAO,GAAG,CAAC,GAAK,EAAE,KAAK,EAAI,gBACrC,EAGK,CACL,SAAS,EACT,KAAM,EAAW,GAAG,CAAC,GAAK,EAAE,GAAG,EAAG,MAAM,CAAC,QAC3C,CACF,CAGO,eAAe,EACpB,CAAgB,CAChB,CAAwB,EAExB,GAAI,CACF,IAAM,EAAW,IAEX,EAAgB,IAAI,EAAA,mBAAmB,CAAC,CAC5C,OAAQ,EAAe,OADC,IACU,CAClC,IAAK,CACP,GAIA,OAFA,MAAM,EAAS,IAAI,CAAC,GAEb,CAAE,SAAS,CAAK,CACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mBAAoB,GAC3B,CAAE,QAAS,GAAO,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,eAAgB,CAC3F,CACF,CAGO,SAAS,EAAuB,CAAW,CAAE,CAAuB,EACzE,GAAI,CACF,IAAM,EAAkB,UAAT,EAAmB,EAAe,YAAY,CAAG,EAAe,YAAY,CACrF,EAAa,CAAC,CAAC,EAAE,EAAO,CAAC,CAAC,CAC1B,EAAQ,EAAI,OAAO,CAAC,GAE1B,GAAc,CAAC,IAAX,EAAc,OAAO,KAEzB,OAAO,EAAI,SAAS,CAAC,EAAI,OAAO,CAAC,GACnC,CAAE,KAAM,CACN,OAAO,IACT,CACF,CAGO,eAAe,EACpB,CAAgB,CAChB,CAAwB,EAExB,GAAI,CAEF,IAAM,EAAY,CAAA,EAAG,EAAe,UAAU,CAAC,CAAC,EAAE,EAAA,CAAU,CAEtD,EAAW,MAAM,MAAM,GAC7B,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,CAAC,KAAK,EAAE,EAAS,MAAM,CAAC,EAAE,EAAE,EAAS,UAAU,CAAA,CAAE,EAGnE,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,MAAO,CAAE,QAAS,QAAM,CAAK,CAC/B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,CAAE,SAAS,EAAO,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,iBAAkB,CAC7F,CACF,CAGO,SAAS,EAAuB,CAAa,EAClD,OAAO,EAAM,MAAM,CAAC,CAAC,EAAO,IAAS,EAAQ,EAAK,IAAI,CAAE,EAC1D,CAGO,SAAS,EAAkB,CAAa,SAEzC,AAAJ,AADkB,EAAuB,GACzB,EAAe,cAAc,CACpC,CADsC,AAE3C,MAAO,GACP,MAAO,CAAC,wBAAwB,EAAE,EAAe,cAAc,CAAG,KAAO,KAAK,QAAQ,CAAC,AACzF,EAEK,CAAE,OAAO,CAAK,CACvB,wPC1MA,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAyB,GAAA,EAAA,uBAAA,AAAsB,EACxD,WADkC,AACrB,MAAM,AAAI,MAAM,0PAA4P,EACzR,uEACA,0BAES,EAA4B,CAAA,EAAA,EAAA,uBAAA,AAAsB,EAC3D,WADqC,AACxB,MAAM,AAAI,MAAM,gQAAkQ,EAC/R,uEACA,6BAES,EAAoB,CAAA,EAAA,EAAA,uBAAsB,AAAtB,EAC7B,WAAa,AADgB,MACV,AAAI,MAAM,gPAAkP,EAC/Q,uEACA,qBAES,EAAyB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACxD,WAAa,AADqB,MACf,AAAI,MAAM,0PAA4P,EACzR,uEACA,0BAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EAClD,WAD4B,AACf,MAAU,AAAJ,MAAU,8OAAgP,EAC7Q,uEACA,oBAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACnD,WAD6B,AAChB,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,uEACA,qBAES,EAAwB,CAAA,EAAA,EAAA,uBAAsB,AAAtB,EACjC,WAAa,AADoB,MACd,AAAI,MAAM,wPAA0P,EACvR,uEACA,+QAlCJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAyB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACxD,WAAa,AADqB,MACf,AAAI,MAAM,0PAA4P,EACzR,mDACA,0BAES,EAA4B,GAAA,EAAA,uBAAsB,AAAtB,EACrC,WAAa,AADwB,MAClB,AAAI,MAAM,gQAAkQ,EAC/R,mDACA,6BAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACnD,WAD6B,AAChB,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,mDACA,qBAES,EAAyB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACxD,WADkC,AACrB,MAAM,AAAI,MAAM,0PAA4P,EACzR,mDACA,0BAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EAClD,WAD4B,AACf,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,mDACA,oBAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACnD,WAD6B,AAChB,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,mDACA,qBAES,EAAwB,CAAA,EAAA,EAAA,uBAAA,AAAsB,EACvD,WAAa,AADoB,MACV,AAAJ,MAAU,wPAA0P,EACvR,mDACA,sKClCJ,EAAA,CAAA,CAAA,QAiBA,EAAA,CAAA,CAAA,QACA,EAAA,CAAA,CAAA,2/GCjBO,IAAM,EAAwB,CAAA,EAAA,AADrC,EAAA,CAAA,CAAA,OACqC,uBAAA,AAAsB,EACvD,EADiC,SACpB,MAAM,AAAI,MAAM,wPAA0P,EACvR,qFACA,8GAHG,IAAM,EAAwB,CAAA,EADrC,AACqC,EADrC,CAAA,CAAA,OACqC,uBAAA,AAAsB,EACvD,EADiC,SACpB,MAAU,AAAJ,MAAU,wPAA0P,EACvR,iEACA,4MCJJ,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAMO,eAAe,EAAc,UAAE,CAAQ,CAAsB,EAGlE,GAAM,CAAC,EAAM,EAAoB,CAAG,MAAM,QAAQ,GAAG,CAAC,CACpD,CAAA,EAAA,EAAA,OAAA,AAAM,IACN,CAAA,EAAA,EAAA,gBAAe,AAAf,EAAiB,CAAE,CADnB,IACyB,EAAG,SAAU,EAAG,AAAzC,GACD,EAEK,EAAgB,EAAoB,OAAO,EAAG,EAAoB,IAAI,CAAC,aAAa,EAAI,EAAE,CAC1F,EAD6F,EAAE,AACrF,EAAoB,OAAO,EAAG,EAAoB,IAAI,CAAC,OAAO,CAE9E,EAFiF,IAG/E,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,qBAAqB,CAAA,CACpB,KAAM,EACN,OAFD,OAEgB,EACf,QAAS,EACT,SAAU,GAGhB,gFC1BO,IAAM,EAAkB,CAAA,EAD/B,AAC+B,EAD/B,CAAA,CAAA,OAC+B,uBAAA,AAAsB,EACjD,EAD2B,SACd,MAAM,AAAI,MAAM,4OAA8O,EAC3Q,2EACA,kGAHG,IAAM,EAAkB,CAAA,EAD/B,AAC+B,EAD/B,CAAA,CAAA,OAC+B,uBAAA,AAAsB,EACjD,EAD2B,SACd,MAAU,AAAJ,MAAU,4OAA8O,EAC3Q,uDACA,wLCHG,IAAM,EAAmB,CAAA,EADhC,AACgC,EADhC,CAAA,CAAA,OACgC,uBAAA,AAAsB,EAClD,EAD4B,SACf,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,0EACA,oGAHG,IAAM,EAAmB,CAAA,EAAA,AADhC,EAAA,CAAA,CAAA,OACgC,uBAAA,AAAsB,EAClD,EAD4B,SACf,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,sDACA,gNCHJ,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEO,IAAM,EAAqB,CAChC,MAAO,uBACP,YAAa,4DACf,EAEe,eAAe,IAE5B,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAElC,MACE,CAAA,EAAA,EAAA,IAAA,EAAA,EAHoB,AAGpB,QAAA,CAAA,WAEE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,SAAU,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAApB,WAAqC,CAAA,CAAA,YACpC,CAAA,EAAA,EAAA,CADmB,EACnB,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,EAAQ,MAAM,KAAtC,AAIH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,CAAC,SAAU,EAAQ,MAAM,GAGzC,AAHC,CAGD,EAAA,EAAA,GAAA,EAAC,EAAA,gBAAgB,CAAA,CAAA,KAGvB,cAHO,4JC3BP,IAAA,EAAmC,EAAA,CAA1BC,AAA0B,CAAA,QAAiG,EAAA,EAAA,CAAA,CAAA,GAAzG,KACgC,EADmC,AACX,CADhD,CACgD,CAAA,CAAA,QAWnF,EAAA,EAAA,CAAA,CAAA,GAAyE,CAXU,IAanF,EAAc,EAAA,CAAA,CAAA,IAAA,GAGd,EAAsB,EAAA,CAAbC,AAAa,CAAA,GAAT,EAAEC,GAEyD,EAFpD,AAE4E,EAAA,CAF1E,AAE0E,CAAA,QAOhG,EAAiC,EAAA,CAAA,CAAA,IAP+D,gBAchG,GAPiC,CAOjC,EAAA,CAAc,GAAA,KAA4C,KAAA,CAAA,YAAA,CAA8C,CAAtB,CAAuB,OAAA,CAAA,IAAjD,OAAiD,CAEzG,EAAA,CACA,KAAO,IAAA,CAAMG,EAAAA,KAAc,CAAA,GAAIL,CAAAA,EAAmB,cAAA,kBADU,OACV,IAChDM,KACEC,EACAG,GAAAA,CADMF,AACA,CAFI,AAEJ,EAAA,OADUC,AAEhBE,EACA,CAAA,CAAA,IAHwB,AAGxB,EAAA,AADU,EACiC,6BAAA,OAC3CC,MAAAA,CAAAA,IAAY,EAAA,wEAAA,OACZC,IAAAA,CAAAA,EAAU,EAAA,EAAA,wEAAA,OACVC,OAAU,CAAA,CAAE,GAAA,EAAA,2EAAA,GACd,aAAA,CAAA,IAAA,EAAA,qCAAA,IACAC,CACEC,CAAAA,KAAYf,GADJ,+BACIA", "ignoreList": [0, 11]}