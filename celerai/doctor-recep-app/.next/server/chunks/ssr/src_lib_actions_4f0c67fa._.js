module.exports={957782:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({generateReferralLink:()=>h,getAdminReferralStats:()=>k,getReferralInfo:()=>g,markReferralConversion:()=>j,processReferralSignup:()=>i,validateReferralCode:()=>l});var d=a.i(930768);a.i(996945);var e=a.i(729149),f=a.i(719358);async function g(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.from("doctors").select(`
        referral_code,
        total_referrals,
        successful_referrals,
        referral_discount_earned,
        referred_by
      `).eq("id",a).single();if(d||!c)return{success:!1,error:"Failed to fetch referral information"};let{count:f}=await b.from("referral_analytics").select("*",{count:"exact",head:!0}).eq("referrer_id",a).eq("status","pending"),{data:g,error:h}=await b.from("referral_analytics").select(`
        id,
        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),
        signup_date,
        conversion_date,
        status
      `).eq("referrer_id",a).order("created_at",{ascending:!1}).limit(10);if(h)return{success:!1,error:"Failed to fetch recent referrals"};let i=null;if(c.referred_by){let{data:a}=await b.from("doctors").select("name, referral_code").eq("id",c.referred_by).single();a&&(i={name:a.name,referral_code:a.referral_code||""})}let j={referral_code:c.referral_code||"",total_referrals:c.total_referrals||0,successful_referrals:c.successful_referrals||0,pending_referrals:f||0,discount_earned:c.referral_discount_earned||0,referred_by:i,recent_referrals:(g||[]).map(a=>({id:a.id,name:a.referred_doctor?.name||"Unknown",email:a.referred_doctor?.email||"Unknown",signup_date:a.signup_date,conversion_date:a.conversion_date,status:a.status}))};return{success:!0,data:j}}catch(a){return console.error("Error fetching referral info:",a),{success:!1,error:"An unexpected error occurred"}}}async function h(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.from("doctors").select("referral_code").eq("id",a).single();if(d||!c?.referral_code)return{success:!1,error:"Failed to fetch referral code"};let f=process.env.NEXT_PUBLIC_APP_URL||"https://celerai.vercel.app",g=`${f}/signup?ref=${c.referral_code}`;return{success:!0,data:g}}catch(a){return console.error("Error generating referral link:",a),{success:!1,error:"An unexpected error occurred"}}}async function i(a,b){try{let c=await (0,e.createClient)(),{data:d,error:f}=await c.from("doctors").select("id, name").eq("referral_code",a).single();if(f||!d)return{success:!1,error:"Invalid referral code"};let{error:g}=await c.from("doctors").update({referred_by:d.id}).eq("id",b);if(g)return{success:!1,error:"Failed to process referral signup"};let{error:h}=await c.from("referral_analytics").insert({referrer_id:d.id,referred_doctor_id:b,referral_code:a,status:"pending"});h&&console.error("Failed to create analytics record:",h);let{data:i}=await c.from("doctors").select("total_referrals").eq("id",d.id).single();if(i){let{error:a}=await c.from("doctors").update({total_referrals:(i.total_referrals||0)+1}).eq("id",d.id);a&&console.error("Failed to update referral count:",a)}return{success:!0,data:!0}}catch(a){return console.error("Error processing referral signup:",a),{success:!1,error:"An unexpected error occurred"}}}async function j(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.rpc("handle_referral_conversion",{referred_doctor_uuid:a});if(d)return console.error("Error marking referral conversion:",d),{success:!1,error:"Failed to process referral conversion"};return(0,f.revalidatePath)("/dashboard"),(0,f.revalidatePath)("/admin/dashboard"),{success:!0,data:c||!1}}catch(a){return console.error("Error marking referral conversion:",a),{success:!1,error:"An unexpected error occurred"}}}async function k(){try{let a=await (0,e.createClient)(),{data:b,error:c}=await a.from("referral_analytics").select("status, discount_earned");if(c)return{success:!1,error:"Failed to fetch referral statistics"};let d=b?.length||0,f=b?.filter(a=>"converted"===a.status).length||0,g=b?.filter(a=>"pending"===a.status).length||0,h=b?.reduce((a,b)=>a+(b.discount_earned||0),0)||0,{data:i,error:j}=await a.from("doctors").select("id, name, referral_code, successful_referrals, referral_discount_earned").gt("successful_referrals",0).order("successful_referrals",{ascending:!1}).limit(10);if(j)return{success:!1,error:"Failed to fetch top referrers"};return{success:!0,data:{total_referrals:d,successful_conversions:f,pending_referrals:g,total_discount_earned:h,top_referrers:(i||[]).map(a=>({id:a.id,name:a.name,referral_code:a.referral_code||"",successful_referrals:a.successful_referrals||0,discount_earned:a.referral_discount_earned||0}))}}}catch(a){return console.error("Error fetching admin referral stats:",a),{success:!1,error:"An unexpected error occurred"}}}async function l(a){try{let b=await (0,e.createClient)(),{data:c,error:d}=await b.from("doctors").select("name, approved").eq("referral_code",a).eq("approved",!0).single();if(d||!c)return{success:!0,data:{valid:!1}};return{success:!0,data:{valid:!0,referrer_name:c.name}}}catch(a){return console.error("Error validating referral code:",a),{success:!1,error:"An unexpected error occurred"}}}(0,a.i(377991).ensureServerEntryExports)([g,h,i,j,k,l]),(0,d.registerServerReference)(g,"40757964b8c9b072995f44631743e71306c1784480",null),(0,d.registerServerReference)(h,"400d27483b0ad440768404c34690724d71663b6083",null),(0,d.registerServerReference)(i,"606bc295d7adf166de394559341675608634558f4e",null),(0,d.registerServerReference)(j,"407564c58b76eea97b9678f27ef8f98ef2a04ae6eb",null),(0,d.registerServerReference)(k,"00ed563c8fb8c346efaec610c0d2a954999d90622b",null),(0,d.registerServerReference)(l,"4072b74acb2fae150f3d07efd9bac13f2f423a1a31",null)},230122:a=>{"use strict";var{g:b,__dirname:c}=a;a.s({changePassword:()=>n,login:()=>l,logout:()=>m,signup:()=>k});var d=a.i(930768);a.i(996945),a.i(622427);var e=a.i(766719),f=a.i(437713),g=a.i(729149),h=a.i(137496),i=a.i(635512),j=a.i(957782);async function k(a,b){let c=i.SignupFormSchema.safeParse({name:b.get("name"),email:b.get("email"),password:b.get("password"),clinic_name:b.get("clinic_name"),phone:b.get("phone")}),d=b.get("referral_code");if(!c.success)return{success:!1,message:"Invalid form fields.",fieldErrors:c.error.flatten().fieldErrors};let{name:k,email:l,password:m,clinic_name:n,phone:o}=c.data,p=await f.default.hash(m,10);try{let a=await (0,g.createClient)(),{data:b}=await a.from("doctors").select("id").eq("email",l).single();if(b)return{success:!1,message:"An account with this email already exists."};if(o){let{data:b}=await a.from("doctors").select("id").eq("phone",o).single();if(b)return{success:!1,message:"An account with this phone number already exists."}}let{data:c,error:e}=await a.from("doctors").insert({name:k,email:l,password_hash:p,clinic_name:n,phone:o,approved:!1,monthly_quota:50}).select("id, approved").single();if(e)return console.error("Database error:",e),{success:!1,message:"An error occurred while creating your account."};if(!c)return{success:!1,message:"An error occurred while creating your account."};if(d&&await (0,j.processReferralSignup)(d,c.id),!c.approved)return{success:!0,message:"Account created successfully! Please wait for admin approval before you can log in."};await (0,h.createSession)(c.id)}catch(a){return console.error("Signup error:",a),{success:!1,message:"An unexpected error occurred."}}return(0,e.redirect)("/dashboard"),{success:!0,message:"Redirecting..."}}async function l(a,b){let c=i.LoginFormSchema.safeParse({email:b.get("email"),password:b.get("password")});if(!c.success)return{success:!1,message:"Invalid form fields.",fieldErrors:c.error.flatten().fieldErrors};let{email:d,password:j}=c.data;try{let a=await (0,g.createClient)(),{data:b,error:c}=await a.from("doctors").select("id, password_hash, approved").eq("email",d).single();if(c||!b||!await f.default.compare(j,b.password_hash))return{success:!1,message:"Invalid email or password."};if(!b.approved)return{success:!1,message:"Your account is pending admin approval. Please wait for approval before logging in."};await (0,h.createSession)(b.id)}catch(a){return console.error("Login error:",a),{success:!1,message:"An unexpected error occurred."}}return(0,e.redirect)("/dashboard"),{success:!0,message:"Redirecting..."}}async function m(){await (0,h.deleteSession)(),(0,e.redirect)("/login")}async function n(a,b){let c=b.get("currentPassword"),d=b.get("newPassword"),e=b.get("confirmPassword");if(!c||!d||!e)return{success:!1,message:"All fields are required."};if(d!==e)return{success:!1,message:"New passwords do not match."};if(d.length<8)return{success:!1,message:"New password must be at least 8 characters long."};try{let b=await (0,g.createClient)(),{data:e,error:i}=await b.from("doctors").select("password_hash").eq("id",a).single();if(i||!e)return{success:!1,message:"Doctor not found."};if(!await f.default.compare(c,e.password_hash))return{success:!1,message:"Current password is incorrect."};let j=await f.default.hash(d,10),{error:k}=await b.from("doctors").update({password_hash:j}).eq("id",a);if(k)return console.error("Password update error:",k),{success:!1,message:"Failed to update password."};return await (0,h.refreshSession)(a),{success:!0,message:"Password changed successfully!"}}catch(a){return console.error("Password change error:",a),{success:!1,message:"An unexpected error occurred."}}}(0,a.i(377991).ensureServerEntryExports)([k,l,m,n]),(0,d.registerServerReference)(k,"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d",null),(0,d.registerServerReference)(l,"6064c1bc78024112540d1fec516d46f480c1310eb6",null),(0,d.registerServerReference)(m,"008a705899f288d02ab102cb7194752ef57be976ca",null),(0,d.registerServerReference)(n,"608ed2498189cfd7c9da6c6017df4df3d3e1da70c2",null)}};

//# sourceMappingURL=src_lib_actions_4f0c67fa._.js.map