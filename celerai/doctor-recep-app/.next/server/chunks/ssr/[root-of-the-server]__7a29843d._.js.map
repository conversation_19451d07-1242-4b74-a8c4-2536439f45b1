{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/src/lib/auth/admin-session.ts", "turbopack:///[project]/src/lib/actions/admin-auth.ts", "turbopack:///[project]/.next-internal/server/app/admin/login/page/actions.js (server actions loader)", "turbopack:///[project]/src/components/admin/admin-login-form.tsx/proxy.mjs", "turbopack:///[project]/src/app/admin/login/page.tsx", "turbopack:///[project]/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "import 'server-only'\nimport { SignJWT, jwtVerify } from 'jose'\nimport { cookies } from 'next/headers'\n\nexport interface AdminSessionPayload {\n  adminId: string;\n  role: 'admin' | 'super_admin';\n  expiresAt: Date;\n}\n\nconst secretKey = process.env.SESSION_SECRET\nconst encodedKey = new TextEncoder().encode(secretKey)\n\nexport async function encryptAdminSession(payload: AdminSessionPayload) {\n  // Fix: Cast payload to Record<string, unknown> for SignJWT\n  return new SignJWT(payload as unknown as Record<string, unknown>)\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('7d')\n    .sign(encodedKey)\n}\n\nexport async function decryptAdminSession(session: string | undefined = '') {\n  try {\n    if (!session) {\n      return null\n    }\n\n    const { payload } = await jwtVerify(session, encodedKey, {\n      algorithms: ['HS256'],\n    })\n    // Fix: cast to unknown first, then to AdminSessionPayload for type safety\n    return payload as unknown as AdminSessionPayload\n  } catch {\n    console.log('Failed to verify admin session')\n    return null\n  }\n}\n\nexport async function createAdminSession(adminId: string, role: 'admin' | 'super_admin' = 'admin') {\n  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n  const session = await encryptAdminSession({ adminId, role, expiresAt })\n  const cookieStore = await cookies()\n\n  console.log('DEBUG: Creating admin session for admin:', adminId, 'role:', role)\n  console.log('DEBUG: Admin session expires at:', expiresAt)\n\n  cookieStore.set('admin_session', session, {\n    httpOnly: true,\n    secure: false, // Always false for debugging\n    expires: expiresAt,\n    sameSite: 'lax',\n    path: '/',\n  })\n  \n  console.log('DEBUG: Admin session cookie set successfully')\n}\n\nexport async function updateAdminSession() {\n  const cookieStore = await cookies()\n  const session = cookieStore.get('admin_session')?.value\n  const payload = await decryptAdminSession(session)\n\n  if (!session || !payload) {\n    return null\n  }\n\n  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n\n  cookieStore.set('admin_session', session, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    expires: expires,\n    sameSite: 'lax',\n    path: '/',\n  })\n}\n\nexport async function deleteAdminSession() {\n  const cookieStore = await cookies()\n  console.log('DEBUG: Deleting admin session cookie')\n  cookieStore.delete('admin_session')\n  console.log('DEBUG: Admin session cookie deleted')\n}\n\n// For backward compatibility with regular session functions\nexport { deleteAdminSession as deleteSession }\n", "'use server'\n\nimport bcrypt from 'bcryptjs'\nimport { createClient } from '@/lib/supabase/server'\n// Ensure createAdminSession is used if it's imported\nimport { createAdminSession, deleteSession } from '@/lib/auth/admin-session'\nimport { AdminLoginFormSchema } from '@/lib/validations'\nimport { FormState } from '@/lib/types'\n\nexport async function adminLogin(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = AdminLoginFormSchema.safeParse({\n    email: formData.get('email'),\n    password: formData.get('password'),\n  })\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      success: false,\n      message: 'Invalid form fields.',\n    }\n  }\n\n  const { email, password } = validatedFields.data\n\n  try {\n    const supabase = await createClient()\n\n    // Get admin from database\n    const { data: admin /* , error */ } = await supabase\n      .from('admins')\n      .select('id, password_hash, role') // 'role' is selected here, which we need\n      .eq('email', email)\n      .single()\n\n    if (!admin) {\n      return {\n        success: false,\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, admin.password_hash)\n    if (!isValidPassword) {\n      return {\n        success: false,\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // *** CRITICAL CHANGE: Pass the 'role' argument to createAdminSession ***\n    // Ensure admin.role is correctly typed as 'admin' | 'super_admin'\n    await createAdminSession(admin.id, admin.role as \"admin\" | \"super_admin\");\n\n    // Return success instead of redirecting (to avoid Next.js 15 grey screen issue)\n    return {\n      success: true,\n      message: 'Login successful! Redirecting...',\n    }\n\n  } catch (error) {\n    console.error(\"Admin Login Error:\", error);\n    return {\n      success: false,\n      message: 'An unexpected error occurred.',\n    }\n  }\n}\n\nexport async function adminLogout() {\n  await deleteSession()\n  // Return success instead of redirecting (to avoid Next.js 15 grey screen issue)\n  return { success: true }\n}\n\nexport async function createAdminUser(\n  email: string,\n  password: string,\n  name: string,\n  role: 'admin' | 'super_admin' = 'admin'\n): Promise<{ success: boolean; error?: string; adminId?: string }> {\n  try {\n    const supabase = await createClient()\n\n    // Check if admin already exists\n    const { data: existingAdmin } = await supabase\n      .from('admins')\n      .select('id')\n      .eq('email', email)\n      .single()\n\n    if (existingAdmin) {\n      return {\n        success: false,\n        error: 'An admin with this email already exists.',\n      }\n    }\n\n    // Hash password\n    const hashedPassword = await bcrypt.hash(password, 10)\n\n    // Insert the admin into the database\n    const { data: admin, error } = await supabase\n      .from('admins')\n      .insert({\n        name,\n        email,\n        password_hash: hashedPassword,\n        role,\n      })\n      .select('id')\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return {\n        success: false,\n        error: 'An error occurred while creating the admin account.',\n      }\n    }\n\n    if (!admin) {\n      return {\n        success: false,\n        error: 'An error occurred while creating the admin account.',\n      }\n    }\n\n    return {\n      success: true,\n      adminId: admin.id,\n    }\n  } catch (error) {\n    console.error('Create admin error:', error)\n    return {\n      success: false,\n      error: 'An unexpected error occurred.',\n    }\n  }\n}", "export {adminLogin as '60e96432ee93dbc1f2cacf69805b78047ca4cf9f56'} from 'ACTIONS_MODULE0'\n", "import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminLoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminLoginForm() from the server but AdminLoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/admin-login-form.tsx <module evaluation>\",\n    \"AdminLoginForm\",\n);\n", "import { Metadata } from 'next'\nimport { AdminLoginForm } from '@/components/admin/admin-login-form'\n\nexport const metadata: Metadata = {\n  title: 'Admin Login - Celer AI',\n  description: 'Admin login for Celer AI',\n}\n\nexport default function AdminLoginPage() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100\">\n            <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n            </svg>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Admin Access\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Sign in to the admin dashboard\n          </p>\n        </div>\n        \n        <AdminLoginForm />\n      </div>\n    </div>\n  )\n}\n", "import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "module", "exports", "require", "AppPageRouteModule", "tree", "pages", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "RouteKind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": "y3BA0BQM,GAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,2WC1BjC,EAAA,CAAA,CAAA,QACA,IAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAQA,IAAM,EAAY,QAAQ,GAAG,CAAC,cAAc,CACtC,EAAa,IAAI,cAAc,MAAM,CAAC,GAErC,eAAe,EAAoB,CAA4B,EAEpE,OAAO,IAAI,EAAA,OAAO,CAAC,GAChB,kBAAkB,CAAC,CAAE,IAAK,EADlB,KAC0B,GAClC,WAAW,GACX,iBAAiB,CAAC,MAClB,IAAI,CAAC,EACV,CAEO,eAAe,EAAoB,EAA8B,EAAE,EACxE,GAAI,CACF,GAAI,CAAC,EACH,OADY,AACL,KAGT,GAAM,SAAE,CAAO,CAAE,CAAG,MAAM,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,EAAS,EAAY,CACvD,WAAY,CAAC,QACf,AADuB,CADG,EAI1B,OAAO,CACT,CAAE,KAAM,CAEN,OADA,QAAQ,GAAG,CAAC,kCACL,IACT,CACF,CAEO,eAAe,EAAmB,CAAe,CAAE,EAAgC,OAAO,EAC/F,IAAM,EAAY,IAAI,KAAK,KAAK,GAAG,GAAK,IAAI,IACtC,CAD2C,CACjC,IADsC,EAChC,EAAoB,CADiB,QACf,OAAS,YAAM,CAAU,GAC/D,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,IAEhC,QAAQ,GAAG,CAAC,cAFc,6BAE8B,EAAS,QAAS,GAC1E,QAAQ,GAAG,CAAC,mCAAoC,GAEhD,EAAY,GAAG,CAAC,gBAAiB,EAAS,CACxC,UAAU,EACV,QAAQ,EACR,QAAS,EACT,SAAU,MACV,KAAM,GACR,GAEA,QAAQ,GAAG,CAAC,+CACd,CAEO,eAAe,IACpB,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAM,AAAN,IACpB,EAAU,EAAY,GAAG,CAAC,kBAAkB,AADxB,MAEpB,EAAU,MAAM,EAAoB,GAE1C,GAAI,CAAC,GAAW,CAAC,EACf,OADwB,AACjB,KAGT,IAAM,EAAU,IAAI,KAAK,KAAK,GAAG,GAAK,IAAI,IAE1C,CAF+C,CAEnC,GAAG,CAFqC,AAEpC,KAFyC,WAExB,EAAS,CACxC,UAAU,EACV,QAAQ,EACR,QAAS,EACT,SAAU,MACV,KAAM,GACR,EACF,CAEO,aAP8B,EAOf,IACpB,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAM,IAChC,QAAQ,GAAG,CAAC,cADc,0BAE1B,EAAY,MAAM,CAAC,iBACnB,QAAQ,GAAG,CAAC,sCACd,iJCjFA,IAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAGO,eAAe,EAAW,CAAgB,CAAE,CAAkB,EAEnE,IAAM,EAAkB,EAAA,oBAAoB,CAAC,SAAS,CAAC,CACrD,KADsB,CACf,EAAS,GAAG,CAAC,SACpB,SAAU,EAAS,GAAG,CAAC,WACzB,GAGA,GAAI,CAAC,EAAgB,OAAO,CAC1B,CAD4B,KACrB,CACL,SAAS,EACT,QAAS,sBACX,EAGF,GAAM,OAAE,CAAK,CAAE,UAAQ,CAAE,CAAG,EAAgB,IAAI,CAEhD,GAAI,CACF,IAAM,EAAW,MAAM,GAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAA,CAAM,AAAe,CAAG,MAAM,EACzC,EAD4B,EACxB,AAJgB,CAGJ,AACX,UACL,MAAM,CAAC,2BAA2B,AAClC,EAAE,CAAC,QAAS,GACZ,MAAM,GAET,GAAI,CAAC,GASD,CADoB,AACnB,GATO,GAQkB,EAAA,EAZgD,KAY1C,CAAC,CACf,MADsB,CAAC,EAAU,EAAM,aAAa,EAPxE,EAO4B,IAPrB,CACL,SAAS,EACT,QAAS,4BACX,EAiBF,OAHA,MAAM,CAAA,EAAA,EAAA,kBAAA,AAAiB,EAAE,EAAM,EAAE,CAAE,EAAM,IAAI,EAGtC,CACL,GAJI,MAIK,EACT,QAAS,kCACX,CAEF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qBAAsB,GAC7B,CACL,QAAS,GACT,QAAS,+BACX,CACF,CACF,CAEO,eAAe,IAGpB,OAFA,MAAM,CAAA,EAAA,EAAA,aAAA,AAAY,IAEX,CAAE,SAAS,CAAK,CACzB,CAEO,OALC,QAKc,EACpB,CAAa,CACb,CAAgB,CAChB,CAAY,CACZ,EAAgC,OAAO,EAEvC,GAAI,CACF,IAAM,EAAW,MAAM,GAAA,EAAA,YAAA,AAAW,IAG5B,CAAE,KAAM,CAAa,CAAE,CAAG,MAAM,EACnC,IAJoB,AAIhB,CAAC,UACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAS,GACZ,MAAM,GAET,GAAI,EACF,MAAO,CACL,MAFe,GAEN,EACT,MAAO,0CACT,EAIF,IAAM,EAAiB,MAAM,EAAA,OAAM,CAAC,IAAI,CAAC,EAAU,IAG7C,CAAE,KAAM,CAAK,OAAE,CAAK,CAAE,CAAG,CAHF,KAGQ,EAClC,IAAI,CAAC,UACL,MAAM,CAAC,MACN,QACA,EACA,cAAe,OACf,CACF,GACC,MAAM,CAAC,MACP,MAAM,GAET,GAAI,EAEF,KAFS,EACT,QAAQ,KAAK,CAAC,kBAAmB,GAC1B,CACL,SAAS,EACT,MAAO,qDACT,EAGF,GAAI,CAAC,EACH,KADU,CACH,CACL,SAAS,EACT,MAAO,qDACT,EAGF,MAAO,CACL,SAAS,EACT,QAAS,EAAM,EAAE,AACnB,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sBAAuB,GAC9B,CACL,SAAS,EACT,MAAO,+BACT,CACF,CACF,2CApIsB,EA8DA,EAMA,IApEA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MA8DA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,MAMA,CAAA,EAAA,EAAA,uBAAA,EAAA,EAAA,6CAAA,8DC7EtB,EAAA,CAAA,CAAA,8eCCO,IAAM,EAAiB,CAAA,EAD9B,AAC8B,EAD9B,CAAA,CAAA,OAC8B,uBAAA,AAAsB,EAChD,EAD0B,SACb,MAAM,AAAI,MAAM,0OAA4O,EACzQ,0EACA,gGAHG,IAAM,EAAiB,GAD9B,AAC8B,EAD9B,CAAA,CAAA,OAC8B,uBAAA,AAAsB,EAChD,EAD0B,SACb,MAAM,AAAI,MAAM,0OAA4O,EACzQ,sDACA,8MCHJ,EAAA,EAAA,CAAA,CAAA,OAEO,IAAM,EAAqB,CAChC,MAAO,yBACP,YAAa,0BACf,EAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+FACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBAAuB,KAAK,OAAO,QAAQ,YAAY,OAAO,wBAC3E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6GAGzE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,kEAAyD,iBAGvE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,kDAAyC,sCAKxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,cAAc,CAAA,CAAA,OAIvB,aAJS,8JCzBT,IAAA,EAAmC,EAAA,CAA1BC,AAA0B,CAAA,QAAiG,EAAA,EAAA,CAAA,CAAA,GAAzG,KACgC,EADmC,AACX,CADhD,CACgD,CAAA,CAAA,QAWnF,EAAA,EAAA,CAAA,CAAA,GAAyE,CAXU,IAanF,EAAc,EAAA,CAAA,CAAA,IAAA,GAGd,EAAsB,EAAA,CAAbC,AAAa,CAAA,GAAT,EAAEC,GAEyD,EAFpD,AAE4E,EAAA,CAAA,AAF1E,CAE0E,QAOhG,EAAiC,EAAA,CAAA,CAAA,IAP+D,gBAchG,GAPiC,CAOjC,EAAA,CAAc,GAAA,KAA4C,KAAA,CAAA,QAAA,CAA8C,EAAC,GAAvB,IAAuB,CAAA,QAAjD,AAAiD,CAEzG,SAAA,CAAA,WAAA,CACA,EAAA,GACEI,CADK,MAAMD,AACXC,CAAAA,CAAY,CAAA,KAAA,CAAA,IAAA,CADa,CACb,GADiBN,IAD6B,eACV,mBACpC,QACVO,CACAG,EACAC,GAFMH,AAENG,CADM,AACNA,CAAAA,CAAU,CAAA,MAFMF,CAGhB,EACAG,GAAAA,CAAAA,CAJwB,AAIxBA,EAAAA,CAAY,MACZC,EACAC,CAAAA,CAAAA,IAAU,CAAE,AAAF,CAAE,AADF,iBAFiC,cAG/B,GACd,UAAA,CAAA,IAAA,EAAA,wEAAA,GACAC,QAAU,CAAA,CAAA,GAAA,EAAA,wEAAA,OACRC,OAAAA,CAAAA,CAAYf,GAAAA,EAAAA,2EAAAA,GACd,aAAA,CAAA,IAAA,EAAA,qCAAA,AACF,EAAA,CAAE", "ignoreList": [0, 6]}