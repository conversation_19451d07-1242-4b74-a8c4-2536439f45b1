const CHUNK_PUBLIC_PATH = "server/instrumentation.js";
const runtime = require("./chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__931d3b50._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_core_build_cjs_e12e5408._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_node_build_cjs_5f216122._.js");
runtime.loadChunk("server/chunks/996e2_@opentelemetry_core_build_esm_d462fceb._.js");
runtime.loadChunk("server/chunks/fb235_@opentelemetry_core_build_esm_b0d37f7b._.js");
runtime.loadChunk("server/chunks/1235f_@opentelemetry_core_build_esm_7bd7be5e._.js");
runtime.loadChunk("server/chunks/5325e_@opentelemetry_core_build_esm_c5c35f40._.js");
runtime.loadChunk("server/chunks/79d4c_@opentelemetry_core_build_esm_bdb21369._.js");
runtime.loadChunk("server/chunks/453a2_@opentelemetry_core_build_esm_73263a33._.js");
runtime.loadChunk("server/chunks/62fd2_@opentelemetry_core_build_esm_ef2b3cd5._.js");
runtime.loadChunk("server/chunks/0c0fe_@opentelemetry_core_build_esm_39ccb7a0._.js");
runtime.loadChunk("server/chunks/5c7f3_@opentelemetry_core_build_esm_da97c9b9._.js");
runtime.loadChunk("server/chunks/8259c_@opentelemetry_core_build_esm_0a7902af._.js");
runtime.loadChunk("server/chunks/7c130_@opentelemetry_core_build_esm_840f6264._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_core_build_esm_bf0d0cfb._.js");
runtime.loadChunk("server/chunks/00340_@opentelemetry_core_build_esm_f87e76bf._.js");
runtime.loadChunk("server/chunks/996e2_@opentelemetry_semantic-conventions_build_esm_77d90b1f._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_semantic-conventions_build_esm_05b3d850._.js");
runtime.loadChunk("server/chunks/5325e_@opentelemetry_semantic-conventions_build_esm_dea679fe._.js");
runtime.loadChunk("server/chunks/79d4c_@opentelemetry_semantic-conventions_build_esm_417db5bb._.js");
runtime.loadChunk("server/chunks/62fd2_@opentelemetry_semantic-conventions_build_esm_132d9e06._.js");
runtime.loadChunk("server/chunks/0c0fe_@opentelemetry_semantic-conventions_build_esm_086f343f._.js");
runtime.loadChunk("server/chunks/5c7f3_@opentelemetry_semantic-conventions_build_esm_ed94a12f._.js");
runtime.loadChunk("server/chunks/8259c_@opentelemetry_semantic-conventions_build_esm_d551d894._.js");
runtime.loadChunk("server/chunks/7c130_@opentelemetry_semantic-conventions_build_esm_21996f33._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_1ac6eb5c._.js");
runtime.loadChunk("server/chunks/00340_@opentelemetry_a3d2cdc5._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_f5699f32._.js");
runtime.loadChunk("server/chunks/node_modules_3b46a202._.js");
runtime.loadChunk("server/chunks/node_modules_05072163._.js");
runtime.loadChunk("server/chunks/node_modules_a7054c35._.js");
runtime.loadChunk("server/chunks/node_modules_c75af394._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_nextjs_build_cjs_fe776d43._.js");
runtime.loadChunk("server/chunks/node_modules_ed66e11c._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__50565227._.js");
runtime.getOrInstantiateRuntimeModule("[project]/src/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/src/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
