{"version": 3, "sources": ["turbopack:///[project]/node_modules/@sentry/core/src/utils/version.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/worldwide.ts", "turbopack:///[project]/node_modules/@sentry/core/src/carrier.ts", "turbopack:///[project]/node_modules/@sentry/core/src/debug-build.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/is.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/browser.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/logger.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/string.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/object.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/misc.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/time.ts", "turbopack:///[project]/node_modules/@sentry/core/src/session.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/merge.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/propagationContext.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/spanOnScope.ts", "turbopack:///[project]/node_modules/@sentry/core/src/scope.ts", "turbopack:///[project]/node_modules/@sentry/core/src/defaultScopes.ts", "turbopack:///[project]/node_modules/@sentry/core/src/asyncContext/stackStrategy.ts", "turbopack:///[project]/node_modules/@sentry/core/src/asyncContext/index.ts", "turbopack:///[project]/node_modules/@sentry/core/src/currentScopes.ts", "turbopack:///[project]/node_modules/@sentry/core/src/constants.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/syncpromise.ts", "turbopack:///[project]/node_modules/@sentry/core/src/eventProcessors.ts", "turbopack:///[project]/node_modules/@sentry/core/src/semanticAttributes.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/baggage.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/dsn.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/hasSpansEnabled.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/spanstatus.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/utils.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/parseSampleRate.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/tracing.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/spanUtils.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/dynamicSamplingContext.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/applyScopeDataToEvent.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/debug-ids.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/stacktrace.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/normalize.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/prepareEvent.ts", "turbopack:///[project]/node_modules/@sentry/core/src/exports.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/vercelWaitUntil.ts", "turbopack:///[project]/node_modules/@sentry/nextjs/src/common/debug-build.ts", "turbopack:///[project]/node_modules/@sentry/nextjs/src/common/utils/responseEnd.ts"], "sourcesContent": ["// This is a magic string replaced by rollup\ndeclare const __SENTRY_SDK_VERSION__: string;\n\nexport const SDK_VERSION = typeof __SENTRY_SDK_VERSION__ === 'string' ? __SENTRY_SDK_VERSION__ : '0.0.0-unknown.0';\n", "/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Carrier } from '../carrier';\nimport type { Client } from '../client';\nimport type { SerializedLog } from '../types-hoist/log';\nimport type { Span } from '../types-hoist/span';\nimport type { SdkSource } from './env';\n\n/** Internal global with common properties and Sentry extensions  */\nexport type InternalGlobal = {\n  navigator?: { userAgent?: string; maxTouchPoints?: number };\n  console: Console;\n  PerformanceObserver?: any;\n  Sentry?: any;\n  onerror?: {\n    (event: object | string, source?: string, lineno?: number, colno?: number, error?: Error): any;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * A map of Sentry clients to their log buffers.\n   *\n   * This is used to store logs that are sent to Sentry.\n   */\n  _sentryClientToLogBufferMap?: WeakMap<Client, Array<SerializedLog>>;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n  _sentryEsmLoaderHookRegistered?: boolean;\n  /**\n   * A map of spans to evaluated feature flags. Populated by feature flag integrations.\n   */\n  _spanToFlagBufferMap?: WeakMap<Span, Set<string>>;\n} & Carrier;\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ = globalThis as unknown as InternalGlobal;\n", "import type { AsyncContextStack } from './asyncContext/stackStrategy';\nimport type { AsyncContextStrategy } from './asyncContext/types';\nimport type { Scope } from './scope';\nimport type { Logger } from './utils/logger';\nimport { SDK_VERSION } from './utils/version';\nimport { GLOBAL_OBJ } from './utils/worldwide';\n\n/**\n * An object that contains globally accessible properties and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: VersionedCarrier;\n}\n\ntype VersionedCarrier = {\n  version?: string;\n} & Record<Exclude<string, 'version'>, SentryCarrier>;\n\nexport interface SentryCarrier {\n  acs?: AsyncContextStrategy;\n  stack?: AsyncContextStack;\n\n  globalScope?: Scope;\n  defaultIsolationScope?: Scope;\n  defaultCurrentScope?: Scope;\n  logger?: Logger;\n\n  /** Overwrites TextEncoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  encodePolyfill?: (input: string) => Uint8Array;\n  /** Overwrites TextDecoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  decodePolyfill?: (input: Uint8Array) => string;\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  // This ensures a Sentry carrier exists\n  getSentryCarrier(GLOBAL_OBJ);\n  return GLOBAL_OBJ;\n}\n\n/** Will either get the existing sentry carrier, or create a new one. */\nexport function getSentryCarrier(carrier: Carrier): SentryCarrier {\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n\n  // For now: First SDK that sets the .version property wins\n  __SENTRY__.version = __SENTRY__.version || SDK_VERSION;\n\n  // Intentionally populating and returning the version of \"this\" SDK instance\n  // rather than what's set in .version so that \"this\" SDK always gets its carrier\n  return (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__[]` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<Prop extends keyof SentryCarrier>(\n  name: Prop,\n  creator: () => NonNullable<SentryCarrier[Prop]>,\n  obj = GLOBAL_OBJ,\n): NonNullable<SentryCarrier[Prop]> {\n  const __SENTRY__ = (obj.__SENTRY__ = obj.__SENTRY__ || {});\n  const carrier = (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n  // Note: We do not want to set `carrier.version` here, as this may be called before any `init` is called, e.g. for the default scopes\n  return carrier[name] || (carrier[name] = creator());\n}\n", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Primitive } from '../types-hoist/misc';\nimport type { ParameterizedString } from '../types-hoist/parameterize';\nimport type { PolymorphicEvent } from '../types-hoist/polymorphics';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: unknown): wat is Error {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat: unknown, className: string): boolean {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: unknown): boolean {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: unknown): wat is string {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given string is parameterized\n * {@link isParameterizedString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isParameterizedString(wat: unknown): wat is ParameterizedString {\n  return (\n    typeof wat === 'object' &&\n    wat !== null &&\n    '__sentry_template_string__' in wat &&\n    '__sentry_template_values__' in wat\n  );\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: unknown): wat is Primitive {\n  return wat === null || isParameterizedString(wat) || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal, or a class instance.\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: unknown): wat is Record<string, unknown> {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: unknown): wat is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: unknown): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: unknown): wat is RegExp {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): wat is PromiseLike<any> {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat?.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: unknown): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n\ninterface VueViewModel {\n  // Vue3\n  __isVue?: boolean;\n  // Vue2\n  _isVue?: boolean;\n}\n/**\n * Checks whether given value's type is a Vue ViewModel.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isVueViewModel(wat: unknown): boolean {\n  // Not using Object.prototype.toString because in Vue 3 it would read the instance's Symbol(Symbol.toStringTag) property.\n  return !!(typeof wat === 'object' && wat !== null && ((wat as VueViewModel).__isVue || (wat as VueViewModel)._isVue));\n}\n\n/**\n * Checks whether the given parameter is a Standard Web API Request instance.\n *\n * Returns false if Request is not available in the current runtime.\n */\nexport function isRequest(request: unknown): request is Request {\n  return typeof Request !== 'undefined' && isInstanceOf(request, Request);\n}\n", "import { isString } from './is';\nimport { GLOBAL_OBJ } from './worldwide';\n\nconst WINDOW = GLOBAL_OBJ as unknown as Window;\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\ntype SimpleNode = {\n  parentNode: SimpleNode;\n} | null;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(\n  elem: unknown,\n  options: string[] | { keyAttrs?: string[]; maxStringLength?: number } = {},\n): string {\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n\n  if (!elem?.tagName) {\n    return '';\n  }\n\n  // @ts-expect-error WINDOW has HTMLElement\n  if (WINDOW.HTMLElement) {\n    // If using the component name annotation plugin, this value may be available on the DOM node\n    if (elem instanceof HTMLElement && elem.dataset) {\n      if (elem.dataset['sentryComponent']) {\n        return elem.dataset['sentryComponent'];\n      }\n      if (elem.dataset['sentryElement']) {\n        return elem.dataset['sentryElement'];\n      }\n    }\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs = keyAttrs?.length\n    ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n    : null;\n\n  if (keyAttrPairs?.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    const className = elem.className;\n    if (className && isString(className)) {\n      const classes = className.split(/\\s+/);\n      for (const c of classes) {\n        out.push(`.${c}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (const k of allowedAttrs) {\n    const attr = elem.getAttribute(k);\n    if (attr) {\n      out.push(`[${k}=\"${attr}\"]`);\n    }\n  }\n\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Given a DOM element, traverses up the tree until it finds the first ancestor node\n * that has the `data-sentry-component` or `data-sentry-element` attribute with `data-sentry-component` taking\n * precedence. This attribute is added at build-time by projects that have the component name annotation plugin installed.\n *\n * @returns a string representation of the component for the provided DOM element, or `null` if not found\n */\nexport function getComponentName(elem: unknown): string | null {\n  // @ts-expect-error WINDOW has HTMLElement\n  if (!WINDOW.HTMLElement) {\n    return null;\n  }\n\n  let currentElem = elem as SimpleNode;\n  const MAX_TRAVERSE_HEIGHT = 5;\n  for (let i = 0; i < MAX_TRAVERSE_HEIGHT; i++) {\n    if (!currentElem) {\n      return null;\n    }\n\n    if (currentElem instanceof HTMLElement) {\n      if (currentElem.dataset['sentryComponent']) {\n        return currentElem.dataset['sentryComponent'];\n      }\n      if (currentElem.dataset['sentryElement']) {\n        return currentElem.dataset['sentryElement'];\n      }\n    }\n\n    currentElem = currentElem.parentNode;\n  }\n\n  return null;\n}\n", "import { getGlobalSingleton } from '../carrier';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { ConsoleLevel } from '../types-hoist/instrument';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS: readonly ConsoleLevel[] = [\n  'debug',\n  'info',\n  'warn',\n  'error',\n  'log',\n  'assert',\n  'trace',\n] as const;\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<ConsoleLevel, LoggerMethod>;\n\n/** This may be mutated by the console instrumentation. */\nexport const originalConsoleMethods: {\n  [key in ConsoleLevel]?: (...args: unknown[]) => void;\n} = {};\n\n/** A Sentry Logger instance. */\nexport interface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n  isEnabled(): boolean;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console as Console;\n  const wrappedFuncs: Partial<LoggerConsoleMethods> = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) as ConsoleLevel[];\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] as LoggerMethod;\n    wrappedFuncs[level] = console[level] as LoggerMethod | undefined;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] as LoggerMethod;\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (DEBUG_BUILD) {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = (...args: Parameters<(typeof GLOBAL_OBJ.console)[typeof name]>) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\n/**\n * This is a logger singleton which either logs things or no-ops if logging is not enabled.\n * The logger is a singleton on the carrier, to ensure that a consistent logger is used throughout the SDK.\n */\nexport const logger = getGlobalSingleton('logger', makeLogger);\n", "import { isRegExp, isString, isVueViewModel } from './is';\n\nexport { escapeStringForRegex } from '../vendor/escapeStringForRegex';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\nexport function safeJoin(input: unknown[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      // This is a hack to fix a Vue3-specific bug that causes an infinite loop of\n      // console warnings. This happens when a Vue template is rendered with\n      // an undeclared variable, which we try to stringify, ultimately causing\n      // Vue to issue another warning which repeats indefinitely.\n      // see: https://github.com/getsentry/sentry-javascript/pull/8981\n      if (isVueViewModel(value)) {\n        output.push('[VueViewModel]');\n      } else {\n        output.push(String(value));\n      }\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nexport function isMatchingPattern(\n  value: string,\n  pattern: RegExp | string,\n  requireExactStringMatch: boolean = false,\n): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nexport function stringMatchesSomePattern(\n  testString: string,\n  patterns: Array<string | RegExp> = [],\n  requireExactStringMatch: boolean = false,\n): boolean {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { WrappedFunction } from '../types-hoist/wrappedfunction';\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPrimitive } from './is';\nimport { logger } from './logger';\nimport { truncate } from './string';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * If the method on the passed object is not a function, the wrapper will not be applied.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  // explicitly casting to unknown because we don't know the type of the method initially at all\n  const original = source[name] as unknown;\n\n  if (typeof original !== 'function') {\n    return;\n  }\n\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    markFunctionWrapped(wrapped, original);\n  }\n\n  try {\n    source[name] = wrapped;\n  } catch {\n    DEBUG_BUILD && logger.log(`Failed to replace method \"${name}\" in object`, source);\n  }\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nexport function addNonEnumerableProperty(obj: object, name: string, value: unknown): void {\n  try {\n    Object.defineProperty(obj, name, {\n      // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n      value: value,\n      writable: true,\n      configurable: true,\n    });\n  } catch (o_O) {\n    DEBUG_BUILD && logger.log(`Failed to add non-enumerable property \"${name}\" to object`, obj);\n  }\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nexport function markFunctionWrapped(wrapped: WrappedFunction, original: WrappedFunction): void {\n  try {\n    const proto = original.prototype || {};\n    wrapped.prototype = original.prototype = proto;\n    addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n  } catch (o_O) {} // eslint-disable-line no-empty\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function getOriginalFunction<T extends Function>(func: WrappedFunction<T>): T | undefined {\n  return func.__sentry_original__;\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\n *  an Error.\n */\nexport function convertToPlainObject<V>(value: V):\n  | {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    }\n  | {\n      [ownProps: string]: unknown;\n      message: string;\n      name: string;\n      stack?: string;\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nexport function extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  const firstKey = keys[0];\n\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n *\n * @deprecated This function is no longer used by the SDK and will be removed in a future major version.\n */\nexport function dropUndefinedKeys<T>(inputValue: T): T {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map<unknown, unknown>();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys<T>(inputValue: T, memoizationMap: Map<unknown, unknown>): T {\n  // Early return for primitive values\n  if (inputValue === null || typeof inputValue !== 'object') {\n    return inputValue;\n  }\n\n  // Check memo map first for all object types\n  const memoVal = memoizationMap.get(inputValue);\n  if (memoVal !== undefined) {\n    return memoVal as T;\n  }\n\n  // handle arrays\n  if (Array.isArray(inputValue)) {\n    const returnValue: unknown[] = [];\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach(value => {\n      returnValue.push(_dropUndefinedKeys(value, memoizationMap));\n    });\n\n    return returnValue as unknown as T;\n  }\n\n  if (isPojo(inputValue)) {\n    const returnValue: { [key: string]: unknown } = {};\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    const keys = Object.keys(inputValue);\n\n    keys.forEach(key => {\n      const val = inputValue[key];\n      if (val !== undefined) {\n        returnValue[key] = _dropUndefinedKeys(val, memoizationMap);\n      }\n    });\n\n    return returnValue as T;\n  }\n\n  // For other object types, return as is\n  return inputValue;\n}\n\nfunction isPojo(input: unknown): input is Record<string, unknown> {\n  // Plain objects have Object as constructor or no constructor\n  const constructor = (input as object).constructor;\n  return constructor === Object || constructor === undefined;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nexport function objectify(wat: unknown): typeof Object {\n  let objectified;\n  switch (true) {\n    // this will catch both undefined and null\n    case wat == undefined:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat as any).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n", "import type { Event } from '../types-hoist/event';\nimport type { Exception } from '../types-hoist/exception';\nimport type { Mechanism } from '../types-hoist/mechanism';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport { addNonEnumerableProperty } from './object';\nimport { snipLine } from './string';\nimport { GLOBAL_OBJ } from './worldwide';\n\ninterface CryptoInternal {\n  getRandomValues(array: Uint8Array): Uint8Array;\n  randomUUID?(): string;\n}\n\n/** An interface for common properties on global */\ninterface CryptoGlobal {\n  msCrypto?: CryptoInternal;\n  crypto?: CryptoInternal;\n}\n\nfunction getCrypto(): CryptoInternal | undefined {\n  const gbl = GLOBAL_OBJ as typeof GLOBAL_OBJ & CryptoGlobal;\n  return gbl.crypto || gbl.msCrypto;\n}\n\n/**\n * UUID4 generator\n * @param crypto Object that provides the crypto API.\n * @returns string Generated UUID4.\n */\nexport function uuid4(crypto = getCrypto()): string {\n  let getRandomByte = (): number => Math.random() * 16;\n  try {\n    if (crypto?.randomUUID) {\n      return crypto.randomUUID().replace(/-/g, '');\n    }\n    if (crypto?.getRandomValues) {\n      getRandomByte = () => {\n        // crypto.getRandomValues might return undefined instead of the typed array\n        // in old Chromium versions (e.g. 23.0.1235.0 (151422))\n        // However, `typedArray` is still filled in-place.\n        // @see https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues#typedarray\n        const typedArray = new Uint8Array(1);\n        crypto.getRandomValues(typedArray);\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return typedArray[0]!;\n      };\n    }\n  } catch (_) {\n    // some runtimes can crash invoking crypto\n    // https://github.com/getsentry/sentry-javascript/issues/8935\n  }\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] as unknown as string) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c as unknown as number) ^ ((getRandomByte() & 15) >> ((c as unknown as number) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event: Event): Exception | undefined {\n  return event.exception?.values?.[0];\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nexport function addExceptionMechanism(event: Event, newMechanism?: Partial<Mechanism>): void {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...currentMechanism?.data, ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\nfunction _parseInt(input: string | undefined): number {\n  return parseInt(input || '', 10);\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = _parseInt(match[1]);\n  const minor = _parseInt(match[2]);\n  const patch = _parseInt(match[3]);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping\n  if (frame.lineno === undefined) {\n    return;\n  }\n\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  // We guard here to ensure this is not larger than the existing number of lines\n  const lineIndex = Math.min(maxLines - 1, sourceLine);\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  frame.context_line = snipLine(lines[lineIndex]!, frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nexport function checkOrSetAlreadyCaught(exception: unknown): boolean {\n  if (isAlreadyCaptured(exception)) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception as { [key: string]: unknown }, '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\nfunction isAlreadyCaptured(exception: unknown): boolean | void {\n  try {\n    return (exception as { __sentry_captured__?: boolean }).__sentry_captured__;\n  } catch {} // eslint-disable-line no-empty\n}\n", "import { GLOBAL_OBJ } from './worldwide';\n\nconst ONE_SECOND_IN_MS = 1000;\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport function dateTimestampInSeconds(): number {\n  return Date.now() / ONE_SECOND_IN_MS;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction createUnixTimestampInSecondsFunc(): () => number {\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & { performance?: Performance };\n  if (!performance?.now) {\n    return dateTimestampInSeconds;\n  }\n\n  // Some browser and environments don't have a timeOrigin, so we fallback to\n  // using Date.now() to compute the starting time.\n  const approxStartingTimeOrigin = Date.now() - performance.now();\n  const timeOrigin = performance.timeOrigin == undefined ? approxStartingTimeOrigin : performance.timeOrigin;\n\n  // performance.now() is a monotonic clock, which means it starts at 0 when the process begins. To get the current\n  // wall clock time (actual UNIX timestamp), we need to add the starting time origin and the current time elapsed.\n  //\n  // TODO: This does not account for the case where the monotonic clock that powers performance.now() drifts from the\n  // wall clock time, which causes the returned timestamp to be inaccurate. We should investigate how to detect and\n  // correct for this.\n  // See: https://github.com/getsentry/sentry-javascript/issues/2590\n  // See: https://github.com/mdn/content/issues/4713\n  // See: https://dev.to/noamr/when-a-millisecond-is-not-a-millisecond-3h6\n  return () => {\n    return (timeOrigin + performance.now()) / ONE_SECOND_IN_MS;\n  };\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds = createUnixTimestampInSecondsFunc();\n\n/**\n * Cached result of getBrowserTimeOrigin.\n */\nlet cachedTimeOrigin: [number | undefined, string] | undefined;\n\n/**\n * Gets the time origin and the mode used to determine it.\n */\nfunction getBrowserTimeOrigin(): [number | undefined, string] {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n  if (!performance?.now) {\n    return [undefined, 'none'];\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing?.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      return [performance.timeOrigin, 'timeOrigin'];\n    } else {\n      return [navigationStart, 'navigationStart'];\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  return [dateNow, 'dateNow'];\n}\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport function browserPerformanceTimeOrigin(): number | undefined {\n  if (!cachedTimeOrigin) {\n    cachedTimeOrigin = getBrowserTimeOrigin();\n  }\n\n  return cachedTimeOrigin[0];\n}\n", "import type { SerializedSession, Session, SessionContext, SessionStatus } from './types-hoist/session';\nimport { uuid4 } from './utils/misc';\nimport { timestampInSeconds } from './utils/time';\n\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nexport function makeSession(context?: Omit<SessionContext, 'started' | 'status'>): Session {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session: Session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see Client.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nexport function updateSession(session: Session, context: SessionContext = {}): void {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.abnormal_mechanism) {\n    session.abnormal_mechanism = context.abnormal_mechanism;\n  }\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nexport function closeSession(session: Session, status?: Exclude<SessionStatus, 'ok'>): void {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session: Session): SerializedSession {\n  return {\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    abnormal_mechanism: session.abnormal_mechanism,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  };\n}\n", "/**\n * Shallow merge two objects.\n * Does not mutate the passed in objects.\n * Undefined/empty values in the merge object will overwrite existing values.\n *\n * By default, this merges 2 levels deep.\n */\nexport function merge<T>(initialObj: T, mergeObj: T, levels = 2): T {\n  // If the merge value is not an object, or we have no merge levels left,\n  // we just set the value to the merge value\n  if (!mergeObj || typeof mergeObj !== 'object' || levels <= 0) {\n    return mergeObj;\n  }\n\n  // If the merge object is an empty object, and the initial object is not undefined, we return the initial object\n  if (initialObj && Object.keys(mergeObj).length === 0) {\n    return initialObj;\n  }\n\n  // Clone object\n  const output = { ...initialObj };\n\n  // Merge values into output, resursively\n  for (const key in mergeObj) {\n    if (Object.prototype.hasOwnProperty.call(mergeObj, key)) {\n      output[key] = merge(output[key], mergeObj[key], levels - 1);\n    }\n  }\n\n  return output;\n}\n", "import { uuid4 } from './misc';\n\n/**\n * Generate a random, valid trace ID.\n */\nexport function generateTraceId(): string {\n  return uuid4();\n}\n\n/**\n * Generate a random, valid span ID.\n */\nexport function generateSpanId(): string {\n  return uuid4().substring(16);\n}\n", "import type { Scope } from '../scope';\nimport type { Span } from '../types-hoist/span';\nimport { addNonEnumerableProperty } from '../utils/object';\n\nconst SCOPE_SPAN_FIELD = '_sentrySpan';\n\ntype ScopeWithMaybeSpan = Scope & {\n  [SCOPE_SPAN_FIELD]?: Span;\n};\n\n/**\n * Set the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _setSpanForScope(scope: Scope, span: Span | undefined): void {\n  if (span) {\n    addNonEnumerableProperty(scope as ScopeWithMaybeSpan, SCOPE_SPAN_FIELD, span);\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete (scope as ScopeWithMaybeSpan)[SCOPE_SPAN_FIELD];\n  }\n}\n\n/**\n * Get the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _getSpanForScope(scope: ScopeWithMaybeSpan): Span | undefined {\n  return scope[SCOPE_SPAN_FIELD];\n}\n", "/* eslint-disable max-lines */\nimport type { Client } from './client';\nimport { updateSession } from './session';\nimport type { Attachment } from './types-hoist/attachment';\nimport type { Breadcrumb } from './types-hoist/breadcrumb';\nimport type { Context, Contexts } from './types-hoist/context';\nimport type { DynamicSamplingContext } from './types-hoist/envelope';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { RequestEventData } from './types-hoist/request';\nimport type { Session } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { Span } from './types-hoist/span';\nimport type { PropagationContext } from './types-hoist/tracing';\nimport type { User } from './types-hoist/user';\nimport { isPlainObject } from './utils/is';\nimport { logger } from './utils/logger';\nimport { merge } from './utils/merge';\nimport { uuid4 } from './utils/misc';\nimport { generateTraceId } from './utils/propagationContext';\nimport { _getSpanForScope, _setSpanForScope } from './utils/spanOnScope';\nimport { truncate } from './utils/string';\nimport { dateTimestampInSeconds } from './utils/time';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * A context to be used for capturing an event.\n * This can either be a Scope, or a partial ScopeContext,\n * or a callback that receives the current scope and returns a new scope to use.\n */\nexport type CaptureContext = Scope | Partial<ScopeContext> | ((scope: Scope) => Scope);\n\n/**\n * Data that can be converted to a Scope.\n */\nexport interface ScopeContext {\n  user: User;\n  level: SeverityLevel;\n  extra: Extras;\n  contexts: Contexts;\n  tags: { [key: string]: Primitive };\n  fingerprint: string[];\n  propagationContext: PropagationContext;\n}\n\nexport interface SdkProcessingMetadata {\n  [key: string]: unknown;\n  requestSession?: {\n    status: 'ok' | 'errored' | 'crashed';\n  };\n  normalizedRequest?: RequestEventData;\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>;\n  capturedSpanScope?: Scope;\n  capturedSpanIsolationScope?: Scope;\n  spanCountBeforeProcessing?: number;\n  ipAddress?: string;\n}\n\n/**\n * Normalized data of the Scope, ready to be used.\n */\nexport interface ScopeData {\n  eventProcessors: EventProcessor[];\n  breadcrumbs: Breadcrumb[];\n  user: User;\n  tags: { [key: string]: Primitive };\n  extra: Extras;\n  contexts: Contexts;\n  attachments: Attachment[];\n  propagationContext: PropagationContext;\n  sdkProcessingMetadata: SdkProcessingMetadata;\n  fingerprint: string[];\n  level?: SeverityLevel;\n  transactionName?: string;\n  span?: Span;\n}\n\n/**\n * Holds additional event information.\n */\nexport class Scope {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called during event processing. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: SdkProcessingMetadata;\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: SeverityLevel;\n\n  /**\n   * Transaction Name\n   *\n   * IMPORTANT: The transaction name on the scope has nothing to do with root spans/transaction objects.\n   * It's purpose is to assign a transaction to the scope that's added to non-transaction events.\n   */\n  protected _transactionName?: string;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** The client on this scope */\n  protected _client?: Client;\n\n  /** Contains the last event id of a captured event.  */\n  protected _lastEventId?: string;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  /**\n   * Clone all data from this scope into a new scope.\n   */\n  public clone(): Scope {\n    const newScope = new Scope();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    if (this._contexts.flags) {\n      // We need to copy the `values` array so insertions on a cloned scope\n      // won't affect the original array.\n      newScope._contexts.flags = {\n        values: [...this._contexts.flags.values],\n      };\n    }\n\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n    newScope._lastEventId = this._lastEventId;\n\n    _setSpanForScope(newScope, _getSpanForScope(this));\n\n    return newScope;\n  }\n\n  /**\n   * Update the client assigned to this scope.\n   * Note that not every scope will have a client assigned - isolation scopes & the global scope will generally not have a client,\n   * as well as manually created scopes.\n   */\n  public setClient(client: Client | undefined): void {\n    this._client = client;\n  }\n\n  /**\n   * Set the ID of the last captured error event.\n   * This is generally only captured on the isolation scope.\n   */\n  public setLastEventId(lastEventId: string | undefined): void {\n    this._lastEventId = lastEventId;\n  }\n\n  /**\n   * Get the client assigned to this scope.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this._client as C | undefined;\n  }\n\n  /**\n   * Get the ID of the last captured error event.\n   * This is generally only available on the isolation scope.\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * Add an event processor that will be called before an event is sent.\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * Set the user for this scope.\n   * Set to `null` to unset the user.\n   */\n  public setUser(user: User | null): this {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the user from this scope.\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * Set an object that will be merged into existing tags on the scope,\n   * and will be sent as tags data with the event.\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single tag that will be sent as tags data with the event.\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set an object that will be merged into existing extra on the scope,\n   * and will be sent as extra data with the event.\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single key:value extra entry that will be sent as extra data with the event.\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the fingerprint on the scope to send with the events.\n   * @param {string[]} fingerprint Fingerprint to group events in Sentry.\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the level on the scope for future events.\n   */\n  public setLevel(level: SeverityLevel): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope so that the name of e.g. taken server route or\n   * the page location is attached to future events.\n   *\n   * IMPORTANT: Calling this function does NOT change the name of the currently active\n   * root span. If you want to change the name of the active root span, use\n   * `Sentry.updateSpanName(rootSpan, 'new name')` instead.\n   *\n   * By default, the SDK updates the scope's transaction name automatically on sensible\n   * occasions, such as a page navigation or when handling a new request on the server.\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets context data with the given name.\n   * Data passed as context will be normalized. You can also pass `null` to unset the context.\n   * Note that context data will not be merged - calling `setContext` will overwrite an existing context with the same key.\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set the session for the scope.\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the session from the scope.\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * Updates the scope with provided data. Can work in three variations:\n   * - plain object containing updatable attributes\n   * - Scope instance that'll extract the attributes from\n   * - callback function that'll receive the current scope as an argument and allow for modifications\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    const scopeInstance =\n      scopeToMerge instanceof Scope\n        ? scopeToMerge.getScopeData()\n        : isPlainObject(scopeToMerge)\n          ? (captureContext as ScopeContext)\n          : undefined;\n\n    const { tags, extra, user, contexts, level, fingerprint = [], propagationContext } = scopeInstance || {};\n\n    this._tags = { ...this._tags, ...tags };\n    this._extra = { ...this._extra, ...extra };\n    this._contexts = { ...this._contexts, ...contexts };\n\n    if (user && Object.keys(user).length) {\n      this._user = user;\n    }\n\n    if (level) {\n      this._level = level;\n    }\n\n    if (fingerprint.length) {\n      this._fingerprint = fingerprint;\n    }\n\n    if (propagationContext) {\n      this._propagationContext = propagationContext;\n    }\n\n    return this;\n  }\n\n  /**\n   * Clears the current scope and resets its properties.\n   * Note: The client will not be cleared.\n   */\n  public clear(): this {\n    // client is not cleared here on purpose!\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._session = undefined;\n    _setSpanForScope(this, undefined);\n    this._attachments = [];\n    this.setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Adds a breadcrumb to the scope.\n   * By default, the last 100 breadcrumbs are kept.\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb: Breadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n      // Breadcrumb messages can theoretically be infinitely large and they're held in memory so we truncate them not to leak (too much) memory\n      message: breadcrumb.message ? truncate(breadcrumb.message, 2048) : breadcrumb.message,\n    };\n\n    this._breadcrumbs.push(mergedBreadcrumb);\n    if (this._breadcrumbs.length > maxCrumbs) {\n      this._breadcrumbs = this._breadcrumbs.slice(-maxCrumbs);\n      this._client?.recordDroppedEvent('buffer_overflow', 'log_item');\n    }\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * Get the last breadcrumb of the scope.\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * Clear all breadcrumbs from the scope.\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Add an attachment to the scope.\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * Clear all attachments from the scope.\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Get the data of this scope, which should be applied to an event during processing.\n   */\n  public getScopeData(): ScopeData {\n    return {\n      breadcrumbs: this._breadcrumbs,\n      attachments: this._attachments,\n      contexts: this._contexts,\n      tags: this._tags,\n      extra: this._extra,\n      user: this._user,\n      level: this._level,\n      fingerprint: this._fingerprint || [],\n      eventProcessors: this._eventProcessors,\n      propagationContext: this._propagationContext,\n      sdkProcessingMetadata: this._sdkProcessingMetadata,\n      transactionName: this._transactionName,\n      span: _getSpanForScope(this),\n    };\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry.\n   */\n  public setSDKProcessingMetadata(newData: SdkProcessingMetadata): this {\n    this._sdkProcessingMetadata = merge(this._sdkProcessingMetadata, newData, 2);\n    return this;\n  }\n\n  /**\n   * Add propagation context to the scope, used for distributed tracing\n   */\n  public setPropagationContext(context: PropagationContext): this {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * Get propagation context from the scope, used for distributed tracing\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * Capture an exception for this scope.\n   *\n   * @returns {string} The id of the captured Sentry event.\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a message for this scope.\n   *\n   * @returns {string} The id of the captured message.\n   */\n  public captureMessage(message: string, level?: SeverityLevel, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a Sentry event for this scope.\n   *\n   * @returns {string} The id of the captured event.\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n", "import { getGlobalSingleton } from './carrier';\nimport { Scope } from './scope';\n\n/** Get the default current scope. */\nexport function getDefaultCurrentScope(): Scope {\n  return getGlobalSingleton('defaultCurrentScope', () => new Scope());\n}\n\n/** Get the default isolation scope. */\nexport function getDefaultIsolationScope(): Scope {\n  return getGlobalSingleton('defaultIsolationScope', () => new Scope());\n}\n", "import type { Client } from '../client';\nimport { getDefaultCurrentScope, getDefaultIsolationScope } from '../defaultScopes';\nimport { Scope } from '../scope';\nimport { isThenable } from '../utils/is';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport type { AsyncContextStrategy } from './types';\n\ninterface Layer {\n  client?: Client;\n  scope: Scope;\n}\n\n/**\n * This is an object that holds a stack of scopes.\n */\nexport class AsyncContextStack {\n  private readonly _stack: [Layer, ...Layer[]];\n  private _isolationScope: Scope;\n\n  public constructor(scope?: Scope, isolationScope?: Scope) {\n    let assignedScope;\n    if (!scope) {\n      assignedScope = new Scope();\n    } else {\n      assignedScope = scope;\n    }\n\n    let assignedIsolationScope;\n    if (!isolationScope) {\n      assignedIsolationScope = new Scope();\n    } else {\n      assignedIsolationScope = isolationScope;\n    }\n\n    // scope stack for domains or the process\n    this._stack = [{ scope: assignedScope }];\n    this._isolationScope = assignedIsolationScope;\n  }\n\n  /**\n   * Fork a scope for the stack.\n   */\n  public withScope<T>(callback: (scope: Scope) => T): T {\n    const scope = this._pushScope();\n\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback(scope);\n    } catch (e) {\n      this._popScope();\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      // @ts-expect-error - isThenable returns the wrong type\n      return maybePromiseResult.then(\n        res => {\n          this._popScope();\n          return res;\n        },\n        e => {\n          this._popScope();\n          throw e;\n        },\n      );\n    }\n\n    this._popScope();\n    return maybePromiseResult;\n  }\n\n  /**\n   * Get the client of the stack.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /**\n   * Returns the scope of the top stack.\n   */\n  public getScope(): Scope {\n    return this.getStackTop().scope;\n  }\n\n  /**\n   * Get the isolation scope for the stack.\n   */\n  public getIsolationScope(): Scope {\n    return this._isolationScope;\n  }\n\n  /**\n   * Returns the topmost scope layer in the order domain > local > process.\n   */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1] as Layer;\n  }\n\n  /**\n   * Push a scope to the stack.\n   */\n  private _pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = this.getScope().clone();\n    this._stack.push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * Pop a scope from the stack.\n   */\n  private _popScope(): boolean {\n    if (this._stack.length <= 1) return false;\n    return !!this._stack.pop();\n  }\n}\n\n/**\n * Get the global async context stack.\n * This will be removed during the v8 cycle and is only here to make migration easier.\n */\nfunction getAsyncContextStack(): AsyncContextStack {\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n\n  return (sentry.stack = sentry.stack || new AsyncContextStack(getDefaultCurrentScope(), getDefaultIsolationScope()));\n}\n\nfunction withScope<T>(callback: (scope: Scope) => T): T {\n  return getAsyncContextStack().withScope(callback);\n}\n\nfunction withSetScope<T>(scope: Scope, callback: (scope: Scope) => T): T {\n  const stack = getAsyncContextStack() as AsyncContextStack;\n  return stack.withScope(() => {\n    stack.getStackTop().scope = scope;\n    return callback(scope);\n  });\n}\n\nfunction withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n  return getAsyncContextStack().withScope(() => {\n    return callback(getAsyncContextStack().getIsolationScope());\n  });\n}\n\n/**\n * Get the stack-based async context strategy.\n */\nexport function getStackAsyncContextStrategy(): AsyncContextStrategy {\n  return {\n    withIsolationScope,\n    withScope,\n    withSetScope,\n    withSetIsolationScope: <T>(_isolationScope: Scope, callback: (isolationScope: Scope) => T) => {\n      return withIsolationScope(callback);\n    },\n    getCurrentScope: () => getAsyncContextStack().getScope(),\n    getIsolationScope: () => getAsyncContextStack().getIsolationScope(),\n  };\n}\n", "import type { Carrier } from './../carrier';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport { getStackAsyncContextStrategy } from './stackStrategy';\nimport type { AsyncContextStrategy } from './types';\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nexport function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n  sentry.acs = strategy;\n}\n\n/**\n * Get the current async context strategy.\n * If none has been setup, the default will be used.\n */\nexport function getAsyncContextStrategy(carrier: Carrier): AsyncContextStrategy {\n  const sentry = getSentryCarrier(carrier);\n\n  if (sentry.acs) {\n    return sentry.acs;\n  }\n\n  // Otherwise, use the default one (stack)\n  return getStackAsyncContextStrategy();\n}\n", "import { getAsyncContextStrategy } from './asyncContext';\nimport { getGlobal<PERSON>ingleton, getMainCarrier } from './carrier';\nimport type { Client } from './client';\nimport { Scope } from './scope';\nimport type { TraceContext } from './types-hoist/context';\nimport { generateSpanId } from './utils/propagationContext';\n\n/**\n * Get the currently active scope.\n */\nexport function getCurrentScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getCurrentScope();\n}\n\n/**\n * Get the currently active isolation scope.\n * The isolation scope is active for the current execution context.\n */\nexport function getIsolationScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getIsolationScope();\n}\n\n/**\n * Get the global scope.\n * This scope is applied to _all_ events.\n */\nexport function getGlobalScope(): Scope {\n  return getGlobalSingleton('globalScope', () => new Scope());\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n */\nexport function withScope<T>(callback: (scope: Scope) => T): T;\n/**\n * Set the given scope as the active scope in the callback.\n */\nexport function withScope<T>(scope: Scope | undefined, callback: (scope: Scope) => T): T;\n/**\n * Either creates a new active scope, or sets the given scope as active scope in the given callback.\n */\nexport function withScope<T>(\n  ...rest: [callback: (scope: Scope) => T] | [scope: Scope | undefined, callback: (scope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [scope, callback] = rest;\n\n    if (!scope) {\n      return acs.withScope(callback);\n    }\n\n    return acs.withSetScope(scope, callback);\n  }\n\n  return acs.withScope(rest[0]);\n}\n\n/**\n * Attempts to fork the current isolation scope and the current scope based on the current async context strategy. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n */\nexport function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T;\n/**\n * Set the provided isolation scope as active in the given callback. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n *\n * If you pass in `undefined` as a scope, it will fork a new isolation scope, the same as if no scope is passed.\n */\nexport function withIsolationScope<T>(isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T): T;\n/**\n * Either creates a new active isolation scope, or sets the given isolation scope as active scope in the given callback.\n */\nexport function withIsolationScope<T>(\n  ...rest:\n    | [callback: (isolationScope: Scope) => T]\n    | [isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [isolationScope, callback] = rest;\n\n    if (!isolationScope) {\n      return acs.withIsolationScope(callback);\n    }\n\n    return acs.withSetIsolationScope(isolationScope, callback);\n  }\n\n  return acs.withIsolationScope(rest[0]);\n}\n\n/**\n * Get the currently active client.\n */\nexport function getClient<C extends Client>(): C | undefined {\n  return getCurrentScope().getClient<C>();\n}\n\n/**\n * Get a trace context for the given scope.\n */\nexport function getTraceContextFromScope(scope: Scope): TraceContext {\n  const propagationContext = scope.getPropagationContext();\n\n  const { traceId, parentSpanId, propagationSpanId } = propagationContext;\n\n  const traceContext: TraceContext = {\n    trace_id: traceId,\n    span_id: propagationSpanId || generateSpanId(),\n  };\n\n  if (parentSpanId) {\n    traceContext.parent_span_id = parentSpanId;\n  }\n\n  return traceContext;\n}\n", "export const DEFAULT_ENVIRONMENT = 'production';\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nconst enum States {\n  /** Pending */\n  PENDING = 0,\n  /** Resolved / OK */\n  RESOLVED = 1,\n  /** Rejected / Error */\n  REJECTED = 2,\n}\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\nexport function resolvedSyncPromise(): PromiseLike<void>;\nexport function resolvedSyncPromise<T>(value: T | PromiseLike<T>): PromiseLike<T>;\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nexport function resolvedSyncPromise<T>(value?: T | PromiseLike<T>): PromiseLike<T> {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nexport function rejectedSyncPromise<T = never>(reason?: any): PromiseLike<T> {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\ntype Executor<T> = (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void;\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nexport class SyncPromise<T> implements PromiseLike<T> {\n  private _state: States;\n  private _handlers: Array<[boolean, (value: T) => void, (reason: any) => any]>;\n  private _value: any;\n\n  public constructor(executor: Executor<T>) {\n    this._state = States.PENDING;\n    this._handlers = [];\n\n    this._runExecutor(executor);\n  }\n\n  /** @inheritdoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** @inheritdoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** @inheritdoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val as unknown as any);\n      });\n    });\n  }\n\n  /** Excute the resolve/reject handlers. */\n  private _executeHandlers(): void {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        handler[1](this._value as unknown as any);\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  }\n\n  /** Run the executor for the SyncPromise. */\n  private _runExecutor(executor: Executor<T>): void {\n    const setResult = (state: States, value?: T | PromiseLike<T> | any): void => {\n      if (this._state !== States.PENDING) {\n        return;\n      }\n\n      if (isThenable(value)) {\n        void (value as PromiseLike<T>).then(resolve, reject);\n        return;\n      }\n\n      this._state = state;\n      this._value = value;\n\n      this._executeHandlers();\n    };\n\n    const resolve = (value: unknown): void => {\n      setResult(States.RESOLVED, value);\n    };\n\n    const reject = (reason: unknown): void => {\n      setResult(States.REJECTED, reason);\n    };\n\n    try {\n      executor(resolve, reject);\n    } catch (e) {\n      reject(e);\n    }\n  }\n}\n", "import { DEBUG_BUILD } from './debug-build';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport { isThenable } from './utils/is';\nimport { logger } from './utils/logger';\nimport { SyncPromise } from './utils/syncpromise';\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nexport function notifyEventProcessors(\n  processors: EventProcessor[],\n  event: Event | null,\n  hint: EventHint,\n  index: number = 0,\n): PromiseLike<Event | null> {\n  return new SyncPromise<Event | null>((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) as Event | null;\n\n      DEBUG_BUILD && processor.id && result === null && logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n", "/**\n * Use this attribute to represent the source of a span.\n * Should be one of: custom, url, route, view, component, task, unknown\n *\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = 'sentry.source';\n\n/**\n * Attributes that holds the sample rate that was locally applied to a span.\n * If this attribute is not defined, it means that the span inherited a sampling decision.\n *\n * NOTE: Is only defined on root spans.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = 'sentry.sample_rate';\n\n/**\n * Attribute holding the sample rate of the previous trace.\n * This is used to sample consistently across subsequent traces in the browser SDK.\n *\n * Note: Only defined on root spans, if opted into consistent sampling\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE = 'sentry.previous_trace_sample_rate';\n\n/**\n * Use this attribute to represent the operation of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_OP = 'sentry.op';\n\n/**\n * Use this attribute to represent the origin of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = 'sentry.origin';\n\n/** The reason why an idle span finished. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON = 'sentry.idle_span_finish_reason';\n\n/** The unit of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT = 'sentry.measurement_unit';\n\n/** The value of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE = 'sentry.measurement_value';\n\n/**\n * A custom span name set by users guaranteed to be taken over any automatically\n * inferred name. This attribute is removed before the span is sent.\n *\n * @internal only meant for internal SDK usage\n * @hidden\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME = 'sentry.custom_span_name';\n\n/**\n * The id of the profile that this span occurred in.\n */\nexport const SEMANTIC_ATTRIBUTE_PROFILE_ID = 'sentry.profile_id';\n\nexport const SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME = 'sentry.exclusive_time';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_HIT = 'cache.hit';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_KEY = 'cache.key';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE = 'cache.item_size';\n\n/** TODO: Remove these once we update to latest semantic conventions */\nexport const SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD = 'http.request.method';\nexport const SEMANTIC_ATTRIBUTE_URL_FULL = 'url.full';\n\n/**\n * A span link attribute to mark the link as a special span link.\n *\n * Known values:\n * - `previous_trace`: The span links to the frontend root span of the previous trace.\n * - `next_trace`: The span links to the frontend root span of the next trace. (Not set by the SDK)\n *\n * Other values may be set as appropriate.\n * @see https://develop.sentry.dev/sdk/telemetry/traces/span-links/#link-types\n */\nexport const SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE = 'sentry.link.type';\n", "import { DEBUG_BUILD } from '../debug-build';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport { isString } from './is';\nimport { logger } from './logger';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX = 'sentry-';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX_REGEX = /^sentry-/;\n\n/**\n * Max length of a serialized baggage string\n *\n * https://www.w3.org/TR/baggage/#limits\n */\nexport const MAX_BAGGAGE_STRING_LENGTH = 8192;\n\n/**\n * Takes a baggage header and turns it into Dynamic Sampling Context, by extracting all the \"sentry-\" prefixed values\n * from it.\n *\n * @param baggageHeader A very bread definition of a baggage header as it might appear in various frameworks.\n * @returns The Dynamic Sampling Context that was found on `baggageHeader`, if there was any, `undefined` otherwise.\n */\nexport function baggageHeaderToDynamicSamplingContext(\n  // Very liberal definition of what any incoming header might look like\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Partial<DynamicSamplingContext> | undefined {\n  const baggageObject = parseBaggageHeader(baggageHeader);\n\n  if (!baggageObject) {\n    return undefined;\n  }\n\n  // Read all \"sentry-\" prefixed values out of the baggage object and put it onto a dynamic sampling context object.\n  const dynamicSamplingContext = Object.entries(baggageObject).reduce<Record<string, string>>((acc, [key, value]) => {\n    if (key.match(SENTRY_BAGGAGE_KEY_PREFIX_REGEX)) {\n      const nonPrefixedKey = key.slice(SENTRY_BAGGAGE_KEY_PREFIX.length);\n      acc[nonPrefixedKey] = value;\n    }\n    return acc;\n  }, {});\n\n  // Only return a dynamic sampling context object if there are keys in it.\n  // A keyless object means there were no sentry values on the header, which means that there is no DSC.\n  if (Object.keys(dynamicSamplingContext).length > 0) {\n    return dynamicSamplingContext as Partial<DynamicSamplingContext>;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Turns a Dynamic Sampling Object into a baggage header by prefixing all the keys on the object with \"sentry-\".\n *\n * @param dynamicSamplingContext The Dynamic Sampling Context to turn into a header. For convenience and compatibility\n * with the `getDynamicSamplingContext` method on the Transaction class ,this argument can also be `undefined`. If it is\n * `undefined` the function will return `undefined`.\n * @returns a baggage header, created from `dynamicSamplingContext`, or `undefined` either if `dynamicSamplingContext`\n * was `undefined`, or if `dynamicSamplingContext` didn't contain any values.\n */\nexport function dynamicSamplingContextToSentryBaggageHeader(\n  // this also takes undefined for convenience and bundle size in other places\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n): string | undefined {\n  if (!dynamicSamplingContext) {\n    return undefined;\n  }\n\n  // Prefix all DSC keys with \"sentry-\" and put them into a new object\n  const sentryPrefixedDSC = Object.entries(dynamicSamplingContext).reduce<Record<string, string>>(\n    (acc, [dscKey, dscValue]) => {\n      if (dscValue) {\n        acc[`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`] = dscValue;\n      }\n      return acc;\n    },\n    {},\n  );\n\n  return objectToBaggageHeader(sentryPrefixedDSC);\n}\n\n/**\n * Take a baggage header and parse it into an object.\n */\nexport function parseBaggageHeader(\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Record<string, string> | undefined {\n  if (!baggageHeader || (!isString(baggageHeader) && !Array.isArray(baggageHeader))) {\n    return undefined;\n  }\n\n  if (Array.isArray(baggageHeader)) {\n    // Combine all baggage headers into one object containing the baggage values so we can later read the Sentry-DSC-values from it\n    return baggageHeader.reduce<Record<string, string>>((acc, curr) => {\n      const currBaggageObject = baggageHeaderToObject(curr);\n      Object.entries(currBaggageObject).forEach(([key, value]) => {\n        acc[key] = value;\n      });\n      return acc;\n    }, {});\n  }\n\n  return baggageHeaderToObject(baggageHeader);\n}\n\n/**\n * Will parse a baggage header, which is a simple key-value map, into a flat object.\n *\n * @param baggageHeader The baggage header to parse.\n * @returns a flat object containing all the key-value pairs from `baggageHeader`.\n */\nfunction baggageHeaderToObject(baggageHeader: string): Record<string, string> {\n  return baggageHeader\n    .split(',')\n    .map(baggageEntry =>\n      baggageEntry.split('=').map(keyOrValue => {\n        try {\n          return decodeURIComponent(keyOrValue.trim());\n        } catch {\n          // We ignore errors here, e.g. if the value cannot be URL decoded.\n          // This will then be skipped in the next step\n          return;\n        }\n      }),\n    )\n    .reduce<Record<string, string>>((acc, [key, value]) => {\n      if (key && value) {\n        acc[key] = value;\n      }\n      return acc;\n    }, {});\n}\n\n/**\n * Turns a flat object (key-value pairs) into a baggage header, which is also just key-value pairs.\n *\n * @param object The object to turn into a baggage header.\n * @returns a baggage header string, or `undefined` if the object didn't have any values, since an empty baggage header\n * is not spec compliant.\n */\nexport function objectToBaggageHeader(object: Record<string, string>): string | undefined {\n  if (Object.keys(object).length === 0) {\n    // An empty baggage header is not spec compliant: We return undefined.\n    return undefined;\n  }\n\n  return Object.entries(object).reduce((baggageHeader, [objectKey, objectValue], currentIndex) => {\n    const baggageEntry = `${encodeURIComponent(objectKey)}=${encodeURIComponent(objectValue)}`;\n    const newBaggageHeader = currentIndex === 0 ? baggageEntry : `${baggageHeader},${baggageEntry}`;\n    if (newBaggageHeader.length > MAX_BAGGAGE_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Not adding key: ${objectKey} with val: ${objectValue} to baggage header due to exceeding baggage size limits.`,\n        );\n      return baggageHeader;\n    } else {\n      return newBaggageHeader;\n    }\n  }, '');\n}\n", "import { DEBUG_BUILD } from '../debug-build';\nimport type { DsnCom<PERSON>, DsnLike, DsnProtocol } from '../types-hoist/dsn';\nimport { consoleSandbox, logger } from './logger';\n\n/** Regular expression used to extract org ID from a DSN host. */\nconst ORG_ID_REGEX = /^o(\\d+)\\./;\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol?: string): protocol is DsnProtocol {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nexport function dsnToString(dsn: DsnComponents, withPassword: boolean = false): string {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents or undefined if @param str is not a valid DSN string\n */\nexport function dsnFromString(str: string): DsnComponents | undefined {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    // This should be logged to the console\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.error(`Invalid Sentry Dsn: ${str}`);\n    });\n    return undefined;\n  }\n\n  const [protocol, publicKey, pass = '', host = '', port = '', lastPath = ''] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() as string;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, publicKey });\n}\n\nfunction dsnFromComponents(components: DsnComponents): DsnComponents {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn: DsnComponents): boolean {\n  if (!DEBUG_BUILD) {\n    return true;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents: ReadonlyArray<keyof DsnComponents> = ['protocol', 'publicKey', 'host', 'projectId'];\n  const hasMissingRequiredComponent = requiredComponents.find(component => {\n    if (!dsn[component]) {\n      logger.error(`Invalid Sentry Dsn: ${component} missing`);\n      return true;\n    }\n    return false;\n  });\n\n  if (hasMissingRequiredComponent) {\n    return false;\n  }\n\n  if (!projectId.match(/^\\d+$/)) {\n    logger.error(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n    return false;\n  }\n\n  if (!isValidProtocol(protocol)) {\n    logger.error(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n    return false;\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    logger.error(`Invalid Sentry Dsn: Invalid port ${port}`);\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Extract the org ID from a DSN host.\n *\n * @param host The host from a DSN\n * @returns The org ID if found, undefined otherwise\n */\nexport function extractOrgIdFromDsnHost(host: string): string | undefined {\n  const match = host.match(ORG_ID_REGEX);\n\n  return match?.[1];\n}\n\n/**\n * Creates a valid Sentry Dsn object, identifying a Sentry instance and project.\n * @returns a valid DsnComponents object or `undefined` if @param from is an invalid DSN source\n */\nexport function makeDsn(from: DsnLike): DsnComponents | undefined {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  if (!components || !validateDsn(components)) {\n    return undefined;\n  }\n  return components;\n}\n", "import { getClient } from '../currentScopes';\nimport type { Options } from '../types-hoist/options';\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean | undefined;\n\n/**\n * Determines if span recording is currently enabled.\n *\n * Spans are recorded when at least one of `tracesSampleRate` and `tracesSampler`\n * is defined in the SDK config. This function does not make any assumption about\n * sampling decisions, it only checks if the SDK is configured to record spans.\n *\n * Important: This function only determines if span recording is enabled. Trace\n * continuation and propagation is separately controlled and not covered by this function.\n * If this function returns `false`, traces can still be propagated (which is what\n * we refer to by \"Tracing without Performance\")\n * @see https://develop.sentry.dev/sdk/telemetry/traces/tracing-without-performance/\n *\n * @param maybeOptions An SDK options object to be passed to this function.\n * If this option is not provided, the function will use the current client's options.\n */\nexport function hasSpansEnabled(\n  maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler'> | undefined,\n): boolean {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const options = maybeOptions || getClient()?.getOptions();\n  return (\n    !!options &&\n    // Note: This check is `!= null`, meaning \"nullish\". `0` is not \"nullish\", `undefined` and `null` are. (This comment was brought to you by 15 minutes of questioning life)\n    (options.tracesSampleRate != null || !!options.tracesSampler)\n  );\n}\n\n/**\n * @see JSDoc of `hasSpansEnabled`\n * @deprecated Use `hasSpansEnabled` instead, which is a more accurately named version of this function.\n * This function will be removed in the next major version of the SDK.\n */\n// TODO(v10): Remove this export\nexport const hasTracingEnabled = hasSpansEnabled;\n", "import type { Span } from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\n\nexport const SPAN_STATUS_UNSET = 0;\nexport const SPAN_STATUS_OK = 1;\nexport const SPAN_STATUS_ERROR = 2;\n\n/**\n * Converts a HTTP status code into a sentry status with a message.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\n// https://develop.sentry.dev/sdk/event-payloads/span/\nexport function getSpanStatusFromHttpCode(httpStatus: number): SpanStatus {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return { code: SPAN_STATUS_OK };\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return { code: SPAN_STATUS_ERROR, message: 'unauthenticated' };\n      case 403:\n        return { code: SPAN_STATUS_ERROR, message: 'permission_denied' };\n      case 404:\n        return { code: SPAN_STATUS_ERROR, message: 'not_found' };\n      case 409:\n        return { code: SPAN_STATUS_ERROR, message: 'already_exists' };\n      case 413:\n        return { code: SPAN_STATUS_ERROR, message: 'failed_precondition' };\n      case 429:\n        return { code: SPAN_STATUS_ERROR, message: 'resource_exhausted' };\n      case 499:\n        return { code: SPAN_STATUS_ERROR, message: 'cancelled' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'invalid_argument' };\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return { code: SPAN_STATUS_ERROR, message: 'unimplemented' };\n      case 503:\n        return { code: SPAN_STATUS_ERROR, message: 'unavailable' };\n      case 504:\n        return { code: SPAN_STATUS_ERROR, message: 'deadline_exceeded' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'internal_error' };\n    }\n  }\n\n  return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n}\n\n/**\n * Sets the Http status attributes on the current span based on the http code.\n * Additionally, the span's status is updated, depending on the http code.\n */\nexport function setHttpStatus(span: Span, httpStatus: number): void {\n  span.setAttribute('http.response.status_code', httpStatus);\n\n  const spanStatus = getSpanStatusFromHttpCode(httpStatus);\n  if (spanStatus.message !== 'unknown_error') {\n    span.setStatus(spanStatus);\n  }\n}\n", "import type { Scope } from '../scope';\nimport type { Span } from '../types-hoist/span';\nimport { addNonEnumerableProperty } from '../utils/object';\n\nconst SCOPE_ON_START_SPAN_FIELD = '_sentryScope';\nconst ISOLATION_SCOPE_ON_START_SPAN_FIELD = '_sentryIsolationScope';\n\ntype SpanWithScopes = Span & {\n  [SCOPE_ON_START_SPAN_FIELD]?: Scope;\n  [ISOLATION_SCOPE_ON_START_SPAN_FIELD]?: Scope;\n};\n\n/** Store the scope & isolation scope for a span, which can the be used when it is finished. */\nexport function setCapturedScopesOnSpan(span: Span | undefined, scope: Scope, isolationScope: Scope): void {\n  if (span) {\n    addNonEnumerableProperty(span, ISOLATION_SCOPE_ON_START_SPAN_FIELD, isolationScope);\n    addNonEnumerableProperty(span, SCOPE_ON_START_SPAN_FIELD, scope);\n  }\n}\n\n/**\n * Grabs the scope and isolation scope off a span that were active when the span was started.\n */\nexport function getCapturedScopesOnSpan(span: Span): { scope?: Scope; isolationScope?: Scope } {\n  return {\n    scope: (span as SpanWithScopes)[SCOPE_ON_START_SPAN_FIELD],\n    isolationScope: (span as SpanWithScopes)[ISOLATION_SCOPE_ON_START_SPAN_FIELD],\n  };\n}\n", "/**\n * Parse a sample rate from a given value.\n * This will either return a boolean or number sample rate, if the sample rate is valid (between 0 and 1).\n * If a string is passed, we try to convert it to a number.\n *\n * Any invalid sample rate will return `undefined`.\n */\nexport function parseSampleRate(sampleRate: unknown): number | undefined {\n  if (typeof sampleRate === 'boolean') {\n    return Number(sampleRate);\n  }\n\n  const rate = typeof sampleRate === 'string' ? parseFloat(sampleRate) : sampleRate;\n  if (typeof rate !== 'number' || isNaN(rate) || rate < 0 || rate > 1) {\n    return undefined;\n  }\n\n  return rate;\n}\n", "import type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { PropagationContext } from '../types-hoist/tracing';\nimport type { TraceparentData } from '../types-hoist/transaction';\nimport { baggageHeaderToDynamicSamplingContext } from './baggage';\nimport { parseSampleRate } from './parseSampleRate';\nimport { generateSpanId, generateTraceId } from './propagationContext';\n\n// eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- RegExp is used for readability here\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n    '([0-9a-f]{32})?' + // trace_id\n    '-?([0-9a-f]{16})?' + // span_id\n    '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent?: string): TraceparentData | undefined {\n  if (!traceparent) {\n    return undefined;\n  }\n\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (!matches) {\n    return undefined;\n  }\n\n  let parentSampled: boolean | undefined;\n  if (matches[3] === '1') {\n    parentSampled = true;\n  } else if (matches[3] === '0') {\n    parentSampled = false;\n  }\n\n  return {\n    traceId: matches[1],\n    parentSampled,\n    parentSpanId: matches[2],\n  };\n}\n\n/**\n * Create a propagation context from incoming headers or\n * creates a minimal new one if the headers are undefined.\n */\nexport function propagationContextFromHeaders(\n  sentryTrace: string | undefined,\n  baggage: string | number | boolean | string[] | null | undefined,\n): PropagationContext {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  if (!traceparentData?.traceId) {\n    return {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  const sampleRand = getSampleRandFromTraceparentAndDsc(traceparentData, dynamicSamplingContext);\n\n  // The sample_rand on the DSC needs to be generated based on traceparent + baggage.\n  if (dynamicSamplingContext) {\n    dynamicSamplingContext.sample_rand = sampleRand.toString();\n  }\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData;\n\n  return {\n    traceId,\n    parentSpanId,\n    sampled: parentSampled,\n    dsc: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n    sampleRand,\n  };\n}\n\n/**\n * Create sentry-trace header from span context values.\n */\nexport function generateSentryTraceHeader(\n  traceId: string | undefined = generateTraceId(),\n  spanId: string | undefined = generateSpanId(),\n  sampled?: boolean,\n): string {\n  let sampledString = '';\n  if (sampled !== undefined) {\n    sampledString = sampled ? '-1' : '-0';\n  }\n  return `${traceId}-${spanId}${sampledString}`;\n}\n\n/**\n * Given any combination of an incoming trace, generate a sample rand based on its defined semantics.\n *\n * Read more: https://develop.sentry.dev/sdk/telemetry/traces/#propagated-random-value\n */\nfunction getSampleRandFromTraceparentAndDsc(\n  traceparentData: TraceparentData | undefined,\n  dsc: Partial<DynamicSamplingContext> | undefined,\n): number {\n  // When there is an incoming sample rand use it.\n  const parsedSampleRand = parseSampleRate(dsc?.sample_rand);\n  if (parsedSampleRand !== undefined) {\n    return parsedSampleRand;\n  }\n\n  // Otherwise, if there is an incoming sampling decision + sample rate, generate a sample rand that would lead to the same sampling decision.\n  const parsedSampleRate = parseSampleRate(dsc?.sample_rate);\n  if (parsedSampleRate && traceparentData?.parentSampled !== undefined) {\n    return traceparentData.parentSampled\n      ? // Returns a sample rand with positive sampling decision [0, sampleRate)\n        Math.random() * parsedSampleRate\n      : // Returns a sample rand with negative sampling decision [sampleRate, 1)\n        parsedSampleRate + Math.random() * (1 - parsedSampleRate);\n  } else {\n    // If nothing applies, return a random sample rand.\n    return Math.random();\n  }\n}\n", "import { getAsyncContextStrategy } from '../asyncContext';\nimport { getMainCarrier } from '../carrier';\nimport { getCurrentScope } from '../currentScopes';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { SentrySpan } from '../tracing/sentrySpan';\nimport { SPAN_STATUS_OK, SPAN_STATUS_UNSET } from '../tracing/spanstatus';\nimport { getCapturedScopesOnSpan } from '../tracing/utils';\nimport type { TraceContext } from '../types-hoist/context';\nimport type { SpanLink, SpanLinkJSON } from '../types-hoist/link';\nimport type { Span, SpanAttributes, SpanJSON, SpanOrigin, SpanTimeInput } from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport { consoleSandbox } from '../utils/logger';\nimport { addNonEnumerableProperty } from '../utils/object';\nimport { generateSpanId } from '../utils/propagationContext';\nimport { timestampInSeconds } from '../utils/time';\nimport { generateSentryTraceHeader } from '../utils/tracing';\nimport { _getSpanForScope } from './spanOnScope';\n\n// These are aligned with OpenTelemetry trace flags\nexport const TRACE_FLAG_NONE = 0x0;\nexport const TRACE_FLAG_SAMPLED = 0x1;\n\nlet hasShownSpanDropWarning = false;\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in an event.\n * By default, this will only include trace_id, span_id & parent_span_id.\n * If `includeAllData` is true, it will also include data, op, status & origin.\n */\nexport function spanToTransactionTraceContext(span: Span): TraceContext {\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n  const { data, op, parent_span_id, status, origin, links } = spanToJSON(span);\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data,\n    op,\n    status,\n    origin,\n    links,\n  };\n}\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in a non-transaction event.\n */\nexport function spanToTraceContext(span: Span): TraceContext {\n  const { spanId, traceId: trace_id, isRemote } = span.spanContext();\n\n  // If the span is remote, we use a random/virtual span as span_id to the trace context,\n  // and the remote span as parent_span_id\n  const parent_span_id = isRemote ? spanId : spanToJSON(span).parent_span_id;\n  const scope = getCapturedScopesOnSpan(span).scope;\n\n  const span_id = isRemote ? scope?.getPropagationContext().propagationSpanId || generateSpanId() : spanId;\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n  };\n}\n\n/**\n * Convert a Span to a Sentry trace header.\n */\nexport function spanToTraceHeader(span: Span): string {\n  const { traceId, spanId } = span.spanContext();\n  const sampled = spanIsSampled(span);\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n\n/**\n *  Converts the span links array to a flattened version to be sent within an envelope.\n *\n *  If the links array is empty, it returns `undefined` so the empty value can be dropped before it's sent.\n */\nexport function convertSpanLinksForEnvelope(links?: SpanLink[]): SpanLinkJSON[] | undefined {\n  if (links && links.length > 0) {\n    return links.map(({ context: { spanId, traceId, traceFlags, ...restContext }, attributes }) => ({\n      span_id: spanId,\n      trace_id: traceId,\n      sampled: traceFlags === TRACE_FLAG_SAMPLED,\n      attributes,\n      ...restContext,\n    }));\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Convert a span time input into a timestamp in seconds.\n */\nexport function spanTimeInputToSeconds(input: SpanTimeInput | undefined): number {\n  if (typeof input === 'number') {\n    return ensureTimestampInSeconds(input);\n  }\n\n  if (Array.isArray(input)) {\n    // See {@link HrTime} for the array-based time format\n    return input[0] + input[1] / 1e9;\n  }\n\n  if (input instanceof Date) {\n    return ensureTimestampInSeconds(input.getTime());\n  }\n\n  return timestampInSeconds();\n}\n\n/**\n * Converts a timestamp to second, if it was in milliseconds, or keeps it as second.\n */\nfunction ensureTimestampInSeconds(timestamp: number): number {\n  const isMs = timestamp > 9999999999;\n  return isMs ? timestamp / 1000 : timestamp;\n}\n\n/**\n * Convert a span to a JSON representation.\n */\n// Note: Because of this, we currently have a circular type dependency (which we opted out of in package.json).\n// This is not avoidable as we need `spanToJSON` in `spanUtils.ts`, which in turn is needed by `span.ts` for backwards compatibility.\n// And `spanToJSON` needs the Span class from `span.ts` to check here.\nexport function spanToJSON(span: Span): SpanJSON {\n  if (spanIsSentrySpan(span)) {\n    return span.getSpanJSON();\n  }\n\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n\n  // Handle a span from @opentelemetry/sdk-base-trace's `Span` class\n  if (spanIsOpenTelemetrySdkTraceBaseSpan(span)) {\n    const { attributes, startTime, name, endTime, status, links } = span;\n\n    // In preparation for the next major of OpenTelemetry, we want to support\n    // looking up the parent span id according to the new API\n    // In OTel v1, the parent span id is accessed as `parentSpanId`\n    // In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n    const parentSpanId =\n      'parentSpanId' in span\n        ? span.parentSpanId\n        : 'parentSpanContext' in span\n          ? (span.parentSpanContext as { spanId?: string } | undefined)?.spanId\n          : undefined;\n\n    return {\n      span_id,\n      trace_id,\n      data: attributes,\n      description: name,\n      parent_span_id: parentSpanId,\n      start_timestamp: spanTimeInputToSeconds(startTime),\n      // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n      timestamp: spanTimeInputToSeconds(endTime) || undefined,\n      status: getStatusMessage(status),\n      op: attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      origin: attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      links: convertSpanLinksForEnvelope(links),\n    };\n  }\n\n  // Finally, at least we have `spanContext()`....\n  // This should not actually happen in reality, but we need to handle it for type safety.\n  return {\n    span_id,\n    trace_id,\n    start_timestamp: 0,\n    data: {},\n  };\n}\n\nfunction spanIsOpenTelemetrySdkTraceBaseSpan(span: Span): span is OpenTelemetrySdkTraceBaseSpan {\n  const castSpan = span as Partial<OpenTelemetrySdkTraceBaseSpan>;\n  return !!castSpan.attributes && !!castSpan.startTime && !!castSpan.name && !!castSpan.endTime && !!castSpan.status;\n}\n\n/** Exported only for tests. */\nexport interface OpenTelemetrySdkTraceBaseSpan extends Span {\n  attributes: SpanAttributes;\n  startTime: SpanTimeInput;\n  name: string;\n  status: SpanStatus;\n  endTime: SpanTimeInput;\n  parentSpanId?: string;\n  links?: SpanLink[];\n}\n\n/**\n * Sadly, due to circular dependency checks we cannot actually import the Span class here and check for instanceof.\n * :( So instead we approximate this by checking if it has the `getSpanJSON` method.\n */\nfunction spanIsSentrySpan(span: Span): span is SentrySpan {\n  return typeof (span as SentrySpan).getSpanJSON === 'function';\n}\n\n/**\n * Returns true if a span is sampled.\n * In most cases, you should just use `span.isRecording()` instead.\n * However, this has a slightly different semantic, as it also returns false if the span is finished.\n * So in the case where this distinction is important, use this method.\n */\nexport function spanIsSampled(span: Span): boolean {\n  // We align our trace flags with the ones OpenTelemetry use\n  // So we also check for sampled the same way they do.\n  const { traceFlags } = span.spanContext();\n  return traceFlags === TRACE_FLAG_SAMPLED;\n}\n\n/** Get the status message to use for a JSON representation of a span. */\nexport function getStatusMessage(status: SpanStatus | undefined): string | undefined {\n  if (!status || status.code === SPAN_STATUS_UNSET) {\n    return undefined;\n  }\n\n  if (status.code === SPAN_STATUS_OK) {\n    return 'ok';\n  }\n\n  return status.message || 'unknown_error';\n}\n\nconst CHILD_SPANS_FIELD = '_sentryChildSpans';\nconst ROOT_SPAN_FIELD = '_sentryRootSpan';\n\ntype SpanWithPotentialChildren = Span & {\n  [CHILD_SPANS_FIELD]?: Set<Span>;\n  [ROOT_SPAN_FIELD]?: Span;\n};\n\n/**\n * Adds an opaque child span reference to a span.\n */\nexport function addChildSpanToSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  // We store the root span reference on the child span\n  // We need this for `getRootSpan()` to work\n  const rootSpan = span[ROOT_SPAN_FIELD] || span;\n  addNonEnumerableProperty(childSpan as SpanWithPotentialChildren, ROOT_SPAN_FIELD, rootSpan);\n\n  // We store a list of child spans on the parent span\n  // We need this for `getSpanDescendants()` to work\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].add(childSpan);\n  } else {\n    addNonEnumerableProperty(span, CHILD_SPANS_FIELD, new Set([childSpan]));\n  }\n}\n\n/** This is only used internally by Idle Spans. */\nexport function removeChildSpanFromSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].delete(childSpan);\n  }\n}\n\n/**\n * Returns an array of the given span and all of its descendants.\n */\nexport function getSpanDescendants(span: SpanWithPotentialChildren): Span[] {\n  const resultSet = new Set<Span>();\n\n  function addSpanChildren(span: SpanWithPotentialChildren): void {\n    // This exit condition is required to not infinitely loop in case of a circular dependency.\n    if (resultSet.has(span)) {\n      return;\n      // We want to ignore unsampled spans (e.g. non recording spans)\n    } else if (spanIsSampled(span)) {\n      resultSet.add(span);\n      const childSpans = span[CHILD_SPANS_FIELD] ? Array.from(span[CHILD_SPANS_FIELD]) : [];\n      for (const childSpan of childSpans) {\n        addSpanChildren(childSpan);\n      }\n    }\n  }\n\n  addSpanChildren(span);\n\n  return Array.from(resultSet);\n}\n\n/**\n * Returns the root span of a given span.\n */\nexport function getRootSpan(span: SpanWithPotentialChildren): Span {\n  return span[ROOT_SPAN_FIELD] || span;\n}\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getActiveSpan) {\n    return acs.getActiveSpan();\n  }\n\n  return _getSpanForScope(getCurrentScope());\n}\n\n/**\n * Logs a warning once if `beforeSendSpan` is used to drop spans.\n */\nexport function showSpanDropWarning(): void {\n  if (!hasShownSpanDropWarning) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.',\n      );\n    });\n    hasShownSpanDropWarning = true;\n  }\n}\n\n/**\n * Updates the name of the given span and ensures that the span name is not\n * overwritten by the Sentry SDK.\n *\n * Use this function instead of `span.updateName()` if you want to make sure that\n * your name is kept. For some spans, for example root `http.server` spans the\n * Sentry SDK would otherwise overwrite the span name with a high-quality name\n * it infers when the span ends.\n *\n * Use this function in server code or when your span is started on the server\n * and on the client (browser). If you only update a span name on the client,\n * you can also use `span.updateName()` the SDK does not overwrite the name.\n *\n * @param span - The span to update the name of.\n * @param name - The name to set on the span.\n */\nexport function updateSpanName(span: Span, name: string): void {\n  span.updateName(name);\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n    [SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]: name,\n  });\n}\n", "import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getClient } from '../currentScopes';\nimport type { Scope } from '../scope';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { Span } from '../types-hoist/span';\nimport { baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader } from '../utils/baggage';\nimport { extractOrgIdFromDsnHost } from '../utils/dsn';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { addNonEnumerableProperty } from '../utils/object';\nimport { getRootSpan, spanIsSampled, spanToJSO<PERSON> } from '../utils/spanUtils';\nimport { getCapturedScopesOnSpan } from './utils';\n\n/**\n * If you change this value, also update the terser plugin config to\n * avoid minification of the object property!\n */\nconst FROZEN_DSC_FIELD = '_frozenDsc';\n\ntype SpanWithMaybeDsc = Span & {\n  [FROZEN_DSC_FIELD]?: Partial<DynamicSamplingContext> | undefined;\n};\n\n/**\n * Freeze the given DSC on the given span.\n */\nexport function freezeDscOnSpan(span: Span, dsc: Partial<DynamicSamplingContext>): void {\n  const spanWithMaybeDsc = span as SpanWithMaybeDsc;\n  addNonEnumerableProperty(spanWithMaybeDsc, FROZEN_DSC_FIELD, dsc);\n}\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatches the `createDsc` lifecycle hook as a side effect.\n */\nexport function getDynamicSamplingContextFromClient(trace_id: string, client: Client): DynamicSamplingContext {\n  const options = client.getOptions();\n\n  const { publicKey: public_key, host } = client.getDsn() || {};\n\n  let org_id: string | undefined;\n  if (options.orgId) {\n    org_id = String(options.orgId);\n  } else if (host) {\n    org_id = extractOrgIdFromDsnHost(host);\n  }\n\n  // Instead of conditionally adding non-undefined values, we add them and then remove them if needed\n  // otherwise, the order of baggage entries changes, which \"breaks\" a bunch of tests etc.\n  const dsc: DynamicSamplingContext = {\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    public_key,\n    trace_id,\n    org_id,\n  };\n\n  client.emit('createDsc', dsc);\n\n  return dsc;\n}\n\n/**\n * Get the dynamic sampling context for the currently active scopes.\n */\nexport function getDynamicSamplingContextFromScope(client: Client, scope: Scope): Partial<DynamicSamplingContext> {\n  const propagationContext = scope.getPropagationContext();\n  return propagationContext.dsc || getDynamicSamplingContextFromClient(propagationContext.traceId, client);\n}\n\n/**\n * Creates a dynamic sampling context from a span (and client and scope)\n *\n * @param span the span from which a few values like the root span name and sample rate are extracted.\n *\n * @returns a dynamic sampling context\n */\nexport function getDynamicSamplingContextFromSpan(span: Span): Readonly<Partial<DynamicSamplingContext>> {\n  const client = getClient();\n  if (!client) {\n    return {};\n  }\n\n  const rootSpan = getRootSpan(span);\n  const rootSpanJson = spanToJSON(rootSpan);\n  const rootSpanAttributes = rootSpanJson.data;\n  const traceState = rootSpan.spanContext().traceState;\n\n  // The span sample rate that was locally applied to the root span should also always be applied to the DSC, even if the DSC is frozen.\n  // This is so that the downstream traces/services can use parentSampleRate in their `tracesSampler` to make consistent sampling decisions across the entire trace.\n  const rootSpanSampleRate =\n    traceState?.get('sentry.sample_rate') ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE];\n\n  function applyLocalSampleRateToDsc(dsc: Partial<DynamicSamplingContext>): Partial<DynamicSamplingContext> {\n    if (typeof rootSpanSampleRate === 'number' || typeof rootSpanSampleRate === 'string') {\n      dsc.sample_rate = `${rootSpanSampleRate}`;\n    }\n    return dsc;\n  }\n\n  // For core implementation, we freeze the DSC onto the span as a non-enumerable property\n  const frozenDsc = (rootSpan as SpanWithMaybeDsc)[FROZEN_DSC_FIELD];\n  if (frozenDsc) {\n    return applyLocalSampleRateToDsc(frozenDsc);\n  }\n\n  // For OpenTelemetry, we freeze the DSC on the trace state\n  const traceStateDsc = traceState?.get('sentry.dsc');\n\n  // If the span has a DSC, we want it to take precedence\n  const dscOnTraceState = traceStateDsc && baggageHeaderToDynamicSamplingContext(traceStateDsc);\n\n  if (dscOnTraceState) {\n    return applyLocalSampleRateToDsc(dscOnTraceState);\n  }\n\n  // Else, we generate it from the span\n  const dsc = getDynamicSamplingContextFromClient(span.spanContext().traceId, client);\n\n  // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n  const source = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n  // after JSON conversion, txn.name becomes jsonSpan.description\n  const name = rootSpanJson.description;\n  if (source !== 'url' && name) {\n    dsc.transaction = name;\n  }\n\n  // How can we even land here with hasSpansEnabled() returning false?\n  // Otel creates a Non-recording span in Tracing Without Performance mode when handling incoming requests\n  // So we end up with an active span that is not sampled (neither positively nor negatively)\n  if (hasSpansEnabled()) {\n    dsc.sampled = String(spanIsSampled(rootSpan));\n    dsc.sample_rand =\n      // In OTEL we store the sample rand on the trace state because we cannot access scopes for NonRecordingSpans\n      // The Sentry OTEL SpanSampler takes care of writing the sample rand on the root span\n      traceState?.get('sentry.sample_rand') ??\n      // On all other platforms we can actually get the scopes from a root span (we use this as a fallback)\n      getCapturedScopesOnSpan(rootSpan).scope?.getPropagationContext().sampleRand.toString();\n  }\n\n  applyLocalSampleRateToDsc(dsc);\n\n  client.emit('createDsc', dsc, rootSpan);\n\n  return dsc;\n}\n\n/**\n * Convert a Span to a baggage header.\n */\nexport function spanToBaggageHeader(span: Span): string | undefined {\n  const dsc = getDynamicSamplingContextFromSpan(span);\n  return dynamicSamplingContextToSentryBaggageHeader(dsc);\n}\n", "import type { ScopeData } from '../scope';\nimport { getDynamicSamplingContextFromSpan } from '../tracing/dynamicSamplingContext';\nimport type { Breadcrumb } from '../types-hoist/breadcrumb';\nimport type { Event } from '../types-hoist/event';\nimport type { Span } from '../types-hoist/span';\nimport { merge } from './merge';\nimport { getRootSpan, spanToJSON, spanToTraceContext } from './spanUtils';\n\n/**\n * Applies data from the scope to the event and runs all event processors on it.\n */\nexport function applyScopeDataToEvent(event: Event, data: ScopeData): void {\n  const { fingerprint, span, breadcrumbs, sdkProcessingMetadata } = data;\n\n  // Apply general data\n  applyDataToEvent(event, data);\n\n  // We want to set the trace context for normal events only if there isn't already\n  // a trace context on the event. There is a product feature in place where we link\n  // errors with transaction and it relies on that.\n  if (span) {\n    applySpanToEvent(event, span);\n  }\n\n  applyFingerprintToEvent(event, fingerprint);\n  applyBreadcrumbsToEvent(event, breadcrumbs);\n  applySdkMetadataToEvent(event, sdkProcessingMetadata);\n}\n\n/** Merge data of two scopes together. */\nexport function mergeScopeData(data: ScopeData, mergeData: ScopeData): void {\n  const {\n    extra,\n    tags,\n    user,\n    contexts,\n    level,\n    sdkProcessingMetadata,\n    breadcrumbs,\n    fingerprint,\n    eventProcessors,\n    attachments,\n    propagationContext,\n    transactionName,\n    span,\n  } = mergeData;\n\n  mergeAndOverwriteScopeData(data, 'extra', extra);\n  mergeAndOverwriteScopeData(data, 'tags', tags);\n  mergeAndOverwriteScopeData(data, 'user', user);\n  mergeAndOverwriteScopeData(data, 'contexts', contexts);\n\n  data.sdkProcessingMetadata = merge(data.sdkProcessingMetadata, sdkProcessingMetadata, 2);\n\n  if (level) {\n    data.level = level;\n  }\n\n  if (transactionName) {\n    data.transactionName = transactionName;\n  }\n\n  if (span) {\n    data.span = span;\n  }\n\n  if (breadcrumbs.length) {\n    data.breadcrumbs = [...data.breadcrumbs, ...breadcrumbs];\n  }\n\n  if (fingerprint.length) {\n    data.fingerprint = [...data.fingerprint, ...fingerprint];\n  }\n\n  if (eventProcessors.length) {\n    data.eventProcessors = [...data.eventProcessors, ...eventProcessors];\n  }\n\n  if (attachments.length) {\n    data.attachments = [...data.attachments, ...attachments];\n  }\n\n  data.propagationContext = { ...data.propagationContext, ...propagationContext };\n}\n\n/**\n * Merges certain scope data. Undefined values will overwrite any existing values.\n * Exported only for tests.\n */\nexport function mergeAndOverwriteScopeData<\n  Prop extends 'extra' | 'tags' | 'user' | 'contexts' | 'sdkProcessingMetadata',\n  Data extends ScopeData,\n>(data: Data, prop: Prop, mergeVal: Data[Prop]): void {\n  data[prop] = merge(data[prop], mergeVal, 1);\n}\n\n/** Exported only for tests */\nexport function mergeArray<Prop extends 'breadcrumbs' | 'fingerprint'>(\n  event: Event,\n  prop: Prop,\n  mergeVal: ScopeData[Prop],\n): void {\n  const prevVal = event[prop];\n  // If we are not merging any new values,\n  // we only need to proceed if there was an empty array before (as we want to replace it with undefined)\n  if (!mergeVal.length && (!prevVal || prevVal.length)) {\n    return;\n  }\n\n  const merged = [...(prevVal || []), ...mergeVal] as ScopeData[Prop];\n  event[prop] = merged.length ? merged : undefined;\n}\n\nfunction applyDataToEvent(event: Event, data: ScopeData): void {\n  const { extra, tags, user, contexts, level, transactionName } = data;\n\n  if (Object.keys(extra).length) {\n    event.extra = { ...extra, ...event.extra };\n  }\n\n  if (Object.keys(tags).length) {\n    event.tags = { ...tags, ...event.tags };\n  }\n\n  if (Object.keys(user).length) {\n    event.user = { ...user, ...event.user };\n  }\n\n  if (Object.keys(contexts).length) {\n    event.contexts = { ...contexts, ...event.contexts };\n  }\n\n  if (level) {\n    event.level = level;\n  }\n\n  // transaction events get their `transaction` from the root span name\n  if (transactionName && event.type !== 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\nfunction applyBreadcrumbsToEvent(event: Event, breadcrumbs: Breadcrumb[]): void {\n  const mergedBreadcrumbs = [...(event.breadcrumbs || []), ...breadcrumbs];\n  event.breadcrumbs = mergedBreadcrumbs.length ? mergedBreadcrumbs : undefined;\n}\n\nfunction applySdkMetadataToEvent(event: Event, sdkProcessingMetadata: ScopeData['sdkProcessingMetadata']): void {\n  event.sdkProcessingMetadata = {\n    ...event.sdkProcessingMetadata,\n    ...sdkProcessingMetadata,\n  };\n}\n\nfunction applySpanToEvent(event: Event, span: Span): void {\n  event.contexts = {\n    trace: spanToTraceContext(span),\n    ...event.contexts,\n  };\n\n  event.sdkProcessingMetadata = {\n    dynamicSamplingContext: getDynamicSamplingContextFromSpan(span),\n    ...event.sdkProcessingMetadata,\n  };\n\n  const rootSpan = getRootSpan(span);\n  const transactionName = spanToJSON(rootSpan).description;\n  if (transactionName && !event.transaction && event.type === 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\n/**\n * Applies fingerprint from the scope to the event if there's one,\n * uses message if there's one instead or get rid of empty fingerprint\n */\nfunction applyFingerprintToEvent(event: Event, fingerprint: ScopeData['fingerprint'] | undefined): void {\n  // Make sure it's an array first and we actually have something in place\n  event.fingerprint = event.fingerprint\n    ? Array.isArray(event.fingerprint)\n      ? event.fingerprint\n      : [event.fingerprint]\n    : [];\n\n  // If we have something on the scope, then merge it with event\n  if (fingerprint) {\n    event.fingerprint = event.fingerprint.concat(fingerprint);\n  }\n\n  // If we have no data at all, remove empty array default\n  if (!event.fingerprint.length) {\n    delete event.fingerprint;\n  }\n}\n", "import type { DebugImage } from '../types-hoist/debugMeta';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { GLOBAL_OBJ } from './worldwide';\n\ntype StackString = string;\ntype CachedResult = [string, string];\n\nlet parsedStackResults: Record<StackString, CachedResult> | undefined;\nlet lastKeysCount: number | undefined;\nlet cachedFilenameDebugIds: Record<string, string> | undefined;\n\n/**\n * Returns a map of filenames to debug identifiers.\n */\nexport function getFilenameToDebugIdMap(stackParser: StackParser): Record<string, string> {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n  if (!debugIdMap) {\n    return {};\n  }\n\n  const debugIdKeys = Object.keys(debugIdMap);\n\n  // If the count of registered globals hasn't changed since the last call, we\n  // can just return the cached result.\n  if (cachedFilenameDebugIds && debugIdKeys.length === lastKeysCount) {\n    return cachedFilenameDebugIds;\n  }\n\n  lastKeysCount = debugIdKeys.length;\n\n  // Build a map of filename -> debug_id.\n  cachedFilenameDebugIds = debugIdKeys.reduce<Record<string, string>>((acc, stackKey) => {\n    if (!parsedStackResults) {\n      parsedStackResults = {};\n    }\n\n    const result = parsedStackResults[stackKey];\n\n    if (result) {\n      acc[result[0]] = result[1];\n    } else {\n      const parsedStack = stackParser(stackKey);\n\n      for (let i = parsedStack.length - 1; i >= 0; i--) {\n        const stackFrame = parsedStack[i];\n        const filename = stackFrame?.filename;\n        const debugId = debugIdMap[stackKey];\n\n        if (filename && debugId) {\n          acc[filename] = debugId;\n          parsedStackResults[stackKey] = [filename, debugId];\n          break;\n        }\n      }\n    }\n\n    return acc;\n  }, {});\n\n  return cachedFilenameDebugIds;\n}\n\n/**\n * Returns a list of debug images for the given resources.\n */\nexport function getDebugImagesForResources(\n  stackParser: StackParser,\n  resource_paths: ReadonlyArray<string>,\n): DebugImage[] {\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  if (!filenameDebugIdMap) {\n    return [];\n  }\n\n  const images: DebugImage[] = [];\n  for (const path of resource_paths) {\n    if (path && filenameDebugIdMap[path]) {\n      images.push({\n        type: 'sourcemap',\n        code_file: path,\n        debug_id: filenameDebugIdMap[path] as string,\n      });\n    }\n  }\n\n  return images;\n}\n", "import type { Event } from '../types-hoist/event';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport type { <PERSON>ack<PERSON>ineParser, StackParser } from '../types-hoist/stacktrace';\n\nconst STACKTRACE_FRAME_LIMIT = 50;\nexport const UNKNOWN_FUNCTION = '?';\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirstLines: number = 0, framesToPop: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i] as string;\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT + framesToPop) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames.slice(framesToPop));\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the scope itself, making it:\n    //\n    //   Sentry.captureException()\n    //   scope.captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION,\n  }));\n}\n\nfunction getLastStackFrame(arr: StackFrame[]): StackFrame {\n  return arr[arr.length - 1] || {};\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Get's stack frames from an event without needing to check for undefined properties.\n */\nexport function getFramesFromEvent(event: Event): StackFrame[] | undefined {\n  const exception = event.exception;\n\n  if (exception) {\n    const frames: StackFrame[] = [];\n    try {\n      // @ts-expect-error Object could be undefined\n      exception.values.forEach(value => {\n        // @ts-expect-error Value could be undefined\n        if (value.stacktrace.frames) {\n          // @ts-expect-error Value could be undefined\n          frames.push(...value.stacktrace.frames);\n        }\n      });\n      return frames;\n    } catch (_oO) {\n      return undefined;\n    }\n  }\n  return undefined;\n}\n", "import type { Primitive } from '../types-hoist/misc';\nimport { isSyntheticEvent, isVueViewModel } from './is';\nimport { convertToPlainObject } from './object';\nimport { getFunctionName } from './stacktrace';\n\ntype Prototype = { constructor?: (...args: unknown[]) => unknown };\n// This is a hack to placate TS, relying on the fact that technically, arrays are objects with integer keys. Normally we\n// think of those keys as actual numbers, but `arr['0']` turns out to work just as well as `arr[0]`, and doing it this\n// way lets us use a single type in the places where behave as if we are only dealing with objects, even if some of them\n// might be arrays.\ntype ObjOrArray<T> = { [key: string]: T };\n\ntype MemoFunc = [\n  // memoize\n  (obj: object) => boolean,\n  // unmemoize\n  (obj: object) => void,\n];\n\n/**\n * Recursively normalizes the given object.\n *\n * - Creates a copy to prevent original input mutation\n * - Skips non-enumerable properties\n * - When stringifying, calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializable values (`undefined`/`NaN`/functions) to serializable format\n * - Translates known global objects/classes to a string representations\n * - Takes care of `Error` object serialization\n * - Optionally limits depth of final output\n * - Optionally limits number of properties/elements included in any single object/array\n *\n * @param input The object to be normalized.\n * @param depth The max depth to which to normalize the object. (Anything deeper stringified whole.)\n * @param maxProperties The max number of elements or properties to be included in any single array or\n * object in the normalized output.\n * @returns A normalized version of the object, or `\"**non-serializable**\"` if any errors are thrown during normalization.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function normalize(input: unknown, depth: number = 100, maxProperties: number = +Infinity): any {\n  try {\n    // since we're at the outermost level, we don't provide a key\n    return visit('', input, depth, maxProperties);\n  } catch (err) {\n    return { ERROR: `**non-serializable** (${err})` };\n  }\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const normalized = normalize(object, depth);\n\n  if (jsonSize(normalized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return normalized as T;\n}\n\n/**\n * Visits a node to perform normalization on it\n *\n * @param key The key corresponding to the given node\n * @param value The node to be visited\n * @param depth Optional number indicating the maximum recursion depth\n * @param maxProperties Optional maximum number of properties/elements included in any single object/array\n * @param memo Optional Memo class handling decycling\n */\nfunction visit(\n  key: string,\n  value: unknown,\n  depth: number = +Infinity,\n  maxProperties: number = +Infinity,\n  memo = memoBuilder(),\n): Primitive | ObjOrArray<unknown> {\n  const [memoize, unmemoize] = memo;\n\n  // Get the simple cases out of the way first\n  if (\n    value == null || // this matches null and undefined -> eqeq not eqeqeq\n    ['boolean', 'string'].includes(typeof value) ||\n    (typeof value === 'number' && Number.isFinite(value))\n  ) {\n    return value as Primitive;\n  }\n\n  const stringified = stringifyValue(key, value);\n\n  // Anything we could potentially dig into more (objects or arrays) will have come back as `\"[object XXXX]\"`.\n  // Everything else will have already been serialized, so if we don't see that pattern, we're done.\n  if (!stringified.startsWith('[object ')) {\n    return stringified;\n  }\n\n  // From here on, we can assert that `value` is either an object or an array.\n\n  // Do not normalize objects that we know have already been normalized. As a general rule, the\n  // \"__sentry_skip_normalization__\" property should only be used sparingly and only should only be set on objects that\n  // have already been normalized.\n  if ((value as ObjOrArray<unknown>)['__sentry_skip_normalization__']) {\n    return value as ObjOrArray<unknown>;\n  }\n\n  // We can set `__sentry_override_normalization_depth__` on an object to ensure that from there\n  // We keep a certain amount of depth.\n  // This should be used sparingly, e.g. we use it for the redux integration to ensure we get a certain amount of state.\n  const remainingDepth =\n    typeof (value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] === 'number'\n      ? ((value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] as number)\n      : depth;\n\n  // We're also done if we've reached the max depth\n  if (remainingDepth === 0) {\n    // At this point we know `serialized` is a string of the form `\"[object XXXX]\"`. Clean it up so it's just `\"[XXXX]\"`.\n    return stringified.replace('object ', '');\n  }\n\n  // If we've already visited this branch, bail out, as it's circular reference. If not, note that we're seeing it now.\n  if (memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // If the value has a `toJSON` method, we call it to extract more information\n  const valueWithToJSON = value as unknown & { toJSON?: () => unknown };\n  if (valueWithToJSON && typeof valueWithToJSON.toJSON === 'function') {\n    try {\n      const jsonValue = valueWithToJSON.toJSON();\n      // We need to normalize the return value of `.toJSON()` in case it has circular references\n      return visit('', jsonValue, remainingDepth - 1, maxProperties, memo);\n    } catch (err) {\n      // pass (The built-in `toJSON` failed, but we can still try to do it ourselves)\n    }\n  }\n\n  // At this point we know we either have an object or an array, we haven't seen it before, and we're going to recurse\n  // because we haven't yet reached the max depth. Create an accumulator to hold the results of visiting each\n  // property/entry, and keep track of the number of items we add to it.\n  const normalized = (Array.isArray(value) ? [] : {}) as ObjOrArray<unknown>;\n  let numAdded = 0;\n\n  // Before we begin, convert`Error` and`Event` instances into plain objects, since some of each of their relevant\n  // properties are non-enumerable and otherwise would get missed.\n  const visitable = convertToPlainObject(value as ObjOrArray<unknown>);\n\n  for (const visitKey in visitable) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(visitable, visitKey)) {\n      continue;\n    }\n\n    if (numAdded >= maxProperties) {\n      normalized[visitKey] = '[MaxProperties ~]';\n      break;\n    }\n\n    // Recursively visit all the child nodes\n    const visitValue = visitable[visitKey];\n    normalized[visitKey] = visit(visitKey, visitValue, remainingDepth - 1, maxProperties, memo);\n\n    numAdded++;\n  }\n\n  // Once we've visited all the branches, remove the parent from memo storage\n  unmemoize(value);\n\n  // Return accumulated values\n  return normalized;\n}\n\n/* eslint-disable complexity */\n/**\n * Stringify the given value. Handles various known special values and types.\n *\n * Not meant to be used on simple primitives which already have a string representation, as it will, for example, turn\n * the number 1231 into \"[Object Number]\", nor on `null`, as it will throw.\n *\n * @param value The value to stringify\n * @returns A stringified representation of the given value\n */\nfunction stringifyValue(\n  key: unknown,\n  // this type is a tiny bit of a cheat, since this function does handle NaN (which is technically a number), but for\n  // our internal use, it'll do\n  value: Exclude<unknown, string | number | boolean | null>,\n): string {\n  try {\n    if (key === 'domain' && value && typeof value === 'object' && (value as { _events: unknown })._events) {\n      return '[Domain]';\n    }\n\n    if (key === 'domainEmitter') {\n      return '[DomainEmitter]';\n    }\n\n    // It's safe to use `global`, `window`, and `document` here in this manner, as we are asserting using `typeof` first\n    // which won't throw if they are not present.\n\n    if (typeof global !== 'undefined' && value === global) {\n      return '[Global]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof window !== 'undefined' && value === window) {\n      return '[Window]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof document !== 'undefined' && value === document) {\n      return '[Document]';\n    }\n\n    if (isVueViewModel(value)) {\n      return '[VueViewModel]';\n    }\n\n    // React's SyntheticEvent thingy\n    if (isSyntheticEvent(value)) {\n      return '[SyntheticEvent]';\n    }\n\n    if (typeof value === 'number' && !Number.isFinite(value)) {\n      return `[${value}]`;\n    }\n\n    if (typeof value === 'function') {\n      return `[Function: ${getFunctionName(value)}]`;\n    }\n\n    if (typeof value === 'symbol') {\n      return `[${String(value)}]`;\n    }\n\n    // stringified BigInts are indistinguishable from regular numbers, so we need to label them to avoid confusion\n    if (typeof value === 'bigint') {\n      return `[BigInt: ${String(value)}]`;\n    }\n\n    // Now that we've knocked out all the special cases and the primitives, all we have left are objects. Simply casting\n    // them to strings means that instances of classes which haven't defined their `toStringTag` will just come out as\n    // `\"[object Object]\"`. If we instead look at the constructor's name (which is the same as the name of the class),\n    // we can make sure that only plain objects come out that way.\n    const objName = getConstructorName(value);\n\n    // Handle HTML Elements\n    if (/^HTML(\\w*)Element$/.test(objName)) {\n      return `[HTMLElement: ${objName}]`;\n    }\n\n    return `[object ${objName}]`;\n  } catch (err) {\n    return `**non-serializable** (${err})`;\n  }\n}\n/* eslint-enable complexity */\n\nfunction getConstructorName(value: unknown): string {\n  const prototype: Prototype | null = Object.getPrototypeOf(value);\n\n  return prototype?.constructor ? prototype.constructor.name : 'null prototype';\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n\n/**\n * Normalizes URLs in exceptions and stacktraces to a base path so Sentry can fingerprint\n * across platforms and working directory.\n *\n * @param url The URL to be normalized.\n * @param basePath The application base path.\n * @returns The normalized URL.\n */\nexport function normalizeUrlToBase(url: string, basePath: string): string {\n  const escapedBase = basePath\n    // Backslash to forward\n    .replace(/\\\\/g, '/')\n    // Escape RegExp special characters\n    .replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&');\n\n  let newUrl = url;\n  try {\n    newUrl = decodeURI(url);\n  } catch (_Oo) {\n    // Sometime this breaks\n  }\n  return (\n    newUrl\n      .replace(/\\\\/g, '/')\n      .replace(/webpack:\\/?/g, '') // Remove intermediate base path\n      // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n      .replace(new RegExp(`(file://)?/*${escapedBase}/*`, 'ig'), 'app:///')\n  );\n}\n\n/**\n * Helper to decycle json objects\n */\nfunction memoBuilder(): MemoFunc {\n  const inner = new WeakSet<object>();\n  function memoize(obj: object): boolean {\n    if (inner.has(obj)) {\n      return true;\n    }\n    inner.add(obj);\n    return false;\n  }\n\n  function unmemoize(obj: object): void {\n    inner.delete(obj);\n  }\n  return [memoize, unmemoize];\n}\n", "import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getGlobalScope } from '../currentScopes';\nimport { notifyEventProcessors } from '../eventProcessors';\nimport type { CaptureContext, ScopeContext } from '../scope';\nimport { Scope } from '../scope';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { ClientOptions } from '../types-hoist/options';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { applyScopeDataToEvent, mergeScopeData } from './applyScopeDataToEvent';\nimport { getFilenameToDebugIdMap } from './debug-ids';\nimport { addExceptionMechanism, uuid4 } from './misc';\nimport { normalize } from './normalize';\nimport { truncate } from './string';\nimport { dateTimestampInSeconds } from './time';\n\n/**\n * This type makes sure that we get either a CaptureContext, OR an EventHint.\n * It does not allow mixing them, which could lead to unexpected outcomes, e.g. this is disallowed:\n * { user: { id: '123' }, mechanism: { handled: false } }\n */\nexport type ExclusiveEventHintOrCaptureContext =\n  | (CaptureContext & Partial<{ [key in keyof EventHint]: never }>)\n  | (EventHint & Partial<{ [key in keyof ScopeContext]: never }>);\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nexport function prepareEvent(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n  scope?: Scope,\n  client?: Client,\n  isolationScope?: Scope,\n): PromiseLike<Event | null> {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = options;\n  const prepared: Event = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  if (client) {\n    client.emit('applyFrameMetadata', event);\n  }\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  const finalScope = getFinalScope(scope, hint.captureContext);\n\n  if (hint.mechanism) {\n    addExceptionMechanism(prepared, hint.mechanism);\n  }\n\n  const clientEventProcessors = client ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Scope.addEventProcessor} gets the finished prepared event.\n  // Merge scope data together\n  const data = getGlobalScope().getScopeData();\n\n  if (isolationScope) {\n    const isolationData = isolationScope.getScopeData();\n    mergeScopeData(data, isolationData);\n  }\n\n  if (finalScope) {\n    const finalScopeData = finalScope.getScopeData();\n    mergeScopeData(data, finalScopeData);\n  }\n\n  const attachments = [...(hint.attachments || []), ...data.attachments];\n  if (attachments.length) {\n    hint.attachments = attachments;\n  }\n\n  applyScopeDataToEvent(prepared, data);\n\n  const eventProcessors = [\n    ...clientEventProcessors,\n    // Run scope event processors _after_ all other processors\n    ...data.eventProcessors,\n  ];\n\n  const result = notifyEventProcessors(eventProcessors, prepared, hint);\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n * Enhances event using the client configuration.\n * It takes care of all \"static\" values like environment, release and `dist`,\n * as well as truncating overly long values.\n *\n * Only exported for tests.\n *\n * @param event event instance to be enhanced\n */\nexport function applyClientOptions(event: Event, options: ClientOptions): void {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  // empty strings do not make sense for environment, release, and dist\n  // so we handle them the same as if they were not provided\n  event.environment = event.environment || environment || DEFAULT_ENVIRONMENT;\n\n  if (!event.release && release) {\n    event.release = release;\n  }\n\n  if (!event.dist && dist) {\n    event.dist = dist;\n  }\n\n  const request = event.request;\n  if (request?.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nexport function applyDebugIds(event: Event, stackParser: StackParser): void {\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.filename) {\n        frame.debug_id = filenameDebugIdMap[frame.filename];\n      }\n    });\n  });\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nexport function applyDebugMeta(event: Event): void {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap: Record<string, string> = {};\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.debug_id) {\n        if (frame.abs_path) {\n          filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n        } else if (frame.filename) {\n          filenameDebugIdMap[frame.filename] = frame.debug_id;\n        }\n        delete frame.debug_id;\n      }\n    });\n  });\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.entries(filenameDebugIdMap).forEach(([filename, debug_id]) => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id,\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event: Event, integrationNames: string[]): void {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n  if (!event) {\n    return null;\n  }\n\n  const normalized: Event = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts?.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      return {\n        ...span,\n        ...(span.data && {\n          data: normalize(span.data, depth, maxBreadth),\n        }),\n      };\n    });\n  }\n\n  // event.contexts.flags (FeatureFlagContext) stores context for our feature\n  // flag integrations. It has a greater nesting depth than our other typed\n  // Contexts, so we re-normalize with a fixed depth of 3 here. We do not want\n  // to skip this in case of conflicting, user-provided context.\n  if (event.contexts?.flags && normalized.contexts) {\n    normalized.contexts.flags = normalize(event.contexts.flags, 3, maxBreadth);\n  }\n\n  return normalized;\n}\n\nfunction getFinalScope(scope: Scope | undefined, captureContext: CaptureContext | undefined): Scope | undefined {\n  if (!captureContext) {\n    return scope;\n  }\n\n  const finalScope = scope ? scope.clone() : new Scope();\n  finalScope.update(captureContext);\n  return finalScope;\n}\n\n/**\n * Parse either an `EventHint` directly, or convert a `CaptureContext` to an `EventHint`.\n * This is used to allow to update method signatures that used to accept a `CaptureContext` but should now accept an `EventHint`.\n */\nexport function parseEventHintOrCaptureContext(\n  hint: ExclusiveEventHintOrCaptureContext | undefined,\n): EventHint | undefined {\n  if (!hint) {\n    return undefined;\n  }\n\n  // If you pass a Scope or `() => Scope` as CaptureContext, we just return this as captureContext\n  if (hintIsScopeOrFunction(hint)) {\n    return { captureContext: hint };\n  }\n\n  if (hintIsScopeContext(hint)) {\n    return {\n      captureContext: hint,\n    };\n  }\n\n  return hint;\n}\n\nfunction hintIsScopeOrFunction(hint: CaptureContext | EventHint): hint is Scope | ((scope: Scope) => Scope) {\n  return hint instanceof Scope || typeof hint === 'function';\n}\n\ntype ScopeContextProperty = keyof ScopeContext;\nconst captureContextKeys: readonly ScopeContextProperty[] = [\n  'user',\n  'level',\n  'extra',\n  'contexts',\n  'tags',\n  'fingerprint',\n  'propagationContext',\n] as const;\n\nfunction hintIsScopeContext(hint: Partial<ScopeContext> | EventHint): hint is Partial<ScopeContext> {\n  return Object.keys(hint).some(key => captureContextKeys.includes(key as ScopeContextProperty));\n}\n", "import { getClient, getCurrentScope, getIsolationScope, withIsolationScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { CaptureContext } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\nimport type { CheckIn, FinishedCheckIn, MonitorConfig } from './types-hoist/checkin';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { Session, SessionContext } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { User } from './types-hoist/user';\nimport { isThenable } from './utils/is';\nimport { logger } from './utils/logger';\nimport { uuid4 } from './utils/misc';\nimport type { ExclusiveEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { parseEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { timestampInSeconds } from './utils/time';\nimport { GLOBAL_OBJ } from './utils/worldwide';\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nexport function captureException(exception: unknown, hint?: ExclusiveEventHintOrCaptureContext): string {\n  return getCurrentScope().captureException(exception, parseEventHintOrCaptureContext(hint));\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param captureContext Define the level of the message or pass in additional data to attach to the message.\n * @returns the id of the captured message.\n */\nexport function captureMessage(message: string, captureContext?: CaptureContext | SeverityLevel): string {\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n  return getCurrentScope().captureMessage(message, level, context);\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured event.\n */\nexport function captureEvent(event: Event, hint?: EventHint): string {\n  return getCurrentScope().captureEvent(event, hint);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\nexport function setContext(name: string, context: { [key: string]: unknown } | null): void {\n  getIsolationScope().setContext(name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nexport function setExtras(extras: Extras): void {\n  getIsolationScope().setExtras(extras);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nexport function setExtra(key: string, extra: Extra): void {\n  getIsolationScope().setExtra(key, extra);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nexport function setTags(tags: { [key: string]: Primitive }): void {\n  getIsolationScope().setTags(tags);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nexport function setTag(key: string, value: Primitive): void {\n  getIsolationScope().setTag(key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nexport function setUser(user: User | null): void {\n  getIsolationScope().setUser(user);\n}\n\n/**\n * The last error event id of the isolation scope.\n *\n * Warning: This function really returns the last recorded error event id on the current\n * isolation scope. If you call this function after handling a certain error and another error\n * is captured in between, the last one is returned instead of the one you might expect.\n * Also, ids of events that were never sent to Sentry (for example because\n * they were dropped in `beforeSend`) could be returned.\n *\n * @returns The last event id of the isolation scope.\n */\nexport function lastEventId(): string | undefined {\n  return getIsolationScope().lastEventId();\n}\n\n/**\n * Create a cron monitor check in and send it to Sentry.\n *\n * @param checkIn An object that describes a check in.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function captureCheckIn(checkIn: CheckIn, upsertMonitorConfig?: MonitorConfig): string {\n  const scope = getCurrentScope();\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. No client defined.');\n  } else if (!client.captureCheckIn) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. Client does not support sending check-ins.');\n  } else {\n    return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);\n  }\n\n  return uuid4();\n}\n\n/**\n * Wraps a callback with a cron monitor check in. The check in will be sent to Sentry when the callback finishes.\n *\n * @param monitorSlug The distinct slug of the monitor.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function withMonitor<T>(\n  monitorSlug: CheckIn['monitorSlug'],\n  callback: () => T,\n  upsertMonitorConfig?: MonitorConfig,\n): T {\n  const checkInId = captureCheckIn({ monitorSlug, status: 'in_progress' }, upsertMonitorConfig);\n  const now = timestampInSeconds();\n\n  function finishCheckIn(status: FinishedCheckIn['status']): void {\n    captureCheckIn({ monitorSlug, status, checkInId, duration: timestampInSeconds() - now });\n  }\n\n  return withIsolationScope(() => {\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback();\n    } catch (e) {\n      finishCheckIn('error');\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      Promise.resolve(maybePromiseResult).then(\n        () => {\n          finishCheckIn('ok');\n        },\n        e => {\n          finishCheckIn('error');\n          throw e;\n        },\n      );\n    } else {\n      finishCheckIn('ok');\n    }\n\n    return maybePromiseResult;\n  });\n}\n\n/**\n * Call `flush()` on the current client, if there is one. See {@link Client.flush}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause\n * the client to wait until all events are sent before resolving the promise.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function flush(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.flush(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Call `close()` on the current client, if there is one. See {@link Client.close}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this\n * parameter will cause the client to wait until all events are sent before disabling itself.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function close(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.close(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events and disable SDK. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Returns true if Sentry has been properly initialized.\n */\nexport function isInitialized(): boolean {\n  return !!getClient();\n}\n\n/** If the SDK is initialized & enabled. */\nexport function isEnabled(): boolean {\n  const client = getClient();\n  return client?.getOptions().enabled !== false && !!client?.getTransport();\n}\n\n/**\n * Add an event processor.\n * This will be added to the current isolation scope, ensuring any event that is processed in the current execution\n * context will have the processor applied.\n */\nexport function addEventProcessor(callback: EventProcessor): void {\n  getIsolationScope().addEventProcessor(callback);\n}\n\n/**\n * Start a session on the current isolation scope.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns the new active session\n */\nexport function startSession(context?: SessionContext): Session {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  // Will fetch userAgent if called from browser sdk\n  const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n  const session = makeSession({\n    user: currentScope.getUser() || isolationScope.getUser(),\n    ...(userAgent && { userAgent }),\n    ...context,\n  });\n\n  // End existing session if there's one\n  const currentSession = isolationScope.getSession();\n  if (currentSession?.status === 'ok') {\n    updateSession(currentSession, { status: 'exited' });\n  }\n\n  endSession();\n\n  // Afterwards we set the new session on the scope\n  isolationScope.setSession(session);\n\n  return session;\n}\n\n/**\n * End the session on the current isolation scope.\n */\nexport function endSession(): void {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session) {\n    closeSession(session);\n  }\n  _sendSessionUpdate();\n\n  // the session is over; take it off of the scope\n  isolationScope.setSession();\n}\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate(): void {\n  const isolationScope = getIsolationScope();\n  const client = getClient();\n  const session = isolationScope.getSession();\n  if (session && client) {\n    client.captureSession(session);\n  }\n}\n\n/**\n * Sends the current session on the scope to Sentry\n *\n * @param end If set the session will be marked as exited and removed from the scope.\n *            Defaults to `false`.\n */\nexport function captureSession(end: boolean = false): void {\n  // both send the update and pull the session from the scope\n  if (end) {\n    endSession();\n    return;\n  }\n\n  // only send the update\n  _sendSessionUpdate();\n}\n", "import { GLOBAL_OBJ } from './worldwide';\n\ninterface VercelRequestContextGlobal {\n  get?():\n    | {\n        waitUntil?: (task: Promise<unknown>) => void;\n      }\n    | undefined;\n}\n\n/**\n * Function that delays closing of a Vercel lambda until the provided promise is resolved.\n *\n * Vendored from https://www.npmjs.com/package/@vercel/functions\n */\nexport function vercelWaitUntil(task: Promise<unknown>): void {\n  const vercelRequestContextGlobal: VercelRequestContextGlobal | undefined =\n    // @ts-expect-error This is not typed\n    GLOBAL_OBJ[Symbol.for('@vercel/request-context')];\n\n  const ctx =\n    vercelRequestContextGlobal?.get && vercelRequestContextGlobal.get() ? vercelRequestContextGlobal.get() : {};\n\n  if (ctx?.waitUntil) {\n    ctx.waitUntil(task);\n  }\n}\n", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "import type { Span } from '@sentry/core';\nimport { fill, flush, logger, setHttpStatus } from '@sentry/core';\nimport type { ServerResponse } from 'http';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { ResponseEndMethod, WrappedResponseEndMethod } from '../types';\n\n/**\n * Wrap `res.end()` so that it ends the span and flushes events before letting the request finish.\n *\n * Note: This wraps a sync method with an async method. While in general that's not a great idea in terms of keeping\n * things in the right order, in this case it's safe, because the native `.end()` actually *is* (effectively) async, and\n * its run actually *is* (literally) awaited, just manually so (which reflects the fact that the core of the\n * request/response code in Node by far predates the introduction of `async`/`await`). When `.end()` is done, it emits\n * the `prefinish` event, and only once that fires does request processing continue. See\n * https://github.com/nodejs/node/commit/7c9b607048f13741173d397795bac37707405ba7.\n *\n * Also note: `res.end()` isn't called until *after* all response data and headers have been sent, so blocking inside of\n * `end` doesn't delay data getting to the end user. See\n * https://nodejs.org/api/http.html#responseenddata-encoding-callback.\n *\n * @param span The span tracking the request\n * @param res: The request's corresponding response\n */\nexport function autoEndSpanOnResponseEnd(span: Span, res: ServerResponse): void {\n  const wrapEndMethod = (origEnd: ResponseEndMethod): WrappedResponseEndMethod => {\n    return function sentryWrappedEnd(this: ServerResponse, ...args: unknown[]) {\n      finishSpan(span, this);\n      return origEnd.call(this, ...args);\n    };\n  };\n\n  // Prevent double-wrapping\n  // res.end may be undefined during build when using `next export` to statically export a Next.js app\n  if (res.end && !(res.end as WrappedResponseEndMethod).__sentry_original__) {\n    fill(res, 'end', wrapEndMethod);\n  }\n}\n\n/** Finish the given response's span and set HTTP status data */\nexport function finishSpan(span: Span, res: ServerResponse): void {\n  setHttpStatus(span, res.statusCode);\n  span.end();\n}\n\n/**\n * Flushes pending Sentry events with a 2 second timeout and in a way that cannot create unhandled promise rejections.\n */\nexport async function flushSafelyWithTimeout(): Promise<void> {\n  try {\n    DEBUG_BUILD && logger.log('Flushing events...');\n    await flush(2000);\n    DEBUG_BUILD && logger.log('Done flushing events');\n  } catch (e) {\n    DEBUG_BUILD && logger.log('Error while flushing events:\\n', e);\n  }\n}\n"], "names": [], "mappings": "2KAGO,IAAM,EAA2D,QAAuB,CAAtE,kDC8DzB,EAAA,CAAA,CAAA,oBACO,IAAM,EAAa,QAAF,EAAa,6JCzB9B,SAAS,IAGd,OADA,EAAgB,CAFY,CAEX,EAFuB,QAEb,CAAC,CAC5B,AADgB,EACT,UAAU,AACnB,CAGO,SAAS,EAAiB,CAAO,EAA0B,AAChE,IAAM,EAAc,EAAQ,GADE,EACH,CAArB,IAAsB,CAAa,EAAQ,KAAD,KAAC,EAAc,CAAA,CAAE,CAAC,AAOlE,OAJA,EAAW,OAAQ,CAAT,AAAW,EAAW,OAAA,CAAD,CAAC,EAAW,WAAW,CAI9C,CAAU,CAAA,EAAC,WAAW,CAAE,CAAE,CAAU,CAAA,EAAC,WAAW,CAAA,EAAK,CAAA,CAAE,AACjE,CAaO,SAAS,EACd,CAAI,CACJ,CAAO,CACP,EAAA,CAAA,CAAM,QAH0B,EAGhB,EAEhB,IAAM,EAAc,EAAI,CAAD,KAAjB,IAAkB,CAAa,EAAI,CAAD,SAAC,EAAc,CAAA,CAAE,CACnD,AADoD,EACzC,CAAU,CAAA,EAAC,CAAd,UAAyB,CAAA,CAAI,CAAU,CAAA,EAAC,WAAW,CAAA,EAAK,CAAA,CAAE,CAAC,AAEzE,OAAO,CAAO,CAAC,EAAI,EAAA,AAAM,EAAN,AAAa,CAAC,EAAM,CAAE,CAAJ,EAAW,CAAE,AACpD,CADqD,EAAH,gDCxElD,EAAA,CAAA,CAAA,qBACO,IAAM,EAA6B,SAAjB,EAAiB,EAAA,OAAA,gBAAA,EAAA,gBAAA,+VCA1C,IAAM,EAAiB,MAAM,CAAC,SAAS,CAAC,QAAQ,CASzC,SAAS,EAAQ,CAAG,EACzB,AADkD,EAA7B,KACb,EAAe,IAAI,CAAC,GAAG,CAC7B,AAD8B,GAAV,CACf,gBAAgB,CACrB,IAAK,oBAAoB,CACzB,IAAK,uBAAuB,CAC5B,IAAK,gCAAgC,CACnC,OAAO,CACT,GADa,MAEX,OAAO,EAAa,EAAK,CAAF,IAAO,CAAC,AACrC,CACA,CAFyB,AAUzB,SAAS,EAAU,CAAG,CAAW,CAAS,EAAmB,AAC3D,EADgB,KACT,EAAe,IAAI,CAAC,GAAG,CAAA,CAAM,CAAC,CAAhB,OAAwB,EAAE,EAAU,CAAC,CAAC,AAC7D,CASO,IAVmD,KAU1C,EAAa,CAAG,EAAoB,AAClD,OAD0B,AACnB,EAAU,EAAK,CAAF,IAAJ,OAAkB,CAAC,AACrC,CASO,SAAS,EAAW,CAAG,EAAoB,AAChD,KADwB,EACjB,EAAU,EAAK,CAAF,IAAJ,KAAgB,CAAC,AACnC,CASO,SAAS,EAAe,CAAG,EAChC,AADoD,OAC7C,EAAU,AADW,EACN,CAAF,IAAJ,SAAoB,CAAC,AACvC,CASO,SAAS,EAAS,CAAG,EAA0B,AACpD,GADsB,IACf,EAAU,EAAK,CAAF,IAAJ,GAAc,CAChC,AADiC,CAU1B,SAAS,EAAsB,CAAG,EAAuC,AAC9E,MACiB,QAAS,EAAxB,AAFiC,OAE1B,GAAI,AACH,IAAK,GAAb,GAAA,AACA,4BAAA,GAAgC,GAAI,AACpC,+BAAgC,CAEpC,CASO,SAAS,EAAY,CAAG,EAA6B,AAC1D,MADyB,CACV,IAAA,GAAR,GAAgB,EAAsB,GAAG,CAAqB,AAAnB,QAA4B,EAAxB,KAAV,EAAiB,GAAA,AAAoB,AAAe,UAAU,CAAC,QAAnB,CAC1F,CASO,CAVuF,QAU9E,EAAc,CAAG,EAA2C,AAC1E,OAAO,CADoB,CACV,EAAK,CAAF,IAAJ,GAAc,CAAC,AACjC,CASO,SAAS,EAAQ,CAAG,EAAoC,AAC7D,EADqB,IACG,WAAA,EAAjB,OAAO,KAAA,EAAyB,EAAa,EAAK,CAAF,IAAO,CAAC,AACjE,CASO,CAV8C,QAUrC,EAAU,CAAG,EAAoB,AAC/C,IADuB,EAChB,AAAmB,WAAA,SAAZ,OAAA,EAA2B,EAAa,EAAK,CAAF,MAAS,CAAb,AAAc,AACrE,CASO,SAAS,EAAS,CAAG,EAA0B,AACpD,GADsB,IACf,EAAU,EAAK,CAAF,IAAJ,GAAc,CAAC,AACjC,CAMO,SAAS,EAAW,CAAG,EAAgC,AAE5D,KAFwB,CAEjB,EAAQ,GAAG,CAAE,CAAN,GAAM,EAA4B,YAApB,OAAO,EAAI,CAAD,GAAM,AAAI,CAAU,AAC5D,CAD6D,AAUtD,SAAS,EAAiB,CAAG,EAAoB,AACtD,OAAO,EAAc,EADS,CACN,CAAA,AAAK,OAAT,MAAuB,GAAG,GAAI,AAAG,mBAAoB,GAAA,AAAO,iBAAA,GAAqB,CACvG,CAUO,CAXmG,QAW1F,EAAa,CAAG,CAAO,CAAI,EAAgB,AACzD,GAAI,CACF,CAFwB,MAEjB,GAAI,UAAW,CAC1B,CAAI,EAD0B,IACnB,EAAE,AAAE,CACX,OAAO,CACX,CACA,CAcO,EAhBS,OAgBA,EAAe,CAAG,EAEhC,AAFoD,MAE7C,CAAC,CAAA,CAAiB,AAFG,QAEM,EAAxB,OAAO,GAAI,AAAwB,IAAK,GAAb,CAAa,EAAT,CAAc,EAAqB,CAArB,MAA6B,EAAI,EAAqB,CAArB,KAAqB,CAAM,CAAC,AACtH,CADuH,AAQhH,SAAS,EAAU,CAAO,EAA+B,AAC9D,IADuB,EACG,WAAA,EAAnB,OAAO,OAAA,EAA2B,EAAa,EAAS,KAAF,EAAS,CAAjB,AAAkB,AACzE,iJClNA,IAAM,EAAS,IAAT,QAAS,UAAW,CAcnB,SAAS,EACd,CAAI,CACJ,EAAwE,CAAA,CAAE,EAE1E,CAFO,EAEH,CAAC,EACH,AAL4B,EAIrB,EAAE,EACF,WAAW,CAOpB,GAAI,CACF,IAOI,EAPA,EAAc,EAEZ,CAKK,CAPY,AAEX,CAAN,CAAQ,CACV,EAHY,AAGH,CAAC,CACV,EAAM,AADC,CACA,AAAH,CAIF,EAAW,KAAK,CAAC,AAAjB,OAAwB,CAAC,GAAW,EAAU,EAAd,AAAsB,CAAtB,EAAI,EAAiB,GAAS,CAC9D,EAAmB,CAAC,KAAK,CAAC,MAAV,CAAiB,CAAC,IAAY,EAAQ,CAAb,IAAY,UAAgB,EAlC7C,EAkCkD,AAlChD,CAoChC,KAAO,AAML,GANoB,IAVM,CAAC,CAUD,EAAT,AAAY,AAC7B,EAAU,AA0BhB,CA3BqC,KAFwE,GA6BpG,AAAqB,CAAE,CAAW,CAAQ,EAAqB,AAQtE,IAAM,EAAM,AAnC0C,AACpB,CAkC5B,AAnCkD,CAmC1C,CAEd,GAAI,CATS,AASR,EAVsB,CAUhB,CAAF,MAAS,CAChB,CADkB,KACX,EAAE,CAIX,GAAI,EAAO,IAAD,OAAY,EAAE,AAElB,IAAK,SAAW,aAAe,EAAK,EAAD,KAAQ,CAAE,CAC/C,GAAI,EAAK,EAAD,KAAQ,CAAC,eAAkB,CACjC,CADmC,AAAH,MACzB,EAAK,EAAD,KAAQ,CAAC,eAAkB,CAExC,CAFuC,EAEnC,EAAK,EAAD,KAAQ,CAAC,aAAgB,CAC/B,CAD8B,AAAG,MAC1B,EAAK,EAAD,KAAQ,CAAC,aAE5B,AAF4C,CAK1C,CALyC,CAKrC,CAAD,GAAK,CAAC,EAAK,EAAD,KAAQ,CAAC,WAAW,EAAE,CAAC,CAGpC,IAAM,EAAe,GAAU,KAAF,EAAvB,AACF,EAAS,MAAD,AAAO,CAAA,AAAC,GAAW,EAAK,EAAhB,AAAe,UAAa,CAAC,IAAU,GAAH,AAAM,CAAL,AAAM,CAAL,EAAgB,CAAC,EAAZ,AAAqB,EAAK,EAAD,CAAN,SAAmB,CAAC,GAAS,EAC3G,EADyG,CAAC,CACtG,CAER,GAAI,GAAc,MAAM,CACtB,CADwB,CAAV,AACD,OAAO,CAAA,AAAC,EAAT,EACV,EAAI,CAAD,GAAK,CAAC,CAAC,CAAC,EADuB,AACrB,CAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACxD,AADyD,CACpD,CAAC,KACG,CACD,EAAK,EAAE,AAAH,EACN,AADW,EACP,CAAD,GAAK,CAAC,CAAC,CAAC,EAAE,EAAK,EAAD,AAAG,CAAC,CAAA,CAAA,CAGA,IAAA,EAAA,EAAA,EAAA,GAAA,IAAA,CACA,GAAA,GAAA,CAAA,EAAA,EAAA,CAAA,OAAA,EAAA,GAEA,IAAA,EAFA,CAAA,CAEA,CAFA,AAEA,IADA,AACA,EADA,KAAA,AACA,CADA,AACA,CADA,IAAA,CAAA,CAEA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,AAGA,CAEA,IAAA,IAAA,CAAA,GADA,CAAA,AACA,YADA,AACA,CADA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CACA,CACA,IAAA,EAAA,EAAA,EAAA,UAAA,CAAA,CAAA,CAAA,CACA,GACA,CADA,CACA,CADA,AACA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA,CAAA,AAEA,CAEA,OAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CACA,AADA,EArFU,EAAa,GAK5B,KALoC,CAKpC,AALqC,AAAX,GAKtC,MAAuB,CAAvB,EAAgC,CAAA,EAAT,EAAc,GAAA,CAAM,EAAI,CAAD,KAAQ,CAAc,EAAQ,AAApB,KAAmB,CAAQ,EAAG,CAApB,AAAoB,CAAA,CAAe,CAAC,CAAE,CAI5G,EAAI,CAAD,GAAK,CAAC,GAET,GAAI,AAAG,CAFS,CAAC,AAEF,KAAD,CAAO,CACrB,EAAc,EAAY,OAAd,EAAa,CAAW,CAGtC,OAAO,EAAI,CAAD,MAAQ,EAAE,CAAC,IAAI,CAtBP,AAsBQ,KAtBH,CAuB3B,CAAI,EADmC,CAAC,GAC7B,EAAK,CAAF,AACV,MAAO,WAAW,AACtB,CACA,CAwE2B,SAAA,IACA,GAAA,CACA,OAFA,AAEA,EAAA,CAFA,GAEA,IAAA,CAAA,QAAA,CAAA,IACA,AADA,CACA,MAAA,EAAA,CACA,MAAA,EAAA,AACA,CACA,CASA,SAAA,EAAA,CAAA,EAAA,AAEA,GAAA,CAAA,EAAA,IAAA,CAFA,MAEA,CACA,CADA,MACA,IAAA,CAGA,IAAA,EAAA,EAEA,EAFA,EAEA,GAFA,CAEA,CAAA,CAAA,CAAA,CAAA,AACA,CADA,CADA,CAAA,CACA,AACA,CAAA,EADA,CAAA,EAAA,CAAA,CAKA,GAAA,CAJA,EAAA,KADA,GAKA,EAAA,WAAA,CAAA,CACA,GAAA,EAAA,OAAA,CAAA,CAAA,cAAA,CACA,CADA,MACA,EAAA,OAAA,CAAA,CAAA,cAAA,CAEA,CAFA,EAEA,EAAA,OAAA,CAAA,CAAA,YAAA,CACA,CADA,MACA,EAAA,OAAA,CAAA,CAAA,YAEA,AAFA,CAIA,CAJA,CAIA,EAAA,OAAA,EAAA,CACA,AADA,CAGA,OAAA,IACA,AADA,4LCrKpB,IAAM,EAA0C,CACrD,OAAO,CACP,GAFyB,GAEnB,CACN,MAAM,CACN,OAAO,CACP,KAAK,CACL,QAAQ,CACR,OAAO,CACP,CAMW,EAET,CAAA,EAeG,SAAS,EAAkB,CAAQ,EAAc,AACtD,GAAI,CAAA,CAAE,IADsB,QACtB,EAAa,UAAA,AAAU,CAAC,CAC5B,CAD8B,MACvB,IAGT,IAHiB,AAGX,EAHa,AAGb,EAAU,GAAV,OAAoB,CAAC,OAAQ,CAC7B,EAA8C,CAAA,CAAE,CAEhD,EAAgB,KAFJ,CAEU,CAAC,IAAI,CAAC,GAGlC,EAAc,OAAO,CAAA,AAAC,GAAT,CACX,IAAM,CADuB,AAHyB,CAIxB,AAJ0B,CAIJ,CAAC,EAAO,CAC5D,CAAY,CAD8C,AAC7C,EAAK,CAAI,CAAO,CAAX,AAAY,EAAO,CACrC,CAAO,CAD4B,AAC3B,EAAK,AAFe,CAEX,CACrB,CADiB,AACd,CAAC,CAEF,GAAI,CACF,OAAO,GACX,GAL0C,EAIvB,EAAE,CACT,CAER,EAAc,OAAO,CAAA,AAAC,GAAT,CACX,CAAO,CAAC,EAAK,CADgB,AACZ,CAAY,CAAhB,AAAiB,EAAO,AAC3C,CAAK,CAAC,AACN,CAFyC,AAGzC,CAqCsC,IAAA,EAAA,CAAA,EAAA,CAAA,CAAA,kBAAA,EAAA,QAAA,CAnCtC,CAmCsC,QAnC7B,EACP,AAkCoC,IAlChC,GAAU,CADG,CAEX,EAA0B,AAFJ,AAChB,CAEV,AAFiB,GACP,GACJ,CAAE,KACN,CADY,EACF,CAChB,CAAK,CACD,CAFE,AAAc,MAET,CAAE,KACP,CADa,EACH,CAChB,CAAK,CACD,CAFE,CAAe,OAER,CAAE,IAAM,CACrB,CAAG,CAkBmC,IAnBV,GAG1B,EAAI,WAAW,CACb,CADe,CACA,OAAO,CAAA,AAAC,IAAT,AACZ,CAAM,CAAC,EADsB,AAClB,CAAI,CAAJ,AAAK,GAAG,IAAI,CACjB,GACF,CAF8E,CAE/D,EADN,EAAE,EACU,CACnB,KADY,KACF,CAAC,OAAO,CAAC,EAAK,CAAC,CAAF,AAAG,EAAA,MAAA,CAAA,CAAA,OAAA,IAAA,CAAA,EAAA,EAAA,EACA,CAAA,CADA,AACA,AAEA,CACA,AADA,AAHA,CAIA,CAAA,CAEA,EAAA,OAAA,CAAA,IAAA,AACA,CAAA,CAAA,EADA,AACA,CAAA,CAAA,QAAA,CACA,CAAA,CAAA,CAGA,CACA,IALA,CAIA,4KCnF/B,SAAS,EAAS,CAAG,CAAU,EAAc,CAAX,AAAY,CAA7B,CAAuC,MAC1C,UAAf,OAAO,GAAI,AAAgB,AAAQ,CAAC,EAAT,AAAW,IAGnC,EAAI,CAAD,KAAQ,EAAG,EAAM,EAAM,CAAN,AAAO,EAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,AACA,CAUA,SAAA,EAAA,CAAA,CAAA,CAAA,EACA,AADA,CAAA,GACA,EAAA,EACA,EAAA,AADA,CAAA,CACA,KAAA,CAAA,CACA,GAAA,GAAA,GAAA,CACA,CADA,EAAA,IACA,EAEA,EAAA,GAAA,AAFA,CAIA,EAAA,CAAA,CAAA,CAAA,AAGA,CALA,EAAA,CAKA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CACA,EAAA,CAAA,EAAA,CACA,GAAA,CAAA,CAAA,AAGA,IAAA,EAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA,GAAA,CAAA,GAgBA,OAhBA,AACA,CADA,CACA,CAAA,CAAA,CAAA,EAAA,CACA,EAAA,CAAA,CAAA,AADA,CAGA,GAAA,CAAA,IACA,EAAA,GAAA,CADA,AACA,CAAA,CADA,EACA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAGA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,GAAA,AACA,CADA,CACA,CAAA,EAAA,AACA,GAAA,CAAA,GAAA,KAAA,EAAA,GAAA,EAEA,EAAA,AAFA,CAEA,GACA,GAAA,GADA,CACA,CADA,IACA,CAAA,CAGA,CACA,CAQA,KATA,IASA,EAAA,CAAA,CAAA,CAAA,EAAA,AACA,CADA,EACA,CAAA,KAAA,CAAA,OAAA,CAAA,GACA,EADA,CAAA,EAAA,CACA,EAAA,CAGA,IAAA,EAAA,EAAA,CAEA,CAFA,GAEA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CACA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CACA,GAAA,CAMA,CAAA,EAAA,EAAA,cAAA,EAAA,GACA,EAAA,AADA,CAAA,EAAA,CACA,CAAA,gBAAA,CAAA,CAEA,EAAA,IAAA,CAAA,MAAA,CAAA,GAEA,CAAA,CAFA,CAAA,CAAA,GAEA,CAAA,CAAA,CACA,EAAA,IAAA,CAAA,8BAAA,CAAA,AACA,CACA,CAEA,OAAA,EAAA,IAAA,CAAA,EACA,CAUA,MAXA,CAAA,EAWA,EACA,CAAA,CACA,CAAA,CACA,GAAA,CAAA,OAHA,CAKA,CAAA,CAAA,CAAA,EAAA,EAAA,IAFA,IAEA,EAAA,KAAA,AAIA,CAAA,AAJA,EAAA,AAIA,EAAA,QAAA,EAAA,GACA,EAAA,EADA,CAAA,CACA,CADA,AACA,KAAA,AAEA,CAFA,AAEA,EAAA,EAAA,QAAA,EAAA,KACA,EADA,AACA,CADA,EAAA,CACA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAIA,AAJA,CAgBA,SAAA,EACA,CAAA,CACA,EAAA,EAAA,CACA,EAAA,CADA,CACA,EAEA,OAAA,EAAA,AALA,IAKA,CAAA,CAAA,EAAA,AAFA,EAEA,EAAA,EAAA,GACA,EADA,CAAA,KAAA,YAAA,CAAA,CAAA,kTCpH7B,SAAS,EAAK,CAAM,CAAP,AAAiC,CAAI,CAAU,CAAkB,EAAiC,AACpH,GAAI,CAAA,AAAE,MAAQ,CAAA,CAAM,CAAC,AACnB,EADqB,KAKvB,IAAM,EAAW,CAAM,CAAC,EAAM,CAE9B,CAFe,AAAa,EAEJ,UAAU,EAA9B,AAAgC,OAAzB,EACT,MADkB,CAIpB,IAAM,EAAU,EAAmB,EAIZ,CAJT,KAA6B,CAAE,GAIZ,EAAE,CAA/B,CAJ8B,MAIvB,GACT,EAAoB,EADH,AACY,GAG/B,EAH6B,CAGzB,CACF,CAJqC,AAI/B,CAJgC,AAI/B,EAAI,CAAI,CAAJ,AACf,CAAI,CALmB,IAIG,AAChB,GACN,WAAY,EAAA,EAAG,MAAM,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,EAAK,EAAD,SAAY,CAAC,CAAE,EAC9E,CACA,CASO,EAX6E,CAAC,MAWrE,EAAyB,CAAG,CAAU,CAAI,CAAU,CAAK,EAAiB,AACxF,GAAI,CACF,MAAM,CAAC,IAF6B,UAEf,CAAC,EAAK,CAAF,CAAQ,CAE/B,CAF6B,IAExB,CAAE,EACP,GADY,KACJ,EAAE,EACV,EADc,UACF,EAAE,CACpB,CAAK,CAAC,AACN,CAFwB,AAEpB,MAAO,EAAK,CAAF,EACV,WAAY,EAAA,EAAG,MAAM,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,EAAK,EAAD,SAAY,CAAC,CAAE,EAC3F,CAD8F,AAE9F,CAF+F,AAWxF,SAAS,EAAoB,CAAO,CAAmB,CAAQ,EAAyB,AAC7F,GAAI,CACF,IAAM,EAAQ,EAAS,AAFQ,MAET,GAAW,EAAG,CAAA,CAAE,CACtC,EAAQ,KAAD,IAAW,CAAE,EAAS,MAAD,GAAC,CAAY,EACzC,EAAyB,CADqB,CACZ,KAAF,eAAR,CAA+B,CAAE,EAC7D,CAAI,KADiE,CAC1D,AAD2D,EACtD,CAAF,AAAE,CAAC,AACjB,CADiB,AAWV,SAAS,EAAwC,CAAI,EAAqC,AAC/F,OAAO,EAAK,EAAD,GADsB,GAXlB,WAYgB,AACjC,CAUO,SAAS,EAAwB,CAAK,EAe3C,GAAA,CAAA,EAAA,EAAI,OAAA,AAAO,AAfuB,EAetB,GACV,EADe,CAAC,EAAE,CACX,CACL,OAAO,CAAE,EAAM,GAAD,IAAQ,CACtB,IAAI,CAAE,EAAM,GAAD,CAAK,CAChB,KAAK,CAAE,EAAM,GAAD,EAAM,CAClB,GAAG,EAAiB,EAAM,AAChC,CAAK,CACI,CAFsB,GAEtB,CAAI,EAAA,EAAA,CAFY,MAEZ,AAAO,EAAC,GAoBjB,EApBsB,CAAC,IAoBhB,CApBkB,EACzB,EAmBY,EAnBN,EAMF,CACF,IAAI,CAAE,EAAM,GAAD,CAAK,CAChB,MAAM,CAAE,EAAqB,EAAM,GAAD,GAAO,CAAC,CAC1C,QAD4B,KACf,CAAE,EAAqB,EAAM,GAAD,UAAc,CAAC,CACxD,CADmC,EAChC,EAAiB,EAAM,AAChC,CAAK,CAMD,CAP2B,KAGA,IAHN,OAGM,EAAvB,OAAO,WAAA,EAAgB,CAAA,EAAA,EAAe,YAAA,AAAY,EAAC,EAAO,GAAF,QAAa,CAAC,EAAE,CAC1E,EAAO,IAAD,EAAC,CAAS,EAAM,GAAD,GAAC,AAAM,EAGvB,CACX,CAGA,CAGA,GAPiB,EACR,IAMA,EAAqB,CAAM,EAAmB,AACrD,GAAI,CACF,MAAA,CAAA,EAAA,EAAO,AAFkB,SAElB,AAAS,EAAC,GAAU,CAAA,EAAJ,AAAI,EAAA,gBAAgB,AAAhB,EAAiB,GAAU,GAAJ,CAAA,EAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EACzF,CAAI,GAD2F,CAAC,EACrF,EAAK,CAAF,AACV,MAAO,WAAW,AACtB,CACA,CAGA,SAAS,EAAiB,CAAG,EAAuC,AAClE,GAAmB,QADI,EACnB,OAAO,GAAI,AAAwB,IAAI,GAAZ,EAS7B,CAT6B,KAStB,CAAA,CAToC,AASlC,EART,IAAM,EAA6C,CAAA,CAAE,CACrD,IAAK,IAAM,CADS,IACG,EACjB,CADc,AAAM,CAAE,IAChB,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAK,CAAF,IAC1C,CAAc,CAAC,CADqC,CAAC,AAC5B,CAAG,CAD2B,AACvB,CAA4B,EAAS,AAAD,CAA7C,CAG3B,OAAO,CACX,CAGA,CAOO,KAVE,IAUO,EAA+B,AAXtB,CAW+B,CAA2B,EAAoB,EAAE,EACvG,AADiH,GAAvB,CACpF,EAAO,EAAF,IAAQ,CAAC,IAAI,CAAC,EAAqB,AADF,IAE5C,EAAK,EAAD,CADmD,CAAC,AAC/C,CADgD,CAC9C,CAEX,IAAM,CAHuC,CAG5B,CAAI,CAAC,CAAC,CAAC,CAExB,CAFe,EAEX,CAAC,EACH,MADW,AACJ,EADM,oBACgB,CAG/B,GAAI,EAAS,MAAD,AAAQ,EAAG,EACrB,MAAA,CAD8B,AAC9B,EADgC,AAChC,EAAO,QAAQ,AAAR,EAAS,EAAU,GAG5B,GAH0B,CAGrB,EAHgC,CAAC,CAG7B,EAAe,EAAK,EAAD,IAAO,CAAE,CAA5B,CAA2C,CAAC,CAAE,IAAgB,CACrE,IAAM,EAAa,CAD8C,CACzC,CAD2C,CAC5C,GAAM,CAAvB,AAAwB,CAAC,CAAE,GAAc,IAAI,CAAC,IAAI,AAAX,CAAC,AAAW,CACzD,KAAI,EAAW,MAAO,CAAE,CAAV,AAAU,CAAS,EAAE,AAGnC,GAAI,IAAiB,EAAK,EAAD,IAArB,AAA4B,CAC9B,CADgC,MACzB,EAET,MAAA,CAAA,CAFmB,CAEnB,EAAO,QAAQ,AAAR,EAAS,EAAY,GAChC,CAEE,IAH4B,CAAW,CAGhC,AAHiC,EAG/B,AACX,CAUO,SAAS,EAAqB,CAAU,EAAQ,AAOrD,OAAO,AAGT,KAViC,IAUxB,EAAsB,CAAU,CAAK,CAAc,EAA4B,AAEtF,EALyB,CAKN,IAAA,GAAf,CAFqB,EAEwB,OAA7C,CAAqD,EAA9B,AAAgC,OAAzB,EAChC,OAAO,CADyB,CAKlC,IAAM,EAAU,EAJG,AAIY,GAAG,CAAC,GACnC,GAAI,AAAY,EADc,EAAe,CAAC,EAClC,EAAa,EAAE,AACzB,OAAO,EAIT,GAAI,EAJa,GAIR,CAAC,OAAO,CAAC,GAAa,CAC7B,IAAM,EADoB,AACK,CADJ,CACM,CAQjC,MARiB,CAEjB,EAAe,GAAG,CAAC,EAAY,GAE/B,EAAW,CAFG,EAAe,GAAa,CAAC,AAEzB,CAAR,AAAQ,AAAC,IACjB,EAAY,GADc,CACV,CAAC,EAAmB,EAAzB,AAAgC,GAAF,AAC/C,CAAK,CAAC,CAEK,CACX,CAEE,GAAI,AAqBN,GA3ByC,AAAsB,CAAC,CAAC,CAG1C,AAGX,GAqBH,AAAO,CAAK,EAA6C,AAEhE,GAFa,CAEP,EAAe,EAAiB,GAAX,IAAT,IAA+B,CACjD,OAAO,IAAgB,OAAJ,CAAc,KAAgB,KACnD,CADmC,CAxBtB,EAwB+C,CAxBlC,CACtB,IAAM,EAA0C,AAD7B,CAC6B,AAD5B,CAC8B,CAalD,MAbiB,CAEjB,EAAe,GAAG,CAAC,EAAY,GAElB,AAEb,GAJc,CAIV,CAJyB,CAEV,CAAC,CAFsB,CAAC,EAEnB,CAAC,GAEpB,OAF8B,AAEvB,CAFwB,AAExB,AAAC,IACX,GADkB,CACZ,EAAM,CAAU,AAAZ,CAAa,EAAI,AACf,CADc,QACL,CAAjB,CAAmB,EAAf,CACN,CAAW,CAAC,EAAK,CAAF,AAAI,EAAmB,EAAK,CAAF,CAAgB,CAAC,AAElE,CAAK,CAAC,CAEK,CACX,CAGE,MAR2C,CAQpC,CACT,CALuB,CA1CK,EAHH,IAAI,CAiDV,EAjDa,CAIhC,AADsC,CAgE/B,AAnE6C,CAGZ,QAgExB,EAAU,CAAG,EAA0B,AACrD,CAjEoD,CAAC,EAgE9B,AACnB,EACJ,QAAQ,CADO,EAGb,CAFU,SAEE,GAAP,EACH,CADO,CACO,EADK,EACD,MAAM,CAAC,GAAG,AAC5B,CAD6B,IAM/B,KAAoB,UAAf,OAAO,GAAmC,AAA/B,QAAuC,EAAvB,OAAO,EACrC,CADyC,CAC3B,MAAM,CAAC,EAAT,CAAY,AACxB,CADyB,IAI3B,KAAA,CAAA,EAAA,EAAK,WAAA,AAAW,EAAC,GAAG,AAElB,CAFmB,CAEL,IAAK,EAAY,CAAZ,EAAnB,QAA0C,CAAC,GAAG,AAC9C,CAD+C,IAIjD,SACE,EAAc,CAEpB,CACE,CAHqB,MAAjB,AAGG,CACT,UADoB,oQC1Rb,SAAS,EAAM,EAVtB,AAU+B,CAAV,GAAC,KAVb,AAU+B,EATtC,IAAM,EAAI,CADM,AACN,CAAE,EADmC,QACxB,CACvB,OAAO,EAAI,CAAD,KAAC,EAAU,EAAI,CAAD,OAAS,AACnC,GAO0C,EAAU,AAClD,IAAI,EAAgB,IAA8B,EAAE,CAAlB,IAAhB,AAAoB,CAAC,MAAM,EAAG,CAChD,GAAI,CACF,GAAI,GAAQ,GAAF,OAAY,CACpB,CADsB,MACf,EAAO,IAAD,MAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CAE1C,GAAQ,GAAF,YAAiB,EAAE,CAC3B,EAAgB,KAKd,CALoB,GAKd,EALM,AAKO,IAAI,IAAN,MAAgB,CAAC,CAAC,CAAC,CAGpC,OAFA,EAAO,IAAD,WAAgB,CAAC,GAEhB,CAAU,CAAC,CAAC,CAAC,CAC5B,CAAO,AAEP,CALyC,AAKrC,CALsC,KAK/B,CAAC,CAAE,CAGd,CAIE,MAAO,AAAE,mCAAsD,OAAO,CAAC,QAAQ,CAAE,AAAF,CAAI,EAEjF,CADJ,AACM,CAAA,CAA2B,CAAmB,GAAlB,GAAkB,CAAE,EAAO,CAAA,EAA0B,CAAC,AAAE,CAAD,AAAG,EAA7C,EAAG,IAAkD,CAAC,EAAE,CAAC,CAExG,CAEA,OALA,EAKS,EAAkB,CAAK,EAAgC,AAC9D,OAAO,EAAM,GADW,AACZ,MAAU,EAAE,MAAM,EAAA,CAAG,CAAC,CAAC,AACrC,CAMO,SAAS,EAAoB,CAAK,EAAiB,AACxD,GAAM,SAAE,CAAO,CAAE,AADgB,QACR,CAAE,CAAA,CAAU,CAAE,EACvC,GAD4C,AACxC,EACF,KADS,EAAE,AACJ,EAGT,IAAM,CAHU,CAGO,EAAkB,KAAK,CAAC,IAC/C,AAAI,AADiB,EAEnB,AAAI,EAAe,CAFmB,GAEnB,EAAQ,EAAe,EAD1B,EAAE,AACA,CAA6B,CACtC,CADwC,AACvC,EAAA,EAAA,CAD+B,GAC/B,CAAA,EAAA,EAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAEA,EAAA,EAFA,EAEA,EAAA,EAAA,IAAA,CAAA,EAAA,GAAA,EAAA,EAAA,OAAA,CAEA,GAAA,IAAA,OAAA,AACA,CASA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,AACA,IAAA,EAAA,EAAA,GAAA,CADA,CACA,IAAA,CAAA,EAAA,GAAA,MAAA,EAAA,CAAA,CAAA,CAAA,AACA,EAAA,EAAA,EAAA,IAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,CAAA,AACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CACA,AADA,CAAA,AACA,EAAA,KAAA,EAAA,CACA,EAAA,EADA,GACA,CAAA,GAAA,EAAA,CAAA,CAEA,EAAA,IAAA,EAAA,AACA,GAAA,GADA,CACA,CAAA,GAAA,CAAA,EAAA,IAAA,CAAA,AAEA,CASA,SAAA,EAAA,CAAA,CAAA,CAAA,EAAA,AACA,IAAA,EAAA,EAAA,GACA,EADA,CACA,AAFA,AACA,CACA,EACA,CAFA,KAAA,CAMA,IAAA,CALA,CAKA,CALA,CAKA,SAAA,CAGA,EAHA,CACA,EAAA,SAAA,CAAA,CAFA,CAEA,GAFA,CAAA,SAAA,CAAA,OAAA,EAAA,EAEA,EAFA,CAEA,CAAA,CAAA,GAAA,CAAA,CAAA,CAEA,GAAA,MAAA,GAAA,EAAA,CACA,IAAA,EAAA,CAAA,EADA,CACA,GAAA,CAAA,GAAA,CAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CACA,EAAA,EADA,OACA,CAAA,EAAA,EAAA,CAAA,CACA,CACA,CAGA,IAAA,EACA,CANA,UAKA,0KACA,CAaA,SAAA,EAAA,CAAA,EAAA,AACA,IADA,GACA,QAAA,CAAA,GAAA,EAAA,CAAA,EAAA,CAAA,AACA,CAMA,SAAA,EAAA,CAAA,EACA,AADA,IACA,EAAA,AADA,EACA,CAAA,EAAA,EAAA,CAAA,IAAA,EAAA,CACA,EAAA,EAAA,CAAA,CADA,AACA,CAAA,AADA,CACA,CAAA,CACA,CADA,CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CADA,CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CADA,KACA,CACA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,KAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,AACA,GADA,EACA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,EACA,GADA,EAAA,AACA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,EAAA,EACA,GADA,EAAA,KACA,CAAA,CAAA,CAAA,CAAA,CAAA,AACA,CAAA,AACA,CASA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,AAEA,MAFA,EAEA,CAFA,GAEA,EAAA,GAAA,EAAA,CAAA,CACA,OAGA,IAAA,EAAA,EAAA,GAAA,CAAA,EAAA,CACA,EAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEA,EAAA,GAAA,QAAA,CAAA,EACA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,GAAA,GACA,EADA,CACA,CAAA,GADA,AACA,CAAA,AADA,AACA,CADA,CACA,EAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAGA,IAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,GAGA,CAHA,CAGA,GAAA,EAHA,CAAA,MAGA,CAAA,CAAA,EAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAEA,EAAA,GAAA,SAAA,CAAA,EACA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CACA,GADA,AACA,CAAA,GAAA,CAAA,EAAA,CADA,CACA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA,AACA,CAuBA,SAAA,EAAA,CAAA,EACA,AADA,GACA,AAeA,SAAA,CAAA,EAAA,AACA,GAAA,AAjBA,CAkBA,CAjBA,MAiBA,EAAA,CAFA,MAEA,YAAA,AACA,CAAA,KAAA,CAAA,CAAA,AACA,CADA,CAlBA,GACA,MADA,CAAA,AACA,EADA,AAIA,EAHA,CAGA,MAGA,SAWA,eAXA,EAAA,EAAA,OAAA,cAAA,EAAA,EACA,CAAA,CADA,CAAA,IACA,EAAA,CAAA,AAEA,CAEA,OAAA,CACA,IADA,oDC7JV,gBAAgB,oGAjDb,SAAS,IACd,OAAO,IAAI,CAAC,GAAG,EAAC,CADoB,EAChB,CACtB,AAFiD,CA4CpC,IAAA,EAAqB,AAlClC,QATsC,CAS7B,EACP,GAAM,EAiCK,WAjCH,CAAY,CAAA,CAAA,EAiC4C,AAjCxC,SADe,CACJ,CACnC,CAFwD,EAEpD,CAAC,GAAa,GAAG,CACnB,CADqB,GAAP,GACP,EAKT,IAAM,EAA2B,IAAI,CAAC,GAAG,EAAC,CAAI,EAAY,CAL3B,EAK8B,EAAE,CACzD,EAAa,AAA0B,CADY,AAAnD,OACA,CAAuC,CAAd,CAAD,SAAC,CAA0B,EAA2B,EAAY,SAAD,CAAW,CAW1G,MAAO,GAXkD,CAYhD,CAAC,CADG,CACU,EAAY,GAAG,EAAA,CAA5B,AAA8B,CAnDjB,EAmDW,CAAU,AAE9C,CArD6B,GA2HtB,SAAS,GAxE8C,CA6E5D,OAJI,AAAC,GACH,GAAmB,AAnDvB,SAAS,CAkDc,CADqB,AA5C1C,CA6CuB,CACJ,CAF8C,AA5C3D,MA8CmC,OA9CjC,CAAY,CALO,AAKP,CAAA,EAAI,AALoC,UAKzB,CACnC,GAAI,CAAC,GAAa,GAAG,CACnB,CADqB,GAAP,EACP,MAAC,EAAW,MAAM,CAAR,AAAS,CAI5B,IAAM,EAAiB,EAAY,GAAG,EAAE,CAClC,EAAU,CADkB,CAAb,EACD,CAAN,AAAO,GAAG,EAAE,CAGpB,EAAkB,EAAY,SAAD,CAAC,CAChC,AADE,IACE,CAAC,GAAG,CAAC,EAAY,SAAD,CAAY,CAAE,EAAiB,OAAO,CAUxD,EAAkB,CATpB,CASgC,AAViB,MAUX,EAAE,AAT/B,CASsB,EAAb,YAAqC,CAGrD,EAAuB,AAFyB,QAAQ,EAAnC,OAAO,CAEP,AAAqB,CAAE,IAAI,CAAC,GAAG,CAAC,EAAkB,EAF3C,AAE4D,OAAO,CAAA,AAGrG,GAd6B,AAW8B,AAA8C,AAGrG,CAHwF,OAC1D,CADgF,CAnBhG,IAAA,CAQW,AAgB3B,EAxBuB,CAQsB,AAgBzC,EAFmB,CAtBI,AAwBJ,EACrB,CAHwB,GAd4B,CAYtB,CAKvB,CAAC,CAL6C,CAKjC,CADF,OAJ4C,CAIrB,AACtB,CAAW,CAAE,AADW,MAFM,EAAE,IAGP,CAAC,MAE7C,MAAO,CAAC,EAAiB,aAAF,IAAmB,CAAC,CAK/C,MAAO,CAAC,EAAS,KAAF,IAAW,CAC5B,AAD6B,GASc,CAAE,CAGpC,CAAgB,CAAC,CAAC,CAAC,AAC5B,mJCvHO,SAAS,EAAY,CAAO,EAAwD,AAEzF,IAAM,EAAA,AAFmB,CAEnB,EAAA,EAAe,KAAf,aAAe,AAAkB,EAAE,EAEnC,EAAmB,CACvB,GAAG,CADQ,AACR,CAAA,EAAA,EAAE,KAAA,AAAK,EAAE,EACZ,IAAI,EAAE,EACN,EADU,OACD,CAAE,EACX,OAAO,CAAE,EACT,AAFuB,QAEf,CAAE,CADW,AACV,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CACT,cAAc,EAAE,EAChB,GADqB,GACf,CAAE,IAAM,KAkHC,OAAA,CAlHY,CAkHZ,EAlHa,EAmHb,CACA,GAAA,CApHoB,AAoHpB,CApHqB,AAoHrB,EAAA,EAAA,GAAA,CAAA,CAAA,CACA,IAAA,CAAA,EAAA,IAAA,CAAA,AAEA,OAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,KAAA,EAAA,EAAA,WAAA,EAAA,CACA,SAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,KAAA,IAAA,EAAA,WAAA,EAAA,CACA,MAAA,CAAA,EAAA,KAAA,CAAA,CACA,MAAA,CAAA,EAAA,KAAA,CAAA,CACA,GAAA,CAAA,QAAA,EAAA,OAAA,EAAA,GAAA,EAAA,QAAA,EAAA,OAAA,EAAA,GAAA,CAAA,CAAA,EAAA,EAAA,GAAA,CAAA,CAAA,MAAA,EACA,OADA,CACA,CAAA,EAAA,KAAA,GAAA,CACA,kBAAA,CAAA,EAAA,KAAA,aAAA,CACA,KAAA,CAAA,CACA,OAAA,CAAA,EAAA,KAAA,EAAA,CACA,WAAA,CAAA,EAAA,KAAA,MAAA,CACA,UAAA,CAAA,EAAA,KAAA,IAAA,CACA,UAAA,CAAA,EAAA,KAAA,IAAA,AACA,CACA,AADA,CACA,CAnInB,CAAG,CAMD,OAJI,GACF,EAAc,EADL,AACc,EADZ,CAIN,CACT,CAJyB,AAkBlB,EAlB2B,CAAC,CAAlB,CAGD,IAeA,EAAc,CAAO,CAAW,EAA0B,CAAA,CAAE,EAiCzD,AAjCiE,CAA7B,EAA1B,AACvB,EAAQ,IAAI,CAAL,CAAO,CACZ,CAAC,EAAQ,KAAD,IAAC,EAAa,EAAQ,IAAI,CAAL,AAAM,UAAU,EAAE,CACjD,EAAQ,KAAD,IAAW,CAAE,EAAQ,IAAI,CAAL,AAAM,UAAA,AAAU,EAGzC,AAAC,EAAQ,GAAI,EAAL,AAAS,EAAD,AAAS,GAAG,EAAE,AAAN,CAC1B,EAAQ,GAAI,CAAE,CAAP,CAAe,IAAI,CAAL,AAAM,EAAG,EAAG,EAAQ,IAAI,CAAL,AAAM,KAAM,EAAG,EAAQ,IAAI,CAAL,AAAM,QAAA,AAAQ,GAIhF,EAAQ,KAAD,IAAC,CAAY,EAAQ,KAAD,IAAW,EAAA,CAAA,EAAA,EAAG,kBAAA,AAAkB,EAAE,EAEzD,EAAQ,KAAD,aAAmB,EAAE,CAC9B,EAAQ,KAAD,aAAC,CAAqB,EAAQ,KAAD,aAAC,AAAkB,EAGrD,EAAQ,KAAD,SAAe,EAAE,AAC1B,GAAQ,IAAD,UAAC,CAAiB,EAAQ,KAAD,SAAC,AAAc,EAE7C,EAAQ,GAAG,EAAE,AAAN,CAET,EAAQ,GAAA,CAA6B,CAA9B,CAA8B,GAAvB,EAAQ,GAAG,CAAC,CAAL,KAAK,CAAgB,EAAQ,GAAA,CAAM,CAAP,AAAO,EAAA,EAAA,KAAA,AAAK,GAAE,OAE5C,IAAjB,EAAQ,GAAkB,CAAb,CAAN,AAAqB,EAC9B,EAAQ,IAAA,CAAD,AAAQ,EAAQ,IAAA,AAAI,CAAL,CAEpB,CAAC,EAAQ,GAAA,EAAD,AAAQ,EAAQ,GAAG,EAAJ,AAAM,CAC/B,EAAQ,GAAI,CAAE,CAAP,AAAQ,EAAA,EAAA,GAAA,EAAA,EAEA,QAAA,EAAA,OAAA,EAAA,KAAA,EAAA,EACA,GAAA,IAAA,GAAA,CAAA,EAAA,KAAA,EAAA,EAEA,EAAA,KAAA,SAAA,CACA,CADA,CACA,KAAA,GAAA,MAAA,OACA,EADA,CACA,QAAA,EAAA,OAAA,EAAA,KAAA,GAAA,CACA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KACA,CACA,IAAA,EAAA,EAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,CACA,EAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,AACA,CACA,EAAA,EAFA,GAEA,EAAA,EAAA,CACA,EAAA,KAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAEA,EAAA,KAAA,MAAA,EAAA,CACA,EAAA,KAAA,MAAA,CAAA,EAAA,KAAA,MAAA,EAEA,CAAA,EAAA,KAAA,IAAA,EAAA,EAAA,KAAA,IAAA,EAAA,CACA,EAAA,KAAA,IAAA,CAAA,EAAA,KAAA,IAAA,EAEA,CAAA,EAAA,KAAA,IAAA,EAAA,EAAA,KAAA,IAAA,EAAA,CACA,EAAA,KAAA,IAAA,CAAA,EAAA,KAAA,IAAA,EAEA,QAAA,EAAA,OAAA,EAAA,KAAA,CAAA,GACA,EAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,EAEA,EAAA,KAAA,CAAA,EAAA,CACA,EAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAEA,CAaA,SAAA,EAAA,CAAA,CAAA,CAAA,EAAA,AACA,IAAA,CADA,CACA,CAAA,CAAA,CACA,EADA,AAEA,EAAA,EADA,EAAA,CACA,GAAA,CAAA,CAAA,CACA,GADA,CACA,EAAA,CAAA,EAAA,KAAA,CAAA,GACA,EAAA,CAAA,IAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAGA,EAAA,EAAA,EACA,GADA,EAAA,CAAA,CAAA,2CCzHnB,EAAA,CAAA,CAAA,WACO,SAAS,EAAS,CAAU,CAAK,CAAnB,AAA2B,CAAK,EAAS,CAAC,EAAK,AAGlE,CAH0D,EAGtD,CAAC,GAAgC,KAAhC,GAAyC,EAA7B,OAAO,GAAyB,GAAU,CAAC,CAA3B,AAC/B,CADsD,AAAM,MACrD,EAIT,GAAI,GAJa,AAIkC,CAAC,EAAE,CAApC,GAAH,GAAS,CAAC,IAAI,CAAC,GAAU,KAAF,CAAC,AAAQ,CAC7C,OAAO,EAIT,IAAM,EAAS,CAAE,CAJE,EAIN,AAAO,CAAA,CAAY,CAGhC,IAAK,IAAM,GAAI,EAAG,EACZ,MADoB,AACd,CADgB,AACf,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAU,GAAG,CAAC,CACrD,CAD+C,AAAQ,AACjD,CAAC,EAAG,CAAA,AAAI,EAAM,CAAM,CAAC,CAAR,CAAY,CAAD,AAAG,CAAQ,CAAC,EAAI,CAAE,AAAH,EAAY,EAAC,CAAC,CAI/D,AAJ2D,OAIpD,CACT,KADe,mHCxBR,SAAS,IACd,MAAA,CAAA,EAAA,EAAO,AADsB,GAAW,EACjC,AAAK,EAAE,CAChB,CAKO,SAAS,IACd,MAAA,CAAA,EAAA,CAD4B,CACrB,EADgC,GAChC,AAAK,EAAE,EAAC,SAAS,CAAC,EAAE,CAC7B,AAD8B,0HCT9B,IAAM,EAAmB,aAAa,CAU/B,AAVD,SAUU,EAAiB,CAAK,CAAS,CAAI,EAA0B,AACvE,IAAI,EAAE,CACR,EAF4B,sBAE5B,AAAwB,EAAC,EAA6B,EAAkB,CAA/C,EAGzB,CAH4E,CAAC,KAGrE,CAAA,CAA6B,EAHiC,AAGhB,AAE1D,CAMO,SAAS,EAAiB,CAAK,CARmB,CAQqB,AAC5E,OAAO,CAAK,CAAC,EADiB,AACA,AAChC,cAD+B,yLC0DxB,OAAM,EA8DJ,GA9DU,CAAA,OA8DC,EAAG,CACnB,IAAI,CAAC,mBAAoB,EAAE,EAC3B,GADgC,CAC5B,CAAC,eAAgB,CAAE,EAAE,CACzB,IAAI,CAAC,gBAAiB,CAAE,EAAE,CAC1B,IAAI,CAAC,YAAa,CAAE,EAAE,CACtB,IAAI,CAAC,YAAa,CAAE,EAAE,CACtB,IAAI,CAAC,KAAM,CAAE,CAAA,CAAE,CACf,IAAI,CAAC,KAAM,CAAE,CAAA,CAAE,CACf,IAAI,CAAC,MAAO,CAAE,CAAA,CAAE,CAChB,IAAI,CAAC,SAAU,CAAE,CAAA,CAAE,CACnB,IAAI,CAAC,sBAAuB,CAAE,CAAA,CAAE,CAChC,IAAI,CAAC,mBAAA,CAAsB,CACzB,OAAO,CAAA,CAAA,EAAA,EAAE,eAAA,AAAe,EAAE,EAC1B,UAAU,CAAE,IAAI,CAAC,MAAM,EAAE,AAC/B,CAAK,AACL,CAKS,KAAK,EAAU,CACpB,IAAM,EAAW,IAAI,EAAN,AA2Bf,GA3B0B,EAAE,EAC5B,EAAS,MAAD,MAAc,CAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAC9C,EAAS,KAAM,CAAP,AAAS,CAAE,GAAG,IAAI,CAAC,KAAA,CAAO,CAClC,EAAS,MAAD,AAAQ,CAAE,CAAE,GAAG,IAAI,CAAC,MAAA,CAAQ,CACpC,EAAS,MAAD,GAAW,CAAE,CAAE,GAAG,IAAI,CAAC,SAAA,CAAW,CACtC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAGxB,EAAS,MAAD,GAAU,CAAC,KAAA,CAAQ,CACzB,MAAM,CAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAChD,CAAO,CAGH,EAAS,KAAA,CAAQ,AAAT,IAAa,CAAC,KAAK,CAC3B,EAAS,MAAD,AAAC,CAAS,IAAI,CAAC,MAAM,CAC7B,EAAS,MAAD,EAAC,CAAW,IAAI,CAAC,QAAQ,CACjC,EAAS,MAAD,UAAC,CAAmB,IAAI,CAAC,gBAAgB,CACjD,EAAS,MAAD,MAAC,CAAe,IAAI,CAAC,YAAY,CACzC,EAAS,MAAD,UAAkB,CAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CACtD,EAAS,MAAD,MAAc,CAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAC9C,EAAS,MAAD,gBAAwB,CAAE,CAAE,GAAG,IAAI,CAAC,sBAAA,CAAwB,CACpE,EAAS,MAAD,aAAqB,CAAE,CAAE,GAAG,IAAI,CAAC,mBAAA,CAAqB,CAC9D,EAAS,MAAD,CAAC,CAAU,IAAI,CAAC,OAAO,CAC/B,EAAS,MAAD,MAAC,CAAe,IAAI,CAAC,YAAY,IAEzC,EAAA,gBAAA,AAAgB,EAAC,EAAQ,CAAA,EAAA,EAAE,CAAF,eAAE,AAAgB,EAAC,IAAI,CAAC,CAAC,CAE3C,CACX,CAOS,MARU,GAQD,CAAC,CAAM,CAA4B,CACjD,IAAI,CAAC,OAAQ,CAAE,CACnB,CAMS,IAPgB,UAOF,CAAC,CAAW,CAA4B,CAC3D,IAAI,CAAC,YAAa,CAAE,CACxB,CAKS,SAN0B,AAMjB,EAAoC,CAClD,OAAO,IAAI,CAAC,OAAQ,AACxB,CAMS,WAAW,EAAuB,CACvC,OAAO,IAAI,CAAC,YAAY,AAC5B,CAKS,gBAAgB,CAAC,CAAQ,CAAgC,CAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAC9B,CAKS,KAN6B,CAAC,WAMb,CAAC,CAAQ,CAAwB,CAEvD,OADA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GACpB,IAAI,AACf,CAFuC,AAQ9B,CAR+B,MAQxB,CAAC,CAAI,CAAqB,CAetC,OAZA,IAAI,CAAC,KAAM,CAAE,GAAQ,CACnB,KAAK,MAAE,EACP,EAAE,CAAE,IADY,GAEhB,EADa,QACH,MAAE,EACZ,OADqB,CACb,MAAE,CAChB,CAAK,CAEG,IAAI,CAAC,CAHY,OAGJ,EAAE,KACjB,aAAa,AAAb,EAAc,IAAI,CAAC,QAAQ,CAAE,MAAE,CAAK,CAAC,CAAC,CAAF,AAGtC,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,OAAO,EAAqB,CACjC,OAAO,IAAI,CAAC,KAAK,AACrB,CAMS,OAAO,CAAC,CAAI,CAAsC,CAMvD,OALA,IAAI,CAAC,KAAA,CAAQ,CACX,GAAG,IAAI,CAAC,KAAK,CACb,GAAG,CAAI,AACb,CAAK,CACD,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,MAAM,CAAC,CAAG,CAAU,CAAK,CAAmB,CAGjD,OAFA,IAAI,CAAC,KAAM,CAAE,CAAE,GAAG,IAAI,CAAC,KAAK,CAAE,CAAC,EAAG,CAAA,AAAG,CAAA,CAAO,CAC5C,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAMS,SAAS,CAAC,CAAM,CAAgB,CAMrC,OALA,IAAI,CAAC,MAAA,CAAS,CACZ,GAAG,IAAI,CAAC,MAAM,CACd,GAAG,CAAM,AACf,CAAK,CACD,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,QAAQ,CAAC,CAAG,CAAU,CAAK,CAAe,CAG/C,OAFA,IAAI,CAAC,MAAO,CAAE,CAAE,GAAG,IAAI,CAAC,MAAM,CAAE,CAAC,EAAG,CAAA,AAAG,CAAA,CAAO,CAC9C,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAMS,cAAc,CAAC,CAAW,CAAkB,CAGjD,OAFA,IAAI,CAAC,YAAa,CAAE,EACpB,IAAI,CAAC,IAD0B,iBACL,EAAE,CACrB,IAAI,AACf,CAKS,QAAQ,CAAC,CAAK,CAAuB,CAG1C,OAFA,IAAI,CAAC,MAAO,CAAE,EACd,GADmB,CACf,CAAC,qBAAqB,EAAE,CACrB,IACX,AADe,CAcN,kBAAkB,CAAC,CAAI,CAAiB,CAG7C,OAFA,IAAI,CAAC,gBAAiB,CAAE,EACxB,EAD4B,EACxB,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAOS,UAAU,CAAC,CAAG,CAAU,CAAO,CAAwB,CAS5D,OARgB,IAAI,EAAE,CAAlB,EAEF,KAFU,EAEH,IAAI,CAAC,SAAS,CAAC,EAAI,CAAD,AAEzB,IAAI,CAAC,SAAS,CAAC,EAAG,CAAI,AAAJ,EAGpB,IAAI,CAH2B,AAG1B,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,UAAU,CAAC,CAAO,CAAkB,CAOzC,OANK,EAGH,IAAI,CAAC,AAHK,EAAE,MAGE,CAAE,EAFhB,KAEuB,EAFhB,IAAI,CAAC,QAAQ,CAItB,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,UAAU,EAAwB,CACvC,OAAO,IAAI,CAAC,QAAQ,AACxB,CAQS,MAAM,CAAC,CAAc,CAAyB,CACnD,GAAI,CAAC,EACH,OAAO,IAAI,CAGb,AAJmB,EAAE,EAIf,EAAyC,UAAW,AAApD,EAAe,OAAO,EAAgC,EAAe,IAAI,CAAA,CAAI,EAS7E,EATqC,EAA+B,EASlE,CAAI,KATqF,EASnF,CAAK,MAAE,CAAI,UAAE,CAAQ,OAAE,CAAK,aAAE,EAAc,EAAE,OAAJ,aAAM,CAAA,CAAA,CAAuB,CANnF,YAMmF,CAN3D,EACpB,EAAa,UAAD,EAAa,GAAA,CAAA,EACzB,EAAA,aAAA,AAAa,EAAC,GACX,OACD,CAAA,CAFwB,AAEf,EAEqF,CAAA,CAAE,CAHhF,AAyBxB,OApBA,IAAI,CAAC,KAAM,CAAE,CAAE,GAAG,IAAI,CAAC,KAAK,CAAE,GAAG,CAAA,CAAM,CACvC,IAAI,CAAC,MAAO,CAAE,CAAE,GAAG,IAAI,CAAC,MAAM,CAAE,GAAG,CAAA,CAAO,CAC1C,IAAI,CAAC,SAAU,CAAE,CAAE,GAAG,IAAI,CAAC,SAAS,CAAE,GAAG,CAAA,CAAU,CAE/C,GAAQ,CAAH,KAAS,CAAC,IAAI,CAAC,GAAM,CAAF,CAAC,IAAO,EAAE,CACpC,IAAI,CAAC,KAAM,CAAE,CAAA,CAAI,CAGf,GACF,EADO,EAAE,CACL,CAAC,MAAO,CAAE,CAAA,CAAK,CAGjB,EAAY,MAAM,EAAE,CAAT,AACb,IAAI,CAAC,YAAa,CAAE,CAAA,CAAW,CAG7B,IACF,IAAI,CAAC,SADe,EAAE,QACG,CAAE,CAAA,CAAkB,CAGxC,IAAI,AACf,CAMS,KAAK,EAAS,CAgBnB,OAdA,IAAI,CAAC,YAAa,CAAE,EAAE,CACtB,IAAI,CAAC,KAAM,CAAE,CAAA,CAAE,CACf,IAAI,CAAC,MAAO,CAAE,CAAA,CAAE,CAChB,IAAI,CAAC,KAAM,CAAE,CAAA,CAAE,CACf,IAAI,CAAC,SAAU,CAAE,CAAA,CAAE,CACnB,IAAI,CAAC,MAAO,CAAE,OACd,EADuB,EACnB,CAAC,gBAAiB,MAAE,EACxB,IAAI,CAAC,EAD4B,UACf,MAAE,EACpB,IAAI,CAAC,EADwB,MACf,MAAE,OAChB,EADyB,cACzB,AAAgB,EAAC,IAAI,MAAE,GACvB,IAAI,CAAC,CAD2B,CAAC,UACf,CAAE,EAAE,CACtB,IAAI,CAAC,qBAAqB,CAAC,CAAE,OAAO,CAAA,CAAA,EAAA,EAAE,eAAA,AAAe,EAAE,EAAE,UAAU,CAAE,IAAI,CAAC,MAAM,EAAC,CAAG,CAAC,CAErF,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAMS,aAAa,CAAC,CAAU,CAAc,CAAc,CAAiB,CAC1E,IAAM,EAAsC,OAA5B,CAAqC,EAAnC,OAAO,EAA8B,EApb3B,GAAG,CAub/B,GAAI,GAHqB,AAGR,CAAC,CAHoD,AAIpE,CADkB,EAHoD,CAG1D,GACL,IAAI,CAGb,IAAM,EAA+B,CACnC,OAR6F,EAQpF,CAAA,CAAA,EADW,AACX,EAAE,sBAAsB,AAAtB,EAAwB,EACnC,GAAG,CAAU,CAEb,OAAO,CAAE,EAAW,OAAA,CAAA,AAAD,CAAC,EAAU,EAAA,QAAA,AAAQ,EAAC,EAAW,OAAO,CAAR,AAAU,IAAI,EAAI,EAAW,OAAO,AAC3F,CADmF,AAC9E,CAUD,OARA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GACnB,IAAI,CAAC,QAD8B,CAAC,GACnB,CAAC,MAAA,CAAS,IAC7B,IAAI,CAAC,AADiC,EAAE,UACtB,CAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAC7C,IAAI,CAAC,CADiD,CAAC,KAC3C,EAAE,kBAAkB,CAAC,iBAAiB,CAAE,UAAU,CAAC,EAGjE,IAAI,CAAC,qBAAqB,EAAE,CAErB,IAAI,AACf,CAKS,iBAAiB,EAA2B,CACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAA,CAAS,CAAC,CACzD,AAD0D,CAMjD,gBAAgB,EAAS,CAG9B,OAFA,IAAI,CAAC,YAAa,CAAE,EAAE,CACtB,IAAI,CAAC,qBAAqB,EAAE,CACrB,IAAI,AACf,CAKS,aAAa,CAAC,CAAU,CAAoB,CAEjD,OADA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAChB,IAAI,AACf,CAKS,EAP4B,CAAC,aAOb,EAAS,CAE9B,OADA,IAAI,CAAC,YAAa,CAAE,EAAE,CACf,IAAI,AACf,CAKS,YAAY,EAAc,CAC/B,MAAO,CACL,WAAW,CAAE,IAAI,CAAC,YAAY,CAC9B,WAAW,CAAE,IAAI,CAAC,YAAY,CAC9B,QAAQ,CAAE,IAAI,CAAC,SAAS,CACxB,IAAI,CAAE,IAAI,CAAC,KAAK,CAChB,KAAK,CAAE,IAAI,CAAC,MAAM,CAClB,IAAI,CAAE,IAAI,CAAC,KAAK,CAChB,KAAK,CAAE,IAAI,CAAC,MAAM,CAClB,WAAW,CAAE,IAAI,CAAC,YAAa,EAAG,EAAE,CACpC,eAAe,CAAE,IAAI,CAAC,gBAAgB,CACtC,kBAAkB,CAAE,IAAI,CAAC,mBAAmB,CAC5C,qBAAqB,CAAE,IAAI,CAAC,sBAAsB,CAClD,eAAe,CAAE,IAAI,CAAC,gBAAgB,CACtC,IAAI,CAAA,CAAA,EAAA,EAAE,gBAAA,AAAgB,EAAC,IAAI,CAAC,AAClC,CAAK,AACL,CAKS,wBAAwB,CAAC,CAAO,CAA+B,CAEpE,OADA,IAAI,CAAC,sBAAuB,CAAA,CAAA,EAAA,EAAE,KAAK,AAAL,EAAM,IAAI,CAAC,sBAAsB,CAAE,EAAS,CAAC,CAAC,CACrE,EADiE,EAC7D,AACf,CAKS,qBAAqB,CAAC,CAAO,CAA4B,CAE9D,OADA,IAAI,CAAC,mBAAoB,CAAE,EACpB,IAAI,AACf,CAFsC,AAO7B,qBAAqB,EAAuB,CACjD,OAAO,IAAI,CAAC,mBAAmB,AACnC,CAOS,gBAAgB,CAAC,CAAS,CAAW,CAAI,CAAsB,CACpE,IAAM,EAAU,GAAM,CAAF,OAAW,EAAA,CAAA,EAAG,EAAA,KAAA,AAAK,EAAE,EAEzC,GAAI,CAAC,IAAI,CAAC,OAAO,CAEf,CAFiB,QACjB,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CACnE,EAGT,IAAM,CAHU,CAGW,AAAI,KAAK,CAAC,UAAZ,iBAAuC,CAAC,CAajE,OAXA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC3B,EACA,CACE,MAFO,WAEU,CAAE,SAAS,YAC5B,EACA,GAAG,CAAI,CACP,QAAQ,CAAE,CAClB,CAH0B,AAGnB,CACD,IAAI,AAFe,EAKd,CACX,CAOS,KARS,SAQK,CAAC,CAAO,CAAU,CAAK,CAAkB,CAAI,CAAsB,CACtF,IAAM,EAAU,GAAM,CAAF,OAAW,EAAA,CAAA,EAAA,EAAG,KAAA,AAAK,EAAE,EAEzC,GAAI,CAAC,IAAI,CAAC,OAAO,CAEf,CAFiB,QACjB,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CACjE,EAGT,IAAM,CAHU,CAGW,AAAI,KAAK,CAAC,GAcrC,IAd4C,CAAC,EAApB,AAEzB,IAAI,CAAC,OAAO,CAAC,cAAc,CACzB,EACA,EACA,CACE,EAHK,AACF,eAEc,CAAE,OAAO,cAC1B,EACA,GAAG,CAAI,CACP,QAAQ,CAAE,CAClB,CAAO,AAHmB,CAIpB,IAFmB,AAEf,EAGC,CACX,CAOS,KARS,OAQG,CAAC,CAAK,CAAS,CAAI,CAAsB,CAC1D,IAAM,EAAU,GAAM,CAAF,OAAW,EAAA,CAAG,EAAA,EAAA,KAAK,AAAL,EAAO,SAEpC,IAAI,CAAC,OAAO,CAKjB,CALmB,GAKf,CAAC,OAAO,CAAC,YAAY,CAAC,EAAO,CAAE,EAAJ,CAAO,CAAI,CAAE,QAAQ,CAAE,CAAA,CAAS,CAAE,IAAI,CAAC,GAJpE,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAC/D,CAMb,CAKY,KAXQ,gBAWa,EAAS,CAIjC,IAAI,CAAC,mBAAmB,EAAE,CAC7B,IAAI,CAAC,mBAAoB,EAAE,EAC3B,EAD+B,EAC3B,CAAC,eAAe,CAAC,OAAO,CAAA,AAAC,IAC3B,EAAS,IAAI,CAAC,AACtB,CADgB,AAD+B,AAExC,CAAC,CACF,IAAI,CAAC,mBAAoB,EAAE,EAEjC,CACA,EAHsC,oJC3oB/B,SAAS,IACd,MAAA,CAAA,EAAA,EAAO,OAD6B,GAAU,QACvC,AAAkB,EAAC,qBAAqB,CAAE,IAAM,IAAA,EAAI,KAAK,CAClE,CAGO,AAJ6D,CAAC,QAIrD,IACd,MAAA,CAAA,EAAA,EAAO,SAD+B,GAAU,MACzC,AAAkB,EAAC,uBAAuB,CAAE,IAAM,IAAA,EAAI,KAAK,CACpE,CADsE,CAAC,8KCKhE,OAAM,EAIJ,WAAW,CAAC,CAAK,CAAU,CAAc,AAJnB,CAI6B,AAJ7B,KAKvB,EAOA,EAHF,EAHG,GACa,EADR,EADO,AACL,AACM,EAAI,EAEpB,GAFyB,AAET,CAOhB,CAT2B,CAMxB,CADqB,CAHH,CAKI,IAAA,EAAI,KAAK,AADjB,CAOnB,CAPqB,AACiB,GAMlC,CAJF,AAIG,GAJsB,GAItB,CAAS,CAAC,CAAE,KAAK,CAAE,CAAc,CAJG,AAIF,CAAC,CACxC,IAAI,CAAC,IADiC,WACjB,CAAE,CAC3B,CAKS,SAAS,CAAI,CAAQ,CAA0B,CACpD,IAEI,EAFE,CAPuC,CAO/B,GAAF,CAAM,CAAC,SAEG,CAFO,EAAE,CAG/B,GAAI,CACF,EAAqB,EAAS,EACpC,CAAM,EADmC,CAAN,AAAO,GAC7B,CAAC,CAAE,CAEV,EAHmB,IAEnB,IAAI,CAAC,SAAS,EAAE,CACV,CAAC,AACb,OAEI,CAAA,EAAA,EAAI,UAAA,AAAU,EAAC,GAEN,EAAmB,IAAI,CAC5B,IACE,EADK,EAHsB,AAIvB,CAAC,AAJuB,EAEP,AAFS,OAIhB,EAAE,CACT,GAAG,AACX,AACD,IAEE,CAFG,KACH,IAAI,CAAC,SAAS,EAAE,CACV,CAAC,AACjB,CAAS,GAIL,IAAI,CAAC,SAAS,EAAE,CACT,EACX,CAKS,SAAS,EAAoC,CAClD,GAPyB,IAOlB,IAAI,CAAC,WAAW,EAAE,CAAC,MAAO,AACrC,CAKS,QAAQ,EAAU,CACvB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,AACnC,CAKS,iBAAiB,EAAU,CAChC,OAAO,IAAI,CAAC,eAAe,AAC/B,CAKS,WAAW,EAAU,CAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAA,CAAS,CAAC,CAAE,AAC/C,CAKU,UAAU,EAAU,CAE1B,IAAM,EAAQ,GAAF,CAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAKrC,OAJA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CACf,MAAM,CAAE,IAAI,CAAC,SAAS,EAAE,OACxB,CACN,CAAK,CAAC,CACK,CAFA,AAGX,CAKU,GANM,MAMG,EAAY,OAC3B,EAAI,IAAI,CAAC,MAAM,CAAC,MAAA,GAAU,CAAC,EAAE,AACtB,CAAC,CAAC,IAAI,CADuB,AACtB,KAD2B,CACrB,CAAC,GAAG,EAAE,AAC9B,CACA,CAMA,SAAS,IACP,IAAM,EAAA,CAAW,EAAA,EAAA,CAAX,IADqB,GAAsB,MAChC,AAAc,EAAE,EAC3B,EAAO,CAAA,EAAA,CAAA,CAAE,gBAAA,AAAgB,EAAC,GAEhC,KAFwC,CAAC,CAEjC,EAAO,IAAD,CAAC,CAAQ,EAAO,IAAD,CAAO,EAAG,IAAI,EAAiB,CAAA,EAAA,EAAC,UAAD,YAAuB,AAAtB,EAAwB,EAAA,CAAA,EAAA,EAAE,wBAAA,AAAwB,EAAE,CAAC,CACpH,CAEA,SAAS,EAAa,CAAQ,EAA0B,AACtD,IADgB,GACT,IAAuB,SAAS,CAAC,EAC1C,CAEA,GAH6B,EAAE,AAAmB,CAAC,GAG1C,EAAgB,CAAK,CAAS,CAAQ,EAA0B,AACvE,IAAM,CADa,CACL,GAAR,CACN,OAAO,EAAM,GAAD,IADsB,EAAG,AACf,CAAC,KACrB,CAD2B,CACrB,GAAD,QAAY,EAAE,CAAC,KAAA,CAAQ,EACrB,EAAS,CADiB,GAGrC,CAFyB,AAIzB,CAJmB,AAAO,QAIjB,EAAsB,CAAQ,EAAmC,AACxE,OAAO,IAAuB,EADL,OACc,CAAC,IAC/B,EAAS,AADS,AAAmB,EAAjB,EACY,EAAxB,cAAqB,CAAoB,CAAlB,CAAoB,CAAC,CAE/D,CAKO,SAAS,IACd,MAAO,kBADmC,EAExC,CAFiE,WAGjE,MADkB,GACT,MACT,EACA,UADY,WACS,CAAE,CAAI,EAAwB,IAC1C,EAAmB,EAD+B,CAG3D,IAH0C,AAAoD,CAC1D,CAAC,OAAV,EAEZ,CAAE,IAAM,IAAuB,QAAQ,EAAE,CACxD,KAD2C,EAAE,UAC5B,CAAE,IAAM,IAAuB,gBAAH,CAAoB,CAAlB,CAAoB,AACvE,CAAG,AACH,sJC1JO,SAAS,EAAwB,CAAQ,EAA0C,AAExF,IAAM,EAAA,CAAA,EAAA,EAAW,CAAX,MAF+B,OAEpB,AAAc,EAAE,CAEjC,CADa,CAAE,EAAA,EAAA,AACT,gBADS,AAAgB,EAAC,GACzB,GAAI,CAAE,CAD2B,AAE1C,CAF2C,AAQpC,MAPgB,GAOP,EAAwB,CAAO,EAAiC,AAC9E,IAAM,EAAO,CAAA,EAAA,CAAA,CAAE,OADsB,SACtB,AAAgB,EAAC,OAAO,CAAC,EAExC,AAAI,EAAO,GAAG,CAAJ,AACD,CADO,CACA,GAAG,CAInB,AAJe,CAIf,EAAA,EAAO,4BAA4B,AAA5B,EAA8B,CACvC,gRCpBO,SAAS,IACd,IAAM,EAAA,CAAA,EAAA,EADuB,AACvB,AAAU,GADuB,WACvB,AAAc,EAAE,EAEhC,MAAO,AADG,CAAA,EAAA,AACA,EADE,uBAAA,AAAuB,EAAC,GACzB,IADgC,CAAC,IAAhC,MACc,EAAE,AAC9B,CAMO,SAAS,IACd,IAAM,EAAA,CAAA,EAAA,EAAA,AAAU,EADe,GAAU,SACzB,AAAc,EAAE,EAEhC,MADU,AACH,CADK,EACF,AADE,EAAA,uBAAuB,AAAvB,EAAwB,GACzB,IADgC,CAAC,YAChB,EAAE,AAChC,CAMO,SAAS,IACd,MAAA,CAAA,EAAO,CADqB,CACrB,EAD+B,gBAC/B,AAAkB,EAAC,aAAa,CAAE,IAAM,IAAA,EAAI,KAAK,CAC1D,CAD4D,AAgBrD,CAhBsD,QAgB7C,EACd,GAAG,CAAA,EAEH,CAHuB,GAGjB,EAAA,CAAA,EAAA,EAAA,AAAU,cAAA,AAAc,EAAE,EAC1B,EAAI,CAAA,EAAE,EAAA,uBAAA,AAAuB,EAAC,GAGpC,GAAoB,CAHuB,AAGtB,CAHuB,EAGxC,EAAK,EAAD,IAAQ,CAAO,CACrB,GAAM,CAAC,EAAO,EAAQ,CAAI,AAAd,IAAkB,CAAR,IAEtB,AAAK,EAIE,EAJH,AAIO,CAJD,AAIA,EAJE,SAIW,CAAC,EAAO,GAAF,AAHpB,EAAI,CAAD,EAG2B,CAAC,KAHlB,CAAC,EAI3B,CAEE,KANiC,CAAC,CAM3B,EAAI,CAAD,QAAU,CAAC,CAAI,CAAC,CAAC,CAAC,CAAC,AAC/B,CA6BO,SAAS,EACd,GAAG,CAAA,EAIH,IAAM,EAAA,CAAA,EAAA,CAL0B,CAK1B,AAAU,cAAA,AAAc,EAAE,EAC1B,EAAI,CAAA,EAAE,EAAA,uBAAA,AAAuB,EAAC,GAGpC,GAAoB,CAHuB,AAGtB,CAHuB,EAGxC,EAAK,EAAD,IAAQ,CAAO,CACrB,GAAM,CAAC,EAAgB,EAAQ,CAAI,IAAI,CAAR,IAAV,AAErB,AAAK,EAIE,EAAI,AAJP,CAIM,SAJS,EAAE,SAIW,CAAC,EAAgB,GAHxC,EAAI,CAAD,EAG6C,CAAC,GAAX,WAHhB,CAAC,EAIpC,CAEE,KAN0C,CAAC,CAMpC,EAAI,CAAD,iBAAmB,CAAC,CAAI,CAAC,CAAC,CAAC,CAAC,AACxC,CAKO,SAAS,IACd,KADuB,EAChB,CADoD,GAClC,SAAS,EAAK,AACzC,AADwB,CAMjB,CANmB,QAMV,EAAyB,CAAK,EAAuB,AAGnE,GAAM,CAAE,SAAO,CAAE,KAHqB,SAGT,mBAAE,CAAA,CAAoB,CAFxB,EAAM,AAEoB,GAFrB,eAEuC,GAFjB,EAAE,CAIlD,EAA6B,CACjC,QAAQ,CAAE,AADM,EAEhB,KADiB,EACV,CAAE,GAAA,CAAA,EAAA,EAAqB,SAArB,KAAqB,AAAc,EAAE,CAClD,CAAG,CAMD,OAJI,IACF,EAAa,MADC,EAAE,EACJ,IAAgB,CAAE,CAAA,CAAY,CAGrC,CACT,WADqB,yEC5Id,IAAM,EAAsB,iBAAF,+HCIf,MAAA,CAAA,SAmBX,SAAS,EAAuB,CAAK,EAAuC,AACjF,OAAO,IAAI,EAAW,AAAC,CADU,GAE/B,EAAQ,EACZ,CAFwB,AAErB,CAAC,AACJ,CAHoC,AACzB,AAAM,AAUV,CAVW,QAUF,EAA+B,CAAM,EAAwB,AAC3E,OAAO,IAAI,EAAY,CAAC,AADS,CACR,CAAE,KACzB,CADoB,AAAW,CACxB,EACX,CAAG,CACH,AAHwC,AAC9B,AACN,EAlCc,AAiCD,CAAC,QAjCA,CAAA,EAEL,AAFK,CAEL,CAAA,EAAA,IAAA,GAAA,CAAD,CAAC,CAAA,CAAA,MAAA,GAAA,CAEC,CAAA,CAAA,EAAA,IAAA,IAAA,CAAD,CAAC,CAAA,CAAA,OAAA,GAAA,CAEA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAD,CAAC,CAAA,CAAA,OAAA,GAAA,AACd,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,AAoCO,OAAM,EAKJ,SALe,CAA8B,CAKlC,CAAC,CAAQ,CAAe,CACxC,IAAI,CAAC,MAAA,CAAS,EAAO,IAAD,GAAQ,CAC5B,IAAI,CAAC,SAAU,CAAE,EAAE,CAEnB,IAAI,CAAC,YAAY,CAAC,EACtB,CAGS,IAAI,CACT,AAL0B,CAKf,AALgB,CAM3B,CAAU,CACwB,CAClC,OAAO,IAAI,EAAY,CAAC,EAAS,KAAF,AAC7B,CADqC,AAAjB,GAChB,CAAC,CADqC,QAC5B,CAAC,IAAI,CAAC,EAClB,EACA,GADK,CAEH,GAAK,CAAD,CAKF,CANM,EAMF,CACF,EAAQ,EAAY,CANR,EAO1B,AAP4B,AAMP,CACP,EAD0B,CAAC,CAAC,CAAT,CACZ,CAAC,CAAE,CACV,EAAO,CAAC,CAAC,AACvB,EADoB,IALR,EAAQ,EAQpB,CAAS,CACD,CATW,CAAC,EAUV,AAVwB,GAUnB,CAAD,CAGF,CAJM,EAIF,CACF,EAAQ,EAJG,AAIQ,EAJN,CAK3B,AADqB,CACP,EADyB,CAAC,CAAC,AAAT,EACX,CAAC,CAAE,CACV,EAAO,CAAC,CACtB,AADuB,EAAH,IALR,EAAO,EAQnB,CAAS,CACF,AATW,CASV,CACF,AAVmB,CAAC,GAUhB,CAAC,gBAAgB,EAAE,AAC7B,CAAK,CAAC,AACN,CAGS,KAAK,CACV,CAAU,CACgB,CAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,AAAD,GAAQ,EAAK,CAAF,CAC/B,CAGS,OAJkC,AAI3B,CAJ4B,AAIlB,CAAS,CAA8C,CAC7E,OAAO,IAAI,EAAqB,CAAC,EAAS,KAAF,AACtC,CADoB,AAA0B,GAC1C,EAD+C,AAE/C,CADG,CAGP,OAAO,CAFO,GAEH,CAAC,IAAI,CAAA,AACd,IACE,GAAa,EACb,AAFO,EAED,CADY,AAClB,CACI,CAFJ,EACW,AAET,GAEZ,CAAS,CACD,CAJe,EAAE,CACJ,AAIX,EAJa,CAIA,EADL,AAER,EAAM,AADW,CACjB,CACI,CAFJ,EAGE,CAFU,EAItB,CAAS,EACD,AAJe,EAAE,CACJ,CAGT,CAHW,AAGV,KACL,CADW,EACP,EAAY,QAAF,IACZ,EAAO,GAAG,AAIZ,CAJQ,AAAK,CAIL,EAChB,CADgB,AACT,CAAC,AACR,CAFe,AAAuB,AAEjC,CAAC,AACN,CAGU,gBAAgB,EAAS,CAC/B,GAAI,IAAI,CAAC,MAAA,GAAW,EAAO,IAAD,GAAQ,CAChC,CADkC,MAIpC,IAAM,EAAiB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAC7C,IAAI,CAAC,SAAU,CAAE,EAAE,CAEnB,EAAe,OAAO,CAAA,AAAC,IAAT,AACR,CAAO,CAAC,CAAC,CAAC,EAAE,CADgB,AAK5B,IAAI,CAAC,MAAA,GAAW,EAAO,IAAD,IAAS,EAAE,AACnC,CAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAA,EAAyB,AAGvC,IAAI,CAAC,MAAA,GAAW,EAAO,IAAD,IAAS,EAAE,AACnC,CAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAGzB,CAAO,CAAC,CAAC,CAAA,EAAI,EACnB,CAAK,CADkB,AACjB,AACN,CAGU,YAAY,CAAC,CAAQ,CAAqB,CAChD,IAAM,EAAY,CAAC,EAAe,GAAV,CAAR,CAAuB,AACrC,GAAI,EADuE,EACnE,CAAC,MAAA,GAAW,EAAO,IAAD,GAAQ,EAAE,AAIpC,GAAA,CAAI,EAAA,EAAA,UAAU,AAAV,EAAW,GAAQ,EAAH,CAAC,SACb,EAAyB,GAAzB,CAA6B,CAAC,EAAS,EAI/C,GAJ6C,CAAQ,CAAC,AAIlD,CAAC,MAAO,CAAE,EACd,GADmB,CACf,CAAC,MAAO,CAAE,EAEd,GAFmB,CAEf,CAAC,gBAAgB,EAAE,CAC7B,CAAK,CAEK,EAAU,AAAC,IACf,CADI,AAAgB,CACV,EAAO,EADuB,EACxB,CAAP,GAAgB,CAAE,EACjC,CAAK,CAEK,CAH4B,CAGlB,AAAD,AAHoB,IAIjC,AADI,EAAgB,AACV,EAAO,GADuB,CACxB,CAAP,GAAgB,CAAE,EACjC,CAAK,CAED,EAHmC,CAAC,AAGhC,CACF,EAAS,EAAS,EACxB,CAAM,CADQ,CAAQ,CAAQ,CAAC,EAClB,CAAC,CAAE,CACV,EAAO,CAAC,CAAC,AACf,CACA,CACA,AAHY,kFCpLL,SAAS,EACd,CAAU,CACV,CAAK,CACL,CAAI,CACJ,EAAgB,CAAC,EAAZ,AAEL,OAAO,CAN4B,GAM5B,EAAI,WAAW,CAAe,CAAC,EAAS,KAAF,AAC3C,CADmD,GAC7C,EADkD,AACtC,CAAU,CAAC,EAAM,CACnC,EADkC,AAAlB,CACF,GAFL,CAEK,GAAV,GAAuC,EAAvC,QAAiD,EAA/B,AAAiC,OAA1B,EAC3B,EAAQ,KADmB,AACpB,AAAM,CAAC,CACT,CACL,IAAM,EAAS,EAAU,CAAE,CAAd,EAAiB,CAAM,CAAC,CAAE,AAAf,IAAmB,CAAE,AAE7C,WAAA,EAAe,EAAU,EAAA,EAAiB,GAAlB,CAAkB,GAAX,GAAW,EAAQ,CAAnB,KAAyB,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,EAAU,EAAE,CAAC,IAAJ,WAAmB,CAAC,CAAC,CAE/G,CAAA,EAAA,EAAI,UAAA,AAAU,EAAC,GACR,EACF,CAFgB,CAAC,EAEb,AAFe,CAEf,AAAC,GAAS,EAAsB,AAA/B,EAA2C,EAAO,EAAM,CAAR,CAAM,AAAU,CAAC,CAAxB,AAAyB,CAAC,IAAI,CAAC,GAA1C,CACnC,GADoF,CAAC,AACjF,CAAC,IAAI,CAAE,GAET,EAAsB,CAFP,CAEmB,AAFlB,EAE0B,EAAM,EAAR,AAAM,AAAU,CAAC,CAAzB,CAAsB,AACxD,IAAI,CAAC,GACL,AAFuB,IACX,AACR,CAAC,IAAI,CAAE,EAEtB,CACA,CAAG,CAAC,AACJ,CAJ4B,CAAC,8GC5B7B,EAAA,CAAA,CAAA,ytBACO,IAAM,EAAmC,gBAQnC,EAAwC,YARP,SAgBjC,EAAuD,YARjB,wBAatC,EAA+B,YALsB,AAUrD,EAAmC,YALN,IAQ7B,EAAoD,YAHnB,qBAMjC,EAA6C,YAHK,cAMlD,EAA8C,YAHH,eAY3C,EAA6C,YATD,cAc5C,EAAgC,YALW,QAO3C,EAAoC,KAFN,mBAI9B,EAA+B,KAFG,OAIlC,EAA+B,YAFF,AAI7B,EAAqC,YAFR,MAK7B,EAAyC,YAHN,UAInC,EAA8B,WAY9B,CAbuC,CAaH,YAZR,mBAYM,mVCzExC,IAAM,EAA4B,UAE5B,EAAkC,WAFR,AAS1B,EAA4B,KASlC,SAAS,EAEd,AAlB2C,CAkB9B,EAEb,IAAM,AAb+B,EAaf,EAAmB,GAEzC,GAAI,CAAC,EAFe,AAGlB,IAHoD,CAAC,EAG9C,AAIT,AAPwC,IAOlC,AALY,EAKa,AALX,EAN+B,CAOjC,AANpB,GAUuC,CAAC,OAAO,CAAC,GAAe,MAAM,CAAyB,CAAC,EAAlC,AAAuC,CAAtC,AAAoC,AAAG,EAAK,CAAF,CAAQ,GAAD,CACvG,CAD6G,CACzG,CAAD,IAAM,CAAC,KAEZ,CAAG,CADoB,AACnB,EADuB,CAAD,IAAM,CAAC,EAA0B,IACzC,EAD+C,CAAC,CAChD,CAAI,CAAA,CAb5B,AAaiC,CAEtB,EAJsC,CAInC,AACT,AAL6C,CAK7C,CAAE,AAL6C,CAK5C,KAJwD,GAQ1D,AAAJ,MAAU,CAAC,IAAI,CAAC,GAAwB,MAAA,CAAS,CAAC,CACzC,CAD2C,MAGlD,CAEJ,CAWO,CAhBiC,CAAC,GAG9B,IAaK,EAEd,CAAsB,CAjBU,CAmBhC,AAjBkB,GAiBb,CAAD,CAeO,OAAA,EAVe,MAAM,CAAC,IALN,EAAE,CAKW,CAAC,GAAwB,CAUtD,CAAA,IAV4D,AATd,CAUvD,AATJ,CASK,EAAK,CAAF,AAAG,EAAQ,EAAS,EAAX,EACP,AAQG,AAVoD,CAChC,AASpB,AAVqD,CACvC,EAEnB,CAAG,CAAC,CAAC,CADK,CACL,CADO,CACP,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAEA,GAAA,AAEA,CAAA,CAAA,EAIA,CAKA,KAbA,IAaA,EACA,CAAA,EAEA,GAAA,GAAA,CAAA,CAAA,EAAA,EAAA,CAHA,GAGA,IAAA,CA3Bb,CA2Ba,IAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAIA,KAAA,CAAA,OAAA,CAAA,GAEA,EAAA,MAAA,CAAA,CAFA,AAEA,CAFA,CAEA,CAAA,AAFA,GAEA,CAEA,IAFA,EAEA,CAAA,OAAA,CADA,AACA,EADA,IAAA,AACA,CADA,MACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CADA,EACA,CACA,CAAA,AADA,CACA,EAAA,CAAA,CACA,CAAA,CAAA,CACA,CAFA,EAEA,AACA,CAAA,CAAA,CAAA,CAGA,EAAA,EACA,CAQA,SAAA,CATA,CAAA,AASA,CAAA,EAAA,AACA,EAVA,KAUA,EACA,KAAA,CAAA,CAFA,EAEA,EACA,GAAA,CAAA,GACA,EAAA,KAAA,CAAA,CADA,EACA,CAAA,CAAA,GAAA,CAAA,IACA,GAAA,CACA,EAFA,IAAA,CAEA,kBAAA,CAAA,EAAA,IAAA,EAAA,CACA,AADA,CACA,AADA,AACA,KAAA,CAGA,MACA,CACA,CAAA,CAAA,EAEA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,GAAA,CACA,CADA,EACA,IACA,CADA,AACA,CAAA,CADA,CACA,CAAA,CAAA,CAAA,CAEA,GAAA,AACA,CAAA,CAAA,CACA,AADA,CAUA,SAAA,EAAA,CAAA,EAAA,AACA,GAAA,CAAA,EAAA,CAAA,MAAA,CAAA,EADA,EACA,CAAA,GAAA,GAAA,CAAA,EAAA,CAKA,OAAA,MAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,IAAA,CACA,AADA,GAAA,CACA,EAAA,CAAA,AADA,EACA,GADA,IACA,WAAA,CAAA,GAAA,CAAA,EAAA,GAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CACA,EAAA,CAAA,GADA,AACA,CADA,CACA,EAAA,CAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,KAAA,GACA,CADA,CACA,MAAA,CAAA,KACA,EADA,SACA,EAAA,EACA,KAFA,CAEA,CAAA,AAFA,IAEA,CACA,CAAA,gBAAA,EAAA,EAAA,OAAA,IAAA,EAAA,EAAA,SAAA,+CAAA,CAAA,EAEA,GAEA,CAEA,CAAA,CAAA,EAAA,CACA,AADA,IAJA,MAEA,kKCxJb,IAAM,EAAe,UAAf,CAA0B,CAG1B,EAAY,OAAZ,0DAA6E,CAe5E,SAAS,EAAY,CAAG,CAAiB,GAAwB,CAAK,EAAU,AACrF,CADyB,EACnB,GADoD,GAClD,CAAI,MAAE,CAAI,MAAE,CAAI,MAAE,CAAI,WAAE,CAAS,UAAE,CAAQ,WAAE,CAAU,CAAA,CAAI,EACnE,CADsE,KAEpE,CAAC,EAAA,EAAA,GAAA,EAAA,CAAA,CAAA,EAAA,GAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,AACA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,AAEA,AAHA,CAWA,EAVA,GAAA,IAUA,EAAA,CAAA,EAAA,AACA,IAAA,EAAA,EAAA,AADA,CACA,GAAA,CAAA,EAAA,CAAA,AAEA,CAFA,EAEA,CAAA,EAAA,GAAA,WAEA,EAAA,cAAA,EAAA,KAEA,CAFA,MAEA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,EAAA,CAAA,CAAA,AACA,CAAA,CAAA,CAIA,GAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CACA,EAAA,EAAA,CACA,EAAA,EAEA,EAAA,EAAA,CAFA,AAEA,CAFA,GAEA,CAAA,CAAA,EAAA,CAAA,CAMA,GALA,EAAA,GAAA,GAAA,CAAA,CAAA,EAAA,CACA,EAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CACA,EAAA,EAAA,GAAA,EAAA,EAGA,EAAA,CACA,IAAA,EADA,AACA,EAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CACA,IACA,EAAA,CAAA,CAAA,EAAA,CAEA,CAHA,AAKA,CAJA,CADA,KAKA,EAAA,MAAA,IAAA,GAAA,EAAA,EAAA,GAAA,IAAA,QAAA,OAAA,EAAA,EAAA,MAAA,CAAA,QAAA,IAAA,CAAA,CAAA,CAAA,AACA,CAEA,KAHA,IAGA,EAAA,CAAA,EAAA,AACA,MAAA,CACA,KAFA,GAEA,CAAA,EAAA,QAAA,CACA,SAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,CACA,IAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CACA,IAAA,CAAA,EAAA,IAAA,CACA,GADA,CACA,CAAA,EAAA,IAAA,EAAA,EAAA,CACA,IAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CACA,SAAA,CAAA,EAAA,QAAA,CACA,AADA,CACA,AACA,CA8CA,SAAA,EAAA,CAAA,EAAA,AACA,IAAA,EAAA,EAAA,CAAA,CAAA,GAAA,CAAA,GAEA,CAHA,MAGA,EAFA,CAAA,AAEA,CAAA,CAAA,CAAA,AACA,CAMA,SAAA,EAAA,CAAA,EAAA,AACA,EADA,EACA,EAAA,QAAA,SAAA,EAAA,EAAA,GAAA,CAAA,CAAA,GACA,CADA,CAAA,CAAA,AACA,GAxDA,AAwDA,MADA,CACA,EAxDA,CAAA,CAwDA,CAxDA,AACA,GAAA,CAAA,EAAA,EADA,SACA,CACA,CADA,MACA,EAGA,EAHA,CAGA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,CAAA,CAAA,GAAA,KAWA,CARA,AADA,CAAA,EASA,QATA,CAAA,MACA,KADA,CAAA,MAAA,AASA,CATA,CASA,UATA,CAAA,CACA,IAAA,CAAA,GACA,CAAA,CAAA,CAAA,CAAA,CAAA,CADA,CACA,CACA,EAAA,AAFA,EACA,IACA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,EAAA,OAAA,CAAA,CAAA,CAAA,EACA,IAAA,EASA,EAAA,KAAA,CAAA,CAAA,MAAA,CAAA,CA1FiB,AAAb,AA+FJ,CALA,GAKA,CAAA,GA/Fa,IAA2B,AAAb,OAAoB,CA+F/C,AA/F2B,EA+F3B,GACA,EAAA,GADA,CAAA,EAAA,AACA,CAAA,KAAA,CAAA,CAAA,qCAAA,EAAA,EAAA,CAAA,CAAA,CACA,GADA,EACA,CAGA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA,EAAA,CAAA,EAAA,GACA,MAAA,CAAA,KAAA,CAAA,CAAA,sBAAA,WAAA,EAAA,EAAA,CAAA,CAAA,EACA,KAAA,CAXA,MAAA,CAAA,KAAA,CAAA,CAAA,sCAAA,EAAA,EAAA,CAAA,CAAA,CACA,IAcA,AAfA,CACA,CAkCA,GAGA,OAHA,AAGA,CACA,AAJA,EAAA,OAGA,kHCtHE,SAAS,EACd,CAAY,EAEZ,GAAkC,OAHL,EAGe,EAAxC,OAAO,kBAAA,EAAoC,CAAC,kBAAkB,CAChE,CADkE,MAC3D,EAGT,GAHc,CAGR,EAAU,GAAa,CAAA,CAAf,CAAe,EAAG,IAAH,KAAG,AAAS,EAAE,GAAE,UAAU,EAAE,CACzD,MACE,CAAC,CAAC,IAE2B,GAFnB,CAEwB,EAAjC,CADL,CACa,KAAD,WAAkB,EAAW,CAAC,CAAC,EAAQ,KAAD,QAAc,AAAb,CAEnD,CAQO,IAAM,EAAoB,eAAF,+GAX/B,sDC7BO,IAAM,EAAoB,EACpB,EAAiB,EACjB,EAAoB,EAS1B,KAXwB,CACH,GAUZ,EAA0B,CAAU,CATrB,CAS2C,AACxE,GAAI,EAAa,KAAO,GAAT,AAAuB,GAAG,CACvC,CADyC,EADJ,AACf,GACf,CAAE,IAAI,CAAE,CAAA,CAAgB,CAGjC,GAAI,GAAc,KAAO,EAAV,AAAuB,GAAG,CACvC,CADyC,GAAlB,GACf,GACN,KAAK,EADW,CACR,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,UAAW,CAC7C,AADgE,MAC3D,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,YAAW,CAAqB,AAClE,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,IAAW,CAAa,AAC1D,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,SAAW,CAAkB,AAC/D,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,cAAW,CAAuB,AACpE,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,aAAW,CAAsB,AACnE,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,IAAW,CAAa,AAC1D,SACE,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,WAAW,CAAoB,AACvE,CAGE,GAAI,GAAc,KAAO,EAAV,AAAuB,GAAG,CACvC,CADyC,GAAlB,GACf,GACN,KAAK,EADW,CACR,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,QAAW,CAAiB,AAC9D,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,MAAW,CAAe,AAC5D,MAAK,GAAG,CACN,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,YAAW,CAAqB,AAClE,SACE,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,SAAW,CAAkB,AACrE,CAGE,MAAO,CAAE,IAAI,CAAE,EAAmB,OAAO,CAAE,OAAX,QAAW,CAAiB,AAC9D,CAMO,SAAS,EAAc,CAAI,CAAQ,CAAU,EAAgB,AAClE,EAAK,EAAD,EADuB,QACV,CAAC,2BAA2B,CAAE,GAE/C,IAAM,EAAa,CAFsC,CAAC,AAEb,EACzC,CAAuB,GADV,IAAsC,CAAC,CAC1C,MAA4B,EAAE,GADA,AAC7B,OAAQ,EACrB,EAAK,EAAD,OAAU,CAAC,EAEnB,QAF6B,CAAC,gIC7D9B,IAAM,EAA4B,cAAc,CAC1C,EAAsC,MADtC,iBAC6D,CAQ5D,SARD,AAQU,EAAwB,CAAI,CAAoB,CAAK,CAAS,CAAc,EAAe,AACrG,IAAI,EAAE,GACR,KAFmC,mBAEnC,AAAwB,EAAC,EAAM,EAAF,AAAuC,QACpE,MADkF,CAAC,iBACnF,AAAwB,CAD0C,CACzC,EAAM,EAAF,AAA6B,GAE9D,CAKO,CAP4D,CAAC,OAOpD,EAAwB,CAAI,EAAmD,AAC7F,KAR0D,CAQnD,CACL,KAAK,CAAG,CAAA,CAAwB,EAA0B,CAC1D,AAHmC,cAGrB,CAAG,CAAA,CAAwB,EAAoC,AACjF,CAAG,AACH,EAH6D,8BACmB,mBCnBzE,SAAS,EAAgB,CAAU,EAA+B,AACvE,GAA0B,OADG,EACM,EAA/B,AAAiC,OAA1B,EACT,OAAO,CADa,KACP,CAAC,GAGhB,IAAM,EAA6B,CAHT,CAAC,AAGrB,MAAsC,EAA/B,OAAO,EAA0B,QAAf,EAAyB,CAAC,GAAc,EACvE,KAAoB,AAD+C,CAAA,EAAc,KAC7D,EAAhB,OAAO,GAAqB,KAAK,CAAC,EAAI,CAAE,IAAG,GAAO,CAAA,GAAK,IAAK,AAAE,CAAC,CAInE,CAJqE,MAI9D,CACT,CAZA,EAWa,AAXb,CAAA,CAAA,yPCEa,IAAA,EAAqB,AAAI,MAAM,CAC1C,SADW,CACA,GAAA,aAAA,mCAcN,SAAS,EAAuB,CAAW,EAAwC,IAUpF,EATJ,GAAI,CAAC,EACH,KAFkC,AAUnB,EARR,AAGT,EAJgB,EAIV,AAJY,EAIF,EAAY,CAHV,IAGe,CAAC,GAAP,AAC3B,GAAK,CAAD,CAWJ,KAXY,CAKO,CALL,EAKQ,CAN8B,CAM5B,AAN6B,CAMjD,CAAO,CAAC,CAAC,CAAE,CACb,GAAgB,EACQ,EADJ,CACO,EAAE,CAApB,CAAO,CAAC,AADjB,CACkB,CAAA,GAClB,GAAgB,CAAA,CAAK,CAGhB,CACL,MAJA,CAIO,CAAE,CAAO,CAAC,CAAC,CAAC,CACnB,aAAa,GACb,YAAY,CAAE,CAAO,CAAC,CAAC,CAAC,AAC5B,CAAG,AACH,CAMO,SAAS,EACd,CAAW,CACX,CAAO,EAEP,IAAM,EAAkB,EAAuB,GACzC,EAAuB,CAAA,EAAA,EAAE,CADT,AAAoC,CAAC,EAJhB,MAIG,KACjB,sBAAE,AAAqC,EAAC,GAErE,GAAI,CAFwE,AAEvE,CAFwE,EAEvD,OAAO,CAC3B,CAD6B,GAAX,EACX,CACL,OAAO,CAAA,CAAA,EAAA,EAAE,eAAe,AAAf,EAAiB,EAC1B,UAAU,CAAE,IAAI,CAAC,MAAM,EAAE,AAC/B,CAAK,CAGH,IAAM,EAAa,AAsCX,SACA,AADA,CACA,CACA,CAAA,EAGA,IAAA,EAAA,GAAA,EAAA,SAAA,AA3C6C,MA2C7C,EAAA,CALA,EAKA,WAAA,CAAA,CACA,QAAA,IAAA,EACA,GADA,EAAA,EACA,EAIA,IAAA,CALA,CAKA,CAAA,EAAA,EAAA,GAJA,MAIA,MAAA,EAAA,GAAA,WAAA,CAAA,QACA,GAAA,GAAA,UAAA,EAAA,CAAA,QAAA,EACA,EAAA,KADA,EAAA,MACA,CAEA,IAAA,CAAA,MAAA,EAAA,CAAA,EAEA,EAAA,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAGA,IAAA,CAAA,MAAA,EAAA,AAEA,EA5D8C,EAAiB,GAGnE,IACF,EAAuB,IAJ4C,OAIhC,CAAE,CAJsD,CAAC,AAI5C,EADxB,EAAE,EACJ,EAAyB,AAAS,EAAA,CAAE,CAG5D,GAAM,SAAE,CAAO,cAAE,CAAY,eAAE,CAAA,CAAgB,CAAE,EAEjD,MAAO,CACL,MAH8D,CAGvD,gBACP,EACA,OAAO,CAAE,EADG,AAEZ,GAAG,CAAE,GAA0B,CAAA,CAAE,EADX,UAEtB,CACJ,CAAG,AACH,CAKO,EARyB,KAClB,EAOE,EACd,EAAO,CAAA,EAAA,EAAA,AAAuB,eAAA,AAAe,CADN,EACQ,CAC/C,EAAM,CAAA,EAAA,CAAA,CAAuB,cAAA,AAAc,GAAE,CAC7C,CAAO,EAEP,IAAI,EAAgB,EAAE,CAItB,OAHI,AAAY,CADE,MACN,EAAa,EAAE,EACzB,EAAgB,EAAU,IAAA,CAAO,AAAjB,IAAiB,CAAI,CAEhC,CAAC,EAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA,AACA,UADA,goBCvEH,IAAM,EAAkB,EAClB,EAAqB,EAE9B,GAA0B,EAOvB,EAVsB,CAGM,MAFH,AAShB,EAA8B,CAAI,EAAsB,AACtE,GAAM,CAAE,AARN,MAQY,CAAE,CAAO,CAAE,OAAO,CAAE,CAAA,CAAA,CAAa,AADJ,EACS,EAAD,SAAY,EAAE,CAC3D,MAAE,CAAI,IAAE,CAAE,gBAAE,CAAc,QAAE,CAAM,QAAE,CAAM,OAAE,CAAM,CAAA,CAAI,EAAW,GAEvE,CAF2E,CAAC,GAAN,CAE/D,gBACL,EACA,OAAO,KADO,OAEd,EACA,IAAI,EADI,IAER,EAAE,OACF,MAAM,GACN,MAAM,EACN,CACJ,CAAG,AACH,CAKO,EAPE,OAOO,EAAmB,CAAI,EAAsB,AAC3D,GAAM,CAAE,QAAM,CADkB,AAChB,OAAO,CAAE,CAAQ,UAAE,CAAA,CAAA,CAAa,EAAK,EAAD,SAAY,EAAE,CAI5D,EAAiB,EAAW,EAAS,EAAW,EAA/B,AAAW,CAA0B,CAAF,CAAC,CAArD,EAA+C,SAAqB,CACpE,EAAA,CAAA,EAAA,EAAQ,uBAAA,AAAuB,EAAC,GAAM,CAAF,CAAC,GAAM,CAIjD,MAAO,gBACL,EACA,OAAO,CAJO,EAAW,EAGX,CAHkB,EAAF,CAAP,kBAA8B,EAAE,CAAC,iBAAkB,EAAA,CAAA,EAAA,EAAG,cAAA,AAAc,EAAC,EAAI,MAAM,KAKtG,CACJ,CAAG,AACH,CAKO,KAPK,IAOI,EAAkB,CAAI,EAAgB,AACpD,GAAM,SADyB,AACvB,CAAO,QAAE,CAAA,CAAS,CAAE,EAAK,EAAD,SAAY,EAAE,CACxC,EAAU,EAAc,GAC9B,AADc,CAAoB,CAAC,IACnC,CAAA,CAD6B,CAC7B,EAAO,yBAAA,AAAyB,EAAC,EAAS,EAAQ,EACpD,CAD0C,AAQnC,CAR2C,GAAS,CAAC,IAQ5C,EAA4B,CAAK,EAA2C,OAC1F,AAAI,GAAS,EAAM,AAAT,GAAQ,GAAC,CAAS,CAAC,CACpB,CADsB,AADU,CAE1B,GAAD,AAAI,CAAC,CAAC,CAAE,OAAO,CAAE,QAAE,CAAM,CAAE,SAAO,YAAE,CAAU,CAAE,GAAG,EAAa,SAAD,GAAG,CAAW,CAAC,GAAA,CAAM,CAC9F,OAAO,CAAE,EACT,IADe,IACP,CAAE,EACV,KADiB,EACV,CAAE,IAAe,MAAJ,OACpB,EACA,GAF0C,AAEvC,CAAW,CACpB,CAAK,CAAC,CAFU,AAET,KAEH,CAEJ,CAKO,KAPI,IAOK,EAAuB,CAAK,EAPxB,AAO6D,MAC1D,AAArB,QAA6B,EAAzB,AAA2B,CADK,MACzB,EACF,EAAyB,CADjB,EAIb,EAHmC,CAAC,EAG/B,CAAC,OAAO,CAAC,GAET,CAAK,CALmB,AAGV,AAER,CAAC,AAFQ,CAER,CAAI,AAFM,CAED,CAAC,CAAC,CAAE,CAAE,GAAG,CAG9B,KAAM,QAAW,IAAI,CAChB,CADkB,CACO,EAAM,GAAD,IAAQ,EAAE,CAAC,CAGlD,CAAA,EAAA,EAAO,IAH0B,cAG1B,AAAkB,EAAE,CAC7B,CAKA,SAAS,EAAyB,CAAS,EAAkB,AAE3D,OADa,AACN,EADkB,EACb,KADC,GAAsB,AADJ,EAEjB,EAAY,IAAA,AAAO,CACnC,CAQO,OATqC,EAS5B,EAAW,CAAI,EAAkB,KAAvB,CACxB,GAoEmD,CApE/C,SAoEyD,EAAtD,KApEa,EAAC,AAoEN,EAAoB,EApEV,AAoEV,CApEW,EAAE,MAoEO,CAnEjC,OAAO,EAAK,EAAD,SAAY,EAAE,CAG3B,GAAM,CAAE,MAAM,CAAE,CAAO,CAAE,OAAO,CAAE,CAAA,CAAA,CAAa,EAAK,EAAD,SAAY,EAAE,CAGjE,GA0CO,AAAE,CA1CL,AAwCuC,AAEnC,EA1CgC,EAwCO,CAE7B,CA1C0B,AAwCkD,CAxCjD,AA0C5B,QAAC,EAAgB,EAAF,AAAW,CAAV,KAAS,GAAC,EAAe,EAAF,AAAW,CAAV,EA1ClB,CA0C4B,EADlD,AACiD,AAAW,EAAF,AAAW,CAAV,CADtD,IAC+D,CAAC,EAAa,EAAF,AAAW,CAAV,KAAS,AAAO,CA1CnE,CAC7C,GAAM,YAAE,CAAU,WAAE,CAAS,MAAE,CAAI,SAAE,CAAO,QAAE,CAAM,CAAE,OAAM,CAAA,CAAI,EAahE,EAboE,IAa7D,SACL,OAAO,IACP,EACA,IAAI,CAAE,CADE,CAER,QADgB,GACL,CAAE,EACb,EADiB,YACH,CAXd,CAWgB,YAAY,IAXV,EACd,EAAK,EAAD,UAAC,CACL,sBAAuB,EACpB,EAAK,EAAD,eAAC,EAAuD,YAC7D,EAQN,OARe,QAQA,CAAE,EAAuB,GAExC,MAFiD,CAAC,EAEzC,CAAE,EAAuB,IAAY,CAFP,EAEE,CAAA,GACzC,EADuD,IACjD,CAAE,EAAiB,AADQ,GAEjC,EAAE,CAD6B,AAC3B,CAD4B,AAClB,CAAA,EAAC,IADS,wBACmB,CAAC,CAC5C,MAAM,CAAE,CAAU,CAAA,EAAC,gCAAgC,CAAE,CACrD,GADmB,EACd,CAAE,EAA4B,EACzC,CAAK,AACL,CAIE,CAN4C,CAAC,IAMtC,SACL,MAPoC,CAO7B,IACP,EACA,MADQ,SACO,CAAE,CAAC,CAClB,IAAI,CAAE,CAAA,CACV,AADY,CAEZ,AADG,CAiCI,SAAS,EAAc,CAAI,EAAiB,AAGjD,GAAM,KAHqB,OAGnB,CAAW,CAAA,CAAI,EAAK,EAAD,SAAY,EAAE,CACzC,OAAO,IAAe,CACxB,CAGO,IAJa,KAIJ,EAAiB,CAAM,EAA8C,AACnF,EALwC,CAKpC,AAAC,GAAU,EAAO,CAAV,EADkB,CACT,AAAC,GAAA,EAAS,iBAAiB,EAAE,MAIlD,AAAI,EAAO,IAAD,AAAM,GAAA,EAAI,cAAc,CACzB,CAD2B,GACvB,CAGN,EAAO,IAAD,GAAC,EAAW,MAJL,SAIoB,AAC1C,CAEA,IAAM,EAAoB,eAApB,IAAuC,CACvC,EAAkB,aAAlB,IAAmC,CAUlC,SAAS,EAAmB,CAAI,CAA6B,CAAS,EAAc,AAGzF,IAAM,EAAW,CAAI,CAAC,EAAe,CAHL,CAGU,IAAI,GAC9C,IADqC,oBACrC,AAAwB,EAAC,EAAwC,EAAiB,GAI9E,CAAI,CAJiB,AAIhB,EAAkB,CAJ+D,AAKxF,CALyF,AAI9D,AACvB,CAAC,EAAkB,CALuD,AAKtD,GAAG,CAAC,KADJ,GAGxB,CAFqC,CAAf,AAAgB,sBAEd,AAAxB,EAAyB,EAAM,EAAF,AAAqB,IAAI,GAAG,CAAC,CAAC,EAAU,CAAC,CAAC,AAE3E,CAGO,CAL6C,GAAoB,KAKxD,EAAwB,CAAI,CAA6B,CAAS,EAAc,AAC1F,CAAI,CAAC,EAAkB,EACzB,AAD2B,CACvB,CAAC,EAAkB,CAAC,KAFW,CAEL,CAAC,CADP,CAG5B,CAKO,IAPmB,EAAkB,CAAC,EAO7B,EAAmB,CAAI,EAAqC,AAC1E,IAAM,EAAY,IAAI,GAAG,AAAT,AADgB,CAmBhC,CAlBiC,MAgBjC,AAdA,SAAS,EAAgB,CAAI,EAAmC,AAE9D,CAYa,GAZT,EAAU,GAAG,CAFK,AAEJ,GAAL,CAAS,AAGX,CAHY,CAGE,CAHA,EAMvB,CAH2B,CAAC,EAGvB,AAHyB,IAAR,AAGX,KAFX,EAAU,EAEW,CAFR,CAAC,EAEU,CADL,AADV,CACc,AADL,CACM,AADL,EACsB,CAAI,IACX,CADgB,AACd,CADe,IAAI,CAAC,CAAI,CAAC,CAApB,CAAsC,CAAE,CAAE,EAAE,EAEnF,EAAgB,EAGxB,EAEkB,GAET,AAT2E,CAO9D,CALW,AAKV,CALW,EAOpB,CAPS,AAOR,IAAI,CAAC,EACpB,CAKO,MANsB,CAAC,EAMd,EAAY,CAAI,EAAmC,AACjE,MADyB,CAClB,CAAI,CAAC,EAAe,EAAK,CAClC,CAKO,EAN+B,OAAT,AAMb,IACd,IAAM,EAAA,CAAA,EADqB,AACrB,EAAA,AAAU,CADgC,aAChC,AAAc,EAAE,EAC1B,EAAM,CAAF,AAAE,EAAA,EAAA,uBAAA,AAAuB,EAAC,OAAO,CAAC,EAC5C,AAAI,EAAI,CAAD,YAAc,CACZ,CADc,CACV,CAAD,YAAc,EAAE,CAGrB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAA,CAAA,EAAA,EAAC,eAAA,AAAe,EAAE,CAAC,CAC5C,CAKO,SAAS,IACT,IACH,CAAA,EAAA,EAAA,MAF+B,GAAS,KAExC,AAAc,AADY,EACX,AADa,KAG1B,CAFmB,MAEZ,CAAC,IAAI,CACV,qIAAqI,CAE7I,CAAK,CAAC,CACF,GAA0B,EAE9B,CAkBO,CApB2B,QAoBlB,EAAe,CAAI,CAAQ,CAAI,EAC7C,AAD6D,CApB3D,CAqBG,EAAD,GADwB,KACb,CAAC,GAChB,CADoB,CAAC,AAChB,EAAD,WAAc,CAAC,CACjB,CAAA,EAAC,gCAAgC,CAAA,CAAG,QAAQ,CAC5C,CAAA,EAAC,0CAA0C,CAAA,CAAG,CAClD,CAAG,CAAC,AACJ,CAFsD,uWCjUtD,IAAM,EAAmB,YAAY,CAS9B,CATD,QASU,EAAgB,CAAI,CAAQ,CAAG,EAAyC,KAEtF,GAF6B,qBAE7B,AAAwB,EADC,AACA,EAAkB,EADb,AAC+B,EAC/D,CADkE,AAQ3D,CAR4D,QAAxB,AAQ3B,EAAoC,AARS,CAQD,CAAU,CAAM,EAAkC,AAC5G,IAII,EAJE,EAAU,EAAO,AAIb,GAJI,CAAQ,MAAW,EAAE,CAE7B,CAAE,IAHyC,KAGhC,CAAE,CAAU,MAAE,CAAK,CAAA,CAAI,EAAO,IAAD,EAAO,EAAG,EAAG,CAAA,CAAE,AAGzD,GAAQ,IAAD,CAAM,CACf,CADiB,CACR,MAAM,CAAC,EAAQ,KAAD,AAAM,CAAC,CACrB,IACT,AADa,EACN,AADQ,CACR,EAAA,CAAA,CAAE,uBAAA,AAAuB,EAAC,EAAI,CAAC,CAKxC,IAAM,EAA8B,CAA3B,AACP,WAAW,CAAE,EAAQ,KAAD,MAAC,EAAe,EAAA,mBAAmB,CACvD,OAAO,CAAE,EAAQ,KAAD,EAAQ,YACxB,UAAU,CACV,QAAQ,CACR,CACJ,CAAG,CAID,GALQ,IAGR,EAAO,IAAD,AAAK,CAAC,WAAW,CAAE,GAAG,AAErB,CAFsB,AAG/B,CAKO,CANK,QAMI,EAAmC,CAAM,CAAU,CAAK,EAA0C,AAChH,IAAM,EAAqB,EAAM,GAAD,WAAP,KADuB,EACM,EAAE,CACxD,OAAO,EAAmB,GAAA,EAAO,EAAoC,EAAmB,OAA/D,AAAsE,CAAE,EACnG,CASO,GAVkG,CAAC,CAAjB,IAUzE,EAAkC,CAAI,EACpD,AADuG,IACjG,EAAS,AAXqD,CAWrD,EAAA,CAAT,CAAS,SAAA,AAAS,EAAE,EAC1B,GAAI,CAAC,AAF0C,EAG7C,IADS,EAAE,AACJ,CAAA,CAAE,CAGX,IAAM,EAAS,CAAA,EAAA,EAAE,CAAF,UAAE,AAAW,EAAC,GACvB,CAD2B,CAAC,AACf,CAAE,EAAA,EAAA,KAAF,KAAE,AAAU,EAAC,GAC1B,EAAqB,EAAa,CADA,CAAC,EACG,CACtC,EAAa,EAAS,CADW,IAAjC,CACqB,KAAY,EAAE,CAAC,UAAU,CAI9C,EACJ,GAAY,GAAG,CAAC,GAAN,MADa,WACa,CAAE,EACtC,CAAkB,CAAA,EAAC,qCAAqC,CAAE,EAC1D,CAAkB,CAAA,EAAC,oDAAoD,CAAC,CAE1E,SAAS,EAA0B,CAAG,EAAoE,AAInF,OAHa,QAAS,EAAvC,GAD4B,IACrB,GAAiE,UAA9B,KAAhB,EAAuB,CAAuB,CAAQ,EAAE,CACpF,EAAI,CAAD,UADgD,AACnC,CAAE,CAAC,EAAA,GAAA,EAEA,CACA,CAGA,CAJA,GAIA,EAAA,CAAA,CAAA,EAAA,CACA,AAPA,EAMA,CACA,EACA,OADA,AACA,CAFA,CACA,AACA,GAIA,IAAA,EAJA,AAIA,CAJA,EAIA,GAAA,CAAA,GAAA,CAAA,GAJA,KAIA,CAAA,CAGA,EAAA,GAAA,CAAA,EAAA,EAAA,KAAA,gCAAA,EAAA,GAEA,GAAA,EACA,KAHA,CAAA,CAGA,EAAA,GAIA,CALA,EAAA,CAKA,EAAA,CAAA,CAAA,EAAA,EAAA,AAJA,CAAA,OAAA,CAIA,EAAA,CAAA,OAAA,CAAA,GAGA,EAAA,CAHA,AAGA,CAHA,AAGA,EAAA,AAHA,gCAGA,CAAA,CAGA,EAAA,CAHA,CAGA,UAAA,CAAA,CAsBA,MArBA,KAAA,GAAA,GAAA,GAAA,AACA,CADA,EAAA,AACA,WAAA,CAAA,CAAA,CAAA,CAMA,CAAA,EAAA,EAAA,eAAA,EAAA,GAAA,CACA,EAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,aAAA,EAAA,IACA,EAAA,CAAA,CADA,CAAA,CAAA,OACA,CAGA,EAFA,CAEA,GAAA,CAAA,GAAA,iBAAA,CAAA,IACA,GACA,uBAAA,EAAA,GAAA,KAAA,CAAA,CAAA,qBAAA,EAAA,CAAA,UAAA,CAAA,KAJA,GAIA,EAAA,EAGA,EAAA,GAAA,AAEA,CAFA,CAEA,IAAA,CAAA,IANA,OAMA,CAAA,CAFA,CAEA,CAAA,EAEA,CACA,CAKA,CANA,EAFA,CAAA,KAQA,EAAA,CAAA,EAAA,AACA,IAAA,EAAA,CAAA,CAAA,GACA,CADA,CAAA,CADA,GAEA,CAAA,EAAA,EAAA,iBADA,0BACA,EAAA,EACA,CADA,CAAA,wLCtJlB,SAAS,EAAsB,CAAK,CAAS,CAAI,EAAmB,IAqK1C,EAAc,GAAT,GApKpC,EADmC,CAC7B,EAoKkD,EAA8C,SApK9F,CAAW,MAAE,CAAI,aAAE,CAAW,uBAAE,CAAA,CAAwB,CAAE,EAGlE,AAkGF,EArGwE,OAqG/D,CAAiB,CAAK,CAAS,CAAI,EAAmB,AAC7D,CAnGgB,EAmGV,OAAE,AADe,CACV,MAAE,CAAI,MAAE,CAAI,UAAE,CAAQ,OAAE,CAAK,iBAAE,CAAgB,CAAA,CAAI,EAE5D,EAFgE,IAE1D,CAAC,IAAI,CAAC,GAAO,EAAF,CAAC,GAAO,EAAE,AAC7B,GAAM,EAAD,GAAO,CAAE,CAAE,GAAG,CAAK,CAAE,GAAG,EAAM,GAAD,EAAC,CAAA,CAAO,CAGxC,MAAM,CAAC,IAAI,CAAC,GAAM,CAAF,CAAC,IAAO,EAAE,CAC5B,EAAM,GAAD,CAAM,CAAE,CAAE,GAAG,CAAI,CAAE,GAAG,EAAM,GAAD,CAAC,CAAA,CAAM,CAGrC,MAAM,CAAC,IAAI,CAAC,GAAM,CAAF,CAAC,IAAO,EAAE,CAC5B,EAAM,GAAD,CAAM,CAAE,CAAE,GAAG,CAAI,CAAE,GAAG,EAAM,GAAD,CAAC,CAAA,CAAM,CAGrC,MAAM,CAAC,IAAI,CAAC,GAAU,KAAF,CAAC,AAAO,EAAE,CAChC,EAAM,GAAD,KAAU,CAAE,CAAE,GAAG,CAAQ,CAAE,GAAG,EAAM,GAAD,KAAC,CAAA,CAAU,CAGjD,IACF,CADO,CACD,CADG,EACJ,EAAO,CAAE,CAAA,CAAK,CAIjB,GAAkC,YAAlB,CAA+B,EAAE,CAA9B,EAAM,GAAD,CAAC,GAC3B,EAAM,GAAD,QAAa,CAAE,CAAA,CAAe,CAEvC,EA7HmB,EAAO,GAAF,AAKlB,CALwB,CAAC,CAM3B,AAqIJ,CAtIU,EAAE,MAsIH,AAAiB,CAAK,CAAS,CAAI,EAAc,AACxD,EAtIkB,AAsIZ,GAAD,KAAC,CADiB,AACN,CACf,KAAK,CAAA,CAAA,EAAA,EAAE,kBAAA,AAAkB,EAAC,GAC1B,CAD8B,CAAC,CAC5B,EAAM,GAAD,KAAS,AACrB,CAAG,CAED,EAAM,GAAD,kBAAC,CAAwB,CAC5B,sBAAsB,CAAA,CAAE,EAAA,EAAA,iCAAA,AAAiC,EAAC,GAC1D,CAD8D,CAAC,CAC5D,EAAM,GAAD,kBACZ,AADkC,CAC/B,CAED,IAAM,EAAS,CAAA,EAAA,EAAE,CAAF,UAAE,AAAW,EAAC,GACvB,CAD2B,CAAC,AAC5B,CAAA,EAAA,EAAkB,UAAA,AAAU,EAAC,GAAU,KAAF,CAAC,KAAY,CACpD,GAAmB,CAAC,EAAM,GAAD,MAAT,EAAsB,EAAkB,aAAa,EAAE,CAA9B,EAAM,GAAD,CAAM,GACtD,EAAM,GAAD,QAAa,CAAE,CAAA,CAAe,AAEvC,EArJqB,EAAO,GAAF,CAAM,CAAC,AAGP,IAAO,CAAF,CA0J7B,EAAM,GAAD,IA1JqC,CAAC,GA0JzB,CAAE,EAAM,GAAD,QAAC,CACtB,KAAK,CAAC,OAAO,CAAC,EAAM,GAAD,QAAY,EAC7B,EAAM,GAAD,QAAC,CACN,CAAC,EAAM,GAAD,QAAY,CAAA,CACpB,EAAE,CAGF,IACF,EAAM,GAAD,EADQ,EAAE,IACT,CAAc,EAAM,GAAD,QAAY,CAAC,MAAM,CAAC,EAAW,CAAC,CAIvD,AAAC,EAAM,GAAD,QAAY,CAAC,MAAM,EAAE,AAC7B,OAAO,EAAM,GAAD,QAAY,CAtK1B,AAqHF,SAAS,AAAwB,CAAK,CAAS,CAAW,EAAsB,AAC9E,IAAM,EAAoB,CAAC,EAtHJ,CAsHQ,EAAM,GAAD,GADN,GACN,EAAyB,EAAG,EAAE,CAAC,EAAE,CAAG,EAAY,CACxE,EAAM,GAAD,GADkE,KACjE,CAAc,EAAkB,MAAO,CAAE,OAAoB,CACrE,AADuC,EAvHb,EAAO,GA0HA,AA1HF,CAuH+C,CAAX,AAtHzC,EAyHqB,CAAT,CAzHL,CAAF,CA0H7B,AA3H0C,CAAC,CA2HrC,GAAD,YAD6D,EAA4C,AAzH1D,CAAC,GA0H/C,CAAwB,CAC5B,GAAG,EAAM,GAAD,kBAAsB,CAC9B,GAAG,CAAqB,AAC5B,CAAG,AA5HH,CAGO,SAAS,EAAe,CAAI,CAAa,CAAS,EAAmB,AAC1E,GAAM,IADsB,GAE1B,CAAK,MACL,CAAI,MACJ,CAAI,UACJ,CAAQ,OACR,CAAK,uBACL,CAAqB,aACrB,CAAW,aACX,CAAW,iBACX,CAAe,aACf,CAAW,oBACX,CAAkB,iBAClB,CAAe,CACf,MAAI,CACN,CAAI,EAEJ,EAA2B,EAAM,EAAF,CAFlB,IAE2B,CAAE,GAC1C,EAD+C,AACpB,CADqB,CACf,EAAF,IAAQ,CAAE,AADf,GAE1B,CAD6C,CAAC,AACnB,EAAM,EAAF,IAAQ,CAAE,CADf,EAE1B,CAD6C,CAAC,AACnB,EAAM,EAAF,MADL,EACiB,CAAE,GAE7C,EAAK,EAAD,CAFiD,CAAC,EAA5B,eAEC,CAAA,CAAA,EAAA,EAAE,KAAA,AAAK,EAAC,EAAK,EAAD,mBAAsB,CAAE,EAAuB,CAAC,CAAC,CAEpF,IACF,CADO,CACF,CADI,CACL,GAAO,CAAE,CAAA,CAAK,CAGhB,CANgF,GAOlF,EAAK,EAAD,OADa,EAAE,IACE,CAAE,CAAA,CAAe,CAGpC,IAAI,AACN,EADQ,AACH,EAAD,EAAM,CAAE,CAAA,CAAI,CAGd,EAAY,MAAM,EAAE,CAAT,AACb,EAAK,EAAD,SAAa,CAAE,CAAC,GAAG,EAAK,EAAD,SAAY,EAAE,EAAG,EAAY,EAGtD,EAAY,KAHyC,CAGnC,EAAE,CAAT,AACb,EAAK,EAAD,SAAa,CAAE,CAAC,GAAG,EAAK,EAAD,SAAY,EAAE,EAAG,EAAY,EAGtD,EAAgB,KAHqC,CAG/B,EAAE,CAC1B,EAAK,EADY,AACb,aAAiB,CAAE,CAAC,GAAG,EAAK,EAAD,aAAgB,EAAE,EAAG,EAAgB,EAGlE,EAAY,MAAM,EAAE,CAH6C,AAGtD,AACb,EAAK,EAAD,SAAa,CAAE,CAAC,GAAG,EAAK,EAAD,SAAY,EAAE,EAAG,EAAY,EAG1D,EAAK,EAAD,GAHqD,aAGjC,CAAE,CAAE,GAAG,EAAK,EAAD,gBAAmB,CAAE,GAAG,CAAA,CAC7D,AADiF,CAO1E,SAAS,EAGd,CAAI,CAAQ,CAAI,CAAQ,CAAQ,EAAoB,AACpD,CAAI,CAAC,EAAI,CAAA,CAAA,EAAI,EAAA,KAAA,AAAK,EAAC,CAAI,CAAC,EAAK,CAAE,CAAH,CAAa,CAAC,CAAC,AAC7C,IADyC,mDCtFrC,EACA,EACA,WADa,GADK,QAEI,oEAKnB,SAAS,EAAwB,CAAW,EAAuC,AACxF,IAAM,EAAA,EAAa,MAAb,IAAuB,AADQ,CACP,eAAe,CAC7C,GAAI,CAAC,EACH,MAAO,CAAA,CADM,AACJ,CAGX,CAJiB,GAIX,EAAc,MAAM,CAAC,IAAI,CAAC,UAAU,AAI1C,AAAI,CAJuC,EAIb,EAAY,MAAA,GAAD,AAAY,EAC5C,GAGT,EAAgB,CAJW,CAIC,IAJsC,EAIhC,AAJkC,CAOpE,EAH2B,AAGF,AAHX,EAGuB,IANN,EAMY,CAAyB,CAAC,CAAjC,CAAsC,CAAF,IAClE,AAAC,GADP,AAAkF,CAE9E,EAAqB,EAF8D,AAE9D,CAAE,CAGzB,IAAM,EAAS,CAAkB,CAAC,AAJX,EAIV,AAA8B,AAJlB,CAMzB,CALE,EAKE,EAFsC,AAGxC,CAAG,CAAC,CAAM,CADF,AACG,CAAC,CADF,AACG,CAAA,CAAI,CAAM,CAAC,CAAC,CAAC,KACrB,CACL,IAAM,EAAc,EAAY,GAEhC,IAAK,AAFa,CAAsB,CAAC,AAAV,EAEtB,CAAE,CAAE,EAAY,MAAA,CAAS,CAAC,CAAX,AAAa,CAAE,EAAG,CAAC,CAAE,CAAC,EAAE,CAAE,CAChD,IAAM,EAAa,CAAW,CAAC,CAAC,CAAC,CAC3B,EAAW,CADA,EACY,GAAvB,IAAqB,CAAU,CAC/B,EAAU,CAAU,CAAC,EAAS,CAEpC,AAFc,GAEV,EAF+B,CAEnB,EAAS,CACvB,CAAG,CADQ,AACP,EADiB,AACT,CAAI,EAChB,CAAkB,CAAC,CADP,CACe,CADJ,AACQ,CAAC,EAAU,EAAQ,AAAvB,CAC3B,GADwC,CAAS,CAE3D,CACA,CACA,CAEI,OAAO,CACX,CAAG,CADW,AACT,CAAA,CAAE,CAAC,CAGR,CAKO,SAAS,EACd,CAAW,CACX,CAAc,EAEd,IAAM,EAAqB,EAAwB,GAEnD,GAAI,CAAC,EACH,EAH4D,AAJtB,CAIuB,EAAtC,CAGhB,EAAE,CAGX,GANkD,CAM5C,EAAuB,CAJN,CAIQ,CAJN,AAKzB,CADY,GACP,IAAM,IAAK,CAAG,EACb,GAAQ,CAAH,AAAqB,CAAC,EAAK,EAClC,AADoC,AAAH,EAC1B,CAFsB,CAAE,EAEzB,AAAK,CAAC,CACV,IAAI,CAAE,WAAW,CACjB,SAAS,CAAE,EACX,EADe,MACP,CAAE,CAAkB,CAAC,EAAM,AAC3C,CAAO,CADkC,AACjC,CAIN,OAAO,CACT,KADe,8NCjFR,IAAM,EAAmB,IAE1B,EAAuB,QAFC,SAEgB,CACxC,AADA,EACqB,gBAArB,iBAAsD,CASrD,SAAS,EAAkB,GAAG,CAAO,EAAkC,AAC5E,IAAM,EAAgB,EAAQ,CADC,GACG,CAAL,AAAM,CAAC,CAAC,CAAE,CAAjC,AAAkC,GAAK,CAAC,CAAC,CAAC,CAAE,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,AAAC,CAAA,EAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAExE,MAAO,CAAC,EAAe,EAAyB,CAAnC,AAAoC,CAAE,EAAsB,CAAC,IACxE,CAD2F,EAAxD,CAAyB,AACtD,EAAuB,EAAE,CACzB,CADM,CACE,EAAM,GAAD,EAAM,CAAC,IAAI,CAAC,CAE/B,IAAK,IAAI,CAAA,CAAI,EAAgB,CAAE,CAAE,EAAM,GAAD,GAAO,CAAE,CAApB,AAAqB,EAAE,CAAE,CAClD,IAAM,EAAO,CAAK,CAAP,AAAQ,CAAC,CAAE,CAKtB,GAAI,EAAK,EAAD,IAAQ,CAAE,IAAI,CACpB,CADsB,QAMxB,IAAM,EAAc,EAAqB,IAAI,CAAC,EAA5B,CAAoC,CAAJ,CAAS,AAAT,EAAQ,KAAQ,CAAC,AAA3B,EAAiD,IAAI,CAAA,CAAI,EAIjG,EAJqG,EAIjG,EAAY,IAJuE,CAIlE,CAAC,GAAP,SAAmB,CAAC,EAInC,AAJqC,IAIhC,IAAM,KAAU,CAAH,CAAkB,CAClC,IAAM,EAAQ,EAAO,CAAT,CADoB,CAGhC,CAFoB,EAEhB,EAAO,CACT,EAH8B,AAEvB,AACA,CAHwB,GAGzB,AAAK,CAAC,GACZ,EADiB,CAAC,EAE5B,CACA,CAEM,GAAI,EAAO,IAAD,EAAC,EAAU,GAAyB,EAC5C,MAER,CAEI,EAL2D,EAAE,GAKtD,EAA4B,CALZ,CAKmB,IAAD,CAAM,CAAC,GACpD,CAAG,AACH,CAQO,MAVwD,CAAC,CAAC,CAUjD,EAAkC,CAAW,AAVvB,EAUuE,OAC3G,AAAI,KAAK,CAAC,OAAO,CAAC,GACT,IAFsC,CAEjB,GADD,AAGtB,CAHuB,AAIhC,CAQO,CAZ2B,KACS,CAAf,AAAgB,CAAf,CAWb,AATI,EASwB,CAAK,EAA2C,AAC1F,GAAI,CAAC,EAAM,GAAD,GAAO,CACf,CADiB,KACV,EAAE,CAF8B,AAKzC,IAAM,EAAa,KAAK,CAAC,IAAI,CAAC,GA2B9B,EA3BmC,CAAC,GAGhC,eAAe,CAAC,IAAI,CAAC,EAAkB,GAAY,OAAF,CAAC,AAAC,EAAY,EAAzB,AAA2B,CAAC,EAAE,AACtE,EAAW,GAAG,EAAE,CAIlB,EAJY,AAID,OAAO,CAAR,CAAU,CAGhB,EAAmB,IAAI,CAAC,EAAkB,GAAY,MAApC,CAAkC,CAAC,AAAC,EAAY,EAAE,AAA3B,CAA4B,EAAE,CACzE,EAAW,GAAG,EAAE,CAUZ,EAVM,AAUa,IAAI,CAAC,EAAkB,GAAY,MAApC,CAAkC,CAAC,AAAC,EAAY,EAAzB,AAA2B,CAAC,EAAE,AACzE,EAAW,GAAG,EAAE,EAIb,CAJO,CAII,KAAK,CAAC,CAAC,CAAR,AA7GY,CA6GF,CA7GI,EA6GoB,GAAG,CAAA,AAAC,IAAU,CAC/D,AADqD,GAClD,AADkD,CAC7C,CACR,KAF+C,CAAC,EAExC,CAAE,EAAM,GAAD,KAAC,EAAY,EAAkB,GAAY,OAAF,CAAU,AAAT,CACzD,GAD6C,KACrC,CAAE,EAAM,GAAD,KAAC,EAAY,EAChC,CAAG,CAAC,AACJ,CADK,AAGL,SAAS,EAJuC,AAIrB,CAAG,EAA4B,AACxD,OAAO,CAAG,CAAC,EAAI,CAAD,AADU,KACF,CAAE,CAAC,CAAA,EAAK,CAAA,CAAE,AAClC,CAEA,IAAM,EAAsB,aAAa,CAKlC,GALD,MAKU,EAAgB,CAAE,EAAmB,AACnD,GAAI,CACF,GAAI,CAAC,EAAA,AAFsB,CAEF,UAAU,EAAxB,AAA0B,OAAnB,EAAA,AAChB,OAAO,EAET,OAAO,EAAE,AAAC,IAAA,EAAQ,CACtB,CAAI,AAH4B,AAG5B,MAAO,CAAC,CAAE,CAGV,OAAO,CACX,AALyC,CAMzC,CAKO,SAAS,EAAmB,CAAK,EAAmC,AACzE,EAR4B,EAQtB,EAAY,EAAM,GAAD,EAAjB,AAD0B,IACC,CAEjC,GAAI,EAAW,CACb,IAAM,EADK,AACkB,EAAE,CAC/B,CADY,EACR,CASF,OAPA,EAAU,MAAM,CAAC,AAAR,OAAe,CAAA,AAAC,IAEnB,EAAM,GAFsB,AAEvB,OAAW,CAAC,MAAM,EAAE,AAE3B,EAAO,IAAD,AAAK,CAAC,GAAG,EAAM,GAAD,OAAW,CAAC,MAAM,CAAC,AAEjD,CAAO,CAAC,CACK,CACb,CAAM,IADa,EACN,EAAK,CAAF,AAEhB,CACA,CAEA,4FCpHO,SAAS,EAEd,CAAM,CAEN,CADF,CACkB,CAAC,CAEjB,CAFK,AACP,CACoB,GAAA,EANW,AAMtB,CALT,AAK0B,AAAI,EAE5B,IAAM,EAAa,EAAU,EAAQ,IAAF,CAAP,AAAc,CAL5C,AAK6C,GAE3C,AAAI,AAoNG,CAAC,CAAC,MApNG,GAoNM,CAAC,AAMD,IAAI,CANE,AAMD,CANE,QAMO,CA1NnB,AA0NoB,IANP,CAMY,CAAC,CAAC,EANT,CAzNjC,AAKyB,AAoNS,CApNP,GAT3B,GA6NyC,CAAC,CAAC,MAAM,CApNpB,EAClB,EAAgB,EAAQ,CADC,CACO,CAAC,AADN,CACQ,AAAb,GAGxB,CACT,GAJqD,AAA3B,CAA4B,KAGlC,kEAxBb,SAAS,EAAU,CAAK,CAAW,EAAgB,GAAjC,AAAsB,AAAc,CAAE,EAAwB,CAAC,OAAQ,EAAO,AACrG,CAD0E,EACtE,CAEF,OAAO,AAiCX,KAjCgB,IAiCP,EACP,CAAG,CACH,CAFY,AAEP,CACL,EAAgB,CAAC,EAAZ,KAAoB,CACzB,EAAwB,CAAC,OAAQ,CACjC,EADa,AACN,AAyOT,EAzOO,OAyOE,EACP,AA1OkB,IA0OZ,EAAQ,GADI,AACN,CAAM,EADa,KACN,CAYzB,CAZmC,KAY5B,CAXP,SAAS,AAAQ,CAAG,EAAmB,IAAvB,EACd,EAAI,EAAM,GAAD,AAAI,CAAC,GAAG,CAAC,CAGlB,CAHoB,CAGd,GAAD,AAAI,CAAC,GAAG,CAAC,AACP,EACX,EAEE,CAHc,QAGL,AAAU,CAAG,EAAgB,AACpC,EAAM,GAAD,CADW,EACJ,CAAC,EACjB,CADoB,CAAC,AAEQ,AAC7B,GAvPsB,EAEpB,GAAM,CAAC,EAAS,EAAS,CAAI,EAG7B,AAHc,EAAmB,CAItB,AAAT,CAJuB,GAIT,CAAd,GAAc,CACd,CAAC,SAAS,CAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,IACpB,CADyB,CAAE,MAC3B,EAAjB,CAFa,MAEN,GAAsB,EAAtB,IAA4B,CAAC,QAAQ,CAAC,GAE9C,EAFmD,CAAC,EACpD,EACO,EAGT,GAHe,CAGT,EA6FR,AA7FsB,SA6Fb,AACP,CAAG,CAGH,CAAK,AAFP,EA/FoC,AAmGlC,GAAI,CACF,GAAY,EAPO,MAOE,GAAjB,GAAA,AAAoB,GAA0B,UAAjB,OAAO,GAAsB,EAAhB,AAAgD,OAAO,CACnG,CADqG,KAC9F,UAAU,CAGnB,GAAY,eAAe,EAAE,CAAzB,EACF,CADM,KACC,aAVb,IAU8B,CAM1B,GAAI,KAAkB,IAAX,GAA0B,GAAnB,CAA6B,CAAV,CACnC,IADmD,EAAE,AAC9C,UAAU,CASnB,GAAwB,aAApB,OAAO,QAAS,EAAmB,IAAU,CAAV,OAAkB,CACvD,CADyD,KAClD,YAAY,CAGrB,GAAI,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GACjB,EADsB,CAAC,EAAE,CAClB,gBAAgB,CAIzB,GAAA,CAAA,EAAA,EAAI,gBAAA,AAAgB,EAAC,GACnB,EADwB,CAAC,EAAE,CACpB,kBAAkB,CAG3B,GAAI,AAAiB,QAAS,SAAnB,GAAsB,CAAC,CAAvB,KAA6B,CAAC,QAAQ,CAAC,GAChD,EADqD,CAAC,EAAE,CACjD,CAAC,CAAC,EAAE,EAAM,CAAC,CAAC,CAAH,AAGlB,GAAqB,UAAU,EAA3B,AAA6B,OAAtB,EACT,GADe,GACR,CAAC,WAAW,EAAA,CAAA,EAAA,EAAE,eAAe,AAAf,EAAgB,GAAO,CAAC,CAAH,AAAI,CAAH,AAG7C,GAAqB,QAAQ,EAAE,AAA3B,OAAO,EACT,GADe,GACR,CAAC,CAAC,EAAE,MAAM,CAAC,GAAO,CAAC,CAAH,AAAI,CAAH,AAI1B,GAAqB,QAAQ,EAAzB,AAA2B,OAApB,EACT,GADe,GACR,CAAC,SAAS,EAAE,MAAM,CAAC,GAAO,CAAC,CAAH,AAAI,CAOrC,AAPkC,IAO5B,EAAU,AAcpB,KAdkB,IAcT,AAAmB,CAAK,EAAmB,AAClD,IAAM,EAf8B,AAeA,MAAM,CAA3B,AAA4B,EADlB,YACgC,CAAC,GAE1D,EAF+D,CAAC,IAEzD,GAAW,MAAF,KAAc,CAAE,EAAU,OAAD,IAAY,CAAC,IAAK,CAAE,gBAC/D,AAD+E,EAjBxC,GAGnC,EAHwC,CAAC,AAGrC,oBAAoB,CAAC,IAAI,CAAC,GAC5B,IADmC,CAAC,CAC7B,CAD+B,AAC9B,cAAc,EAAE,EAAQ,CAAC,CAAC,CAGpC,EAHiC,IAG1B,CAAC,QAAQ,EAAE,EAAQ,CAAC,CAC/B,AADgC,CAC5B,EADyB,IAClB,EAAK,CAAF,AACV,MAAO,CAAC,sBAAsB,EAAE,EAAI,CAAD,AAAE,CAAC,AAC1C,CACA,EAtKqC,EAAK,CAAF,EAItC,EAJ6C,CAAC,AAI1C,CAAC,EAAY,SAAD,CAAW,CAAC,UAAU,CAAC,CACrC,CADuC,MAChC,EAQT,GAAI,EAA+B,GAA9B,CARe,yBAQ+C,CACjE,CADmE,AAAH,MACzD,EAMT,GANe,CAMT,EACiF,UAArF,EADmB,KACX,EAA8B,GAA9B,oCAAuE,CACzE,CADyE,CAC3C,GAA9B,oCAAyE,CAC3E,CADyE,CAI/E,GAHW,AAGY,CAAC,EAAE,CAAtB,EAEF,OAAO,EAAY,GAFF,IAES,CAAC,CAAT,QAAkB,CAAE,EAAE,CAAC,CAI3C,GAAI,EAAQ,GACV,EADS,AAAM,CAAC,EAAE,CACX,cAAc,CAKvB,GAAI,GAAqD,UAAU,EAA/D,AAAmB,AAA8C,OAD7C,AACM,EAAgB,GADhB,GACgB,CAC5C,GAAI,CACF,EAFyC,EAEnC,EAAY,EAAgB,KAAlB,CAAwB,EAAE,CAE1C,IAFiC,GAE1B,EAAM,EAAE,CAAH,AAAK,EAAW,EAAiB,CAAC,CAAE,EAAe,CAArC,CAChC,CAAM,CADmE,CAAC,GAAzB,CACpC,EADsD,AACjD,CAAF,AAEhB,CAME,IAAM,EAAc,KAAK,CAAC,EAAT,KAAgB,CAAC,GAAS,EAAJ,AAAK,CAAL,AAAS,CAAA,CAAE,CAAE,AAChD,EAAW,CAAC,CAIV,EAAU,CAAA,CAJH,CAIG,EAAE,EAAF,kBAAE,AAAoB,EAAC,GAEvC,IAFoE,AAE/D,IAAM,KAAY,EAAW,CAAd,AAElB,GAAI,CAAC,EAFyB,IAEnB,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAW,GACnD,IADiD,CAAU,CAAC,EAAE,CAIhE,GAAI,GAAY,EAAe,CAC7B,CAAU,CADC,AACA,EAAQ,CAAI,KADI,AACR,cAAuB,CAC1C,KACN,CAGI,IAAM,EAAa,CAAS,CAAC,EAAS,CACtC,CAAU,CAAC,CADM,CACE,CAAI,AADc,EACR,EAAU,CAApB,AAAS,CAAuB,EAAiB,CAAC,CAAE,AAAlC,EAAiD,EAArC,CAEjD,CAF0F,CAAC,CAG/F,CAME,IATsF,AAE5E,EAAE,CAIZ,EAAU,GAGH,CACT,CAJiB,CAAC,AAhID,CAgIN,CAhIQ,CAAE,EAAO,EAmIT,AAnIgB,CAAT,CAC1B,CADiC,AAC7B,MAAO,EAAK,CACZ,AADU,CADkC,CAAC,IAEtC,CAAE,KAAK,CAAE,CAAC,sBAAsB,EAAE,EAAI,CAAD,AAAE,CAAA,CAAG,AACrD,CACA,CAkPO,SAAS,EAAmB,CAAG,CAAU,CAAQ,EAAkB,AACxE,IAAM,EAAc,EAEjB,GAH6B,GAElC,CACY,CAAC,KAAK,CAAE,GAAG,CACvB,CACK,OAAO,CAAC,EAHb,mBAGkC,CAAE,IADpC,EAC0C,CAAC,CAErC,EAAS,EACb,CADgB,CAAL,CACP,CACF,EAAS,IAAF,KAAW,CAAC,EACvB,CAD0B,AACtB,CADuB,KAChB,EAAK,CAEhB,AAFc,CAGZ,OACE,EACG,OAAO,CAAC,KAAK,CAAE,GAAG,EAClB,OAAO,CAAC,cAAc,CAAE,EAAE,CAAA,CAAA,AAE1B,OAAO,CAAC,AAAI,MAAM,CAAC,CAAC,YAAY,EAAE,EAFR,AAEoB,EAAE,CAAC,CAAE,IAAI,CAAV,AAAW,CAAE,SAAS,CAE1E,oUC5QO,SAAS,EACd,CAAO,CACP,CAAK,CACL,CAAI,CACJ,CAAK,CACL,CAAM,CACN,AAN0B,CAMZ,MAoKmB,EAAc,EAlK/C,CAkKsC,EAlKhC,CAAE,UAkKuD,EAAkB,EAlKzE,GAAiB,CAAC,qBAAE,EAAsB,GAAA,CAAQ,CAAE,EACtD,EAAkB,CACtB,EAFiE,CAE9D,CAAK,CADI,AAEZ,EAH0B,MAGlB,CAAE,EAAM,GAAD,KAAU,EAAG,EAAK,EAAD,MAAU,EAAG,CAAA,EAAA,EAAA,KAAA,AAAK,EAAE,EACpD,SAAS,CAAE,EAAM,GAAD,MAAC,EAAA,CAAA,EAAA,EAAa,sBAAA,AAAsB,EAAE,CAC1D,CAAG,CACK,EAAe,EAAK,EAAD,MAAN,IAAO,EAAgB,EAAQ,KAAD,OAAa,CAAC,GAAG,CAAC,GAAK,CAAC,CAAC,IAAI,CAAC,CAE/E,EAAmB,EAAU,KACH,CADC,CAAS,AA2JhC,CA3JiC,EACD,GAAF,AA0Jb,CA3JH,KA2JU,CAAE,CAAC,CA1JiB,CAAC,AA0J7B,AAAa,CAC/B,EAAM,GAAI,AAAL,CAAO,EAAM,GAAD,AAAC,EAAO,CAAA,CAAE,CAC3B,EAAM,GAAD,AAAI,CAAC,YAAA,CAAe,CAAC,GAAI,EAAM,GAAD,AAAI,CAAC,YAAA,EAAgB,EAAE,CAAC,EAAE,CAAG,EAAiB,EA1J/E,GACF,EAAO,CADC,EAAE,CACJ,AAAK,CAAC,EAyJoE,kBAzJhD,CAAE,GAIhC,EAJqC,CAAC,EAIjC,AAAU,MAAT,GAAkB,CAAb,CAAe,CAC5B,EAAc,EAAU,EAAQ,IAAV,CAAS,EAAlB,IAA8B,CAAC,CAK9C,IAAM,EA8NR,AA9NqB,QAAF,CA8NV,AAAc,CAAK,CAAqB,CAAc,CA9N7B,CA8N8E,AAC9G,GAAI,CAAC,EACH,EAFkB,KAEX,EAGT,GAJmB,AACL,CAGR,CAJe,CAIF,EAAQ,EAAM,CAAd,EAAa,CAA1B,CAAgC,EAAG,CAAE,IAAA,EAAI,KAAK,CAEpD,CAFsD,MACtD,EAAW,MAAM,CAAC,CAAR,EACH,CACT,EAtOmC,EAAO,EAAK,CAAP,CAAM,CAqO3B,CADe,CAAC,SApO0B,CAAC,CAExD,EAAK,EAAD,OAAU,EAAE,KAClB,qBAAqB,AAArB,EAAsB,EAAU,EAAK,EAAD,EAAN,KAAgB,CAAC,CAGjD,IAAM,EAAwB,EAAS,EAAO,EAAhB,EAAe,WAAvC,GAA0D,EAAC,CAAI,EAAE,CAKjE,EAAA,CAAA,EAAA,EAAO,cAAA,AAAc,EAAE,EAAC,YAAY,EAAE,CAE5C,GAAI,CAFS,CAEO,CAClB,IAAM,EAAgB,EAAe,GADrB,MACI,GAAgB,AAAa,EAAE,EACnD,EAAA,EAAA,cAAc,AAAd,EAAe,EAAM,EAAF,AACvB,CAEE,GAAI,EAAY,CACd,IAAM,AAJ4B,CAAC,CAIZ,CADX,CACsB,QAAD,EAAZ,EAAyB,EAAE,MAChD,cAAA,AAAc,EAAC,EAAM,EAAF,AACvB,CAEE,IAAM,EAAc,CAAC,GAAI,CAHY,CAGP,AAHQ,EAGT,CAAX,QAAY,EAAe,EAAE,CAAC,EAAE,CAAG,EAAK,EAAD,SAAY,CAAC,CAClE,EAAY,MAAM,EAAE,CAAT,AACb,EAAK,EAAD,SAAa,CAAE,CAAA,CAAW,MAGhC,qBAAA,AAAqB,EAAC,EAAU,GAEhC,CAFoC,CAAC,CAAP,CAExB,EAAkB,IACnB,KAEA,EAAK,EAAD,YAFiB,CAED,CACxB,CAID,MAAO,AAFM,CAAA,EAAA,EAAE,CAEF,oBAFE,AAAqB,EAAC,EAAiB,EAAU,GAElD,CAFsD,CAAC,CAAP,CAE5C,CAAA,AAAC,GAFiC,AAWlD,CARI,GAKF,AANsB,AACjB,EAAE,AAKQ,GAAG,AAGU,CAHT,QAAL,CAGZ,OAAO,GAA+B,EAAiB,CAAC,EA8GhE,AA9GkE,AACrD,MADiB,GA8GrB,AAAe,AA9GsB,CA8GjB,CAAgB,CAAK,CAAU,CA7GjC,AA6G2C,EAAwB,AAC5F,GAAI,CAAC,EACH,CAFmB,EACX,EAAE,EACH,IAAI,CAGb,IAAM,EAAoB,CACxB,GAAG,CAAK,CACR,EAFc,CAEV,EAAM,GAAD,QAAC,EAAe,CACvB,WAAW,CAAE,EAAM,GAAD,QAAY,CAAC,GAAG,CAAC,AAAE,IAAA,AAAI,CACvC,GAAG,CAAC,CACJ,GAAI,CAAC,CAAC,IAAA,EAAQ,CACZ,IAAI,CAAA,CAAA,EAAA,EAAE,SAAA,AAAS,EAAC,CAAC,CAAC,IAAI,CAAE,EAAO,EACzC,CADuC,AAC9B,CAAC,AACV,CAAO,CAAC,AACR,CADS,AACJ,CACD,AADE,EAH6C,CAAC,AAI5C,EAAM,GAAD,CAAC,EAAQ,CAChB,IAAI,CAAA,CAAA,EAAA,EAAE,SAAA,AAAS,EAAC,EAAM,GAAD,CAAK,CAAE,EAAO,EACzC,CADuC,AAClC,CAAC,AACF,GAAI,EAAM,CAFqC,CAAC,CAEvC,KAAC,EAAY,CACpB,QAAQ,CAAA,CAAE,EAAA,EAAA,SAAA,AAAS,EAAC,EAAM,GAAD,KAAS,CAAE,EAAO,EACjD,CAD+C,AAC1C,CAAC,AACF,GAAI,EAAM,CAF6C,CAAC,CAE/C,EAAC,EAAS,CACjB,KAAK,CAAA,CAAA,EAAA,EAAE,SAAA,AAAS,EAAC,EAAM,GAAD,EAAM,CAAE,EAAO,EAC3C,CADyC,AACpC,AACL,CADM,AACH,CAsCD,KAxCmD,CAAC,CAWhD,EAAM,GAAD,KAAS,EAAE,KAAA,EAAS,EAAW,QAAD,AAAS,EAAE,CAChD,EAAW,QAAD,AAAS,CAAC,KAAA,CAAQ,EAAM,GAAD,KAAS,CAAC,KAAK,CAG5C,EAAM,GAAD,KAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAC7B,EAAW,QAAQ,AAAT,CAAU,KAAK,CAAC,IAAA,CAAA,CAAA,EAAA,EAAO,SAAA,AAAS,EAAC,EAAM,GAAD,KAAS,CAAC,KAAK,CAAC,IAAI,CAAE,EAAO,EAAU,CAAZ,AAAa,EAKxF,EAAM,GAAD,EAAM,EAAE,AACf,GAAW,KAAA,CAAQ,CAAT,CAAe,GAAD,EAAM,CAAC,GAAG,CAAA,AAAC,IAAA,AAC1B,CACL,GAFuC,AAEpC,CAAI,CACP,GAAI,EAAK,EAAD,EAAC,EAAQ,CACf,IAAI,CAAA,CAAA,EAAA,EAAE,SAAA,AAAS,EAAC,EAAK,EAAD,EAAK,CAAE,EAAO,EAC5C,CAD0C,AACjC,CAAC,AACV,CAAO,CACF,CAAC,CAOA,EAVgD,AAU1C,CAV2C,EAU5C,KAAS,EAAE,KAAA,EAAS,EAAW,QAAD,AAAS,EAAE,AAChD,GAAW,OAAD,CAAS,CAAC,KAAA,CAAA,CAAA,EAAA,EAAQ,SAAS,AAAT,EAAU,EAAM,GAAD,KAAS,CAAC,KAAK,CAAE,CAAC,CAAE,EAAU,CAAC,CAGrE,CACT,EA5K4B,EAAK,CAAF,CAAkB,GAEtC,AAyKQ,EAvKnB,CAFc,AAaP,MAfwC,GAe/B,EAAmB,CAAK,CAf4B,AAenB,CAfoB,AAeb,EAAuB,AAC7E,GAAM,QAD0B,KACxB,CAAW,SAAE,CAAO,MAAE,CAAI,gBAAE,EAAiB,GAAI,CAAA,CAAI,EAI7D,EAAM,GAJ6C,AAI9C,AAJ+D,QAI9D,CAAc,EAAM,GAAD,QAAa,EAAG,GAAY,EAAG,MAAH,aAAsB,CAEvE,CAAC,EAAM,GAAD,IAAS,EAAG,IACpB,EAAM,CADqB,EAAE,AACxB,IAAS,CAAE,CAAA,CAAO,CAGrB,CAAC,EAAM,GAAD,CAAM,EAAG,IAAI,AACrB,EADuB,AACjB,GAAD,CAAM,CAAE,CAAA,CAAI,CAGnB,IAAM,EAAU,EAAM,GAAhB,AAAe,IAAQ,AACzB,IAAS,GAAF,AAAK,EAAE,CAChB,EAAQ,GAAA,CAAA,CAAD,AAAC,EAAA,EAAM,QAAA,AAAQ,EAAC,EAAQ,GAAG,CAAE,CAAN,CAAoB,CAAC,AAEvD,CAKO,SAAS,EAAc,CAAK,CAAS,CAAW,EAAqB,AAE1E,IAAM,EAAqB,AAFA,CAEA,EAAA,EAAA,WAAF,YAAE,AAAuB,EAAC,GAEnD,EAAM,GAAD,GAFyD,CAAC,EAEhD,EAAE,MAAM,EAAE,OAAO,CAAA,AAAC,IAC/B,EAAU,GADqB,IAAa,AACnC,GAAW,EAAE,MAAM,EAAE,OAAO,CAAA,AAAC,IAChC,CADgC,CAC1B,GADmC,AACpC,KAAS,EAAE,CAClB,EAAM,GAAD,KAAU,CAAE,CAAkB,CAAC,EAAM,GAAD,MAAS,AAAC,CAE3D,CAAK,CAAC,AACN,CAAG,CAAC,AACJ,CAKO,SAAS,EAAe,CAAK,EAAe,AAEjD,IAAM,EAA6C,CAAA,CAAE,CAFzB,AAgB5B,GAbA,EAAM,GAAD,KADmB,CACT,EAAE,MAAM,EAAE,OAAO,CAAC,AAAD,IAC9B,EAAU,GADqB,IAAa,AACnC,GAAW,EAAE,MAAM,EAAE,OAAO,CAAC,IAAA,AAChC,EAAM,EADmC,CACpC,KAAS,EAAE,CACd,EAAM,GAAD,KAAS,CAChB,CADkB,AACA,CAAC,EAAM,GAAD,KAAS,CAAE,CAAE,EAAM,GAAD,KAAS,CAC1C,EAAM,GAAD,KAAS,EAAE,CACzB,CAAkB,CAAC,EAAM,GAAD,KAAS,CAAE,CAAE,EAAM,GAAD,KAAS,AAAR,EAE7C,OAAO,EAAM,GAAD,KAAS,CAE7B,CAAK,CAAC,AACN,CAAG,CAAC,CAEE,AAA2C,CAAC,EAAE,GAAxC,KAAC,IAAI,CAAC,GAAoB,MAAA,CAClC,OAIF,CALkC,CAAC,AAK7B,GAAD,OAAY,CAAE,EAAM,GAAD,OAAC,EAAc,CAAA,CAAE,CACzC,EAAM,GAAD,OAAW,CAAC,MAAO,CAAE,EAAM,GAAD,OAAW,CAAC,MAAO,EAAG,EAAE,CACvD,IAAM,EAAS,EAAM,EAAR,CAAO,OAAW,CAAC,MAAM,CACtC,MAAM,CAAC,OAAO,CAAC,GAAoB,OAAO,CAAC,CAAC,CAAC,EAAU,EAAS,CAA/B,CAAC,EAAmB,AACnD,CADmE,CAAN,AACtD,IAAD,AAAK,CAAC,CACV,IAAI,CAAE,WAAW,CACjB,SAAS,CAAE,QAAQ,GACnB,CACN,CAAK,CAAC,AACN,CAAG,CAAC,AACJ,CAsGO,EAzGO,OAyGE,EACd,CAAI,EAEJ,GAAK,CAAD,GAAK,EAAE,SAKX,CAa6B,EAbH,CAAtB,CAa6B,EAbH,AAa4E,AArB9D,CAQb,EAAE,MAc1B,EAAgB,KAAA,EAdE,AAcuB,UAAU,EAA1B,OAAO,GAehC,CAfgC,KAe1B,CAAC,CAfS,GAeL,CAzBK,AAyBJ,GAAM,CAAF,AAzBI,CAyBH,AAzBI,EAAE,AAyBD,CAAA,AAAC,GAAA,AAAO,EAAmB,QAAQ,CAAC,GAAA,CA5BxD,CAAE,AA4BkF,CAAC,CAAvC,YA5B9B,CAAE,CAAA,CAAM,CAS1B,EACT,CAOA,CARa,GAQP,EAAsD,CAC1D,MAAM,CACN,OAAO,CACP,AAHsB,OAGf,CACP,UAAU,CACV,MAAM,CACN,aAAa,CACb,oBAAoB,CACpB,qhBCzTK,SAAS,EAAiB,CAAS,CAAW,CAAI,EAA+C,AACtG,MAAA,CAAA,EAD8B,AAC9B,EAAO,eAAA,AAAe,EAAE,EAAC,gBAAgB,CAAC,EAAS,CAAA,EAAA,EAAE,EAAF,4BAAgC,AAA9B,EAA+B,GACtF,CAD0F,AAUnF,CAVoF,CAAC,OAU5E,EAAe,CAAO,CAAU,CAAc,EAA2C,AAGvG,IAAM,EAAkC,CAHZ,EAGhB,KAAqC,EAAnC,OAAO,EAA8B,OAAiB,EAC9D,EAAoC,CADrB,EAA6C,EAC5D,AADuE,GAC1B,EAAnC,OAAO,EAA8B,YAAf,IAAiB,CAAA,CAAiB,MAAE,EAC1E,IADuD,EACvD,CADmF,AACnF,EAAA,EAAO,eAAA,AAAe,EAAE,EAAC,cAAc,CAAC,EAAS,CAA1C,CAAiD,EAC1D,CASO,AAV0C,AAAO,IAAS,CAAC,IAUlD,EAAa,CAAK,CAAS,CAAI,EAC7C,AADmE,KAAzC,CAC1B,CAAA,EAAA,EAAO,eAAA,AAAe,EAAE,EAAC,YAAY,CAAC,EAAO,EAC/C,CAD6C,AAQtC,CAR4C,CAAC,OAQpC,EAAW,CAAI,CAAU,CAAO,EAA2C,GAAjE,EACxB,iBAAA,AAAiB,EAAE,EAAC,UAAU,CAAC,EAAM,EAAF,AACrC,CAMO,IAPuC,CAAC,IAO/B,EAAU,CAAM,EAAgB,GAC9C,CADuB,CACvB,iBAAA,AAAiB,EAAE,EAAC,SAAS,CAAC,EAChC,CAOO,GAR+B,CAAC,KAQvB,EAAS,CAAG,CAAU,CAAK,EAAe,CAAlC,IACtB,iBAAA,AAAiB,EAAE,EAAC,QAAQ,CAAC,EAAK,CAAF,CAClC,CAMO,EAPkC,CAAC,MAO1B,EAAQ,CAAI,EAAsC,EAA3C,GACrB,iBAAA,AAAiB,EAAE,EAAC,OAAO,CAAC,EAC9B,CAUO,CAX2B,CAAC,OAWnB,EAAO,CAAG,CAAU,CAAK,CAAnB,CAAsC,GAC1D,EAAA,iBAAA,AAAiB,EAAE,EAAC,MAAM,CAAC,EAAK,CAAF,CAChC,CAOO,EARgC,CAAC,MAQxB,EAAQ,CAAI,EAAqB,EAA1B,GACrB,iBAAA,AAAiB,EAAE,EAAC,OAAO,CAAC,EAC9B,CAaO,CAd2B,CAAC,OAcnB,IACd,MAAA,CADyB,AACzB,EAAA,CADgD,CACzC,iBAAiB,AAAjB,EAAmB,EAAC,WAAW,EAAE,AAC1C,CASO,SAAS,EAAe,CAAO,CAAW,CAAmB,EAA0B,AAC5F,IAAM,EAAQ,CADc,AACd,EAAR,AAAQ,EAAA,eAAA,AAAe,EAAE,EACzB,EAAA,CAAA,EAAA,CAAA,CAAS,SAAA,AAAS,EAAE,EAC1B,GAAK,CAAD,CAEG,GAAK,CAAD,AAFA,CAEQ,CAFN,GAEK,UAAe,CAG/B,CAHiC,MAG1B,EAAO,IAAD,UAAe,CAAC,EAAS,EAAqB,GAAvB,EAA4B,CAAC,IAFjE,OAEyD,IAFzD,EAAA,EAAe,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,QAFjG,WAAA,EAAA,EAAe,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAO3E,MAAA,CAAA,EAAA,EAAO,KAAA,AAAK,EAAE,CAChB,CASO,SAAS,EACd,CAAW,CACX,CAAQ,CACR,CAAmB,EAEnB,EALyB,EAKnB,EAAY,EAAe,KAA3B,OAA0B,CAAG,EAAa,MAAM,CAAE,EAAV,WAAU,CAAe,CAAE,GACnE,EAAA,CAAA,EAAA,EAAM,SADgF,CAAC,QACjF,AAAkB,EAAE,EAEhC,SAAS,EAAc,CAAM,EAAmC,AAC9D,EAAe,MADK,MACN,CAAG,SAAa,EAAF,IAAQ,MAAE,EAAW,OAAF,CAAU,CAAA,CAAA,EAAA,EAAE,kBAAA,AAAkB,IAAK,CAAA,CAAK,CAAL,AAAM,AAC5F,CAEE,MAAA,CAAA,EAAA,EAAO,kBAAA,AAAkB,EAAC,KACxB,CAD8B,GAC1B,EACJ,GAAI,CACF,EAAqB,GAC3B,CAAM,IAD6B,EAAE,AACxB,AAHa,CAGZ,CAAE,CAEV,GAHmB,GAEnB,EAAc,OAAO,CAAC,CAChB,CAAC,AACb,CAFmB,AAkBf,MAdA,CAAI,EAAA,EAAA,UAAU,AAAV,EAAW,GACb,OAAO,CAAC,OAAO,AADgB,CACf,AADgB,EAAE,CACE,IAAI,CACtC,KACE,CADI,CACU,GAFgB,CAAC,AAEb,CAAC,AAC7B,CAAS,CACD,AADC,IAGC,AAJa,CAEV,KACH,EAAc,OAAO,CAAC,CAChB,CAAC,AACjB,CAAS,AAFc,EAKjB,EAAc,IAAI,CAAC,CAGd,CACX,CAAG,CACH,AADI,CAWG,CAfY,aAGU,CAYP,EAAM,CAAO,EAAR,AAAqC,AAC9D,IAAM,EAAA,CAAS,EAAA,CAAT,CAAS,SAAA,AAAS,EAAE,SAC1B,AAAI,EACK,EAAO,EADN,EACK,AADH,CACS,CAAC,MAEtB,CAF6B,CAAC,SAE9B,EAAA,EAAe,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAC9D,OAAO,CAAC,OAAO,EAAC,GACzB,CAUO,CAXuB,CAAC,aAWT,EAAM,CAAO,EAA6B,AAC9D,AADyB,IACnB,EAAA,CAAA,EAAA,CAAA,CAAS,SAAA,AAAS,EAAE,SACtB,AAAJ,EACS,EAAO,EADN,EACK,AADH,CACS,CAAC,IAEtB,EAAA,CAF6B,CAAC,SAE9B,EAAA,EAAe,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAC9E,OAAO,CAAC,OAAO,EAAC,GACzB,CAKO,CANuB,CAAC,OAMf,IACd,MAAO,CAAC,CAAA,CAAA,AADmB,EACnB,CAD+B,CAC9B,SAAA,AAAS,EAAE,CACtB,CAGO,SAAS,IACd,IAAM,CADiB,CACjB,CAAA,CAD6B,CAC7B,CAAA,CAAS,SAAA,AAAS,EAAE,EAC1B,OAAO,GAAQ,GAAF,OAAY,EAAE,CAAC,OAAA,IAAY,GAAS,CAAC,CAAV,AAAW,GAAQ,GAAF,SAAc,EAAE,AAC3E,CAOO,SAAS,EAAkB,CAAQ,EAAwB,KAChE,OAD+B,UAC/B,AAAiB,EAAE,EAAC,iBAAiB,CAAC,EACxC,CASO,KAVyC,CAAC,GAUjC,EAAa,CAAO,EAA4B,AAC9D,IAAM,EAAA,CADoB,AACpB,EAAA,EAAiB,OAAjB,UAAiB,AAAiB,EAAE,EACpC,EAAA,CAAe,EAAA,EAAA,KAAf,UAAe,AAAe,EAAE,EAGhC,WAAE,CAAA,CAAY,CAAA,EAAE,UAAU,CAAC,SAAA,EAAa,CAAA,CAAE,CAE1C,EAAA,CAAA,EAAA,EAAU,AAAV,WAAU,AAAW,EAAC,CAC1B,IAAI,CAAE,EAAa,OAAO,EAAG,CAAX,CAAc,EAAe,OAAO,EAAE,CACxD,EAD8C,CAC1C,GAAa,MAAb,KAAe,CAAA,CAAW,CAAC,AAC/B,GAAG,CAAO,AACd,CAAG,CAAC,AAFmB,CAKf,EAAiB,EAAe,UAAU,AAA3B,EAAgB,AAAa,CAUlD,OATI,GAAgB,MAAO,GAAI,EAAb,EAAiB,EAAE,KACnC,aAAA,AAAa,EAAC,EAAgB,CAAE,MAAM,CAAE,IAAZ,IAAqB,CAAC,CAAC,CAGrD,IAGA,EAAe,IAHL,EAAE,IAGa,CAAC,CAAZ,EAEP,CACT,CAKO,EAR4B,CAAC,EAEpB,IAMA,IACd,IAAM,EADkB,AACD,CAAA,EADU,AACV,EAAA,OAAjB,UAAiB,AAAiB,EAAE,EAGpC,EAFA,AAEU,CAFV,EAAA,EAAe,AAEf,OAAsB,QAFP,AAAe,EAAE,EAET,UAAU,EAAC,EAAK,EAAe,UAAU,EAAX,AAAa,CACpE,OAAO,CACT,CADW,WACX,AAAY,EAAC,GAEf,IAFsB,AAKtB,CALuB,CAKR,UAAU,EAAX,AAAa,AAC7B,AAJoB,CASpB,CATsB,QASb,IACP,IAAM,EAAA,CAAiB,EAAA,EAAA,GADE,GAAS,CAC5B,UAAiB,AAAiB,EAAE,EACpC,EAAA,CAAA,EAAA,CAAA,CAAS,SAAA,AAAS,EAAE,EACpB,EAAU,EAAe,GAAjB,OAA2B,EAAE,AACvC,AAD0B,IACf,GAAH,AACV,EAAO,CADY,EAAE,CACf,UAAe,CAAC,EAE1B,CAQO,IAV0B,CAAC,IAUlB,EAAe,GAAG,AAAY,CAAK,EAAQ,AAEzD,GAAI,EAAK,CAFmB,AAErB,WACL,IAKF,GACF,GANc,EAAE,UAKI,EAAE,gFCxTf,SAAS,EAAgB,CAAI,EAA0B,AAC5D,IAAM,IAEJ,EAH2B,QAGjB,CAAC,MAAM,CAAC,GAAG,CAFS,AAER,GAD1B,sBACmD,CAAC,CAAC,CAE7C,EACJ,CADQ,EACoB,GAAA,EAAO,EAJvC,AAIkE,GAAG,EAAG,CAAE,EAA2B,GAAG,EAAG,CAAE,CAAA,CAA/E,AAAiF,CAEzG,GAAG,AAAE,IAFsD,KAE7C,EAAE,AAClB,CAHgG,CAG5F,CAAD,QAAU,CAAC,EAElB,EAFsB,CAAC,8CClBvB,EAAA,CAAA,CAAA,qBACO,IAAM,EAA6B,SAAjB,EAAiB,EAAA,OAAA,gBAAA,EAAA,gBAAA,8HCwCnC,eAAe,IACpB,GAAI,GACF,WAAA,CAFwC,CAExC,EAF0D,AAE3C,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAC/C,MAAA,CAAA,EAAA,EAAM,KAAA,AAAK,EAAC,IAAI,CAAC,EACjB,WAAA,EAAA,EAAe,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,AACrD,CAAI,MAAO,CAAC,CAAE,GACV,WAAA,EAAA,EAAe,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAE,CAAC,CAAC,AAClE,CACA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]}