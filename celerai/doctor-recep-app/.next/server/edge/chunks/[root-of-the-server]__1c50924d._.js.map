{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/sentry.edge.config.ts"], "sourcesContent": ["// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).\n// The config you add here will be used whenever one of the edge features is loaded.\n// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\n\nimport * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: \"https://<EMAIL>/4509552950640720\",\n\n  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\n  tracesSampleRate: 1,\n\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\n  debug: false,\n});\n"], "names": [], "mappings": "AAAA,4GAA4G;AAC5G,oFAAoF;AACpF,2GAA2G;AAC3G,6DAA6D;;AAE7D;;AAEA,CAAA,GAAA,gMAAA,CAAA,OAAW,AAAD,EAAE;IACV,KAAK;IAEL,mHAAmH;IACnH,kBAAkB;IAElB,2GAA2G;IAC3G,OAAO;AACT"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/instrumentation.ts"], "sourcesContent": ["import * as Sentry from '@sentry/nextjs';\n\nexport async function register() {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    await import('../sentry.server.config');\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    await import('../sentry.edge.config');\n  }\n}\n\nexport const onRequestError = Sentry.captureRequestError;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,uCAA2C;;IAE3C;IAEA,wCAAyC;QACvC;IACF;AACF;AAEO,MAAM,iBAAiB,gMAAA,CAAA,sBAA0B"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "AAAA,KAAK,QAAQ,KAAK,CAAC;AACnB,MAAM;AACN,QAAQ,KAAK,CAAC,KAAO;AACrB,KAAK,QAAQ,CAAC,6BAA6B,GAAG,IAAI,MAAM,SAAS;IAC7D,KAAI,OAAO,EAAE,IAAI;QACb,IAAI,SAAS,QAAQ;YACjB,OAAO,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,KAAK;QAC3C;QACA,IAAI,SAAS,CAAC,GAAG,OAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK;QAClE,OAAO,IAAI,GAAG,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,CAAC,MAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;QACvE,OAAO;IACX;AACJ"}}]}