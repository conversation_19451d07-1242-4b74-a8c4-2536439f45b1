(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["chunks/[root-of-the-server]__a332d90a._.js",{836987:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addHandler:()=>o,maybeInstrument:()=>l,resetInstrumentationHandlers:()=>s,triggerHandlers:()=>u});var r=e.i(653333),i=e.i(36650),a=e.i(732762);let t={},n={};function o(e,n){t[e]=t[e]||[],t[e].push(n)}function s(){Object.keys(t).forEach(e=>{t[e]=void 0})}function l(e,t){if(!n[e]){n[e]=!0;try{t()}catch(t){r.DEBUG_BUILD&&i.logger.error(`Error while instrumenting ${e}`,t)}}}function u(e,n){let o=e&&t[e];if(o)for(let t of o)try{t(n)}catch(n){r.DEBUG_BUILD&&i.logger.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${(0,a.getFunctionName)(t)}
Error:`,n)}}}},336295:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addGlobalErrorInstrumentationHandler:()=>a});var r=e.i(441619),i=e.i(836987);let t=null;function a(e){let t="error";(0,i.addHandler)(t,e),(0,i.maybeInstrument)(t,o)}function o(){t=r.GLOBAL_OBJ.onerror,r.GLOBAL_OBJ.onerror=function(e,n,r,a,o){return(0,i.triggerHandlers)("error",{column:a,error:o,line:r,msg:e,url:n}),!!t&&t.apply(this,arguments)},r.GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__=!0}}},522433:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addGlobalUnhandledRejectionInstrumentationHandler:()=>a});var r=e.i(441619),i=e.i(836987);let t=null;function a(e){let t="unhandledrejection";(0,i.addHandler)(t,e),(0,i.maybeInstrument)(t,o)}function o(){t=r.GLOBAL_OBJ.onunhandledrejection,r.GLOBAL_OBJ.onunhandledrejection=function(e){return(0,i.triggerHandlers)("unhandledrejection",e),!t||t.apply(this,arguments)},r.GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}}},122744:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({registerSpanErrorInstrumentation:()=>u});var r=e.i(653333),i=e.i(336295),a=e.i(522433),o=e.i(36650),s=e.i(526321),l=e.i(883270);let t=!1;function u(){t||(t=!0,(0,i.addGlobalErrorInstrumentationHandler)(c),(0,a.addGlobalUnhandledRejectionInstrumentationHandler)(c))}function c(){let e=(0,s.getActiveSpan)(),t=e&&(0,s.getRootSpan)(e);if(t){let e="internal_error";r.DEBUG_BUILD&&o.logger.log(`[Tracing] Root span: ${e} -> Global error occurred`),t.setStatus({code:l.SPAN_STATUS_ERROR,message:e})}}c.tag="sentry_tracingErrorCallback"}},562572:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({applySdkMetadata:()=>i});var r=e.i(20992);function i(e,t,n=[t],a="npm"){let o=e._metadata||{};o.sdk||(o.sdk={name:`sentry.javascript.${t}`,packages:n.map(e=>({name:`${a}:@sentry/${e}`,version:r.SDK_VERSION})),version:r.SDK_VERSION}),e._metadata=o}},400230:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getHttpSpanDetailsFromUrlObject:()=>s,getSanitizedUrlString:()=>c,getSanitizedUrlStringFromUrlObject:()=>o,isURLObjectRelative:()=>i,parseStringToURLObject:()=>a,parseUrl:()=>l,stripUrlQueryAndFragment:()=>u});var r=e.i(325939);function i(e){return"isRelative"in e}function a(e,t){let n=0>=e.indexOf("://")&&0!==e.indexOf("//"),r=t??(n?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(e,r))return;let t=new URL(e,r);if(n)return{isRelative:n,pathname:t.pathname,search:t.search,hash:t.hash};return t}catch{}}function o(e){if(i(e))return e.pathname;let t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}function s(e,t,n,a,s){let l={[r.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:n,[r.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"url"};return s&&(l["server"===t?"http.route":"url.template"]=s,l[r.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]="route"),a?.method&&(l[r.SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD]=a.method.toUpperCase()),e&&(e.search&&(l["url.query"]=e.search),e.hash&&(l["url.fragment"]=e.hash),e.pathname&&(l["url.path"]=e.pathname,"/"===e.pathname&&(l[r.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]="route")),!i(e)&&(l[r.SEMANTIC_ATTRIBUTE_URL_FULL]=e.href,e.port&&(l["url.port"]=e.port),e.protocol&&(l["url.scheme"]=e.protocol),e.hostname&&(l["server"===t?"server.address":"url.domain"]=e.hostname))),[function(e,t,n,r){let i=n?.method?.toUpperCase()??"GET",a=r||(e?"client"===t?o(e):e.pathname:"/");return`${i} ${a}`}(e,t,a,s),l]}function l(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function u(e){return e.split(/[?#]/,1)[0]}function c(e){let{protocol:t,host:n,path:r}=e,i=n?.replace(/^.*@/,"[filtered]:[filtered]@").replace(/(:80)$/,"").replace(/(:443)$/,"")||"";return`${t?`${t}://`:""}${i}${r}`}},580767:function(e){var{g:t,__dirname:n,m:r,e:i}=e;r.exports=e.x("node:buffer",()=>require("node:buffer"))},371466:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addItemToEnvelope:()=>l,createAttachmentEnvelopeItem:()=>f,createEnvelope:()=>s,createEventEnvelopeHeaders:()=>T,createSpanEnvelopeItem:()=>g,envelopeContainsItemType:()=>c,envelopeItemTypeToDataCategory:()=>h,forEachEnvelopeItem:()=>u,getSdkMetadataForEnvelopeHeader:()=>E,parseEnvelope:()=>_,serializeEnvelope:()=>d});var r=e.i(550361),i=e.i(869033),a=e.i(369674),o=e.i(441619);function s(e,t=[]){return[e,t]}function l(e,t){let[n,r]=e;return[n,[...r,t]]}function u(e,t){for(let n of e[1]){let e=n[0].type;if(t(n,e))return!0}return!1}function c(e,t){return u(e,(e,n)=>t.includes(n))}function p(e){let t=(0,r.getSentryCarrier)(o.GLOBAL_OBJ);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}function d(e){let[t,n]=e,r=JSON.stringify(t);function i(e){"string"==typeof r?r="string"==typeof e?r+e:[p(r),e]:r.push("string"==typeof e?p(e):e)}for(let e of n){let[t,n]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof n||n instanceof Uint8Array)i(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify((0,a.normalize)(n))}i(e)}}return"string"==typeof r?r:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),n=0;for(let r of e)t.set(r,n),n+=r.length;return t}(r)}function _(e){let t="string"==typeof e?p(e):e;function n(e){let n=t.subarray(0,e);return t=t.subarray(e+1),n}function i(){let e=t.indexOf(10);return e<0&&(e=t.length),JSON.parse(function(e){let t=(0,r.getSentryCarrier)(o.GLOBAL_OBJ);return t.decodePolyfill?t.decodePolyfill(e):new TextDecoder().decode(e)}(n(e)))}let a=i(),s=[];for(;t.length;){let e=i(),t="number"==typeof e.length?e.length:void 0;s.push([e,t?n(t):i()])}return[a,s]}function g(e){return[{type:"span"},e]}function f(e){let t="string"==typeof e.data?p(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}let t={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function h(e){return t[e]}function E(e){if(!e?.sdk)return;let{name:t,version:n}=e.sdk;return{name:t,version:n}}function T(e,t,n,r){let a=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:(0,i.dsnToString)(r)},...a&&{trace:a}}}}},899184:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({createCheckInEnvelope:()=>a});var r=e.i(869033),i=e.i(371466);function a(e,t,n,a,o){let s={sent_at:new Date().toISOString()};n?.sdk&&(s.sdk={name:n.sdk.name,version:n.sdk.version}),a&&o&&(s.dsn=(0,r.dsnToString)(o)),t&&(s.trace=t);let l=[{type:"check_in"},e];return(0,i.createEnvelope)(s,[l])}},969033:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getEnvelopeEndpointWithUrlEncodedAuth:()=>a,getReportDialogEndpoint:()=>o});var r=e.i(869033);function i(e){let t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function a(e,t,n){return t||`${i(e)}${e.projectId}/envelope/?${function(e,t){let n={sentry_version:"7"};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}(e,n)}`}function o(e,t){let n=(0,r.makeDsn)(e);if(!n)return"";let a=`${i(n)}embed/error-page/`,o=`dsn=${(0,r.dsnToString)(n)}`;for(let e in t)if("dsn"!==e&&"onClose"!==e)if("user"===e){let e=t.user;if(!e)continue;e.name&&(o+=`&name=${encodeURIComponent(e.name)}`),e.email&&(o+=`&email=${encodeURIComponent(e.email)}`)}else o+=`&${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`;return`${a}?${o}`}},982440:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({createEventEnvelope:()=>l,createSessionEnvelope:()=>s,createSpanEnvelope:()=>u});var r=e.i(872150),i=e.i(869033),a=e.i(371466),o=e.i(526321);function s(e,t,n,r){let o=(0,a.getSdkMetadataForEnvelopeHeader)(n),s={sent_at:new Date().toISOString(),...o&&{sdk:o},...!!r&&t&&{dsn:(0,i.dsnToString)(t)}},l="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return(0,a.createEnvelope)(s,[l])}function l(e,t,n,r){var i;let o=(0,a.getSdkMetadataForEnvelopeHeader)(n),s=e.type&&"replay_event"!==e.type?e.type:"event";(i=n?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let l=(0,a.createEventEnvelopeHeaders)(e,o,r,t);delete e.sdkProcessingMetadata;let u=[{type:s},e];return(0,a.createEnvelope)(l,[u])}function u(e,t){let n=(0,r.getDynamicSamplingContextFromSpan)(e[0]),s=t?.getDsn(),l=t?.getOptions().tunnel,u={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!l&&s&&{dsn:(0,i.dsnToString)(s)}},c=t?.getOptions().beforeSendSpan,p=c?e=>{let t=(0,o.spanToJSON)(e),n=c(t);return n||((0,o.showSpanDropWarning)(),t)}:o.spanToJSON,d=[];for(let t of e){let e=p(t);e&&d.push((0,a.createSpanEnvelopeItem)(e))}return(0,a.createEnvelope)(u,d)}},171485:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addIntegration:()=>c,afterSetupIntegrations:()=>l,defineIntegration:()=>p,getIntegrationsToSetup:()=>o,installedIntegrations:()=>t,setupIntegration:()=>u,setupIntegrations:()=>s});var r=e.i(548729),i=e.i(653333),a=e.i(36650);let t=[];function o(e){let t,n=e.defaultIntegrations||[],r=e.integrations;if(n.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(r))t=[...n,...r];else if("function"==typeof r){let e=r(n);t=Array.isArray(e)?e:[e]}else t=n;let i={};return t.forEach(e=>{let{name:t}=e,n=i[t];n&&!n.isDefaultInstance&&e.isDefaultInstance||(i[t]=e)}),Object.values(i)}function s(e,t){let n={};return t.forEach(t=>{t&&u(e,t,n)}),n}function l(e,t){for(let n of t)n?.afterAllSetup&&n.afterAllSetup(e)}function u(e,n,r){if(r[n.name]){i.DEBUG_BUILD&&a.logger.log(`Integration skipped because it was already installed: ${n.name}`);return}if(r[n.name]=n,-1===t.indexOf(n.name)&&"function"==typeof n.setupOnce&&(n.setupOnce(),t.push(n.name)),n.setup&&"function"==typeof n.setup&&n.setup(e),"function"==typeof n.preprocessEvent){let t=n.preprocessEvent.bind(n);e.on("preprocessEvent",(n,r)=>t(n,r,e))}if("function"==typeof n.processEvent){let t=n.processEvent.bind(n),r=Object.assign((n,r)=>t(n,r,e),{id:n.name});e.addEventProcessor(r)}i.DEBUG_BUILD&&a.logger.log(`Integration installed: ${n.name}`)}function c(e){let t=(0,r.getClient)();if(!t){i.DEBUG_BUILD&&a.logger.warn(`Cannot add integration "${e.name}" because no SDK Client is available.`);return}t.addIntegration(e)}function p(e){return e}}},272339:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({createClientReportEnvelope:()=>a});var r=e.i(371466),i=e.i(321858);function a(e,t,n){let a=[{type:"client_report"},{timestamp:n||(0,i.dateTimestampInSeconds)(),discarded_events:e}];return(0,r.createEnvelope)(t?{dsn:t}:{},[a])}},114400:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){let t=[];e.message&&t.push(e.message);try{let n=e.exception.values[e.exception.values.length-1];n?.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch(e){}return t}e.s({getPossibleEventMessages:()=>r})},873573:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({convertSpanJsonToTransactionEvent:()=>a,convertTransactionEventToSpanJson:()=>i});var r=e.i(325939);function i(e){let{trace_id:t,parent_span_id:n,span_id:i,status:a,origin:o,data:s,op:l}=e.contexts?.trace??{};return{data:s??{},description:e.transaction,op:l,parent_span_id:n,span_id:i??"",start_timestamp:e.start_timestamp??0,status:a,timestamp:e.timestamp,trace_id:t??"",origin:o,profile_id:s?.[r.SEMANTIC_ATTRIBUTE_PROFILE_ID],exclusive_time:s?.[r.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME],measurements:e.measurements,is_segment:!0}}function a(e){return{type:"transaction",timestamp:e.timestamp,start_timestamp:e.start_timestamp,transaction:e.description,contexts:{trace:{trace_id:e.trace_id,span_id:e.span_id,parent_span_id:e.parent_span_id,op:e.op,status:e.status,origin:e.origin,data:{...e.data,...e.profile_id&&{[r.SEMANTIC_ATTRIBUTE_PROFILE_ID]:e.profile_id},...e.exclusive_time&&{[r.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME]:e.exclusive_time}}}},measurements:e.measurements}}},417703:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({BaseClient:()=>x,Client:()=>D,_getTraceInfoFromScope:()=>P});var r=e.i(969033),i=e.i(312268),a=e.i(548729),o=e.i(653333),s=e.i(982440),l=e.i(171485),u=e.i(447489),c=e.i(872150),p=e.i(272339),d=e.i(869033),_=e.i(371466),g=e.i(114400),f=e.i(877130),h=e.i(36650),E=e.i(851195),T=e.i(416971),S=e.i(678436),m=e.i(265405),v=e.i(526321),O=e.i(783296),y=e.i(873573);let t="Not capturing exception because it's already been captured.",n="Discarded session because of missing or non-string release",N=Symbol.for("SentryInternalError"),M=Symbol.for("SentryDoNotSendEventError");function I(e){return{message:e,[N]:!0}}function R(e){return{message:e,[M]:!0}}function A(e){return!!e&&"object"==typeof e&&N in e}function b(e){return!!e&&"object"==typeof e&&M in e}class D{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=(0,d.makeDsn)(e.dsn):o.DEBUG_BUILD&&h.logger.warn("No DSN provided, client will not send events."),this._dsn){let t=(0,r.getEnvelopeEndpointWithUrlEncodedAuth)(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}}captureException(e,n,r){let i=(0,T.uuid4)();if((0,T.checkOrSetAlreadyCaught)(e))return o.DEBUG_BUILD&&h.logger.log(t),i;let a={event_id:i,...n};return this._process(this.eventFromException(e,a).then(e=>this._captureEvent(e,a,r))),a.event_id}captureMessage(e,t,n,r){let i={event_id:(0,T.uuid4)(),...n},a=(0,f.isParameterizedString)(e)?e:String(e),o=(0,f.isPrimitive)(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(o.then(e=>this._captureEvent(e,i,r))),i.event_id}captureEvent(e,n,r){let i=(0,T.uuid4)();if(n?.originalException&&(0,T.checkOrSetAlreadyCaught)(n.originalException))return o.DEBUG_BUILD&&h.logger.log(t),i;let a={event_id:i,...n},s=e.sdkProcessingMetadata||{},l=s.capturedSpanScope,u=s.capturedSpanIsolationScope;return this._process(this._captureEvent(e,a,l||r,u)),a.event_id}captureSession(e){this.sendSession(e),(0,u.updateSession)(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(n=>t.flush(e).then(e=>n&&e))):(0,O.resolvedSyncPromise)(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];(0,l.setupIntegration)(this,e,this._integrations),t||(0,l.afterSetupIntegrations)(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let n=(0,s.createEventEnvelope)(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])n=(0,_.addItemToEnvelope)(n,(0,_.createAttachmentEnvelopeItem)(e));let r=this.sendEnvelope(n);r&&r.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let{release:t,environment:r=i.DEFAULT_ENVIRONMENT}=this._options;if("aggregates"in e){let i=e.attrs||{};if(!i.release&&!t){o.DEBUG_BUILD&&h.logger.warn(n);return}i.release=i.release||t,i.environment=i.environment||r,e.attrs=i}else{if(!e.release&&!t){o.DEBUG_BUILD&&h.logger.warn(n);return}e.release=e.release||t,e.environment=e.environment||r}this.emit("beforeSendSession",e);let a=(0,s.createSessionEnvelope)(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(a)}recordDroppedEvent(e,t,n=1){if(this._options.sendClientReports){let r=`${e}:${t}`;o.DEBUG_BUILD&&h.logger.log(`Recording outcome: "${r}"${n>1?` (${n} times)`:""}`),this._outcomes[r]=(this._outcomes[r]||0)+n}}on(e,t){let n=this._hooks[e]=this._hooks[e]||[];return n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}}emit(e,...t){let n=this._hooks[e];n&&n.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>(o.DEBUG_BUILD&&h.logger.error("Error while sending envelope:",e),e)):(o.DEBUG_BUILD&&h.logger.error("Transport disabled"),(0,O.resolvedSyncPromise)({}))}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=(0,l.setupIntegrations)(this,e),(0,l.afterSetupIntegrations)(this,e)}_updateSessionFromEvent(e,t){let n="fatal"===t.level,r=!1,i=t.exception?.values;if(i)for(let e of(r=!0,i)){let t=e.mechanism;if(t?.handled===!1){n=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&n)&&((0,u.updateSession)(e,{...n&&{status:"crashed"},errors:e.errors||Number(r||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new O.SyncPromise(t=>{let n=0,r=setInterval(()=>{0==this._numProcessing?(clearInterval(r),t(!0)):(n+=1,e&&n>=e&&(clearInterval(r),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,n,r){let i=this.getOptions(),o=Object.keys(this._integrations);return!t.integrations&&o?.length&&(t.integrations=o),this.emit("preprocessEvent",e,t),e.type||r.setLastEventId(e.event_id||t.event_id),(0,m.prepareEvent)(i,e,t,n,this,r).then(e=>(null===e||(this.emit("postprocessEvent",e,t),e.contexts={trace:(0,a.getTraceContextFromScope)(n),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:(0,c.getDynamicSamplingContextFromScope)(this,n),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},n=(0,a.getCurrentScope)(),r=(0,a.getIsolationScope)()){return o.DEBUG_BUILD&&L(e)&&h.logger.log(`Captured error event \`${(0,g.getPossibleEventMessages)(e)[0]||"<unknown>"}\``),this._processEvent(e,t,n,r).then(e=>e.event_id,e=>{o.DEBUG_BUILD&&(b(e)?h.logger.log(e.message):A(e)?h.logger.warn(e.message):h.logger.warn(e))})}_processEvent(e,t,n,r){let i=this.getOptions(),{sampleRate:a}=i,o=C(e),s=L(e),l=e.type||"error",u=`before send for type \`${l}\``,c=void 0===a?void 0:(0,S.parseSampleRate)(a);if(s&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error"),(0,O.rejectedSyncPromise)(R(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));let p="replay_event"===l?"replay":l;return this._prepareEvent(e,t,n,r).then(e=>{if(null===e)throw this.recordDroppedEvent("event_processor",p),R("An event processor returned `null`, will not send event.");return t.data&&!0===t.data.__sentry__?e:function(e,t){let n=`${t} must return \`null\` or a valid event.`;if((0,f.isThenable)(e))return e.then(e=>{if(!(0,f.isPlainObject)(e)&&null!==e)throw I(n);return e},e=>{throw I(`${t} rejected with ${e}`)});if(!(0,f.isPlainObject)(e)&&null!==e)throw I(n);return e}(function(e,t,n,r){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:o}=t,s=n;if(L(s)&&i)return i(s,r);if(C(s)){if(o){let e=o((0,y.convertTransactionEventToSpanJson)(s));if(e?s=(0,E.merge)(n,(0,y.convertSpanJsonToTransactionEvent)(e)):(0,v.showSpanDropWarning)(),s.spans){let e=[];for(let t of s.spans){let n=o(t);n?e.push(n):((0,v.showSpanDropWarning)(),e.push(t))}s.spans=e}}if(a){if(s.spans){let e=s.spans.length;s.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(s,r)}}return s}(0,i,e,t),u)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",p),o){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw R(`${u} returned \`null\`, will not send event.`)}let a=n.getSession()||r.getSession();if(s&&a&&this._updateSessionFromEvent(a,i),o){let e=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let l=i.transaction_info;return o&&l&&i.transaction!==e.transaction&&(i.transaction_info={...l,source:"custom"}),this.sendEvent(i,t),i}).then(null,e=>{if(b(e)||A(e))throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),I(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[n,r]=e.split(":");return{reason:n,category:r,quantity:t}})}_flushOutcomes(){o.DEBUG_BUILD&&h.logger.log("Flushing outcomes...");let e=this._clearOutcomes();if(0===e.length){o.DEBUG_BUILD&&h.logger.log("No outcomes to send");return}if(!this._dsn){o.DEBUG_BUILD&&h.logger.log("No dsn provided, will not send outcomes");return}o.DEBUG_BUILD&&h.logger.log("Sending outcomes:",e);let t=(0,p.createClientReportEnvelope)(e,this._options.tunnel&&(0,d.dsnToString)(this._dsn));this.sendEnvelope(t)}}let x=D;function L(e){return void 0===e.type}function C(e){return"transaction"===e.type}function P(e,t){return t?(0,a.withScope)(t,()=>{let n=(0,v.getActiveSpan)(),r=n?(0,v.spanToTraceContext)(n):(0,a.getTraceContextFromScope)(t);return[n?(0,c.getDynamicSamplingContextFromSpan)(n):(0,c.getDynamicSamplingContextFromScope)(e,t),r]}):[void 0,void 0]}}},181234:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SEVERITY_TEXT_TO_SEVERITY_NUMBER:()=>t});let t={trace:1,debug:5,info:9,warn:13,error:17,fatal:21}}},514774:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({createLogContainerEnvelopeItem:()=>a,createLogEnvelope:()=>o});var r=e.i(869033),i=e.i(371466);function a(e){return[{type:"log",item_count:e.length,content_type:"application/vnd.sentry.items.log+json"},{items:e}]}function o(e,t,n,o){let s={};return t?.sdk&&(s.sdk={name:t.sdk.name,version:t.sdk.version}),n&&o&&(s.dsn=(0,r.dsnToString)(o)),(0,i.createEnvelope)(s,[a(e)])}},4353:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({_INTERNAL_captureLog:()=>E,_INTERNAL_captureSerializedLog:()=>h,_INTERNAL_flushLogsBuffer:()=>T,_INTERNAL_getLogBuffer:()=>S,logAttributeToSerializedLogAttribute:()=>g});var r=e.i(417703),i=e.i(548729),a=e.i(653333),o=e.i(885708),s=e.i(877130),l=e.i(36650),u=e.i(671442),c=e.i(321858),p=e.i(441619),d=e.i(181234),_=e.i(514774);function g(e){switch(typeof e){case"number":if(Number.isInteger(e))return{value:e,type:"integer"};return{value:e,type:"double"};case"boolean":return{value:e,type:"boolean"};case"string":return{value:e,type:"string"};default:{let t="";try{t=JSON.stringify(e)??""}catch{}return{value:t,type:"string"}}}}function f(e,t,n,r=!0){n&&(!e[t]||r)&&(e[t]=n)}function h(e,t){let n=S(e);void 0===n?p.GLOBAL_OBJ._sentryClientToLogBufferMap?.set(e,[t]):(p.GLOBAL_OBJ._sentryClientToLogBufferMap?.set(e,[...n,t]),n.length>=100&&T(e,n))}function E(e,t=(0,i.getClient)(),n=(0,i.getCurrentScope)(),p=h){if(!t){a.DEBUG_BUILD&&l.logger.warn("No client available to capture log.");return}let{_experiments:_,release:T,environment:S}=t.getOptions(),{enableLogs:m=!1,beforeSendLog:v}=_??{};if(!m){a.DEBUG_BUILD&&l.logger.warn("logging option not enabled, log will not be captured.");return}let[,O]=(0,r._getTraceInfoFromScope)(t,n),y={...e.attributes},{user:{id:I,email:R,username:A}}=function(e){let t=(0,i.getGlobalScope)().getScopeData();return(0,o.mergeScopeData)(t,(0,i.getIsolationScope)().getScopeData()),(0,o.mergeScopeData)(t,e.getScopeData()),t}(n);f(y,"user.id",I,!1),f(y,"user.email",R,!1),f(y,"user.name",A,!1),f(y,"sentry.release",T),f(y,"sentry.environment",S);let{name:b,version:L}=t.getSdkMetadata()?.sdk??{};f(y,"sentry.sdk.name",b),f(y,"sentry.sdk.version",L);let C=e.message;if((0,s.isParameterizedString)(C)){let{__sentry_template_string__:e,__sentry_template_values__:t=[]}=C;y["sentry.message.template"]=e,t.forEach((e,t)=>{y[`sentry.message.parameter.${t}`]=e})}let P=(0,u._getSpanForScope)(n);f(y,"sentry.trace.parent_span_id",P?.spanContext().spanId);let N={...e,attributes:y};t.emit("beforeCaptureLog",N);let M=v?v(N):N;if(!M){t.recordDroppedEvent("before_send","log_item",1),a.DEBUG_BUILD&&l.logger.warn("beforeSendLog returned null, log will not be captured.");return}let{level:D,message:x,attributes:U={},severityNumber:w}=M;p(t,{timestamp:(0,c.timestampInSeconds)(),level:D,body:x,trace_id:O?.trace_id,severity_number:w??d.SEVERITY_TEXT_TO_SEVERITY_NUMBER[D],attributes:Object.keys(U).reduce((e,t)=>(e[t]=g(U[t]),e),{})}),t.emit("afterCaptureLog",M)}function T(e,t){let n=t??S(e)??[];if(0===n.length)return;let r=e.getOptions(),i=(0,_.createLogEnvelope)(n,r._metadata,r.tunnel,e.getDsn());p.GLOBAL_OBJ._sentryClientToLogBufferMap?.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(i)}function S(e){return p.GLOBAL_OBJ._sentryClientToLogBufferMap?.get(e)}p.GLOBAL_OBJ._sentryClientToLogBufferMap=new WeakMap},61640:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({eventFromMessage:()=>c,eventFromUnknownInput:()=>u,exceptionFromError:()=>l,parseStackFrames:()=>s});var r=e.i(877130),i=e.i(416971),a=e.i(369674),o=e.i(227325);function s(e,t){return e(t.stack||"",1)}function l(e,t){let n={type:t.name||t.constructor.name,value:t.message},r=s(e,t);return r.length&&(n.stacktrace={frames:r}),n}function u(e,t,n,s){let u=s?.data&&s.data.mechanism||{handled:!0,type:"generic"},[c,p]=function(e,t,n,i){if((0,r.isError)(n))return[n,void 0];if(t.synthetic=!0,(0,r.isPlainObject)(n)){let t=e?.getOptions().normalizeDepth,s={__serialized__:(0,a.normalizeToSize)(n,t)},l=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let n=e[t];if(n instanceof Error)return n}}(n);if(l)return[l,s];let u=function(e){if("name"in e&&"string"==typeof e.name){let t=`'${e.name}' captured as exception`;return"message"in e&&"string"==typeof e.message&&(t+=` with message '${e.message}'`),t}if("message"in e&&"string"==typeof e.message)return e.message;let t=(0,o.extractExceptionKeysForMessage)(e);if((0,r.isErrorEvent)(e))return`Event \`ErrorEvent\` captured as exception with message \`${e.message}\``;let n=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`${n&&"Object"!==n?`'${n}'`:"Object"} captured as exception with keys: ${t}`}(n),c=i?.syntheticException||Error(u);return c.message=u,[c,s]}let s=i?.syntheticException||Error(n);return s.message=`${n}`,[s,void 0]}(e,u,n,s),d={exception:{values:[l(t,c)]}};return p&&(d.extra=p),(0,i.addExceptionTypeValue)(d,void 0,void 0),(0,i.addExceptionMechanism)(d,u),{...d,event_id:s?.event_id}}function c(e,t,n="info",a,o){let l={event_id:a?.event_id,level:n};if(o&&a?.syntheticException){let n=s(e,a.syntheticException);n.length&&(l.exception={values:[{value:t,stacktrace:{frames:n}}]},(0,i.addExceptionMechanism)(l,{synthetic:!0}))}if((0,r.isParameterizedString)(t)){let{__sentry_template_string__:e,__sentry_template_values__:n}=t;return l.logentry={message:e,params:n},l}return l.message=t,l}},938476:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({ServerRuntimeClient:()=>t});var r=e.i(899184),i=e.i(417703),a=e.i(548729),o=e.i(653333),s=e.i(4353),l=e.i(122744),u=e.i(877130),c=e.i(36650),p=e.i(416971),d=e.i(61640),_=e.i(783296);class t extends i.Client{constructor(e){if((0,l.registerSpanErrorInstrumentation)(),super(e),this._logWeight=0,this._options._experiments?.enableLogs){let e=this;e.on("flushLogs",()=>{e._logWeight=0,clearTimeout(e._logFlushIdleTimeout)}),e.on("afterCaptureLog",t=>{e._logWeight+=function(e){let t=0;return e.message&&(t+=2*e.message.length),e.attributes&&Object.values(e.attributes).forEach(e=>{Array.isArray(e)?t+=e.length*f(e[0]):(0,u.isPrimitive)(e)?t+=f(e):t+=100}),t}(t),e._logWeight>=8e5?(0,s._INTERNAL_flushLogsBuffer)(e):e._logFlushIdleTimeout=setTimeout(()=>{(0,s._INTERNAL_flushLogsBuffer)(e)},5e3)}),e.on("flush",()=>{(0,s._INTERNAL_flushLogsBuffer)(e)})}}eventFromException(e,t){let n=(0,d.eventFromUnknownInput)(this,this._options.stackParser,e,t);return n.level="error",(0,_.resolvedSyncPromise)(n)}eventFromMessage(e,t="info",n){return(0,_.resolvedSyncPromise)((0,d.eventFromMessage)(this._options.stackParser,e,t,n,this._options.attachStacktrace))}captureException(e,t,n){return g(t),super.captureException(e,t,n)}captureEvent(e,t,n){return!e.type&&e.exception?.values&&e.exception.values.length>0&&g(t),super.captureEvent(e,t,n)}captureCheckIn(e,t,n){let a="checkInId"in e&&e.checkInId?e.checkInId:(0,p.uuid4)();if(!this._isEnabled())return o.DEBUG_BUILD&&c.logger.warn("SDK not enabled, will not capture check-in."),a;let{release:s,environment:l,tunnel:u}=this.getOptions(),d={check_in_id:a,monitor_slug:e.monitorSlug,status:e.status,release:s,environment:l};"duration"in e&&(d.duration=e.duration),t&&(d.monitor_config={schedule:t.schedule,checkin_margin:t.checkinMargin,max_runtime:t.maxRuntime,timezone:t.timezone,failure_issue_threshold:t.failureIssueThreshold,recovery_threshold:t.recoveryThreshold});let[_,g]=(0,i._getTraceInfoFromScope)(this,n);g&&(d.contexts={trace:g});let f=(0,r.createCheckInEnvelope)(d,_,this.getSdkMetadata(),u,this.getDsn());return o.DEBUG_BUILD&&c.logger.info("Sending checkin:",e.monitorSlug,e.status),this.sendEnvelope(f),a}_prepareEvent(e,t,n,r){return this._options.platform&&(e.platform=e.platform||this._options.platform),this._options.runtime&&(e.contexts={...e.contexts,runtime:e.contexts?.runtime||this._options.runtime}),this._options.serverName&&(e.server_name=e.server_name||this._options.serverName),super._prepareEvent(e,t,n,r)}}function g(e){let t=(0,a.getIsolationScope)().getScopeData().sdkProcessingMetadata.requestSession;if(t){let n=e?.mechanism?.handled??!0;n&&"crashed"!==t.status?t.status="errored":n||(t.status="crashed")}}function f(e){return"string"==typeof e?2*e.length:"number"==typeof e?8:4*("boolean"==typeof e)}}},477984:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({handleCallbackErrors:()=>i});var r=e.i(877130);function i(e,t,n=()=>{}){var a,o,s;let l;try{l=e()}catch(e){throw t(e),n(),e}return a=l,o=t,s=n,(0,r.isThenable)(a)?a.then(e=>(s(),e),e=>{throw o(e),s(),e}):(s(),a)}},895818:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({sampleSpan:()=>s});var r=e.i(653333),i=e.i(988191),a=e.i(36650),o=e.i(678436);function s(e,t,n){let s,l;if(!(0,i.hasSpansEnabled)(e))return[!1];"function"==typeof e.tracesSampler?(s=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),l=!0):void 0!==t.parentSampled?s=t.parentSampled:void 0!==e.tracesSampleRate&&(s=e.tracesSampleRate,l=!0);let u=(0,o.parseSampleRate)(s);if(void 0===u)return r.DEBUG_BUILD&&a.logger.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(s)} of type ${JSON.stringify(typeof s)}.`),[!1];if(!u)return r.DEBUG_BUILD&&a.logger.log(`[Tracing] Discarding transaction because ${"function"==typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),[!1,u,l];let c=n<u;return!c&&r.DEBUG_BUILD&&a.logger.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(s)})`),[c,u,l]}},372618:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({LRUMap:()=>t});class t{constructor(e){this._maxSize=e,this._cache=new Map}get size(){return this._cache.size}get(e){let t=this._cache.get(e);if(void 0!==t)return this._cache.delete(e),this._cache.set(e,t),t}set(e,t){this._cache.size>=this._maxSize&&this._cache.delete(this._cache.keys().next().value),this._cache.set(e,t)}remove(e){let t=this._cache.get(e);return t&&this._cache.delete(e),t}clear(){this._cache.clear()}keys(){return Array.from(this._cache.keys())}values(){let e=[];return this._cache.forEach(t=>e.push(t)),e}}}},421820:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e,t,n){let r,i,a,o=n?.maxWait?Math.max(n.maxWait,t):0,s=n?.setTimeoutImpl||setTimeout;function l(){return u(),r=e()}function u(){void 0!==i&&clearTimeout(i),void 0!==a&&clearTimeout(a),i=a=void 0}function c(){return i&&clearTimeout(i),i=s(l,t),o&&void 0===a&&(a=s(l,o)),r}return c.cancel=u,c.flush=function(){return void 0!==i||void 0!==a?l():r},c}e.s({debounce:()=>r})},610520:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({setMeasurement:()=>s,timedEventsToMeasurements:()=>l});var r=e.i(653333),i=e.i(325939),a=e.i(36650),o=e.i(526321);function s(e,t,n,l=(0,o.getActiveSpan)()){let u=l&&(0,o.getRootSpan)(l);u&&(r.DEBUG_BUILD&&a.logger.log(`[Measurement] Setting measurement on root span: ${e} = ${t} ${n}`),u.addEvent(e,{[i.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]:t,[i.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]:n}))}function l(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let n=e.attributes||{},r=n[i.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT],a=n[i.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE];"string"==typeof r&&"number"==typeof a&&(t[e.name]={value:a,unit:r})}),t}},609849:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({logSpanEnd:()=>s,logSpanStart:()=>o});var r=e.i(653333),i=e.i(36650),a=e.i(526321);function o(e){if(!r.DEBUG_BUILD)return;let{description:t="< unknown name >",op:n="< unknown op >",parent_span_id:o}=(0,a.spanToJSON)(e),{spanId:s}=e.spanContext(),l=(0,a.spanIsSampled)(e),u=(0,a.getRootSpan)(e),c=u===e,p=`[Tracing] Starting ${l?"sampled":"unsampled"} ${c?"root ":""}span`,d=[`op: ${n}`,`name: ${t}`,`ID: ${s}`];if(o&&d.push(`parent ID: ${o}`),!c){let{op:e,description:t}=(0,a.spanToJSON)(u);d.push(`root ID: ${u.spanContext().spanId}`),e&&d.push(`root op: ${e}`),t&&d.push(`root description: ${t}`)}i.logger.log(`${p}
  ${d.join("\n  ")}`)}function s(e){if(!r.DEBUG_BUILD)return;let{description:t="< unknown name >",op:n="< unknown op >"}=(0,a.spanToJSON)(e),{spanId:o}=e.spanContext(),s=(0,a.getRootSpan)(e)===e,l=`[Tracing] Finishing "${n}" ${s?"root ":""}span "${t}" with ID ${o}`;i.logger.log(l)}},913423:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({isNativeFunction:()=>l,supportsDOMError:()=>i,supportsDOMException:()=>a,supportsErrorEvent:()=>r,supportsFetch:()=>n,supportsHistory:()=>o,supportsNativeFetch:()=>u,supportsReferrerPolicy:()=>p,supportsReportingObserver:()=>c}),e.i(653333),e.i(36650);let t=e.i(441619).GLOBAL_OBJ;function r(){try{return new ErrorEvent(""),!0}catch(e){return!1}}function i(){try{return new DOMError(""),!0}catch(e){return!1}}function a(){try{return new DOMException(""),!0}catch(e){return!1}}function o(){return"history"in t&&!!t.history}let n=s;function s(){if(!("fetch"in t))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}function l(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function u(){return!0}function c(){return"ReportingObserver"in t}function p(){if(!s())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(e){return!1}}}},871401:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({addFetchEndInstrumentationHandler:()=>c,addFetchInstrumentationHandler:()=>u,parseFetchArgs:()=>h});var r=e.i(877130),i=e.i(227325),a=e.i(913423),o=e.i(321858),s=e.i(441619),l=e.i(836987);function u(e,t){let n="fetch";(0,l.addHandler)(n,e),(0,l.maybeInstrument)(n,()=>p(void 0,t))}function c(e){let t="fetch-body-resolved";(0,l.addHandler)(t,e),(0,l.maybeInstrument)(t,()=>p(_))}function p(e,t=!1){(!t||(0,a.supportsNativeFetch)())&&(0,i.fill)(s.GLOBAL_OBJ,"fetch",function(t){return function(...n){let a=Error(),{method:u,url:c}=h(n),p={args:n,fetchData:{method:u,url:c},startTimestamp:1e3*(0,o.timestampInSeconds)(),virtualError:a,headers:function(e){let[t,n]=e;try{if("object"==typeof n&&null!==n&&"headers"in n&&n.headers)return new Headers(n.headers);if((0,r.isRequest)(t))return new Headers(t.headers)}catch{}}(n)};return e||(0,l.triggerHandlers)("fetch",{...p}),t.apply(s.GLOBAL_OBJ,n).then(async t=>(e?e(t):(0,l.triggerHandlers)("fetch",{...p,endTimestamp:1e3*(0,o.timestampInSeconds)(),response:t}),t),e=>{if((0,l.triggerHandlers)("fetch",{...p,endTimestamp:1e3*(0,o.timestampInSeconds)(),error:e}),(0,r.isError)(e)&&void 0===e.stack&&(e.stack=a.stack,(0,i.addNonEnumerableProperty)(e,"framesToPop",1)),e instanceof TypeError&&("Failed to fetch"===e.message||"Load failed"===e.message||"NetworkError when attempting to fetch resource."===e.message))try{let t=new URL(p.fetchData.url);e.message=`${e.message} (${t.host})`}catch{}throw e})}})}async function d(e,t){if(e?.body){let n=e.body,r=n.getReader(),i=setTimeout(()=>{n.cancel().then(null,()=>{})},9e4),a=!0;for(;a;){let e;try{e=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);let{done:i}=await r.read();clearTimeout(e),i&&(t(),a=!1)}catch(e){a=!1}finally{clearTimeout(e)}}clearTimeout(i),r.releaseLock(),n.cancel().then(null,()=>{})}}function _(e){let t;try{t=e.clone()}catch{return}d(t,()=>{(0,l.triggerHandlers)("fetch-body-resolved",{endTimestamp:1e3*(0,o.timestampInSeconds)(),response:e})})}function g(e,t){return!!e&&"object"==typeof e&&!!e[t]}function f(e){return"string"==typeof e?e:e?g(e,"url")?e.url:e.toString?e.toString():"":""}function h(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,n]=e;return{url:f(t),method:g(n,"method")?String(n.method).toUpperCase():"GET"}}let t=e[0];return{url:f(t),method:g(t,"method")?String(t.method).toUpperCase():"GET"}}},12822:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e,t){var n,r,a,o;let s=t?.getDsn(),l=t?.getOptions().tunnel;return n=e,!!(r=s)&&n.includes(r.host)||(a=e,!!(o=l)&&i(a)===i(o))}function i(e){return"/"===e[e.length-1]?e.slice(0,-1):e}e.s({isSentryRequestUrl:()=>r})},166883:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SentryNonRecordingSpan:()=>t});var r=e.i(848532),i=e.i(526321);class t{constructor(e={}){this._traceId=e.traceId||(0,r.generateTraceId)(),this._spanId=e.spanId||(0,r.generateSpanId)()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:i.TRACE_FLAG_NONE}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}}},444127:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SentrySpan:()=>t});var r=e.i(548729),i=e.i(653333),a=e.i(982440),o=e.i(325939),s=e.i(36650),l=e.i(848532),u=e.i(526321),c=e.i(321858),p=e.i(872150),d=e.i(609849),_=e.i(610520),g=e.i(673189);class t{constructor(e={}){this._traceId=e.traceId||(0,l.generateTraceId)(),this._spanId=e.spanId||(0,l.generateSpanId)(),this._startTime=e.startTimestamp||(0,c.timestampInSeconds)(),this._links=e.links,this._attributes={},this.setAttributes({[o.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"manual",[o.SEMANTIC_ATTRIBUTE_SENTRY_OP]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:n?u.TRACE_FLAG_SAMPLED:u.TRACE_FLAG_NONE}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=(0,u.spanTimeInputToSeconds)(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(o.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,"custom"),this}end(e){this._endTime||(this._endTime=(0,u.spanTimeInputToSeconds)(e),(0,d.logSpanEnd)(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[o.SEMANTIC_ATTRIBUTE_SENTRY_OP],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:(0,u.getStatusMessage)(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[o.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN],profile_id:this._attributes[o.SEMANTIC_ATTRIBUTE_PROFILE_ID],exclusive_time:this._attributes[o.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME],measurements:(0,_.timedEventsToMeasurements)(this._events),is_segment:this._isStandaloneSpan&&(0,u.getRootSpan)(this)===this||void 0,segment_id:this._isStandaloneSpan?(0,u.getRootSpan)(this).spanContext().spanId:void 0,links:(0,u.convertSpanLinksForEnvelope)(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){i.DEBUG_BUILD&&s.logger.log("[Tracing] Adding an event to span:",e);let r=f(t)?t:n||(0,c.timestampInSeconds)(),a=f(t)?{}:t||{},o={name:e,time:(0,u.spanTimeInputToSeconds)(r),attributes:a};return this._events.push(o),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=(0,r.getClient)();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===(0,u.getRootSpan)(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=(0,r.getClient)();if(!t)return;let n=e[1];if(!n||0===n.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}((0,a.createSpanEnvelope)([this],e)):(i.DEBUG_BUILD&&s.logger.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));let t=this._convertSpanToTransaction();t&&((0,g.getCapturedScopesOnSpan)(this).scope||(0,r.getCurrentScope)()).captureEvent(t)}_convertSpanToTransaction(){if(!h((0,u.spanToJSON)(this)))return;this._name||(i.DEBUG_BUILD&&s.logger.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");let{scope:e,isolationScope:n}=(0,g.getCapturedScopesOnSpan)(this),r=e?.getScopeData().sdkProcessingMetadata?.normalizedRequest;if(!0!==this._sampled)return;let a=(0,u.getSpanDescendants)(this).filter(e=>{var n;return e!==this&&!((n=e)instanceof t&&n.isStandaloneSpan())}).map(e=>(0,u.spanToJSON)(e)).filter(h),l=this._attributes[o.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];delete this._attributes[o.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME],a.forEach(e=>{delete e.data[o.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]});let c={contexts:{trace:(0,u.spanToTransactionTraceContext)(this)},spans:a.length>1e3?a.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):a,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:(0,p.getDynamicSamplingContextFromSpan)(this)},request:r,...l&&{transaction_info:{source:l}}},d=(0,_.timedEventsToMeasurements)(this._events);return d&&Object.keys(d).length&&(i.DEBUG_BUILD&&s.logger.log("[Measurements] Adding measurements to transaction event",JSON.stringify(d,void 0,2)),c.measurements=d),c}}function f(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function h(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}}},545222:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({continueTrace:()=>n,startInactiveSpan:()=>R,startNewTrace:()=>L,startSpan:()=>y,startSpanManual:()=>I,suppressTracing:()=>b,withActiveSpan:()=>A});var r=e.i(230098),i=e.i(550361),a=e.i(548729),o=e.i(653333),s=e.i(325939),l=e.i(477984),u=e.i(988191),c=e.i(36650),p=e.i(678436),d=e.i(848532),_=e.i(671442),g=e.i(526321),f=e.i(219422),h=e.i(872150),E=e.i(609849),T=e.i(895818),S=e.i(166883),m=e.i(444127),v=e.i(883270),O=e.i(673189);let t="__SENTRY_SUPPRESS_TRACING__";function y(e,t){let n=N();if(n.startSpan)return n.startSpan(e,t);let r=P(e),{forceTransaction:i,parentSpan:o,scope:s}=e,u=s?.clone();return(0,a.withScope)(u,()=>x(o)(()=>{let n=(0,a.getCurrentScope)(),o=D(n),s=e.onlyIfParent&&!o?new S.SentryNonRecordingSpan:C({parentSpan:o,spanArguments:r,forceTransaction:i,scope:n});return(0,_._setSpanForScope)(n,s),(0,l.handleCallbackErrors)(()=>t(s),()=>{let{status:e}=(0,g.spanToJSON)(s);s.isRecording()&&(!e||"ok"===e)&&s.setStatus({code:v.SPAN_STATUS_ERROR,message:"internal_error"})},()=>{s.end()})}))}function I(e,t){let n=N();if(n.startSpanManual)return n.startSpanManual(e,t);let r=P(e),{forceTransaction:i,parentSpan:o,scope:s}=e,u=s?.clone();return(0,a.withScope)(u,()=>x(o)(()=>{let n=(0,a.getCurrentScope)(),o=D(n),s=e.onlyIfParent&&!o?new S.SentryNonRecordingSpan:C({parentSpan:o,spanArguments:r,forceTransaction:i,scope:n});return(0,_._setSpanForScope)(n,s),(0,l.handleCallbackErrors)(()=>t(s,()=>s.end()),()=>{let{status:e}=(0,g.spanToJSON)(s);s.isRecording()&&(!e||"ok"===e)&&s.setStatus({code:v.SPAN_STATUS_ERROR,message:"internal_error"})})}))}function R(e){let t=N();if(t.startInactiveSpan)return t.startInactiveSpan(e);let n=P(e),{forceTransaction:r,parentSpan:i}=e;return(e.scope?t=>(0,a.withScope)(e.scope,t):void 0!==i?e=>A(i,e):e=>e())(()=>{let t=(0,a.getCurrentScope)(),i=D(t);return e.onlyIfParent&&!i?new S.SentryNonRecordingSpan:C({parentSpan:i,spanArguments:n,forceTransaction:r,scope:t})})}let n=(e,t)=>{let n=(0,i.getMainCarrier)(),o=(0,r.getAsyncContextStrategy)(n);if(o.continueTrace)return o.continueTrace(e,t);let{sentryTrace:s,baggage:l}=e;return(0,a.withScope)(e=>{let n=(0,f.propagationContextFromHeaders)(s,l);return e.setPropagationContext(n),t()})};function A(e,t){let n=N();return n.withActiveSpan?n.withActiveSpan(e,t):(0,a.withScope)(n=>((0,_._setSpanForScope)(n,e||void 0),t(n)))}function b(e){let n=N();return n.suppressTracing?n.suppressTracing(e):(0,a.withScope)(n=>{n.setSDKProcessingMetadata({[t]:!0});let r=e();return n.setSDKProcessingMetadata({[t]:void 0}),r})}function L(e){return(0,a.withScope)(t=>(t.setPropagationContext({traceId:(0,d.generateTraceId)(),sampleRand:Math.random()}),o.DEBUG_BUILD&&c.logger.info(`Starting a new trace with id ${t.getPropagationContext().traceId}`),A(null,e)))}function C({parentSpan:e,spanArguments:n,forceTransaction:r,scope:i}){let o;if(!(0,u.hasSpansEnabled)()){let t=new S.SentryNonRecordingSpan;if(r||!e){let e={sampled:"false",sample_rate:"0",transaction:n.name,...(0,h.getDynamicSamplingContextFromSpan)(t)};(0,h.freezeDscOnSpan)(t,e)}return t}let s=(0,a.getIsolationScope)();if(e&&!r)o=function(e,n,r){let{spanId:i,traceId:o}=e.spanContext(),s=!n.getScopeData().sdkProcessingMetadata[t]&&(0,g.spanIsSampled)(e),l=s?new m.SentrySpan({...r,parentSpanId:i,traceId:o,sampled:s}):new S.SentryNonRecordingSpan({traceId:o});(0,g.addChildSpanToSpan)(e,l);let u=(0,a.getClient)();return u&&(u.emit("spanStart",l),r.endTimestamp&&u.emit("spanEnd",l)),l}(e,i,n),(0,g.addChildSpanToSpan)(e,o);else if(e){let t=(0,h.getDynamicSamplingContextFromSpan)(e),{traceId:r,spanId:a}=e.spanContext(),s=(0,g.spanIsSampled)(e);o=M({traceId:r,parentSpanId:a,...n},i,s),(0,h.freezeDscOnSpan)(o,t)}else{let{traceId:e,dsc:t,parentSpanId:r,sampled:a}={...s.getPropagationContext(),...i.getPropagationContext()};o=M({traceId:e,parentSpanId:r,...n},i,a),t&&(0,h.freezeDscOnSpan)(o,t)}return(0,E.logSpanStart)(o),(0,O.setCapturedScopesOnSpan)(o,i,s),o}function P(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let n={...t};return n.startTimestamp=(0,g.spanTimeInputToSeconds)(e.startTime),delete n.startTime,n}return t}function N(){let e=(0,i.getMainCarrier)();return(0,r.getAsyncContextStrategy)(e)}function M(e,n,r){let i=(0,a.getClient)(),l=i?.getOptions()||{},{name:u=""}=e,d={spanAttributes:{...e.attributes},spanName:u,parentSampled:r};i?.emit("beforeSampling",d,{decision:!1});let _=d.parentSampled??r,g=d.spanAttributes,f=n.getPropagationContext(),[h,E,S]=n.getScopeData().sdkProcessingMetadata[t]?[!1]:(0,T.sampleSpan)(l,{name:u,parentSampled:_,attributes:g,parentSampleRate:(0,p.parseSampleRate)(f.dsc?.sample_rate)},f.sampleRand),v=new m.SentrySpan({...e,attributes:{[s.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"custom",[s.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:void 0!==E&&S?E:void 0,...g},sampled:h});return!h&&i&&(o.DEBUG_BUILD&&c.logger.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),i.recordDroppedEvent("sample_rate","transaction")),i&&i.emit("spanStart",v),v}function D(e){let t=(0,_._getSpanForScope)(e);if(!t)return;let n=(0,a.getClient)();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?(0,g.getRootSpan)(t):t}function x(e){return void 0!==e?t=>A(e,t):e=>e()}}},734155:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getTraceData:()=>d});var r=e.i(230098),i=e.i(550361),a=e.i(548729),o=e.i(917),s=e.i(36650),l=e.i(526321),u=e.i(872150),c=e.i(219422),p=e.i(257562);function d(e={}){let t=e.client||(0,a.getClient)();if(!(0,o.isEnabled)()||!t)return{};let n=(0,i.getMainCarrier)(),_=(0,r.getAsyncContextStrategy)(n);if(_.getTraceData)return _.getTraceData(e);let g=e.scope||(0,a.getCurrentScope)(),f=e.span||(0,l.getActiveSpan)(),h=f?(0,l.spanToTraceHeader)(f):function(e){let{traceId:t,sampled:n,propagationSpanId:r}=e.getPropagationContext();return(0,c.generateSentryTraceHeader)(t,r,n)}(g),E=f?(0,u.getDynamicSamplingContextFromSpan)(f):(0,u.getDynamicSamplingContextFromScope)(t,g),T=(0,p.dynamicSamplingContextToSentryBaggageHeader)(E);return c.TRACEPARENT_REGEXP.test(h)?{"sentry-trace":h,baggage:T}:(s.logger.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}},143517:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({_addTracingHeadersToFetchRequest:()=>f,instrumentFetchRequest:()=>g});var r=e.i(548729),i=e.i(325939),a=e.i(877130),o=e.i(988191),s=e.i(526321),l=e.i(257562),u=e.i(166883),c=e.i(883270),p=e.i(545222),d=e.i(734155),_=e.i(400230);function g(e,t,n,a,l="auto.http.browser"){if(!e.fetchData)return;let{method:d,url:h}=e.fetchData,E=(0,o.hasSpansEnabled)()&&t(h);if(e.endTimestamp&&E){let t=e.fetchData.__span;if(!t)return;let n=a[t];n&&(function(e,t){if(t.response){(0,c.setHttpStatus)(e,t.response.status);let n=t.response?.headers&&t.response.headers.get("content-length");if(n){let t=parseInt(n);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:c.SPAN_STATUS_ERROR,message:"internal_error"});e.end()}(n,e),delete a[t]);return}let T=!!(0,s.getActiveSpan)(),S=E&&T?(0,p.startInactiveSpan)(function(e,t,n){let r=(0,_.parseStringToURLObject)(e);return{name:r?`${t} ${(0,_.getSanitizedUrlStringFromUrlObject)(r)}`:t,attributes:function(e,t,n,r){let a={url:e,type:"fetch","http.method":n,[i.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:r,[i.SEMANTIC_ATTRIBUTE_SENTRY_OP]:"http.client"};return t&&((0,_.isURLObjectRelative)(t)||(a["http.url"]=t.href,a["server.address"]=t.host),t.search&&(a["http.query"]=t.search),t.hash&&(a["http.fragment"]=t.hash)),a}(e,r,t,n)}}(h,d,l)):new u.SentryNonRecordingSpan;if(e.fetchData.__span=S.spanContext().spanId,a[S.spanContext().spanId]=S,n(e.fetchData.url)){let t=e.args[0],n=e.args[1]||{},r=f(t,n,(0,o.hasSpansEnabled)()&&T?S:void 0);r&&(e.args[1]=n,n.headers=r)}let m=(0,r.getClient)();if(m){let t={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};m.emit("beforeOutgoingRequestSpan",S,t)}return S}function f(e,t,n){var r;let i=(0,d.getTraceData)({span:n}),o=i["sentry-trace"],s=i.baggage;if(!o)return;let l=t.headers||((0,a.isRequest)(e)?e.headers:void 0);if(!l)return{...i};if(r=l,"undefined"!=typeof Headers&&(0,a.isInstanceOf)(r,Headers)){let e=new Headers(l);if(e.get("sentry-trace")||e.set("sentry-trace",o),s){let t=e.get("baggage");t?h(t)||e.set("baggage",`${t},${s}`):e.set("baggage",s)}return e}if(Array.isArray(l)){let e=[...l];l.find(e=>"sentry-trace"===e[0])||e.push(["sentry-trace",o]);let t=l.find(e=>"baggage"===e[0]&&h(e[1]));return s&&!t&&e.push(["baggage",s]),e}{let e="sentry-trace"in l?l["sentry-trace"]:void 0,t="baggage"in l?l.baggage:void 0,n=t?Array.isArray(t)?[...t]:[t]:[],r=t&&(Array.isArray(t)?t.find(e=>h(e)):h(t));return s&&!r&&n.push(s),{...l,"sentry-trace":e??o,baggage:n.length>0?n.join(","):void 0}}}function h(e){return e.split(",").some(e=>e.trim().startsWith(l.SENTRY_BAGGAGE_KEY_PREFIX))}},385205:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({addBreadcrumb:()=>o});var r=e.i(548729),i=e.i(36650),a=e.i(321858);function o(e,t){let n=(0,r.getClient)(),o=(0,r.getIsolationScope)();if(!n)return;let{beforeBreadcrumb:s=null,maxBreadcrumbs:l=100}=n.getOptions();if(l<=0)return;let u={timestamp:(0,a.dateTimestampInSeconds)(),...e},c=s?(0,i.consoleSandbox)(()=>s(u,t)):u;null!==c&&(n.emit&&n.emit("beforeAddBreadcrumb",c,t),o.addBreadcrumb(c,l))}},482545:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}e.s({getBreadcrumbLogLevelFromHttpStatusCode:()=>r})},77599:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SENTRY_BUFFER_FULL_ERROR:()=>t,makePromiseBuffer:()=>i});var r=e.i(783296);let t=Symbol.for("SentryBufferFullError");function i(e){let n=[];function i(e){return n.splice(n.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:n,add:function(a){if(!(void 0===e||n.length<e))return(0,r.rejectedSyncPromise)(t);let o=a();return -1===n.indexOf(o)&&n.push(o),o.then(()=>i(o)).then(null,()=>i(o).then(null,()=>{})),o},drain:function(e){return new r.SyncPromise((t,i)=>{let a=n.length;if(!a)return t(!0);let o=setTimeout(()=>{e&&e>0&&t(!1)},e);n.forEach(e=>{(0,r.resolvedSyncPromise)(e).then(()=>{--a||(clearTimeout(o),t(!0))},i)})})}}}}},317205:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({DEFAULT_RETRY_AFTER:()=>t,disabledUntil:()=>i,isRateLimited:()=>a,parseRetryAfterHeader:()=>r,updateRateLimits:()=>o});let t=6e4;function r(e,n=Date.now()){let i=parseInt(`${e}`,10);if(!isNaN(i))return 1e3*i;let a=Date.parse(`${e}`);return isNaN(a)?t:a-n}function i(e,t){return e[t]||e.all||0}function a(e,t,n=Date.now()){return i(e,t)>n}function o(e,{statusCode:t,headers:n},i=Date.now()){let a={...e},s=n?.["x-sentry-rate-limits"],l=n?.["retry-after"];if(s)for(let e of s.trim().split(",")){let[t,n,,,r]=e.split(":",5),o=parseInt(t,10),s=(isNaN(o)?60:o)*1e3;if(n)for(let e of n.split(";"))"metric_bucket"===e?(!r||r.split(";").includes("custom"))&&(a[e]=i+s):a[e]=i+s;else a.all=i+s}else l?a.all=i+r(l,i):429===t&&(a.all=i+6e4);return a}}},68401:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({DEFAULT_TRANSPORT_BUFFER_SIZE:()=>t,createTransport:()=>u});var r=e.i(653333),i=e.i(371466),a=e.i(36650),o=e.i(77599),s=e.i(317205),l=e.i(783296);let t=64;function u(e,n,c=(0,o.makePromiseBuffer)(e.bufferSize||t)){let p={};return{send:function(t){let u=[];if((0,i.forEachEnvelopeItem)(t,(t,n)=>{let r=(0,i.envelopeItemTypeToDataCategory)(n);(0,s.isRateLimited)(p,r)?e.recordDroppedEvent("ratelimit_backoff",r):u.push(t)}),0===u.length)return(0,l.resolvedSyncPromise)({});let d=(0,i.createEnvelope)(t[0],u),_=t=>{(0,i.forEachEnvelopeItem)(d,(n,r)=>{e.recordDroppedEvent(t,(0,i.envelopeItemTypeToDataCategory)(r))})};return c.add(()=>n({body:(0,i.serializeEnvelope)(d)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&r.DEBUG_BUILD&&a.logger.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),p=(0,s.updateRateLimits)(p,e),e),e=>{throw _("network_error"),r.DEBUG_BUILD&&a.logger.error("Encountered error running transport request:",e),e})).then(e=>e,e=>{if(e===o.SENTRY_BUFFER_FULL_ERROR)return r.DEBUG_BUILD&&a.logger.error("Skipped sending event because buffer is full."),_("queue_overflow"),(0,l.resolvedSyncPromise)({});throw e})},flush:e=>c.drain(e)}}}},750080:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({filenameIsInApp:()=>i,node:()=>a,nodeStackLineParser:()=>o});var r=e.i(732762);function i(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==e&&!e.includes("node_modules/")}function a(e){let t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return a=>{let o=a.match(n);if(o){let t,n,a,l,u;if(o[1]){let e=(a=o[1]).lastIndexOf(".");if("."===a[e-1]&&e--,e>0){t=a.slice(0,e),n=a.slice(e+1);let r=t.indexOf(".Module");r>0&&(a=a.slice(r+1),t=t.slice(0,r))}l=void 0}n&&(l=t,u=n),"<anonymous>"===n&&(u=void 0,a=void 0),void 0===a&&(u=u||r.UNKNOWN_FUNCTION,a=l?`${l}.${u}`:u);let c=o[2]?.startsWith("file://")?o[2].slice(7):o[2],p="native"===o[5];return c?.match(/\/[A-Z]:/)&&(c=c.slice(1)),c||!o[5]||p||(c=o[5]),{filename:c?decodeURI(c):void 0,module:e?e(c):void 0,function:a,lineno:s(o[3]),colno:s(o[4]),in_app:i(c||"",p)}}if(a.match(t))return{filename:a}}}function o(e){return[90,a(e)]}function s(e){return parseInt(e||"",10)||void 0}},506407:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({_shouldDropEvent:()=>s,dedupeIntegration:()=>t});var r=e.i(653333),i=e.i(171485),a=e.i(36650),o=e.i(732762);let t=(0,i.defineIntegration)(()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{if(s(t,e))return r.DEBUG_BUILD&&a.logger.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return e=t}}});function s(e,t){return!!t&&!!(function(e,t){let n=e.message,r=t.message;return(!!n||!!r)&&(!n||!!r)&&(!!n||!r)&&n===r&&!!u(e,t)&&!!l(e,t)&&!0}(e,t)||function(e,t){let n=c(t),r=c(e);return!!n&&!!r&&n.type===r.type&&n.value===r.value&&!!u(e,t)&&!!l(e,t)}(e,t))}function l(e,t){let n=(0,o.getFramesFromEvent)(e),r=(0,o.getFramesFromEvent)(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let e=0;e<r.length;e++){let t=r[e],i=n[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function u(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch(e){return!1}}function c(e){return e.exception?.values&&e.exception.values[0]}}},15570:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({eventFiltersIntegration:()=>n,inboundFiltersIntegration:()=>p});var r=e.i(653333),i=e.i(171485),a=e.i(114400),o=e.i(36650),s=e.i(416971),l=e.i(483213);let t=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],n=(0,i.defineIntegration)((e={})=>{let t;return{name:"EventFilters",setup(n){t=u(e,n.getOptions())},processEvent:(n,i,p)=>(t||(t=u(e,p.getOptions())),!function(e,t){if(e.type){if("transaction"===e.type&&function(e,t){if(!t?.length)return!1;let n=e.transaction;return!!n&&(0,l.stringMatchesSomePattern)(n,t)}(e,t.ignoreTransactions))return r.DEBUG_BUILD&&o.logger.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${(0,s.getEventDescription)(e)}`),!0}else{var n,i,u;if(n=e,i=t.ignoreErrors,i?.length&&(0,a.getPossibleEventMessages)(n).some(e=>(0,l.stringMatchesSomePattern)(e,i)))return r.DEBUG_BUILD&&o.logger.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${(0,s.getEventDescription)(e)}`),!0;if(u=e,u.exception?.values?.length&&!u.message&&!u.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value))return r.DEBUG_BUILD&&o.logger.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${(0,s.getEventDescription)(e)}`),!0;if(function(e,t){if(!t?.length)return!1;let n=c(e);return!!n&&(0,l.stringMatchesSomePattern)(n,t)}(e,t.denyUrls))return r.DEBUG_BUILD&&o.logger.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${(0,s.getEventDescription)(e)}.
Url: ${c(e)}`),!0;if(!function(e,t){if(!t?.length)return!0;let n=c(e);return!n||(0,l.stringMatchesSomePattern)(n,t)}(e,t.allowUrls))return r.DEBUG_BUILD&&o.logger.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${(0,s.getEventDescription)(e)}.
Url: ${c(e)}`),!0}return!1}(n,t)?n:null)}}),p=(0,i.defineIntegration)((e={})=>({...n(e),name:"InboundFilters"}));function u(e={},n={}){return{allowUrls:[...e.allowUrls||[],...n.allowUrls||[]],denyUrls:[...e.denyUrls||[],...n.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...n.ignoreErrors||[],...e.disableErrorDefaults?[]:t],ignoreTransactions:[...e.ignoreTransactions||[],...n.ignoreTransactions||[]]}}function c(e){try{let t=[...e.exception?.values??[]].reverse().find(e=>e.mechanism?.parent_id===void 0&&e.stacktrace?.frames?.length),n=t?.stacktrace?.frames;return n?function(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(t){return r.DEBUG_BUILD&&o.logger.error(`Cannot extract url for event ${(0,s.getEventDescription)(e)}`),null}}}},256502:e=>{"use strict";var{g:t,__dirname:n}=e;{let t;e.s({functionToStringIntegration:()=>o});var r=e.i(548729),i=e.i(171485),a=e.i(227325);let n=new WeakMap,o=(0,i.defineIntegration)(()=>({name:"FunctionToString",setupOnce(){t=Function.prototype.toString;try{Function.prototype.toString=function(...e){let i=(0,a.getOriginalFunction)(this),o=n.has((0,r.getClient)())&&void 0!==i?i:this;return t.apply(o,e)}}catch{}},setup(e){n.set(e,!0)}}))}},194371:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({applyAggregateErrorsToEvent:()=>i});var r=e.i(877130);function i(e,t,n,i,s,l){if(!s.exception?.values||!l||!(0,r.isInstanceOf)(l.originalException,Error))return;let u=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;u&&(s.exception.values=function e(t,n,i,s,l,u,c,p){if(u.length>=i+1)return u;let d=[...u];if((0,r.isInstanceOf)(s[l],Error)){a(c,p);let r=t(n,s[l]),u=d.length;o(r,l,u,p),d=e(t,n,i,s[l],l,[r,...d],r,u)}return Array.isArray(s.errors)&&s.errors.forEach((s,u)=>{if((0,r.isInstanceOf)(s,Error)){a(c,p);let r=t(n,s),_=d.length;o(r,`errors[${u}]`,_,p),d=e(t,n,i,s,l,[r,...d],r,_)}}),d}(e,t,i,l.originalException,n,s.exception.values,u,0))}function a(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function o(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}},602584:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({linkedErrorsIntegration:()=>t});var r=e.i(171485),i=e.i(194371),a=e.i(61640);let t=(0,r.defineIntegration)((e={})=>{let t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,r,o){let s=o.getOptions();(0,i.applyAggregateErrorsToEvent)(a.exceptionFromError,s.stackParser,n,t,e,r)}}})}},435406:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({addConsoleInstrumentationHandler:()=>s});var r=e.i(36650),i=e.i(227325),a=e.i(441619),o=e.i(836987);function s(e){let t="console";(0,o.addHandler)(t,e),(0,o.maybeInstrument)(t,l)}function l(){"console"in a.GLOBAL_OBJ&&r.CONSOLE_LEVELS.forEach(function(e){e in a.GLOBAL_OBJ.console&&(0,i.fill)(a.GLOBAL_OBJ.console,e,function(t){return r.originalConsoleMethods[e]=t,function(...t){(0,o.triggerHandlers)("console",{args:t,level:e});let n=r.originalConsoleMethods[e];n?.apply(a.GLOBAL_OBJ.console,t)}})})}},687713:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){return"warn"===e?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}e.s({severityLevelFromString:()=>r})},611198:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addConsoleBreadcrumb:()=>p,consoleIntegration:()=>t});var r=e.i(385205),i=e.i(548729),a=e.i(435406),o=e.i(171485),s=e.i(36650),l=e.i(687713),u=e.i(483213),c=e.i(441619);let t=(0,o.defineIntegration)((e={})=>{let t=new Set(e.levels||s.CONSOLE_LEVELS);return{name:"Console",setup(e){(0,a.addConsoleInstrumentationHandler)(({args:n,level:r})=>{(0,i.getClient)()===e&&t.has(r)&&p(r,n)})}}});function p(e,t){let n={category:"console",data:{arguments:t,logger:"console"},level:(0,l.severityLevelFromString)(e),message:d(t)};if("assert"===e)if(!1!==t[0])return;else{let e=t.slice(1);n.message=e.length>0?`Assertion failed: ${d(e)}`:"Assertion failed",n.data.arguments=e}(0,r.addBreadcrumb)(n,{input:t,level:e})}function d(e){return"util"in c.GLOBAL_OBJ&&"function"==typeof c.GLOBAL_OBJ.util.format?c.GLOBAL_OBJ.util.format(...e):(0,u.safeJoin)(e," ")}}},917449:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){let t={},n=0;for(;n<e.length;){let r=e.indexOf("=",n);if(-1===r)break;let i=e.indexOf(";",n);if(-1===i)i=e.length;else if(i<r){n=e.lastIndexOf(";",r-1)+1;continue}let a=e.slice(n,r).trim();if(void 0===t[a]){let n=e.slice(r+1,i).trim();34===n.charCodeAt(0)&&(n=n.slice(1,-1));try{t[a]=-1!==n.indexOf("%")?decodeURIComponent(n):n}catch(e){t[a]=n}}n=i+1}return t}e.s({parseCookie:()=>r})},129108:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({getClientIPAddress:()=>r,ipHeaderNames:()=>t});let t=["X-Client-IP","X-Forwarded-For","Fly-Client-IP","CF-Connecting-IP","Fastly-Client-Ip","True-Client-Ip","X-Real-IP","X-Cluster-Client-IP","X-Forwarded","Forwarded-For","Forwarded","X-Vercel-Forwarded-For"];function r(e){return t.map(t=>{let n=e[t],r=Array.isArray(n)?n.join(";"):n;return"Forwarded"===t?function(e){if(!e)return null;for(let t of e.split(";"))if(t.startsWith("for="))return t.slice(4);return null}(r):r?.split(",").map(e=>e.trim())}).reduce((e,t)=>t?e.concat(t):e,[]).find(e=>{var t;return null!==e&&(t=e,/(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-fA-F\d]{1,4}:){7}(?:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-fA-F\d]{1,4}|:)|(?:[a-fA-F\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,2}|:)|(?:[a-fA-F\d]{1,4}:){4}(?:(?::[a-fA-F\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,3}|:)|(?:[a-fA-F\d]{1,4}:){3}(?:(?::[a-fA-F\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,4}|:)|(?:[a-fA-F\d]{1,4}:){2}(?:(?::[a-fA-F\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,5}|:)|(?:[a-fA-F\d]{1,4}:){1}(?:(?::[a-fA-F\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,6}|:)|(?::(?:(?::[a-fA-F\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-fA-F\d]{1,4}){1,7}|:)))(?:%[0-9a-zA-Z]{1,})?$)/.test(t))})||null}}},382585:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({requestDataIntegration:()=>n});var r=e.i(171485),i=e.i(917449),a=e.i(129108);let t={cookies:!0,data:!0,headers:!0,query_string:!0,url:!0},n=(0,r.defineIntegration)((e={})=>{let n={...t,...e.include};return{name:"RequestData",processEvent(e,t,r){let{sdkProcessingMetadata:o={}}=e,{normalizedRequest:s,ipAddress:l}=o,u={...n,ip:n.ip??r.getOptions().sendDefaultPii};return s&&function(e,t,n,r){if(e.request={...e.request,...function(e,t){let n={},r={...e.headers};return t.headers&&(n.headers=r,t.cookies||delete r.cookie,t.ip||a.ipHeaderNames.forEach(e=>{delete r[e]})),n.method=e.method,t.url&&(n.url=e.url),t.cookies&&(n.cookies=e.cookies||(r?.cookie?(0,i.parseCookie)(r.cookie):void 0)||{}),t.query_string&&(n.query_string=e.query_string),t.data&&(n.data=e.data),n}(t,r)},r.ip){let r=t.headers&&(0,a.getClientIPAddress)(t.headers)||n.ipAddress;r&&(e.user={...e.user,ip_address:r})}}(e,s,{ipAddress:l},u),e}}})}},415172:e=>{"use strict";var{g:t,__dirname:n}=e;{function r(e,...t){let n=new String(String.raw(e,...t));return n.__sentry_template_string__=e.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),n.__sentry_template_values__=t,n}e.s({fmt:()=>t,parameterize:()=>r});let t=r}},808721:function(e){var{g:t,__dirname:n,m:r,e:i}=e;(()=>{"use strict";var e={491:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let r=n(223),i=n(172),a=n(930),o="context",s=new r.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,n,...r){return this._getContextManager().with(e,t,n,...r)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let r=n(56),i=n(912),a=n(957),o=n(172);class s{constructor(){function e(e){return function(...t){let n=(0,o.getGlobal)("diag");if(n)return n[e](...t)}}let t=this;t.setLogger=(e,n={logLevel:a.DiagLogLevel.INFO})=>{var r,s,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(r=e.stack)?r:e.message),!1}"number"==typeof n&&(n={logLevel:n});let u=(0,o.getGlobal)("diag"),c=(0,i.createLogLevelDiagLogger)(null!=(s=n.logLevel)?s:a.DiagLogLevel.INFO,e);if(u&&!n.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new r.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let r=n(660),i=n(172),a=n(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(o)||r.NOOP_METER_PROVIDER}getMeter(e,t,n){return this.getMeterProvider().getMeter(e,t,n)}disable(){(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let r=n(172),i=n(874),a=n(194),o=n(277),s=n(369),l=n(930),u="propagation",c=new i.NoopTextMapPropagator;class p{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new p),this._instance}setGlobalPropagator(e){return(0,r.registerGlobal)(u,e,l.DiagAPI.instance())}inject(e,t,n=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,n)}extract(e,t,n=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,n)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,r.unregisterGlobal)(u,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,r.getGlobal)(u)||c}}t.PropagationAPI=p},997:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let r=n(172),i=n(846),a=n(139),o=n(607),s=n(930),l="trace";class u{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,r.registerGlobal)(l,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,r.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,r.unregisterGlobal)(l,s.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let r=n(491),i=(0,n(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(r.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class n{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let r=new n(this._entries);return r._entries.set(e,t),r}removeEntry(e){let t=new n(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new n(this._entries);for(let n of e)t._entries.delete(n);return t}clear(){return new n}}t.BaggageImpl=n},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let r=n(930),i=n(993),a=n(830),o=r.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=n(491).ContextAPI.getInstance()},223:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let r=n(780);t.NoopContextManager=class{active(){return r.ROOT_CONTEXT}with(e,t,n,...r){return t.call(n,...r)}bind(e,t){return t}enable(){return this}disable(){return this}}},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class n{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,r)=>{let i=new n(t._currentContext);return i._currentContext.set(e,r),i},t.deleteValue=e=>{let r=new n(t._currentContext);return r._currentContext.delete(e),r}}}t.ROOT_CONTEXT=new n},506:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=n(930).DiagAPI.instance()},56:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let r=n(172);function i(e,t,n){let i=(0,r.getGlobal)("diag");if(i)return n.unshift(t),i[e](...n)}t.DiagComponentLogger=class{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return i("debug",this._namespace,e)}error(...e){return i("error",this._namespace,e)}info(...e){return i("info",this._namespace,e)}warn(...e){return i("warn",this._namespace,e)}verbose(...e){return i("verbose",this._namespace,e)}}},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let n=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];t.DiagConsoleLogger=class{constructor(){for(let e=0;e<n.length;e++)this[n[e].n]=function(e){return function(...t){if(console){let n=console[e];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,t)}}}(n[e].c)}}},912:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let r=n(957);t.createLogLevelDiagLogger=function(e,t){function n(n,r){let i=t[n];return"function"==typeof i&&e>=r?i.bind(t):function(){}}return e<r.DiagLogLevel.NONE?e=r.DiagLogLevel.NONE:e>r.DiagLogLevel.ALL&&(e=r.DiagLogLevel.ALL),t=t||{},{error:n("error",r.DiagLogLevel.ERROR),warn:n("warn",r.DiagLogLevel.WARN),info:n("info",r.DiagLogLevel.INFO),debug:n("debug",r.DiagLogLevel.DEBUG),verbose:n("verbose",r.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,(n=t.DiagLogLevel||(t.DiagLogLevel={}))[n.NONE=0]="NONE",n[n.ERROR=30]="ERROR",n[n.WARN=50]="WARN",n[n.INFO=60]="INFO",n[n.DEBUG=70]="DEBUG",n[n.VERBOSE=80]="VERBOSE",n[n.ALL=9999]="ALL"},172:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let r=n(200),i=n(521),a=n(130),o=i.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),l=r._globalThis;t.registerGlobal=function(e,t,n,r=!1){var a;let o=l[s]=null!=(a=l[s])?a:{version:i.VERSION};if(!r&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return n.error(t.stack||t.message),!1}if(o.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${i.VERSION}`);return n.error(t.stack||t.message),!1}return o[e]=t,n.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,n;let r=null==(t=l[s])?void 0:t.version;if(r&&(0,a.isCompatible)(r))return null==(n=l[s])?void 0:n[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let n=l[s];n&&delete n[e]}},130:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let r=n(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),n=new Set,r=e.match(i);if(!r)return()=>!1;let a={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=a.prerelease)return function(t){return t===e};function o(e){return n.add(e),!1}return function(e){if(t.has(e))return!0;if(n.has(e))return!1;let r=e.match(i);if(!r)return o(e);let s={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(null!=s.prerelease||a.major!==s.major)return o(e);if(0===a.major)return a.minor===s.minor&&a.patch<=s.patch?(t.add(e),!0):o(e);return a.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(r.VERSION)},886:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=n(653).MetricsAPI.getInstance()},901:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,(n=t.ValueType||(t.ValueType={}))[n.INT=0]="INT",n[n.DOUBLE=1]="DOUBLE"},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class n{constructor(){}createHistogram(e,n){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,n){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,n){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,n){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,n){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,n){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=n;class r{}t.NoopMetric=r;class i extends r{add(e,t){}}t.NoopCounterMetric=i;class a extends r{add(e,t){}}t.NoopUpDownCounterMetric=a;class o extends r{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class l extends s{}t.NoopObservableCounterMetric=l;class u extends s{}t.NoopObservableGaugeMetric=u;class c extends s{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new n,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let r=n(102);class i{getMeter(e,t,n){return r.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(46),t)},651:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n._globalThis=void 0,n._globalThis="object"==typeof globalThis?globalThis:t},46:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(651),t)},939:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=n(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0,t.NoopTextMapPropagator=class{inject(e,t){}extract(e,t){return e}fields(){return[]}}},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,n){null!=e&&(e[t]=n)}}},845:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=n(997).TraceAPI.getInstance()},403:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let r=n(476);t.NonRecordingSpan=class{constructor(e=r.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}},614:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let r=n(491),i=n(607),a=n(403),o=n(139),s=r.ContextAPI.getInstance();t.NoopTracer=class{startSpan(e,t,n=s.active()){var r;if(null==t?void 0:t.root)return new a.NonRecordingSpan;let l=n&&(0,i.getSpanContext)(n);return"object"==typeof(r=l)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&(0,o.isSpanContextValid)(l)?new a.NonRecordingSpan(l):new a.NonRecordingSpan}startActiveSpan(e,t,n,r){let a,o,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(a=t,l=n):(a=t,o=n,l=r);let u=null!=o?o:s.active(),c=this.startSpan(e,a,u),p=(0,i.setSpan)(u,c);return s.with(p,l,void 0,c)}}},124:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let r=n(614);t.NoopTracerProvider=class{getTracer(e,t,n){return new r.NoopTracer}}},125:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let r=new(n(614)).NoopTracer;t.ProxyTracer=class{constructor(e,t,n,r){this._provider=e,this.name=t,this.version=n,this.options=r}startSpan(e,t,n){return this._getTracer().startSpan(e,t,n)}startActiveSpan(e,t,n,r){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):r}}},846:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let r=n(125),i=new(n(124)).NoopTracerProvider;t.ProxyTracerProvider=class{getTracer(e,t,n){var i;return null!=(i=this.getDelegateTracer(e,t,n))?i:new r.ProxyTracer(this,e,t,n)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,n){var r;return null==(r=this._delegate)?void 0:r.getTracer(e,t,n)}}},996:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,(n=t.SamplingDecision||(t.SamplingDecision={}))[n.NOT_RECORD=0]="NOT_RECORD",n[n.RECORD=1]="RECORD",n[n.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"},607:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let r=n(780),i=n(403),a=n(491),o=(0,r.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function l(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(a.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let r=n(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let n=this._clone();return n._internalState.has(e)&&n._internalState.delete(e),n._internalState.set(e,t),n}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let n=t.trim(),i=n.indexOf("=");if(-1!==i){let a=n.slice(0,i),o=n.slice(i+1,t.length);(0,r.validateKey)(a)&&(0,r.validateValue)(o)&&e.set(a,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let n="[_0-9a-z-*/]",r=`[a-z]${n}{0,255}`,i=`[a-z0-9]${n}{0,240}@[a-z]${n}{0,13}`,a=RegExp(`^(?:${r}|${i})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let r=n(325);t.createTraceState=function(e){return new r.TraceStateImpl(e)}},476:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let r=n(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:r.TraceFlags.NONE}},357:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,(n=t.SpanKind||(t.SpanKind={}))[n.INTERNAL=0]="INTERNAL",n[n.SERVER=1]="SERVER",n[n.CLIENT=2]="CLIENT",n[n.PRODUCER=3]="PRODUCER",n[n.CONSUMER=4]="CONSUMER"},139:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let r=n(476),i=n(403),a=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return a.test(e)&&e!==r.INVALID_TRACEID}function l(e){return o.test(e)&&e!==r.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=l,t.isSpanContextValid=function(e){return s(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,(n=t.SpanStatusCode||(t.SpanStatusCode={}))[n.UNSET=0]="UNSET",n[n.OK=1]="OK",n[n.ERROR=2]="ERROR"},475:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,(n=t.TraceFlags||(t.TraceFlags={}))[n.NONE=0]="NONE",n[n.SAMPLED=1]="SAMPLED"},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function a(t){var n=i[t];if(void 0!==n)return n.exports;var r=i[t]={exports:{}},o=!0;try{e[t].call(r.exports,r,r.exports,a),o=!1}finally{o&&delete i[t]}return r.exports}a.ab=n+"/";var o={};(()=>{Object.defineProperty(o,"__esModule",{value:!0}),o.trace=o.propagation=o.metrics=o.diag=o.context=o.INVALID_SPAN_CONTEXT=o.INVALID_TRACEID=o.INVALID_SPANID=o.isValidSpanId=o.isValidTraceId=o.isSpanContextValid=o.createTraceState=o.TraceFlags=o.SpanStatusCode=o.SpanKind=o.SamplingDecision=o.ProxyTracerProvider=o.ProxyTracer=o.defaultTextMapSetter=o.defaultTextMapGetter=o.ValueType=o.createNoopMeter=o.DiagLogLevel=o.DiagConsoleLogger=o.ROOT_CONTEXT=o.createContextKey=o.baggageEntryMetadataFromString=void 0;var e=a(369);Object.defineProperty(o,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=a(780);Object.defineProperty(o,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(o,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var n=a(972);Object.defineProperty(o,"DiagConsoleLogger",{enumerable:!0,get:function(){return n.DiagConsoleLogger}});var r=a(957);Object.defineProperty(o,"DiagLogLevel",{enumerable:!0,get:function(){return r.DiagLogLevel}});var i=a(102);Object.defineProperty(o,"createNoopMeter",{enumerable:!0,get:function(){return i.createNoopMeter}});var s=a(901);Object.defineProperty(o,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var l=a(194);Object.defineProperty(o,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(o,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var u=a(125);Object.defineProperty(o,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var c=a(846);Object.defineProperty(o,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var p=a(996);Object.defineProperty(o,"SamplingDecision",{enumerable:!0,get:function(){return p.SamplingDecision}});var d=a(357);Object.defineProperty(o,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var _=a(847);Object.defineProperty(o,"SpanStatusCode",{enumerable:!0,get:function(){return _.SpanStatusCode}});var g=a(475);Object.defineProperty(o,"TraceFlags",{enumerable:!0,get:function(){return g.TraceFlags}});var f=a(98);Object.defineProperty(o,"createTraceState",{enumerable:!0,get:function(){return f.createTraceState}});var h=a(139);Object.defineProperty(o,"isSpanContextValid",{enumerable:!0,get:function(){return h.isSpanContextValid}}),Object.defineProperty(o,"isValidTraceId",{enumerable:!0,get:function(){return h.isValidTraceId}}),Object.defineProperty(o,"isValidSpanId",{enumerable:!0,get:function(){return h.isValidSpanId}});var E=a(476);Object.defineProperty(o,"INVALID_SPANID",{enumerable:!0,get:function(){return E.INVALID_SPANID}}),Object.defineProperty(o,"INVALID_TRACEID",{enumerable:!0,get:function(){return E.INVALID_TRACEID}}),Object.defineProperty(o,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return E.INVALID_SPAN_CONTEXT}});let T=a(67);Object.defineProperty(o,"context",{enumerable:!0,get:function(){return T.context}});let S=a(506);Object.defineProperty(o,"diag",{enumerable:!0,get:function(){return S.diag}});let m=a(886);Object.defineProperty(o,"metrics",{enumerable:!0,get:function(){return m.metrics}});let v=a(939);Object.defineProperty(o,"propagation",{enumerable:!0,get:function(){return v.propagation}});let O=a(845);Object.defineProperty(o,"trace",{enumerable:!0,get:function(){return O.trace}}),o.default={context:T.context,diag:S.diag,metrics:m.metrics,propagation:v.propagation,trace:O.trace}})(),r.exports=o})()},216050:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({VercelEdgeClient:()=>t,getDefaultIntegrations:()=>nn,init:()=>nr,logger:()=>nF,winterCGFetchIntegration:()=>nM});var r,i,a,o,s,l,u,c=e.i(580767),p=e.i(938476),d=e.i(562572),_=e.i(230098),g=e.i(526321),f=e.i(325939),h=e.i(988191),E=e.i(872150),T=e.i(548729),S=e.i(673189),m=e.i(257562),v=e.i(219422),O=e.i(477984),y=e.i(678436),I=e.i(895818),R=e.i(36650),A=e.i(372618),b=e.i(20992),L=e.i(173666),C=e.i(421820),P=e.i(610520),N=e.i(917),M=e.i(609849),D=e.i(483213),x=e.i(227325),U=e.i(883270),w=e.i(400230),B=e.i(171485),F=e.i(871401),k=e.i(12822),j=e.i(143517),G=e.i(385205),V=e.i(482545),$=e.i(68401),H=e.i(545222),Y=e.i(77599),X=e.i(441619),z=e.i(732762),K=e.i(750080),J=e.i(506407),W=e.i(15570),q=e.i(256502),Q=e.i(602584),Z=e.i(611198),ee=e.i(382585),et=e.i(4353),en=e.i(415172),er=e.i(808721);void 0===globalThis.performance&&(globalThis.performance={timeOrigin:0,now:()=>Date.now()});class t extends p.ServerRuntimeClient{constructor(e){(0,d.applySdkMetadata)(e,"vercel-edge"),e._metadata=e._metadata||{},super({...e,platform:"javascript",runtime:{name:"vercel-edge"},serverName:e.serverName||process.env.SENTRY_NAME})}async flush(e){let t=this.traceProvider,n=t?.activeSpanProcessor;return n&&await n.forceFlush(),this.getOptions().sendClientReports&&this._flushOutcomes(),super.flush(e)}}var ei="telemetry.sdk.name",ea="telemetry.sdk.language",eo="telemetry.sdk.version",es=(0,er.createContextKey)("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function el(e){return e.setValue(es,!0)}function eu(e){return!0===e.getValue(es)}var ec="baggage",ep=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},ed=function(){function e(){}return e.prototype.inject=function(e,t,n){var r=er.propagation.getBaggage(e);if(!(!r||eu(e))){var i=r.getAllEntries().map(function(e){var t=ep(e,2),n=t[0],r=t[1],i=encodeURIComponent(n)+"="+encodeURIComponent(r.value);return void 0!==r.metadata&&(i+=";"+r.metadata.toString()),i}).filter(function(e){return e.length<=4096}).slice(0,180).reduce(function(e,t){var n=""+e+(""!==e?",":"")+t;return n.length>8192?e:n},"");i.length>0&&n.set(t,ec,i)}},e.prototype.extract=function(e,t,n){var r=n.get(t,ec),i=Array.isArray(r)?r.join(","):r;if(!i)return e;var a={};return 0===i.length||(i.split(",").forEach(function(e){var t=function(e){var t,n=e.split(";");if(!(n.length<=0)){var r=n.shift();if(r){var i=r.indexOf("=");if(!(i<=0)){var a=decodeURIComponent(r.substring(0,i).trim()),o=decodeURIComponent(r.substring(i+1).trim());return n.length>0&&(t=(0,er.baggageEntryMetadataFromString)(n.join(";"))),{key:a,value:o,metadata:t}}}}}(e);if(t){var n={value:t.value};t.metadata&&(n.metadata=t.metadata),a[t.key]=n}}),0===Object.entries(a).length)?e:er.propagation.setBaggage(e,er.propagation.createBaggage(a))},e.prototype.fields=function(){return[ec]},e}(),e_=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},eg=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o};function ef(e){var t,n,r={};if("object"!=typeof e||null==e)return r;try{for(var i=e_(Object.entries(e)),a=i.next();!a.done;a=i.next()){var o,s=eg(a.value,2),l=s[0],u=s[1];if(o=l,"string"!=typeof o||!(o.length>0)){er.diag.warn("Invalid attribute key: "+l);continue}if(!eh(u)){er.diag.warn("Invalid attribute value set for key: "+l);continue}Array.isArray(u)?r[l]=u.slice():r[l]=u}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return r}function eh(e){return null==e||(Array.isArray(e)?function(e){try{for(var t,n,r,i=e_(e),a=i.next();!a.done;a=i.next()){var o=a.value;if(null!=o){if(!r){if(eE(o)){r=typeof o;continue}return!1}if(typeof o!==r)return!1}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return!0}(e):eE(e))}function eE(e){switch(typeof e){case"number":case"boolean":case"string":return!0}return!1}var eT=function(e){var t;er.diag.error("string"==typeof(t=e)?t:JSON.stringify(function(e){for(var t={},n=e;null!==n;)Object.getOwnPropertyNames(n).forEach(function(e){if(!t[e]){var r=n[e];r&&(t[e]=String(r))}}),n=Object.getPrototypeOf(n);return t}(t)))};function eS(e){try{eT(e)}catch(e){}}!function(e){e.AlwaysOff="always_off",e.AlwaysOn="always_on",e.ParentBasedAlwaysOff="parentbased_always_off",e.ParentBasedAlwaysOn="parentbased_always_on",e.ParentBasedTraceIdRatio="parentbased_traceidratio",e.TraceIdRatio="traceidratio"}(a||(a={}));var em=["OTEL_SDK_DISABLED"],ev=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"],eO=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"],ey=1/0,eI={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:er.DiagLogLevel.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:ey,OTEL_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:ey,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:128,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:ey,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:128,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:a.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]},eR={ALL:er.DiagLogLevel.ALL,VERBOSE:er.DiagLogLevel.VERBOSE,DEBUG:er.DiagLogLevel.DEBUG,INFO:er.DiagLogLevel.INFO,WARN:er.DiagLogLevel.WARN,ERROR:er.DiagLogLevel.ERROR,NONE:er.DiagLogLevel.NONE};function eA(e){var t={};for(var n in eI)if("OTEL_LOG_LEVEL"===n)!function(e,t,n){var r=n[e];if("string"==typeof r){var i=eR[r.toUpperCase()];null!=i&&(t[e]=i)}}(n,t,e);else if(em.indexOf(n)>-1){if(void 0!==e[n]){var r=String(e[n]);t[n]="true"===r.toLowerCase()}}else if(ev.indexOf(n)>-1)!function(e,t,n,r,i){if(void 0===r&&(r=-1/0),void 0===i&&(i=1/0),void 0!==n[e]){var a=Number(n[e]);isNaN(a)||(a<r?t[e]=r:a>i?t[e]=i:t[e]=a)}}(n,t,e);else if(eO.indexOf(n)>-1)!function(e,t,n,r){void 0===r&&(r=",");var i=n[e];"string"==typeof i&&(t[e]=i.split(r).map(function(e){return e.trim()}))}(n,t,e);else{var i=e[n];null!=i&&(t[n]=String(i))}return t}function eb(){return Object.assign({},eI,eA(process.env))}var eL={timeOrigin:0,now:()=>Date.now()},eC=((o={})["telemetry.sdk.name"]="opentelemetry",o["process.runtime.name"]="node",o["telemetry.sdk.language"]="nodejs",o["telemetry.sdk.version"]="1.30.1",o);function eP(e){return[Math.trunc(e/1e3),Math.round(e%1e3*1e6)]}function eN(){return eL.timeOrigin}function eM(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function eD(e){return eM(e)||"number"==typeof e||e instanceof Date}function ex(e,t){var n=[e[0]+t[0],e[1]+t[1]];return n[1]>=1e9&&(n[1]-=1e9,n[0]+=1),n}!function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILED=1]="FAILED"}(s||(s={}));var eU=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ew=function(){function e(e){var t;void 0===e&&(e={}),this._propagators=null!=(t=e.propagators)?t:[],this._fields=Array.from(new Set(this._propagators.map(function(e){return"function"==typeof e.fields?e.fields():[]}).reduce(function(e,t){return e.concat(t)},[])))}return e.prototype.inject=function(e,t,n){var r,i;try{for(var a=eU(this._propagators),o=a.next();!o.done;o=a.next()){var s=o.value;try{s.inject(e,t,n)}catch(e){er.diag.warn("Failed to inject with "+s.constructor.name+". Err: "+e.message)}}}catch(e){r={error:e}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(r)throw r.error}}},e.prototype.extract=function(e,t,n){return this._propagators.reduce(function(e,r){try{return r.extract(e,t,n)}catch(e){er.diag.warn("Failed to extract with "+r.constructor.name+". Err: "+e.message)}return e},e)},e.prototype.fields=function(){return this._fields.slice()},e}(),eB="[_0-9a-z-*/]",eF=RegExp("^(?:[a-z]"+eB+"{0,255}|"+("[a-z0-9]"+eB+"{0,240}@[a-z]")+eB+"{0,13})$"),ek=/^[ -~]{0,255}[!-~]$/,ej=/,|=/,eG=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var n=this._clone();return n._internalState.has(e)&&n._internalState.delete(e),n._internalState.set(e,t),n},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,n){return t.push(n+"="+e.get(n)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var n=t.trim(),r=n.indexOf("=");if(-1!==r){var i=n.slice(0,r),a=n.slice(r+1,t.length);eF.test(i)&&ek.test(a)&&!ej.test(a)&&e.set(i,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}(),eV="traceparent",e$="tracestate",eH=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$"),eY=function(){function e(){}return e.prototype.inject=function(e,t,n){var r=er.trace.getSpanContext(e);if(!(!r||eu(e))&&(0,er.isSpanContextValid)(r)){var i="00-"+r.traceId+"-"+r.spanId+"-0"+Number(r.traceFlags||er.TraceFlags.NONE).toString(16);n.set(t,eV,i),r.traceState&&n.set(t,e$,r.traceState.serialize())}},e.prototype.extract=function(e,t,n){var r,i=n.get(t,eV);if(!i)return e;var a=Array.isArray(i)?i[0]:i;if("string"!=typeof a)return e;var o=(r=eH.exec(a))&&("00"!==r[1]||!r[5])?{traceId:r[2],spanId:r[3],traceFlags:parseInt(r[4],16)}:null;if(!o)return e;o.isRemote=!0;var s=n.get(t,e$);if(s){var l=Array.isArray(s)?s.join(","):s;o.traceState=new eG("string"==typeof l?l:void 0)}return er.trace.setSpanContext(e,o)},e.prototype.fields=function(){return[eV,e$]},e}(),eX=Function.prototype.toString,ez=eX.call(Object),eK=(r=Object.getPrototypeOf,i=Object,function(e){return r(i(e))}),eJ=Object.prototype,eW=eJ.hasOwnProperty,eq=Symbol?Symbol.toStringTag:void 0,eQ=eJ.toString;function eZ(e){if(null==(t=e)||"object"!=typeof t||"[object Object]"!==(null==(n=e)?void 0===n?"[object Undefined]":"[object Null]":eq&&eq in Object(n)?function(e){var t=eW.call(e,eq),n=e[eq],r=!1;try{e[eq]=void 0,r=!0}catch(e){}var i=eQ.call(e);return r&&(t?e[eq]=n:delete e[eq]),i}(n):(r=n,eQ.call(r))))return!1;var t,n,r,i=eK(e);if(null===i)return!0;var a=eW.call(i,"constructor")&&i.constructor;return"function"==typeof a&&a instanceof a&&eX.call(a)===ez}function e0(e){return e3(e)?e.slice():e}function e1(e,t,n){for(var r=n.get(e[t])||[],i=0,a=r.length;i<a;i++){var o=r[i];if(o.key===t&&o.obj===e)return!0}return!1}function e3(e){return Array.isArray(e)}function e2(e){return"function"==typeof e}function e5(e){return!e4(e)&&!e3(e)&&!e2(e)&&"object"==typeof e}function e4(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||void 0===e||e instanceof Date||e instanceof RegExp||null===e}var e6=function(){function e(){var e=this;this._promise=new Promise(function(t,n){e._resolve=t,e._reject=n})}return Object.defineProperty(e.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),e.prototype.resolve=function(e){this._resolve(e)},e.prototype.reject=function(e){this._reject(e)},e}(),e7=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},e8=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},e9=function(){function e(e,t){this._callback=e,this._that=t,this._isCalled=!1,this._deferred=new e6}return Object.defineProperty(e.prototype,"isCalled",{get:function(){return this._isCalled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"promise",{get:function(){return this._deferred.promise},enumerable:!1,configurable:!0}),e.prototype.call=function(){for(var e,t=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!this._isCalled){this._isCalled=!0;try{Promise.resolve((e=this._callback).call.apply(e,e8([this._that],e7(n),!1))).then(function(e){return t._deferred.resolve(e)},function(e){return t._deferred.reject(e)})}catch(e){this._deferred.reject(e)}}return this._deferred.promise},e}(),te=globalThis&&globalThis.__assign||function(){return(te=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},tt=globalThis&&globalThis.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,a){function o(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}l((r=r.apply(e,t||[])).next())})},tn=globalThis&&globalThis.__generator||function(e,t){var n,r,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){var l=[a,s];if(n)throw TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===l[0]||2===l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=t.call(e,o)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}},tr=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},ti=function(){function e(e,t){var n,r=this;this._attributes=e,this.asyncAttributesPending=null!=t,this._syncAttributes=null!=(n=this._attributes)?n:{},this._asyncAttributesPromise=null==t?void 0:t.then(function(e){return r._attributes=Object.assign({},r._attributes,e),r.asyncAttributesPending=!1,e},function(e){return er.diag.debug("a resource's async attributes promise rejected: %s",e),r.asyncAttributesPending=!1,{}})}return e.empty=function(){return e.EMPTY},e.default=function(){var t;return new e(((t={})["service.name"]="unknown_service:",t[ea]=eC[ea],t[ei]=eC[ei],t[eo]=eC[eo],t))},Object.defineProperty(e.prototype,"attributes",{get:function(){var e;return this.asyncAttributesPending&&er.diag.error("Accessing resource attributes before async attributes settled"),null!=(e=this._attributes)?e:{}},enumerable:!1,configurable:!0}),e.prototype.waitForAsyncAttributes=function(){return tt(this,void 0,void 0,function(){return tn(this,function(e){switch(e.label){case 0:if(!this.asyncAttributesPending)return[3,2];return[4,this._asyncAttributesPromise];case 1:e.sent(),e.label=2;case 2:return[2]}})})},e.prototype.merge=function(t){var n,r=this;if(!t)return this;var i=te(te({},this._syncAttributes),null!=(n=t._syncAttributes)?n:t.attributes);return this._asyncAttributesPromise||t._asyncAttributesPromise?new e(i,Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then(function(e){var n,i=tr(e,2),a=i[0],o=i[1];return te(te(te(te({},r._syncAttributes),a),null!=(n=t._syncAttributes)?n:t.attributes),o)})):new e(i)},e.EMPTY=new e({}),e}(),ta="exception.type",to="exception.message",ts=globalThis&&globalThis.__assign||function(){return(ts=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},tl=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},tu=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},tc=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},tp=function(){function e(e,t,n,r,i,a,o,s,l,u){void 0===o&&(o=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:er.SpanStatusCode.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=r,this.parentSpanId=a,this.kind=i,this.links=o;var c=Date.now();this._performanceStartTime=eL.now(),this._performanceOffset=c-(this._performanceStartTime+eN()),this._startTimeProvided=null!=s,this.startTime=this._getTime(null!=s?s:c),this.resource=e.resource,this.instrumentationLibrary=e.instrumentationLibrary,this._spanLimits=e.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,null!=u&&this.setAttributes(u),this._spanProcessor=e.getActiveSpanProcessor(),this._spanProcessor.onStart(this,t)}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return null==t||this._isSpanEnded()||(0===e.length?er.diag.warn("Invalid attribute key: "+e):eh(t)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,e)?this._droppedAttributesCount++:this.attributes[e]=this._truncateToSize(t):er.diag.warn("Invalid attribute value set for key: "+e)),this},e.prototype.setAttributes=function(e){var t,n;try{for(var r=tl(Object.entries(e)),i=r.next();!i.done;i=r.next()){var a=tu(i.value,2),o=a[0],s=a[1];this.setAttribute(o,s)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return this},e.prototype.addEvent=function(e,t,n){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return er.diag.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(0===this._droppedEventsCount&&er.diag.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),eD(t)&&(eD(n)||(n=t),t=void 0);var r=ef(t);return this.events.push({name:e,attributes:r,time:this._getTime(n),droppedAttributesCount:0}),this},e.prototype.addLink=function(e){return this.links.push(e),this},e.prototype.addLinks=function(e){var t;return(t=this.links).push.apply(t,tc([],tu(e),!1)),this},e.prototype.setStatus=function(e){return this._isSpanEnded()||(this.status=ts({},e),null!=this.status.message&&"string"!=typeof e.message&&(er.diag.warn("Dropping invalid status.message of type '"+typeof e.message+"', expected 'string'"),delete this.status.message)),this},e.prototype.updateName=function(e){return this._isSpanEnded()||(this.name=e),this},e.prototype.end=function(e){var t,n,r,i;if(this._isSpanEnded())return void er.diag.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once.");this._ended=!0,this.endTime=this._getTime(e),this._duration=(t=this.startTime,r=(n=this.endTime)[0]-t[0],(i=n[1]-t[1])<0&&(r-=1,i+=1e9),[r,i]),this._duration[0]<0&&(er.diag.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&er.diag.warn("Dropped "+this._droppedEventsCount+" events because eventCountLimit reached"),this._spanProcessor.onEnd(this)},e.prototype._getTime=function(e){if("number"==typeof e&&e<=eL.now()){var t;return t=e+this._performanceOffset,ex(eP(eN()),eP("number"==typeof t?t:eL.now()))}if("number"==typeof e)return eP(e);if(e instanceof Date)return eP(e.getTime());if(eM(e))return e;if(this._startTimeProvided)return eP(Date.now());var n=eL.now()-this._performanceStartTime;return ex(this.startTime,eP(n))},e.prototype.isRecording=function(){return!1===this._ended},e.prototype.recordException=function(e,t){var n={};"string"==typeof e?n[to]=e:e&&(e.code?n[ta]=e.code.toString():e.name&&(n[ta]=e.name),e.message&&(n[to]=e.message),e.stack&&(n["exception.stacktrace"]=e.stack)),n[ta]||n[to]?this.addEvent("exception",n,t):er.diag.warn("Failed to record an exception "+e)},Object.defineProperty(e.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),e.prototype._isSpanEnded=function(){return this._ended&&er.diag.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},e.prototype._truncateToLimitUtil=function(e,t){return e.length<=t?e:e.substring(0,t)},e.prototype._truncateToSize=function(e){var t=this,n=this._attributeValueLengthLimit;return n<=0?(er.diag.warn("Attribute value limit must be positive, got "+n),e):"string"==typeof e?this._truncateToLimitUtil(e,n):Array.isArray(e)?e.map(function(e){return"string"==typeof e?t._truncateToLimitUtil(e,n):e}):e},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(l||(l={}));var td=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:l.NOT_RECORD}},e.prototype.toString=function(){return"AlwaysOffSampler"},e}(),t_=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:l.RECORD_AND_SAMPLED}},e.prototype.toString=function(){return"AlwaysOnSampler"},e}(),tg=function(){function e(e){var t,n,r,i;this._root=e.root,this._root||(eS(Error("ParentBasedSampler must have a root sampler configured")),this._root=new t_),this._remoteParentSampled=null!=(t=e.remoteParentSampled)?t:new t_,this._remoteParentNotSampled=null!=(n=e.remoteParentNotSampled)?n:new td,this._localParentSampled=null!=(r=e.localParentSampled)?r:new t_,this._localParentNotSampled=null!=(i=e.localParentNotSampled)?i:new td}return e.prototype.shouldSample=function(e,t,n,r,i,a){var o=er.trace.getSpanContext(e);return o&&(0,er.isSpanContextValid)(o)?o.isRemote?o.traceFlags&er.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(e,t,n,r,i,a):this._remoteParentNotSampled.shouldSample(e,t,n,r,i,a):o.traceFlags&er.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(e,t,n,r,i,a):this._localParentNotSampled.shouldSample(e,t,n,r,i,a):this._root.shouldSample(e,t,n,r,i,a)},e.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},e}(),tf=function(){function e(e){void 0===e&&(e=0),this._ratio=e,this._ratio=this._normalize(e),this._upperBound=Math.floor(0xffffffff*this._ratio)}return e.prototype.shouldSample=function(e,t){return{decision:(0,er.isValidTraceId)(t)&&this._accumulate(t)<this._upperBound?l.RECORD_AND_SAMPLED:l.NOT_RECORD}},e.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},e.prototype._normalize=function(e){return"number"!=typeof e||isNaN(e)?0:e>=1?1:e<=0?0:e},e.prototype._accumulate=function(e){for(var t=0,n=0;n<e.length/8;n++){var r=8*n;t=(t^parseInt(e.slice(r,r+8),16))>>>0}return t},e}(),th=a.AlwaysOn;function tE(){var e=eb();return{sampler:tT(e),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:e.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:e.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:e.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:e.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function tT(e){switch(void 0===e&&(e=eb()),e.OTEL_TRACES_SAMPLER){case a.AlwaysOn:return new t_;case a.AlwaysOff:return new td;case a.ParentBasedAlwaysOn:return new tg({root:new t_});case a.ParentBasedAlwaysOff:return new tg({root:new td});case a.TraceIdRatio:return new tf(tS(e));case a.ParentBasedTraceIdRatio:return new tg({root:new tf(tS(e))});default:return er.diag.error('OTEL_TRACES_SAMPLER value "'+e.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+th+'".'),new t_}}function tS(e){if(void 0===e.OTEL_TRACES_SAMPLER_ARG||""===e.OTEL_TRACES_SAMPLER_ARG)return er.diag.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(er.diag.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):t<0||t>1?(er.diag.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):t}var tm=function(){function e(e,t){this._exporter=e,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var n=eb();this._maxExportBatchSize="number"==typeof(null==t?void 0:t.maxExportBatchSize)?t.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==t?void 0:t.maxQueueSize)?t.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==t?void 0:t.scheduledDelayMillis)?t.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==t?void 0:t.exportTimeoutMillis)?t.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new e9(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(er.diag.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return e.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){this._shutdownOnce.isCalled||(e.spanContext().traceFlags&er.TraceFlags.SAMPLED)!=0&&this._addToBuffer(e)},e.prototype.shutdown=function(){return this._shutdownOnce.call()},e.prototype._shutdown=function(){var e=this;return Promise.resolve().then(function(){return e.onShutdown()}).then(function(){return e._flushAll()}).then(function(){return e._exporter.shutdown()})},e.prototype._addToBuffer=function(e){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&er.diag.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(er.diag.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(e),this._maybeStartTimer()},e.prototype._flushAll=function(){var e=this;return new Promise(function(t,n){for(var r=[],i=Math.ceil(e._finishedSpans.length/e._maxExportBatchSize),a=0;a<i;a++)r.push(e._flushOneBatch());Promise.all(r).then(function(){t()}).catch(n)})},e.prototype._flushOneBatch=function(){var e=this;return(this._clearTimer(),0===this._finishedSpans.length)?Promise.resolve():new Promise(function(t,n){var r=setTimeout(function(){n(Error("Timeout"))},e._exportTimeoutMillis);er.context.with(el(er.context.active()),function(){e._finishedSpans.length<=e._maxExportBatchSize?(i=e._finishedSpans,e._finishedSpans=[]):i=e._finishedSpans.splice(0,e._maxExportBatchSize);for(var i,a=function(){return e._exporter.export(i,function(e){var i;clearTimeout(r),e.code===s.SUCCESS?t():n(null!=(i=e.error)?i:Error("BatchSpanProcessor: span export failed"))})},o=null,l=0,u=i.length;l<u;l++){var c=i[l];c.resource.asyncAttributesPending&&c.resource.waitForAsyncAttributes&&(null!=o||(o=[]),o.push(c.resource.waitForAsyncAttributes()))}null===o?a():Promise.all(o).then(a,function(e){eS(e),n(e)})})})},e.prototype._maybeStartTimer=function(){var e=this;if(!this._isExporting){var t=function(){e._isExporting=!0,e._flushOneBatch().finally(function(){e._isExporting=!1,e._finishedSpans.length>0&&(e._clearTimer(),e._maybeStartTimer())}).catch(function(t){e._isExporting=!1,eS(t)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return t();void 0===this._timer&&(this._timer=setTimeout(function(){return t()},this._scheduledDelayMillis),this._timer.unref())}},e.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},e}(),tv=globalThis&&globalThis.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),tO=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return tv(t,e),t.prototype.onShutdown=function(){},t}(tm),ty=function(){this.generateTraceId=tR(16),this.generateSpanId=tR(8)},tI=c.Buffer.allocUnsafe(16);function tR(e){return function(){for(var t=0;t<e/4;t++)tI.writeUInt32BE(0x100000000*Math.random()>>>0,4*t);for(var t=0;t<e;t++)if(tI[t]>0)break;else t===e-1&&(tI[e-1]=1);return tI.toString("hex",0,e)}}var tA=function(){function e(e,t,n){this._tracerProvider=n;var r,i,a,o=(r={sampler:tT()},(a=Object.assign({},i=tE(),r,t)).generalLimits=Object.assign({},i.generalLimits,t.generalLimits||{}),a.spanLimits=Object.assign({},i.spanLimits,t.spanLimits||{}),a);this._sampler=o.sampler,this._generalLimits=o.generalLimits,this._spanLimits=o.spanLimits,this._idGenerator=t.idGenerator||new ty,this.resource=n.resource,this.instrumentationLibrary=e}return e.prototype.startSpan=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n=er.context.active()),t.root&&(n=er.trace.deleteSpan(n));var r,i,a,o,s,l,u=er.trace.getSpan(n);if(eu(n)){er.diag.debug("Instrumentation suppressed, returning Noop Span");var c=er.trace.wrapSpanContext(er.INVALID_SPAN_CONTEXT);return c}var p=null==u?void 0:u.spanContext(),d=this._idGenerator.generateSpanId();p&&er.trace.isSpanContextValid(p)?(o=p.traceId,s=p.traceState,l=p.spanId):o=this._idGenerator.generateTraceId();var _=null!=(r=t.kind)?r:er.SpanKind.INTERNAL,g=(null!=(i=t.links)?i:[]).map(function(e){return{context:e.context,attributes:ef(e.attributes)}}),f=ef(t.attributes),h=this._sampler.shouldSample(n,o,e,_,f,g);s=null!=(a=h.traceState)?a:s;var E={traceId:o,spanId:d,traceFlags:h.decision===er.SamplingDecision.RECORD_AND_SAMPLED?er.TraceFlags.SAMPLED:er.TraceFlags.NONE,traceState:s};if(h.decision===er.SamplingDecision.NOT_RECORD){er.diag.debug("Recording is off, propagating context in a non-recording span");var c=er.trace.wrapSpanContext(E);return c}var T=ef(Object.assign(f,h.attributes));return new tp(this,n,e,E,_,l,g,t.startTime,void 0,T)},e.prototype.startActiveSpan=function(e,t,n,r){if(!(arguments.length<2)){2==arguments.length?o=t:3==arguments.length?(i=t,o=n):(i=t,a=n,o=r);var i,a,o,s=null!=a?a:er.context.active(),l=this.startSpan(e,i,s),u=er.trace.setSpan(s,l);return er.context.with(u,o,void 0,l)}},e.prototype.getGeneralLimits=function(){return this._generalLimits},e.prototype.getSpanLimits=function(){return this._spanLimits},e.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},e}(),tb=globalThis&&globalThis.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},tL=function(){function e(e){this._spanProcessors=e}return e.prototype.forceFlush=function(){var e,t,n=[];try{for(var r=tb(this._spanProcessors),i=r.next();!i.done;i=r.next()){var a=i.value;n.push(a.forceFlush())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return new Promise(function(e){Promise.all(n).then(function(){e()}).catch(function(t){eS(t||Error("MultiSpanProcessor: forceFlush failed")),e()})})},e.prototype.onStart=function(e,t){var n,r;try{for(var i=tb(this._spanProcessors),a=i.next();!a.done;a=i.next())a.value.onStart(e,t)}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}},e.prototype.onEnd=function(e){var t,n;try{for(var r=tb(this._spanProcessors),i=r.next();!i.done;i=r.next())i.value.onEnd(e)}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}},e.prototype.shutdown=function(){var e,t,n=[];try{for(var r=tb(this._spanProcessors),i=r.next();!i.done;i=r.next()){var a=i.value;n.push(a.shutdown())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}return new Promise(function(e,t){Promise.all(n).then(function(){e()},t)})},e}(),tC=function(){function e(){}return e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){},e.prototype.shutdown=function(){return Promise.resolve()},e.prototype.forceFlush=function(){return Promise.resolve()},e}(),tP=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,a=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}return o},tN=globalThis&&globalThis.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};!function(e){e[e.resolved=0]="resolved",e[e.timeout=1]="timeout",e[e.error=2]="error",e[e.unresolved=3]="unresolved"}(u||(u={}));var tM=function(){function e(e){void 0===e&&(e={}),this._registeredSpanProcessors=[],this._tracers=new Map;var t,n,r,i,a,o,s,l,u,c,p,d,_,g,f,h,E,T=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=e.shift(),r=new WeakMap;e.length>0;)n=function e(t,n,r,i){if(void 0===r&&(r=0),!(r>20)){if(r++,e4(t)||e4(n)||e2(n))s=e0(n);else if(e3(t)){if(s=t.slice(),e3(n))for(var a,o,s,l=0,u=n.length;l<u;l++)s.push(e0(n[l]));else if(e5(n))for(var c=Object.keys(n),l=0,u=c.length;l<u;l++){var p=c[l];s[p]=e0(n[p])}}else if(e5(t))if(e5(n)){if(a=t,o=n,!(eZ(a)&&eZ(o)))return n;s=Object.assign({},t);for(var c=Object.keys(n),l=0,u=c.length;l<u;l++){var p=c[l],d=n[p];if(e4(d))void 0===d?delete s[p]:s[p]=d;else{var _=s[p];if(e1(t,p,i)||e1(n,p,i))delete s[p];else{if(e5(_)&&e5(d)){var g=i.get(_)||[],f=i.get(d)||[];g.push({obj:t,key:p}),f.push({obj:n,key:p}),i.set(_,g),i.set(d,f)}s[p]=e(s[p],d,r,i)}}}}else s=n;return s}}(n,e.shift(),0,r);return n}({},tE(),(g=Object.assign({},(t=e).spanLimits),f=eA(process.env),g.attributeCountLimit=null!=(s=null!=(o=null!=(a=null!=(r=null==(n=t.spanLimits)?void 0:n.attributeCountLimit)?r:null==(i=t.generalLimits)?void 0:i.attributeCountLimit)?a:f.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)?o:f.OTEL_ATTRIBUTE_COUNT_LIMIT)?s:128,g.attributeValueLengthLimit=null!=(_=null!=(d=null!=(p=null!=(u=null==(l=t.spanLimits)?void 0:l.attributeValueLengthLimit)?u:null==(c=t.generalLimits)?void 0:c.attributeValueLengthLimit)?p:f.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)?d:f.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?_:ey,Object.assign({},t,{spanLimits:g})));if(this.resource=null!=(h=T.resource)?h:ti.empty(),T.mergeResourceWithDefaults&&(this.resource=ti.default().merge(this.resource)),this._config=Object.assign({},T,{resource:this.resource}),null==(E=e.spanProcessors)?void 0:E.length)this._registeredSpanProcessors=tN([],tP(e.spanProcessors),!1),this.activeSpanProcessor=new tL(this._registeredSpanProcessors);else{var S=this._buildExporterFromEnv();if(void 0!==S){var m=new tO(S);this.activeSpanProcessor=m}else this.activeSpanProcessor=new tC}}return e.prototype.getTracer=function(e,t,n){var r=e+"@"+(t||"")+":"+((null==n?void 0:n.schemaUrl)||"");return this._tracers.has(r)||this._tracers.set(r,new tA({name:e,version:t,schemaUrl:null==n?void 0:n.schemaUrl},this._config,this)),this._tracers.get(r)},e.prototype.addSpanProcessor=function(e){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(function(e){return er.diag.error("Error while trying to shutdown current span processor",e)}),this._registeredSpanProcessors.push(e),this.activeSpanProcessor=new tL(this._registeredSpanProcessors)},e.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},e.prototype.register=function(e){void 0===e&&(e={}),er.trace.setGlobalTracerProvider(this),void 0===e.propagator&&(e.propagator=this._buildPropagatorFromEnv()),e.contextManager&&er.context.setGlobalContextManager(e.contextManager),e.propagator&&er.propagation.setGlobalPropagator(e.propagator)},e.prototype.forceFlush=function(){var e=this._config.forceFlushTimeoutMillis,t=this._registeredSpanProcessors.map(function(t){return new Promise(function(n){var r,i=setTimeout(function(){n(Error("Span processor did not completed within timeout period of "+e+" ms")),r=u.timeout},e);t.forceFlush().then(function(){clearTimeout(i),r!==u.timeout&&n(r=u.resolved)}).catch(function(e){clearTimeout(i),r=u.error,n(e)})})});return new Promise(function(e,n){Promise.all(t).then(function(t){var r=t.filter(function(e){return e!==u.resolved});r.length>0?n(r):e()}).catch(function(e){return n([e])})})},e.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},e.prototype._getPropagator=function(e){var t;return null==(t=this.constructor._registeredPropagators.get(e))?void 0:t()},e.prototype._getSpanExporter=function(e){var t;return null==(t=this.constructor._registeredExporters.get(e))?void 0:t()},e.prototype._buildPropagatorFromEnv=function(){var e=this,t=Array.from(new Set(eb().OTEL_PROPAGATORS)),n=t.map(function(t){var n=e._getPropagator(t);return n||er.diag.warn('Propagator "'+t+'" requested through environment variable is unavailable.'),n}).reduce(function(e,t){return t&&e.push(t),e},[]);return 0===n.length?void 0:1===t.length?n[0]:new ew({propagators:n})},e.prototype._buildExporterFromEnv=function(){var e=eb().OTEL_TRACES_EXPORTER;if("none"!==e&&""!==e){var t=this._getSpanExporter(e);return t||er.diag.error('Exporter "'+e+'" requested through environment variable is unavailable.'),t}},e._registeredPropagators=new Map([["tracecontext",function(){return new eY}],["baggage",function(){return new ed}]]),e._registeredExporters=new Map,e}();let n="http.method",na="http.url",no="http.status_code",ns="http.request.method",nl="http.response.status_code",nu="url.full",nc="sentry.parentIsRemote";function tD(e){return"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0}function tx(e){return!!e.attributes&&"object"==typeof e.attributes}let np="sentry-trace",nd="baggage",n_="sentry.dsc",ng="sentry.sampled_not_recording",nf="sentry.url",nh=(0,er.createContextKey)("sentry_scopes"),nE=(0,er.createContextKey)("sentry_fork_isolation_scope"),nT=(0,er.createContextKey)("sentry_fork_set_scope"),nS=(0,er.createContextKey)("sentry_fork_set_isolation_scope"),nm="_scopeContext";function tU(e){return e.getValue(nh)}function tw(e,t){return e.setValue(nh,t)}function tB(e){let{traceFlags:t,traceState:n}=e,r=!!n&&"1"===n.get(ng);if(t===er.TraceFlags.SAMPLED)return!0;if(r)return!1;let i=n?n.get(n_):void 0,a=i?(0,m.baggageHeaderToDynamicSamplingContext)(i):void 0;return a?.sampled==="true"||a?.sampled!=="false"&&void 0}function tF(e,t,r){let i=t[ns]||t[n];if(i)return function({name:e,kind:t,attributes:n},r){let i=["http"];switch(t){case er.SpanKind.CLIENT:i.push("client");break;case er.SpanKind.SERVER:i.push("server")}n["sentry.http.prefetch"]&&i.push("prefetch");let{urlPath:a,url:o,query:s,fragment:l,hasRoute:u}=function(e,t){let n=e["http.target"],r=e[na]||e[nu],i=e["http.route"],a="string"==typeof r?(0,w.parseUrl)(r):void 0,o=a?(0,w.getSanitizedUrlString)(a):void 0,s=a?.search||void 0,l=a?.hash||void 0;return"string"==typeof i?{urlPath:i,url:o,query:s,fragment:l,hasRoute:!0}:t===er.SpanKind.SERVER&&"string"==typeof n?{urlPath:(0,w.stripUrlQueryAndFragment)(n),url:o,query:s,fragment:l,hasRoute:!1}:a?{urlPath:o,url:o,query:s,fragment:l,hasRoute:!1}:"string"==typeof n?{urlPath:(0,w.stripUrlQueryAndFragment)(n),url:o,query:s,fragment:l,hasRoute:!1}:{urlPath:void 0,url:o,query:s,fragment:l,hasRoute:!1}}(n,t);if(!a)return{...tj(e,n),op:i.join(".")};let c=n["sentry.graphql.operation"],p=`${r} ${a}`,d=c?`${p} (${function(e){if(Array.isArray(e)){let t=e.slice().sort();return t.length<=5?t.join(", "):`${t.slice(0,5).join(", ")}, +${t.length-5}`}return`${e}`}(c)})`:p,_={};o&&(_.url=o),s&&(_["http.query"]=s),l&&(_["http.fragment"]=l);let g=t===er.SpanKind.CLIENT||t===er.SpanKind.SERVER,h=n[f.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]||"manual",E=!`${h}`.startsWith("auto"),T="custom"===n[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],S=n[f.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME],{description:m,source:v}=T||null!=S||!g&&E?tj(e,n):{description:d,source:u||"/"===a?"route":"url"};return{op:i.join("."),description:m,source:v,data:_}}({attributes:t,name:e,kind:r},i);let a=t["db.system"],o="string"==typeof t[f.SEMANTIC_ATTRIBUTE_SENTRY_OP]&&t[f.SEMANTIC_ATTRIBUTE_SENTRY_OP].startsWith("cache.");if(a&&!o)return function({attributes:e,name:t}){let n=e[f.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];if("string"==typeof n)return{op:"db",description:n,source:e[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]||"custom"};if("custom"===e[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE])return{op:"db",description:t,source:"custom"};let r=e["db.statement"];return{op:"db",description:r?r.toString():t,source:"task"}}({attributes:t,name:e});let s="custom"===t[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]?"custom":"route";if(t["rpc.service"])return{...tj(e,t,"route"),op:"rpc"};if(t["messaging.system"])return{...tj(e,t,s),op:"message"};let l=t["faas.trigger"];return l?{...tj(e,t,s),op:l.toString()}:{op:void 0,description:e,source:"custom"}}function tk(e){let t=tx(e)?e.attributes:{};return tF(e.name?e.name:"<unknown>",t,"number"==typeof e.kind?e.kind:er.SpanKind.INTERNAL)}function tj(e,t,n="custom"){let r=t[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]||n,i=t[f.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];return i&&"string"==typeof i?{description:i,source:r}:{description:e,source:r}}function tG(){return er.trace.getActiveSpan()}let nv="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function tV({dsc:e,sampled:t}){let n=e?(0,m.dynamicSamplingContextToSentryBaggageHeader)(e):void 0,r=new eG,i=n?r.set(n_,n):r;return!1===t?i.set(ng,"1"):i}let nO=new Set;function t$(e){nO.add(e)}class ny extends ed{constructor(){super(),t$("SentryPropagator"),this._urlMatchesTargetsMap=new A.LRUMap(100)}inject(e,t,n){if(eu(e)){nv&&R.logger.log("[Tracing] Not injecting trace data for url because tracing is suppressed.");return}let r=er.trace.getSpan(e),i=r&&function(e){let t=(0,g.spanToJSON)(e).data,n=t[na]||t[nu];if("string"==typeof n)return n;let r=e.spanContext().traceState?.get(nf);if(r)return r}(r);if(!function(e,t,n){if("string"!=typeof e||!t)return!0;let r=n?.get(e);if(void 0!==r)return nv&&!r&&R.logger.log(nI,e),r;let i=(0,D.stringMatchesSomePattern)(e,t);return n?.set(e,i),nv&&!i&&R.logger.log(nI,e),i}(i,(0,T.getClient)()?.getOptions()?.tracePropagationTargets,this._urlMatchesTargetsMap)){nv&&R.logger.log("[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:",i);return}let a=function(e){try{let t=e[nd];return Array.isArray(t)?t.join(","):t}catch{return}}(t),o=er.propagation.getBaggage(e)||er.propagation.createBaggage({}),{dynamicSamplingContext:s,traceId:l,spanId:u,sampled:c}=tH(e);if(a){let e=(0,m.parseBaggageHeader)(a);e&&Object.entries(e).forEach(([e,t])=>{o=o.setEntry(e,{value:t})})}s&&(o=Object.entries(s).reduce((e,[t,n])=>n?e.setEntry(`${m.SENTRY_BAGGAGE_KEY_PREFIX}${t}`,{value:n}):e,o)),l&&l!==er.INVALID_TRACEID&&n.set(t,np,(0,v.generateSentryTraceHeader)(l,u,c)),super.inject(er.propagation.setBaggage(e,o),t,n)}extract(e,t,n){let r=n.get(t,np),i=n.get(t,nd);return tX(tY(e,{sentryTrace:r?Array.isArray(r)?r[0]:r:void 0,baggage:i}))}fields(){return[np,nd]}}let nI="[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:";function tH(e,t={}){let n=er.trace.getSpan(e);if(n?.spanContext().isRemote){let e=n.spanContext();return{dynamicSamplingContext:(0,E.getDynamicSamplingContextFromSpan)(n),traceId:e.traceId,spanId:void 0,sampled:tB(e)}}if(n){let e=n.spanContext();return{dynamicSamplingContext:(0,E.getDynamicSamplingContextFromSpan)(n),traceId:e.traceId,spanId:e.spanId,sampled:tB(e)}}let r=t.scope||tU(e)?.scope||(0,T.getCurrentScope)(),i=t.client||(0,T.getClient)(),a=r.getPropagationContext();return{dynamicSamplingContext:i?(0,E.getDynamicSamplingContextFromScope)(i,r):void 0,traceId:a.traceId,spanId:a.propagationSpanId,sampled:a.sampled}}function tY(e,{sentryTrace:t,baggage:n}){let{traceId:r,parentSpanId:i,sampled:a,dsc:o}=(0,v.propagationContextFromHeaders)(t,n);if(!i)return e;let s=function({spanId:e,traceId:t,sampled:n,dsc:r}){let i=tV({dsc:r,sampled:n});return{traceId:t,spanId:e,isRemote:!0,traceFlags:n?er.TraceFlags.SAMPLED:er.TraceFlags.NONE,traceState:i}}({traceId:r,spanId:i,sampled:a,dsc:o});return er.trace.setSpanContext(e,s)}function tX(e){let t=tU(e);return tw(e,{scope:t?t.scope:(0,T.getCurrentScope)().clone(),isolationScope:t?t.isolationScope:(0,T.getIsolationScope)()})}function tz(e,t){let n=tq(),{name:r,parentSpan:i}=e;return t1(i)(()=>{let i=tZ(e.scope,e.forceTransaction),a=e.onlyIfParent&&!er.trace.getSpan(i)?el(i):i,o=tQ(e);return n.startActiveSpan(r,o,a,e=>(0,O.handleCallbackErrors)(()=>t(e),()=>{void 0===(0,g.spanToJSON)(e).status&&e.setStatus({code:er.SpanStatusCode.ERROR})},()=>e.end()))})}function tK(e,t){let n=tq(),{name:r,parentSpan:i}=e;return t1(i)(()=>{let i=tZ(e.scope,e.forceTransaction),a=e.onlyIfParent&&!er.trace.getSpan(i)?el(i):i,o=tQ(e);return n.startActiveSpan(r,o,a,e=>(0,O.handleCallbackErrors)(()=>t(e,()=>e.end()),()=>{void 0===(0,g.spanToJSON)(e).status&&e.setStatus({code:er.SpanStatusCode.ERROR})}))})}function tJ(e){let t=tq(),{name:n,parentSpan:r}=e;return t1(r)(()=>{let r=tZ(e.scope,e.forceTransaction),i=e.onlyIfParent&&!er.trace.getSpan(r)?el(r):r,a=tQ(e);return t.startSpan(n,a,i)})}function tW(e,t){let n=e?er.trace.setSpan(er.context.active(),e):er.trace.deleteSpan(er.context.active());return er.context.with(n,()=>t((0,T.getCurrentScope)()))}function tq(){let e=(0,T.getClient)();return e?.tracer||er.trace.getTracer("@sentry/opentelemetry",b.SDK_VERSION)}function tQ(e){var t;let{startTime:n,attributes:r,kind:i,op:a,links:o}=e,s="number"==typeof n?(t=n)<0x2540be3ff?1e3*t:t:n;return{attributes:a?{[f.SEMANTIC_ATTRIBUTE_SENTRY_OP]:a,...r}:r,kind:i,links:o,startTime:s}}function tZ(e,t){let n=function(e){if(e){let t=e[nm];if(t)return t}return er.context.active()}(e),r=er.trace.getSpan(n);if(!r||!t)return n;let i=er.trace.deleteSpan(n),{spanId:a,traceId:o}=r.spanContext(),s=tB(r.spanContext()),l=(0,g.getRootSpan)(r),u=tV({dsc:(0,E.getDynamicSamplingContextFromSpan)(l),sampled:s}),c={traceId:o,spanId:a,isRemote:!0,traceFlags:s?er.TraceFlags.SAMPLED:er.TraceFlags.NONE,traceState:u};return er.trace.setSpanContext(i,c)}function t0(e,t){let n=tX(tY(er.context.active(),e));return er.context.with(n,t)}function t1(e){return void 0!==e?t=>tW(e,t):e=>e()}function t3(e){let t=el(er.context.active());return er.context.with(t,e)}function t2({span:e,scope:t,client:n}={}){let r=(t&&t[nm])??er.context.active();if(e){let{scope:t}=(0,S.getCapturedScopesOnSpan)(e);r=t&&t[nm]||er.trace.setSpan(er.context.active(),e)}let{traceId:i,spanId:a,sampled:o,dynamicSamplingContext:s}=tH(r,{scope:t,client:n});return{"sentry-trace":(0,v.generateSentryTraceHeader)(i,a,o),baggage:(0,m.dynamicSamplingContextToSentryBaggageHeader)(s)}}function t5(e){return!0===e.attributes[nc]?void 0:tD(e)}function t4(e,t){let n=e.get(t.id);return n?.span?n:n&&!n.span?(n.span=t.span,n.parentNode=t.parentNode,n):(e.set(t.id,t),t)}let nR={1:"cancelled",2:"unknown_error",3:"invalid_argument",4:"deadline_exceeded",5:"not_found",6:"already_exists",7:"permission_denied",8:"resource_exhausted",9:"failed_precondition",10:"aborted",11:"out_of_range",12:"unimplemented",13:"internal_error",14:"unavailable",15:"data_loss",16:"unauthenticated"},nA=e=>Object.values(nR).includes(e);function t6(e){let t=tx(e)?e.attributes:{},n=e.status?e.status:void 0;if(n){if(n.code===er.SpanStatusCode.OK)return{code:U.SPAN_STATUS_OK};else if(n.code===er.SpanStatusCode.ERROR){if(void 0===n.message){let e=t7(t);if(e)return e}return n.message&&nA(n.message)?{code:U.SPAN_STATUS_ERROR,message:n.message}:{code:U.SPAN_STATUS_ERROR,message:"unknown_error"}}}let r=t7(t);return r||(n?.code===er.SpanStatusCode.UNSET?{code:U.SPAN_STATUS_OK}:{code:U.SPAN_STATUS_ERROR,message:"unknown_error"})}function t7(e){let t=e[nl]||e[no],n=e["rpc.grpc.status_code"],r="number"==typeof t?t:"string"==typeof t?parseInt(t):void 0;return"number"==typeof r?(0,U.getSpanStatusFromHttpCode)(r):"string"==typeof n?{code:U.SPAN_STATUS_ERROR,message:nR[n]||"unknown_error"}:void 0}class nb{constructor(e){this._finishedSpanBucketSize=e?.timeout||300,this._finishedSpanBuckets=Array(this._finishedSpanBucketSize).fill(void 0),this._lastCleanupTimestampInS=Math.floor(Date.now()/1e3),this._spansToBucketEntry=new WeakMap,this._sentSpans=new Map,this._debouncedFlush=(0,C.debounce)(this.flush.bind(this),1,{maxWait:100})}export(e){let t=Math.floor(Date.now()/1e3);if(this._lastCleanupTimestampInS!==t){let e=0;this._finishedSpanBuckets.forEach((n,r)=>{n&&n.timestampInS<=t-this._finishedSpanBucketSize&&(e+=n.spans.size,this._finishedSpanBuckets[r]=void 0)}),e>0&&nv&&R.logger.log(`SpanExporter dropped ${e} spans because they were pending for more than ${this._finishedSpanBucketSize} seconds.`),this._lastCleanupTimestampInS=t}let n=t%this._finishedSpanBucketSize,r=this._finishedSpanBuckets[n]||{timestampInS:t,spans:new Set};this._finishedSpanBuckets[n]=r,r.spans.add(e),this._spansToBucketEntry.set(e,r);let i=t5(e);(!i||this._sentSpans.has(i))&&this._debouncedFlush()}flush(){let e=this._finishedSpanBuckets.flatMap(e=>e?Array.from(e.spans):[]);this._flushSentSpanCache();let t=this._maybeSend(e),n=t.size,r=e.length-n;nv&&R.logger.log(`SpanExporter exported ${n} spans, ${r} spans are waiting for their parent spans to finish`);let i=Date.now()+3e5;for(let e of t){this._sentSpans.set(e.spanContext().spanId,i);let t=this._spansToBucketEntry.get(e);t&&t.spans.delete(e)}this._debouncedFlush.cancel()}clear(){this._finishedSpanBuckets=this._finishedSpanBuckets.fill(void 0),this._sentSpans.clear(),this._debouncedFlush.cancel()}_maybeSend(e){let t=function(e){let t=new Map;for(let n of e)!function(e,t){let n=t.spanContext().spanId,r=t5(t);if(!r)return t4(e,{id:n,span:t,children:[]});let i=function(e,t){let n=e.get(t);return n||t4(e,{id:t,children:[]})}(e,r),a=t4(e,{id:n,span:t,parentNode:i,children:[]});i.children.push(a)}(t,n);return Array.from(t,function([e,t]){return t})}(e),n=new Set;for(let e of this._getCompletedRootNodes(t)){let t=e.span;n.add(t);let r=function(e){let{op:t,description:n,data:r,origin:i="manual",source:a}=t8(e),o=(0,S.getCapturedScopesOnSpan)(e),s=e.attributes[f.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE],l={[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:a,[f.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:s,[f.SEMANTIC_ATTRIBUTE_SENTRY_OP]:t,[f.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:i,...r,...t9(e.attributes)},{links:u}=e,{traceId:c,spanId:p}=e.spanContext(),d=tD(e),_=t6(e),h={parent_span_id:d,span_id:p,trace_id:c,data:l,origin:i,op:t,status:(0,g.getStatusMessage)(_),links:(0,g.convertSpanLinksForEnvelope)(u)},T=l[nl];return{contexts:{trace:h,otel:{resource:e.resource.attributes},..."number"==typeof T?{response:{status_code:T}}:void 0},spans:[],start_timestamp:(0,g.spanTimeInputToSeconds)(e.startTime),timestamp:(0,g.spanTimeInputToSeconds)(e.endTime),transaction:n,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:o.scope,capturedSpanIsolationScope:o.isolationScope,sampleRate:s,dynamicSamplingContext:(0,E.getDynamicSamplingContextFromSpan)(e)},...a&&{transaction_info:{source:a}}}}(t),i=r.spans||[];for(let t of e.children)!function e(t,n,r){let i=t.span;if(i&&r.add(i),!i)return void t.children.forEach(t=>{e(t,n,r)});let a=i.spanContext().spanId,o=i.spanContext().traceId,s=tD(i),{attributes:l,startTime:u,endTime:c,links:p}=i,{op:d,description:_,data:h,origin:E="manual"}=t8(i),T={[f.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:E,[f.SEMANTIC_ATTRIBUTE_SENTRY_OP]:d,...t9(l),...h},S=t6(i),m={span_id:a,trace_id:o,data:T,description:_,parent_span_id:s,start_timestamp:(0,g.spanTimeInputToSeconds)(u),timestamp:(0,g.spanTimeInputToSeconds)(c)||void 0,status:(0,g.getStatusMessage)(S),op:d,origin:E,measurements:(0,P.timedEventsToMeasurements)(i.events),links:(0,g.convertSpanLinksForEnvelope)(p)};n.push(m),t.children.forEach(t=>{e(t,n,r)})}(t,i,n);r.spans=i.length>1e3?i.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):i;let a=(0,P.timedEventsToMeasurements)(t.events);a&&(r.measurements=a),(0,N.captureEvent)(r)}return n}_flushSentSpanCache(){let e=Date.now();for(let[t,n]of this._sentSpans.entries())n<=e&&this._sentSpans.delete(t)}_nodeIsCompletedRootNodeOrHasSentParent(e){return!!e.span&&(!e.parentNode||this._sentSpans.has(e.parentNode.id))}_getCompletedRootNodes(e){return e.filter(e=>this._nodeIsCompletedRootNodeOrHasSentParent(e))}}function t8(e){let{op:t,source:r,origin:i}=function(e){let t=e.attributes,n=t[f.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN];return{origin:n,op:t[f.SEMANTIC_ATTRIBUTE_SENTRY_OP],source:t[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]}}(e),{op:a,description:o,source:s,data:l}=tk(e);return{op:t||a,description:o,source:r||s,origin:i,data:{...l,...function(e){let t=e.attributes,r={};e.kind!==er.SpanKind.INTERNAL&&(r["otel.kind"]=er.SpanKind[e.kind]);let i=t[no];i&&(r[nl]=i);let a=function(e){if(!tx(e))return{};let t=e.attributes[nu]||e.attributes[na],r={url:t,"http.method":e.attributes[ns]||e.attributes[n]};!r["http.method"]&&r.url&&(r["http.method"]="GET");try{if("string"==typeof t){let e=(0,w.parseUrl)(t);r.url=(0,w.getSanitizedUrlString)(e),e.search&&(r["http.query"]=e.search),e.hash&&(r["http.fragment"]=e.hash)}}catch{}return r}(e);return a.url&&(r.url=a.url),a["http.query"]&&(r["http.query"]=a["http.query"].slice(1)),a["http.fragment"]&&(r["http.fragment"]=a["http.fragment"].slice(1)),r}(e)}}}function t9(e){let t={...e};return delete t[f.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE],delete t[nc],delete t[f.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME],t}class nL{constructor(e){t$("SentrySpanProcessor"),this._exporter=new nb(e)}async forceFlush(){this._exporter.flush()}async shutdown(){this._exporter.clear()}onStart(e,t){let n=er.trace.getSpan(t),r=tU(t);n&&!n.spanContext().isRemote&&(0,g.addChildSpanToSpan)(n,e),n?.spanContext().isRemote&&e.setAttribute(nc,!0),t===er.ROOT_CONTEXT&&(r={scope:(0,L.getDefaultCurrentScope)(),isolationScope:(0,L.getDefaultIsolationScope)()}),r&&(0,S.setCapturedScopesOnSpan)(e,r.scope,r.isolationScope),(0,M.logSpanStart)(e);let i=(0,T.getClient)();i?.emit("spanStart",e)}onEnd(e){(0,M.logSpanEnd)(e);let t=(0,T.getClient)();t?.emit("spanEnd",e),this._exporter.export(e)}}class nC{constructor(e){this._client=e,t$("SentrySampler")}shouldSample(e,t,r,i,a,o){let s=this._client.getOptions(),u=function(e){let t=er.trace.getSpan(e);return t&&(0,er.isSpanContextValid)(t.spanContext())?t:void 0}(e),c=u?.spanContext();if(!(0,h.hasSpansEnabled)(s))return ne({decision:void 0,context:e,spanAttributes:a});let p=a[n]||a[ns];if(i===er.SpanKind.CLIENT&&p&&(!u||c?.isRemote))return ne({decision:void 0,context:e,spanAttributes:a});let d=u?function(e,t,n){let r=e.spanContext();if((0,er.isSpanContextValid)(r)&&r.traceId===t){if(r.isRemote){let t=tB(e.spanContext());return nv&&R.logger.log(`[Tracing] Inheriting remote parent's sampled decision for ${n}: ${t}`),t}let t=tB(r);return nv&&R.logger.log(`[Tracing] Inheriting parent's sampled decision for ${n}: ${t}`),t}}(u,t,r):void 0;if(!(!u||c?.isRemote))return ne({decision:d?l.RECORD_AND_SAMPLED:l.NOT_RECORD,context:e,spanAttributes:a});let{description:_,data:g,op:E}=tF(r,a,i),T={...g,...a};E&&(T[f.SEMANTIC_ATTRIBUTE_SENTRY_OP]=E);let S={decision:!0};if(this._client.emit("beforeSampling",{spanAttributes:T,spanName:_,parentSampled:d,parentContext:c},S),!S.decision)return ne({decision:void 0,context:e,spanAttributes:a});let{isolationScope:v}=tU(e)??{},O=c?.traceState?c.traceState.get(n_):void 0,A=O?(0,m.baggageHeaderToDynamicSamplingContext)(O):void 0,b=(0,y.parseSampleRate)(A?.sample_rand)??Math.random(),[L,C,P]=(0,I.sampleSpan)(s,{name:_,attributes:T,normalizedRequest:v?.getScopeData().sdkProcessingMetadata.normalizedRequest,parentSampled:d,parentSampleRate:(0,y.parseSampleRate)(A?.sample_rate)},b),N=`${p}`.toUpperCase();return"OPTIONS"===N||"HEAD"===N?(nv&&R.logger.log(`[Tracing] Not sampling span because HTTP method is '${N}' for ${r}`),ne({decision:l.NOT_RECORD,context:e,spanAttributes:a,sampleRand:b,downstreamTraceSampleRate:0})):(L||void 0!==d||(nv&&R.logger.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),this._client.recordDroppedEvent("sample_rate","transaction")),{...ne({decision:L?l.RECORD_AND_SAMPLED:l.NOT_RECORD,context:e,spanAttributes:a,sampleRand:b,downstreamTraceSampleRate:P?C:void 0}),attributes:{[f.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:P?C:void 0}})}toString(){return"SentrySampler"}}function ne({decision:e,context:t,spanAttributes:n,sampleRand:r,downstreamTraceSampleRate:i}){let a=function(e,t){let n=er.trace.getSpan(e),r=n?.spanContext(),i=r?.traceState||new eG,a=t[na]||t[nu];return a&&"string"==typeof a&&(i=i.set(nf,a)),i}(t,n);return(void 0!==i&&(a=a.set("sentry.sample_rate",`${i}`)),void 0!==r&&(a=a.set("sentry.sample_rand",`${r}`)),void 0==e)?{decision:l.NOT_RECORD,traceState:a}:e===l.NOT_RECORD?{decision:e,traceState:a.set(ng,"1")}:{decision:e,traceState:a}}let nP="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,nN=new WeakMap,nM=(0,B.defineIntegration)((e={})=>{let t=void 0===e.breadcrumbs||e.breadcrumbs,n=e.shouldCreateSpanForRequest,r=new A.LRUMap(100),i=new A.LRUMap(100),a={};function o(e){let t=(0,T.getClient)();if(!t)return!1;let n=t.getOptions();if(void 0===n.tracePropagationTargets)return!0;let r=i.get(e);if(void 0!==r)return r;let a=(0,D.stringMatchesSomePattern)(e,n.tracePropagationTargets);return i.set(e,a),a}function s(e){if(void 0===n)return!0;let t=r.get(e);if(void 0!==t)return t;let i=n(e);return r.set(e,i),i}return{name:"WinterCGFetch",setupOnce(){(0,F.addFetchInstrumentationHandler)(e=>{let n=(0,T.getClient)();n&&nN.get(n)&&!(0,k.isSentryRequestUrl)(e.fetchData.url,n)&&((0,j.instrumentFetchRequest)(e,s,o,a,"auto.http.wintercg_fetch"),t&&function(e){let{startTimestamp:t,endTimestamp:n}=e;if(!n)return;let r={method:e.fetchData.method,url:e.fetchData.url};if(e.error){let i={data:e.error,input:e.args,startTimestamp:t,endTimestamp:n};(0,G.addBreadcrumb)({category:"fetch",data:r,level:"error",type:"http"},i)}else{let i=e.response;r.request_body_size=e.fetchData.request_body_size,r.response_body_size=e.fetchData.response_body_size,r.status_code=i?.status;let a={input:e.args,response:i,startTimestamp:t,endTimestamp:n},o=(0,V.getBreadcrumbLogLevelFromHttpStatusCode)(r.status_code);(0,G.addBreadcrumb)({category:"fetch",data:r,type:"http",level:o},a)}}(e))})},setup(e){nN.set(e,!0)}}});class nD{constructor(e=30){this.$=[],this._taskProducers=[],this._bufferSize=e}add(e){return this._taskProducers.length>=this._bufferSize?Promise.reject(Y.SENTRY_BUFFER_FULL_ERROR):(this._taskProducers.push(e),Promise.resolve({}))}drain(e){let t=[...this._taskProducers];return this._taskProducers=[],new Promise(n=>{let r=setTimeout(()=>{e&&e>0&&n(!1)},e);Promise.all(t.map(e=>e().then(null,()=>{}))).then(()=>{clearTimeout(r),n(!0)})})}}function nt(e){return(0,$.createTransport)(e,function(t){let n={body:t.body,method:"POST",headers:e.headers,...e.fetchOptions};return(0,H.suppressTracing)(()=>fetch(e.url,n).then(e=>({statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}})))},new nD(e.bufferSize))}let nx=["addListener","on","once","prependListener","prependOnceListener"];class nU{constructor(){nU.prototype.__init.call(this),nU.prototype.__init2.call(this)}bind(e,t){return"object"==typeof t&&null!==t&&"on"in t?this._bindEventEmitter(e,t):"function"==typeof t?this._bindFunction(e,t):t}_bindFunction(e,t){let n=this,r=function(...r){return n.with(e,()=>t.apply(this,r))};return Object.defineProperty(r,"length",{enumerable:!1,configurable:!0,writable:!1,value:t.length}),r}_bindEventEmitter(e,t){return void 0!==this._getPatchMap(t)||(this._createPatchMap(t),nx.forEach(n=>{void 0!==t[n]&&(t[n]=this._patchAddListener(t,t[n],e))}),"function"==typeof t.removeListener&&(t.removeListener=this._patchRemoveListener(t,t.removeListener)),"function"==typeof t.off&&(t.off=this._patchRemoveListener(t,t.off)),"function"==typeof t.removeAllListeners&&(t.removeAllListeners=this._patchRemoveAllListeners(t,t.removeAllListeners))),t}_patchRemoveListener(e,t){let n=this;return function(r,i){let a=n._getPatchMap(e)?.[r];if(void 0===a)return t.call(this,r,i);let o=a.get(i);return t.call(this,r,o||i)}}_patchRemoveAllListeners(e,t){let n=this;return function(r){let i=n._getPatchMap(e);return void 0!==i&&(0==arguments.length?n._createPatchMap(e):void 0!==i[r]&&delete i[r]),t.apply(this,arguments)}}_patchAddListener(e,t,n){let r=this;return function(i,a){if(r._wrapped)return t.call(this,i,a);let o=r._getPatchMap(e);void 0===o&&(o=r._createPatchMap(e));let s=o[i];void 0===s&&(s=new WeakMap,o[i]=s);let l=r.bind(n,a);s.set(a,l),r._wrapped=!0;try{return t.call(this,i,l)}finally{r._wrapped=!1}}}_createPatchMap(e){let t=Object.create(null);return e[this._kOtListeners]=t,t}_getPatchMap(e){return e[this._kOtListeners]}__init(){this._kOtListeners=Symbol("OtListeners")}__init2(){this._wrapped=!1}}class nw extends nU{constructor(){super();let e=X.GLOBAL_OBJ.AsyncLocalStorage;e?this._asyncLocalStorage=new e:(nP&&R.logger.warn("Tried to register AsyncLocalStorage async context strategy in a runtime that doesn't support AsyncLocalStorage."),this._asyncLocalStorage={getStore(){},run(e,t,...n){return t.apply(this,n)},disable(){}})}active(){return this._asyncLocalStorage.getStore()??er.ROOT_CONTEXT}with(e,t,n,...r){let i=null==n?t:t.bind(n);return this._asyncLocalStorage.run(e,i,...r)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}}let nB=(0,z.createStackParser)((0,K.nodeStackLineParser)());function nn(e){return[(0,J.dedupeIntegration)(),(0,W.inboundFiltersIntegration)(),(0,q.functionToStringIntegration)(),(0,Q.linkedErrorsIntegration)(),nM(),(0,Z.consoleIntegration)(),...e.sendDefaultPii?[(0,ee.requestDataIntegration)()]:[]]}function nr(e={}){function n(){let e=tU(er.context.active());return e||{scope:(0,L.getDefaultCurrentScope)(),isolationScope:(0,L.getDefaultIsolationScope)()}}function r(){return n().scope}function i(){return n().isolationScope}if((0,_.setAsyncContextStrategy)({withScope:function(e){let t=er.context.active();return er.context.with(t,()=>e(r()))},withSetScope:function(e,t){let n=e[nm]||er.context.active();return er.context.with(n.setValue(nT,e),()=>t(e))},withSetIsolationScope:function(e,t){let n=er.context.active();return er.context.with(n.setValue(nS,e),()=>t(i()))},withIsolationScope:function(e){let t=er.context.active();return er.context.with(t.setValue(nE,!0),()=>e(i()))},getCurrentScope:r,getIsolationScope:i,startSpan:tz,startSpanManual:tK,startInactiveSpan:tJ,getActiveSpan:tG,suppressTracing:t3,getTraceData:t2,continueTrace:t0,withActiveSpan:tW}),(0,T.getCurrentScope)().update(e.initialScope),void 0===e.defaultIntegrations&&(e.defaultIntegrations=nn(e)),void 0===e.dsn&&process.env.SENTRY_DSN&&(e.dsn=process.env.SENTRY_DSN),void 0===e.tracesSampleRate&&process.env.SENTRY_TRACES_SAMPLE_RATE){let t=parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE);isFinite(t)&&(e.tracesSampleRate=t)}if(void 0===e.release){let t=function(e){if(process.env.SENTRY_RELEASE)return process.env.SENTRY_RELEASE;if(X.GLOBAL_OBJ.SENTRY_RELEASE?.id)return X.GLOBAL_OBJ.SENTRY_RELEASE.id;let t=process.env.GITHUB_SHA||process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_SHA||process.env.CI_BUILD_REF||process.env.CI_COMMIT_SHA||process.env.BITBUCKET_COMMIT,n=process.env.APPVEYOR_PULL_REQUEST_HEAD_COMMIT||process.env.APPVEYOR_REPO_COMMIT||process.env.CODEBUILD_RESOLVED_SOURCE_VERSION||process.env.AWS_COMMIT_ID||process.env.BUILD_SOURCEVERSION||process.env.GIT_CLONE_COMMIT_HASH||process.env.BUDDY_EXECUTION_REVISION||process.env.BUILDKITE_COMMIT||process.env.CIRCLE_SHA1||process.env.CIRRUS_CHANGE_IN_REPO||process.env.CF_REVISION||process.env.CM_COMMIT||process.env.CF_PAGES_COMMIT_SHA||process.env.DRONE_COMMIT_SHA||process.env.FC_GIT_COMMIT_SHA||process.env.HEROKU_TEST_RUN_COMMIT_VERSION||process.env.HEROKU_SLUG_COMMIT||process.env.RAILWAY_GIT_COMMIT_SHA||process.env.RENDER_GIT_COMMIT||process.env.SEMAPHORE_GIT_SHA||process.env.TRAVIS_PULL_REQUEST_SHA||process.env.VERCEL_GIT_COMMIT_SHA||process.env.VERCEL_GITHUB_COMMIT_SHA||process.env.VERCEL_GITLAB_COMMIT_SHA||process.env.VERCEL_BITBUCKET_COMMIT_SHA||process.env.ZEIT_GITHUB_COMMIT_SHA||process.env.ZEIT_GITLAB_COMMIT_SHA||process.env.ZEIT_BITBUCKET_COMMIT_SHA,r=process.env.CI_COMMIT_ID||process.env.SOURCE_COMMIT||process.env.SOURCE_VERSION||process.env.GIT_COMMIT||process.env.COMMIT_REF||process.env.BUILD_VCS_NUMBER||process.env.CI_COMMIT_SHA;return t||n||r||void 0}();void 0!==t&&(e.release=t)}e.environment=e.environment||process.env.SENTRY_ENVIRONMENT||function(e){let t=process.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}()||"production";let a=new t({...e,stackParser:(0,z.stackParserFromStackParserOptions)(e.stackParser||nB),integrations:(0,B.getIntegrationsToSetup)(e),transport:e.transport||nt});return(0,T.getCurrentScope)().setClient(a),a.init(),e.skipOpenTelemetrySetup||(function(e){var t;e.getOptions().debug&&function(){let e=new Proxy(R.logger,{get:(e,t,n)=>Reflect.get(e,"verbose"===t?"debug":t,n)});er.diag.disable(),er.diag.setLogger(e,er.DiagLogLevel.DEBUG)}();let n=new tM({sampler:new nC(e),resource:new ti({"service.name":"edge","service.namespace":"sentry","service.version":b.SDK_VERSION}),forceFlushTimeoutMillis:500,spanProcessors:[new nL({timeout:e.getOptions().maxSpanWaitDuration})]}),r=(t=nw,class extends t{constructor(...e){super(...e),t$("SentryContextManager")}with(e,t,n,...r){let i=tU(e),a=i?.scope||(0,T.getCurrentScope)(),o=i?.isolationScope||(0,T.getIsolationScope)(),s=!0===e.getValue(nE),l=e.getValue(nT),u=e.getValue(nS),c=l||a.clone(),p=tw(e,{scope:c,isolationScope:u||(s?o.clone():o)}).deleteValue(nE).deleteValue(nT).deleteValue(nS);return(0,x.addNonEnumerableProperty)(c,nm,p),super.with(p,t,n,...r)}});er.trace.setGlobalTracerProvider(n),er.propagation.setGlobalPropagator(new ny),er.context.setGlobalContextManager(new r),e.traceProvider=n}(a),function(){if(!nP)return;let e=Array.from(nO),t=["SentryContextManager","SentryPropagator"];for(let n of((0,h.hasSpansEnabled)()&&t.push("SentrySpanProcessor"),t))e.includes(n)||R.logger.error(`You have to set up the ${n}. Without this, the OpenTelemetry & Sentry integration will not work properly.`);e.includes("SentrySampler")||R.logger.warn("You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.")}()),a.on("createDsc",(e,t)=>{if(!t)return;let n=(0,g.spanToJSON)(t).data[f.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],{description:r}=t.name?tk(t):{description:void 0};if("url"!==n&&r&&(e.transaction=r),(0,h.hasSpansEnabled)()){let n=tB(t.spanContext());e.sampled=void 0==n?void 0:String(n)}}),a.on("preprocessEvent",e=>{let t=tG();if(!t||"transaction"===e.type)return;e.contexts={trace:(0,g.spanToTraceContext)(t),...e.contexts};let n=(0,g.getRootSpan)(t);return e.sdkProcessingMetadata={dynamicSamplingContext:(0,E.getDynamicSamplingContextFromSpan)(n),...e.sdkProcessingMetadata},e}),a}function ni(e,t,n,r){(0,et._INTERNAL_captureLog)({level:e,message:t,attributes:n,severityNumber:r})}let nF=Object.defineProperty({__proto__:null,debug:function(e,t){ni("debug",e,t)},error:function(e,t){ni("error",e,t)},fatal:function(e,t){ni("fatal",e,t)},fmt:en.fmt,info:function(e,t){ni("info",e,t)},trace:function(e,t){ni("trace",e,t)},warn:function(e,t){ni("warn",e,t)}},Symbol.toStringTag,{value:"Module"})}},757214:function(e){var{g:t,__dirname:n,m:r,e:i}=e;"use strict";i._=function(e){return e&&e.__esModule?e:{default:e}}},331790:function(e){var{g:t,__dirname:n,m:r,e:i}=e;"use strict";r.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},54976:function(e){var{g:t,__dirname:n,m:r,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0});var a={APP_BUILD_MANIFEST:function(){return v},APP_CLIENT_INTERNALS:function(){return Z},APP_PATHS_MANIFEST:function(){return T},APP_PATH_ROUTES_MANIFEST:function(){return S},BARREL_OPTIMIZATION_PREFIX:function(){return H},BLOCKED_PAGES:function(){return k},BUILD_ID_FILE:function(){return F},BUILD_MANIFEST:function(){return m},CLIENT_PUBLIC_FILES_PATH:function(){return j},CLIENT_REFERENCE_MANIFEST:function(){return Y},CLIENT_STATIC_FILES_PATH:function(){return G},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return et},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return q},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return Q},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return er},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return ei},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return ee},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return en},COMPILER_INDEXES:function(){return s},COMPILER_NAMES:function(){return n},CONFIG_FILES:function(){return B},DEFAULT_RUNTIME_WEBPACK:function(){return ea},DEFAULT_SANS_SERIF_FONT:function(){return ec},DEFAULT_SERIF_FONT:function(){return eu},DEV_CLIENT_MIDDLEWARE_MANIFEST:function(){return x},DEV_CLIENT_PAGES_MANIFEST:function(){return N},DYNAMIC_CSS_MANIFEST:function(){return W},EDGE_RUNTIME_WEBPACK:function(){return eo},EDGE_UNSUPPORTED_NODE_APIS:function(){return ef},EXPORT_DETAIL:function(){return A},EXPORT_MARKER:function(){return R},FUNCTIONS_CONFIG_MANIFEST:function(){return O},IMAGES_MANIFEST:function(){return C},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return J},MIDDLEWARE_BUILD_MANIFEST:function(){return z},MIDDLEWARE_MANIFEST:function(){return M},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return K},MODERN_BROWSERSLIST_TARGET:function(){return t.default},NEXT_BUILTIN_DOCUMENT:function(){return $},NEXT_FONT_MANIFEST:function(){return I},PAGES_MANIFEST:function(){return h},PHASE_DEVELOPMENT_SERVER:function(){return _},PHASE_EXPORT:function(){return c},PHASE_INFO:function(){return f},PHASE_PRODUCTION_BUILD:function(){return p},PHASE_PRODUCTION_SERVER:function(){return d},PHASE_TEST:function(){return g},PRERENDER_MANIFEST:function(){return b},REACT_LOADABLE_MANIFEST:function(){return U},ROUTES_MANIFEST:function(){return L},RSC_MODULE_TYPES:function(){return eg},SERVER_DIRECTORY:function(){return w},SERVER_FILES_MANIFEST:function(){return P},SERVER_PROPS_ID:function(){return el},SERVER_REFERENCE_MANIFEST:function(){return X},STATIC_PROPS_ID:function(){return es},STATIC_STATUS_PAGES:function(){return ep},STRING_LITERAL_DROP_BUNDLE:function(){return V},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return y},SYSTEM_ENTRYPOINTS:function(){return eh},TRACE_OUTPUT_VERSION:function(){return ed},TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST:function(){return D},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return e_},UNDERSCORE_NOT_FOUND_ROUTE:function(){return l},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return u},WEBPACK_STATS:function(){return E}};for(var o in a)Object.defineProperty(i,o,{enumerable:!0,get:a[o]});let t=e.r(757214)._(e.r(331790)),n={client:"client",server:"server",edgeServer:"edge-server"},s={[n.client]:0,[n.server]:1,[n.edgeServer]:2},l="/_not-found",u=""+l+"/page",c="phase-export",p="phase-production-build",d="phase-production-server",_="phase-development-server",g="phase-test",f="phase-info",h="pages-manifest.json",E="webpack-stats.json",T="app-paths-manifest.json",S="app-path-routes-manifest.json",m="build-manifest.json",v="app-build-manifest.json",O="functions-config-manifest.json",y="subresource-integrity-manifest",I="next-font-manifest",R="export-marker.json",A="export-detail.json",b="prerender-manifest.json",L="routes-manifest.json",C="images-manifest.json",P="required-server-files.json",N="_devPagesManifest.json",M="middleware-manifest.json",D="_clientMiddlewareManifest.json",x="_devMiddlewareManifest.json",U="react-loadable-manifest.json",w="server",B=["next.config.js","next.config.mjs","next.config.ts"],F="BUILD_ID",k=["/_document","/_app","/_error"],j="public",G="static",V="__NEXT_DROP_CLIENT_FILE__",$="__NEXT_BUILTIN_DOCUMENT__",H="__barrel_optimize__",Y="client-reference-manifest",X="server-reference-manifest",z="middleware-build-manifest",K="middleware-react-loadable-manifest",J="interception-route-rewrite-manifest",W="dynamic-css-manifest",q="main",Q=""+q+"-app",Z="app-pages-internals",ee="react-refresh",et="amp",en="webpack",er="polyfills",ei=Symbol(er),ea="webpack-runtime",eo="edge-runtime-webpack",es="__N_SSG",el="__N_SSP",eu={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},ec={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},ep=["/500"],ed=1,e_=6e3,eg={client:"client",server:"server"},ef=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],eh=new Set([q,ee,et,Q]);("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),r.exports=i.default)}},182877:function(e){var{g:t,__dirname:n,m:r,e:i}=e;r.exports=e.r(54976)},836178:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({isBuild:()=>i});var r=e.i(182877);function i(){return process.env.NEXT_PHASE===r.PHASE_PRODUCTION_BUILD}},725931:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}e.s({escapeStringForRegex:()=>r})},784647:e=>{"use strict";var{g:t,__dirname:n}=e;{function r(e,t){let n=0;for(let t=e.length-1;t>=0;t--){let r=e[t];"."===r?e.splice(t,1):".."===r?(e.splice(t,1),n++):n&&(e.splice(t,1),n--)}if(t)for(;n--;)e.unshift("..");return e}e.s({basename:()=>d,dirname:()=>p,isAbsolute:()=>u,join:()=>c,normalizePath:()=>l,relative:()=>s,resolve:()=>a});let t=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function i(e){let n=e.length>1024?`<truncated>${e.slice(-1024)}`:e,r=t.exec(n);return r?r.slice(1):[]}function a(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:"/";i&&(t=`${i}/${t}`,n="/"===i.charAt(0))}return t=r(t.split("/").filter(e=>!!e),!n).join("/"),(n?"/":"")+t||"."}function o(e){let t=0;for(;t<e.length&&""===e[t];t++);let n=e.length-1;for(;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}function s(e,t){e=a(e).slice(1),t=a(t).slice(1);let n=o(e.split("/")),r=o(t.split("/")),i=Math.min(n.length,r.length),s=i;for(let e=0;e<i;e++)if(n[e]!==r[e]){s=e;break}let l=[];for(let e=s;e<n.length;e++)l.push("..");return(l=l.concat(r.slice(s))).join("/")}function l(e){let t=u(e),n="/"===e.slice(-1),i=r(e.split("/").filter(e=>!!e),!t).join("/");return i||t||(i="."),i&&n&&(i+="/"),(t?"/":"")+i}function u(e){return"/"===e.charAt(0)}function c(...e){return l(e.join("/"))}function p(e){let t=i(e),n=t[0]||"",r=t[1];return n||r?(r&&(r=r.slice(0,r.length-1)),n+r):"."}function d(e,t){let n=i(e)[2]||"";return t&&n.slice(-1*t.length)===t&&(n=n.slice(0,n.length-t.length)),n}}},405099:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({generateIteratee:()=>o,rewriteFramesIntegration:()=>t});var r=e.i(171485),i=e.i(784647),a=e.i(441619);let t=(0,r.defineIntegration)((e={})=>{let t=e.root,n=e.prefix||"app:///",r="window"in a.GLOBAL_OBJ&&!!a.GLOBAL_OBJ.window,i=e.iteratee||o({isBrowser:r,root:t,prefix:n});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t?.frames&&t.frames.map(e=>i(e))}}}})}}}catch(t){return e}}(t)),t}}});function o({isBrowser:e,root:t,prefix:n}){return r=>{if(!r.filename)return r;let a=/^[a-zA-Z]:\\/.test(r.filename)||r.filename.includes("\\")&&!r.filename.includes("/"),o=/^\//.test(r.filename);if(e){if(t){let e=r.filename;0===e.indexOf(t)&&(r.filename=e.replace(t,n))}}else if(a||o){let e=a?r.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):r.filename,o=t?(0,i.relative)(t,e):(0,i.basename)(e);r.filename=`${n}${o}`}return r}}}},935033:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({distDirRewriteFramesIntegration:()=>t});var r=e.i(171485),i=e.i(725931),a=e.i(405099);let t=(0,r.defineIntegration)(({distDirName:e})=>{let t=e.replace(/(\/|\\)$/,""),n=RegExp(`.*${(0,i.escapeStringForRegex)(t)}`);return{...(0,a.rewriteFramesIntegration)({iteratee:e=>(e.filename=e.filename?.replace(n,"app:///_next"),e)}),name:"DistDirRewriteFrames"}})}},797750:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({init:()=>f,withSentryConfig:()=>h});var r=e.i(122744),i=e.i(441619),a=e.i(562572),o=e.i(526321),s=e.i(325939),l=e.i(400230),u=e.i(519535),c=e.i(548729),p=e.i(216050),d=e.i(836178),_=e.i(551616),g=e.i(935033);function f(e={}){if((0,r.registerSpanErrorInstrumentation)(),(0,d.isBuild)())return;let t=(0,p.getDefaultIntegrations)(e);t.push((0,g.distDirRewriteFramesIntegration)({distDirName:".next"}));let n={defaultIntegrations:t,release:"4c9110383dcc2daecb60c7a28fb318d2fde56503",...e};(0,a.applySdkMetadata)(n,"nextjs",["nextjs","vercel-edge"]);let i=(0,p.init)(n);i?.on("spanStart",e=>{let t=(0,o.spanToJSON)(e).data;t?.["next.span_type"]!==void 0&&e.setAttribute(s.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,"auto"),t?.["next.span_type"]==="Middleware.execute"&&(e.setAttribute(s.SEMANTIC_ATTRIBUTE_SENTRY_OP,"http.server.middleware"),e.setAttribute(s.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,"url"))}),i?.on("preprocessEvent",e=>{"transaction"===e.type&&e.contexts?.trace?.data?.["next.span_type"]==="Middleware.execute"&&e.contexts?.trace?.data?.["next.span_name"]&&e.transaction&&(e.transaction=(0,l.stripUrlQueryAndFragment)(e.contexts.trace.data["next.span_name"]))}),i?.on("spanEnd",e=>{e===(0,o.getRootSpan)(e)&&(0,u.vercelWaitUntil)((0,_.flushSafelyWithTimeout)())});try{(0,c.getGlobalScope)().setTag("turbopack",!0)}catch{}}function h(e){return e}i.GLOBAL_OBJ},536757:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({}),(0,e.i(797750).init)({dsn:"https://<EMAIL>/4509552950640720",tracesSampleRate:1,debug:!1})}}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__a332d90a._.js.map