(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["chunks/edge-wrapper_2780f442.js",{},{otherChunks:["chunks/_6826e369._.js","chunks/[root-of-the-server]__a332d90a._.js","chunks/node_modules_@sentry_2f99c5a2._.js"],runtimeModuleIds:[348105]}]),(()=>{let e;if(!Array.isArray(globalThis.TURBOPACK))return;let t=Symbol("reexported objects"),n=Object.prototype.hasOwnProperty,r="undefined"!=typeof Symbol&&Symbol.toStringTag;function o(e,t,r){n.call(e,t)||Object.defineProperty(e,t,r)}function u(e,t){for(let n in o(e,"__esModule",{value:!0}),r&&o(e,r,{value:"Module"}),t){let r=t[n];Array.isArray(r)?o(e,n,{get:r[0],set:r[1],enumerable:!0}):o(e,n,{get:r,enumerable:!0})}Object.seal(e)}function l(e,t,n){e.namespaceObject=e.exports,u(t,n)}function a(e,r,o){let u;(u=e[t])||(u=e[t]=[],e.exports=e.namespaceObject=new Proxy(r,{get(e,t){if(n.call(e,t)||"default"===t||"__esModule"===t)return Reflect.get(e,t);for(let e of u){let n=Reflect.get(e,t);if(void 0!==n)return n}},ownKeys(e){let t=Reflect.ownKeys(e);for(let e of u)for(let n of Reflect.ownKeys(e))"default"===n||t.includes(n)||t.push(n);return t}})),"object"==typeof o&&null!==o&&e[t].push(o)}function i(e,t){e.exports=t}function s(e,t){e.exports=e.namespaceObject=t}let c=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,f=[null,c({}),c([]),c(c)];function d(e,t,n){let r=Object.create(null);for(let t=e;("object"==typeof t||"function"==typeof t)&&!f.includes(t);t=c(t))for(let n of Object.getOwnPropertyNames(t))r[n]=function(e,t){return()=>e[t]}(e,n);return n&&"default"in r||(r.default=()=>e),u(t,r),t}function p(e){return"function"==typeof e?function(...t){return e.apply(this,t)}:Object.create(null)}function h(e,t){let n=F(t,e);if(n.error)throw n.error;if(n.namespaceObject)return n.namespaceObject;let r=n.exports;return n.namespaceObject=d(r,p(r),r&&r.__esModule)}let b="function"==typeof require?require:function(){throw Error("Unexpected use of runtime require")};function y(e,t){let n=F(t,e);if(n.error)throw n.error;return n.exports}function m(e){function t(t){if(n.call(e,t))return e[t].module();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r}return t.keys=()=>Object.keys(e),t.resolve=t=>{if(n.call(e,t))return e[t].id();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r},t.import=async e=>await t(e),t}function O(e){return"string"==typeof e?e:e.path}function w(){let e,t;return{promise:new Promise((n,r)=>{t=r,e=n}),resolve:e,reject:t}}let g=Symbol("turbopack queues"),_=Symbol("turbopack exports"),j=Symbol("turbopack error");function k(e){e&&1!==e.status&&(e.status=1,e.forEach(e=>e.queueCount--),e.forEach(e=>e.queueCount--?e.queueCount++:e()))}function R(e,t,n){let r=n?Object.assign([],{status:-1}):void 0,o=new Set,{resolve:u,reject:l,promise:a}=w(),i=Object.assign(a,{[_]:e.exports,[g]:e=>{r&&e(r),o.forEach(e),i.catch(()=>{})}}),s={get:()=>i,set(e){e!==i&&(i[_]=e)}};Object.defineProperty(e,"exports",s),Object.defineProperty(e,"namespaceObject",s),t(function(e){let t=e.map(e=>{if(null!==e&&"object"==typeof e){if(g in e)return e;if(null!=e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then){let t=Object.assign([],{status:0}),n={[_]:{},[g]:e=>e(t)};return e.then(e=>{n[_]=e,k(t)},e=>{n[j]=e,k(t)}),n}}return{[_]:e,[g]:()=>{}}}),n=()=>t.map(e=>{if(e[j])throw e[j];return e[_]}),{promise:u,resolve:l}=w(),a=Object.assign(()=>l(n),{queueCount:0});function i(e){e!==r&&!o.has(e)&&(o.add(e),e&&0===e.status&&(a.queueCount++,e.push(a)))}return t.map(e=>e[g](i)),a.queueCount?u:n()},function(e){e?l(i[j]=e):u(i[_]),k(r)}),r&&-1===r.status&&(r.status=0)}let U=function(e){let t=new URL(e,"x:/"),n={};for(let e in t)n[e]=t[e];for(let t in n.href=e,n.pathname=e.replace(/[?#].*/,""),n.origin=n.protocol="",n.toString=n.toJSON=(...t)=>e,n)Object.defineProperty(this,t,{enumerable:!0,configurable:!0,value:n[t]})};function C(e,t){throw Error(`Invariant: ${t(e)}`)}U.prototype=URL.prototype;var P=function(e){return e[e.Runtime=0]="Runtime",e[e.Parent=1]="Parent",e[e.Update=2]="Update",e}(P||{});let $=Object.create(null),T=new Set,x=new Map,v=new Map,S=new Map,E=new Map;async function M(e,t){let n;if("string"==typeof t)return K(e,t);let r=t.included||[],o=r.map(e=>!!$[e]||S.get(e));if(o.length>0&&o.every(e=>e))return Promise.all(o);let u=t.moduleChunks||[],l=u.map(e=>E.get(e)).filter(e=>e);if(l.length>0){if(l.length===u.length)return Promise.all(l);let t=new Set;for(let e of u)E.has(e)||t.add(e);for(let n of t){let t=K(e,n);E.set(n,t),l.push(t)}n=Promise.all(l)}else for(let r of(n=K(e,t.path),u))E.has(r)||E.set(r,n);for(let e of r)S.has(e)||S.set(e,n);return n}async function A(t,n){try{await e.loadChunk(n,t)}catch(r){let e;switch(t.type){case 0:e=`as a runtime dependency of chunk ${t.chunkPath}`;break;case 1:e=`from module ${t.parentId}`;break;case 2:e="from an HMR update";break;default:C(t,e=>`Unknown source type: ${e?.type}`)}throw Error(`Failed to load chunk ${n} ${e}${r?`: ${r}`:""}`,r?{cause:r}:void 0)}}async function K(e,t){return A(e,I(t))}function N(e){return`/ROOT/${e??""}`}function q(e){let t=new Blob([`self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};
self.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(e.reverse().map(I),null,2)};
importScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`],{type:"text/javascript"});return URL.createObjectURL(t)}function I(e){return`${e.split("/").map(e=>encodeURIComponent(e)).join("/")}`}function L([t,n,r]){let o=function(e){if("string"==typeof e)return e;let t=decodeURIComponent(("undefined"!=typeof TURBOPACK_NEXT_CHUNK_URLS?TURBOPACK_NEXT_CHUNK_URLS.pop():e.getAttribute("src")).replace(/[?#].*$/,""));return t.startsWith("")?t.slice(0):t}(t);for(let[e,t]of Object.entries(n))$[e]||($[e]=t),function(e,t){let n=x.get(e);n?n.add(t):(n=new Set([t]),x.set(e,n));let r=v.get(t);r?r.add(e):(r=new Set([e]),v.set(t,r))}(e,o);return e.registerChunk(o,r)}let B=/\.js(?:\?[^#]*)?(?:#.*)?$/,H={},F=(e,t)=>{let n=H[e];return n||W(e,{type:P.Parent,parentId:t.id})};function W(e,t){let n=$[e];if("function"!=typeof n){let n;switch(t.type){case P.Runtime:n=`as a runtime entry of chunk ${t.chunkPath}`;break;case P.Parent:n=`because it was required from module ${t.parentId}`;break;case P.Update:n="because of an HMR update";break;default:C(t,e=>`Unknown source type: ${e?.type}`)}throw Error(`Module ${e} was instantiated ${n}, but the module factory is not available. It might have been deleted in an HMR update.`)}switch(t.type){case P.Runtime:T.add(e);break;case P.Parent:break;case P.Update:throw Error("Unexpected");default:C(t,e=>`Unknown source type: ${e?.type}`)}let r={exports:{},error:void 0,loaded:!1,id:e,namespaceObject:void 0};H[e]=r;try{var o;let t={type:P.Parent,parentId:e},u=y.bind(null,r);n.call(r.exports,((o={a:R.bind(null,r),e:r.exports,r:y.bind(null,r),t:b,f:m,i:h.bind(null,r),s:l.bind(null,r,r.exports),j:a.bind(null,r,r.exports),v:i.bind(null,r),n:s.bind(null,r),m:r,c:H,M:$,l:M.bind(null,t),L:A.bind(null,t),w:z.bind(null,t),u:J.bind(null,t),g:globalThis,P:N,U:U,R:function(e){let t=u(e);return t?.default??t},b:q,d:"string"==typeof r.id?r.id.replace(/(^|\/)\/+$/,""):r.id}).x=X,o.y=D,o))}catch(e){throw r.error=e,e}return r.loaded=!0,r.namespaceObject&&r.exports!==r.namespaceObject&&d(r.exports,r.namespaceObject),r}async function D(e){let t;try{t=await import(e)}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return t&&t.__esModule&&t.default&&"default"in t.default?d(t.default,p(t),!0):t}function X(e,t,n=!1){let r;try{r=t()}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return!n||r.__esModule?r:d(r,p(r),!0)}async function z(e,t,n){let r=await J(e,t);return await WebAssembly.instantiate(r,n)}async function J(e,t){let n=(function(e){let t=e.split("/").pop(),n=t.split(".").shift();return""===n?t:n})(t).replace(/[^a-zA-Z0-9$_]/gi,"_"),r=`wasm_${n}`,o=globalThis[r];if(!o)throw Error(`dynamically loading WebAssembly is not supported in this runtime and global \`${r}\` was not injected`);return o}X.resolve=(e,t)=>require.resolve(e,t),(()=>{e={registerChunk(e,o){t.add(e),function(e){let t=n.get(e);if(null!=t){for(let n of t)n.requiredChunks.delete(e),0===n.requiredChunks.size&&r(n.runtimeModuleIds,n.chunkPath);n.delete(e)}}(e),null!=o&&(0===o.otherChunks.length?r(o.runtimeModuleIds,e):function(e,o,u){let l=new Set,a={runtimeModuleIds:u,chunkPath:e,requiredChunks:l};for(let e of o){let r=O(e);if(t.has(r))continue;l.add(r);let o=n.get(r);null==o&&(o=new Set,n.set(r,o)),o.add(a)}0===a.requiredChunks.size&&r(a.runtimeModuleIds,a.chunkPath)}(e,o.otherChunks.filter(e=>{var t;return t=O(e),B.test(t)}),o.runtimeModuleIds))},loadChunk(e,t){throw Error("chunk loading is not supported")}};let t=new Set,n=new Map;function r(e,t){for(let n of e)!function(e,t){let n=H[e];if(n){if(n.error)throw n.error;return}W(e,{type:P.Runtime,chunkPath:t})}(n,t)}})();let Z=globalThis.TURBOPACK;globalThis.TURBOPACK={push:L},Z.forEach(L)})();

//# sourceMappingURL=edge-wrapper_2780f442.js.map