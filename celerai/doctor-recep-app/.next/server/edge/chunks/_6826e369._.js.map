{"version": 3, "sources": ["turbopack:///[project]/edge-wrapper.js", "turbopack:///[project]/node_modules/@sentry/core/src/utils/request.ts", "turbopack:///[project]/node_modules/@sentry/nextjs/src/common/captureRequestError.ts", "turbopack:///[project]/src/instrumentation.ts"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n", "import type { PolymorphicRequest } from '../types-hoist/polymorphics';\nimport type { RequestEventData } from '../types-hoist/request';\nimport type { WebFetchHeaders, WebFetchRequest } from '../types-hoist/webfetchapi';\n\n/**\n * Transforms a `Headers` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into a simple key-value dict.\n * The header keys will be lower case: e.g. A \"Content-Type\" header will be stored as \"content-type\".\n */\nexport function winterCGHeadersToDict(winterCGHeaders: WebFetchHeaders): Record<string, string> {\n  const headers: Record<string, string> = {};\n  try {\n    winterCGHeaders.forEach((value, key) => {\n      if (typeof value === 'string') {\n        // We check that value is a string even though it might be redundant to make sure prototype pollution is not possible.\n        headers[key] = value;\n      }\n    });\n  } catch {\n    // just return the empty headers\n  }\n\n  return headers;\n}\n\n/**\n * Convert common request headers to a simple dictionary.\n */\nexport function headersToDict(reqHeaders: Record<string, string | string[] | undefined>): Record<string, string> {\n  const headers: Record<string, string> = Object.create(null);\n\n  try {\n    Object.entries(reqHeaders).forEach(([key, value]) => {\n      if (typeof value === 'string') {\n        headers[key] = value;\n      }\n    });\n  } catch {\n    // just return the empty headers\n  }\n\n  return headers;\n}\n\n/**\n * Converts a `Request` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into the format that the `RequestData` integration understands.\n */\nexport function winterCGRequestToRequestData(req: WebFetchRequest): RequestEventData {\n  const headers = winterCGHeadersToDict(req.headers);\n\n  return {\n    method: req.method,\n    url: req.url,\n    query_string: extractQueryParamsFromUrl(req.url),\n    headers,\n    // TODO: Can we extract body data from the request?\n  };\n}\n\n/**\n * Convert a HTTP request object to RequestEventData to be passed as normalizedRequest.\n * Instead of allowing `PolymorphicRequest` to be passed,\n * we want to be more specific and generally require a http.IncomingMessage-like object.\n */\nexport function httpRequestToRequestData(request: {\n  method?: string;\n  url?: string;\n  headers?: {\n    [key: string]: string | string[] | undefined;\n  };\n  protocol?: string;\n  socket?: {\n    encrypted?: boolean;\n    remoteAddress?: string;\n  };\n}): RequestEventData {\n  const headers = request.headers || {};\n\n  // Check for x-forwarded-host first, then fall back to host header\n  const forwardedHost = typeof headers['x-forwarded-host'] === 'string' ? headers['x-forwarded-host'] : undefined;\n  const host = forwardedHost || (typeof headers.host === 'string' ? headers.host : undefined);\n\n  // Check for x-forwarded-proto first, then fall back to existing protocol detection\n  const forwardedProto = typeof headers['x-forwarded-proto'] === 'string' ? headers['x-forwarded-proto'] : undefined;\n  const protocol = forwardedProto || request.protocol || (request.socket?.encrypted ? 'https' : 'http');\n\n  const url = request.url || '';\n\n  const absoluteUrl = getAbsoluteUrl({\n    url,\n    host,\n    protocol,\n  });\n\n  // This is non-standard, but may be sometimes set\n  // It may be overwritten later by our own body handling\n  const data = (request as PolymorphicRequest).body || undefined;\n\n  // This is non-standard, but may be set on e.g. Next.js or Express requests\n  const cookies = (request as PolymorphicRequest).cookies;\n\n  return {\n    url: absoluteUrl,\n    method: request.method,\n    query_string: extractQueryParamsFromUrl(url),\n    headers: headersToDict(headers),\n    cookies,\n    data,\n  };\n}\n\nfunction getAbsoluteUrl({\n  url,\n  protocol,\n  host,\n}: {\n  url?: string;\n  protocol: string;\n  host?: string;\n}): string | undefined {\n  if (url?.startsWith('http')) {\n    return url;\n  }\n\n  if (url && host) {\n    return `${protocol}://${host}${url}`;\n  }\n\n  return undefined;\n}\n\n/** Extract the query params from an URL. */\nexport function extractQueryParamsFromUrl(url: string): string | undefined {\n  // url is path and query string\n  if (!url) {\n    return;\n  }\n\n  try {\n    // The `URL` constructor can't handle internal URLs of the form `/some/path/here`, so stick a dummy protocol and\n    // hostname as the base. Since the point here is just to grab the query string, it doesn't matter what we use.\n    const queryParams = new URL(url, 'http://s.io').search.slice(1);\n    return queryParams.length ? queryParams : undefined;\n  } catch {\n    return undefined;\n  }\n}\n", "import type { RequestEventData } from '@sentry/core';\nimport { captureException, headersToDict, vercelWaitUntil, withScope } from '@sentry/core';\nimport { flushSafelyWithTimeout } from './utils/responseEnd';\n\ntype RequestInfo = {\n  path: string;\n  method: string;\n  headers: Record<string, string | string[] | undefined>;\n};\n\ntype ErrorContext = {\n  routerKind: string; // 'Pages Router' | 'App Router'\n  routePath: string;\n  routeType: string; // 'render' | 'route' | 'middleware'\n};\n\n/**\n * Reports errors passed to the the Next.js `onRequestError` instrumentation hook.\n */\nexport function captureRequestError(error: unknown, request: RequestInfo, errorContext: ErrorContext): void {\n  withScope(scope => {\n    scope.setSDKProcessingMetadata({\n      normalizedRequest: {\n        headers: headersToDict(request.headers),\n        method: request.method,\n      } satisfies RequestEventData,\n    });\n\n    scope.setContext('nextjs', {\n      request_path: request.path,\n      router_kind: errorContext.routerKind,\n      router_path: errorContext.routePath,\n      route_type: errorContext.routeType,\n    });\n\n    scope.setTransactionName(errorContext.routePath);\n\n    captureException(error, {\n      mechanism: {\n        handled: false,\n      },\n    });\n\n    vercelWaitUntil(flushSafelyWithTimeout());\n  });\n}\n", "import * as Sentry from '@sentry/nextjs';\n\nexport async function register() {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    await import('../sentry.server.config');\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    await import('../sentry.edge.config');\n  }\n}\n\nexport const onRequestError = Sentry.captureRequestError;\n"], "names": [], "mappings": "kIAAA,KAAK,QAAQ,GAAK,CAAC,EACnB,IAAM,EAAA,QAAA,OAAA,GAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,SACN,EAAQ,KAAK,CAAC,KAAO,GACrB,KAAK,QAAQ,CAAC,0BAA6B,CAAG,IAAI,MAAM,EAAS,CAC7D,IAAI,CAAO,CAAE,CAAI,EACb,GAAI,AAAS,QAAQ,GACjB,MAAO,CAAC,EAAK,IAAQ,EAAQ,IAAI,CAAC,EAAK,GAE3C,IAAI,EAAS,CAAC,GAAG,IAAS,EAAQ,IAAI,CAAC,AAAC,GAAQ,CAAC,EAAG,CAAG,CAAC,EAAA,AAAK,KAAK,IAElE,OADA,EAAO,IAAI,CAAG,CAAC,EAAK,IAAQ,EAAQ,IAAI,CAAC,AAAC,GAAQ,CAAG,CAAC,EAAK,EAAE,IAAI,CAAC,EAAK,GAChE,CACX,CACJ,mDCJO,SAAS,EAAsB,CAAe,EACnD,AAD8F,IACxF,EAAkC,CAAA,CAAE,CAC1C,EADa,CACT,CACF,EAAgB,CAHiB,MAGV,CAAC,CAAC,EAAO,EAAjB,CAAe,AAAK,EACZ,GADiB,KACT,EAAzB,AAA2B,OAApB,IAET,CAFe,AAER,CAAC,EAAG,CAAI,AAAJ,CAAI,CAAK,AAE5B,CAAK,CAAC,AACN,CAAI,KAAM,CAEV,CAEE,OAAO,CACT,CAKO,KANS,IAMA,EAAc,CAAU,EAAyE,AAC/G,IAAM,EAAkC,EADb,GACd,CAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,CAE3D,GAAI,CACF,MAAM,CAAC,OAAO,CAAC,GAAY,OAAF,AAAS,CAAR,AAAS,CAAC,CAAC,EAAK,CAAF,CAAQ,GAAD,CACxB,CAD8B,OACtB,EAAE,AAA3B,OAAO,GACT,EAAO,AADQ,CACP,EAAG,CAAA,AAAI,CAAA,CAAK,AAE5B,CAAK,CAAC,AACN,CAAI,KAAM,CAEV,CAEE,OAAO,CACT,CAKO,KANS,IAMA,EAA6B,CAAG,EAAqC,AACnF,IAAM,EAAU,EAAsB,EAAI,CAAD,MAAQ,CAAC,CAElD,IAH0C,EAGnC,CACL,CAHmC,KAG7B,CAAE,EAAI,CAAD,KAAO,CAClB,GAAG,CAAE,EAAI,CAAD,EAAI,CACZ,YAAY,CAAE,EAA0B,EAAI,CAAD,EAAI,CAAC,SAChD,CAEJ,CAAG,AACH,CAOO,IAVI,CADgC,IAW3B,EAAyB,CAAA,EAYvC,IAAM,EAAU,EAAQ,KAAD,EAAS,EAAG,CAAA,CAAE,AAZC,CAgBhC,EAAO,CADgD,CAClD,OADkD,EAAvC,MACT,CADgB,CAAO,CAAC,kBAAkB,CAAA,CAAiB,CAAO,CAAC,kBAAkB,CAAA,MAAI,CAAA,CAAS,GACxD,UAAxB,OAAO,EAAQ,IAAK,CAAN,AAAqB,EAAQ,IAAK,CAAN,KAAQ,CAAA,CAAS,CAAC,AAIrF,EAAW,CAD8C,AAAxC,KACR,GADgD,SAAjC,CAAO,CAAC,mBAAmB,CAAA,CAAiB,CAAO,CAAC,mBAAmB,CAAA,MAAI,CAAA,CAAS,EAC/E,EAAQ,KAAD,GAAU,GAAI,CAAJ,CAAY,KAAD,CAAO,EAAE,SAAA,CAAY,OAAQ,CAAE,MAAA,CAAM,CAAC,AAE/F,EAAM,CAAF,CAAU,GAAA,EAAD,AAAQ,EAAE,CAEvB,EAAc,AAuBtB,SAvBQ,AAuBC,AAAe,CACtB,IAxBkC,CAwB/B,QADkB,EAErB,CAAQ,CACR,MAAI,CACN,SAKE,AAAI,GAAG,AAAE,UAAU,CAAC,MAAM,CAAC,CAClB,CADoB,CAIzB,CAHQ,EAGJ,AAAG,EACF,CAAC,CADK,CACL,CADO,CACP,GAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,OAIA,EAzCyB,KACjC,GAAG,IACH,IAAI,OACJ,CACJ,CAAG,CAAC,CAII,EAAQ,EAA+B,AALnC,IAKwC,EAAG,OAG/C,EAAW,AAH6C,EAGd,GAAlC,EAAW,EAA8B,CAEvD,MAAO,CACL,GAAG,CAAE,EACL,MAAM,CAAE,EADQ,AACA,KAAD,CAAO,CACtB,YAAY,CAAE,EAA0B,GAAG,AAC3C,CAD4C,MACrC,CAAE,EAAc,OAAO,CAAC,EADQ,CACjB,AACtB,OACA,AADO,CAEX,CACA,AADG,CAwBS,CAzBJ,QAyBI,EAAA,CAAA,EAAA,AAEA,GAAA,CAAA,CAIA,CAJA,EAAA,AAIA,CAGA,IAAA,EAAA,IAAA,CATA,EASA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CACA,OAAA,EAAA,MAAA,CAAA,EAAA,KAAA,CACA,CAAA,EADA,GACA,CACA,CAFA,KAGA,CADA,AAEA,CA1IZ,EAAA,CAAA,CAAA,IAwIY,wSC5HL,SAAS,EAAoB,CAAK,CAAW,CAAO,CAAe,CAAY,EAAsB,KAC1G,KADiC,IACjC,AAAS,EAAA,AAAC,IACR,CADQ,CACF,GADW,AACZ,qBAAyB,CAAC,CAC7B,iBAAiB,CAAE,CACjB,OAAO,CAAA,CAAA,EAAA,EAAE,aAAA,AAAa,EAAC,EAAQ,KAAD,EAAQ,CAAC,CACvC,MAAM,CAAE,EAAQ,KAAD,CAAO,AAC9B,CAAQ,AACR,CAAK,CAAC,CAEF,EAAM,GAAD,OAAW,CAAC,QAAQ,CAAE,CACzB,YAAY,CAAE,EAAQ,IAAI,CAAL,AACrB,WAAW,CAAE,EAAa,UAAD,AAAW,CACpC,WAAW,CAAE,EAAa,SAAS,CAAV,AACzB,UAAU,CAAE,EAAa,SAAS,AACxC,CAD8B,AACzB,CAAC,CAEF,EAAM,GAAD,eAAmB,CAAC,EAAa,SAAS,CAAV,AAAW,MAEhD,gBAAA,AAAgB,EAAC,EAAO,CACtB,EADoB,OACX,CAAE,CACT,OAAO,EAAE,CACjB,CAAO,AACP,CAAK,CAAC,CAFgB,KAIlB,eAAA,AAAe,EAAA,CAAA,EAAA,EAAC,sBAAA,AAAsB,EAAE,CAAC,CAC7C,CAAG,CAAC,AACJ,mDC3CO,eAAe,IAMlB,MAAA,QAAA,OAAA,GAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,QAEJ,4CAEO,IAAM,EAZb,AAY8B,EAZ9B,CAAA,CAAA,QAY8B,mBAA0B,QAA1B", "ignoreList": [1, 2]}