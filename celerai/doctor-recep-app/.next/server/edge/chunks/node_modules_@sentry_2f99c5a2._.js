(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["chunks/node_modules_@sentry_2f99c5a2._.js",{20992:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SDK_VERSION:()=>t});let t="9.31.0"}},441619:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({GLOBAL_OBJ:()=>t});let t=globalThis}},550361:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getGlobalSingleton:()=>o,getMainCarrier:()=>s,getSentryCarrier:()=>a});var r=e.i(20992),i=e.i(441619);function s(){return a(i.GLOBAL_OBJ),i.GLOBAL_OBJ}function a(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||r.SDK_VERSION,t[r.SDK_VERSION]=t[r.SDK_VERSION]||{}}function o(e,t,n=i.GLOBAL_OBJ){let s=n.__SENTRY__=n.__SENTRY__||{},a=s[r.SDK_VERSION]=s[r.SDK_VERSION]||{};return a[e]||(a[e]=t())}},653333:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({DEBUG_BUILD:()=>t});let t="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__}},877130:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({isDOMError:()=>a,isDOMException:()=>o,isElement:()=>d,isError:()=>r,isErrorEvent:()=>s,isEvent:()=>g,isInstanceOf:()=>m,isParameterizedString:()=>u,isPlainObject:()=>p,isPrimitive:()=>l,isRegExp:()=>f,isRequest:()=>E,isString:()=>c,isSyntheticEvent:()=>h,isThenable:()=>_,isVueViewModel:()=>S});let t=Object.prototype.toString;function r(e){switch(t.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return m(e,Error)}}function i(e,n){return t.call(e)===`[object ${n}]`}function s(e){return i(e,"ErrorEvent")}function a(e){return i(e,"DOMError")}function o(e){return i(e,"DOMException")}function c(e){return i(e,"String")}function u(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function l(e){return null===e||u(e)||"object"!=typeof e&&"function"!=typeof e}function p(e){return i(e,"Object")}function g(e){return"undefined"!=typeof Event&&m(e,Event)}function d(e){return"undefined"!=typeof Element&&m(e,Element)}function f(e){return i(e,"RegExp")}function _(e){return!!(e?.then&&"function"==typeof e.then)}function h(e){return p(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function m(e,t){try{return e instanceof t}catch(e){return!1}}function S(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function E(e){return"undefined"!=typeof Request&&m(e,Request)}}},385356:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({getComponentName:()=>a,getLocationHref:()=>s,htmlTreeAsString:()=>i});var r=e.i(877130);let t=e.i(441619).GLOBAL_OBJ;function i(e,n={}){if(!e)return"<unknown>";try{let i,s=e,a=[],o=0,c=0,u=Array.isArray(n)?n:n.keyAttrs,l=!Array.isArray(n)&&n.maxStringLength||80;for(;s&&o++<5&&(i=function(e,n){let i=[];if(!e?.tagName)return"";if(t.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}i.push(e.tagName.toLowerCase());let s=n?.length?n.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(s?.length)s.forEach(e=>{i.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&i.push(`#${e.id}`);let t=e.className;if(t&&(0,r.isString)(t))for(let e of t.split(/\s+/))i.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let n=e.getAttribute(t);n&&i.push(`[${t}="${n}"]`)}return i.join("")}(s,u),"html"!==i&&(!(o>1)||!(c+3*a.length+i.length>=l)));)a.push(i),c+=i.length,s=s.parentNode;return a.reverse().join(" > ")}catch(e){return"<unknown>"}}function s(){try{return t.document.location.href}catch(e){return""}}function a(e){if(!t.HTMLElement)return null;let n=e;for(let e=0;e<5&&n;e++){if(n instanceof HTMLElement){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}n=n.parentNode}return null}}},36650:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({CONSOLE_LEVELS:()=>t,consoleSandbox:()=>a,logger:()=>o,originalConsoleMethods:()=>n});var r=e.i(550361),i=e.i(653333),s=e.i(441619);let t=["debug","info","warn","error","log","assert","trace"],n={};function a(e){if(!("console"in s.GLOBAL_OBJ))return e();let t=s.GLOBAL_OBJ.console,r={},i=Object.keys(n);i.forEach(e=>{let i=n[e];r[e]=t[e],t[e]=i});try{return e()}finally{i.forEach(e=>{t[e]=r[e]})}}let o=(0,r.getGlobalSingleton)("logger",function(){let e=!1,n={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return i.DEBUG_BUILD?t.forEach(t=>{n[t]=(...n)=>{e&&a(()=>{s.GLOBAL_OBJ.console[t](`Sentry Logger [${t}]:`,...n)})}}):t.forEach(e=>{n[e]=()=>void 0}),n})}},483213:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({isMatchingPattern:()=>o,safeJoin:()=>a,snipLine:()=>s,stringMatchesSomePattern:()=>c,truncate:()=>i});var r=e.i(877130);function i(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function s(e,t){let n=e,r=n.length;if(r<=150)return n;t>r&&(t=r);let i=Math.max(t-60,0);i<5&&(i=0);let s=Math.min(i+140,r);return s>r-5&&(s=r),s===r&&(i=Math.max(s-140,0)),n=n.slice(i,s),i>0&&(n=`'{snip} ${n}`),s<r&&(n+=" {snip}"),n}function a(e,t){if(!Array.isArray(e))return"";let n=[];for(let t=0;t<e.length;t++){let i=e[t];try{(0,r.isVueViewModel)(i)?n.push("[VueViewModel]"):n.push(String(i))}catch(e){n.push("[value cannot be serialized]")}}return n.join(t)}function o(e,t,n=!1){return!!(0,r.isString)(e)&&((0,r.isRegExp)(t)?t.test(e):!!(0,r.isString)(t)&&(n?e===t:e.includes(t)))}function c(e,t=[],n=!1){return t.some(t=>o(e,t,n))}},227325:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({addNonEnumerableProperty:()=>u,convertToPlainObject:()=>g,dropUndefinedKeys:()=>h,extractExceptionKeysForMessage:()=>_,fill:()=>c,getOriginalFunction:()=>p,markFunctionWrapped:()=>l,objectify:()=>m});var r=e.i(653333),i=e.i(385356),s=e.i(877130),a=e.i(36650),o=e.i(483213);function c(e,t,n){if(!(t in e))return;let i=e[t];if("function"!=typeof i)return;let s=n(i);"function"==typeof s&&l(s,i);try{e[t]=s}catch{r.DEBUG_BUILD&&a.logger.log(`Failed to replace method "${t}" in object`,e)}}function u(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch(n){r.DEBUG_BUILD&&a.logger.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function l(e,t){try{let n=t.prototype||{};e.prototype=t.prototype=n,u(e,"__sentry_original__",t)}catch(e){}}function p(e){return e.__sentry_original__}function g(e){if((0,s.isError)(e))return{message:e.message,name:e.name,stack:e.stack,...f(e)};if(!(0,s.isEvent)(e))return e;{let t={type:e.type,target:d(e.target),currentTarget:d(e.currentTarget),...f(e)};return"undefined"!=typeof CustomEvent&&(0,s.isInstanceOf)(e,CustomEvent)&&(t.detail=e.detail),t}}function d(e){try{return(0,s.isElement)(e)?(0,i.htmlTreeAsString)(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function f(e){if("object"!=typeof e||null===e)return{};{let t={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}function _(e,t=40){let n=Object.keys(g(e));n.sort();let r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return(0,o.truncate)(r,t);for(let e=n.length;e>0;e--){let r=n.slice(0,e).join(", ");if(!(r.length>t)){if(e===n.length)return r;return(0,o.truncate)(r,t)}}return""}function h(e){return function e(t,n){if(null===t||"object"!=typeof t)return t;let r=n.get(t);if(void 0!==r)return r;if(Array.isArray(t)){let r=[];return n.set(t,r),t.forEach(t=>{r.push(e(t,n))}),r}if(function(e){let t=e.constructor;return t===Object||void 0===t}(t)){let r={};return n.set(t,r),Object.keys(t).forEach(i=>{let s=t[i];void 0!==s&&(r[i]=e(s,n))}),r}return t}(e,new Map)}function m(e){let t;switch(!0){case void 0==e:t=new String(e);break;case"symbol"==typeof e||"bigint"==typeof e:t=Object(e);break;case(0,s.isPrimitive)(e):t=new e.constructor(e);break;default:t=e}return t}},416971:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addContextToFrame:()=>d,addExceptionMechanism:()=>l,addExceptionTypeValue:()=>u,checkOrSetAlreadyCaught:()=>f,getEventDescription:()=>c,parseSemver:()=>g,uuid4:()=>a});var r=e.i(227325),i=e.i(483213),s=e.i(441619);function a(e=function(){let e=s.GLOBAL_OBJ;return e.crypto||e.msCrypto}()){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function o(e){return e.exception?.values?.[0]}function c(e){let{message:t,event_id:n}=e;if(t)return t;let r=o(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function u(e,t,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],s=i[0]=i[0]||{};s.value||(s.value=t||""),s.type||(s.type=n||"Error")}function l(e,t){let n=o(e);if(!n)return;let r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){let e={...r?.data,...t.data};n.mechanism.data=e}}let t=/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;function p(e){return parseInt(e||"",10)}function g(e){let n=e.match(t)||[],r=p(n[1]),i=p(n[2]),s=p(n[3]);return{buildmetadata:n[5],major:isNaN(r)?void 0:r,minor:isNaN(i)?void 0:i,patch:isNaN(s)?void 0:s,prerelease:n[4]}}function d(e,t,n=5){if(void 0===t.lineno)return;let r=e.length,s=Math.max(Math.min(r-1,t.lineno-1),0);t.pre_context=e.slice(Math.max(0,s-n),s).map(e=>(0,i.snipLine)(e,0));let a=Math.min(r-1,s);t.context_line=(0,i.snipLine)(e[a],t.colno||0),t.post_context=e.slice(Math.min(s+1,r),s+1+n).map(e=>(0,i.snipLine)(e,0))}function f(e){if(function(e){try{return e.__sentry_captured__}catch{}}(e))return!0;try{(0,r.addNonEnumerableProperty)(e,"__sentry_captured__",!0)}catch(e){}return!1}}},321858:e=>{"use strict";var{g:t,__dirname:n}=e;{let t;e.s({browserPerformanceTimeOrigin:()=>s,dateTimestampInSeconds:()=>i,timestampInSeconds:()=>n});var r=e.i(441619);function i(){return Date.now()/1e3}let n=function(){let{performance:e}=r.GLOBAL_OBJ;if(!e?.now)return i;let t=Date.now()-e.now(),n=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(n+e.now())/1e3}();function s(){return t||(t=function(){let{performance:e}=r.GLOBAL_OBJ;if(!e?.now)return[void 0,"none"];let t=e.now(),n=Date.now(),i=e.timeOrigin?Math.abs(e.timeOrigin+t-n):36e5,s=e.timing?.navigationStart,a="number"==typeof s?Math.abs(s+t-n):36e5;if(i<36e5||a<36e5)if(i<=a)return[e.timeOrigin,"timeOrigin"];else return[s,"navigationStart"];return[n,"dateNow"]}()),t[0]}}},447489:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({closeSession:()=>o,makeSession:()=>s,updateSession:()=>a});var r=e.i(416971),i=e.i(321858);function s(e){let t=(0,i.timestampInSeconds)(),n={sid:(0,r.uuid4)(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>{var e;return e=n,{sid:`${e.sid}`,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}}}};return e&&a(n,e),n}function a(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||(0,i.timestampInSeconds)(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:(0,r.uuid4)()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function o(e,t){let n={};t?n={status:t}:"ok"===e.status&&(n={status:"exited"}),a(e,n)}},851195:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({merge:()=>function e(t,n,r=2){if(!n||"object"!=typeof n||r<=0)return n;if(t&&0===Object.keys(n).length)return t;let i={...t};for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&(i[t]=e(i[t],n[t],r-1));return i}})},848532:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({generateSpanId:()=>s,generateTraceId:()=>i});var r=e.i(416971);function i(){return(0,r.uuid4)()}function s(){return(0,r.uuid4)().substring(16)}},671442:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({_getSpanForScope:()=>s,_setSpanForScope:()=>i});var r=e.i(227325);let t="_sentrySpan";function i(e,n){n?(0,r.addNonEnumerableProperty)(e,t,n):delete e[t]}function s(e){return e[t]}}},495850:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({Scope:()=>t});var r=e.i(447489),i=e.i(877130),s=e.i(36650),a=e.i(851195),o=e.i(416971),c=e.i(848532),u=e.i(671442),l=e.i(483213),p=e.i(321858);class t{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:(0,c.generateTraceId)(),sampleRand:Math.random()}}clone(){let e=new t;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,(0,u._setSpanForScope)(e,(0,u._getSpanForScope)(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&(0,r.updateSession)(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let n="function"==typeof e?e(this):e,{tags:r,extra:s,user:a,contexts:o,level:c,fingerprint:u=[],propagationContext:l}=(n instanceof t?n.getScopeData():(0,i.isPlainObject)(n)?e:void 0)||{};return this._tags={...this._tags,...r},this._extra={...this._extra,...s},this._contexts={...this._contexts,...o},a&&Object.keys(a).length&&(this._user=a),c&&(this._level=c),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,(0,u._setSpanForScope)(this,void 0),this._attachments=[],this.setPropagationContext({traceId:(0,c.generateTraceId)(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let n="number"==typeof t?t:100;if(n<=0)return this;let r={timestamp:(0,p.dateTimestampInSeconds)(),...e,message:e.message?(0,l.truncate)(e.message,2048):e.message};return this._breadcrumbs.push(r),this._breadcrumbs.length>n&&(this._breadcrumbs=this._breadcrumbs.slice(-n),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:(0,u._getSpanForScope)(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=(0,a.merge)(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let n=t?.event_id||(0,o.uuid4)();if(!this._client)return s.logger.warn("No client configured on scope - will not capture exception!"),n;let r=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},this),n}captureMessage(e,t,n){let r=n?.event_id||(0,o.uuid4)();if(!this._client)return s.logger.warn("No client configured on scope - will not capture message!"),r;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...n,event_id:r},this),r}captureEvent(e,t){let n=t?.event_id||(0,o.uuid4)();return this._client?this._client.captureEvent(e,{...t,event_id:n},this):s.logger.warn("No client configured on scope - will not capture event!"),n}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}}},173666:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getDefaultCurrentScope:()=>s,getDefaultIsolationScope:()=>a});var r=e.i(550361),i=e.i(495850);function s(){return(0,r.getGlobalSingleton)("defaultCurrentScope",()=>new i.Scope)}function a(){return(0,r.getGlobalSingleton)("defaultIsolationScope",()=>new i.Scope)}},444183:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({AsyncContextStack:()=>t,getStackAsyncContextStrategy:()=>p});var r=e.i(173666),i=e.i(495850),s=e.i(877130),a=e.i(550361);class t{constructor(e,t){let n,r;n=e||new i.Scope,r=t||new i.Scope,this._stack=[{scope:n}],this._isolationScope=r}withScope(e){let t,n=this._pushScope();try{t=e(n)}catch(e){throw this._popScope(),e}return(0,s.isThenable)(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function o(){let e=(0,a.getMainCarrier)(),n=(0,a.getSentryCarrier)(e);return n.stack=n.stack||new t((0,r.getDefaultCurrentScope)(),(0,r.getDefaultIsolationScope)())}function c(e){return o().withScope(e)}function u(e,t){let n=o();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function l(e){return o().withScope(()=>e(o().getIsolationScope()))}function p(){return{withIsolationScope:l,withScope:c,withSetScope:u,withSetIsolationScope:(e,t)=>l(t),getCurrentScope:()=>o().getScope(),getIsolationScope:()=>o().getIsolationScope()}}}},230098:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getAsyncContextStrategy:()=>a,setAsyncContextStrategy:()=>s});var r=e.i(550361),i=e.i(444183);function s(e){let t=(0,r.getMainCarrier)();(0,r.getSentryCarrier)(t).acs=e}function a(e){let t=(0,r.getSentryCarrier)(e);return t.acs?t.acs:(0,i.getStackAsyncContextStrategy)()}},548729:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({getClient:()=>g,getCurrentScope:()=>o,getGlobalScope:()=>u,getIsolationScope:()=>c,getTraceContextFromScope:()=>d,withIsolationScope:()=>p,withScope:()=>l});var r=e.i(230098),i=e.i(550361),s=e.i(495850),a=e.i(848532);function o(){let e=(0,i.getMainCarrier)();return(0,r.getAsyncContextStrategy)(e).getCurrentScope()}function c(){let e=(0,i.getMainCarrier)();return(0,r.getAsyncContextStrategy)(e).getIsolationScope()}function u(){return(0,i.getGlobalSingleton)("globalScope",()=>new s.Scope)}function l(...e){let t=(0,i.getMainCarrier)(),n=(0,r.getAsyncContextStrategy)(t);if(2===e.length){let[t,r]=e;return t?n.withSetScope(t,r):n.withScope(r)}return n.withScope(e[0])}function p(...e){let t=(0,i.getMainCarrier)(),n=(0,r.getAsyncContextStrategy)(t);if(2===e.length){let[t,r]=e;return t?n.withSetIsolationScope(t,r):n.withIsolationScope(r)}return n.withIsolationScope(e[0])}function g(){return o().getClient()}function d(e){let{traceId:t,parentSpanId:n,propagationSpanId:r}=e.getPropagationContext(),i={trace_id:t,span_id:r||(0,a.generateSpanId)()};return n&&(i.parent_span_id=n),i}},312268:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({DEFAULT_ENVIRONMENT:()=>t});let t="production"}},783296:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SyncPromise:()=>t,rejectedSyncPromise:()=>a,resolvedSyncPromise:()=>s});var r,i=e.i(877130);function s(e){return new t(t=>{t(e)})}function a(e){return new t((t,n)=>{n(e)})}!function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(r||(r={}));class t{constructor(e){this._state=r.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new t((t,r)=>{this._handlers.push([!1,n=>{if(e)try{t(e(n))}catch(e){r(e)}else t(n)},e=>{if(n)try{t(n(e))}catch(e){r(e)}else r(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new t((t,n)=>{let r,i;return this.then(t=>{i=!1,r=t,e&&e()},t=>{i=!0,r=t,e&&e()}).then(()=>{if(i)return void n(r);t(r)})})}_executeHandlers(){if(this._state===r.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===r.RESOLVED&&e[1](this._value),this._state===r.REJECTED&&e[2](this._value),e[0]=!0)})}_runExecutor(e){let t=(e,t)=>{if(this._state===r.PENDING){if((0,i.isThenable)(t))return void t.then(n,s);this._state=e,this._value=t,this._executeHandlers()}},n=e=>{t(r.RESOLVED,e)},s=e=>{t(r.REJECTED,e)};try{e(n,s)}catch(e){s(e)}}}}},870958:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({notifyEventProcessors:()=>function e(t,n,o,c=0){return new a.SyncPromise((a,u)=>{let l=t[c];if(null===n||"function"!=typeof l)a(n);else{let p=l({...n},o);r.DEBUG_BUILD&&l.id&&null===p&&s.logger.log(`Event processor "${l.id}" dropped event`),(0,i.isThenable)(p)?p.then(n=>e(t,n,o,c+1).then(a)).then(null,u):e(t,p,o,c+1).then(a).then(null,u)}})}});var r=e.i(653333),i=e.i(877130),s=e.i(36650),a=e.i(783296)},325939:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SEMANTIC_ATTRIBUTE_CACHE_HIT:()=>g,SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE:()=>f,SEMANTIC_ATTRIBUTE_CACHE_KEY:()=>d,SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME:()=>p,SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD:()=>_,SEMANTIC_ATTRIBUTE_PROFILE_ID:()=>l,SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME:()=>u,SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON:()=>a,SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT:()=>o,SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE:()=>c,SEMANTIC_ATTRIBUTE_SENTRY_OP:()=>i,SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN:()=>s,SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE:()=>r,SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE:()=>n,SEMANTIC_ATTRIBUTE_SENTRY_SOURCE:()=>t,SEMANTIC_ATTRIBUTE_URL_FULL:()=>h,SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE:()=>m});let t="sentry.source",n="sentry.sample_rate",r="sentry.previous_trace_sample_rate",i="sentry.op",s="sentry.origin",a="sentry.idle_span_finish_reason",o="sentry.measurement_unit",c="sentry.measurement_value",u="sentry.custom_span_name",l="sentry.profile_id",p="sentry.exclusive_time",g="cache.hit",d="cache.key",f="cache.item_size",_="http.request.method",h="url.full",m="sentry.link.type"}},257562:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({MAX_BAGGAGE_STRING_LENGTH:()=>p,SENTRY_BAGGAGE_KEY_PREFIX:()=>t,SENTRY_BAGGAGE_KEY_PREFIX_REGEX:()=>n,baggageHeaderToDynamicSamplingContext:()=>a,dynamicSamplingContextToSentryBaggageHeader:()=>o,objectToBaggageHeader:()=>l,parseBaggageHeader:()=>c});var r=e.i(653333),i=e.i(877130),s=e.i(36650);let t="sentry-",n=/^sentry-/,p=8192;function a(e){let r=c(e);if(!r)return;let i=Object.entries(r).reduce((e,[r,i])=>(r.match(n)&&(e[r.slice(t.length)]=i),e),{});return Object.keys(i).length>0?i:void 0}function o(e){if(e)return l(Object.entries(e).reduce((e,[n,r])=>(r&&(e[`${t}${n}`]=r),e),{}))}function c(e){if(e&&((0,i.isString)(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(u(t)).forEach(([t,n])=>{e[t]=n}),e),{}):u(e)}function u(e){return e.split(",").map(e=>e.split("=").map(e=>{try{return decodeURIComponent(e.trim())}catch{return}})).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}function l(e){if(0!==Object.keys(e).length)return Object.entries(e).reduce((e,[t,n],i)=>{let a=`${encodeURIComponent(t)}=${encodeURIComponent(n)}`,o=0===i?a:`${e},${a}`;return o.length>p?(r.DEBUG_BUILD&&s.logger.warn(`Not adding key: ${t} with val: ${n} to baggage header due to exceeding baggage size limits.`),e):o},"")}}},869033:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({dsnFromString:()=>a,dsnToString:()=>s,extractOrgIdFromDsnHost:()=>c,makeDsn:()=>u});var r=e.i(653333),i=e.i(36650);let t=/^o(\d+)\./,n=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function s(e,t=!1){let{host:n,path:r,pass:i,port:a,projectId:o,protocol:c,publicKey:u}=e;return`${c}://${u}${t&&i?`:${i}`:""}@${n}${a?`:${a}`:""}/${r?`${r}/`:r}${o}`}function a(e){let t=n.exec(e);if(!t)return void(0,i.consoleSandbox)(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[r,s,a="",c="",u="",l=""]=t.slice(1),p="",g=l,d=g.split("/");if(d.length>1&&(p=d.slice(0,-1).join("/"),g=d.pop()),g){let e=g.match(/^\d+/);e&&(g=e[0])}return o({host:c,pass:a,path:p,projectId:g,port:u,protocol:r,publicKey:s})}function o(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function c(e){let n=e.match(t);return n?.[1]}function u(e){let t="string"==typeof e?a(e):o(e);if(t&&function(e){if(!r.DEBUG_BUILD)return!0;let{port:t,projectId:n,protocol:s}=e;return!["protocol","publicKey","host","projectId"].find(t=>!e[t]&&(i.logger.error(`Invalid Sentry Dsn: ${t} missing`),!0))&&(n.match(/^\d+$/)?"http"!==s&&"https"!==s?(i.logger.error(`Invalid Sentry Dsn: Invalid protocol ${s}`),!1):!(t&&isNaN(parseInt(t,10)))||(i.logger.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):(i.logger.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(t))return t}}},988191:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({hasSpansEnabled:()=>i,hasTracingEnabled:()=>t});var r=e.i(548729);function i(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||(0,r.getClient)()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}let t=i}},883270:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SPAN_STATUS_ERROR:()=>s,SPAN_STATUS_OK:()=>n,SPAN_STATUS_UNSET:()=>t,getSpanStatusFromHttpCode:()=>r,setHttpStatus:()=>i});let t=0,n=1,s=2;function r(e){if(e<400&&e>=100)return{code:n};if(e>=400&&e<500)switch(e){case 401:return{code:s,message:"unauthenticated"};case 403:return{code:s,message:"permission_denied"};case 404:return{code:s,message:"not_found"};case 409:return{code:s,message:"already_exists"};case 413:return{code:s,message:"failed_precondition"};case 429:return{code:s,message:"resource_exhausted"};case 499:return{code:s,message:"cancelled"};default:return{code:s,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:s,message:"unimplemented"};case 503:return{code:s,message:"unavailable"};case 504:return{code:s,message:"deadline_exceeded"};default:return{code:s,message:"internal_error"}}return{code:s,message:"unknown_error"}}function i(e,t){e.setAttribute("http.response.status_code",t);let n=r(t);"unknown_error"!==n.message&&e.setStatus(n)}}},673189:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({getCapturedScopesOnSpan:()=>s,setCapturedScopesOnSpan:()=>i});var r=e.i(227325);let t="_sentryScope",n="_sentryIsolationScope";function i(e,i,s){e&&((0,r.addNonEnumerableProperty)(e,n,s),(0,r.addNonEnumerableProperty)(e,t,i))}function s(e){return{scope:e[t],isolationScope:e[n]}}}},678436:e=>{"use strict";var{g:t,__dirname:n}=e;function r(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}e.s({parseSampleRate:()=>r})},219422:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({TRACEPARENT_REGEXP:()=>t,extractTraceparentData:()=>a,generateSentryTraceHeader:()=>c,propagationContextFromHeaders:()=>o});var r=e.i(257562),i=e.i(678436),s=e.i(848532);let t=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function a(e){let n;if(!e)return;let r=e.match(t);if(r)return"1"===r[3]?n=!0:"0"===r[3]&&(n=!1),{traceId:r[1],parentSampled:n,parentSpanId:r[2]}}function o(e,t){let n=a(e),o=(0,r.baggageHeaderToDynamicSamplingContext)(t);if(!n?.traceId)return{traceId:(0,s.generateTraceId)(),sampleRand:Math.random()};let c=function(e,t){let n=(0,i.parseSampleRate)(t?.sample_rand);if(void 0!==n)return n;let r=(0,i.parseSampleRate)(t?.sample_rate);return r&&e?.parentSampled!==void 0?e.parentSampled?Math.random()*r:r+Math.random()*(1-r):Math.random()}(n,o);o&&(o.sample_rand=c.toString());let{traceId:u,parentSpanId:l,parentSampled:p}=n;return{traceId:u,parentSpanId:l,sampled:p,dsc:o||{},sampleRand:c}}function c(e=(0,s.generateTraceId)(),t=(0,s.generateSpanId)(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${e}-${t}${r}`}}},526321:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({TRACE_FLAG_NONE:()=>t,TRACE_FLAG_SAMPLED:()=>n,addChildSpanToSpan:()=>I,convertSpanLinksForEnvelope:()=>S,getActiveSpan:()=>N,getRootSpan:()=>x,getSpanDescendants:()=>A,getStatusMessage:()=>b,removeChildSpanFromSpan:()=>C,showSpanDropWarning:()=>O,spanIsSampled:()=>T,spanTimeInputToSeconds:()=>E,spanToJSON:()=>v,spanToTraceContext:()=>h,spanToTraceHeader:()=>m,spanToTransactionTraceContext:()=>_,updateSpanName:()=>R});var r=e.i(230098),i=e.i(550361),s=e.i(548729),a=e.i(325939),o=e.i(883270),c=e.i(673189),u=e.i(36650),l=e.i(227325),p=e.i(848532),g=e.i(321858),d=e.i(219422),f=e.i(671442);let t=0,n=1,D=!1;function _(e){let{spanId:t,traceId:n}=e.spanContext(),{data:r,op:i,parent_span_id:s,status:a,origin:o,links:c}=v(e);return{parent_span_id:s,span_id:t,trace_id:n,data:r,op:i,status:a,origin:o,links:c}}function h(e){let{spanId:t,traceId:n,isRemote:r}=e.spanContext(),i=r?t:v(e).parent_span_id,s=(0,c.getCapturedScopesOnSpan)(e).scope;return{parent_span_id:i,span_id:r?s?.getPropagationContext().propagationSpanId||(0,p.generateSpanId)():t,trace_id:n}}function m(e){let{traceId:t,spanId:n}=e.spanContext(),r=T(e);return(0,d.generateSentryTraceHeader)(t,n,r)}function S(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:r,...i},attributes:s})=>({span_id:e,trace_id:t,sampled:r===n,attributes:s,...i})):void 0}function E(e){return"number"==typeof e?y(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?y(e.getTime()):(0,g.timestampInSeconds)()}function y(e){return e>0x2540be3ff?e/1e3:e}function v(e){var t;if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:n,traceId:r}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:s,endTime:o,status:c,links:u}=e;return{span_id:n,trace_id:r,data:t,description:s,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0,start_timestamp:E(i),timestamp:E(o)||void 0,status:b(c),op:t[a.SEMANTIC_ATTRIBUTE_SENTRY_OP],origin:t[a.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN],links:S(u)}}return{span_id:n,trace_id:r,start_timestamp:0,data:{}}}function T(e){let{traceFlags:t}=e.spanContext();return t===n}function b(e){if(e&&e.code!==o.SPAN_STATUS_UNSET)return e.code===o.SPAN_STATUS_OK?"ok":e.message||"unknown_error"}let M="_sentryChildSpans",L="_sentryRootSpan";function I(e,t){let n=e[L]||e;(0,l.addNonEnumerableProperty)(t,L,n),e[M]?e[M].add(t):(0,l.addNonEnumerableProperty)(e,M,new Set([t]))}function C(e,t){e[M]&&e[M].delete(t)}function A(e){let t=new Set;return!function e(n){if(!t.has(n)&&T(n))for(let r of(t.add(n),n[M]?Array.from(n[M]):[]))e(r)}(e),Array.from(t)}function x(e){return e[L]||e}function N(){let e=(0,i.getMainCarrier)(),t=(0,r.getAsyncContextStrategy)(e);return t.getActiveSpan?t.getActiveSpan():(0,f._getSpanForScope)((0,s.getCurrentScope)())}function O(){D||((0,u.consoleSandbox)(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),D=!0)}function R(e,t){e.updateName(t),e.setAttributes({[a.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"custom",[a.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]:t})}}},872150:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({freezeDscOnSpan:()=>g,getDynamicSamplingContextFromClient:()=>d,getDynamicSamplingContextFromScope:()=>f,getDynamicSamplingContextFromSpan:()=>_,spanToBaggageHeader:()=>h});var r=e.i(312268),i=e.i(548729),s=e.i(325939),a=e.i(257562),o=e.i(869033),c=e.i(988191),u=e.i(227325),l=e.i(526321),p=e.i(673189);let t="_frozenDsc";function g(e,n){(0,u.addNonEnumerableProperty)(e,t,n)}function d(e,t){let n,i=t.getOptions(),{publicKey:s,host:a}=t.getDsn()||{};i.orgId?n=String(i.orgId):a&&(n=(0,o.extractOrgIdFromDsnHost)(a));let c={environment:i.environment||r.DEFAULT_ENVIRONMENT,release:i.release,public_key:s,trace_id:e,org_id:n};return t.emit("createDsc",c),c}function f(e,t){let n=t.getPropagationContext();return n.dsc||d(n.traceId,e)}function _(e){let n=(0,i.getClient)();if(!n)return{};let r=(0,l.getRootSpan)(e),o=(0,l.spanToJSON)(r),u=o.data,g=r.spanContext().traceState,f=g?.get("sentry.sample_rate")??u[s.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]??u[s.SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE];function _(e){return("number"==typeof f||"string"==typeof f)&&(e.sample_rate=`${f}`),e}let h=r[t];if(h)return _(h);let m=g?.get("sentry.dsc"),S=m&&(0,a.baggageHeaderToDynamicSamplingContext)(m);if(S)return _(S);let E=d(e.spanContext().traceId,n),y=u[s.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],v=o.description;return"url"!==y&&v&&(E.transaction=v),(0,c.hasSpansEnabled)()&&(E.sampled=String((0,l.spanIsSampled)(r)),E.sample_rand=g?.get("sentry.sample_rand")??(0,p.getCapturedScopesOnSpan)(r).scope?.getPropagationContext().sampleRand.toString()),_(E),n.emit("createDsc",E,r),E}function h(e){let t=_(e);return(0,a.dynamicSamplingContextToSentryBaggageHeader)(t)}}},885708:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({applyScopeDataToEvent:()=>a,mergeAndOverwriteScopeData:()=>c,mergeScopeData:()=>o});var r=e.i(872150),i=e.i(851195),s=e.i(526321);function a(e,t){var n,i,a,o;let{fingerprint:c,span:u,breadcrumbs:l,sdkProcessingMetadata:p}=t;(function(e,t){let{extra:n,tags:r,user:i,contexts:s,level:a,transactionName:o}=t;Object.keys(n).length&&(e.extra={...n,...e.extra}),Object.keys(r).length&&(e.tags={...r,...e.tags}),Object.keys(i).length&&(e.user={...i,...e.user}),Object.keys(s).length&&(e.contexts={...s,...e.contexts}),a&&(e.level=a),o&&"transaction"!==e.type&&(e.transaction=o)})(e,t),u&&function(e,t){e.contexts={trace:(0,s.spanToTraceContext)(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:(0,r.getDynamicSamplingContextFromSpan)(t),...e.sdkProcessingMetadata};let n=(0,s.getRootSpan)(t),i=(0,s.spanToJSON)(n).description;i&&!e.transaction&&"transaction"===e.type&&(e.transaction=i)}(e,u),n=e,i=c,n.fingerprint=n.fingerprint?Array.isArray(n.fingerprint)?n.fingerprint:[n.fingerprint]:[],i&&(n.fingerprint=n.fingerprint.concat(i)),n.fingerprint.length||delete n.fingerprint,function(e,t){let n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}(e,l),a=e,o=p,a.sdkProcessingMetadata={...a.sdkProcessingMetadata,...o}}function o(e,t){let{extra:n,tags:r,user:s,contexts:a,level:o,sdkProcessingMetadata:u,breadcrumbs:l,fingerprint:p,eventProcessors:g,attachments:d,propagationContext:f,transactionName:_,span:h}=t;c(e,"extra",n),c(e,"tags",r),c(e,"user",s),c(e,"contexts",a),e.sdkProcessingMetadata=(0,i.merge)(e.sdkProcessingMetadata,u,2),o&&(e.level=o),_&&(e.transactionName=_),h&&(e.span=h),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),p.length&&(e.fingerprint=[...e.fingerprint,...p]),g.length&&(e.eventProcessors=[...e.eventProcessors,...g]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...f}}function c(e,t,n){e[t]=(0,i.merge)(e[t],n,1)}},359392:e=>{"use strict";var{g:t,__dirname:n}=e;{let t,n,a;e.s({getDebugImagesForResources:()=>s,getFilenameToDebugIdMap:()=>i});var r=e.i(441619);function i(e){let i=r.GLOBAL_OBJ._sentryDebugIds;if(!i)return{};let s=Object.keys(i);return a&&s.length===n?a:(n=s.length,a=s.reduce((n,r)=>{t||(t={});let s=t[r];if(s)n[s[0]]=s[1];else{let s=e(r);for(let e=s.length-1;e>=0;e--){let a=s[e],o=a?.filename,c=i[r];if(o&&c){n[o]=c,t[r]=[o,c];break}}}return n},{}))}function s(e,t){let n=i(e);if(!n)return[];let r=[];for(let e of t)e&&n[e]&&r.push({type:"sourcemap",code_file:e,debug_id:n[e]});return r}}},732762:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({UNKNOWN_FUNCTION:()=>t,createStackParser:()=>r,getFramesFromEvent:()=>c,getFunctionName:()=>o,stackParserFromStackParserOptions:()=>i,stripSentryFramesAndReverse:()=>s});let t="?",n=/\(error: (.*)\)/,u=/captureMessage|captureException/;function r(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0,i=0)=>{let a=[],o=e.split("\n");for(let e=r;e<o.length;e++){let r=o[e];if(r.length>1024)continue;let s=n.test(r)?r.replace(n,"$1"):r;if(!s.match(/\S*Error: /)){for(let e of t){let t=e(s);if(t){a.push(t);break}}if(a.length>=50+i)break}}return s(a.slice(i))}}function i(e){return Array.isArray(e)?r(...e):e}function s(e){if(!e.length)return[];let n=Array.from(e);return/sentryWrapped/.test(a(n).function||"")&&n.pop(),n.reverse(),u.test(a(n).function||"")&&(n.pop(),u.test(a(n).function||"")&&n.pop()),n.slice(0,50).map(e=>({...e,filename:e.filename||a(n).filename,function:e.function||t}))}function a(e){return e[e.length-1]||{}}let l="<anonymous>";function o(e){try{if(!e||"function"!=typeof e)return l;return e.name||l}catch(e){return l}}function c(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){}}}}},369674:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({normalize:()=>a,normalizeToSize:()=>function e(t,n=3,r=102400){let i=a(t,n);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>r?e(t,n-1,r):i},normalizeUrlToBase:()=>o});var r=e.i(877130),i=e.i(227325),s=e.i(732762);function a(e,n=100,o=Infinity){try{return function e(n,a,o=Infinity,c=Infinity,u=function(){let e=new WeakSet;return[function(t){return!!e.has(t)||(e.add(t),!1)},function(t){e.delete(t)}]}()){let[l,p]=u;if(null==a||["boolean","string"].includes(typeof a)||"number"==typeof a&&Number.isFinite(a))return a;let g=function(e,n){try{if("domain"===e&&n&&"object"==typeof n&&n._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if(void 0!==t&&n===t)return"[Global]";if("undefined"!=typeof document&&n===document)return"[Document]";if((0,r.isVueViewModel)(n))return"[VueViewModel]";if((0,r.isSyntheticEvent)(n))return"[SyntheticEvent]";if("number"==typeof n&&!Number.isFinite(n))return`[${n}]`;if("function"==typeof n)return`[Function: ${(0,s.getFunctionName)(n)}]`;if("symbol"==typeof n)return`[${String(n)}]`;if("bigint"==typeof n)return`[BigInt: ${String(n)}]`;let i=function(e){let t=Object.getPrototypeOf(e);return t?.constructor?t.constructor.name:"null prototype"}(n);if(/^HTML(\w*)Element$/.test(i))return`[HTMLElement: ${i}]`;return`[object ${i}]`}catch(e){return`**non-serializable** (${e})`}}(n,a);if(!g.startsWith("[object "))return g;if(a.__sentry_skip_normalization__)return a;let d="number"==typeof a.__sentry_override_normalization_depth__?a.__sentry_override_normalization_depth__:o;if(0===d)return g.replace("object ","");if(l(a))return"[Circular ~]";if(a&&"function"==typeof a.toJSON)try{let t=a.toJSON();return e("",t,d-1,c,u)}catch(e){}let f=Array.isArray(a)?[]:{},_=0,h=(0,i.convertToPlainObject)(a);for(let t in h){if(!Object.prototype.hasOwnProperty.call(h,t))continue;if(_>=c){f[t]="[MaxProperties ~]";break}let n=h[t];f[t]=e(t,n,d-1,c,u),_++}return p(a),f}("",e,n,o)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function o(e,t){let n=t.replace(/\\/g,"/").replace(/[|\\{}()[\]^$+*?.]/g,"\\$&"),r=e;try{r=decodeURI(e)}catch(e){}return r.replace(/\\/g,"/").replace(/webpack:\/?/g,"").replace(RegExp(`(file://)?/*${n}/*`,"ig"),"app:///")}},265405:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({applyClientOptions:()=>f,applyDebugIds:()=>_,applyDebugMeta:()=>h,parseEventHintOrCaptureContext:()=>m,prepareEvent:()=>d});var r=e.i(312268),i=e.i(548729),s=e.i(870958),a=e.i(495850),o=e.i(885708),c=e.i(359392),u=e.i(416971),l=e.i(369674),p=e.i(483213),g=e.i(321858);function d(e,t,n,r,c,p){var d,m;let{normalizeDepth:S=3,normalizeMaxBreadth:E=1e3}=e,y={...t,event_id:t.event_id||n.event_id||(0,u.uuid4)(),timestamp:t.timestamp||(0,g.dateTimestampInSeconds)()},v=n.integrations||e.integrations.map(e=>e.name);f(y,e),d=y,(m=v).length>0&&(d.sdk=d.sdk||{},d.sdk.integrations=[...d.sdk.integrations||[],...m]),c&&c.emit("applyFrameMetadata",t),void 0===t.type&&_(y,e.stackParser);let T=function(e,t){if(!t)return e;let n=e?e.clone():new a.Scope;return n.update(t),n}(r,n.captureContext);n.mechanism&&(0,u.addExceptionMechanism)(y,n.mechanism);let b=c?c.getEventProcessors():[],I=(0,i.getGlobalScope)().getScopeData();if(p){let e=p.getScopeData();(0,o.mergeScopeData)(I,e)}if(T){let e=T.getScopeData();(0,o.mergeScopeData)(I,e)}let C=[...n.attachments||[],...I.attachments];C.length&&(n.attachments=C),(0,o.applyScopeDataToEvent)(y,I);let A=[...b,...I.eventProcessors];return(0,s.notifyEventProcessors)(A,y,n).then(e=>(e&&h(e),"number"==typeof S&&S>0)?function(e,t,n){if(!e)return null;let r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:(0,l.normalize)(e.data,t,n)}}))},...e.user&&{user:(0,l.normalize)(e.user,t,n)},...e.contexts&&{contexts:(0,l.normalize)(e.contexts,t,n)},...e.extra&&{extra:(0,l.normalize)(e.extra,t,n)}};return e.contexts?.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=(0,l.normalize)(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(e=>({...e,...e.data&&{data:(0,l.normalize)(e.data,t,n)}}))),e.contexts?.flags&&r.contexts&&(r.contexts.flags=(0,l.normalize)(e.contexts.flags,3,n)),r}(e,S,E):e)}function f(e,t){let{environment:n,release:i,dist:s,maxValueLength:a=250}=t;e.environment=e.environment||n||r.DEFAULT_ENVIRONMENT,!e.release&&i&&(e.release=i),!e.dist&&s&&(e.dist=s);let o=e.request;o?.url&&(o.url=(0,p.truncate)(o.url,a))}function _(e,t){let n=(0,c.getFilenameToDebugIdMap)(t);e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.filename&&(e.debug_id=n[e.filename])})})}function h(e){let t={};if(e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})}),0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let n=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{n.push({type:"sourcemap",code_file:e,debug_id:t})})}function m(e){if(e){var n;return(n=e)instanceof a.Scope||"function"==typeof n||Object.keys(e).some(e=>t.includes(e))?{captureContext:e}:e}}let t=["user","level","extra","contexts","tags","fingerprint","propagationContext"]}},917:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({addEventProcessor:()=>N,captureCheckIn:()=>T,captureEvent:()=>f,captureException:()=>g,captureMessage:()=>d,captureSession:()=>M,close:()=>C,endSession:()=>R,flush:()=>I,isEnabled:()=>x,isInitialized:()=>A,lastEventId:()=>v,setContext:()=>_,setExtra:()=>m,setExtras:()=>h,setTag:()=>E,setTags:()=>S,setUser:()=>y,startSession:()=>O,withMonitor:()=>b});var r=e.i(548729),i=e.i(653333),s=e.i(447489),a=e.i(877130),o=e.i(36650),c=e.i(416971),u=e.i(265405),l=e.i(321858),p=e.i(441619);function g(e,t){return(0,r.getCurrentScope)().captureException(e,(0,u.parseEventHintOrCaptureContext)(t))}function d(e,t){let n="string"==typeof t?t:void 0,i="string"!=typeof t?{captureContext:t}:void 0;return(0,r.getCurrentScope)().captureMessage(e,n,i)}function f(e,t){return(0,r.getCurrentScope)().captureEvent(e,t)}function _(e,t){(0,r.getIsolationScope)().setContext(e,t)}function h(e){(0,r.getIsolationScope)().setExtras(e)}function m(e,t){(0,r.getIsolationScope)().setExtra(e,t)}function S(e){(0,r.getIsolationScope)().setTags(e)}function E(e,t){(0,r.getIsolationScope)().setTag(e,t)}function y(e){(0,r.getIsolationScope)().setUser(e)}function v(){return(0,r.getIsolationScope)().lastEventId()}function T(e,t){let n=(0,r.getCurrentScope)(),s=(0,r.getClient)();if(s)if(s.captureCheckIn)return s.captureCheckIn(e,t,n);else i.DEBUG_BUILD&&o.logger.warn("Cannot capture check-in. Client does not support sending check-ins.");else i.DEBUG_BUILD&&o.logger.warn("Cannot capture check-in. No client defined.");return(0,c.uuid4)()}function b(e,t,n){let i=T({monitorSlug:e,status:"in_progress"},n),s=(0,l.timestampInSeconds)();function o(t){T({monitorSlug:e,status:t,checkInId:i,duration:(0,l.timestampInSeconds)()-s})}return(0,r.withIsolationScope)(()=>{let e;try{e=t()}catch(e){throw o("error"),e}return(0,a.isThenable)(e)?Promise.resolve(e).then(()=>{o("ok")},e=>{throw o("error"),e}):o("ok"),e})}async function I(e){let t=(0,r.getClient)();return t?t.flush(e):(i.DEBUG_BUILD&&o.logger.warn("Cannot flush events. No client defined."),Promise.resolve(!1))}async function C(e){let t=(0,r.getClient)();return t?t.close(e):(i.DEBUG_BUILD&&o.logger.warn("Cannot flush events and disable SDK. No client defined."),Promise.resolve(!1))}function A(){return!!(0,r.getClient)()}function x(){let e=(0,r.getClient)();return e?.getOptions().enabled!==!1&&!!e?.getTransport()}function N(e){(0,r.getIsolationScope)().addEventProcessor(e)}function O(e){let t=(0,r.getIsolationScope)(),n=(0,r.getCurrentScope)(),{userAgent:i}=p.GLOBAL_OBJ.navigator||{},a=(0,s.makeSession)({user:n.getUser()||t.getUser(),...i&&{userAgent:i},...e}),o=t.getSession();return o?.status==="ok"&&(0,s.updateSession)(o,{status:"exited"}),R(),t.setSession(a),a}function R(){let e=(0,r.getIsolationScope)(),t=(0,r.getCurrentScope)().getSession()||e.getSession();t&&(0,s.closeSession)(t),D(),e.setSession()}function D(){let e=(0,r.getIsolationScope)(),t=(0,r.getClient)(),n=e.getSession();n&&t&&t.captureSession(n)}function M(e=!1){if(e)return void R();D()}},519535:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({vercelWaitUntil:()=>i});var r=e.i(441619);function i(e){let t=r.GLOBAL_OBJ[Symbol.for("@vercel/request-context")],n=t?.get&&t.get()?t.get():{};n?.waitUntil&&n.waitUntil(e)}},8365:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({DEBUG_BUILD:()=>t});let t="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__}},551616:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({flushSafelyWithTimeout:()=>a});var r=e.i(36650),i=e.i(917),s=e.i(8365);async function a(){try{s.DEBUG_BUILD&&r.logger.log("Flushing events..."),await (0,i.flush)(2e3),s.DEBUG_BUILD&&r.logger.log("Done flushing events")}catch(e){s.DEBUG_BUILD&&r.logger.log("Error while flushing events:\n",e)}}}}]);

//# sourceMappingURL=node_modules_%40sentry_2f99c5a2._.js.map