(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/storage.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@smithy_core_dist-es_9707ba3d._.js",
  "static/chunks/node_modules_@aws-sdk_client-s3_60df6f97._.js",
  "static/chunks/node_modules_d04f64be._.js",
  "static/chunks/src_lib_storage_ts_7db1c311._.js",
  "static/chunks/src_lib_storage_ts_aedde6a9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/storage.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/lib/actions/consultations.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_actions_7c405d5a._.js",
  "static/chunks/src_lib_actions_consultations_ts_10389a22._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/actions/consultations.ts [app-client] (ecmascript)");
    });
});
}}),
}]);