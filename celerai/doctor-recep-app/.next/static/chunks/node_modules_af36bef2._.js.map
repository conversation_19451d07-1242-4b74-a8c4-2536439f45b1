{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "sdk.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/react/src/sdk.ts"], "sourcesContent": ["import type { BrowserOptions } from '@sentry/browser';\nimport { init as browserInit, setContext } from '@sentry/browser';\nimport type { Client } from '@sentry/core';\nimport { applySdkMetadata } from '@sentry/core';\nimport { version } from 'react';\n\n/**\n * Inits the React SDK\n */\nexport function init(options: BrowserOptions): Client | undefined {\n  const opts = {\n    ...options,\n  };\n\n  applySdkMetadata(opts, 'react');\n  setContext('react', { version });\n  return browserInit(opts);\n}\n"], "names": ["browserInit"], "mappings": ";;;;;;;;;;AAMA;;CAEA,GACO,SAAS,IAAI,CAAC,OAAO,EAAsC;IAChE,MAAM,OAAO;QACX,GAAG,OAAO;IACd,CAAG;oLAED,mBAAA,AAAgB,EAAC,IAAI,EAAE,OAAO,CAAC;uKAC/B,aAAA,AAAU,EAAC,OAAO,EAAE;+KAAE,UAAA;IAAA,CAAS,CAAC;IAChC,gLAAOA,OAAAA,AAAW,EAAC,IAAI,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js"], "sourcesContent": ["var UNKNOWN_FUNCTION = '<unknown>';\n/**\n * This parses the different stack traces and puts them into one format\n * This borrows heavily from TraceKit (https://github.com/csnover/TraceKit)\n */\n\nfunction parse(stackString) {\n  var lines = stackString.split('\\n');\n  return lines.reduce(function (stack, line) {\n    var parseResult = parseChrome(line) || parseWinjs(line) || parseGecko(line) || parseNode(line) || parseJSC(line);\n\n    if (parseResult) {\n      stack.push(parseResult);\n    }\n\n    return stack;\n  }, []);\n}\nvar chromeRe = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\nvar chromeEvalRe = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n\nfunction parseChrome(line) {\n  var parts = chromeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n\n  var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n\n  var submatch = chromeEvalRe.exec(parts[2]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line/column number\n    parts[2] = submatch[1]; // url\n\n    parts[3] = submatch[2]; // line\n\n    parts[4] = submatch[3]; // column\n  }\n\n  return {\n    file: !isNative ? parts[2] : null,\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: isNative ? [parts[2]] : [],\n    lineNumber: parts[3] ? +parts[3] : null,\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar winjsRe = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseWinjs(line) {\n  var parts = winjsRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar geckoRe = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nvar geckoEvalRe = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\n\nfunction parseGecko(line) {\n  var parts = geckoRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n  var submatch = geckoEvalRe.exec(parts[3]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line number\n    parts[3] = submatch[1];\n    parts[4] = submatch[2];\n    parts[5] = null; // no column when eval\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: parts[2] ? parts[2].split(',') : [],\n    lineNumber: parts[4] ? +parts[4] : null,\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar javaScriptCoreRe = /^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;\n\nfunction parseJSC(line) {\n  var parts = javaScriptCoreRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[4],\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar nodeRe = /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseNode(line) {\n  var parts = nodeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nexport { parse };\n"], "names": [], "mappings": ";;;AAAA,IAAI,mBAAmB;AACvB;;;CAGC,GAED,SAAS,MAAM,WAAW;IACxB,IAAI,QAAQ,YAAY,KAAK,CAAC;IAC9B,OAAO,MAAM,MAAM,CAAC,SAAU,KAAK,EAAE,IAAI;QACvC,IAAI,cAAc,YAAY,SAAS,WAAW,SAAS,WAAW,SAAS,UAAU,SAAS,SAAS;QAE3G,IAAI,aAAa;YACf,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT,GAAG,EAAE;AACP;AACA,IAAI,WAAW;AACf,IAAI,eAAe;AAEnB,SAAS,YAAY,IAAI;IACvB,IAAI,QAAQ,SAAS,IAAI,CAAC;IAE1B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,IAAI,WAAW,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc,GAAG,gBAAgB;IAE7E,IAAI,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,GAAG,gBAAgB;IAEzE,IAAI,WAAW,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;IAEzC,IAAI,UAAU,YAAY,MAAM;QAC9B,iEAAiE;QACjE,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,MAAM;QAE9B,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,OAAO;QAE/B,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,SAAS;IACnC;IAEA,OAAO;QACL,MAAM,CAAC,WAAW,KAAK,CAAC,EAAE,GAAG;QAC7B,YAAY,KAAK,CAAC,EAAE,IAAI;QACxB,WAAW,WAAW;YAAC,KAAK,CAAC,EAAE;SAAC,GAAG,EAAE;QACrC,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;QACnC,QAAQ,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;IACjC;AACF;AAEA,IAAI,UAAU;AAEd,SAAS,WAAW,IAAI;IACtB,IAAI,QAAQ,QAAQ,IAAI,CAAC;IAEzB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO;QACL,MAAM,KAAK,CAAC,EAAE;QACd,YAAY,KAAK,CAAC,EAAE,IAAI;QACxB,WAAW,EAAE;QACb,YAAY,CAAC,KAAK,CAAC,EAAE;QACrB,QAAQ,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;IACjC;AACF;AAEA,IAAI,UAAU;AACd,IAAI,cAAc;AAElB,SAAS,WAAW,IAAI;IACtB,IAAI,QAAQ,QAAQ,IAAI,CAAC;IAEzB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,IAAI,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC;IACxD,IAAI,WAAW,YAAY,IAAI,CAAC,KAAK,CAAC,EAAE;IAExC,IAAI,UAAU,YAAY,MAAM;QAC9B,0DAA0D;QAC1D,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QACtB,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;QACtB,KAAK,CAAC,EAAE,GAAG,MAAM,sBAAsB;IACzC;IAEA,OAAO;QACL,MAAM,KAAK,CAAC,EAAE;QACd,YAAY,KAAK,CAAC,EAAE,IAAI;QACxB,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QAC9C,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;QACnC,QAAQ,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;IACjC;AACF;AAEA,IAAI,mBAAmB;AAEvB,SAAS,SAAS,IAAI;IACpB,IAAI,QAAQ,iBAAiB,IAAI,CAAC;IAElC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO;QACL,MAAM,KAAK,CAAC,EAAE;QACd,YAAY,KAAK,CAAC,EAAE,IAAI;QACxB,WAAW,EAAE;QACb,YAAY,CAAC,KAAK,CAAC,EAAE;QACrB,QAAQ,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;IACjC;AACF;AAEA,IAAI,SAAS;AAEb,SAAS,UAAU,IAAI;IACrB,IAAI,QAAQ,OAAO,IAAI,CAAC;IAExB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO;QACL,MAAM,KAAK,CAAC,EAAE;QACd,YAAY,KAAK,CAAC,EAAE,IAAI;QACxB,WAAW,EAAE;QACb,YAAY,CAAC,KAAK,CAAC,EAAE;QACrB,QAAQ,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;IACjC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/common/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": "AAEA;;;;CAIA;;;AACO,MAAM,WAAY,GAAiB,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "file": "devErrorSymbolicationEventProcessor.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/common/devErrorSymbolicationEventProcessor.ts"], "sourcesContent": ["import type { Event, EventHint } from '@sentry/core';\nimport { G<PERSON><PERSON><PERSON>L_OBJ, logger, parseSemver, suppressTracing } from '@sentry/core';\nimport type { StackFrame } from 'stacktrace-parser';\nimport * as stackTraceParser from 'stacktrace-parser';\nimport { DEBUG_BUILD } from './debug-build';\n\ntype OriginalStackFrameResponse = {\n  originalStackFrame: StackFrame;\n  originalCodeFrame: string | null;\n  sourcePackage?: string;\n};\n\nconst globalWithInjectedValues = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  _sentryBasePath?: string;\n  next?: {\n    version?: string;\n  };\n};\n\n/**\n * Event processor that will symbolicate errors by using the webpack/nextjs dev server that is used to show stack traces\n * in the dev overlay.\n */\nexport async function devErrorSymbolicationEventProcessor(event: Event, hint: EventHint): Promise<Event | null> {\n  // Filter out spans for requests resolving source maps for stack frames in dev mode\n  if (event.type === 'transaction') {\n    event.spans = event.spans?.filter(span => {\n      const httpUrlAttribute: unknown = span.data?.['http.url'];\n      if (typeof httpUrlAttribute === 'string') {\n        return !httpUrlAttribute.includes('__nextjs_original-stack-frame'); // could also be __nextjs_original-stack-frames (plural)\n      }\n\n      return true;\n    });\n  }\n\n  // Due to changes across Next.js versions, there are a million things that can go wrong here so we just try-catch the\n  // entire event processor. Symbolicated stack traces are just a nice to have.\n  try {\n    if (hint.originalException && hint.originalException instanceof Error && hint.originalException.stack) {\n      const frames = stackTraceParser.parse(hint.originalException.stack);\n\n      const nextjsVersion = globalWithInjectedValues.next?.version || '0.0.0';\n      const parsedNextjsVersion = nextjsVersion ? parseSemver(nextjsVersion) : {};\n\n      let resolvedFrames: ({\n        originalCodeFrame: string | null;\n        originalStackFrame: StackFrame | null;\n      } | null)[];\n\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      if (parsedNextjsVersion.major! > 15 || (parsedNextjsVersion.major === 15 && parsedNextjsVersion.minor! >= 2)) {\n        const r = await resolveStackFrames(frames);\n        if (r === null) {\n          return event;\n        }\n        resolvedFrames = r;\n      } else {\n        resolvedFrames = await Promise.all(\n          frames.map(frame => resolveStackFrame(frame, hint.originalException as Error)),\n        );\n      }\n\n      if (event.exception?.values?.[0]?.stacktrace?.frames) {\n        event.exception.values[0].stacktrace.frames = event.exception.values[0].stacktrace.frames.map(\n          (frame, i, frames) => {\n            const resolvedFrame = resolvedFrames[frames.length - 1 - i];\n            if (!resolvedFrame?.originalStackFrame || !resolvedFrame.originalCodeFrame) {\n              return {\n                ...frame,\n                platform: frame.filename?.startsWith('node:internal') ? 'nodejs' : undefined, // simple hack that will prevent a source mapping error from showing up\n                in_app: false,\n              };\n            }\n\n            const { contextLine, preContextLines, postContextLines } = parseOriginalCodeFrame(\n              resolvedFrame.originalCodeFrame,\n            );\n\n            return {\n              ...frame,\n              pre_context: preContextLines,\n              context_line: contextLine,\n              post_context: postContextLines,\n              function: resolvedFrame.originalStackFrame.methodName,\n              filename: resolvedFrame.originalStackFrame.file || undefined,\n              lineno: resolvedFrame.originalStackFrame.lineNumber || undefined,\n              colno: resolvedFrame.originalStackFrame.column || undefined,\n            };\n          },\n        );\n      }\n    }\n  } catch (e) {\n    return event;\n  }\n\n  return event;\n}\n\nasync function resolveStackFrame(\n  frame: StackFrame,\n  error: Error,\n): Promise<{ originalCodeFrame: string | null; originalStackFrame: StackFrame | null } | null> {\n  try {\n    if (!(frame.file?.startsWith('webpack-internal:') || frame.file?.startsWith('file:'))) {\n      return null;\n    }\n\n    const params = new URLSearchParams();\n    params.append('isServer', String(false)); // doesn't matter since it is overwritten by isAppDirectory\n    params.append('isEdgeServer', String(false)); // doesn't matter since it is overwritten by isAppDirectory\n    params.append('isAppDirectory', String(true)); // will force server to do more thorough checking\n    params.append('errorMessage', error.toString());\n    Object.keys(frame).forEach(key => {\n      params.append(key, (frame[key as keyof typeof frame] ?? '').toString());\n    });\n\n    let basePath = process.env._sentryBasePath ?? globalWithInjectedValues._sentryBasePath ?? '';\n\n    // Prefix the basepath with a slash if it doesn't have one\n    if (basePath !== '' && !basePath.match(/^\\//)) {\n      basePath = `/${basePath}`;\n    }\n\n    const controller = new AbortController();\n    const timer = setTimeout(() => controller.abort(), 3000);\n    const res = await suppressTracing(() =>\n      fetch(\n        `${\n          // eslint-disable-next-line no-restricted-globals\n          typeof window === 'undefined' ? 'http://localhost:3000' : '' // TODO: handle the case where users define a different port\n        }${basePath}/__nextjs_original-stack-frame?${params.toString()}`,\n        {\n          signal: controller.signal,\n        },\n      ).finally(() => {\n        clearTimeout(timer);\n      }),\n    );\n\n    if (!res.ok || res.status === 204) {\n      return null;\n    }\n\n    const body: OriginalStackFrameResponse = await res.json();\n\n    return {\n      originalCodeFrame: body.originalCodeFrame,\n      originalStackFrame: body.originalStackFrame,\n    };\n  } catch (e) {\n    DEBUG_BUILD && logger.error('Failed to symbolicate event with Next.js dev server', e);\n    return null;\n  }\n}\n\nasync function resolveStackFrames(\n  frames: StackFrame[],\n): Promise<{ originalCodeFrame: string | null; originalStackFrame: StackFrame | null }[] | null> {\n  try {\n    const postBody = {\n      frames: frames\n        .filter(frame => {\n          return !!frame.file;\n        })\n        .map(frame => {\n          // https://github.com/vercel/next.js/blob/df0573a478baa8b55478a7963c473dddd59a5e40/packages/next/src/client/components/react-dev-overlay/server/middleware-turbopack.ts#L129\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          frame.file = frame.file!.replace(/^rsc:\\/\\/React\\/[^/]+\\//, '').replace(/\\?\\d+$/, '');\n\n          return {\n            file: frame.file,\n            methodName: frame.methodName ?? '<unknown>',\n            arguments: [],\n            lineNumber: frame.lineNumber ?? 0,\n            column: frame.column ?? 0,\n          };\n        }),\n      isServer: false,\n      isEdgeServer: false,\n      isAppDirectory: true,\n    };\n\n    let basePath = process.env._sentryBasePath ?? globalWithInjectedValues._sentryBasePath ?? '';\n\n    // Prefix the basepath with a slash if it doesn't have one\n    if (basePath !== '' && !basePath.match(/^\\//)) {\n      basePath = `/${basePath}`;\n    }\n\n    const controller = new AbortController();\n    const timer = setTimeout(() => controller.abort(), 3000);\n\n    const res = await suppressTracing(() =>\n      fetch(\n        `${\n          // eslint-disable-next-line no-restricted-globals\n          typeof window === 'undefined' ? 'http://localhost:3000' : '' // TODO: handle the case where users define a different port\n        }${basePath}/__nextjs_original-stack-frames`,\n        {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          signal: controller.signal,\n          body: JSON.stringify(postBody),\n        },\n      ).finally(() => {\n        clearTimeout(timer);\n      }),\n    );\n\n    if (!res.ok || res.status === 204) {\n      return null;\n    }\n\n    const body: { value: OriginalStackFrameResponse }[] = await res.json();\n\n    return body.map(frame => {\n      return {\n        originalCodeFrame: frame.value.originalCodeFrame,\n        originalStackFrame: frame.value.originalStackFrame,\n      };\n    });\n  } catch (e) {\n    DEBUG_BUILD && logger.error('Failed to symbolicate event with Next.js dev server', e);\n    return null;\n  }\n}\n\nfunction parseOriginalCodeFrame(codeFrame: string): {\n  contextLine: string | undefined;\n  preContextLines: string[];\n  postContextLines: string[];\n} {\n  const preProcessedLines = codeFrame\n    // Remove ASCII control characters that are used for syntax highlighting\n    .replace(\n      // eslint-disable-next-line no-control-regex\n      /[\\u001b\\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, // https://stackoverflow.com/a/29497680\n      '',\n    )\n    .split('\\n')\n    // Remove line that is supposed to indicate where the error happened\n    .filter(line => !line.match(/^\\s*\\|/))\n    // Find the error line\n    .map(line => ({\n      line,\n      isErrorLine: !!line.match(/^>/),\n    }))\n    // Remove the leading part that is just for prettier output\n    .map(lineObj => ({\n      ...lineObj,\n      line: lineObj.line.replace(/^.*\\|/, ''),\n    }));\n\n  const preContextLines = [];\n  let contextLine: string | undefined = undefined;\n  const postContextLines = [];\n\n  let reachedContextLine = false;\n\n  for (const preProcessedLine of preProcessedLines) {\n    if (preProcessedLine.isErrorLine) {\n      contextLine = preProcessedLine.line;\n      reachedContextLine = true;\n    } else if (reachedContextLine) {\n      postContextLines.push(preProcessedLine.line);\n    } else {\n      preContextLines.push(preProcessedLine.line);\n    }\n  }\n\n  return {\n    contextLine,\n    preContextLines,\n    postContextLines,\n  };\n}\n"], "names": [], "mappings": ";;;AAsHmB;;;;;;;;;;AA1GnB,MAAM,wBAAA,6KAA2B,aAAA;AAOjC;;;CAGA,GACO,eAAe,mCAAmC,CAAC,KAAK,EAAS,IAAI,EAAoC;IAChH,mFAAA;IACE,IAAI,KAAK,CAAC,IAAK,KAAI,aAAa,EAAE;QAChC,KAAK,CAAC,KAAA,GAAQ,KAAK,CAAC,KAAK,EAAE,MAAM,EAAC,IAAA,IAAQ;YACxC,MAAM,gBAAgB,GAAY,IAAI,CAAC,IAAI,EAAA,CAAG,UAAU,CAAC;YACzD,IAAI,OAAO,gBAAiB,KAAI,QAAQ,EAAE;gBACxC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAA,CAAA,wDAAA;YAC1E;YAEM,OAAO,IAAI;QACjB,CAAK,CAAC;IACN;IAEA,qHAAA;IACA,6EAAA;IACE,IAAI;QACF,IAAI,IAAI,CAAC,iBAAA,IAAqB,IAAI,CAAC,iBAAkB,YAAW,SAAS,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACrG,MAAM,MAAA,OAAS,gBAAgB,CAAC,0KAAA,AAAK,EAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAEnE,MAAM,gBAAgB,wBAAwB,CAAC,IAAI,EAAE,OAAQ,IAAG,OAAO;YACvE,MAAM,mBAAoB,GAAE,aAAc,2BAAE,sLAAA,AAAW,EAAC,aAAa,CAAA,GAAI,EAAE;YAE3E,IAAI;YAKV,oEAAA;YACM,IAAI,mBAAmB,CAAC,KAAK,GAAI,EAAG,IAAI,mBAAmB,CAAC,KAAM,KAAI,EAAG,IAAG,mBAAmB,CAAC,KAAK,IAAK,CAAC,CAAC,CAAE;gBAC5G,MAAM,CAAE,GAAE,MAAM,kBAAkB,CAAC,MAAM,CAAC;gBAC1C,IAAI,CAAE,KAAI,IAAI,EAAE;oBACd,OAAO,KAAK;gBACtB;gBACQ,cAAA,GAAiB,CAAC;YAC1B,OAAa;gBACL,cAAe,GAAE,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,CAAC,SAAS,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAA,EAA2B,CAAC;YAExF;YAEM,IAAI,KAAK,CAAC,SAAS,EAAE,MAAM,EAAA,CAAG,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE;gBACpD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAO,GAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAC3F,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,KAAK;oBACpB,MAAM,aAAA,GAAgB,cAAc,CAAC,MAAM,CAAC,MAAA,GAAS,CAAA,GAAI,CAAC,CAAC;oBAC3D,IAAI,CAAC,aAAa,EAAE,kBAAmB,IAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE;wBAC1E,OAAO;4BACL,GAAG,KAAK;4BACR,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,eAAe,CAAA,GAAI,QAAA,GAAW,SAAS;4BAC5E,MAAM,EAAE,KAAK;wBAC7B,CAAe;oBACf;oBAEY,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAA,EAAmB,GAAE,sBAAsB,CAC/E,aAAa,CAAC,iBAAiB;oBAGjC,OAAO;wBACL,GAAG,KAAK;wBACR,WAAW,EAAE,eAAe;wBAC5B,YAAY,EAAE,WAAW;wBACzB,YAAY,EAAE,gBAAgB;wBAC9B,QAAQ,EAAE,aAAa,CAAC,kBAAkB,CAAC,UAAU;wBACrD,QAAQ,EAAE,aAAa,CAAC,kBAAkB,CAAC,IAAA,IAAQ,SAAS;wBAC5D,MAAM,EAAE,aAAa,CAAC,kBAAkB,CAAC,UAAA,IAAc,SAAS;wBAChE,KAAK,EAAE,aAAa,CAAC,kBAAkB,CAAC,MAAA,IAAU,SAAS;oBACzE,CAAa;gBACb,CAAW;YAEX;QACA;IACA,CAAI,CAAA,OAAO,CAAC,EAAE;QACV,OAAO,KAAK;IAChB;IAEE,OAAO,KAAK;AACd;AAEA,eAAe,iBAAiB,CAC9B,KAAK,EACL,KAAK;IAEL,IAAI;QACF,IAAI,CAAA,CAAE,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;YACrF,OAAO,IAAI;QACjB;QAEI,MAAM,MAAO,GAAE,IAAI,eAAe,EAAE;QACpC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,CAAA,2DAAA;QACxC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,CAAA,2DAAA;QAC5C,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA,CAAA,iDAAA;QAC7C,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAC,GAAA,IAAO;YAChC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAI,CAAA,IAA0B,EAAE,EAAE,QAAQ,EAAE,CAAC;QAC7E,CAAK,CAAC;QAEF,IAAI,QAAA,8KAAkB,CAAC,GAAG,CAAC,eAAA,IAAmB,wBAAwB,CAAC,eAAA,IAAmB,EAAE;QAEhG,0DAAA;QACI,IAAI,QAAS,KAAI,EAAG,IAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC7C,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QACA;QAEA,MAAA,UAAA,GAAA,IAAA,eAAA,EAAA;QACA,MAAA,KAAA,GAAA,UAAA,CAAA,IAAA,UAAA,CAAA,KAAA,EAAA,EAAA,IAAA,CAAA;QACA,MAAA,GAAA,GAAA,kLAAA,kBAAA,EAAA,IACA,KAAA,CACA,CAAA,EACA,iDAAA;YACA,OAAA,MAAA,KAAA,WAAA,GAAA,uBAAA,GAAA,EAAA,CAAA,4DAAA;eACA,QAAA,CAAA,+BAAA,EAAA,MAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EACA;gBACA,MAAA,EAAA,UAAA,CAAA,MAAA;YACA,CAAA,EACA,OAAA,CAAA,MAAA;gBACA,YAAA,CAAA,KAAA,CAAA;YACA,CAAA,CAAA;QAGA,IAAA,CAAA,GAAA,CAAA,EAAA,IAAA,GAAA,CAAA,MAAA,KAAA,GAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,MAAA,IAAA,GAAA,MAAA,GAAA,CAAA,IAAA,EAAA;QAEA,OAAA;YACA,iBAAA,EAAA,IAAA,CAAA,iBAAA;YACA,kBAAA,EAAA,IAAA,CAAA,kBAAA;QACA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;0LACA,cAAA,2KAAA,SAAA,CAAA,KAAA,CAAA,qDAAA,EAAA,CAAA,CAAA;QACA,OAAA,IAAA;IACA;AACA;AAEA,eAAA,kBAAA,CACA,MAAA;IAEA,IAAA;QACA,MAAA,QAAA,GAAA;YACA,MAAA,EAAA,OACA,MAAA,EAAA,KAAA,IAAA;gBACA,OAAA,CAAA,CAAA,KAAA,CAAA,IAAA;YACA,CAAA,EACA,GAAA,EAAA,KAAA,IAAA;gBACA,4KAAA;gBACA,oEAAA;gBACA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,QAAA,EAAA,EAAA,CAAA;gBAEA,OAAA;oBACA,IAAA,EAAA,KAAA,CAAA,IAAA;oBACA,UAAA,EAAA,KAAA,CAAA,UAAA,IAAA,WAAA;oBACA,SAAA,EAAA,EAAA;oBACA,UAAA,EAAA,KAAA,CAAA,UAAA,IAAA,CAAA;oBACA,MAAA,EAAA,KAAA,CAAA,MAAA,IAAA,CAAA;gBACA,CAAA;YACA,CAAA,CAAA;YACA,QAAA,EAAA,KAAA;YACA,YAAA,EAAA,KAAA;YACA,cAAA,EAAA,IAAA;QACA,CAAA;QAEA,IAAA,QAAA,oKAAA,UAAA,CAAA,GAAA,CAAA,eAAA,IAAA,wBAAA,CAAA,eAAA,IAAA,EAAA;QAEA,0DAAA;QACA,IAAA,QAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA;YACA,QAAA,GAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA;QACA;QAEA,MAAA,UAAA,GAAA,IAAA,eAAA,EAAA;QACA,MAAA,KAAA,GAAA,UAAA,CAAA,IAAA,UAAA,CAAA,KAAA,EAAA,EAAA,IAAA,CAAA;QAEA,MAAA,GAAA,GAAA,kLAAA,kBAAA,EAAA,IACA,KAAA,CACA,CAAA,EACA,iDAAA;YACA,OAAA,MAAA,KAAA,WAAA,GAAA,uBAAA,GAAA,EAAA,CAAA,4DAAA;eACA,QAAA,CAAA,+BAAA,CAAA,EACA;gBACA,MAAA,EAAA,MAAA;gBACA,OAAA,EAAA;oBACA,cAAA,EAAA,kBAAA;gBACA,CAAA;gBACA,MAAA,EAAA,UAAA,CAAA,MAAA;gBACA,IAAA,EAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA;YACA,CAAA,EACA,OAAA,CAAA,MAAA;gBACA,YAAA,CAAA,KAAA,CAAA;YACA,CAAA,CAAA;QAGA,IAAA,CAAA,GAAA,CAAA,EAAA,IAAA,GAAA,CAAA,MAAA,KAAA,GAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,MAAA,IAAA,GAAA,MAAA,GAAA,CAAA,IAAA,EAAA;QAEA,OAAA,IAAA,CAAA,GAAA,EAAA,KAAA,IAAA;YACA,OAAA;gBACA,iBAAA,EAAA,KAAA,CAAA,KAAA,CAAA,iBAAA;gBACA,kBAAA,EAAA,KAAA,CAAA,KAAA,CAAA,kBAAA;YACA,CAAA;QACA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;0LACA,cAAA,2KAAA,SAAA,CAAA,KAAA,CAAA,qDAAA,EAAA,CAAA,CAAA;QACA,OAAA,IAAA;IACA;AACA;AAEA,SAAA,sBAAA,CAAA,SAAA;IAKA,MAAA,iBAAA,GAAA,SACA,wEAAA;KACA,OAAA,CACA,4CAAA;IACA,6EAAA,EACA,EAAA,EAEA,KAAA,CAAA,IAAA,CACA,oEAAA;KACA,MAAA,EAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CACA,sBAAA;KACA,GAAA,EAAA,IAAA,GAAA,CAAA;YACA,IAAA;YACA,WAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA;QACA,CAAA,CAAA,CACA,2DAAA;KACA,GAAA,CAAA,OAAA,IAAA,CAAA;YACA,GAAA,OAAA;YACA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,OAAA,EAAA,EAAA,CAAA;QACA,CAAA,CAAA,CAAA;IAEA,MAAA,eAAA,GAAA,EAAA;IACA,IAAA,WAAA,GAAA,SAAA;IACA,MAAA,gBAAA,GAAA,EAAA;IAEA,IAAA,kBAAA,GAAA,KAAA;IAEA,KAAA,MAAA,gBAAA,IAAA,iBAAA,CAAA;QACA,IAAA,gBAAA,CAAA,WAAA,EAAA;YACA,WAAA,GAAA,gBAAA,CAAA,IAAA;YACA,kBAAA,GAAA,IAAA;QACA,CAAA,MAAA,IAAA,kBAAA,EAAA;YACA,gBAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA;QACA,CAAA,MAAA;YACA,eAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA;QACA;IACA;IAEA,OAAA;QACA,WAAA;QACA,eAAA;QACA,gBAAA;IACA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "file": "getVercelEnv.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/common/getVercelEnv.ts"], "sourcesContent": ["/**\n * Returns an environment setting value determined by Vercel's `VERCEL_ENV` environment variable.\n *\n * @param isClient Flag to indicate whether to use the `NEXT_PUBLIC_` prefixed version of the environment variable.\n */\nexport function getVercelEnv(isClient: boolean): string | undefined {\n  const vercelEnvVar = isClient ? process.env.NEXT_PUBLIC_VERCEL_ENV : process.env.VERCEL_ENV;\n  return vercelEnvVar ? `vercel-${vercelEnvVar}` : undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIA;;;;AACO,SAAS,YAAY,CAAC,QAAQ,EAA+B;IAClE,MAAM,YAAA,GAAe,QAAA,oKAAW,UAAO,CAAC,GAAG,CAAC,sBAAA,oKAAyB,UAAO,CAAC,GAAG,CAAC,UAAU;IAC3F,OAAO,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA,GAAA,SAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "file": "nextNavigationErrorUtils.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/common/nextNavigationErrorUtils.ts"], "sourcesContent": ["import { isError } from '@sentry/core';\n\n/**\n * Determines whether input is a Next.js not-found error.\n * https://beta.nextjs.org/docs/api-reference/notfound#notfound\n */\nexport function isNotFoundNavigationError(subject: unknown): boolean {\n  return (\n    isError(subject) &&\n    ['NEXT_NOT_FOUND', 'NEXT_HTTP_ERROR_FALLBACK;404'].includes(\n      (subject as Error & { digest?: unknown }).digest as string,\n    )\n  );\n}\n\n/**\n * Determines whether input is a Next.js redirect error.\n * https://beta.nextjs.org/docs/api-reference/redirect#redirect\n */\nexport function isRedirectNavigationError(subject: unknown): boolean {\n  return (\n    isError(subject) &&\n    typeof (subject as Error & { digest?: unknown }).digest === 'string' &&\n    (subject as Error & { digest: string }).digest.startsWith('NEXT_REDIRECT;') // a redirect digest looks like \"NEXT_REDIRECT;[redirect path]\"\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;CAGA,GACO,SAAS,yBAAyB,CAAC,OAAO,EAAoB;IACnE,8KACE,UAAA,AAAO,EAAC,OAAO,CAAE,IACjB;QAAC,gBAAgB;QAAE,8BAA8B;KAAC,CAAC,QAAQ,CACzD,AAAC,OAAA,CAAyC,MAAO;AAGvD;AAEA;;;CAGA,GACO,SAAS,yBAAyB,CAAC,OAAO,EAAoB;IACnE,8KACE,UAAA,AAAO,EAAC,OAAO,CAAE,IACjB,OAAO,AAAC,OAAA,CAAyC,MAAA,KAAW,QAAS,IACrE,AAAC,QAAuC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAA,CAAA,+DAAA;;AAE9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "file": "appRouterRoutingInstrumentation.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/routing/appRouterRoutingInstrumentation.ts"], "sourcesContent": ["import type { Client, Span } from '@sentry/core';\nimport {\n  browserPerformanceTimeOrigin,\n  GLO<PERSON>L_OBJ,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '@sentry/core';\nimport { startBrowserTracingNavigationSpan, startBrowserTracingPageLoadSpan, WINDOW } from '@sentry/react';\n\nexport const INCOMPLETE_APP_ROUTER_INSTRUMENTATION_TRANSACTION_NAME = 'incomplete-app-router-transaction';\n\n/**\n * This mutable keeps track of what router navigation instrumentation mechanism we are using.\n *\n * The default one is 'router-patch' which is a way of instrumenting that worked up until Next.js 15.3.0 was released.\n * For this method we took the global router instance and simply monkey patched all the router methods like push(), replace(), and so on.\n * This worked because Next.js itself called the router methods for things like the <Link /> component.\n * Vercel decided that it is not good to call these public API methods from within the framework so they switched to an internal system that completely bypasses our monkey patching. This happened in 15.3.0.\n *\n * We raised with <PERSON>er<PERSON> that this breaks our SDK so together with them we came up with an API for `instrumentation-client.ts` called `onRouterTransitionStart` that is called whenever a navigation is kicked off.\n *\n * Now we have the problem of version compatibility.\n * For older Next.js versions we cannot use the new hook so we need to always patch the router.\n * For newer Next.js versions we cannot know whether the user actually registered our handler for the `onRouterTransitionStart` hook, so we need to wait until it was called at least once before switching the instrumentation mechanism.\n * The problem is, that the user may still have registered a hook and then call a patched router method.\n * First, the monkey patched router method will be called, starting a navigation span, then the hook will also called.\n * We need to handle this case and not create two separate navigation spans but instead update the current navigation span and then switch to the new instrumentation mode.\n * This is all denoted by this `navigationRoutingMode` variable.\n */\nlet navigationRoutingMode: 'router-patch' | 'transition-start-hook' = 'router-patch';\n\nconst currentRouterPatchingNavigationSpanRef: NavigationSpanRef = { current: undefined };\n\n/** Instruments the Next.js app router for pageloads. */\nexport function appRouterInstrumentPageLoad(client: Client): void {\n  const origin = browserPerformanceTimeOrigin();\n  startBrowserTracingPageLoadSpan(client, {\n    name: WINDOW.location.pathname,\n    // pageload should always start at timeOrigin (and needs to be in s, not ms)\n    startTime: origin ? origin / 1000 : undefined,\n    attributes: {\n      [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n      [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.pageload.nextjs.app_router_instrumentation',\n      [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n    },\n  });\n}\n\ninterface NavigationSpanRef {\n  current: Span | undefined;\n}\n\ninterface NextRouter {\n  back: () => void;\n  forward: () => void;\n  push: (target: string) => void;\n  replace: (target: string) => void;\n}\n\n// Yes, yes, I know we shouldn't depend on these internals. But that's where we are at. We write the ugly code, so you don't have to.\nconst GLOBAL_OBJ_WITH_NEXT_ROUTER = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  // Available until 13.4.4-canary.3 - https://github.com/vercel/next.js/pull/50210\n  nd?: {\n    router?: NextRouter;\n  };\n  // Available from 13.4.4-canary.4 - https://github.com/vercel/next.js/pull/50210\n  next?: {\n    router?: NextRouter;\n  };\n};\n\n/*\n * The routing instrumentation needs to handle a few cases:\n * - Router operations:\n *  - router.push() (either explicitly called or implicitly through <Link /> tags)\n *  - router.replace() (either explicitly called or implicitly through <Link replace /> tags)\n *  - router.back()\n *  - router.forward()\n * - Browser operations:\n *  - native Browser-back / popstate event (implicitly called by router.back())\n *  - native Browser-forward / popstate event (implicitly called by router.forward())\n */\n\n/** Instruments the Next.js app router for navigation. */\nexport function appRouterInstrumentNavigation(client: Client): void {\n  routerTransitionHandler = (href, navigationType) => {\n    const pathname = new URL(href, WINDOW.location.href).pathname;\n\n    if (navigationRoutingMode === 'router-patch') {\n      navigationRoutingMode = 'transition-start-hook';\n    }\n\n    const currentNavigationSpan = currentRouterPatchingNavigationSpanRef.current;\n    if (currentNavigationSpan) {\n      currentNavigationSpan.updateName(pathname);\n      currentNavigationSpan.setAttributes({\n        'navigation.type': `router.${navigationType}`,\n      });\n      currentRouterPatchingNavigationSpanRef.current = undefined;\n    } else {\n      startBrowserTracingNavigationSpan(client, {\n        name: pathname,\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.nextjs.app_router_instrumentation',\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n          'navigation.type': `router.${navigationType}`,\n        },\n      });\n    }\n  };\n\n  WINDOW.addEventListener('popstate', () => {\n    if (currentRouterPatchingNavigationSpanRef.current?.isRecording()) {\n      currentRouterPatchingNavigationSpanRef.current.updateName(WINDOW.location.pathname);\n      currentRouterPatchingNavigationSpanRef.current.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'url');\n    } else {\n      currentRouterPatchingNavigationSpanRef.current = startBrowserTracingNavigationSpan(client, {\n        name: WINDOW.location.pathname,\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.nextjs.app_router_instrumentation',\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n          'navigation.type': 'browser.popstate',\n        },\n      });\n    }\n  });\n\n  let routerPatched = false;\n  let triesToFindRouter = 0;\n  const MAX_TRIES_TO_FIND_ROUTER = 500;\n  const ROUTER_AVAILABILITY_CHECK_INTERVAL_MS = 20;\n  const checkForRouterAvailabilityInterval = setInterval(() => {\n    triesToFindRouter++;\n    const router = GLOBAL_OBJ_WITH_NEXT_ROUTER?.next?.router ?? GLOBAL_OBJ_WITH_NEXT_ROUTER?.nd?.router;\n\n    if (routerPatched || triesToFindRouter > MAX_TRIES_TO_FIND_ROUTER) {\n      clearInterval(checkForRouterAvailabilityInterval);\n    } else if (router) {\n      clearInterval(checkForRouterAvailabilityInterval);\n      routerPatched = true;\n\n      patchRouter(client, router, currentRouterPatchingNavigationSpanRef);\n\n      // If the router at any point gets overridden - patch again\n      (['nd', 'next'] as const).forEach(globalValueName => {\n        const globalValue = GLOBAL_OBJ_WITH_NEXT_ROUTER[globalValueName];\n        if (globalValue) {\n          GLOBAL_OBJ_WITH_NEXT_ROUTER[globalValueName] = new Proxy(globalValue, {\n            set(target, p, newValue) {\n              if (p === 'router' && typeof newValue === 'object' && newValue !== null) {\n                patchRouter(client, newValue, currentRouterPatchingNavigationSpanRef);\n              }\n\n              // @ts-expect-error we cannot possibly type this\n              target[p] = newValue;\n              return true;\n            },\n          });\n        }\n      });\n    }\n  }, ROUTER_AVAILABILITY_CHECK_INTERVAL_MS);\n}\n\nfunction transactionNameifyRouterArgument(target: string): string {\n  try {\n    // We provide an arbitrary base because we only care about the pathname and it makes URL parsing more resilient.\n    return new URL(target, 'http://example.com/').pathname;\n  } catch {\n    return '/';\n  }\n}\n\nconst patchedRouters = new WeakSet<NextRouter>();\n\nfunction patchRouter(client: Client, router: NextRouter, currentNavigationSpanRef: NavigationSpanRef): void {\n  if (patchedRouters.has(router)) {\n    return;\n  }\n  patchedRouters.add(router);\n\n  (['back', 'forward', 'push', 'replace'] as const).forEach(routerFunctionName => {\n    if (router?.[routerFunctionName]) {\n      // @ts-expect-error Weird type error related to not knowing how to associate return values with the individual functions - we can just ignore\n      router[routerFunctionName] = new Proxy(router[routerFunctionName], {\n        apply(target, thisArg, argArray) {\n          if (navigationRoutingMode !== 'router-patch') {\n            return target.apply(thisArg, argArray);\n          }\n\n          let transactionName = INCOMPLETE_APP_ROUTER_INSTRUMENTATION_TRANSACTION_NAME;\n          const transactionAttributes: Record<string, string> = {\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.nextjs.app_router_instrumentation',\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n          };\n\n          if (routerFunctionName === 'push') {\n            transactionName = transactionNameifyRouterArgument(argArray[0]);\n            transactionAttributes['navigation.type'] = 'router.push';\n          } else if (routerFunctionName === 'replace') {\n            transactionName = transactionNameifyRouterArgument(argArray[0]);\n            transactionAttributes['navigation.type'] = 'router.replace';\n          } else if (routerFunctionName === 'back') {\n            transactionAttributes['navigation.type'] = 'router.back';\n          } else if (routerFunctionName === 'forward') {\n            transactionAttributes['navigation.type'] = 'router.forward';\n          }\n\n          currentNavigationSpanRef.current = startBrowserTracingNavigationSpan(client, {\n            name: transactionName,\n            attributes: transactionAttributes,\n          });\n\n          return target.apply(thisArg, argArray);\n        },\n      });\n    }\n  });\n}\n\nlet routerTransitionHandler: undefined | ((href: string, navigationType: string) => void) = undefined;\n\n/**\n * A handler for Next.js' `onRouterTransitionStart` hook in `instrumentation-client.ts` to record navigation spans in Sentry.\n */\nexport function captureRouterTransitionStart(href: string, navigationType: string): void {\n  if (routerTransitionHandler) {\n    routerTransitionHandler(href, navigationType);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAUO,MAAM,sDAAuD,GAAE;AAEtE;;;;;;;;;;;;;;;;;CAiBA,GACA,IAAI,qBAAqB,GAA6C,cAAc;AAEpF,MAAM,sCAAsC,GAAsB;IAAE,OAAO,EAAE;AAAA,CAAW;AAExF,sDAAA,GACO,SAAS,2BAA2B,CAAC,MAAM,EAAgB;IAChE,MAAM,MAAA,4KAAS,+BAAA,AAA4B,EAAE;6MAC7C,mCAAA,AAA+B,EAAC,MAAM,EAAE;QACtC,IAAI,2KAAE,SAAM,CAAC,QAAQ,CAAC,QAAQ;QAClC,4EAAA;QACI,SAAS,EAAE,MAAO,GAAE,SAAS,IAAA,GAAO,SAAS;QAC7C,UAAU,EAAE;YACV,CAAC,yMAA4B,CAAA,EAAG,UAAU;YAC1C,2KAAC,mCAAgC,CAAA,EAAG,iDAAiD;YACrF,2KAAC,mCAAgC,CAAA,EAAG,KAAK;QAC/C,CAAK;IACL,CAAG,CAAC;AACJ;AAaA,qIAAA;AACA,MAAM,2BAAA,4KAA8B,cAAA;AAWpC;;;;;;;;;;CAUA,GAEA,uDAAA,GACO,SAAS,6BAA6B,CAAC,MAAM,EAAgB;IAClE,0BAA0B,CAAC,IAAI,EAAE,cAAc,KAAK;QAClD,MAAM,QAAA,GAAW,IAAI,GAAG,CAAC,IAAI,2KAAE,SAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ;QAE7D,IAAI,qBAAsB,KAAI,cAAc,EAAE;YAC5C,qBAAA,GAAwB,uBAAuB;QACrD;QAEI,MAAM,qBAAA,GAAwB,sCAAsC,CAAC,OAAO;QAC5E,IAAI,qBAAqB,EAAE;YACzB,qBAAqB,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC1C,qBAAqB,CAAC,aAAa,CAAC;gBAClC,iBAAiB,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;YACA,CAAA,CAAA;YACA,sCAAA,CAAA,OAAA,GAAA,SAAA;QACA,CAAA,MAAA;gBACA,0OAAA,EAAA,MAAA,EAAA;gBACA,IAAA,EAAA,QAAA;gBACA,UAAA,EAAA;oBACA,2KAAA,+BAAA,CAAA,EAAA,YAAA;oBACA,CAAA,6MAAA,CAAA,EAAA,mDAAA;oBACA,2KAAA,mCAAA,CAAA,EAAA,KAAA;oBACA,iBAAA,EAAA,CAAA,OAAA,EAAA,cAAA,CAAA,CAAA;gBACA,CAAA;YACA,CAAA,CAAA;QACA;IACA,CAAA;6KAEA,SAAA,CAAA,gBAAA,CAAA,UAAA,EAAA,MAAA;QACA,IAAA,sCAAA,CAAA,OAAA,EAAA,WAAA,EAAA,EAAA;YACA,sCAAA,CAAA,OAAA,CAAA,UAAA,yKAAA,UAAA,CAAA,QAAA,CAAA,QAAA,CAAA;YACA,sCAAA,CAAA,OAAA,CAAA,YAAA,2KAAA,mCAAA,EAAA,KAAA,CAAA;QACA,CAAA,MAAA;YACA,sCAAA,CAAA,OAAA,GAAA,8OAAA,EAAA,MAAA,EAAA;gBACA,IAAA,2KAAA,SAAA,CAAA,QAAA,CAAA,QAAA;gBACA,UAAA,EAAA;oBACA,2KAAA,mCAAA,CAAA,EAAA,mDAAA;oBACA,2KAAA,mCAAA,CAAA,EAAA,KAAA;oBACA,iBAAA,EAAA,kBAAA;gBACA,CAAA;YACA,CAAA,CAAA;QACA;IACA,CAAA,CAAA;IAEA,IAAA,aAAA,GAAA,KAAA;IACA,IAAA,iBAAA,GAAA,CAAA;IACA,MAAA,wBAAA,GAAA,GAAA;IACA,MAAA,qCAAA,GAAA,EAAA;IACA,MAAA,kCAAA,GAAA,WAAA,CAAA,MAAA;QACA,iBAAA,EAAA;QACA,MAAA,MAAA,GAAA,2BAAA,EAAA,IAAA,EAAA,MAAA,IAAA,2BAAA,EAAA,EAAA,EAAA,MAAA;QAEA,IAAA,aAAA,IAAA,iBAAA,GAAA,wBAAA,EAAA;YACA,aAAA,CAAA,kCAAA,CAAA;QACA,CAAA,MAAA,IAAA,MAAA,EAAA;YACA,aAAA,CAAA,kCAAA,CAAA;YACA,aAAA,GAAA,IAAA;YAEA,WAAA,CAAA,MAAA,EAAA,MAAA,EAAA,sCAAA,CAAA;YAEA,2DAAA;YACA;gBAAA,IAAA;gBAAA,MAAA;aAAA,CAAA,OAAA,EAAA,eAAA,IAAA;gBACA,MAAA,WAAA,GAAA,2BAAA,CAAA,eAAA,CAAA;gBACA,IAAA,WAAA,EAAA;oBACA,2BAAA,CAAA,eAAA,CAAA,GAAA,IAAA,KAAA,CAAA,WAAA,EAAA;wBACA,GAAA,EAAA,MAAA,EAAA,CAAA,EAAA,QAAA,EAAA;4BACA,IAAA,CAAA,KAAA,QAAA,IAAA,OAAA,QAAA,KAAA,QAAA,IAAA,QAAA,KAAA,IAAA,EAAA;gCACA,WAAA,CAAA,MAAA,EAAA,QAAA,EAAA,sCAAA,CAAA;4BACA;4BAEA,gDAAA;4BACA,MAAA,CAAA,CAAA,CAAA,GAAA,QAAA;4BACA,OAAA,IAAA;wBACA,CAAA;oBACA,CAAA,CAAA;gBACA;YACA,CAAA,CAAA;QACA;IACA,CAAA,EAAA,qCAAA,CAAA;AACA;AAEA,SAAA,gCAAA,CAAA,MAAA,EAAA;IACA,IAAA;QACA,gHAAA;QACA,OAAA,IAAA,GAAA,CAAA,MAAA,EAAA,qBAAA,CAAA,CAAA,QAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,GAAA;IACA;AACA;AAEA,MAAA,cAAA,GAAA,IAAA,OAAA,EAAA;AAEA,SAAA,WAAA,CAAA,MAAA,EAAA,MAAA,EAAA,wBAAA,EAAA;IACA,IAAA,cAAA,CAAA,GAAA,CAAA,MAAA,CAAA,EAAA;QACA;IACA;IACA,cAAA,CAAA,GAAA,CAAA,MAAA,CAAA;IAEA;QAAA,MAAA;QAAA,SAAA;QAAA,MAAA;QAAA,SAAA;KAAA,CAAA,OAAA,EAAA,kBAAA,IAAA;QACA,IAAA,MAAA,EAAA,CAAA,kBAAA,CAAA,EAAA;YACA,6IAAA;YACA,MAAA,CAAA,kBAAA,CAAA,GAAA,IAAA,KAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,EAAA;gBACA,KAAA,EAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA;oBACA,IAAA,qBAAA,KAAA,cAAA,EAAA;wBACA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA;oBACA;oBAEA,IAAA,eAAA,GAAA,sDAAA;oBACA,MAAA,qBAAA,GAAA;wBACA,0KAAA,gCAAA,CAAA,EAAA,YAAA;wBACA,2KAAA,mCAAA,CAAA,EAAA,mDAAA;wBACA,2KAAA,mCAAA,CAAA,EAAA,KAAA;oBACA,CAAA;oBAEA,IAAA,kBAAA,KAAA,MAAA,EAAA;wBACA,eAAA,GAAA,gCAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;wBACA,qBAAA,CAAA,iBAAA,CAAA,GAAA,aAAA;oBACA,CAAA,MAAA,IAAA,kBAAA,KAAA,SAAA,EAAA;wBACA,eAAA,GAAA,gCAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;wBACA,qBAAA,CAAA,iBAAA,CAAA,GAAA,gBAAA;oBACA,CAAA,MAAA,IAAA,kBAAA,KAAA,MAAA,EAAA;wBACA,qBAAA,CAAA,iBAAA,CAAA,GAAA,aAAA;oBACA,CAAA,MAAA,IAAA,kBAAA,KAAA,SAAA,EAAA;wBACA,qBAAA,CAAA,iBAAA,CAAA,GAAA,gBAAA;oBACA;oBAEA,wBAAA,CAAA,OAAA,6MAAA,oCAAA,EAAA,MAAA,EAAA;wBACA,IAAA,EAAA,eAAA;wBACA,UAAA,EAAA,qBAAA;oBACA,CAAA,CAAA;oBAEA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA;gBACA,CAAA;YACA,CAAA,CAAA;QACA;IACA,CAAA,CAAA;AACA;AAEA,IAAA,uBAAA,GAAA,SAAA;AAEA;;CAEA,GACA,SAAA,4BAAA,CAAA,IAAA,EAAA,cAAA,EAAA;IACA,IAAA,uBAAA,EAAA;QACA,uBAAA,CAAA,IAAA,EAAA,cAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "file": "pagesRouterRoutingInstrumentation.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/routing/pagesRouterRoutingInstrumentation.ts"], "sourcesContent": ["import type { Client, TransactionSource } from '@sentry/core';\nimport {\n  browserPerformanceTimeOrigin,\n  logger,\n  parseBaggageHeader,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  stripUrlQueryAndFragment,\n} from '@sentry/core';\nimport { startBrowserTracingNavigationSpan, startBrowserTracingPageLoadSpan, WINDOW } from '@sentry/react';\nimport type { NEXT_DATA } from 'next/dist/shared/lib/utils';\nimport RouterImport from 'next/router';\nimport type { ParsedUrlQuery } from 'querystring';\nimport { DEBUG_BUILD } from '../../common/debug-build';\n\n// next/router v10 is CJS\n//\n// For ESM/CJS interoperability 'reasons', depending on how this file is loaded, Router might be on the default export\nconst Router: typeof RouterImport = RouterImport.events\n  ? RouterImport\n  : (RouterImport as unknown as { default: typeof RouterImport }).default;\n\nconst globalObject = WINDOW as typeof WINDOW & {\n  __BUILD_MANIFEST?: {\n    sortedPages?: string[];\n  };\n};\n\n/**\n * Describes data located in the __NEXT_DATA__ script tag. This tag is present on every page of a Next.js app.\n */\ninterface SentryEnhancedNextData extends NEXT_DATA {\n  props: {\n    pageProps?: {\n      _sentryTraceData?: string; // trace parent info, if injected by a data-fetcher\n      _sentryBaggage?: string; // baggage, if injected by a data-fetcher\n      // These two values are only injected by `getStaticProps` in a very special case with the following conditions:\n      // 1. The page's `getStaticPaths` method must have returned `fallback: 'blocking'`.\n      // 2. The requested page must be a \"miss\" in terms of \"Incremental Static Regeneration\", meaning the requested page has not been generated before.\n      // In this case, a page is requested and only served when `getStaticProps` is done. There is not even a fallback page or similar.\n    };\n  };\n}\n\ninterface NextDataTagInfo {\n  route?: string;\n  params?: ParsedUrlQuery;\n  sentryTrace?: string;\n  baggage?: string;\n}\n\n/**\n * Every Next.js page (static and dynamic ones) comes with a script tag with the id \"__NEXT_DATA__\". This script tag\n * contains a JSON object with data that was either generated at build time for static pages (`getStaticProps`), or at\n * runtime with data fetchers like `getServerSideProps.`.\n *\n * We can use this information to:\n * - Always get the parameterized route we're in when loading a page.\n * - Send trace information (trace-id, baggage) from the server to the client.\n *\n * This function extracts this information.\n */\nfunction extractNextDataTagInformation(): NextDataTagInfo {\n  let nextData: SentryEnhancedNextData | undefined;\n  // Let's be on the safe side and actually check first if there is really a __NEXT_DATA__ script tag on the page.\n  // Theoretically this should always be the case though.\n  const nextDataTag = globalObject.document.getElementById('__NEXT_DATA__');\n  if (nextDataTag?.innerHTML) {\n    try {\n      nextData = JSON.parse(nextDataTag.innerHTML);\n    } catch (e) {\n      DEBUG_BUILD && logger.warn('Could not extract __NEXT_DATA__');\n    }\n  }\n\n  if (!nextData) {\n    return {};\n  }\n\n  const nextDataTagInfo: NextDataTagInfo = {};\n\n  const { page, query, props } = nextData;\n\n  // `nextData.page` always contains the parameterized route - except for when an error occurs in a data fetching\n  // function, then it is \"/_error\", but that isn't a problem since users know which route threw by looking at the\n  // parent transaction\n  // TODO: Actually this is a problem (even though it is not that big), because the DSC and the transaction payload will contain\n  // a different transaction name. Maybe we can fix this. Idea: Also send transaction name via pageProps when available.\n  nextDataTagInfo.route = page;\n  nextDataTagInfo.params = query;\n\n  if (props?.pageProps) {\n    nextDataTagInfo.sentryTrace = props.pageProps._sentryTraceData;\n    nextDataTagInfo.baggage = props.pageProps._sentryBaggage;\n  }\n\n  return nextDataTagInfo;\n}\n\n/**\n * Instruments the Next.js pages router for pageloads.\n * Only supported for client side routing. Works for Next >= 10.\n *\n * Leverages the SingletonRouter from the `next/router` to\n * generate pageload/navigation transactions and parameterize\n * transaction names.\n */\nexport function pagesRouterInstrumentPageLoad(client: Client): void {\n  const { route, params, sentryTrace, baggage } = extractNextDataTagInformation();\n  const parsedBaggage = parseBaggageHeader(baggage);\n  let name = route || globalObject.location.pathname;\n\n  // /_error is the fallback page for all errors. If there is a transaction name for /_error, use that instead\n  if (parsedBaggage?.['sentry-transaction'] && name === '/_error') {\n    name = parsedBaggage['sentry-transaction'];\n    // Strip any HTTP method from the span name\n    name = name.replace(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\\s+/i, '');\n  }\n\n  const origin = browserPerformanceTimeOrigin();\n  startBrowserTracingPageLoadSpan(\n    client,\n    {\n      name,\n      // pageload should always start at timeOrigin (and needs to be in s, not ms)\n      startTime: origin ? origin / 1000 : undefined,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.pageload.nextjs.pages_router_instrumentation',\n        [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: route ? 'route' : 'url',\n        ...(params && client.getOptions().sendDefaultPii && { ...params }),\n      },\n    },\n    { sentryTrace, baggage },\n  );\n}\n\n/**\n * Instruments the Next.js pages router for navigation.\n * Only supported for client side routing. Works for Next >= 10.\n *\n * Leverages the SingletonRouter from the `next/router` to\n * generate pageload/navigation transactions and parameterize\n * transaction names.\n */\nexport function pagesRouterInstrumentNavigation(client: Client): void {\n  Router.events.on('routeChangeStart', (navigationTarget: string) => {\n    const strippedNavigationTarget = stripUrlQueryAndFragment(navigationTarget);\n    const matchedRoute = getNextRouteFromPathname(strippedNavigationTarget);\n\n    let newLocation: string;\n    let spanSource: TransactionSource;\n\n    if (matchedRoute) {\n      newLocation = matchedRoute;\n      spanSource = 'route';\n    } else {\n      newLocation = strippedNavigationTarget;\n      spanSource = 'url';\n    }\n\n    startBrowserTracingNavigationSpan(client, {\n      name: newLocation,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.nextjs.pages_router_instrumentation',\n        [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: spanSource,\n      },\n    });\n  });\n}\n\nfunction getNextRouteFromPathname(pathname: string): string | undefined {\n  const pageRoutes = globalObject.__BUILD_MANIFEST?.sortedPages;\n\n  // Page route should in 99.999% of the cases be defined by now but just to be sure we make a check here\n  if (!pageRoutes) {\n    return;\n  }\n\n  return pageRoutes.find(route => {\n    const routeRegExp = convertNextRouteToRegExp(route);\n    return pathname.match(routeRegExp);\n  });\n}\n\n/**\n * Converts a Next.js style route to a regular expression that matches on pathnames (no query params or URL fragments).\n *\n * In general this involves replacing any instances of square brackets in a route with a wildcard:\n * e.g. \"/users/[id]/info\" becomes /\\/users\\/([^/]+?)\\/info/\n *\n * Some additional edgecases need to be considered:\n * - All routes have an optional slash at the end, meaning users can navigate to \"/users/[id]/info\" or\n *   \"/users/[id]/info/\" - both will be resolved to \"/users/[id]/info\".\n * - Non-optional \"catchall\"s at the end of a route must be considered when matching (e.g. \"/users/[...params]\").\n * - Optional \"catchall\"s at the end of a route must be considered when matching (e.g. \"/users/[[...params]]\").\n *\n * @param route A Next.js style route as it is found in `global.__BUILD_MANIFEST.sortedPages`\n */\nfunction convertNextRouteToRegExp(route: string): RegExp {\n  // We can assume a route is at least \"/\".\n  const routeParts = route.split('/');\n\n  let optionalCatchallWildcardRegex = '';\n  if (routeParts[routeParts.length - 1]?.match(/^\\[\\[\\.\\.\\..+\\]\\]$/)) {\n    // If last route part has pattern \"[[...xyz]]\" we pop the latest route part to get rid of the required trailing\n    // slash that would come before it if we didn't pop it.\n    routeParts.pop();\n    optionalCatchallWildcardRegex = '(?:/(.+?))?';\n  }\n\n  const rejoinedRouteParts = routeParts\n    .map(\n      routePart =>\n        routePart\n          .replace(/^\\[\\.\\.\\..+\\]$/, '(.+?)') // Replace catch all wildcard with regex wildcard\n          .replace(/^\\[.*\\]$/, '([^/]+?)'), // Replace route wildcards with lazy regex wildcards\n    )\n    .join('/');\n\n  // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- routeParts are from the build manifest, so no raw user input\n  return new RegExp(\n    `^${rejoinedRouteParts}${optionalCatchallWildcardRegex}(?:/)?$`, // optional slash at the end\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAgBA,yBAAA;AACA,EAAA;AACA,sHAAA;AACA,MAAM,MAAM,qIAAwB,UAAY,CAAC,MAAA,qIAC7C,UAAA,GACA,kIAAC,UAAa,CAAgD,OAAO;AAEzE,MAAM,YAAA,4KAAe,SAAA;AAMrB;;CAEA,GAqBA;;;;;;;;;;CAUA,GACA,SAAS,6BAA6B,GAAoB;IACxD,IAAI,QAAQ;IACd,gHAAA;IACA,uDAAA;IACE,MAAM,WAAY,GAAE,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC;IACzE,IAAI,WAAW,EAAE,SAAS,EAAE;QAC1B,IAAI;YACF,QAAA,GAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC;QAClD,CAAM,CAAA,OAAO,CAAC,EAAE;8LACV,cAAA,2KAAe,SAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC;QACnE;IACA;IAEE,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,CAAA,CAAE;IACb;IAEE,MAAM,eAAe,GAAoB,CAAA,CAAE;IAE3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAA,EAAQ,GAAE,QAAQ;IAEzC,+GAAA;IACA,gHAAA;IACA,qBAAA;IACA,8HAAA;IACA,sHAAA;IACE,eAAe,CAAC,KAAM,GAAE,IAAI;IAC5B,eAAe,CAAC,MAAO,GAAE,KAAK;IAE9B,IAAI,KAAK,EAAE,SAAS,EAAE;QACpB,eAAe,CAAC,WAAY,GAAE,KAAK,CAAC,SAAS,CAAC,gBAAgB;QAC9D,eAAe,CAAC,OAAQ,GAAE,KAAK,CAAC,SAAS,CAAC,cAAc;IAC5D;IAEE,OAAO,eAAe;AACxB;AAEA;;;;;;;CAOA,GACO,SAAS,6BAA6B,CAAC,MAAM,EAAgB;IAClE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAQ,EAAA,GAAI,6BAA6B,EAAE;IAC/E,MAAM,aAAc,+KAAE,qBAAA,AAAkB,EAAC,OAAO,CAAC;IACjD,IAAI,OAAO,KAAA,IAAS,YAAY,CAAC,QAAQ,CAAC,QAAQ;IAEpD,4GAAA;IACE,IAAI,aAAa,EAAA,CAAG,oBAAoB,CAAA,IAAK,IAAA,KAAS,SAAS,EAAE;QAC/D,IAAK,GAAE,aAAa,CAAC,oBAAoB,CAAC;QAC9C,2CAAA;QACI,IAAA,GAAO,IAAI,CAAC,OAAO,CAAC,6DAA6D,EAAE,EAAE,CAAC;IAC1F;IAEE,MAAM,MAAA,4KAAS,+BAAA,AAA4B,EAAE;8MAC7C,kCAAA,AAA+B,EAC7B,MAAM,EACN;QACE,IAAI;QACV,4EAAA;QACM,SAAS,EAAE,MAAO,GAAE,SAAS,IAAA,GAAO,SAAS;QAC7C,UAAU,EAAE;YACV,2KAAC,+BAA4B,CAAA,EAAG,UAAU;YAC1C,2KAAC,mCAAgC,CAAA,EAAG,mDAAmD;YACvF,2KAAC,mCAAgC,CAAA,EAAG,QAAQ,OAAA,GAAU,KAAK;YAC3D,GAAI,MAAA,IAAU,MAAM,CAAC,UAAU,EAAE,CAAC,cAAA,IAAkB;gBAAE,GAAG,MAAA;YAAA,CAAQ,CAAC;QAC1E,CAAO;IACP,CAAK,EACD;QAAE,WAAW;QAAE,OAAA;IAAA,CAAS;AAE5B;AAEA;;;;;;;CAOA,GACO,SAAS,+BAA+B,CAAC,MAAM,EAAgB;IACpE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,KAAa;QACjE,MAAM,wBAAyB,2KAAE,2BAAA,AAAwB,EAAC,gBAAgB,CAAC;QAC3E,MAAM,YAAa,GAAE,wBAAwB,CAAC,wBAAwB,CAAC;QAEvE,IAAI,WAAW;QACf,IAAI,UAAU;QAEd,IAAI,YAAY,EAAE;YAChB,WAAA,GAAc,YAAY;YAC1B,UAAA,GAAa,OAAO;QAC1B,OAAW;YACL,WAAA,GAAc,wBAAwB;YACtC,UAAA,GAAa,KAAK;QACxB;kNAEI,oCAAA,AAAiC,EAAC,MAAM,EAAE;YACxC,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE;gBACV,2KAAC,+BAA4B,CAAA,EAAG,YAAY;gBAC5C,0KAAC,oCAAgC,CAAA,EAAG,qDAAqD;gBACzF,2KAAC,mCAAgC,CAAA,EAAG,UAAU;YACtD,CAAO;QACP,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA,SAAS,wBAAwB,CAAC,QAAQ,EAA8B;IACtE,MAAM,UAAW,GAAE,YAAY,CAAC,gBAAgB,EAAE,WAAW;IAE/D,uGAAA;IACE,IAAI,CAAC,UAAU,EAAE;QACf;IACJ;IAEE,OAAO,UAAU,CAAC,IAAI,EAAC,SAAS;QAC9B,MAAM,WAAY,GAAE,wBAAwB,CAAC,KAAK,CAAC;QACnD,OAAO,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;IACtC,CAAG,CAAC;AACJ;AAEA;;;;;;;;;;;;;CAaA,GACA,SAAS,wBAAwB,CAAC,KAAK,EAAkB;IACzD,yCAAA;IACE,MAAM,aAAa,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;IAEnC,IAAI,6BAA8B,GAAE,EAAE;IACtC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAA,GAAS,CAAC,CAAC,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;QACtE,+GAAA;QACA,uDAAA;QACI,UAAU,CAAC,GAAG,EAAE;QAChB,6BAAA,GAAgC,aAAa;IACjD;IAEE,MAAM,qBAAqB,WACxB,GAAG,EACF,SAAU,GACR,UACG,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAA,CAAA,iDAAA;SACjC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAErC,IAAI,CAAC,GAAG,CAAC;IAEd,sIAAA;IACE,OAAO,IAAI,MAAM,CACf,CAAC,CAAC,EAAE,kBAAkB,CAAC,EAAA,6BAAA,CAAA,OAAA,CAAA;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "file": "nextRoutingInstrumentation.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/routing/nextRoutingInstrumentation.ts"], "sourcesContent": ["import type { Client } from '@sentry/core';\nimport { WINDOW } from '@sentry/react';\nimport { appRouterInstrumentNavigation, appRouterInstrumentPageLoad } from './appRouterRoutingInstrumentation';\nimport { pagesRouterInstrumentNavigation, pagesRouterInstrumentPageLoad } from './pagesRouterRoutingInstrumentation';\n\n/**\n * Instruments the Next.js Client Router for page loads.\n */\nexport function nextRouterInstrumentPageLoad(client: Client): void {\n  const isAppRouter = !WINDOW.document.getElementById('__NEXT_DATA__');\n  if (isAppRouter) {\n    appRouterInstrumentPageLoad(client);\n  } else {\n    pagesRouterInstrumentPageLoad(client);\n  }\n}\n\n/**\n * Instruments the Next.js Client Router for navigation.\n */\nexport function nextRouterInstrumentNavigation(client: Client): void {\n  const isAppRouter = !WINDOW.document.getElementById('__NEXT_DATA__');\n  if (isAppRouter) {\n    appRouterInstrumentNavigation(client);\n  } else {\n    pagesRouterInstrumentNavigation(client);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAKA;;CAEA,GACO,SAAS,4BAA4B,CAAC,MAAM,EAAgB;IACjE,MAAM,WAAY,GAAE,0KAAC,SAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC;IACpE,IAAI,WAAW,EAAE;0NACf,8BAAA,AAA2B,EAAC,MAAM,CAAC;IACvC,OAAS;4NACL,gCAAA,AAA6B,EAAC,MAAM,CAAC;IACzC;AACA;AAEA;;CAEA,GACO,SAAS,8BAA8B,CAAC,MAAM,EAAgB;IACnE,MAAM,WAAY,GAAE,0KAAC,SAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC;IACpE,IAAI,WAAW,EAAE;0NACf,gCAAA,AAA6B,EAAC,MAAM,CAAC;IACzC,OAAS;4NACL,kCAAA,AAA+B,EAAC,MAAM,CAAC;IAC3C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "file": "browserTracingIntegration.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/browserTracingIntegration.ts"], "sourcesContent": ["import type { Integration } from '@sentry/core';\nimport { browserTracingIntegration as originalBrowserTracingIntegration } from '@sentry/react';\nimport { nextRouterInstrumentNavigation, nextRouterInstrumentPageLoad } from './routing/nextRoutingInstrumentation';\n\n/**\n * A custom browser tracing integration for Next.js.\n */\nexport function browserTracingIntegration(\n  options: Parameters<typeof originalBrowserTracingIntegration>[0] = {},\n): Integration {\n  const browserTracingIntegrationInstance = originalBrowserTracingIntegration({\n    ...options,\n    instrumentNavigation: false,\n    instrumentPageLoad: false,\n    onRequestSpanStart(...args) {\n      const [span, { headers }] = args;\n\n      // Next.js prefetch requests have a `next-router-prefetch` header\n      if (headers?.get('next-router-prefetch')) {\n        span?.setAttribute('http.request.prefetch', true);\n      }\n\n      return options.onRequestSpanStart?.(...args);\n    },\n  });\n\n  const { instrumentPageLoad = true, instrumentNavigation = true } = options;\n\n  return {\n    ...browserTracingIntegrationInstance,\n    afterAllSetup(client) {\n      // We need to run the navigation span instrumentation before the `afterAllSetup` hook on the normal browser\n      // tracing integration because we need to ensure the order of execution is as follows:\n      // Instrumentation to start span on RSC fetch request runs -> Instrumentation to put tracing headers from active span on fetch runs\n      // If it were the other way around, the RSC fetch request would not receive the tracing headers from the navigation transaction.\n      if (instrumentNavigation) {\n        nextRouterInstrumentNavigation(client);\n      }\n\n      browserTracingIntegrationInstance.afterAllSetup(client);\n\n      if (instrumentPageLoad) {\n        nextRouterInstrumentPageLoad(client);\n      }\n    },\n  };\n}\n"], "names": ["originalBrowserTracingIntegration"], "mappings": ";;;;;;;AAIA;;CAEA,GACO,SAAS,yBAAyB,CACvC,OAAO,GAA4D,CAAA,CAAE;IAErE,MAAM,iCAAA,6MAAoCA,4BAAAA,AAAiC,EAAC;QAC1E,GAAG,OAAO;QACV,oBAAoB,EAAE,KAAK;QAC3B,kBAAkB,EAAE,KAAK;QACzB,kBAAkB,EAAC,GAAG,IAAI,EAAE;YAC1B,MAAM,CAAC,IAAI,EAAE,EAAE,OAAQ,EAAC,CAAE,GAAE,IAAI;YAEtC,iEAAA;YACM,IAAI,OAAO,EAAE,GAAG,CAAC,sBAAsB,CAAC,EAAE;gBACxC,IAAI,EAAE,YAAY,CAAC,uBAAuB,EAAE,IAAI,CAAC;YACzD;YAEM,OAAO,OAAO,CAAC,kBAAkB,GAAG,GAAG,IAAI,CAAC;QAClD,CAAK;IACL,CAAG,CAAC;IAEF,MAAM,EAAE,kBAAA,GAAqB,IAAI,EAAE,oBAAA,GAAuB,IAAA,EAAO,GAAE,OAAO;IAE1E,OAAO;QACL,GAAG,iCAAiC;QACpC,aAAa,EAAC,MAAM,EAAE;YAC1B,2GAAA;YACA,sFAAA;YACA,mIAAA;YACA,gIAAA;YACM,IAAI,oBAAoB,EAAE;6NACxB,iCAAA,AAA8B,EAAC,MAAM,CAAC;YAC9C;YAEM,iCAAiC,CAAC,aAAa,CAAC,MAAM,CAAC;YAEvD,IAAI,kBAAkB,EAAE;6NACtB,+BAAA,AAA4B,EAAC,MAAM,CAAC;YAC5C;QACA,CAAK;IACL,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "file": "clientNormalizationIntegration.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/clientNormalizationIntegration.ts"], "sourcesContent": ["import { defineIntegration } from '@sentry/core';\nimport { rewriteFramesIntegration } from '@sentry/react';\n\nexport const nextjsClientStackFrameNormalizationIntegration = defineIntegration(\n  ({\n    assetPrefix,\n    basePath,\n    rewriteFramesAssetPrefixPath,\n    experimentalThirdPartyOriginStackFrames,\n  }: {\n    assetPrefix?: string;\n    basePath?: string;\n    rewriteFramesAssetPrefixPath: string;\n    experimentalThirdPartyOriginStackFrames: boolean;\n  }) => {\n    const rewriteFramesInstance = rewriteFramesIntegration({\n      // Turn `<origin>/<path>/_next/static/...` into `app:///_next/static/...`\n      iteratee: frame => {\n        if (experimentalThirdPartyOriginStackFrames) {\n          // Not sure why but access to global WINDOW from @sentry/Browser causes hideous ci errors\n          // eslint-disable-next-line no-restricted-globals\n          const windowOrigin = typeof window !== 'undefined' && window.location ? window.location.origin : '';\n          // A filename starting with the local origin and not ending with J<PERSON> is most likely JS in HTML which we do not want to rewrite\n          if (frame.filename?.startsWith(windowOrigin) && !frame.filename.endsWith('.js')) {\n            return frame;\n          }\n\n          if (assetPrefix) {\n            // If the user defined an asset prefix, we need to strip it so that we can match it with uploaded sourcemaps.\n            // assetPrefix always takes priority over basePath.\n            if (frame.filename?.startsWith(assetPrefix)) {\n              frame.filename = frame.filename.replace(assetPrefix, 'app://');\n            }\n          } else if (basePath) {\n            // If the user defined a base path, we need to strip it to match with uploaded sourcemaps.\n            // We should only do this for same-origin filenames though, so that third party assets are not rewritten.\n            try {\n              const { origin: frameOrigin } = new URL(frame.filename as string);\n              if (frameOrigin === windowOrigin) {\n                frame.filename = frame.filename?.replace(frameOrigin, 'app://').replace(basePath, '');\n              }\n            } catch (err) {\n              // Filename wasn't a properly formed URL, so there's nothing we can do\n            }\n          }\n        } else {\n          try {\n            const { origin } = new URL(frame.filename as string);\n            frame.filename = frame.filename?.replace(origin, 'app://').replace(rewriteFramesAssetPrefixPath, '');\n          } catch (err) {\n            // Filename wasn't a properly formed URL, so there's nothing we can do\n          }\n        }\n\n        // We need to URI-decode the filename because Next.js has wildcard routes like \"/users/[id].js\" which show up as \"/users/%5id%5.js\" in Error stacktraces.\n        // The corresponding sources that Next.js generates have proper brackets so we also need proper brackets in the frame so that source map resolving works.\n        if (experimentalThirdPartyOriginStackFrames) {\n          if (frame.filename?.includes('/_next')) {\n            frame.filename = decodeURI(frame.filename);\n          }\n\n          if (\n            frame.filename?.match(\n              /\\/_next\\/static\\/chunks\\/(main-|main-app-|polyfills-|webpack-|framework-|framework\\.)[0-9a-f]+\\.js$/,\n            )\n          ) {\n            // We don't care about these frames. It's Next.js internal code.\n            frame.in_app = false;\n          }\n        } else {\n          if (frame.filename?.startsWith('app:///_next')) {\n            frame.filename = decodeURI(frame.filename);\n          }\n\n          if (\n            frame.filename?.match(\n              /^app:\\/\\/\\/_next\\/static\\/chunks\\/(main-|main-app-|polyfills-|webpack-|framework-|framework\\.)[0-9a-f]+\\.js$/,\n            )\n          ) {\n            // We don't care about these frames. It's Next.js internal code.\n            frame.in_app = false;\n          }\n        }\n\n        return frame;\n      },\n    });\n\n    return {\n      ...rewriteFramesInstance,\n      name: 'NextjsClientStackFrameNormalization',\n    };\n  },\n);\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,8CAA+C,0KAAE,oBAAA,AAAiB,EAC7E,CAAC,EACC,WAAW,EACX,QAAQ,EACR,4BAA4B,EAC5B,uCAAuC,EAC3C;IAMI,MAAM,qBAAA,OAAwB,gNAAA,AAAwB,EAAC;QAC3D,yEAAA;QACM,QAAQ,GAAE,KAAA,IAAS;YACjB,IAAI,uCAAuC,EAAE;gBACrD,yFAAA;gBACA,iDAAA;gBACU,MAAM,YAAa,GAAE,OAAO,MAAO,KAAI,eAAe,MAAM,CAAC,QAAA,GAAW,MAAM,CAAC,QAAQ,CAAC,MAAA,GAAS,EAAE;gBAC7G,6HAAA;gBACU,IAAI,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,YAAY,CAAA,IAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBAC/E,OAAO,KAAK;gBACxB;gBAEU,IAAI,WAAW,EAAE;oBAC3B,6GAAA;oBACA,mDAAA;oBACY,IAAI,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,EAAE;wBAC3C,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;oBAC5E;gBACA,CAAY,MAAK,IAAI,QAAQ,EAAE;oBAC/B,0FAAA;oBACA,yGAAA;oBACY,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,WAAY,EAAA,GAAI,IAAI,GAAG,CAAC,KAAK,CAAC,QAAA,EAAmB;wBACjE,IAAI,WAAY,KAAI,YAAY,EAAE;4BAChC,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACrG;oBACA,CAAc,CAAA,OAAO,GAAG,EAAE;oBAC1B,sEAAA;oBACA;gBACA;YACA,OAAe;gBACL,IAAI;oBACF,MAAM,EAAE,MAAA,EAAS,GAAE,IAAI,GAAG,CAAC,KAAK,CAAC,QAAA,EAAmB;oBACpD,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC;gBAChH,CAAY,CAAA,OAAO,GAAG,EAAE;gBACxB,sEAAA;gBACA;YACA;YAEA,yJAAA;YACA,yJAAA;YACQ,IAAI,uCAAuC,EAAE;gBAC3C,IAAI,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;oBACtC,KAAK,CAAC,QAAS,GAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACtD;gBAEU,IACE,KAAK,CAAC,QAAQ,EAAE,KAAK,CACnB,qGAAqG,GAEvG;oBACZ,gEAAA;oBACY,KAAK,CAAC,MAAO,GAAE,KAAK;gBAChC;YACA,OAAe;gBACL,IAAI,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,cAAc,CAAC,EAAE;oBAC9C,KAAK,CAAC,QAAS,GAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACtD;gBAEU,IACE,KAAK,CAAC,QAAQ,EAAE,KAAK,CACnB,8GAA8G,GAEhH;oBACZ,gEAAA;oBACY,KAAK,CAAC,MAAO,GAAE,KAAK;gBAChC;YACA;YAEQ,OAAO,KAAK;QACpB,CAAO;IACP,CAAK,CAAC;IAEF,OAAO;QACL,GAAG,qBAAqB;QACxB,IAAI,EAAE,qCAAqC;IACjD,CAAK;AACL,CAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "file": "tunnelRoute.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/tunnelRoute.ts"], "sourcesContent": ["import { dsnFromString, GLOBAL_OBJ, logger } from '@sentry/core';\nimport type { BrowserOptions } from '@sentry/react';\nimport { DEBUG_BUILD } from '../common/debug-build';\n\nconst globalWithInjectedValues = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  _sentryRewritesTunnelPath?: string;\n};\n\n/**\n * Applies the `tunnel` option to the Next.js SDK options based on `withSentryConfig`'s `tunnelRoute` option.\n */\nexport function applyTunnelRouteOption(options: BrowserOptions): void {\n  const tunnelRouteOption = process.env._sentryRewritesTunnelPath || globalWithInjectedValues._sentryRewritesTunnelPath;\n  if (tunnelRouteOption && options.dsn) {\n    const dsnComponents = dsnFromString(options.dsn);\n    if (!dsnComponents) {\n      return;\n    }\n    const sentrySaasDsnMatch = dsnComponents.host.match(/^o(\\d+)\\.ingest(?:\\.([a-z]{2}))?\\.sentry\\.io$/);\n    if (sentrySaasDsnMatch) {\n      const orgId = sentrySaasDsnMatch[1];\n      const regionCode = sentrySaasDsnMatch[2];\n      let tunnelPath = `${tunnelRouteOption}?o=${orgId}&p=${dsnComponents.projectId}`;\n      if (regionCode) {\n        tunnelPath += `&r=${regionCode}`;\n      }\n      options.tunnel = tunnelPath;\n      DEBUG_BUILD && logger.info(`Tunneling events to \"${tunnelPath}\"`);\n    } else {\n      DEBUG_BUILD && logger.warn('Provided DSN is not a Sentry SaaS DSN. Will not tunnel events.');\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAY4B,OAAO,CAAC,GAAG,CAAC,yBAA0B;;;;;;;AARlE,MAAM,wBAAA,6KAA2B,aAAA;AAIjC;;CAEA,GACO,SAAS,sBAAsB,CAAC,OAAO,EAAwB;IACpE,MAAM,iBAAkB,sDAA2C,wBAAwB,CAAC,yBAAyB;IACrH,IAAI,iBAAA,IAAqB,OAAO,CAAC,GAAG,EAAE;QACpC,MAAM,wLAAgB,gBAAA,AAAa,EAAC,OAAO,CAAC,GAAG,CAAC;QAChD,IAAI,CAAC,aAAa,EAAE;YAClB;QACN;QACI,MAAM,kBAAmB,GAAE,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC;QACpG,IAAI,kBAAkB,EAAE;YACtB,MAAM,KAAM,GAAE,kBAAkB,CAAC,CAAC,CAAC;YACnC,MAAM,UAAW,GAAE,kBAAkB,CAAC,CAAC,CAAC;YACxC,IAAI,UAAA,GAAa,CAAC,EAAA,iBAAA,CAAA,GAAA,EAAA,KAAA,CAAA,GAAA,EAAA,aAAA,CAAA,SAAA,CAAA,CAAA;YACA,IAAA,UAAA,EAAA;gBACA,UAAA,IAAA,CAAA,GAAA,EAAA,UAAA,CAAA,CAAA;YACA;YACA,OAAA,CAAA,MAAA,GAAA,UAAA;8LACA,cAAA,2KAAA,SAAA,CAAA,IAAA,CAAA,CAAA,qBAAA,EAAA,UAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA,MAAA;8LACA,cAAA,2KAAA,SAAA,CAAA,IAAA,CAAA,gEAAA,CAAA;QACA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40sentry/nextjs/src/client/index.ts"], "sourcesContent": ["import type { Client, EventProcessor, Integration } from '@sentry/core';\nimport { addEventProcessor, applySdkMetadata, consoleSandbox, getGlobalScope, GLOBAL_OBJ } from '@sentry/core';\nimport type { BrowserOptions } from '@sentry/react';\nimport { getDefaultIntegrations as getReactDefaultIntegrations, init as reactInit } from '@sentry/react';\nimport { devErrorSymbolicationEventProcessor } from '../common/devErrorSymbolicationEventProcessor';\nimport { getVercelEnv } from '../common/getVercelEnv';\nimport { isRedirectNavigationError } from '../common/nextNavigationErrorUtils';\nimport { browserTracingIntegration } from './browserTracingIntegration';\nimport { nextjsClientStackFrameNormalizationIntegration } from './clientNormalizationIntegration';\nimport { INCOMPLETE_APP_ROUTER_INSTRUMENTATION_TRANSACTION_NAME } from './routing/appRouterRoutingInstrumentation';\nimport { applyTunnelRouteOption } from './tunnelRoute';\n\nexport * from '@sentry/react';\nexport * from '../common';\nexport { captureUnderscoreErrorException } from '../common/pages-router-instrumentation/_error';\nexport { browserTracingIntegration } from './browserTracingIntegration';\nexport { captureRouterTransitionStart } from './routing/appRouterRoutingInstrumentation';\n\nlet clientIsInitialized = false;\n\nconst globalWithInjectedValues = GLOBAL_OBJ as typeof GLOBAL_OBJ & {\n  _sentryRewriteFramesAssetPrefixPath: string;\n  _sentryAssetPrefix?: string;\n  _sentryBasePath?: string;\n  _sentryRelease?: string;\n  _experimentalThirdPartyOriginStackFrames?: string;\n};\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean;\n\n/** Inits the Sentry NextJS SDK on the browser with the React SDK. */\nexport function init(options: BrowserOptions): Client | undefined {\n  if (clientIsInitialized) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[@sentry/nextjs] You are calling `Sentry.init()` more than once on the client. This can happen if you have both a `sentry.client.config.ts` and a `instrumentation-client.ts` file with `Sentry.init()` calls. It is recommended to call `Sentry.init()` once in `instrumentation-client.ts`.',\n      );\n    });\n  }\n  clientIsInitialized = true;\n\n  const opts = {\n    environment: getVercelEnv(true) || process.env.NODE_ENV,\n    defaultIntegrations: getDefaultIntegrations(options),\n    release: process.env._sentryRelease || globalWithInjectedValues._sentryRelease,\n    ...options,\n  } satisfies BrowserOptions;\n\n  applyTunnelRouteOption(opts);\n  applySdkMetadata(opts, 'nextjs', ['nextjs', 'react']);\n\n  const client = reactInit(opts);\n\n  const filterTransactions: EventProcessor = event =>\n    event.type === 'transaction' && event.transaction === '/404' ? null : event;\n  filterTransactions.id = 'NextClient404Filter';\n  addEventProcessor(filterTransactions);\n\n  const filterIncompleteNavigationTransactions: EventProcessor = event =>\n    event.type === 'transaction' && event.transaction === INCOMPLETE_APP_ROUTER_INSTRUMENTATION_TRANSACTION_NAME\n      ? null\n      : event;\n  filterIncompleteNavigationTransactions.id = 'IncompleteTransactionFilter';\n  addEventProcessor(filterIncompleteNavigationTransactions);\n\n  const filterNextRedirectError: EventProcessor = (event, hint) =>\n    isRedirectNavigationError(hint?.originalException) || event.exception?.values?.[0]?.value === 'NEXT_REDIRECT'\n      ? null\n      : event;\n  filterNextRedirectError.id = 'NextRedirectErrorFilter';\n  addEventProcessor(filterNextRedirectError);\n\n  if (process.env.NODE_ENV === 'development') {\n    addEventProcessor(devErrorSymbolicationEventProcessor);\n  }\n\n  try {\n    // @ts-expect-error `process.turbopack` is a magic string that will be replaced by Next.js\n    if (process.turbopack) {\n      getGlobalScope().setTag('turbopack', true);\n    }\n  } catch (e) {\n    // Noop\n    // The statement above can throw because process is not defined on the client\n  }\n\n  return client;\n}\n\nfunction getDefaultIntegrations(options: BrowserOptions): Integration[] {\n  const customDefaultIntegrations = getReactDefaultIntegrations(options);\n  // This evaluates to true unless __SENTRY_TRACING__ is text-replaced with \"false\",\n  // in which case everything inside will get tree-shaken away\n  if (typeof __SENTRY_TRACING__ === 'undefined' || __SENTRY_TRACING__) {\n    customDefaultIntegrations.push(browserTracingIntegration());\n  }\n\n  // These values are injected at build time, based on the output directory specified in the build config. Though a default\n  // is set there, we set it here as well, just in case something has gone wrong with the injection.\n  const rewriteFramesAssetPrefixPath =\n    process.env._sentryRewriteFramesAssetPrefixPath ||\n    globalWithInjectedValues._sentryRewriteFramesAssetPrefixPath ||\n    '';\n  const assetPrefix = process.env._sentryAssetPrefix || globalWithInjectedValues._sentryAssetPrefix;\n  const basePath = process.env._sentryBasePath || globalWithInjectedValues._sentryBasePath;\n  const experimentalThirdPartyOriginStackFrames =\n    process.env._experimentalThirdPartyOriginStackFrames === 'true' ||\n    globalWithInjectedValues._experimentalThirdPartyOriginStackFrames === 'true';\n  customDefaultIntegrations.push(\n    nextjsClientStackFrameNormalizationIntegration({\n      assetPrefix,\n      basePath,\n      rewriteFramesAssetPrefixPath,\n      experimentalThirdPartyOriginStackFrames,\n    }),\n  );\n\n  return customDefaultIntegrations;\n}\n\n/**\n * Just a passthrough in case this is imported from the client.\n */\nexport function withSentryConfig<T>(exportedUserNextConfig: T): T {\n  return exportedUserNextConfig;\n}\n"], "names": ["reactInit", "getReactDefaultIntegrations"], "mappings": ";;;;AA4CuC,OAAO,CAAC,GAAG,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1B3D,IAAI,mBAAA,GAAsB,KAAK;AAE/B,MAAM,wBAAA,6KAA2B,aAAA;AAQjC,2DAAA;AAGA,mEAAA,GACO,SAAS,IAAI,CAAC,OAAO,EAAsC;IAChE,IAAI,mBAAmB,EAAE;mLACvB,iBAAA,AAAc,EAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CACV,+RAA+R;QAEvS,CAAK,CAAC;IACN;IACE,mBAAA,GAAsB,IAAI;IAE1B,MAAM,OAAO;QACX,WAAW,GAAE,kMAAA,AAAY,EAAC,IAAI,CAAE;QAChC,mBAAmB,EAAE,sBAAsB,CAAC,OAAO,CAAC;QACpD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,cAAe,sDAAG,wBAAwB,CAAC,cAAc;QAC9E,GAAG,OAAO;IACd,CAAI;uLAEF,yBAAA,AAAsB,EAAC,IAAI,CAAC;oLAC5B,mBAAA,AAAgB,EAAC,IAAI,EAAE,QAAQ,EAAE;QAAC,QAAQ;QAAE,OAAO;KAAC,CAAC;IAErD,MAAM,MAAO,mKAAEA,OAAAA,AAAS,EAAC,IAAI,CAAC;IAE9B,MAAM,kBAAkB,IAAmB,KAAM,GAC/C,KAAK,CAAC,IAAK,KAAI,iBAAiB,KAAK,CAAC,WAAA,KAAgB,MAAA,GAAS,IAAA,GAAO,KAAK;IAC7E,kBAAkB,CAAC,EAAG,GAAE,qBAAqB;uKAC7C,oBAAA,AAAiB,EAAC,kBAAkB,CAAC;IAErC,MAAM,sCAAsC,IAAmB,KAAM,GACnE,KAAK,CAAC,IAAK,KAAI,iBAAiB,KAAK,CAAC,WAAA,mNAAgB,yDAAA,GAClD,OACA,KAAK;IACX,sCAAsC,CAAC,EAAG,GAAE,6BAA6B;KACzE,sLAAA,AAAiB,EAAC,sCAAsC,CAAC;IAEzD,MAAM,uBAAuB,GAAmB,CAAC,KAAK,EAAE,IAAI,mMAC1D,4BAAA,AAAyB,EAAC,IAAI,EAAE,iBAAiB,CAAA,IAAK,KAAK,CAAC,SAAS,EAAE,MAAM,EAAA,CAAG,CAAC,CAAC,EAAE,UAAU,kBAC1F,OACA,KAAK;IACX,uBAAuB,CAAC,EAAG,GAAE,yBAAyB;uKACtD,oBAAA,AAAiB,EAAC,uBAAuB,CAAC;IAE1C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAA,KAAa,WAAe,EAAF;QACxC,uLAAA,AAAiB,yMAAC,sCAAmC,CAAC;IAC1D;IAEE,IAAI;QACN,0FAAA;QACI,IAAI,OAAO,CAAC,SAAS,mBAAE;qLACrB,iBAAA,AAAc,EAAE,EAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC;QAChD;IACA,CAAI,CAAA,OAAO,CAAC,EAAE;IACd,OAAA;IACA,6EAAA;IACA;IAEE,OAAO,MAAM;AACf;AAEA,SAAS,sBAAsB,CAAC,OAAO,EAAiC;IACtE,MAAM,yBAA0B,GAAEC,kMAAAA,AAA2B,EAAC,OAAO,CAAC;IACxE,kFAAA;IACA,4DAAA;IACE,IAAI,OAAO,kBAAA,KAAuB,WAAY,IAAG,kBAAkB,EAAE;QACnE,yBAAyB,CAAC,IAAI,kMAAC,4BAAA,AAAyB,EAAE,CAAC;IAC/D;IAEA,yHAAA;IACA,kGAAA;IACE,MAAM,4BAA6B,GACjC,OAAO,CAAC,GAAG,CAAC,4BACZ,OADgD,iBACxB,CAAC,mCAAoC,IAC7D,EAAE;IACJ,MAAM,WAAY,oKAAE,UAAO,CAAC,GAAG,CAAC,kBAAmB,IAAG,wBAAwB,CAAC,kBAAkB;IACjG,MAAM,QAAS,oKAAE,UAAO,CAAC,GAAG,CAAC,eAAgB,IAAG,wBAAwB,CAAC,eAAe;IACxF,MAAM,uCAAwC,oKAC5C,UAAO,CAAC,GAAG,CAAC,wCAAA,KAA6C,MAAO,IAChE,wBAAwB,CAAC,wCAAyC,KAAI,MAAM;IAC9E,yBAAyB,CAAC,IAAI,uMAC5B,iDAAA,AAA8C,EAAC;QAC7C,WAAW;QACX,QAAQ;QACR,4BAA4B;QAC5B,uCAAuC;IAC7C,CAAK,CAAC;IAGJ,OAAO,yBAAyB;AAClC;AAEA;;CAEA,GACO,SAAS,gBAAgB,CAAI,sBAAsB,EAAQ;IAChE,OAAO,sBAAsB;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}]}