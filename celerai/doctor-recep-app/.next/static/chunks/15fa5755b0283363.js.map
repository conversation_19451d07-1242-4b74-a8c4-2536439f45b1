{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/pages/_app.tsx"], "sourcesContent": ["import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n"], "names": ["App", "appGetInitialProps", "Component", "ctx", "pageProps", "loadGetInitialProps", "React", "render", "props", "origGetInitialProps", "getInitialProps"], "mappings": "uSAiCqBA,2CAjCH,CAAA,CAAA,IAAA,SAWkB,CAAA,CAAA,IAAA,IAcpC,eAAeC,EAAmB,CAGrB,EAHqB,GAAA,WAChCC,CAAS,KACTC,CAAG,CACQ,CAHqB,EAKhC,MAAO,CAAEC,UADS,MAAMC,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACH,EAAWC,EACpC,CACrB,CAEe,MAAMH,UAAsCM,EAAAA,OAAK,CAACJ,SAAS,CAOxEK,QAAS,CACP,GAAM,WAAEL,CAAS,WAAEE,CAAS,CAAE,CAAG,IAAI,CAACI,KAAK,CAE3C,MAAO,CAAP,AAAO,EAAA,EAAA,GAAA,EAACN,EAAR,AAAQA,CAAW,GAAGE,CAAS,EACjC,CACF,CAZqBJ,EAIZS,mBAAAA,CAAsBR,EAJVD,EAKZU,eAAAA,CAAkBT", "ignoreList": [0]}