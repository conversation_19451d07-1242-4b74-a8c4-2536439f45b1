{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/instrumentation-client.ts"], "sourcesContent": ["// This file configures the initialization of Sentry on the client.\n// The added config here will be used whenever a users loads a page in their browser.\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\n\nimport * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: \"https://<EMAIL>/4509552950640720\",\n\n  // Add optional integrations for additional features\n  integrations: [\n    Sentry.replayIntegration(),\n  ],\n\n  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\n  tracesSampleRate: 1,\n\n  // Define how likely Replay events are sampled.\n  // This sets the sample rate to be 10%. You may want this to be 100% while\n  // in development and sample at a lower rate in production\n  replaysSessionSampleRate: 0.1,\n\n  // Define how likely Replay events are sampled when an error occurs.\n  replaysOnErrorSampleRate: 1.0,\n\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\n  debug: false,\n});\n\nexport const onRouterTransitionStart = Sentry.captureRouterTransitionStart;"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,qFAAqF;AACrF,6DAA6D;;;;AAE7D;AAAA;AAAA;;AAEA,CAAA,GAAA,wLAAA,CAAA,OAAW,AAAD,EAAE;IACV,KAAK;IAEL,oDAAoD;IACpD,cAAc;QACZ,CAAA,GAAA,iLAAA,CAAA,oBAAwB,AAAD;KACxB;IAED,mHAAmH;IACnH,kBAAkB;IAElB,+CAA+C;IAC/C,0EAA0E;IAC1E,0DAA0D;IAC1D,0BAA0B;IAE1B,oEAAoE;IACpE,0BAA0B;IAE1B,2GAA2G;IAC3G,OAAO;AACT;AAEO,MAAM,0BAA0B,6MAAA,CAAA,+BAAmC", "debugId": null}}]}