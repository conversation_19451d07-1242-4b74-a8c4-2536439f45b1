(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{12597:function(e){var{g:t,__dirname:n,m:r,e:a}=e;{"use strict";e.i(922271),Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"warnOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}}},884991:e=>{"use strict";var{g:t,__dirname:n}=e;{function r(){return["/dashboard","/info","/settings","/templates","/admin"].some(e=>window.location.pathname.startsWith(e))}e.s({initializeAnalytics:()=>c,trackAuth:()=>l,trackConsultation:()=>s,trackEvent:()=>i,trackPageView:()=>o,trackQuotaWarning:()=>u});let t=["consultation_generated","summary_approved","quota_warning","dashboard_viewed","settings_accessed","template_updated"];function a(e){let t={},n=["page_title","consultation_type","quota_percentage","feature_used","error_type"];for(let[r,a]of Object.entries(e))n.includes(r)&&void 0!==a&&("quota_percentage"===r&&"number"==typeof a?t[r]=Math.round(a):"string"==typeof a&&a.length<100?t[r]=a:"boolean"==typeof a&&(t[r]=a));return t}function i(e,n={}){if("function"!=typeof window.gtag)return void console.warn("GA4 not loaded, skipping analytics event:",e);try{if(r()){if(!t.includes(e))return void console.warn(`Analytics: Blocked unauthorized event in authenticated zone: ${e}`);let r=a(n);console.log(`Analytics: Tracking safe authenticated event: ${e}`,r),window.gtag("event",e,r)}else{let t=a(n);console.log(`Analytics: Tracking public event: ${e}`,t),window.gtag("event",e,t)}}catch(e){console.error("Analytics tracking error:",e)}}function o(e){r()?i("dashboard_viewed",{page_title:"Dashboard"}):i("page_view",{page_title:e})}function s(e,t){r()&&("generated"===e?i("consultation_generated",{consultation_type:t}):"approved"===e&&i("summary_approved",{consultation_type:t}))}function u(e){r()&&i("quota_warning",{quota_percentage:e})}function l(e){r()||i(e)}function c(){console.log("Analytics initialized with two-zone security model"),o(document.title)}}},555366:function(e){var{g:t,__dirname:n,m:r,e:a}=e;r.exports=e.r(341842)},992051:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({Analytics:()=>w});var r=e.i(922271),a=e.i(838653),i=e.i(555366),o=()=>{window.va||(window.va=function(...e){(window.vaq=window.vaq||[]).push(e)})};function s(){return"undefined"!=typeof window}function u(){return"production"}function l(){return"development"===((s()?window.vam:u())||"production")}function c(e){return RegExp(`/${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(?=[/?#]|$)`)}function d(e){return(0,a.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,a.useEffect)(()=>{!function(e={debug:!0}){var t;if(!s())return;!function(e="auto"){if("auto"===e){window.vam=u();return}window.vam=e}(e.mode),o(),e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend));let n=e.scriptSrc?e.scriptSrc:l()?"https://va.vercel-scripts.com/v1/script.debug.js":e.basePath?`${e.basePath}/insights/script.js`:"/_vercel/insights/script.js";if(document.head.querySelector(`script[src*="${n}"]`))return;let r=document.createElement("script");r.src=n,r.defer=!0,r.dataset.sdkn="@vercel/analytics"+(e.framework?`/${e.framework}`:""),r.dataset.sdkv="1.5.0",e.disableAutoTrack&&(r.dataset.disableAutoTrack="1"),e.endpoint?r.dataset.endpoint=e.endpoint:e.basePath&&(r.dataset.endpoint=`${e.basePath}/insights`),e.dsn&&(r.dataset.dsn=e.dsn),r.onerror=()=>{let e=l()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log(`[Vercel Web Analytics] Failed to load script from ${n}. ${e}`)},l()&&!1===e.debug&&(r.dataset.debug="false"),document.head.appendChild(r)}({framework:e.framework||"react",basePath:e.basePath??function(){if(void 0!==r.default&&void 0!==r.default.env)return r.default.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,a.useEffect)(()=>{e.route&&e.path&&function({route:e,path:t}){var n;null==(n=window.va)||n.call(window,"pageview",{route:e,path:t})}({route:e.route,path:e.path})},[e.route,e.path]),null}var f=()=>{let e=(0,i.useParams)(),t=(0,i.useSearchParams)(),n=(0,i.usePathname)();return e?{route:function(e,t){if(!e||!t)return e;let n=e;try{let e=Object.entries(t);for(let[t,r]of e)if(!Array.isArray(r)){let e=c(r);e.test(n)&&(n=n.replace(e,`/[${t}]`))}for(let[t,r]of e)if(Array.isArray(r)){let e=c(r.join("/"));e.test(n)&&(n=n.replace(e,`/[...${t}]`))}return n}catch(t){return e}}(n,Object.keys(e).length?e:Object.fromEntries(t.entries())),path:n}:{route:null,path:n}};function p(e){let{route:t,path:n}=f();return a.default.createElement(d,{path:n,route:t,...e,basePath:function(){if(void 0!==r.default&&void 0!==r.default.env)return r.default.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}(),framework:"next"})}function w(e){return a.default.createElement(a.Suspense,{fallback:null},a.default.createElement(p,{...e}))}},104927:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({SpeedInsights:()=>f});var r=e.i(922271),a=e.i(838653),i=e.i(555366),o=()=>{window.si||(window.si=function(...e){(window.siq=window.siq||[]).push(e)})};function s(){return false}function u(e){return RegExp(`/${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(?=[/?#]|$)`)}function l(e){(0,a.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{let n=function(e={}){var t;if("undefined"==typeof window||null===e.route)return null;o();let n=e.scriptSrc?e.scriptSrc:s()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":e.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":e.basePath?`${e.basePath}/speed-insights/script.js`:"/_vercel/speed-insights/script.js";if(document.head.querySelector(`script[src*="${n}"]`))return null;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend));let r=document.createElement("script");return r.src=n,r.defer=!0,r.dataset.sdkn="@vercel/speed-insights"+(e.framework?`/${e.framework}`:""),r.dataset.sdkv="1.2.0",e.sampleRate&&(r.dataset.sampleRate=e.sampleRate.toString()),e.route&&(r.dataset.route=e.route),e.endpoint?r.dataset.endpoint=e.endpoint:e.basePath&&(r.dataset.endpoint=`${e.basePath}/speed-insights/vitals`),e.dsn&&(r.dataset.dsn=e.dsn),s()&&!1===e.debug&&(r.dataset.debug="false"),r.onerror=()=>{console.log(`[Vercel Speed Insights] Failed to load script from ${n}. Please check if any content blockers are enabled and try again.`)},document.head.appendChild(r),{setRoute:e=>{r.dataset.route=e??void 0}}}({framework:e.framework??"react",basePath:e.basePath??function(){if(void 0!==r.default&&void 0!==r.default.env)return r.default.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});n&&(t.current=n.setRoute)}},[e.route]),null}var c=()=>{let e=(0,i.useParams)(),t=(0,i.useSearchParams)()||new URLSearchParams,n=(0,i.usePathname)();return e?function(e,t){if(!e||!t)return e;let n=e;try{let e=Object.entries(t);for(let[t,r]of e)if(!Array.isArray(r)){let e=u(r);e.test(n)&&(n=n.replace(e,`/[${t}]`))}for(let[t,r]of e)if(Array.isArray(r)){let e=u(r.join("/"));e.test(n)&&(n=n.replace(e,`/[...${t}]`))}return n}catch(t){return e}}(n,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function d(e){let t=c();return a.default.createElement(l,{route:t,...e,framework:"next",basePath:function(){if(void 0!==r.default&&void 0!==r.default.env)return r.default.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function f(e){return a.default.createElement(a.Suspense,{fallback:null},a.default.createElement(d,{...e}))}},211942:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({AnalyticsProvider:()=>s});var r=e.i(731636),a=e.i(838653),i=e.i(555366),o=e.i(884991);function s({children:e}){let t=(0,i.usePathname)();return(0,a.useEffect)(()=>{let e=setTimeout(()=>{"requestIdleCallback"in window?requestIdleCallback(()=>(0,o.initializeAnalytics)(),{timeout:2e3}):(0,o.initializeAnalytics)()},500);return()=>clearTimeout(e)},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{"requestIdleCallback"in window?requestIdleCallback(()=>(0,o.trackPageView)(document.title),{timeout:1e3}):(0,o.trackPageView)(document.title)},100);return()=>clearTimeout(e)},[t]),(0,r.jsx)(r.Fragment,{children:e})}}}]);

//# sourceMappingURL=5927a063fc0c40fe.js.map