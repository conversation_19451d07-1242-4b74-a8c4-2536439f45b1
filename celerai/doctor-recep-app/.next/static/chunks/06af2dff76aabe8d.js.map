{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts", "turbopack:///[project]/node_modules/next/src/client/use-merged-ref.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils/error-once.ts", "turbopack:///[project]/node_modules/next/src/client/app-dir/link.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/side-effect.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/amp-context.shared-runtime.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/amp-mode.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/head.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/image-blur-svg.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/image-config.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/get-img-props.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/image-config-context.shared-runtime.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/image-loader.ts", "turbopack:///[project]/node_modules/next/src/client/image-component.tsx", "turbopack:///[project]/src/lib/actions/data:e8d848 <text/javascript>", "turbopack:///[project]/src/components/auth/signup-form.tsx"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n", "import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n", "/* __next_internal_action_entry_do_not_use__ [{\"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d\":\"signup\"},\"src/lib/actions/auth.ts\",\"\"] */\"use turbopack no side effects\";import{createServerReference,callServer,findSourceMapURL}from\"private-next-rsc-action-client-wrapper\";export var signup=/*#__PURE__*/createServerReference(\"607d8dc567442cb3b7d1e7963a8393b3cb3cfbb26d\",callServer,void 0,findSourceMapURL,\"signup\");", "'use client'\n\nimport { useActionState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { signup } from '@/lib/actions/auth'\nimport { FormState } from '@/lib/types'\nimport { trackAuth } from '@/lib/analytics'\n\n// CORRECTED initialState to conform to FormState interface\nconst initialState: FormState = {\n  success: false,\n  message: '',\n  // fieldErrors is optional, so it can be omitted or set to an empty object if needed\n  // fieldErrors: {},\n}\n\ninterface SignupFormProps {\n  referralCode?: string\n}\n\nexport function SignupForm({ referralCode }: SignupFormProps) {\n  const [state, formAction, isPending] = useActionState(signup, initialState)\n  const router = useRouter()\n\n  // Auto-redirect to login page after 3 seconds for successful account creation\n  useEffect(() => {\n    if (state?.success && state?.message?.includes('Account created successfully')) {\n      // Track successful signup completion\n      trackAuth('signup_completed')\n\n      const timer = setTimeout(() => {\n        router.push('/login')\n      }, 3000)\n\n      return () => clearTimeout(timer)\n    }\n  }, [state?.success, state?.message, router])\n\n  // Handle form submission to track signup started\n  const handleSubmit = (formData: FormData) => {\n    // Track signup attempt\n    trackAuth('signup_started')\n\n    // Call the original form action\n    formAction(formData)\n  }\n\n  return (\n    <form action={handleSubmit} className=\"mt-8 space-y-6\">\n      {/* Hidden field for referral code */}\n      {referralCode && (\n        <input\n          type=\"hidden\"\n          name=\"referral_code\"\n          value={referralCode}\n        />\n      )}\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Full Name\n          </label>\n          <input\n            id=\"name\"\n            name=\"name\"\n            type=\"text\"\n            autoComplete=\"name\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Dr. John Doe\"\n          />\n          {state?.fieldErrors?.name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"<EMAIL>\"\n          />\n          {state?.fieldErrors?.email && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.email[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Password\n          </label>\n          <input\n            id=\"password\"\n            name=\"password\"\n            type=\"password\"\n            autoComplete=\"new-password\"\n            required\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Create a strong password\"\n          />\n          {state?.fieldErrors?.password && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.password[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"clinic_name\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Hospital Name\n          </label>\n          <input\n            id=\"clinic_name\"\n            name=\"clinic_name\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"ABC Medical Center\"\n          />\n          {state?.fieldErrors?.clinic_name && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.clinic_name[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Phone Number\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-4\">\n              <span className=\"text-slate-500 text-sm font-medium\">+91</span>\n            </div>\n            <input\n              id=\"phone\"\n              name=\"phone\"\n              type=\"tel\"\n              autoComplete=\"tel\"\n              className=\"w-full pl-12 pr-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n              placeholder=\"9876543210\"\n            />\n          </div>\n          {state?.fieldErrors?.phone && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.phone[0]}</span>\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label htmlFor=\"referral_code\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Referral ID (Optional)\n          </label>\n          <input\n            id=\"referral_id\"\n            name=\"referral_code\"\n            type=\"text\"\n            className=\"w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/50 backdrop-blur-sm placeholder-slate-400 text-slate-900\"\n            placeholder=\"Enter referral ID if you have one (optional)\"\n            defaultValue={referralCode || ''}\n          />\n          {state?.fieldErrors?.referral_code && (\n            <p className=\"mt-2 text-sm text-red-600 flex items-center space-x-1\">\n              <span className=\"w-1 h-1 bg-red-600 rounded-full\"></span>\n              <span>{state.fieldErrors.referral_id[0]}</span>\n            </p>\n          )}\n        </div>\n\n        {/* Terms of Service and Privacy Policy Checkbox */}\n        <div className=\"flex items-start space-x-3\">\n          <input\n            id=\"terms\"\n            name=\"terms\"\n            type=\"checkbox\"\n            required\n            className=\"mt-1 w-4 h-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500 focus:ring-2\"\n          />\n          <label htmlFor=\"terms\" className=\"text-sm text-slate-600 leading-relaxed\">\n            I accept the{' '}\n            <a href=\"/terms\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Terms of Service\n            </a>\n            {' '}and{' '}\n            <a href=\"/privacy\" target=\"_blank\" className=\"text-indigo-600 hover:text-purple-600 font-medium transition-colors\">\n              Privacy Policy\n            </a>\n          </label>\n        </div>\n      </div>\n\n      {state?.message && (\n        <div className={`rounded-xl p-4 border ${\n          state.success\n            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'\n            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'\n        }`}>\n          <p className={`text-sm font-medium flex items-center space-x-2 ${\n            state.success ? 'text-emerald-800' : 'text-red-800'\n          }`}>\n            <span className={`w-2 h-2 rounded-full ${\n              state.success ? 'bg-emerald-400' : 'bg-red-400'\n            }`}></span>\n            <span>{state.message}</span>\n          </p>\n        </div>\n      )}\n\n      <div>\n        <button\n          type=\"submit\"\n          disabled={isPending}\n          className=\"group relative w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2\"\n        >\n          {isPending ? (\n            <>\n              <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n              <span>Creating your magical account...</span>\n            </>\n          ) : (\n            <>\n              <span>Start Creating Magic</span>\n              <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n              </svg>\n            </>\n          )}\n        </button>\n      </div>\n    </form>\n  )\n}"], "names": ["process", "env", "NEXT_RUNTIME", "require", "callServer", "createServerReference", "findSourceMapURL", "useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "NODE_ENV", "errorOnce", "_", "LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "window", "useMemo", "resolvedHref", "child", "Children", "only", "childRef", "observeLinkVisibilityOnMount", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "type", "addBasePath", "link", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext", "SideEffect", "isServer", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "delete", "_pendingUpdate", "AmpStateContext", "isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "map", "c", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "Head", "ampState", "HeadManagerContext", "Effect", "getImageBlurSvg", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "blurDataURL", "objectFit", "std", "svgWidth", "svgHeight", "viewBox", "preserveAspectRatio", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "remotePatterns", "qualities", "unoptimized", "getImgProps", "VALID_LOADING_VALUES", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "smallestRatio", "Math", "min", "widths", "s", "kind", "w", "find", "p", "generateImgAttrs", "config", "quality", "srcSet", "last", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "fetchPriority", "decoding", "layout", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "sort", "b", "isDefaultLoader", "customImageLoader", "obj", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "split", "endsWith", "qualityInt", "imgStyle", "Object", "assign", "position", "left", "top", "right", "bottom", "color", "backgroundImage", "backgroundSize", "includes", "placeholder<PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "imgAttributes", "ImageConfigContext", "DEFAULT_Q", "q", "prev", "cur", "abs", "encodeURIComponent", "NEXT_DEPLOYMENT_ID", "__next_img_default", "__NEXT_IMAGE_OPTS", "Image", "configEnv", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "Event", "defineProperty", "writable", "prevented", "stopped", "isPropagationStopped", "persist", "stopPropagation", "getDynamicProps", "use", "fetchpriority", "ImageElement", "forwardRef", "setShowAltText", "onError", "ownRef", "complete", "data-nimg", "ImagePreload", "isAppRouter", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "rel", "pagesRouter", "RouterContext", "configContext", "useState", "imgMeta"], "mappings": "kKAec,EAAA,CAAA,CAAA,OAERG,QAAQ,8DAdLC,UAAU,CAAA,kBAAVA,EAAAA,UAAU,EASNC,qBAAqB,CAAA,kBAArBA,GARJC,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,8EADE,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,IAQpBD,EACV,AAAC,CAAD,CAAC,CAAA,CAAA,OAI2C,CAC7CA,qBAAqB,4ICTPE,eAAAA,qCAAAA,aAT8B,CAAA,CAAA,IAAA,IASvC,SAASA,EACdC,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MACvCC,EAAWD,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MAS7C,MAAOE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAChB,AAACC,IACC,GAAIA,AAAY,SAAM,CACpB,IAAMC,EAAaL,EAASI,OAAO,CAC/BC,IACFL,EAASI,MADK,CACE,CAAG,KACnBC,KAEF,IAAMC,EAAaJ,EAASE,OAAO,CAC/BE,IACFJ,EAASE,MADK,CACE,CAAG,KACnBE,IAEJ,MACMR,CADC,GAEHE,EADQ,AACCI,OAAO,CAAGG,EAAST,EAAMM,EAAAA,EAEhCL,GACFG,GADQ,AACCE,OAAO,CAAGG,EAASR,EAAMK,EAAAA,CAGxC,EACA,CAACN,EAAMC,EAAK,CAEhB,CAEA,SAASQ,EACPT,CAAgC,CAChCM,CAAiB,EAEjB,GAAoB,YAAhB,OAAON,EAST,OADAA,EAAKM,OAAO,CAAGA,EACR,KACLN,EAAKM,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAMI,EAAUV,EAAKM,SACrB,AAAI,AAAmB,YAAY,OAAxBI,EACFA,EAEA,IAAMV,EAAK,KAEtB,CAMF,MANS,oRC3DoB,EAAA,CAAA,CAAA,iFAUpBY,YAAAA,qCAAAA,KAXT,IAAIA,EAAY,AAACC,IAAe,oECkXD,EAAA,CAAA,CAAA,gEArD/B,OAyZC,CAAA,kBAzZuBC,GA+ZXC,aAAa,CAAA,kBAAbA,+GA1tB2D,CAAA,CAAA,IAAA,SAE9C,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,OACJ,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACH,CAAA,CAAA,IAAA,WASlB,CAAA,CAAA,IAAA,QACoB,CAAA,CAAA,IAAA,QACY,CAAA,CAAA,IAAA,IAsRvC,SAASiC,EAAkBC,CAAkC,QAC3D,AAAI,AAA0B,UAAU,OAA7BA,EACFA,EAGFC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAACD,EACnB,CAYe,SAASnC,EACtBqC,CAGC,EAED,IAEIK,EA+LAoC,EAyLAmB,EA1XE,CAAC3D,EAAYC,EAAwB,CAAGC,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAACC,EAAAA,gBAAgB,EAItEvB,EAAkB7B,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MAE9C,CACJ2B,KAAM2B,CAAQ,CACd1B,GAAI2B,CAAM,CACVF,SAAUG,CAAY,CACtBC,SAAUC,EAAe,IAAI,CAC7BC,UAAQ,SACR7B,CAAO,SACP8B,CAAO,QACP7B,CAAM,CACN8B,SAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,YACtBlC,CAAU,CACVmC,IAAKC,CAAY,yBACjBC,CAAuB,CACvB,GAAGC,EACJ,CAAGtB,EAEJK,EAAWG,EAGTU,IACqB,UAApB,IAAA,GAAOb,GAA6C,UAApB,OAAOA,CAAa,CAAO,GAC5D,AACAA,EAAW,CAAA,EAAA,EAAA,GAAA,CAAXA,CAAYkB,IAAAA,MAAZlB,IAAeA,KAGjB,IAAMmB,EAAS7B,EAAAA,OAAK,CAAC8B,UAAU,CAACC,EAAAA,gBAAgB,EAE1CC,GAAmC,IAAjBjB,EAQlBkB,EACJlB,AAAiB,SAAOmB,EAAAA,YAAY,CAACC,IAAI,CAAGD,EAAAA,YAAY,CAACE,IAAI,CA2IzD,MAAEpD,CAAI,IAAEC,CAAE,CAAE,CAAGe,EAAAA,OAAK,CAAC4C,OAAO,CAAC,KACjC,IAAMC,EAAe3C,EAAkBS,GACvC,MAAO,CACL3B,KAAM6D,EACN5D,GAAI2B,EAASV,EAAkBU,GAAUiC,CAC3C,CACF,EAAG,CAAClC,EAAUC,EAAO,EAIjBW,IA4BAuB,EAAQ9C,EAAAA,OAAK,CA5BG,AA4BF+C,QAAQ,CAACC,IAAI,CAACtC,EAAAA,EAYhC,IAAMuC,EAAgB1B,EAClBuB,GAA0B,UAAjB,OAAOA,GAAsBA,EAAMtB,GAAG,CAC/CC,EAMEyB,EAA+BlD,EAAAA,OAAK,CAACzC,WAAW,CACpD,AAAC4F,IACKtB,AAAW,MAAM,KACnB3C,EAAgB1B,OAAO,CAAG4F,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACzCD,EACAnE,EACA6C,EACAI,EACAD,EACAzB,EAAAA,EAIG,KACDrB,EAAgB1B,OAAO,EAAE,CAC3B6F,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACnE,EAAgB1B,OAAO,EACvD0B,EAAgB1B,OAAO,CAAG,MAE5B8F,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACH,EAC9B,GAEF,CAACnB,EAAiBhD,EAAM6C,EAAQI,EAAiB1B,EAAwB,EAKrEiD,EAMF,CACFhC,IATgBvE,CAAAA,AASXsG,EATWtG,EAAAA,YAAAA,AAAY,EAACiG,EAA8BD,GAU3D/B,QAAQnC,CAAC,EASH,AAACwC,GAAqC,YAAnB,AAA+B,OAAxBL,GAC5BA,EAAQnC,GAIRwC,GACAuB,EAAMzC,KAAK,EACX,AAA+B,YAC/B,OADOyC,EAAMzC,KAAK,CAACa,OAAO,EAE1B4B,EAAMzC,KAAK,CAACa,OAAO,CAACnC,GAGjB8C,IAID9C,EAAE0E,EAJO,cAIS,EAItB3E,AAvYN,AAmY8B,SAnYrBA,AACPC,CAAmB,CACnBC,CAAY,CACZC,CAAU,CACVC,CAAqD,CACrDC,CAAiB,CACjBC,CAAgB,CAChBC,CAAmC,EAEnC,GAAM,UAAEC,CAAQ,CAAE,CAAGP,EAAEV,aAAa,CAKpC,KAFoD,AAGjDkB,MAHsBD,EAASE,WAAW,IAzB/C,AA4ByBtB,SA5BhBA,AAAgBC,CAAuB,EAE9C,IAAMG,EADcH,AACLC,EADWC,aAAa,CACZE,YAAY,CAAC,UACxC,OACGD,GAAqB,UAAXA,GACXH,EAAMK,OAAO,EACbL,EAAMM,OAAO,EACbN,EAAMO,QAAQ,EACdP,EAAMQ,MAAM,EACXR,EADe,AACTS,WAAW,EAAgC,IAA5BT,EAAMS,UADiB,CACN,CAACC,KAAK,AAEjD,EAiByCE,IACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,WAAA,GAC7B,AAKF,GAAI,CAACC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACV,GAAO,CACjBG,IAGFJ,EAAEY,GAHS,WAGK,GAChBC,SAAST,OAAO,CAACH,IAInB,MACF,CAEAD,EAAEY,cAAc,GAyBhBK,EAAAA,OAAK,CAACC,eAAe,CAvBJ,AAuBKJ,KAtBpB,GAAIR,EAAY,CACd,IAAIS,GAAqB,EAQzB,GANAT,EAAW,CACTM,eAAgB,KACdG,GAAqB,CACvB,CACF,GAEIA,EACF,MAEJ,CAEAC,CAAAA,EAAAA,EAAAA,MAL0B,gBAKJ,AAAtBA,EACEd,GAAMD,EACNG,EAAU,UAAY,OACtBC,SAAAA,EACAF,EAAgB1B,KADN,EACa,CAE3B,GAGF,EA2UkBuB,EAAGC,EAAMC,EAAIC,EAAiBC,EAASC,EAAQC,GAC7D,EACA8B,aAAapC,CAAC,EACR,AAACwC,GAA8C,YAAY,AAAxC,OAAOH,GAC5BA,EAAiBrC,GAIjBwC,GACAuB,EAAMzC,KAAK,EACX,AAAoC,YACpC,OADOyC,EAAMzC,KAAK,CAACc,YAAY,EAE/B2B,EAAMzC,KAAK,CAACc,YAAY,CAACpC,GAGtB8C,GAIAG,GAKL2B,CAAAA,CATa,CASbA,EAAAA,WALwBjH,OAKxBiH,AAAkB,CALchH,CAM9BoC,EAAEV,AAN+B,CAACR,QAAQ,IAM3B,EAF4C,AAG3D6F,IAH+BhC,EAKnC,EACAL,aAEI,CAFU3E,GAVqC,KAYtC2E,AAAatC,AAFJpC,CAEK,EAFF,AAGf,AAAC4E,CAHeqC,EAG+B,IAbS,QAarC,AAAwC,OAAjCtC,GAC5BA,EAJ4C,AAI3BvC,GAHrB8E,AAOItC,GACAuB,EAAMzC,KAAK,EACX,AAAoC,YACpC,OADOyC,EAAMzC,KAAK,CAACgB,YAAY,EAE/ByB,EAAMzC,KAAK,CAACgB,YAAY,CAACtC,GAGtB8C,GAIAG,GAKL2B,CAAAA,CATa,CASbA,EAAAA,SALsB,SAKtBA,AAAkB,EAChB5E,EAAEV,aAAa,EAF4C,AAG3DqF,IAH+BhC,EAKnC,CACN,EAmCA,MA9BIoC,CAAAA,AA8BJ,EA9BIA,EAAAA,OA8BJ,MA9BIA,AAAa,EAAC7E,GAChBuE,EAAWxE,AADU,IACN,CAAGC,EAElB,AAACsC,IACDP,IACgB,MAAf8B,CAAsB,CAAhBiB,AAAkB,IAAd,EAAc,SAAUjB,EAAMzC,KAAI,GAC7C,CACAmD,EAAWxE,IAAI,CAAGgF,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC/E,EAAAA,EAc9BgF,EATE1C,EASKvB,EAAAA,CAAPiE,MAAY,CAACC,IATK,AASlBD,QAAyB,CAACnB,EAAOU,GAG/B,CAAA,EAAA,EAAA,GAAA,EAAC5B,IAAAA,CAAG,GAAGD,CAAS,CAAG,GAAG6B,CAAU,UAC7B9C,IAML,CAAA,EAAA,EAAA,GAAA,EAACyD,EAAkBC,QAAQ,CAAA,CAACC,MAAO/D,WAChC2D,GAGP,GAhsB0B,CAAA,CAAA,IAAA,IAksB1B,IAAME,EAAoBG,CAAAA,EAAAA,EAAAA,aAApBH,AAAoBG,AAAa,EAErC7D,EAAAA,OAFI0D,SAEY,EAELlG,EAAgB,IACpB6D,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACqC,qWCzsBpB,UAAA,qCAAwBI,aAnBuC,CAAA,CAAA,IAAA,IAezDC,EAA6B,aAAlB,OAAO7B,OAClB8B,EAA4BD,EAAW,KAAO,EAAIE,EAAAA,eAAe,CACjEC,EAAsBH,EAAW,KAAO,EAAII,EAAAA,SAAS,CAE5C,SAASL,EAAWlE,CAAsB,EACvD,GAAM,CAAEwE,aAAW,yBAAEC,CAAuB,CAAE,CAAGzE,EAEjD,SAAS0E,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAelC,EAAAA,QAAQ,CAACmC,OAAO,CACnCC,MAAMC,IAAI,CAACP,EAAYG,gBAAgB,EAA0BK,MAAM,CACrEC,UAGJT,EAAYU,UAAU,CAACT,EAAwBG,EAAc5E,GAC/D,CACF,CAEA,GAAImE,EAAU,KACZK,CAAAA,OAAAA,GAA6B,AAA7BA,EAAAA,KAA6B,AAA7BA,EAAAA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+BW,GAAG,CAACnF,EAAMK,QAAQ,EACjDqE,GACF,CAsCA,OApCAN,EAA0B,SACxBI,EACA,OADAA,MAAAA,CAAAA,EAAAA,AAA6B,GAA7BA,IAA6B,AAA7BA,EAAAA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+BW,GAAG,CAACnF,EAAMK,QAAQ,EAC1C,SACLmE,CAAAA,OAAAA,GAAAA,AAA6B,EAA7BA,KAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+BY,MAAM,CAACpF,EAAMK,QAAQ,CACtD,CACF,GAOA+D,EAA0B,KACpBI,IACFA,EAAYa,OADG,OACW,CAAGX,CAAAA,EAExB,KACDF,IACFA,EAAYa,OADG,OACW,CAAGX,CAAAA,CAEjC,IAGFJ,EAAoB,KACdE,GAAeA,EAAYa,cAAc,EAAE,CAC7Cb,EAAYa,cAAc,GAC1Bb,EAAYa,cAAc,CAAG,MAExB,KACDb,GAAeA,EAAYa,cAAc,EAAE,CAC7Cb,EAAYa,cAAc,GAC1Bb,EAAYa,cAAc,CAAG,KAEjC,IAGK,IACT,oECxE6B,EAAA,CAAA,CAAA,iFAFhBC,kBAAAA,qCAAAA,KAAN,IAAMA,EAAsC3F,gBAFjC,CAAA,CAAA,IAAA,KAEiCA,OAAK,CAACsE,aAAa,CAAC,CAAC,oECFjE,SAASsB,EAAY,CAAA,EAAA,GAAA,UAC1BC,GAAW,CAAK,CAChBC,UAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,KAAA,IAAA,EAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,0EANgBH,cAAAA,qCAAAA,sEC4IRlJ,IAAAA,EAAoB,EAAZC,AAAY,CAAA,CAAA,CAAT,CAACkB,QAAQ,sDA2D5B,OAAmB,CAAA,kBAAnB,GA1LgBmI,WAAW,CAAA,kBAAXA,6HAX4B,CAAA,CAAA,IAAA,aACzB,CAAA,CAAA,IAAA,SACa,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACP,CAAA,CAAA,IAAA,IAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,IAAAA,GAAY,CAAA,EACtC,IAAMC,EAAO,CAAC,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,AAACH,GACHC,EAAKG,IAAI,CAAA,AACP,CAAA,AAFY,EAEZ,EAAA,GAAA,EAACF,CADM,MACNA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpC3D,CAA2C,QAG3C,AAAqB,UAAjB,OAAOA,GAAuC,UAAjB,AAA2B,OAApBA,EAC/B2D,EAGL3D,EAAMiB,IAAI,GAAK/D,EAAAA,OAAK,CAAC0G,QAAQ,CACxBD,CAD0B,CACrBE,MAAM,CAEhB3G,EAAAA,OAAK,CAAC+C,QAAQ,CAACmC,OAAO,CAACpC,EAAMzC,KAAK,CAACK,QAAQ,EAAEkG,MAAM,CAEjD,CACEC,EACAC,IAEA,AAC2B,UAAzB,OAAOA,EARsF,CASpE,UAAzB,AACA,OADOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDL,EAAKE,MAAM,CAAC7D,EACrB,GA/CyB,CAAA,CAAA,IAAA,EA6BkF,CAoB3G,IAAMiE,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASoB,EACPC,CAAoD,CACpD/H,CAAQ,EAER,GAAM,WAAE4F,CAAS,CAAE,CAAG5F,EACtB,OAAO+H,EACJxB,MAAM,CAACJ,EAAkB,EAAE,EAC3B6B,OAAO,GACP1B,MAAM,CAACX,EAAYC,GAAWoC,OAAO,IACrChD,MAAM,CAAC2B,AAxEZ,SAASA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAO,AAACC,IACN,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIF,EAAE9E,GAAG,EAAqB,UAAjB,OAAO8E,EAAE9E,GAAG,EAAiB8E,EAAE9E,GAAG,CAACiF,OAAO,CAAC,KAAO,EAAG,CAChED,GAAS,EACT,IAAMhF,EAAM8E,EAAE9E,GAAG,CAACkF,KAAK,CAACJ,EAAE9E,GAAG,CAACiF,OAAO,CAAC,KAAO,GACzCR,EAAKU,GAAG,CAACnF,GACX+E,GAAW,AADM,EAGjBN,EAAKzB,GAAG,CAAChD,EAEb,CAGA,OAAQ8E,EAAEvD,IAAI,EACZ,IAAK,QACL,IAAK,OACCoD,EAAKQ,GAAG,CAACL,EAAEvD,IAAI,EACjBwD,CADoB,EACT,EAEXJ,EAAK3B,GAAG,CAAC8B,EAAEvD,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAI6D,EAAI,EAAGC,EAAMd,EAAUe,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWhB,CAAS,CAACa,EAAE,CAC7B,GAAKN,CAAD,CAAGjH,KAAK,CAAC2H,cAAc,CAACD,GAE5B,GAAiB,KAFsB,MAEX,CAAxBA,EACEX,EAAUO,GAAG,CAACI,GAChBR,GAAW,EAEXH,EAAU5B,CAHiB,EAGd,CAACuC,OAEX,CACL,IAAME,EAAWX,EAAEjH,KAAK,CAAC0H,EAAS,CAC5BG,EAAab,CAAc,CAACU,EAAS,EAAI,IAAIb,GAC9Ca,EAAa,SAAbA,GAAuB,CAACP,CAAAA,CAAK,EAAMU,EAAWP,GAAG,CAACM,GACrDV,GAAW,GAEXW,EAHgE,AAGrD1C,GAAG,CAACyC,GACfZ,CAAc,CAACU,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOX,CACT,CACF,KAgBKc,OAAO,GACPC,GAAG,CAAC,CAACC,EAA4BX,KAChC,IAAMpF,EAAM+F,EAAE/F,GAAG,EAAIoF,EACrB,GAC2B,AAD3B,EAEElL,OAAAA,CAAQC,GAAG,CAAC6L,qBAAqB,EACjC,CAACvC,CADDvJ,EAIa,QAFb,CAEE6L,CALuB,CAKrBxE,IAAI,EACNwE,EAAElI,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAACoI,IAAI,CAClEC,AAAD,GAASH,EAAElI,KAAK,CAAC,IAAO,CAACsI,OAF+D,GAErD,CAACD,IAEtC,CACA,IAAME,EAAW,CAAE,GAAIL,EAAElI,KAAK,EAAI,CAAC,CAAG,AAAF,EAOpC,OANAuI,AAMA,CANQ,CAAC,SAMT,GANqB,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAW/E,EAGnB+E,CAAQ,CAAC,uBAAuB,EAAG,EAE5B5I,EAAAA,OAAK,CAACkE,YAAY,CAACqE,EAAGK,EAC/B,CAiBF,OAAO5I,AAAP,EAAOA,OAAK,CAACkE,CAAb,WAAyB,CAACqE,EAAG,KAAE/F,CAAI,EACrC,EACJ,KAoBA,EAdA,SAceqG,AAdNA,AAAK,CAA2C,EAA3C,GAAA,UAAEnI,CAAQ,CAAiC,CAA3C,EACNoI,EAAWhH,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC6D,EAAAA,eAAe,EACrCd,EAAc/C,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACiH,EAAAA,kBAAkB,EACjD,MACE,CAAA,AADF,EACE,EAAA,GAAA,EAACC,EAAAA,AADH,OACS,CAAA,CACLlE,wBAAyBqD,EACzBtD,YAAaA,EACboB,UAAWL,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACkD,YAEtBpI,GAGP,4QCnMC,cACM,SAASuI,EAAgB,CAc/B,EAd+B,GAAA,UAC9BC,CAAQ,WACRC,CAAS,WACTC,CAAS,CACTC,YAAU,aACVC,CAAW,WACXC,CAAS,CAQV,CAd+B,EAgBxBE,EAAWL,EAAwB,GAAZA,EAAiBF,EACxCQ,EAAYL,EAA0B,GAAbA,EAAkBF,EAE3CQ,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,mDAAoDC,EAA5C,QAAoD,8FAA2FH,MAAI,oQAAiQA,MAAI,qEARpYG,EACxB,OACc,YAAdJ,EACE,IAKufK,OAJze,UAAdL,EACE,iBACA,MAAA,EAEygB,sCAAqCD,EAAY,iBACpkB,0EA9BgBL,kBAAAA,qCAAAA,8HCHHY,aAAa,CAAA,kBAAbA,GAiIAC,kBAAkB,CAAA,kBAAlBA,uEAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CC,KAAM,eACNC,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,oBAAqB,GACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAe9G,EACf+G,eAAgB,EAAE,CAClBC,eAAWhH,EACXiH,aAAa,CACf,oEC6R+B,EAAA,CAAA,CAAA,iFA9KfC,cAAAA,qCAAAA,OAjQS,CAAA,CAAA,IAAA,WACO,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,IAkF7BE,EAAiC,CACrC,eACA,OACA,OACA,aACApH,OACD,CA4BD,SAASqH,EACPC,CAAoC,EAEpC,YAA0CtH,IAAlCsH,EAAsBC,OAChC,AADuC,CAwBvC,SAASM,EAAOC,CAAU,SACxB,AAAI,KAAa,IAANA,EACFA,EAEQ,KAHa,KAG1B,AAAuB,OAAhBA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAASf,EACd,CAyBa,CACbyC,CAKC,MAkBmBjF,IAjDpB,IA0CI4E,EAqEA/D,EACAC,EAhHJ,KACE8B,CAAG,OACHgB,CAAK,aACLrB,GAAc,CAAK,UACnB2C,GAAW,CAAK,SAChBC,CAAO,WACPC,CAAS,CACTP,SAAO,OACPlB,CAAK,QACL0B,CAAM,CACNC,QAAO,CAAK,OACZC,CAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,aACrB5E,CAAW,eACX6E,CAAa,UACbC,EAAW,OAAO,QAClBC,CAAM,WACN9E,CAAS,gBACT+E,CAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGC,EACQ,CAzBb,EAyCM,SAAEC,CAAO,aAAEC,CAAW,cAAEC,CAAY,eAAEC,CAAa,CAAE,CAAGrB,EAE1DjF,EAAImG,GAAW5E,EAAAA,kBAAkB,CACrC,GAAI,aAAcvB,EAChB4E,CADmB,CACV5E,MACJ,CACL,IAAM6D,EAAW,IAAI7D,EAAEwB,WAAW,IAAKxB,EAAEyB,UAAU,CAAC,CAAC8E,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAClEhF,EAAcxB,EAAEwB,WAAW,CAAC+E,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAC/ClE,EAAuB,AAAvBA,OAAYtC,EAAAA,EAAEsC,SAAAA,AAAS,EAAA,KAAA,EAAXtC,EAAauG,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAClD5B,EAAS,CAAE,GAAG5E,CAAC,UAAE6D,cAAUrC,YAAac,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBgE,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,AAAItM,MACR,yIADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,IAAI2H,EAAgCuE,EAAKvE,MAAM,EAAI2E,CAGnD,QAAOJ,EAAKvE,MAAM,CAClB,OAAQuE,EAAapB,MAAM,CAI3B,IAAM2B,EAAkB,uBAAwB9E,EAEhD,GAAI8E,EACF,IAAI7B,AAAkB,UAAU,CADb,EACRjD,MAAM,CACf,MAAM,OAAA,cAGL,CAHK,AAAI3H,yBACW4I,EAAlB,IAAsB,gCACpB,kEAFC,oBAAA,OAAA,mBAAA,gBAAA,CAGN,EACF,KACK,CAIL,IAAM8D,EAAoB/E,EAC1BA,EAAS,AAACgF,IACR,GAAM,CAAE/B,OAAQpP,CAAC,CAAE,GAAGoR,EAAM,CAAGD,EAC/B,OAAOD,EAAkBE,EAC3B,CACF,CAEA,GAAId,EAAQ,CACK,QAAQ,CAAnBA,IACFR,GAAO,CAAA,EAUT,IAAM4B,EAAcL,AARsD,CACxEC,UAAW,CAAEC,SAAU,OAAQ1B,OAAQ,MAAO,EAC9C2B,WAAY,CAAErD,MAAO,OAAQ0B,OAAQ,MAAO,CAC9C,CAKiC,CAACS,EAAO,CACrCoB,IACF3B,EAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQ2B,CAAW,CAAC,EAErC,IAAMC,EAAcF,AARsC,CACxDD,WAAY,QACZ1B,KAAM,OACR,CAKiC,CAACQ,EAAO,CACrCqB,GAAe,CAACvD,IAClBA,EAAQuD,CADiB,AACjBA,CAEZ,CAEA,IAAIC,EAAY,GACZzG,EAAWwC,EAAOQ,GAClB/C,EAAYuC,EAAOkC,GAGvB,GA/OE,CAFoBzC,AAiPlBG,AA/OD,CAACH,CA+OeA,CAjP6B,GAG9C,AAAe,YACdD,KADMC,IACND,EAAgBC,QACfE,CARoCxH,IAQlBsH,AARdA,EAAwBA,GAQVA,AARa,CAQM,CA4OhB,CACvB,IAAMyE,EAAkB1E,EAAgBC,GAAOA,EAAIC,OAAO,CAAGD,EAE7D,GAAI,CAACyE,EAAgBzE,GAAG,CACtB,CADwB,KAClB,OAAA,cAIL,CAJK,AAAI5I,MACP,8IAA6IsN,KAAKC,SAAS,CAC1JF,IAFE,oBAAA,OAAA,mBAAA,gBAAA,CAIN,GAEF,GAAI,CAACA,EAAgBhC,MAAM,EAAI,CAACgC,EAAgB1D,KAAK,CACnD,CADqD,KAC/C,OAAA,cAIL,CAJK,AAAI3J,MACP,2JAA0JsN,KAAKC,SAAS,CACvKF,IAFE,oBAAA,OAAA,kBAAA,gBAAA,CAIN,GAQF,GALAxG,EAAYwG,EAAgBxG,SAAS,CACrCC,EAAauG,EAAgBvG,UAAU,CACvCC,EAAcA,GAAesG,EAAgBtG,WAAW,CACxDqG,EAAYC,EAAgBzE,GAAG,CAE3B,CAAC0C,EACH,GAAI,AAAC3E,CADI,EACSC,GAGX,GAAID,GAHM,AAGM,CAACC,CAHK,CAGM,CACjC,IAAM4G,EAAQ7G,EAAW0G,EAAgB1D,KAAK,CAC9C/C,EAAYuD,KAAKsD,KAAK,CAACJ,EAAgBhC,MAAM,CAAGmC,EAClD,MAAO,GAAI,CAAC7G,GAAYC,EAAW,CACjC,IAAM4G,EAAQ5G,EAAYyG,EAAgBhC,MAAM,CAChD1E,EAAWwD,KAAKsD,KAAK,CAACJ,EAAgB1D,KAAK,CAAG6D,EAChD,OARE7G,EAAW0G,EAAgB1D,KAAK,CAChC/C,EAAYyG,EAAgBhC,MAAM,AASxC,CAGA,IAAIqC,EACF,CAACxC,IAAyB,QAAZC,CAAAA,GAAsB,KAAmB,IAAZA,CAAY,CAAU,CAC/D,CAACvC,EAJLA,EAAqB,UAAf,OAAOA,EAAmBA,EAAMwE,CAAAA,GAI1BxE,EAAIxC,UAAU,CAAC,UAAYwC,EAAIxC,UAAU,CAAC,QAAA,GAAU,CAE9DmC,EAAc,GACdmF,GAAS,GAEP9C,EAAOrC,WAAW,EAAE,CACtBA,GAAc,CAAA,EAGdkE,GACA,CAAC7B,EAAO3C,mBAAmB,EAC3BW,EAAI+E,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,CAGArF,GAAc,CAAA,EAGhB,IAAMsF,EAAa1E,EAAO0B,GAyMpBiD,EAAWC,OAAOC,MAAM,CAC5B1C,EACI,CACE2C,SAAU,WACV5C,OAAQ,OACR1B,MAAO,OACPuE,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACRrH,iBACA+E,CACF,EACA,CAAC,EACLK,EAAc,CAAC,EAAI,CAAEkC,MAAO,aAAc,EAC1C/C,GAGIgD,EACJ,AAAClC,GAAgC,UAAhBV,EAWb,KAVgB,SAAhBA,EACG,yCAAwCjF,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,CACvDC,qBACAC,YACAC,aACAC,EACAC,YAAaA,GAAe,GAC5BC,UAAW8G,EAAS9G,SAAS,AAC/B,GAAG,KACF,QAAO2E,EAAY,KAAI,AAG1B6C,EAAiB,AAAC9F,EAA+B+F,QAAQ,CAC7DX,EAAS9G,QAJ4C,CAInC,EAGhB8G,AAAuB,WAAd9G,SAAS,CAChB,YAAY,AACZ,QAHF8G,EAAS9G,SAAS,CAKlB0H,EAAqCH,EACrC,gBACEC,EACAG,CANuD,kBAMnCb,EAAS/B,cAAc,EAAI,UAC/C6C,iBAAkB,4BAClBL,CACF,EACA,CAAC,EAeCM,EA3dR,AA2dwBlE,SA3dfA,AAAiB,CAQR,EARQ,GAAA,CACxBC,QAAM,KACNhC,CAAG,aACHL,CAAW,OACXoB,CAAK,SACLkB,CAAO,OACPjB,CAAK,QACLjC,CAAM,CACU,CARQ,EASxB,GAAIY,EACF,MAAO,KADQ,AACNK,EAAKkC,YAAQxJ,EAAWsI,WAAOtI,CAAU,EAGpD,GAAM,QAAE+I,CAAM,MAAEE,CAAI,CAAE,CAxExB,AAwE2Bb,SAvEzB,AADOA,CAC+B,CACtCC,CAAyB,CACzBC,CAAyB,EAFzB,GAAA,CAAEpC,aAAW,UAAEqC,CAAQ,CAAe,CAAtC,EAIA,GAAID,EAAO,CAET,IAAME,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAACL,IAC5CG,EAAajG,EADwCkG,EACpC,CAACP,GAD0C,MACjCO,CAAK,CAAC,EAAE,GAErC,GAAID,EAAaxE,MAAM,CAAE,CACvB,IAAM2E,EAA4C,IAA5BC,KAAKC,GAAG,IAAIL,GAClC,MAAO,CACLM,OAAQR,EAAS/G,MAAM,CAAC,AAACwH,GAAMA,GAAK9C,CAAW,CAAC,EAAE,CAAG0C,GACrDK,KAAM,GACR,CACF,CACA,MAAO,CAAEF,OAAQR,EAAUU,KAAM,GAAI,CACvC,OACqB,AAArB,UAA+B,AAA3B,OAAOZ,EACF,CAAEU,OAAQ7C,EAAa+C,KAAM,GAAI,EAkBnC,CAAEF,OAfM,IACV,IAAI1F,IAEL,AADA,AAQA,CAACgF,EAAe,EAAE,AAAVA,EAA0B,CAAC5D,GAAG,CACpC,AAACyE,GAAMX,EAASY,CADa,GACT,CAAEC,AAAD,GAAOA,GAAKF,IAAMX,CAAQ,CAACA,EAAStE,MAAM,CAAG,EAAE,GAGzE,CACgBgF,KAAM,GAAI,CAC7B,EA+BqCK,EAAQjB,EAAOC,GAC5CmB,EA7CmE,AA6C5DV,EA9C8D,AA8CvD9E,MAAM,CAAG,EAE7B,MAAO,CACLqE,MAAO,AAACA,GAAkB,MAATW,EAAyBX,EAAV,QAChCkB,OAAQT,EACLtE,GAAG,CACF,CAACyE,EAAGnF,IACCsC,EAAO,QAAEiD,MAAQhC,UAAKiC,EAASlB,MAAOa,CAAE,GAAG,KACnC,CAATD,KAAAA,EAAeC,EAAInF,GAAI,CAAA,CACtBkF,GAENS,IAAI,CAAC,MAQRpC,IAAKjB,EAAO,CAAEiD,aAAQhC,UAAKiC,EAASlB,MAAOU,CAAM,CAACU,EAAK,AAAC,EAC1D,CACF,EAwbyC,QACrCH,EACAhC,kBACAL,EACAoB,MAAOhD,EACPkE,QAASgD,QACTjE,EACAjC,QACF,GA4BA,MAAO,CAAE7J,MAde,CACtB,GAAGoO,CAAI,CACPf,QAASuC,EAAS,OAASvC,gBAC3BS,EACAjC,MAAOhD,EACP0E,OAAQzE,WACRiF,YACAT,EACAG,MAAO,CAAE,GAAGuC,CAAQ,CAAE,GAAGY,CAAgB,AAAC,EAC1C9E,MAAOiF,EAAcjF,KAAK,CAC1BkB,OAAQ+D,EAAc/D,MAAM,CAC5BlC,IAAK4C,GAAeqD,EAAcjG,GAAG,AACvC,EAEgBhF,KADH,aAAE2E,WAAa2C,cAAUS,EAAaL,MAAK,CACnC,CACvB,oECltB6B,EAAA,CAAA,CAAA,iFAHhBwD,qBAAAA,qCAAAA,2BAJK,CAAA,CAAA,IAAA,SAEiB,CAAA,CAAA,IAAA,IAEtBA,EACXrR,EAAAA,OAAK,CAACsE,aAAa,CAAsBwF,EAAAA,kBAAkB,oECD7D,SAAS+E,EAAc,CAKM,MA8EzB1B,EAnFmB,GAAA,QACrBA,CAAM,CACNhC,KAAG,OACHe,CAAK,SACLkB,CAAO,CACoB,CALN,EAiFfmE,EACJnE,IAAAA,AACgB,OADhBA,AACAD,EAAAA,EAAOtC,SAAAA,AAAS,EAAA,KAAA,EAAhBsC,EAAkBvG,MAAM,CAAC,CAAC4K,EAAMC,IAC9B/E,KAAKgF,GAAG,CAACD,EAtFG,IAsFGH,AAAa5E,KAAKgF,GAAG,CAACF,MAAoBC,CAAbH,CAAmBE,EAAAA,CAAAA,KAEjEF,AAEF,OAAUnE,EAAOlD,IAAI,CAAC,QAAO0H,mBAAmBxG,GAAK,MAAKe,EAAM,MAAKqF,GACnEpG,CAAAA,CAAIxC,UAAU,CAAC,wBAEX,EAAC,CAFqCjM,AAI9C,CAvF+B,EAAA,CAAA,CAAA,GAmFuBC,GAAG,CAACiV,kBAAkB,GACpE,AAAC,UAAOlV,QAAQC,GAAG,CAACiV,kBAAkB,aAS9C,UAAA,qCAAA,KAFA/C,EAAcgD,kBAAkB,EAAG,MAEnC,EAAehD,oECnEgC,EAAA,CAAA,CAAA,iFAsUlCkD,QAAAA,qCAAAA,2DA/VN,CAAA,CAAA,IAAA,aACc,CAAA,CAAA,IAAA,aACJ,CAAA,CAAA,IAAA,SACW,CAAA,CAAA,IAAA,QAYO,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,MACV,CAAA,CAAA,IAAA,WACK,CAAA,CAAA,IAAA,YAGJ,CAAA,CAAA,IAAA,SACG,CAAA,CAAA,IAAA,IAGvBC,EAAAA,KAAAA,KAAAA,CAAAA,6LAyBN,SAASG,EACPC,CAA2B,CAC3BlE,CAA6B,CAC7BmE,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrCzH,CAAoB,CACpB0H,CAA8B,EAE9B,IAAMrH,EAAMiH,QAAAA,KAAAA,EAAAA,EAAKjH,GAAG,CACfiH,GAAOA,CAAG,CAAC,kBAAkB,GAAKjH,IAGvCiH,CAH4C,AAGzC,CAAC,kBAAkB,CAAGjH,EAEzB8B,CADU,WAAYmF,EAAMA,EAAIK,MAAM,GAAKC,QAAQC,OAAO,EAAA,EACxDC,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC,KACrB,GAAKT,AAAD,EAAKU,aAAa,EAAKV,EAAD,AAAKW,WAAW,EAAE,AAW5C,GAHoB,SAAS,CAAzB7E,GACFqE,GAAgB,GAEdF,MAAAA,EAAAA,KAAAA,EAAAA,EAAW7U,OAAO,CAAE,CAItB,IAAMW,EAAQ,IAAI6U,MAAM,QACxB1C,OAAO2C,cAAc,CAAC9U,EAAO,SAAU,CAAE+U,SAAU,GAAO7O,MAAO+N,CAAI,GACrE,IAAIe,GAAY,EACZC,GAAU,EACdf,EAAU7U,OAAO,CAAC,CAChB,GAAGW,CAAK,CACRS,YAAaT,EACbE,cAAe+T,EACf9T,OAAQ8T,EACRtS,mBAAoB,IAAMqT,EAC1BE,qBAAsB,IAAMD,EAC5BE,QAAS,KAAO,EAChB3T,eAAgB,KACdwT,GAAY,EACZhV,EAAMwB,cAAc,EACtB,EACA4T,gBAAiB,KACfH,EAAU,GACVjV,EAAMoV,eAAe,EACvB,CACF,EACF,EACIjB,MAAAA,EAAAA,KAAAA,EAAAA,EAAsB9U,OAAAA,AAAO,EAAE,CACjC8U,EAAqB9U,OAAO,CAAC4U,GAkDjC,GACF,CAEA,SAASoB,EACPrF,CAAsB,SAEtB,AAAYsF,EAAAA,EAARnO,CAAW,CAIN,EAJS,aAIP6I,CAAc,EAIlB,CAAEuF,cAAevF,CAAc,CACxC,CA9IsB,aAAlB,AAA+B,OAAxBxL,SACPsP,WAAmBC,qBAAqB,EAAG,CAAA,EA+I/C,IAAMyB,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,AAAU,EAC7B,CAAA,EAwBEnS,IAzBEkS,IAEF,KACExI,CAAG,QACHkC,CAAM,OACNlB,CAAK,QACLyB,CAAM,CACN1B,OAAK,UACLkC,CAAQ,WACRT,CAAS,OACTG,CAAK,eACLK,CAAa,aACbD,CAAW,SACXR,CAAO,aACP5C,CAAW,MACX+C,CAAI,CACJwE,WAAS,sBACTC,CAAoB,iBACpBC,CAAe,gBACfsB,CAAc,YACdrB,CAAU,QACVxE,CAAM,SACN8F,CAAO,CACP,GAAGrF,EACJ,CAAA,EAGKsF,EAASxW,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EACxB,AAAC6U,IACMA,IAGD0B,CAHM,GAQR1B,EAAIjH,GALO,AAKJ,CAAGiH,EAAIjH,GAAG,AAAHA,EAYZiH,EAAI4B,QAAQ,EAAE,AAChB7B,EACEC,EACAlE,EACAmE,EACAC,EACAC,EACAzH,EACA0H,GAGN,EACA,CACErH,EACA+C,EACAmE,EACAC,EACAC,EACAuB,EACAhJ,EACA0H,EACD,EAGGhR,EAAMvE,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAACwE,EAAcsS,GAEvC,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAAC3B,EADH,IACGA,CACE,GAAG3D,CAAI,CACP,GAAG+E,EAAgBrF,EAAc,CAIlCT,QAASA,EACTxB,MAAOA,EACP0B,OAAQA,EACRQ,SAAUA,EACV6F,YAAWpG,EAAO,OAAS,IAC3BF,UAAWA,EACXG,MAAOA,EAOP3B,MAAOA,EACPkB,OAAQA,EACRlC,IAAKA,EACL3J,IAAKA,EACLwM,OAAQ,AAAC7P,IAEPgU,EADYhU,EAAME,UAEhB+T,GAF6B,CAG7BlE,EACAmE,EACAC,EACAC,EACAzH,EACA0H,EAEJ,EACAsB,QAAS,AAAC3V,IAER0V,GAAe,GACK,SAAS,CAAzB3F,GAEFqE,GAAgB,GAEduB,GACFA,EAAQ3V,EAEZ,EAHe,CAMrB,GAGF,SAAS+V,EAAa,CAMrB,EANqB,GAAA,CACpBC,aAAW,eACX/C,CAAa,CAId,CANqB,EAOdjC,EAAO,CACXlQ,GAAI,QACJmV,YAAahD,EAAc/D,MAAM,CACjCrD,WAAYoH,EAAcjF,KAAK,CAC/BkI,YAAajD,EAAciD,WAAW,CACtCC,eAAgBlD,EAAckD,cAAc,CAC5C,GAAGd,EAAgBpC,EAAcjD,aAAa,CAAC,AACjD,SAEA,AAAIgG,GAAeI,EAAAA,OAAQ,CAACC,OAAO,EAAE,AAEnCD,EAAAA,OAAQ,CAACC,OAAO,CACdpD,EAAcjG,GAAG,CAEjBgE,GAEK,MAIP,CAAA,EAAA,EAAA,GAAA,EAACtG,EAAAA,OAAI,CAAA,UACH,CAAA,EAAA,EAAA,GAAA,EAAC5E,EAAD,KAACA,CAOCwQ,IAAI,CAfwD,SAqB5DzV,KAAMoS,EAAc/D,MAAM,MAAGxJ,EAAYuN,EAAcjG,GAAG,CACzD,GAAGgE,CAAI,EAZN,UACAiC,EAAcjG,GAAG,CACjBiG,EAAc/D,MAAM,CACpB+D,EAAcjF,KAAK,GAa7B,CAOO,IAAM4F,EAAQ6B,CAAAA,EAAAA,EAAAA,CAAR7B,SAAkB,AAAV6B,EACnB,AADW7B,CACV1R,EAAOoB,KACN,IAAMiT,EAAc5S,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC6S,EAAAA,aAAa,EAItCC,EAAgB9S,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACuP,EAAAA,kBAAkB,EAC7ClE,EAASvK,GAAAA,EAAAA,OAAAA,AAAO,EAAC,SAIH2F,EAHlB,IAAMA,EAAIyJ,GAAa4C,GAAiB9K,EAAAA,kBAAkB,CACpDsC,EAAW,IAAI7D,EAAEwB,WAAW,IAAKxB,EAAEyB,UAAU,CAAC,CAAC8E,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAClEhF,EAAcxB,EAAEwB,WAAW,CAAC+E,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAC/ClE,EAAAA,AAAuB,OAAXtC,EAAAA,EAAEsC,SAAAA,AAAS,EAAA,KAAA,EAAXtC,EAAauG,IAAI,CAAC,CAAClN,EAAGmN,IAAMnN,EAAImN,GAClD,MAAO,CAAE,GAAGxG,CAAC,UAAE6D,cAAUrC,YAAac,CAAU,CAClD,EAAG,CAAC+J,EAAc,EAEZ,QAAE5G,CAAM,mBAAEC,CAAiB,CAAE,CAAG5N,EAChCgS,EAAYhV,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAC2Q,GAEzBpJ,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRyN,EAAU7U,OAAO,CAAGwQ,CACtB,EAAG,CAACA,EAAO,EAEX,IAAMsE,EAAuBjV,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAC4Q,GAEpCrJ,GAAAA,EAAAA,SAAAA,AAAS,EAAC,KACR0N,EAAqB9U,OAAO,CAAGyQ,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACW,EAAc2D,EAAgB,CAAGsC,GAAAA,EAAAA,QAAAA,AAAQ,GAAC,GAC3C,CAAClG,EAAakF,EAAe,CAAGgB,CAAAA,EAAAA,EAAAA,QAAAA,AAAQ,EAAC,IAEzC,CAAExU,MAAO+Q,CAAa,CAAEjL,KAAM2O,CAAO,CAAE,CAAG/J,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC1K,EAAO,CACjEwO,cAAAA,EAAAA,OAAa,CACbH,QAASvB,EACTyB,2BACAD,CACF,GAEA,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAAA,CADF,CACE,QAAA,CAAA,WAEI,CAAA,EAAA,EAAA,GAAA,EAACgF,EAAAA,CACE,GAAGvC,CAAa,CACjBtG,YAAagK,EAAQhK,WAAW,CAChCoD,YAAa4G,EAAQ5G,WAAW,CAChCL,KAAMiH,EAAQjH,IAAI,CAClBwE,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjBsB,eAAgBA,EAChBrB,WAAYnS,EAAM8L,KAAK,CACvB3K,IAAKC,IAGRqT,EAAQrH,QAAQ,CACf,CAAA,CADe,CACf,EAAA,GAAA,EAACyG,EAAAA,CADc,AAEbC,YApDY,CAoDCA,AApDAO,EAqDbtD,cAAeA,IAEf,OAGV,2QCxakI,EAAA,CAAA,CAAA,gBAAkC,IAAA,EAAA,EAAA,CAAA,CAAA,QAAiH,EAAoB,CAAA,EAAA,EAAA,EAAb,WAAW,QAAE,AAAoB,EAAE,cAAtB,+BAAmE,EAAA,UAAU,CAAC,KAAK,EAAE,EAAA,gBAAgB,CAAlC,AAAmC,oBAAjB,iFCEhY,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QAGA,IAAM,EAA0B,CAC9B,SAAS,EACT,QAAS,EAGX,EAMO,SAAS,EAAW,cAAE,CAAY,CAAmB,EAC1D,GAAM,CAAC,EAAO,EAAY,EAAU,CAAG,CAAA,EAAA,EAAA,cAAA,AAAa,EAAE,EAAA,MAAM,CAAE,GACxD,EAAS,CAAA,EAAA,EAAA,EADwB,OAChB,AAAR,UAGf,CAAA,AAJsD,EAItD,EAAA,SAAA,AAAQ,EAAE,EAHK,GAIb,GAAI,GAAO,SAAW,GAAO,GAD/B,MACwC,SAAS,gCAAiC,CAE9E,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,oBAEV,IAAM,EAAQ,AAFd,WAEyB,KACvB,EAAO,IAAI,CAAC,SACd,EAAG,KAEH,MAAO,IAAM,aAAa,EAC5B,CACF,EAAG,CAAC,GAAO,QAAS,GAAO,QAAS,EAAO,EAYzC,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,OATa,AAAC,CASN,GAPd,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,kBAGV,EAAW,EACb,EAG8B,EAP5B,QAOsC,2BAEnC,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,SACL,KAAK,gBACL,MAAO,IAIX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,OAAO,UAAU,yDAAgD,cAGhF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,OACH,KAAK,OACL,KAAK,OACL,aAAa,OACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,iBAEb,GAAO,aAAa,MACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,IAAI,CAAC,EAAE,SAKtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,yDAAgD,kBAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,QACL,aAAa,QACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,uBAEb,GAAO,aAAa,OACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,KAAK,CAAC,EAAE,SAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,WAAW,UAAU,yDAAgD,aAGpF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,WACL,aAAa,eACb,QAAQ,CAAA,CAAA,EACR,UAAU,mOACV,YAAY,6BAEb,GAAO,aAAa,UACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,QAAQ,CAAC,EAAE,SAK1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,cAAc,UAAU,yDAAgD,kBAGvF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,cACH,KAAK,cACL,KAAK,OACL,UAAU,mOACV,YAAY,uBAEb,GAAO,aAAa,aACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,WAAW,CAAC,EAAE,SAK7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,yDAAgD,iBAGjF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8CAAqC,UAEvD,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,MACL,aAAa,MACb,UAAU,yOACV,YAAY,kBAGf,GAAO,aAAa,OACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,KAAK,CAAC,EAAE,SAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,gBAAgB,UAAU,yDAAgD,2BAGzF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,cACH,KAAK,gBACL,KAAK,OACL,UAAU,mOACV,YAAY,+CACZ,aAAc,GAAgB,KAE/B,GAAO,aAAa,eACnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kEACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oCAChB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,WAAW,CAAC,WAAW,CAAC,EAAE,SAM7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,WACL,QAAQ,CAAA,CAAA,EACR,UAAU,6FAEZ,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,mDAAyC,eAC3D,IACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,SAAS,OAAO,SAAS,UAAU,+EAAsE,qBAGhH,IAAI,MAAI,IACT,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,WAAW,OAAO,SAAS,UAAU,+EAAsE,4BAOxH,GAAO,SACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,sBAAsB,EACrC,EAAM,OAAO,CACT,iEACA,yDAAA,CACJ,UACA,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAW,CAAC,gDAAgD,EAC7D,EAAM,OAAO,CAAG,mBAAqB,eAAA,CACrC,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,qBAAqB,EACrC,EAAM,OAAO,CAAG,iBAAmB,aAAA,CACnC,GACF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAM,OAAO,QAK1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,EACV,UAAU,2WAET,EACC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8EACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,wCAGR,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,yBACN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDAAyD,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAChH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wCAQrF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}