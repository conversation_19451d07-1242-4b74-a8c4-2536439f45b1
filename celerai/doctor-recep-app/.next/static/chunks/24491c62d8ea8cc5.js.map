{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/error-boundary.tsx", "turbopack:///[project]/node_modules/next/src/client/components/layout-router.tsx", "turbopack:///[project]/node_modules/next/src/client/components/render-from-template-context.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/invariant-error.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils/reflect-utils.ts", "turbopack:///[project]/node_modules/next/src/client/request/search-params.browser.prod.ts", "turbopack:///[project]/node_modules/next/src/client/request/search-params.browser.ts", "turbopack:///[project]/node_modules/next/src/client/request/params.browser.prod.ts", "turbopack:///[project]/node_modules/next/src/client/request/params.browser.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts", "turbopack:///[project]/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/after-task-async-storage-instance.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/after-task-async-storage.external.ts", "turbopack:///[project]/node_modules/next/src/server/request/utils.ts", "turbopack:///[project]/node_modules/next/src/server/request/search-params.ts", "turbopack:///[project]/node_modules/next/src/server/request/params.ts", "turbopack:///[project]/node_modules/next/src/client/components/client-page.tsx", "turbopack:///[project]/node_modules/next/src/client/components/client-segment.tsx", "turbopack:///[project]/node_modules/next/src/client/components/metadata/browser-resolved-metadata.tsx", "turbopack:///[project]/node_modules/next/src/shared/lib/server-inserted-metadata.shared-runtime.ts", "turbopack:///[project]/node_modules/next/src/client/components/metadata/server-inserted-metadata.tsx", "turbopack:///[project]/node_modules/next/src/client/components/metadata/async-metadata.tsx", "turbopack:///[project]/node_modules/next/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n", "'use client'\n\nimport type {\n  <PERSON>ache<PERSON>ode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n      navigatedAt: -1,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n", "import type { SearchParams } from '../../server/request/search-params'\n\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  return promise\n}\n", "export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n", "import type { Params } from '../../server/request/params'\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeUntrackedExoticParams(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n", "export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n", "export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n", "import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n", "import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n", "import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n", "import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  if (prerenderStore.type === 'prerender') {\n    // We are in a dynamicIO (PPR or otherwise) prerender\n    return makeAbortingExoticSearchParams(workStore.route, prerenderStore)\n  }\n\n  // The remaining cases are prerender-ppr and prerender-legacy\n  // We are in a legacy static generation and need to interrupt the prerender\n  // when search params are accessed.\n  return makeErroringExoticSearchParams(workStore, prerenderStore)\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeAbortingExoticSearchParams(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            const error = createSearchAccessError(route, expression)\n            abortAndThrowOnSynchronousRequestDataAccess(\n              route,\n              expression,\n              error,\n              prerenderStore\n            )\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        const error = createSearchAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      const error = createSearchAccessError(route, expression)\n      abortAndThrowOnSynchronousRequestDataAccess(\n        route,\n        expression,\n        error,\n        prerenderStore\n      )\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n", "'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n", "import { use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport function BrowserResolvedMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { metadata, error } = use(promise)\n  // If there's metadata error on client, discard the browser metadata\n  // and let metadata outlet deal with the error. This will avoid the duplication metadata.\n  if (error) return null\n  return metadata\n}\n", "'use client'\n\nimport type React from 'react'\nimport { createContext } from 'react'\n\nexport type MetadataResolver = () => React.ReactNode\ntype MetadataResolverSetter = (m: MetadataResolver) => void\n\nexport const ServerInsertedMetadataContext =\n  createContext<MetadataResolverSetter | null>(null)\n", "import { use, useContext } from 'react'\nimport {\n  type MetadataResolver,\n  ServerInsertedMetadataContext,\n} from '../../../shared/lib/server-inserted-metadata.shared-runtime'\nimport type { StreamingMetadataResolvedState } from './types'\n\n// Receives a metadata resolver setter from the context, and will pass the metadata resolving promise to\n// the context where we gonna use it to resolve the metadata, and render as string to append in <body>.\nconst useServerInsertedMetadata = (metadataResolver: MetadataResolver) => {\n  const setMetadataResolver = useContext(ServerInsertedMetadataContext)\n\n  if (setMetadataResolver) {\n    setMetadataResolver(metadataResolver)\n  }\n}\n\nexport function ServerInsertMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  // Apply use() to the metadata promise to suspend the rendering in SSR.\n  const { metadata } = use(promise)\n  // Insert metadata into the HTML stream through the `useServerInsertedMetadata`\n  useServerInsertedMetadata(() => metadata)\n\n  return null\n}\n", "'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport const AsyncMetadata =\n  typeof window === 'undefined'\n    ? (\n        require('./server-inserted-metadata') as typeof import('./server-inserted-metadata')\n      ).ServerInsertMetadata\n    : (\n        require('./browser-resolved-metadata') as typeof import('./browser-resolved-metadata')\n      ).BrowserResolvedMetadata\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n", "'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["process", "env", "NODE_ENV", "HTTPAccessFallbackBoundary", "HTTPAccessFallbackErrorBoundary", "React", "Component", "componentDidCatch", "props", "missingSlots", "size", "has", "getDerivedStateFromError", "error", "isHTTPAccessFallbackError", "httpStatus", "getAccessFallbackHTTPStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "getAccessFallbackErrorTypeByStatus", "constructor", "useUntrackedPathname", "useContext", "MissingSlotContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "focusAndScrollRef", "apply", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_PATCH", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "parallel<PERSON><PERSON>er<PERSON>ey", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "treeSegment", "concat", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "stateKey", "newLazyCacheNode", "head", "prefetchHead", "TemplateContext", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "RedirectBoundary", "RenderFromTemplateContext", "InvariantError", "message", "options", "endsWith", "describeHasCheckingStringProperty", "describeStringPropertyAccess", "wellKnownProperties", "isDefinitelyAValidIdentifier", "target", "prop", "test", "JSON", "stringify", "stringifiedProp", "Set", "makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "underlyingSearchParams", "cachedSearchParams", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "require", "makeUntrackedExoticSearchParamsWithDevWarnings", "createRenderSearchParamsFromClient", "makeUntrackedExoticParams", "C<PERSON>d<PERSON><PERSON><PERSON>", "underlyingParams", "cachedParams", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "createRenderParamsFromClient", "ReflectAdapter", "receiver", "Reflect", "bind", "deleteProperty", "__NEXT_DYNAMIC_IO", "console", "createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "fn", "logErrorOrWarn", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "afterTaskAsyncStorageInstance", "createAsyncLocalStorage", "afterTaskAsyncStorage", "isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "invalidUsageError", "afterTaskStore", "getStore", "rootTaskSpawnPhase", "isPrefetchRequest", "createPrerenderSearchParamsForClientPage", "createSearchParamsFromClient", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "makeErroringExoticSearchParamsForUseCache", "workUnitStore", "workUnitAsyncStorage", "createPrerenderSearchParams", "createRenderSearchParams", "forceStatic", "prerenderStore", "makeHangingPromise", "renderSignal", "makeAbortingExoticSearchParams", "makeErroringExoticSearchParams", "CachedSearchParamsForUseCache", "proxiedPromise", "Proxy", "hasOwn", "annotateDynamicAccess", "createSearchAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "ownKeys", "dynamicShouldError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "store", "defineProperty", "trackDynamicDataInDynamicRender", "writable", "enumerable", "configurable", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "proxiedProperties", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "scheduleImmediate", "push", "add", "newValue", "syncIODev", "delete", "missingProperties", "warnForIncompleteEnumeration", "warnForSyncAccess", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "createIncompleteEnumerationError", "prefix", "describeListOfPropertyNames", "properties", "description", "i", "createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "createPrerenderParams", "createRenderParams", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "hasSomeFallbackParams", "makeAbortingExoticParams", "makeErroringExoticParams", "createParamsAccessError", "augmentedUnderlying", "ClientPageRoot", "searchParams", "params", "promises", "workAsyncStorage", "clientSearchParams", "clientParams", "ClientSegmentRoot", "slots", "BrowserResolvedMetadata", "metadata", "ServerInsertedMetadataContext", "createContext", "ServerInsertMetadata", "useServerInsertedMetadata", "metadataResolver", "setMetadataResolver", "AsyncMetadata", "AsyncMetadataOutlet", "MetadataOutlet", "digest", "MetadataBoundary", "OutletBoundary", "ViewportBoundary", "NameSpace", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME"], "mappings": "+KAyD0B,EAAA,CAAA,CAAA,iFA8FVG,6BAAAA,qCAAAA,6CA1IkB,CAAA,CAAA,IAAA,SACG,CAAA,CAAA,IAAA,QAM9B,CAAA,CAAA,IAAA,MACkB,CAAA,CAAA,IAAA,WACU,CAAA,CAAA,IAAA,EAqBnC,OAAMC,UAAwCC,EAAAA,OAAK,CAACC,SAAS,CAY3DC,mBAA0B,CAqB1B,CAEA,OAAOK,yBAAyBC,CAAU,CAAE,CAC1C,GAAIC,CAAAA,EAAAA,EAAAA,yBAAAA,AAAyB,EAACD,GAE5B,KAFoC,CAE7B,CACLI,gBAFiBD,CAEAD,AAFAC,EAAAA,EAAAA,2BAAAA,AAA2B,EAACH,EAG/C,CAGF,OAAMA,CACR,CAEA,OAAOK,yBACLV,CAA2C,CAC3CW,CAA8B,CACE,QAOhC,AAAIX,EAAMY,QAAQ,GAAKD,EAAME,gBAAgB,EAAIF,EAAMF,eAAe,CAC7D,CACLA,AAFoE,qBAEnDK,EACjBD,iBAAkBb,EAAMY,QAAQ,AAClC,EAEK,CACLH,gBAAiBE,EAAMF,eAAe,CACtCI,iBAAkBb,EAAMY,QAAQ,AAClC,CACF,CAEAG,QAAS,CACP,GAAM,CAAEC,UAAQ,WAAEC,CAAS,CAAEC,cAAY,UAAEC,CAAQ,CAAE,CAAG,IAAI,CAACnB,KAAK,CAC5D,iBAAES,CAAe,CAAE,CAAG,IAAI,CAACE,KAAK,CAChCS,EAAkB,CACtB,CAACC,EAAAA,qBAAqB,CAACC,SAAS,CAAC,CAAEN,EACnC,CAACK,EAAAA,qBAAqB,CAACE,SAAS,CAAC,CAAEN,EACnC,CAACI,EAAAA,qBAAqB,CAACG,YAAY,CAAC,CAAEN,CACxC,EAEA,GAAIT,EAAiB,CACnB,IAAMgB,EACJhB,IAAoBY,EAAAA,qBAAqB,CAACC,SAAS,EAAIN,EACnDU,EACJjB,IAAoBY,EAAAA,qBAAqB,CAACE,SAAS,EAAIN,EACnDU,EACJlB,IAAoBY,EAAAA,qBAAqB,CAACG,YAAY,EAAIN,SAG5D,AAAMO,GAAcC,CAAhB,CAAED,CAA6BE,EAKjC,CAAA,EAAA,EAAA,IAAA,EAAA,CAL8C,CAK9C,EALkD,MAKlD,CAAA,WACE,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAKC,KAAK,SAASC,QAAQ,aAC3BtC,EAMA4B,CAAe,CAACX,EAAgB,EANxBhB,EANJ0B,CAMO,AASlB,CATmBzB,AAWnB,OAAOyB,CACT,AAZ6B,CAzF7Ba,YAAYhC,CAA2C,CAAE,CACvD,KAAK,CAACA,GACN,IAAI,CAACW,CAuF2B,IAvFtB,CAAG,CACXF,WAsF8B,UAtFbK,CAsFa,CArF9BD,EAsFM,CAAA,GAAA,WAtFYb,CAsFZ,CAtFkBY,EAsFlB,EAACgB,IAtFyB,AAClC,CACF,CAgGF,CAEO,CAdMA,QAcGjC,EAA2B,CAKT,EALS,GAAA,UACzCqB,CAAQ,WACRC,CAAS,cACTC,CAAY,UACZC,CAAQ,CACwB,CALS,EAUnCP,EAAWqB,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,IAC/BhC,EAAeiC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,kBAAkB,SAGlD,AAF4BnB,GAAYC,CAEpCmB,EAFiDlB,EAIjD,CAAA,EAAA,EAAA,GAAA,EAACtB,AAJ2D,EAI3DA,CACCgB,CAHgB,QAGNA,EACVI,SAAUA,EACVC,UAAWA,EACXC,aAAcA,EACdjB,aAAcA,WAEbkB,IAKA,CAAA,EAAA,EAAA,GAAA,EAAA,EAAA,QAAA,CAAA,UAAGA,GACZ,0RCjDiC,EAAA,CAAA,CAAA,iFAqWjC,OAJA,GAIA,qCAAwBkB,uDAzdjB,CAAA,CAAA,IAAA,YASA,CAAA,CAAA,IAAA,aACc,CAAA,CAAA,IAAA,SAKd,CAAA,CAAA,IAAA,OAC6B,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACL,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACM,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,QACU,CAAA,CAAA,IAAA,QACN,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,OACV,CAAA,CAAA,IAAA,IAoDlCY,EACJC,EAAAA,OAAQ,CACRD,4DAA4D,CAmBxDM,EAAiB,CACrB,SACA,SACA,OACA,QACA,MACA,QACA,IACA,IACD,CA2BD,SAASU,EAAuBR,CAAoB,CAAES,CAAsB,EAC1E,IAAML,EAAOJ,EAAQK,qBAAqB,GAC1C,OAAOD,EAAKM,GAAG,EAAI,GAAKN,EAAKM,GAAG,EAAID,CACtC,CA0BA,MAAMQ,UAAmC7E,EAAAA,OAAK,CAACC,SAAS,CA4GtD6E,mBAAoB,CAClB,IAAI,CAACC,qBAAqB,EAC5B,CAEAC,oBAAqB,CAEf,IAAI,CAAC7E,KAAK,CAAC8E,iBAAiB,CAACC,KAAK,EACpC,AADsC,IAClC,CAACH,qBAAqB,EAE9B,CAEA7D,QAAS,CACP,OAAO,IAAI,CAACf,KAAK,CAACmB,QAAQ,AAC5B,mBAzHF,KAAA,IAAA,GAAA,IAAA,CACEyD,qBAAAA,CAAwB,KAEtB,GAAM,CAAEE,mBAAiB,aAAEE,CAAW,CAAE,CAAG,IAAI,CAAChF,KAAK,CAErD,GAAI8E,EAAkBC,KAAK,CAAE,KAf7BT,EAmBE,GAC4C,IAA1CQ,EAAkBG,YAAY,CAACrC,MAAM,EACrC,CAACkC,EAAkBG,YAAY,CAACC,IAAI,CAAC,AAACC,GACpCH,EAAYjB,KAAK,CAAC,CAACtB,EAAS2C,IAC1BvC,GAAAA,EAAAA,YAAAA,AAAY,EAACJ,EAAS0C,CAAoB,CAACC,EAAM,IAIrD,CADA,MAIF,IAAIC,EAEiC,KAC/BhB,EAAeS,EAAkBT,YAAY,CAanD,GAXIA,IACFgB,EA1CN,AAAIhB,AAAiB,OAAO,CA0CZD,AADM,EAxCbE,SAASC,IAAI,CAKIF,AAAxBC,OAAAA,EAAAA,SAASE,cAAc,CAACH,EAAAA,CAAAA,CAAxBC,EAEAA,SAASG,iBAAiB,CAACJ,AAkCUA,EAlCG,CAAC,EAAE,AAkCNA,EAK/B,AAACgB,GACHA,GA1GF,AAAJ,AAAsB,GAyGJ,IACFlC,MA1GmB,OAAxBE,OAA+B,KAMnCC,GADLL,EAA6DE,MA4DiC,KA5DjCA,AAAW,EAqG9C,KAAI,EAIxB,CAAEkC,CAAAA,EAxG0BjC,WAwGPkC,OAAAA,CAAM,CAC7B,EADiC,KAMnC,KAAO,CAAED,CAAAA,aAAmBE,WAAAA,CAAU,EA9F5C,AA8FkD/B,SA9FvBC,AAAlBD,CAAsC,EAI7C,GAAI,CAAC,SAAU,QAAQ,CAACE,QAAQ,CAACC,iBAAiBF,GAASG,QAAQ,EAOjE,CAPoE,MAO7D,EAKT,IAAMC,EAAOJ,EAAQK,qBAAqB,GAC1C,OAAOP,EAAeQ,KAAK,CAAC,AAACC,GAAwB,IAAfH,CAAI,CAACG,EAAK,CAClD,EA4EoEqB,IAAU,CAUtE,GAAmC,MAAM,CAArCA,EAAQG,kBAAkB,CAC5B,OAEFH,EAAUA,EAAQG,kBACpB,AADsC,CAItCV,EAAkBC,KAAK,EAAG,EAC1BD,EAAkBT,YAAY,CAAG,KACjCS,EAAkBG,YAAY,CAAG,EAAE,CAEnCQ,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChB,KAEE,GAAIpB,EAAc,YACdgB,EAAwBK,cAAc,GAM1C,IAAMC,EAAcrB,SAASsB,eAAe,CACtC1B,EAAiByB,EAAYE,YAAY,EAG3C5B,EAAuBoB,EAAwBnB,KAQnDyB,EAAYG,SAAS,CAR+C,AAQ5C,EAGpB,AAAC7B,EAAuBoB,EAAwBnB,IAEhDmB,EAAwBK,WAFyC,GAE3B,GAE5C,EACA,CAEEK,iBAAiB,EACjBC,eAAgBlB,EAAkBkB,cAAc,AAClD,GAIFlB,EAAkBkB,cAAc,EAAG,EAGnCX,EAAQY,KAAK,EACf,CACF,EAgBF,CAEA,SAASC,EAAsB,CAM9B,EAN8B,GAAA,aAC7BlB,CAAW,UACX7D,CAAQ,CAIT,CAN8B,EAOvBgF,EAAUjE,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACkE,EAAAA,yBAAyB,EACpD,GAAI,CAACD,EACH,MAAM,CADM,MACN,cAAuD,CAAvD,AAAIE,MAAM,8CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAsD,GAG9D,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAAC3B,EADH,AACGA,CACCM,YAAaA,EACbF,kBAAmBqB,EAAQrB,iBAAiB,UAE3C3D,GAGP,CAKA,SAASmF,EAAkB,CAU1B,EAV0B,GAAA,MACzBC,CAAI,aACJvB,CAAW,WACXwB,CAAS,CACTC,KAAG,CAMJ,CAV0B,EAWnBN,EAAUjE,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACkE,EAAAA,yBAAyB,EACpD,GAAI,CAACD,EACH,MAAM,CADM,MACN,cAAuD,CAAvD,AAAIE,MAAM,8CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAsD,GAG9D,GAAM,CAAEE,KAAMG,CAAQ,CAAE,CAAGP,EASrBQ,EACsB,OAA1BH,EAAUI,WAAW,CAAYJ,EAAUI,WAAW,CAAGJ,EAAUK,GAAG,CAKlEA,EAAWC,CAAAA,EAAAA,EAAAA,gBAAAA,AAAgB,EAACN,EAAUK,GAAG,CAAEF,GAM3CI,EACW,UAAf,OAAOF,GAA4B,OAARA,GAAoC,YAApB,OAAOA,EAAIG,IAAI,CACtDC,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACJ,GACJA,EAEN,GAAI,CAACE,EAAa,CAMhB,IAAIG,EAAWV,EAAUU,QAAQ,CACjC,GAAiB,OAAbA,EAAmB,CAKrB,IAAMC,EAAc7E,AA/U1B,SAASA,EACPC,CAAgD,CAChDC,CAAiC,EAEjC,GAAID,EAAmB,CACrB,GAAM,CAACE,EAASC,EAAiB,CAAGH,EAC9BI,EAAsC,IAA7BJ,EAAkBK,MAAM,CAEvC,GAAIC,CAAAA,EAAAA,EAAAA,YAAY,AAAZA,EAAaL,CAAc,CAAC,EAAE,CAAEC,IAC9BD,CAAc,CAAC,EAAE,CAACM,CADsB,aACR,CAACJ,GAAmB,CACtD,GAAIC,EAAQ,CACV,IAAMI,EAAUT,EACdxB,OACA0B,CAAc,CAAC,EAAE,CAACE,EAAiB,EAErC,MAAO,CACLF,CAAc,CAAC,EAAE,CACjB,CACE,GAAGA,CAAc,CAAC,EAAE,CACpB,CAACE,EAAiB,CAAE,CAClBK,CAAO,CAAC,EAAE,CACVA,CAAO,CAAC,EAAE,CACVA,CAAO,CAAC,EAAE,CACV,UAEJ,AADG,EAEJ,AACH,CAEA,MAAO,CACLP,CAAc,CAAC,EAAE,CACjB,CACE,GAAGA,CAAc,CAAC,EAAE,CACpB,CAACE,EAAiB,CAAEJ,EAClBC,EAAkBS,KAAK,CAAC,GACxBR,CAAc,CAAC,EAAE,CAACE,EAAiB,CAEvC,EACD,AACH,CAEJ,CAEA,OAAOF,CACT,EAmSyC,CAAC,MAAOwC,EAAY,CAAE0B,GACnDU,EAAiBC,CAAAA,EAAAA,EAAAA,iCAAiC,AAAjCA,EAAkCX,GACnDY,EAAcC,KAAKC,GAAG,GAC5BhB,EAAUU,QAAQ,CAAGA,EAAWO,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EACjD,IAAIC,IAAIjB,EAAKkB,SAASC,MAAM,EAC5B,CACEC,kBAAmBV,EACnBW,QAASV,EAAiBjB,EAAQ2B,OAAO,CAAG,IAC9C,GACAd,IAAI,CAAC,AAACe,IACNC,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,KACdC,CAAAA,EAAAA,EAAAA,uBAAAA,AAAuB,EAAC,CACtBC,KAAMC,EAAAA,mBAAmB,CACzBC,aAAc1B,iBACdqB,cACAT,CACF,EACF,GAEOS,IAITd,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACC,EACN,CAGAD,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACoB,EAAAA,kBAAkB,CACxB,CAmBA,MAdE,CAAA,AAcKC,EAdL,EAAA,GAAA,EAACC,EAAAA,UAD2E,SACxD,CAACC,QAAQ,CAAA,CAC3BC,MAAO,CACLC,WAAYnC,EACZoC,gBAAiBnC,EACjBoC,kBAAmB5D,EAGnByB,IAAKA,CACP,WAECM,GAKP,CAMA,SAAS8B,EAAgB,CAMxB,EANwB,IAenBE,EAfmB,SACvBD,CAAO,UACP3H,CAAQ,CAIT,CANwB,EA2BvB,GALE4H,CAKEA,CAViB,UAAnB,OAAOD,GACPA,AAAY,UACqB,YACjC,AADA,OAAQA,EAAgB9B,IAAI,CAGRC,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EADG6B,AACFE,GAEJF,EAGC,CACrB,IAAMG,EAAaF,CAAiB,CAAC,EAAE,CACjCG,EAAgBH,CAAiB,CAAC,EAAE,CACpCI,EAAiBJ,CAAiB,CAAC,EAAE,CAC3C,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACK,EADH,AACGA,QAAQ,CAAA,CACPC,SACE,CADFA,AACE,EAAA,EAAA,IAAA,EAAA,CADFA,CACE,QAAA,CAAA,WACGH,EACAC,EACAF,cAIJ9H,GAGP,CAEA,MAAO,CAAP,AAAO,EAAA,EAAA,GAAA,EAAA,EAAP,AAAO,QAAA,CAAA,UAAGA,GACZ,CAMe,SAASkB,EAAkB,CAsBzC,EAtByC,GAAA,CACxCiH,mBAAiB,OACjBjJ,CAAK,aACLkJ,CAAW,cACXC,CAAY,gBACZC,CAAc,iBACdC,CAAe,UACfC,CAAQ,CACR3I,UAAQ,WACRC,CAAS,CACTC,cAAY,CAYb,CAtByC,EAuBlCiF,EAAUjE,CAAAA,EAAAA,EAAAA,UAAU,AAAVA,EAAWqG,EAAAA,mBAAmB,EAC9C,GAAI,CAACpC,EACH,MAAM,CADM,MACN,cAA2D,CAA3D,AAAIE,MAAM,kDAAV,oBAAA,OAAA,kBAAA,gBAAA,CAA0D,GAGlE,GAAM,YAAEqC,CAAU,iBAAEC,CAAe,mBAAEC,CAAiB,CAAEnC,KAAG,CAAE,CAAGN,EAI1DyD,EAAuBjB,EAAgBkB,cAAc,CACvDC,EAAaF,EAAqBG,GAAG,CAACT,GAGrCQ,IACHA,EAAa,IAAIE,EADF,EAEfJ,EAAqBK,GAAG,CAACX,EAAmBQ,IAK9C,IAAMI,EAAoBxB,CAAU,CAAC,EAAE,CACjCnC,EAAOmC,CAAU,CAAC,EAAE,CAACY,EAAkB,CACvCa,EAAc5D,CAAI,CAAC,EAAE,CAErBvB,EACkB,OAAtB4D,AAEI,AACA,EACA,CAACU,EAAkB,CACnBV,EAAkBwB,MAAM,CAAC,CAACF,EAAmBZ,EAAkB,EAY/De,EAAWC,CAAAA,EAAAA,EAAAA,QAd0B,YAc1BA,AAAoB,EAACH,GAChCI,EAAWD,GAAAA,EAAAA,SAhB2D,WAgBvC,AAApBA,EAAqBH,GAAa,GAG/C3D,EAAYsD,CAHyC,CAG9BC,GAAG,CAACM,GAC/B,QAAkBvJ,GAJ0D,CAIxE0F,EAAyB,CAG3B,IAAMgE,EAAkC,CACtCtD,SAAU,KACVL,IAAK,KACLD,YAAa,KACb6D,KAAM,KACNC,aAAc,KACdb,eAAgB,IAAIG,IACpBlB,QAAS,KACTxB,YAAa,CAAC,CAChB,EAGAd,EAAYgE,EACZV,EAAWG,GAAG,CAACI,EAAUG,EAC3B,CAoBA,IAAMzB,EAAoBJ,EAAgBG,OAAO,CAEjD,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAAC6B,CADH,CACGA,eAAe,CAACnC,QAAQ,CAAA,CAEvBC,MACE,CAAA,AADFA,EACE,EAAA,GAAA,EAACvC,EADHuC,AACGvC,CAAsBlB,YAAaA,WAClC,CAAA,EAAA,EAAA,GAAA,EAAC4F,EAAD,AAACA,aAAa,CAAA,CACZC,eAAgBxK,EAChBkJ,YAAaA,EACbC,aAAcA,WAEd,CAAA,EAAA,EAAA,GAAA,EAACX,EAAD,AAACA,CAAgBC,QAASC,WACxB,CAAA,EAAA,EAAA,GAAA,EAACpJ,EAAAA,AAAD,0BAA2B,CAAA,CACzBqB,SAAUA,EACVC,UAAWA,EACXC,aAAcA,WAEd,CAAA,EAAA,EAAA,GAAA,EAAC4J,EAAAA,AAAD,gBAAiB,CAAA,UACf,CAAA,EAAA,EAAA,GAAA,EAACxE,EAAD,AAACA,CACCG,IAAKA,EACLF,KAAMA,EACNC,UAAWA,EACXxB,YAAaA,wBAS1ByE,EACAC,EACAC,IA9BIY,EAiCX,mWCnnBA,UAAA,qCAAwBQ,6CAHoB,CAAA,CAAA,IAAA,SACZ,CAAA,CAAA,IAAA,GAEjB,SAASA,IACtB,IAAM5J,EAAWe,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACyI,EAAAA,eAAe,EAC3C,MAAO,CAAP,AAAO,EAAA,EAAA,GAAA,EAAA,EAAA,AAAP,QAAO,CAAA,UAAGxJ,GACZ,mWCRa6J,iBAAAA,qCAAAA,IAAN,OAAMA,UAAuB3E,MAClCrE,YAAYiJ,CAAe,CAAEC,CAAsB,CAAE,CACnD,KAAK,CACF,eAAaD,CAAAA,CAAQE,QAAQ,CAAC,KAAOF,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DC,GAEF,IAAI,CAACrJ,IAAI,CAAG,gBACd,CACF,4HCMgBuJ,iCAAiC,CAAA,kBAAjCA,GAPAC,4BAA4B,CAAA,kBAA5BA,GAeHC,mBAAmB,CAAA,kBAAnBA,uEAjBb,IAAMC,EAA+B,6BAE9B,SAASF,EAA6BG,CAAc,CAAEC,CAAY,SACvE,AAAIF,EAA6BG,IAAI,CAACD,GAC5B,IADmC,AAC/BD,EAAO,IAAGC,EAAK,IAErB,IAAID,EAAO,IAAGG,KAAKC,SAAS,CAACH,GAAM,IAC7C,CAEO,SAASL,EACdI,CAAc,CACdC,CAAY,EAEZ,IAAMI,EAAkBF,KAAKC,SAAS,CAACH,GACvC,MAAQ,gBAAgBD,EAAO,KAAIK,EAAgB,QAASA,EAAgB,OAAML,EAAO,eAC3F,CAEO,IAAMF,EAAsB,IAAIQ,IAAI,CACzC,iBACA,gBACA,uBACA,WACA,UACA,iBAIA,OACA,QACA,UAIA,SAGA,cAIA,SACA,WACA,aACD,6ICzCeC,kCAAAA,qCAAAA,aALoB,CAAA,CAAA,IAAA,IAG9BC,EAAqB,IAAIC,QAExB,SAASF,EACdG,CAAoC,EAEpC,IAAMC,EAAqBH,EAAmBjC,GAAG,CAACmC,GAClD,GAAIC,EACF,OAAOA,EAMT,IAAMC,EAAUC,GAPQ,KAOAC,OAAO,CAACJ,GAYhC,OAXAF,EAAmB/B,GAAG,CAACiC,EAAwBE,GAE/CG,OAAOC,IAAI,CAACN,GAAwBO,OAAO,CAAC,AAAChB,IACvCH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,KAIxBW,CAAe,CAJgB,AAIfX,EAAK,CAAGS,CAAsB,CAACT,EAAAA,AAAK,CAE1D,GAEOW,CACT,0RC5BQM,EAAQ,CAAA,CAAA,UACRC,8CAA8C,yBAJzCC,qCAAAA,qCAAAA,KAAN,IAAMA,EAMLF,EAAQ,CAAA,CAAA,IAAA,IACRX,+BAA+B,mWCDvBc,4BAAAA,qCAAAA,aALoB,CAAA,CAAA,IAAA,IAG9BC,EAAe,IAAIb,QAElB,SAASY,EACdE,CAAwB,EAExB,IAAMC,EAAeF,EAAa/C,GAAG,CAACgD,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZZ,EAAUC,QAAQC,OAAO,CAACS,GAYhC,OAXAD,EAAa7C,GAAG,CAAC8C,EAAkBX,GAEnCG,OAAOC,IAAI,CAACO,GAAkBN,OAAO,CAAC,AAAChB,IACjCH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,KAIxBW,CAAe,CAJgB,AAIfX,EAAK,CAAGsB,CAAgB,CAACtB,EAAAA,AAAK,CAEpD,GAEOW,CACT,0RCzBOM,EAAQ,CAAA,CAAA,GACNO,iDAAiD,6BAH7CC,+BAAAA,qCAAAA,KAAN,IAAMA,EAKLR,EAAQ,CAAA,CAAA,IAAA,IACRG,yBAAyB,mWCNpBM,iBAAAA,qCAAAA,IAAN,OAAMA,EACX,OAAOpD,IACLyB,CAAS,CACTC,CAAqB,CACrB2B,CAAiB,CACZ,CACL,IAAM3E,EAAQ4E,QAAQtD,GAAG,CAACyB,EAAQC,EAAM2B,SACxC,AAAqB,YAAjB,AAA6B,OAAtB3E,EACFA,EAAM6E,IAAI,CAAC9B,GAGb/C,CACT,CAEA,OAAOwB,IACLuB,CAAS,CACTC,CAAqB,CACrBhD,CAAU,CACV2E,CAAa,CACJ,CACT,OAAOC,QAAQpD,GAAG,CAACuB,EAAQC,EAAMhD,EAAO2E,EAC1C,CAEA,OAAOjN,IAAsBqL,CAAS,CAAEC,CAAqB,CAAW,CACtE,OAAO4B,QAAQlN,GAAG,CAACqL,EAAQC,EAC7B,CAEA,OAAO8B,eACL/B,CAAS,CACTC,CAAqB,CACZ,CACT,OAAO4B,QAAQE,cAAc,CAAC/B,EAAQC,EACxC,CACF,oECnBiB,EAAA,CAAA,CAAA,iFA2BDiC,8CAAAA,qCAAAA,odAzCO,CAAA,CAAA,IAAA,oIAEvB,IAAMC,EAAsC,CAAEC,QAAS,IAAK,EAGtDC,EACmB,YAAvB,OAAOhO,EAAMgO,KAAK,CACdhO,EAAMgO,KAAK,CACVC,AAAD,GAAgCA,EAKhCC,EAEFN,QAAQO,IAAI,CA0BT,SAASN,EACdS,CAAoC,EAEpC,OAAO,SAASC,AAAgB,GAAGC,CAAU,EAkBzCN,EAjBcI,KAAcE,GAmBhC,CACF,CA9C+BR,EAE7B,AAACK,CAyCkBjD,GAxCjB,GAAI,CACF8C,EAAeJ,EAASC,OAAO,CACjC,QAAU,CACRD,EAASC,OAAO,CAAG,IACrB,CACF,6BAP0E,iHCjB/DU,gCAAAA,qCAAAA,KAAN,IAAMA,EACXC,CAAAA,EAAAA,EAHsC,CAAA,CAAA,IAAA,GAGtCA,uBAAAA,AAAuB,+ICahBC,wBAAAA,qCAAAA,EAAAA,6BAAqB,YAdyC,CAAA,CAAA,IAAA,8HC+BvDC,+BAA+B,CAAA,kBAA/BA,GAZAC,oCAAoC,CAAA,kBAApCA,GAlBAC,qCAAqC,CAAA,kBAArCA,GASAC,qDAAqD,CAAA,kBAArDA,+EAbsB,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,IAG/B,SAASD,EACdE,CAAa,CACbC,CAAkB,EAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,EAAM,iDAAiD,EAAEC,EAAW,0HAA0H,CAAC,EADpM,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,CAEO,SAASF,EACdC,CAAa,CACbC,CAAkB,EAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,EAAM,4EAA4E,EAAEC,EAAW,0HAA0H,CAAC,EAD/N,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,CAEO,SAASJ,EACdM,CAAoB,EAEpB,IAAM3O,EAAQ,OAAA,cAEb,CAFa,AAAIgG,MAChB,CAAC,MAAM,EAAE2I,EAAUH,KAAK,CAAC,oVAAoV,CAAC,EADlW,oBAAA,OAAA,mBAAA,gBAAA,CAEd,EAIA,OAFAG,EAAUC,iBAAiB,GAAK5O,EAE1BA,CACR,CAEO,SAASoO,IACd,IAAMS,EAAiBV,EAAAA,qBAAqB,CAACW,QAAQ,GACrD,MAAOD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBE,kBAAAA,AAAkB,IAAK,QAChD,mECwHOJ,EAAUK,CAAAA,CAAiB,GAAjBA,6DAtDDC,wCAAwC,CAAA,kBAAxCA,GAxCAC,4BAA4B,CAAA,kBAA5BA,GAmBHC,mCAAmC,CAAA,kBAAnCA,GAGGC,qCAAqC,CAAA,kBAArCA,GAmWAC,yCAAyC,CAAA,kBAAzCA,+EAtbe,CAAA,CAAA,IAAA,QAQxB,CAAA,CAAA,IAAA,QAQA,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,QACyB,CAAA,CAAA,IAAA,QAKrD,CAAA,CAAA,IAAA,QAIA,CAAA,CAAA,IAAA,IAiCA,SAASH,EACdrD,CAAoC,CACpC8C,CAAoB,EAEpB,IAAMW,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GACnD,GAAIQ,EACF,OAAQA,EAAczH,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAO2H,EAA4Bb,EAAWW,EAGlD,CAEF,OAAOG,EAAyB5D,EAAwB8C,EAC1D,GAhDkC,CAAA,CAAA,IAAA,IAmD3B,IAAMQ,EACXC,EAEK,SAASA,EACdvD,CAAoC,CACpC8C,CAAoB,EAEpB,IAAMW,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GACnD,GAAIQ,EACF,OAAQA,EAAczH,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAO2H,EAA4Bb,EAAWW,EAGlD,CAEF,OAAOG,EAAyB5D,EAAwB8C,EAC1D,CAEO,SAASM,EACdN,CAAoB,EAEpB,GAAIA,EAAUe,WAAW,CAGvB,CAHyB,MAGlB1D,QAAQC,OAAO,CAAC,CAAC,GAG1B,IAAM0D,EAAiBJ,EAAAA,oBAAoB,CAACT,QAAQ,UACpD,AAAIa,GAA0C,aAAa,CAArCA,EAAe9H,IAAI,CAIhC+H,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACD,EAAeE,YAAY,CAAE,kBAKlD7D,QAAQC,OAAO,CAAC,CAAC,EAC1B,CAEA,SAASuD,EACPb,CAAoB,CACpBgB,CAA8B,SAE9B,AAAIhB,EAAUe,WAAW,CAGhB1D,CAHkB,OAGVC,OAAO,CAAC,CAAC,GAGE,AAAxB0D,aAAqC,GAAtB9H,IAAI,CAEdiI,AAwCX,SAASA,AACPtB,CAAa,CACbmB,CAAoC,EAEpC,IAAM7D,EAAqBH,EAAmBjC,GAAG,CAACiG,GAClD,GAAI7D,EACF,OAAOA,EAGT,IAAMC,EAAU6D,CAAAA,EAAAA,AAJQ,EAIRA,kBAAAA,AAAkB,EAChCD,EAAeE,YAAY,CAC3B,kBAGII,EAAiB,IAAIC,MAAMnE,EAAS,CACxCrC,IAAIyB,CAAM,CAAEC,CAAI,CAAE2B,CAAQ,EACxB,GAAIb,OAAOiE,MAAM,CAACpE,EAASX,GAIzB,IAJgC,GAIzB0B,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,GAG1C,OAAQ3B,GACN,IAAK,OAIH,MADAgF,CAAAA,EAAAA,EAAAA,qBAAAA,AAAqB,EADnB,AACoB3B,wDAAYkB,GAC3B7C,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,EAE1C,KAAK,SAIH,MADAqD,CAAAA,EAAAA,EAAAA,qBAAAA,AAAqB,EAAC3B,AADpB,yDACgCkB,GAC3B7C,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,EAG1C,SACE,GAAoB,UAAhB,OAAO3B,GAAqB,CAACH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,GAAO,CAC9D,IAAMqD,EAAazD,GAAAA,EAAAA,4BAAAA,AAA4B,EAC7C,eACAI,GAEIpL,EAAQqQ,EAAwB7B,EAAOC,GAC7C6B,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACzC9B,EACAC,EACAzO,EACA2P,EAEJ,CACA,OAAO7C,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,EAE5C,CACF,EACAjN,IAAIqL,CAAM,CAAEC,CAAI,EAKd,GAAoB,UAAhB,OAAOA,EAAmB,CAC5B,IAAMqD,EAAa1D,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EAClD,eACAK,GAEIpL,EAAQqQ,EAAwB7B,EAAOC,GAC7C6B,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACzC9B,EACAC,EACAzO,EACA2P,EAEJ,CACA,OAAO7C,EAAAA,cAAc,CAAChN,GAAG,CAACqL,EAAQC,EACpC,EACAmF,UACE,IAAM9B,EACJ,+DACIzO,EAAQqQ,EAAwB7B,EAAOC,GAC7C6B,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACzC9B,EACAC,EACAzO,EACA2P,EAEJ,CACF,GAGA,OADAhE,EAAmB/B,GAAG,CAAC+F,EAAgBM,GAChCA,CACT,EAlI0CtB,EAAUH,KAAK,CAAEmB,GAoI3D,AA9HSI,SA8HAA,AACPpB,CAAoB,CACpBgB,CAAwD,EAExD,IAAM7D,EAAqBH,EAAmBjC,GAAG,CAACiF,GAClD,GAAI7C,EACF,OAAOA,EAOT,IAAMC,EAAUC,GARQ,KAQAC,OAAO,CAACJ,AAJD,CAAC,GAM1BoE,EAAiB,IAAIC,MAAMnE,EAAS,CACxCrC,IAAIyB,CAAM,CAAEC,CAAI,CAAE2B,CAAQ,EACxB,GAAIb,OAAOiE,MAAM,CAACpE,EAASX,GAIzB,IAJgC,GAIzB0B,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,GAG1C,OAAQ3B,GACN,IAAK,OAAQ,CACX,IAAMqD,EACJ,uDACEE,GAAU6B,kBAAkB,CAC9BjC,CADgC,AAChCA,EAAAA,EAAAA,qDAAAA,AAAqD,EACnDI,EAAUH,KAAK,CACfC,GAE+B,iBAAiB,CAAzCkB,EAAe9H,IAAI,CAE5B4I,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,GAGJ,MACF,CACA,IAAK,SAAU,CACb,IAAMlB,EACJ,yDACEE,EAAU6B,kBAAkB,CAC9BjC,CADgC,AAChCA,EAAAA,EAAAA,qDAAAA,AAAqD,EACnDI,EAAUH,KAAK,CACfC,GAE+B,iBAAiB,CAAzCkB,EAAe9H,IAAI,CAE5B4I,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,GAGJ,MACF,CACA,QACE,GAAI,AAAgB,iBAATvE,GAAqB,CAACH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,GAAO,CAC9D,IAAMqD,EAAazD,CAAAA,EAAAA,EAAAA,4BAAAA,AAA4B,EAC7C,eACAI,GAEEuD,EAAU6B,kBAAkB,CAC9BjC,CAAAA,AADgC,EAChCA,EAAAA,qDAAAA,AAAqD,EACnDI,EAAUH,KAAK,CACfC,GAEOkB,AAAwB,iBAAiB,GAA1B9H,IAAI,CAE5B4I,GAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,EAGN,CACA,OAAO7C,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,EAE5C,CACF,EACAjN,IAAIqL,CAAM,CAAEC,CAAI,EAKd,GAAoB,UAAhB,OAAOA,EAAmB,CAC5B,IAAMqD,EAAa1D,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EAClD,eACAK,GAsBF,OApBIuD,EAAU6B,kBAAkB,CAC9BjC,CADgC,AAChCA,EAAAA,EAAAA,qDAAAA,AAAqD,EACnDI,EAAUH,KAAK,CACfC,GAEOkB,AAAwB,iBAAiB,GAA1B9H,IAAI,CAE5B4I,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,IAGG,CACT,CACA,OAAO7C,EAAAA,cAAc,CAAChN,GAAG,CAACqL,EAAQC,EACpC,EACAmF,UACE,IAAM9B,EACJ,+DACEE,EAAU6B,kBAAkB,CAC9BjC,CADgC,AAChCA,EAAAA,EAAAA,qDAAqD,AAArDA,EACEI,EAAUH,KAAK,CACfC,GAE+B,iBAAiB,CAAzCkB,EAAe9H,IAAI,CAE5B4I,GAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,GAAAA,EAAAA,gCAAAA,AAAgC,EAAClC,EAAYE,EAAWgB,EAE5D,CACF,GAGA,OADAhE,EAAmB/B,GAAG,CAAC+E,EAAWsB,GAC3BA,CACT,EAlSwCtB,EAAWgB,EACnD,CAEA,SAASF,EACP5D,CAAoC,CACpC8C,CAAoB,SAEhBA,AAAJ,EAAce,WAAW,CAGhB1D,CAHkB,OAGVC,OAAO,CAAC,CAAC,GAWfP,AAwUb,SAASA,AACPG,CAAoC,CACpC+E,CAAgB,EAEhB,IAAM9E,EAAqBH,EAAmBjC,GAAG,CAACmC,GAClD,GAAIC,EACF,OAAOA,EAMT,IAAMC,EAAUC,GAPQ,KAOAC,OAAO,CAACJ,GAwBhC,OAvBAF,EAAmB/B,GAAG,CAACiC,EAAwBE,GAE/CG,OAAOC,IAAI,CAACN,GAAwBO,OAAO,CAAC,AAAChB,IACvC,AAACH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,IAC3Bc,GADkC,IAC3B2E,cAAc,CAAC9E,EAASX,EAAM,CACnC1B,MACE,IAAM4F,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GAEnD,MADAgC,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACF,EAAOtB,GAChCzD,CAAsB,CAACT,EAAK,AACrC,EACAxB,IAAIxB,CAAK,EACP8D,OAAO2E,cAAc,CAAC9E,EAASX,EAAM,OACnChD,EACA2I,UAAU,EACVC,YAAY,CACd,EACF,EACAA,YAAY,EACZC,cAAc,CAChB,EAEJ,GAEOlF,CACT,EA7W6CF,EAAwB8C,EAGrE,CAGA,IAAMhD,EAAqB,IAAIC,QAEzBoE,EAAgC,IAAIpE,QA4QnC,SAASyD,EACdV,CAAoB,EAEpB,IAAM7C,EAAqBkE,EAA8BtG,GAAG,CAACiF,GAC7D,GAAI7C,EACF,OAAOA,EAGT,IAAMC,EAAUC,GAJQ,KAIAC,OAAO,CAAC,CAAC,GAE3BgE,EAAiB,IAAIC,MAAMnE,EAAS,CACxCrC,KAAIyB,EAAQC,EAAM2B,EAAR,AAAM,GACVb,GADoB,IACbiE,MAAM,CAACpE,EAASX,IAST,GATgB,OAShC,EACCA,KADMA,GACG,SAATA,CAAmB,EAACH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,IAE7CiD,CAFiD,EAEjDA,CADA,CACAA,oCAAAA,AAAoC,EAACM,GAP9B7B,EAAAA,cAAc,CAACpD,GAAG,CAACyB,EAAQC,EAAM2B,QAY5CjN,CAAIqL,EAAQC,IAAF,AAAM,CAMI,UAAhB,EACCA,KADMA,GACG,SAATA,CAAmB,EAACH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,IAE7CiD,CAFiD,AAEjDA,EAAAA,CADA,CACAA,oCAAoC,AAApCA,EAAqCM,GAGhC7B,EAAAA,cAAc,CAAChN,GAAG,CAACqL,EAAQC,IAEpCmF,UACElC,CAAAA,EAAAA,EAAAA,oCAAAA,AAAoC,EAACM,EACvC,CACF,GAGA,OADAqB,EAA8BpG,GAAG,CAAC+E,EAAWsB,GACtCA,CACT,CAmOA,SAASI,EACP7B,CAAyB,CACzBC,CAAkB,EAElB,IAAM2D,EAAS5D,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIxI,MACT,CAAA,EAAGoM,EAAO,KAAK,EAAE3D,EAAW,gIAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,gBAAA,CAIP,EACF,CAjB0BpB,AAc6C,CAd7CA,AAc8C,EAd9CA,CAepB,CAfoBA,AAenB,2CAfmBA,AAA2C,EACnEgD,GAIAhD,CAAAA,EAAAA,EAAAA,SAUmE,CAAC,iCAVpEA,AAA2C,EAAC8E,AAc9C,SAASA,AACP3D,CAAyB,CACzBC,CAAkB,CAClBoD,CAAgC,EAEhC,IAAMO,EAAS5D,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAON,CAPM,AAAIxI,MACT,CAAA,EAAGoM,EAAO,KAAK,EAAE3D,EAIf,SAJ0B,EAAE,wLAI5B,EAAG4D,AAKT,SAASA,AAA4BC,CAAyB,EAC5D,OAAQA,EAAW/P,MAAM,EACvB,KAAK,EACH,MAAM,OAAA,cAEL,CAFK,IAAIoI,EAAAA,cAAc,CACtB,uFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAE2H,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC/B,MAAK,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACxD,SAAS,CACP,IAAIC,EAAc,GAClB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAW/P,MAAM,CAAG,EAAGiQ,IAAK,AAC9CD,GAAe,CAAC,EAAE,EAAED,CAAU,CAACE,EAAE,CAAC,IAAI,CAAC,CAGzC,OAAOD,AADPA,EAAe,CAAC,QAAQ,EAAED,CAAU,CAACA,EAAW/P,MAAM,CAAG,EAAE,CAAC,EAAE,CAEhE,AAFiE,CAGnE,CACF,EAxBqCsP,GAAmB,gEAAE,CAJvB,AAIwB,EALlD,CAMH,AAJA,CAAC,AAIA,kBANE,OAAA,iBAAA,gBAAA,CAOP,EACF,CAFqE,CAAC,CAJC,CAAC,GAClE,CAAC,6DCviB0ClD,EAAUK,CDuiBY,ACviBZA,CAAiB,ADuiBJ,GCviBbA,ADwiBrD,CAAC,4DCzqBSyD,KDyqBwD,CAAC,GACnE,aC1qBgC,CAAA,kBAAtBA,GA2DAC,qCAAqC,CAAA,kBAArCA,GAvCHC,6BAA6B,CAAA,kBAA7BA,GAGGC,0BAA0B,CAAA,kBAA1BA,GAkBAC,kCAAkC,CAAA,kBAAlCA,yEAhGe,CAAA,CAAA,IAAA,YAMxB,CAAA,CAAA,IAAA,QAQA,CAAA,CAAA,IAAA,QACwB,CAAA,CAAA,IAAA,QAIxB,CAAA,CAAA,IAAA,QAC4B,CAAA,CAAA,IAAA,QACyB,CAAA,CAAA,IAAA,IAkCrD,SAASJ,EACd/F,CAAwB,CACxBiC,CAAoB,QAEpB,IAAMW,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GACnD,GAAIQ,EACF,OAAQA,EAAczH,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOiL,EAAsBpG,EAAkBiC,EAAWW,EAG9D,CAEF,OAAOyD,AAgHPpE,EAhH4CA,IAAlBjC,EAC5B,CA+GsB,EAhKY,CAAA,CAAA,IAAA,IAqD3B,IAAMiG,EAAgCE,EAGtC,SAASD,EACdlG,CAAwB,CACxBiC,CAAoB,QAEpB,IAAMW,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GACnD,GAAIQ,EACF,OAAQA,EAAczH,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOiL,EAAsBpG,EAAkBiC,EAAWW,EAG9D,CAEF,OAAOyD,EAAqCpE,EAiGnCnC,EAjGiBE,EAC5B,CAEO,SAASmG,EACdnG,CAAwB,CACxBiC,CAAoB,OA4FejC,CA1FnC,IAAM4C,EAAgBC,EAAAA,oBAAoB,CAACT,QAAQ,GACnD,GAAIQ,EACF,OAAQA,EAAczH,IADL,AACS,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOiL,EAAsBpG,EAAkBiC,EAAWW,EAG9D,CAEF,OAAOyD,EAAqCpE,IAAlBjC,EAC5B,CAEO,SAASgG,EACdhG,CAAwB,CACxBiC,CAAoB,EAEpB,IAAMgB,EAAiBJ,EAAAA,oBAAoB,CAACT,QAAQ,GACpD,GAAIa,GAA0C,cAAxBA,EAAe9H,IAAI,CAAkB,CACzD,IAAMmL,EAAiBrE,EAAUsE,mBAAmB,CACpD,GAAID,EACF,KAAK,IAAInF,KADS,AACFnB,EACd,GAAIsG,EAAelT,GAAG,CAAC+N,GAIrB,GAJ2B,AADG,GAKvB+B,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAACD,EAAeE,YAAY,CAAE,WAE3D,CAEJ,CAIA,OAAO7D,QAAQC,OAAO,CAACS,EACzB,CAEA,SAASoG,EACPpG,CAAwB,CACxBiC,CAAoB,CACpBgB,CAA8B,EAE9B,IAAMqD,EAAiBrE,EAAUsE,mBAAmB,CACpD,GAAID,EAAgB,CAClB,IAAIE,GAAwB,EAC5B,IAAK,IAAMrF,KAAOnB,EAChB,GAAIsG,EAAelT,GAAG,CAAC+N,GAAM,CAC3BqF,EAFgC,CAER,EACxB,KACF,CAGF,GAAIA,QAE0B,AAA5B,aAAyC,CAArCvD,CAFqB,CAEN9H,IAAI,CAEdsL,AAwCf,SAASA,AACPzG,CAAwB,CACxB8B,CAAa,CACbmB,CAAoC,EAEpC,IAAMhD,EAAeF,EAAa/C,GAAG,CAACgD,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZZ,EAAU6D,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChCD,EAAeE,YAAY,CAC3B,YAiCF,OA/BApD,EAAa7C,GAAG,CAAC8C,EAAkBX,GAEnCG,OAAOC,IAAI,CAACO,GAAkBN,OAAO,CAAC,AAAChB,IACjCH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,IAI1Bc,GAJiC,IAI1B2E,cAAc,CAAC9E,EAASX,EAAM,CACnC1B,MACE,IAAM+E,EAAazD,CAAAA,EAAAA,EAAAA,4BAAAA,AAA4B,EAAC,SAAUI,GACpDpL,EAAQqT,EAAwB7E,EAAOC,GAC7C6B,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACzC9B,EACAC,EACAzO,EACA2P,EAEJ,EACA/F,IAAI8H,CAAQ,EACVxF,OAAO2E,cAAc,CAAC9E,EAASX,EAAM,CACnChD,MAAOsJ,EACPX,SAAU,GACVC,YAAY,CACd,EACF,EACAA,YAAY,EACZC,cAAc,CAChB,EAEJ,GAEOlF,CACT,EArFUW,EACAiC,EAAUH,KAAK,CACfmB,GAOGyD,AA8Eb,SAASA,AACP1G,CAAwB,CACxBsG,CAAmC,CACnCrE,CAAoB,CACpBgB,CAAwD,EAExD,IAAMhD,EAAeF,EAAa/C,GAAG,CAACgD,GACtC,GAAIC,EACF,OAAOA,EAGT,GAJkB,CAIZ2G,EAAsB,CAAE,GAAG5G,CAAgB,AAAC,EAK5CX,EAAUC,QAAQC,OAAO,CAACqH,GA6EhC,OA5EA7G,EAAa7C,GAAG,CAAC8C,EAAkBX,GAEnCG,OAAOC,IAAI,CAACO,GAAkBN,OAAO,CAAC,AAAChB,IACjCH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,KAItB4H,EAAelT,AAJc,GAIX,CAACsL,IACrBc,GAD4B,IACrB2E,cAAc,CAACyC,EAAqBlI,EAAM,CAC/C1B,MACE,IAAM+E,EAAazD,CAAAA,EAAAA,EAAAA,4BAAAA,AAA4B,EAAC,SAAUI,GAO9B,iBAAiB,CAAzCuE,EAAe9H,IAAI,CAErB4I,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,EAGN,EACAqB,YAAY,CACd,GACA9E,OAAO2E,cAAc,CAAC9E,EAASX,EAAM,CACnC1B,MACE,IAAM+E,EAAazD,CAAAA,EAAAA,EAAAA,4BAAAA,AAA4B,EAAC,SAAUI,GAO9B,iBAAiB,CAAzCuE,EAAe9H,IAAI,CAErB4I,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAClB9B,EAAUH,KAAK,CACfC,EACAkB,EAAee,eAAe,EAIhCC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EAC9BlC,EACAE,EACAgB,EAGN,EACA/F,IAAI8H,CAAQ,EACVxF,OAAO2E,cAAc,CAAC9E,EAASX,EAAM,CACnChD,MAAOsJ,EACPX,SAAU,GACVC,YAAY,CACd,EACF,EACAA,YAAY,EACZC,cAAc,CAChB,IAEElF,CAAe,CAACX,EAAK,CAAGsB,CAAgB,CAACtB,EAAK,CAGtD,GAEOW,CACT,EA3KQW,EACAsG,EACArE,EACAgB,EAGN,CAGA,OAAOnD,EAA0BE,EACnC,CAiBA,IAAMD,EAAe,IAAIb,QAkJzB,SAASY,EAA0BE,CAAwB,EACzD,IAAMC,EAAeF,EAAa/C,GAAG,CAACgD,GACtC,GAAIC,EACF,OAAOA,EAMT,GAPkB,CAOZZ,EAAUC,QAAQC,OAAO,CAACS,GAYhC,OAXAD,EAAa7C,GAAG,CAAC8C,EAAkBX,GAEnCG,OAAOC,IAAI,CAACO,GAAkBN,OAAO,CAAC,AAAChB,IACjCH,EAAAA,mBAAmB,CAACnL,GAAG,CAACsL,KAIxBW,CAAe,CAJgB,AAIfX,EAAK,CAAGsB,CAAgB,CAACtB,EAAAA,AAAK,CAEpD,GAEOW,CACT,CA6FA,SAASsH,EACP7E,CAAyB,CACzBC,CAAkB,EAElB,IAAM2D,EAAS5D,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIxI,MACT,CAAA,EAAGoM,EAAO,KAAK,EAAE3D,EAAW,0HAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAIP,CADI,CAAC,AAEP,CAjB0BpB,CAAAA,EAAAA,EAAAA,2CAA2C,AAA3CA,EACxBgG,GAIAhG,CAAAA,EAAAA,EAAAA,GAUmE,CAAC,uCAVpEA,AAA2C,EAAC8E,AAc9C,SAASA,AACP3D,CAAyB,CACzBC,CAAkB,CAClBoD,CAAgC,EAEhC,IAAMO,EAAS5D,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAON,CAPM,AAAIxI,MACT,CAAA,EAAGoM,EAAO,KAAK,EAAE3D,EAIf,SAJ0B,EAAE,oKAI5B,EAAG4D,AAKT,SAASA,AAA4BC,CAAyB,EAC5D,OAAQA,EAAW/P,MAAM,EACvB,KAAK,EACH,MAAM,OAAA,cAEL,CAFK,IAAIoI,EAAAA,cAAc,CACtB,uFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAE2H,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC/B,MAAK,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACxD,SAAS,CACP,IAAIC,EAAc,GAClB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAW/P,MAAM,CAAG,EAAGiQ,IAAK,AAC9CD,GAAe,CAAC,EAAE,EAAED,CAAU,CAACE,EAAE,CAAC,IAAI,CAAC,CAGzC,OAAOD,AADPA,EAAe,CAAC,QAAQ,EAAED,CAAU,CAACA,EAAW/P,MAAM,CAAG,EAAE,CAAC,EAAE,CAAC,AAEjE,CACF,CACF,EAxBqCsP,GAAmB,gEAAE,CAJvB,AAIwB,EALlD,CAMH,AAJA,CAAC,AAIA,kBANE,OAAA,mBAAA,cAEwD,CAFxD,AAEyD,EAKhE,CADmE,AAH/D,CAGgE,AAEtE,AALO,gEAAgE,CAAC,GAClE,CAAC,mDAAmD,CAAC,GACrD,kBCndU0B,iBAAAA,qCAAAA,2BAZe,CAAA,CAAA,IAAA,IAYxB,SAASA,EAAe,CAW9B,EAX8B,GAAA,WAC7B9T,CAAS,cACT+T,CAAY,QACZC,CAAM,UAENC,CAAQ,CAMT,CAX8B,EAY7B,GAAsB,aAAlB,OAAO1Q,OAAwB,CACjC,IAGI4Q,EACAC,EAJE,OATqD,WASnDF,CAAgB,CAAE,CACxBtH,EAAQ,CAAA,CAAA,IAAA,IAMJuE,EAAQ+C,EAAiB7E,QAAQ,GACvC,GAAI,CAAC8B,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAIjG,EAAAA,cAAc,CACtB,4EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,8BAAEuE,CAA4B,CAAE,CACpC7C,EAAQ,CAAA,CAAA,IAAA,GACVuH,EAAqB1E,EAA6BsE,EAAc5C,GAEhE,GAAM,wBAAE6B,CAAsB,CAAE,CAC9BpG,EAAQ,CAAA,CAAA,IAAA,IAGV,OAFAwH,AAEA,EAFepB,EAAuBgB,EAAQ7C,GAEvC,CAAA,CAAP,CAAO,EAAA,GAAA,EAACnR,EAAAA,CAAUgU,OAAQI,EAAcL,aAAcI,GACxD,CAAO,CACL,GAAM,CAAErH,oCAAkC,CAAE,CAC1CF,EAAQ,CAAA,CAAA,IAAA,IACJuH,EAAqBrH,EAAmCiH,GACxD,8BAAE3G,CAA4B,CAAE,CACpCR,EAAQ,CAAA,CAAA,IAAA,IACJwH,EAAehH,EAA6B4G,GAElD,MAAO,CAAP,AAAO,EAAA,EAAA,GAAA,EAAChU,EAAAA,AAAR,CAAkBgU,OAAQI,EAAcL,aAAcI,GACxD,CACF,mWC/CgBE,oBAAAA,qCAAAA,2BAZe,CAAA,CAAA,IAAA,IAYxB,SAASA,EAAkB,CAWjC,EAXiC,GAAA,WAChCrU,CAAS,OACTsU,CAAK,QACLN,CAAM,SAEN1H,CAAO,CAMR,CAXiC,EAYhC,GAAI,AAAkB,oBAAX/I,OAAwB,CACjC,IAGI6Q,EAHE,UATqD,QASnDF,CAAgB,CAAE,CACxBtH,EAAQ,CAAA,CAAA,IAAA,IAKJuE,EAAQ+C,EAAiB7E,QAAQ,GACvC,GAAI,CAAC8B,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAIjG,EAAAA,cAAc,CACtB,sGADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,CAAE8H,wBAAsB,CAAE,CAC9BpG,EAAQ,CAAA,CAAA,IAAA,IAGV,OAFAwH,AAEA,EAFepB,EAAuBgB,EAAQ7C,GAEvC,CAAA,CAAP,CAAO,EAAA,GAAA,EAACnR,EAAAA,CAAW,GAAGsU,CAAK,CAAEN,OAAQI,GACvC,CAAO,CACL,GAAM,8BAAEhH,CAA4B,CAAE,CACpCR,EAAQ,CAAA,CAAA,IAAA,IACJwH,EAAehH,EAA6B4G,GAClD,MAAO,CAAA,AAAP,EAAO,EAAA,GAAA,EAAChU,EAAAA,AAAR,CAAmB,GAAGsU,CAAK,CAAEN,OAAQI,GACvC,CACF,mWChDgBG,0BAAAA,qCAAAA,aAHI,CAAA,CAAA,IAAA,IAGb,SAASA,EAAwB,CAIvC,EAJuC,GAAA,SACtCjI,CAAO,CAGR,CAJuC,EAKhC,CAAEkI,UAAQ,CAAEjU,OAAK,CAAE,CAAG4G,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACmF,UAGhC,AAAI/L,EAAc,KAAP,AACJiU,CACT,mWCLaC,gCAAAA,qCAAAA,KAAN,IAAMA,EACXC,CAAAA,EAAAA,EAN4B,CAAA,CAAA,IAAA,IAM5BA,aAAAA,AAAa,EAAgC,iJCQ/BC,uBAAAA,qCAAAA,aAjBgB,CAAA,CAAA,IAAA,QAIzB,CAAA,CAAA,IAAA,IAKDC,EAA4B,AAACC,IACjC,IAAMC,EAAsB1S,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACqS,EAAAA,6BAA6B,EAEhEK,GACFA,EAAoBD,EAExB,EAEO,SAASF,EAAqB,CALV,AAS1B,EAJoC,GAAA,SACnCrI,CAAO,CAGR,CAJoC,EAM7B,UAAEkI,CAAQ,CAAE,CAAGrN,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACmF,GAIzB,OAFAsI,EAA0B,IAAMJ,GAEzB,IACT,kVCvBaO,aAAa,CAAA,kBAAbA,GA0BGC,mBAAmB,CAAA,kBAAnBA,6FA7Bc,CAAA,CAAA,IAAA,IAGjBD,EACO,aAAlB,OAAOxR,OAEDqJ,EAAQ,CAAA,CAAA,IAAA,IACR+H,oBAAoB,CAEpB/H,EAAQ,CAAA,CAAA,IAAA,IACR2H,uBAAuB,CAE/B,SAASU,EAAe,CAIvB,EAJuB,GAAA,CACtB3I,SAAO,CAGR,CAJuB,EAKhB,OAAE/L,CAAK,QAAE2U,CAAM,CAAE,CAAG/N,CAAAA,EAAAA,EAAAA,GAAAA,AAAG,EAACmF,GAC9B,GAAI/L,EAMF,KANS,CACL2U,IAGA3U,EAAc2U,EAHN,IAGY,CAAGA,CAAAA,EAErB3U,EAER,OAAO,IACT,CAEO,SAASyU,EAAoB,CAInC,EAJmC,GAAA,SAClC1I,CAAO,CAGR,CAJmC,EAKlC,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAAChD,EADH,AACGA,QAAQ,CAAA,CAACC,SAAU,cAClB,CAAA,EAAA,EAAA,GAAA,EAAC0L,EAAD,AAACA,CAAe3I,QAASA,KAG/B,kVCPa6I,gBAAgB,CAAA,kBAAhBA,GAUAC,cAAc,CAAA,kBAAdA,GALAC,gBAAgB,CAAA,kBAAhBA,+EAjCN,CAAA,CAAA,IAAA,IAIDC,EAAY,CAChB,CAACC,EAAAA,sBAAsB,CAAC,CAAE,SAAU,CAInC,EAJmC,GAAA,UAClClU,CAAQ,CAGT,CAJmC,EAKlC,OAAOA,CACT,EACA,CAACmU,EAAAA,sBAAsB,CAAC,CAAE,SAAU,CAInC,EAJmC,GAAA,CAClCnU,UAAQ,CAGT,CAJmC,EAKlC,OAAOA,CACT,EACA,CAACoU,EAAAA,oBAAoB,CAAC,CAAE,SAAU,CAIjC,EAJiC,GAAA,UAChCpU,CAAQ,CAGT,CAJiC,EAKhC,OAAOA,CACT,CACF,EAEa8T,EAGXG,CAAS,CAACC,EAAAA,aAFV,AACA,SACgC,CAACrS,KAAK,CAAC,GAAoC,CAEhEmS,EAGXC,CAAS,CAACE,EAAAA,aAFV,AACA,SACgC,CAACtS,KAAK,CAAC,GAAoC,CAEhEkS,CARiD,CAW5DE,CAAS,CAACG,EAAAA,WAFV,AACA,IAXgF,KAYlD,CAACvS,KAAK,CAAC,GAAkC,IANX,oBADoB,iBAMpB,oBADoB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}