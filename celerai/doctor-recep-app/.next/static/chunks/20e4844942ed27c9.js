(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{333254:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var a=e.i(722486);let t=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],s=(0,a.default)("house",t)}},963535:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Home:()=>a.default});var a=e.i(333254)},913114:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({DashboardHeader:()=>u});var a=e.i(731636),l=e.i(186240),r=e.i(828846),n=e.i(60992),i=e.i(565992),d=e.i(471895),o=e.i(855423),c=e.i(963535),m=e.i(65659),x=e.i(19820),h=e.i(838653);function u({user:e}){let[t,s]=(0,h.useState)(!1),[u,g]=(0,h.useState)(null),[p,f]=(0,h.useState)("general"),[b,v]=(0,h.useState)(""),[j,N]=(0,h.useState)(!1),w=async()=>{s(!0),g(null),f("general"),v("")},y=async()=>{if(!b.trim())return void g("Please enter a message");N(!0),g(null);try{if(e?.id){let t=await (0,x.createContactRequest)(e.id,`Subject: ${p}

Message: ${b}`,p);t.success?(console.log("Contact request created successfully"),setTimeout(()=>s(!1),2e3)):(g(t.error||"Failed to create contact request"),console.error("Contact request failed:",t.error))}else g("User not found")}catch(e){g(e instanceof Error?e.message:"Unknown error"),console.error("Error creating contact request:",e)}finally{N(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-sm shadow-sm border-b border-orange-200/50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between py-4 sm:py-6",children:[(0,a.jsxs)("div",{className:"hidden sm:block flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"text-lg sm:text-xl font-bold text-slate-800",children:(0,a.jsxs)("h2",{className:"truncate",children:["Dr. ",e?.name||"Doctor"]})}),(0,a.jsx)("p",{className:"text-sm text-slate-600 truncate",children:e?.clinic_name||"Medical Practice"})]}),(0,a.jsxs)(l.default,{href:"/dashboard",className:"flex items-center space-x-4 flex-shrink-0 mx-4 hover:opacity-80 transition-opacity",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,a.jsx)(d.Stethoscope,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"})]}),(0,a.jsxs)("div",{className:"hidden sm:block text-center",children:[(0,a.jsx)("h1",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent",children:"Celer AI"}),(0,a.jsx)("p",{className:"text-sm text-teal-600/80",children:"AI-Powered Healthcare"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-4 flex-1 justify-end",children:[(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-1",children:(0,a.jsxs)(l.default,{href:"/dashboard",className:"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-orange-50 hover:text-teal-700 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,a.jsx)(c.Home,{className:"w-4 h-4 mr-2"}),"Dashboard"]})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-3",children:[(0,a.jsxs)("button",{type:"button",onClick:w,className:"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,a.jsx)(i.Phone,{className:"w-4 h-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Contact"})]}),(0,a.jsxs)(l.default,{href:"/settings",prefetch:!0,className:"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,a.jsx)(n.Settings,{className:"w-4 h-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Settings"})]}),(0,a.jsx)("form",{action:m.logout,children:(0,a.jsxs)("button",{type:"submit",className:"inline-flex items-center px-2 sm:px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-md hover:shadow-lg transition-all duration-150 transform hover:scale-105 active:scale-95",children:[(0,a.jsx)(r.LogOut,{className:"w-4 h-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})})]})]})]})})}),t&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 ease-out scale-100",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-teal-500 to-emerald-600 rounded-t-2xl p-6 text-white",children:[(0,a.jsx)("button",{onClick:()=>s(!1),className:"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors",children:(0,a.jsx)(o.X,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.Phone,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:"Contact Founder"}),(0,a.jsx)("p",{className:"text-emerald-100",children:"Get in touch with us"})]})]})]}),(0,a.jsx)("div",{className:"p-6 space-y-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Subject"}),(0,a.jsxs)("select",{value:p,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-black bg-white",children:[(0,a.jsx)("option",{value:"general",children:"General Feedback"}),(0,a.jsx)("option",{value:"technical",children:"Technical Issue"}),(0,a.jsx)("option",{value:"billing",children:"Billing Question"}),(0,a.jsx)("option",{value:"feature",children:"Feature Request"}),(0,a.jsx)("option",{value:"bug",children:"Bug Report"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Message"}),(0,a.jsx)("textarea",{value:b,onChange:e=>v(e.target.value),placeholder:"Please describe your issue or feedback...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none text-black bg-white placeholder-gray-500"})]}),u?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"text-sm text-red-700 font-medium",children:["❌ ",u]})}):j?(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,a.jsx)("div",{className:"text-sm text-blue-700 font-medium",children:"📤 Submitting your request..."})}):null,(0,a.jsx)("button",{onClick:y,disabled:j||!b.trim(),className:"w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:j?"Submitting...":"Submit Request"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"or contact directly"})})]}),(0,a.jsxs)("div",{className:"text-center space-y-3",children:[(0,a.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-slate-800 mb-2",children:"📞 +91 8921628177"}),(0,a.jsx)("div",{className:"text-sm text-slate-600",children:"Available: 9 AM - 9 PM IST"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("a",{href:"tel:+918921628177",className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"📞 Call Now"}),(0,a.jsx)("a",{href:"https://wa.me/918921628177",target:"_blank",rel:"noopener noreferrer",className:"flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"💬 WhatsApp"})]})]})]})})]})})})]})}},619109:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({TemplatesSidebar:()=>d});var a=e.i(731636),l=e.i(328161),r=e.i(618423),n=e.i(685068),i=e.i(766805);function d({templates:e,selectedTemplate:t,onSelectTemplate:s,onNewTemplate:d}){return(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-orange-200/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-800",children:"Templates"}),(0,a.jsxs)("button",{onClick:d,className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,a.jsx)(l.Plus,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"New"})]})]}),(0,a.jsxs)("div",{className:"text-xs text-slate-600",children:[e.length," template",1!==e.length?"s":""]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:0===e.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(r.FileText,{className:"mx-auto h-12 w-12 text-slate-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-800",children:"No templates"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"Get started by creating your first template."})]}):e.map(e=>(0,a.jsx)("div",{onClick:()=>s(e),className:`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${t?.id===e.id?"bg-gradient-to-r from-teal-50 to-emerald-50 border-teal-200 shadow-md":"bg-white/70 border-orange-200/50 hover:bg-orange-50/50"}`,children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-800 truncate",children:e.name}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-2 text-xs text-slate-600",children:[(0,a.jsx)(n.Clock,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:(0,i.formatRelativeTime)(e.updated_at)})]}),(0,a.jsxs)("p",{className:"mt-2 text-xs text-slate-500 line-clamp-2",children:[e.content.substring(0,80),"..."]})]})})},e.id))}),(0,a.jsx)("div",{className:"p-4 border-t border-orange-200/50",children:(0,a.jsx)("div",{className:"text-xs text-slate-500 text-center",children:"Templates help standardize your consultation notes"})})]})}},974077:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({TemplateEditor:()=>c});var a=e.i(731636),l=e.i(838653),r=e.i(365037),n=e.i(190982),i=e.i(400171),d=e.i(618423),o=e.i(766805);function c({template:e,onSave:t}){let[s,c]=(0,l.useState)(""),[m,x]=(0,l.useState)(""),[h,u]=(0,l.useState)(!1);(0,l.useEffect)(()=>{e&&(c(e.name),x(e.content),u(!1))},[e]);let g=async()=>{if(m)try{await navigator.clipboard.writeText(m)}catch(e){console.error("Failed to copy template content:",e)}};return e?(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b border-orange-200/50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[h?(0,a.jsx)("input",{type:"text",value:s,onChange:e=>c(e.target.value),className:"text-lg font-semibold text-slate-800 bg-transparent border-b border-teal-300 focus:outline-none focus:border-teal-500 w-full",placeholder:"Template name..."}):(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-800 truncate",children:e.name}),(0,a.jsxs)("div",{className:"mt-1 text-xs text-slate-600",children:["Created: ",(0,o.formatDate)(e.created_at)," • Last updated: ",(0,o.formatDate)(e.updated_at)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsxs)("button",{onClick:g,className:"flex items-center space-x-2 px-3 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-lg text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-200",children:[(0,a.jsx)(n.Copy,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Copy"})]}),h?(0,a.jsxs)("button",{onClick:()=>{e&&(t({...e,name:s,content:m,updated_at:new Date().toISOString()}),u(!1))},className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,a.jsx)(r.Save,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Save"})]}):(0,a.jsxs)("button",{onClick:()=>u(!0),className:"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg",children:[(0,a.jsx)(i.Edit3,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Edit"})]})]})]})}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsxs)("div",{className:"h-full",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Template Content"}),(0,a.jsx)("textarea",{value:m,onChange:e=>x(e.target.value),placeholder:"Enter your template content here...",className:"w-full h-full p-4 border border-orange-200 rounded-lg resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white text-slate-800 text-sm font-mono",readOnly:!h,style:{minHeight:"400px"}})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-orange-200/50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-slate-500",children:[(0,a.jsxs)("span",{children:[m.length," characters • ",m.split("\n").length," lines"]}),h&&(0,a.jsx)("span",{className:"text-teal-600 font-medium",children:"Editing mode - Click Save to apply changes"})]})})]}):(0,a.jsx)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.FileText,{className:"mx-auto h-12 w-12 text-slate-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-800",children:"No template selected"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"Select a template from the sidebar to view and edit it."})]})})}},87024:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var a=e.i(722486);let t=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],s=(0,a.default)("sparkles",t)}},389262:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Sparkles:()=>a.default});var a=e.i(87024)},99405:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({ComingSoonOverlay:()=>o});var a=e.i(731636),l=e.i(838653),r=e.i(855423),n=e.i(389262),i=e.i(685068),d=e.i(185104);function o(){let[e,t]=(0,l.useState)(!0);return e?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full -translate-y-16 translate-x-16 opacity-50"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-100 to-amber-100 rounded-full translate-y-12 -translate-x-12 opacity-50"}),(0,a.jsx)("button",{onClick:()=>t(!1),className:"absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(r.X,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-teal-500 to-emerald-600 rounded-full mb-4",children:(0,a.jsx)(n.Sparkles,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Coming Soon!"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Custom templates feature is under development"})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-lg",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(d.Zap,{className:"w-5 h-5 text-teal-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Smart Templates"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"AI-powered template suggestions"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(i.Clock,{className:"w-5 h-5 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Quick Access"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"One-click template application"})]})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 text-white rounded-lg text-sm font-medium",children:[(0,a.jsx)(n.Sparkles,{className:"w-4 h-4 mr-2"}),"Expected: Q1 2025"]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Click the X button to close this overlay"})})]})]})}):null}},354046:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({TemplatesInterface:()=>o});var a=e.i(731636),l=e.i(838653),r=e.i(913114),n=e.i(619109),i=e.i(974077),d=e.i(99405);let t=[{id:"1",name:"General Consultation",content:"Patient presents with...\n\nChief Complaint:\n\nHistory of Present Illness:\n\nPhysical Examination:\n\nAssessment:\n\nPlan:",created_at:"2024-01-15T10:00:00Z",updated_at:"2024-01-15T10:00:00Z"},{id:"2",name:"Follow-up Visit",content:"Follow-up visit for...\n\nInterval History:\n\nCurrent Medications:\n\nReview of Systems:\n\nPhysical Examination:\n\nAssessment and Plan:",created_at:"2024-01-10T14:30:00Z",updated_at:"2024-01-12T09:15:00Z"},{id:"3",name:"Discharge Summary",content:"Discharge Summary\n\nAdmission Date:\nDischarge Date:\n\nPrimary Diagnosis:\nSecondary Diagnoses:\n\nHospital Course:\n\nDischarge Medications:\n\nFollow-up Instructions:",created_at:"2024-01-08T16:45:00Z",updated_at:"2024-01-08T16:45:00Z"}];function o({user:e,doctorId:s}){let[o,c]=(0,l.useState)(t[0]),[m]=(0,l.useState)(t);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative",children:[(0,a.jsx)(r.DashboardHeader,{user:e}),(0,a.jsx)("main",{className:"pt-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(n.TemplatesSidebar,{templates:m,selectedTemplate:o,onSelectTemplate:c,onNewTemplate:()=>{c({id:`new-${Date.now()}`,name:"New Template",content:"",created_at:new Date().toISOString(),updated_at:new Date().toISOString()})}})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(i.TemplateEditor,{template:o,onSave:e=>{console.log("Saving template:",e)}})})]})})}),(0,a.jsx)(d.ComingSoonOverlay,{})]})}}}}]);

//# sourceMappingURL=b3749f05731772bd.js.map