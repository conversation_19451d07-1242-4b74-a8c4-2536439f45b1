{"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts", "turbopack:///[turbopack]/browser/runtime/base/runtime-base.ts", "turbopack:///[turbopack]/browser/runtime/base/build-base.ts", "turbopack:///[turbopack]/browser/runtime/dom/runtime-backend-dom.ts"], "sourcesContent": ["/**\n * This file contains runtime types and functions that are shared between all\n * TurboPack ECMAScript runtimes.\n *\n * It will be prepended to the runtime code of each runtime.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"./runtime-types.d.ts\" />\n\ntype EsmNamespaceObject = Record<string, any>;\n\n// @ts-ignore Defined in `dev-base.ts`\ndeclare function getOrInstantiateModuleFromParent<M>(\n  id: ModuleId,\n  sourceModule: M\n): M;\n\nconst REEXPORTED_OBJECTS = Symbol(\"reexported objects\");\n\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>;\n\ninterface ModuleContextEntry {\n  id: () => ModuleId;\n  module: () => any;\n}\n\ninterface ModuleContext {\n  // require call\n  (moduleId: ModuleId): Exports | EsmNamespaceObject;\n\n  // async import call\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>;\n\n  keys(): ModuleId[];\n\n  resolve(moduleId: ModuleId): ModuleId;\n}\n\ntype GetOrInstantiateModuleFromParent<M> = (\n  moduleId: ModuleId,\n  parentModule: M\n) => M;\n\ndeclare function getOrInstantiateRuntimeModule(moduleId: ModuleId, chunkPath: ChunkPath): Module;\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst toStringTag = typeof Symbol !== \"undefined\" && Symbol.toStringTag;\n\nfunction defineProp(\n  obj: any,\n  name: PropertyKey,\n  options: PropertyDescriptor & ThisType<any>\n) {\n  if (!hasOwnProperty.call(obj, name))\n    Object.defineProperty(obj, name, options);\n}\n\n/**\n * Adds the getters to the exports object.\n */\nfunction esm(\n  exports: Exports,\n  getters: Record<string, (() => any) | [() => any, (v: any) => void]>\n) {\n  defineProp(exports, \"__esModule\", { value: true });\n  if (toStringTag) defineProp(exports, toStringTag, { value: \"Module\" });\n  for (const key in getters) {\n    const item = getters[key];\n    if (Array.isArray(item)) {\n      defineProp(exports, key, {\n        get: item[0],\n        set: item[1],\n        enumerable: true,\n      });\n    } else {\n      defineProp(exports, key, { get: item, enumerable: true });\n    }\n  }\n  Object.seal(exports);\n}\n\n/**\n * Makes the module an ESM with exports\n */\nfunction esmExport(\n  module: Module,\n  exports: Exports,\n  getters: Record<string, () => any>\n) {\n  module.namespaceObject = module.exports;\n  esm(exports, getters);\n}\n\nfunction ensureDynamicExports(module: Module, exports: Exports) {\n  let reexportedObjects = module[REEXPORTED_OBJECTS];\n\n  if (!reexportedObjects) {\n    reexportedObjects = module[REEXPORTED_OBJECTS] = [];\n    module.exports = module.namespaceObject = new Proxy(exports, {\n      get(target, prop) {\n        if (\n          hasOwnProperty.call(target, prop) ||\n          prop === \"default\" ||\n          prop === \"__esModule\"\n        ) {\n          return Reflect.get(target, prop);\n        }\n        for (const obj of reexportedObjects!) {\n          const value = Reflect.get(obj, prop);\n          if (value !== undefined) return value;\n        }\n        return undefined;\n      },\n      ownKeys(target) {\n        const keys = Reflect.ownKeys(target);\n        for (const obj of reexportedObjects!) {\n          for (const key of Reflect.ownKeys(obj)) {\n            if (key !== \"default\" && !keys.includes(key)) keys.push(key);\n          }\n        }\n        return keys;\n      },\n    });\n  }\n}\n\n/**\n * Dynamically exports properties from an object\n */\nfunction dynamicExport(\n  module: Module,\n  exports: Exports,\n  object: Record<string, any>\n) {\n  ensureDynamicExports(module, exports);\n\n  if (typeof object === \"object\" && object !== null) {\n    module[REEXPORTED_OBJECTS]!.push(object);\n  }\n}\n\nfunction exportValue(module: Module, value: any) {\n  module.exports = value;\n}\n\nfunction exportNamespace(module: Module, namespace: any) {\n  module.exports = module.namespaceObject = namespace;\n}\n\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\n  return () => obj[key];\n}\n\n/**\n * @returns prototype of the object\n */\nconst getProto: (obj: any) => any = Object.getPrototypeOf\n  ? (obj) => Object.getPrototypeOf(obj)\n  : (obj) => obj.__proto__;\n\n/** Prototypes that are not expanded for exports */\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)];\n\n/**\n * @param raw\n * @param ns\n * @param allowExportDefault\n *   * `false`: will have the raw module as default export\n *   * `true`: will have the default property as default export\n */\nfunction interopEsm(\n  raw: Exports,\n  ns: EsmNamespaceObject,\n  allowExportDefault?: boolean\n) {\n  const getters: { [s: string]: () => any } = Object.create(null);\n  for (\n    let current = raw;\n    (typeof current === \"object\" || typeof current === \"function\") &&\n    !LEAF_PROTOTYPES.includes(current);\n    current = getProto(current)\n  ) {\n    for (const key of Object.getOwnPropertyNames(current)) {\n      getters[key] = createGetter(raw, key);\n    }\n  }\n\n  // this is not really correct\n  // we should set the `default` getter if the imported module is a `.cjs file`\n  if (!(allowExportDefault && \"default\" in getters)) {\n    getters[\"default\"] = () => raw;\n  }\n\n  esm(ns, getters);\n  return ns;\n}\n\nfunction createNS(raw: Module[\"exports\"]): EsmNamespaceObject {\n  if (typeof raw === \"function\") {\n    return function (this: any, ...args: any[]) {\n      return raw.apply(this, args);\n    };\n  } else {\n    return Object.create(null);\n  }\n}\n\nfunction esmImport(\n  sourceModule: Module,\n  id: ModuleId\n): Exclude<Module[\"namespaceObject\"], undefined> {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\n  if (module.error) throw module.error;\n\n  // any ES module has to have `module.namespaceObject` defined.\n  if (module.namespaceObject) return module.namespaceObject;\n\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\n  const raw = module.exports;\n  return (module.namespaceObject = interopEsm(\n    raw,\n    createNS(raw),\n    raw && (raw as any).__esModule\n  ));\n}\n\n// Add a simple runtime require so that environments without one can still pass\n// `typeof require` CommonJS checks so that exports are correctly registered.\nconst runtimeRequire =\n  // @ts-ignore\n  typeof require === \"function\"\n    // @ts-ignore\n    ? require\n    : function require() {\n        throw new Error(\"Unexpected use of runtime require\");\n      };\n\nfunction commonJsRequire(sourceModule: Module, id: ModuleId): Exports {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule);\n  if (module.error) throw module.error;\n  return module.exports;\n}\n\n/**\n * `require.context` and require/import expression runtime.\n */\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\n  function moduleContext(id: ModuleId): Exports {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].module();\n    }\n\n    const e = new Error(`Cannot find module '${id}'`);\n    (e as any).code = \"MODULE_NOT_FOUND\";\n    throw e;\n  }\n\n  moduleContext.keys = (): ModuleId[] => {\n    return Object.keys(map);\n  };\n\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].id();\n    }\n\n    const e = new Error(`Cannot find module '${id}'`);\n    (e as any).code = \"MODULE_NOT_FOUND\";\n    throw e;\n  };\n\n  moduleContext.import = async (id: ModuleId) => {\n    return await (moduleContext(id) as Promise<Exports>);\n  };\n\n  return moduleContext;\n}\n\n/**\n * Returns the path of a chunk defined by its data.\n */\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\n  return typeof chunkData === \"string\" ? chunkData : chunkData.path;\n}\n\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\n  return (\n    maybePromise != null &&\n    typeof maybePromise === \"object\" &&\n    \"then\" in maybePromise &&\n    typeof maybePromise.then === \"function\"\n  );\n}\n\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\n  return turbopackQueues in obj;\n}\n\nfunction createPromise<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void;\n  let reject: (reason?: any) => void;\n\n  const promise = new Promise<T>((res, rej) => {\n    reject = rej;\n    resolve = res;\n  });\n\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  };\n}\n\n// everything below is adapted from webpack\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\n\nconst turbopackQueues = Symbol(\"turbopack queues\");\nconst turbopackExports = Symbol(\"turbopack exports\");\nconst turbopackError = Symbol(\"turbopack error\");\n\nconst enum QueueStatus {\n  Unknown = -1,\n  Unresolved = 0,\n  Resolved = 1,\n}\n\ntype AsyncQueueFn = (() => void) & { queueCount: number };\ntype AsyncQueue = AsyncQueueFn[] & {\n  status: QueueStatus;\n};\n\nfunction resolveQueue(queue?: AsyncQueue) {\n  if (queue && queue.status !== QueueStatus.Resolved) {\n    queue.status = QueueStatus.Resolved;\n    queue.forEach((fn) => fn.queueCount--);\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()));\n  }\n}\n\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>;\n\ntype AsyncModuleExt = {\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void;\n  [turbopackExports]: Exports;\n  [turbopackError]?: any;\n};\n\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt;\n\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\n  return deps.map((dep): AsyncModuleExt => {\n    if (dep !== null && typeof dep === \"object\") {\n      if (isAsyncModuleExt(dep)) return dep;\n      if (isPromise(dep)) {\n        const queue: AsyncQueue = Object.assign([], {\n          status: QueueStatus.Unresolved,\n        });\n\n        const obj: AsyncModuleExt = {\n          [turbopackExports]: {},\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\n        };\n\n        dep.then(\n          (res) => {\n            obj[turbopackExports] = res;\n            resolveQueue(queue);\n          },\n          (err) => {\n            obj[turbopackError] = err;\n            resolveQueue(queue);\n          }\n        );\n\n        return obj;\n      }\n    }\n\n    return {\n      [turbopackExports]: dep,\n      [turbopackQueues]: () => {},\n    };\n  });\n}\n\nfunction asyncModule(\n  module: Module,\n  body: (\n    handleAsyncDependencies: (\n      deps: Dep[]\n    ) => Exports[] | Promise<() => Exports[]>,\n    asyncResult: (err?: any) => void\n  ) => void,\n  hasAwait: boolean\n) {\n  const queue: AsyncQueue | undefined = hasAwait\n    ? Object.assign([], { status: QueueStatus.Unknown })\n    : undefined;\n\n  const depQueues: Set<AsyncQueue> = new Set();\n\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>();\n\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\n    [turbopackExports]: module.exports,\n    [turbopackQueues]: (fn) => {\n      queue && fn(queue);\n      depQueues.forEach(fn);\n      promise[\"catch\"](() => {});\n    },\n  } satisfies AsyncModuleExt);\n\n  const attributes: PropertyDescriptor = {\n    get(): any {\n      return promise;\n    },\n    set(v: any) {\n      // Calling `esmExport` leads to this.\n      if (v !== promise) {\n        promise[turbopackExports] = v;\n      }\n    },\n  };\n\n  Object.defineProperty(module, \"exports\", attributes);\n  Object.defineProperty(module, \"namespaceObject\", attributes);\n\n  function handleAsyncDependencies(deps: Dep[]) {\n    const currentDeps = wrapDeps(deps);\n\n    const getResult = () =>\n      currentDeps.map((d) => {\n        if (d[turbopackError]) throw d[turbopackError];\n        return d[turbopackExports];\n      });\n\n    const { promise, resolve } = createPromise<() => Exports[]>();\n\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\n      queueCount: 0,\n    });\n\n    function fnQueue(q: AsyncQueue) {\n      if (q !== queue && !depQueues.has(q)) {\n        depQueues.add(q);\n        if (q && q.status === QueueStatus.Unresolved) {\n          fn.queueCount++;\n          q.push(fn);\n        }\n      }\n    }\n\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue));\n\n    return fn.queueCount ? promise : getResult();\n  }\n\n  function asyncResult(err?: any) {\n    if (err) {\n      reject((promise[turbopackError] = err));\n    } else {\n      resolve(promise[turbopackExports]);\n    }\n\n    resolveQueue(queue);\n  }\n\n  body(handleAsyncDependencies, asyncResult);\n\n  if (queue && queue.status === QueueStatus.Unknown) {\n    queue.status = QueueStatus.Unresolved;\n  }\n}\n\n/**\n * A pseudo \"fake\" URL object to resolve to its relative path.\n *\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\n * hydration mismatch.\n *\n * This is based on webpack's existing implementation:\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\n */\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\n  const realUrl = new URL(inputUrl, \"x:/\");\n  const values: Record<string, any> = {};\n  for (const key in realUrl) values[key] = (realUrl as any)[key];\n  values.href = inputUrl;\n  values.pathname = inputUrl.replace(/[?#].*/, \"\");\n  values.origin = values.protocol = \"\";\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl;\n  for (const key in values)\n    Object.defineProperty(this, key, {\n      enumerable: true,\n      configurable: true,\n      value: values[key],\n    });\n};\n\nrelativeURL.prototype = URL.prototype;\n\n/**\n * Utility function to ensure all variants of an enum are handled.\n */\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\n  throw new Error(`Invariant: ${computeMessage(never)}`);\n}\n\n/**\n * A stub function to make `require` available but non-functional in ESM.\n */\nfunction requireStub(_moduleId: ModuleId): never {\n  throw new Error(\"dynamic usage of require is not supported\");\n}\n", "/**\n * This file contains runtime types and functions that are shared between all\n * Turbopack *development* ECMAScript runtimes.\n *\n * It will be appended to the runtime code of each runtime right after the\n * shared runtime utils.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../base/globals.d.ts\" />\n/// <reference path=\"../../../shared/runtime-utils.ts\" />\n\n// Used in WebWorkers to tell the runtime about the chunk base path\ndeclare var TURBOPACK_WORKER_LOCATION: string;\n// Used in WebWorkers to tell the runtime about the current chunk url since it can't be detected via document.currentScript\n// Note it's stored in reversed order to use push and pop\ndeclare var TURBOPACK_NEXT_CHUNK_URLS: ChunkUrl[] | undefined;\n\n// Injected by rust code\ndeclare var CHUNK_BASE_PATH: string;\ndeclare var CHUNK_SUFFIX_PATH: string;\n\n// Provided by build or dev base\ndeclare function instantiateModule(id: ModuleId, source: SourceInfo): Module;\n\ntype RuntimeParams = {\n  otherChunks: ChunkData[];\n  runtimeModuleIds: ModuleId[];\n};\n\ntype ChunkRegistration = [\n  chunkPath: ChunkScript,\n  chunkModules: ModuleFactories,\n  params: RuntimeParams | undefined\n];\n\ntype ChunkList = {\n  script: ChunkListScript;\n  chunks: ChunkData[];\n  source: \"entry\" | \"dynamic\";\n};\n\nenum SourceType {\n  /**\n   * The module was instantiated because it was included in an evaluated chunk's\n   * runtime.\n   */\n  Runtime = 0,\n  /**\n   * The module was instantiated because a parent module imported it.\n   */\n  Parent = 1,\n  /**\n   * The module was instantiated because it was included in a chunk's hot module\n   * update.\n   */\n  Update = 2,\n}\n\ntype SourceInfo =\n  | {\n      type: SourceType.Runtime;\n      chunkPath: ChunkPath;\n    }\n  | {\n      type: SourceType.Parent;\n      parentId: ModuleId;\n    }\n  | {\n      type: SourceType.Update;\n      parents?: ModuleId[];\n    };\n\ninterface RuntimeBackend {\n  registerChunk: (chunkPath: ChunkPath, params?: RuntimeParams) => void;\n  loadChunk: (chunkUrl: ChunkUrl, source: SourceInfo) => Promise<void>;\n}\n\ninterface DevRuntimeBackend {\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>;\n  unloadChunk?: (chunkUrl: ChunkUrl) => void;\n  restart: () => void;\n}\n\nconst moduleFactories: ModuleFactories = Object.create(null);\n/**\n * Module IDs that are instantiated as part of the runtime of a chunk.\n */\nconst runtimeModules: Set<ModuleId> = new Set();\n/**\n * Map from module ID to the chunks that contain this module.\n *\n * In HMR, we need to keep track of which modules are contained in which so\n * chunks. This is so we don't eagerly dispose of a module when it is removed\n * from chunk A, but still exists in chunk B.\n */\nconst moduleChunksMap: Map<ModuleId, Set<ChunkPath>> = new Map();\n/**\n * Map from a chunk path to all modules it contains.\n */\nconst chunkModulesMap: Map<ChunkPath, Set<ModuleId>> = new Map();\n/**\n * Chunk lists that contain a runtime. When these chunk lists receive an update\n * that can't be reconciled with the current state of the page, we need to\n * reload the runtime entirely.\n */\nconst runtimeChunkLists: Set<ChunkListPath> = new Set();\n/**\n * Map from a chunk list to the chunk paths it contains.\n */\nconst chunkListChunksMap: Map<ChunkListPath, Set<ChunkPath>> = new Map();\n/**\n * Map from a chunk path to the chunk lists it belongs to.\n */\nconst chunkChunkListsMap: Map<ChunkPath, Set<ChunkListPath>> = new Map();\n\nconst availableModules: Map<ModuleId, Promise<any> | true> = new Map();\n\nconst availableModuleChunks: Map<ChunkPath, Promise<any> | true> = new Map();\n\nasync function loadChunk(\n  source: SourceInfo,\n  chunkData: ChunkData\n): Promise<any> {\n  if (typeof chunkData === \"string\") {\n    return loadChunkPath(source, chunkData);\n  }\n\n  const includedList = chunkData.included || [];\n  const modulesPromises = includedList.map((included) => {\n    if (moduleFactories[included]) return true;\n    return availableModules.get(included);\n  });\n  if (modulesPromises.length > 0 && modulesPromises.every((p) => p)) {\n    // When all included items are already loaded or loading, we can skip loading ourselves\n    return Promise.all(modulesPromises);\n  }\n\n  const includedModuleChunksList = chunkData.moduleChunks || [];\n  const moduleChunksPromises = includedModuleChunksList\n    .map((included) => {\n      // TODO(alexkirsz) Do we need this check?\n      // if (moduleFactories[included]) return true;\n      return availableModuleChunks.get(included);\n    })\n    .filter((p) => p);\n\n  let promise;\n  if (moduleChunksPromises.length > 0) {\n    // Some module chunks are already loaded or loading.\n\n    if (moduleChunksPromises.length === includedModuleChunksList.length) {\n      // When all included module chunks are already loaded or loading, we can skip loading ourselves\n      return Promise.all(moduleChunksPromises);\n    }\n\n    const moduleChunksToLoad: Set<ChunkPath> = new Set();\n    for (const moduleChunk of includedModuleChunksList) {\n      if (!availableModuleChunks.has(moduleChunk)) {\n        moduleChunksToLoad.add(moduleChunk);\n      }\n    }\n\n    for (const moduleChunkToLoad of moduleChunksToLoad) {\n      const promise = loadChunkPath(source, moduleChunkToLoad);\n\n      availableModuleChunks.set(moduleChunkToLoad, promise);\n\n      moduleChunksPromises.push(promise);\n    }\n\n    promise = Promise.all(moduleChunksPromises);\n  } else {\n    promise = loadChunkPath(source, chunkData.path);\n\n    // Mark all included module chunks as loading if they are not already loaded or loading.\n    for (const includedModuleChunk of includedModuleChunksList) {\n      if (!availableModuleChunks.has(includedModuleChunk)) {\n        availableModuleChunks.set(includedModuleChunk, promise);\n      }\n    }\n  }\n\n  for (const included of includedList) {\n    if (!availableModules.has(included)) {\n      // It might be better to race old and new promises, but it's rare that the new promise will be faster than a request started earlier.\n      // In production it's even more rare, because the chunk optimization tries to deduplicate modules anyway.\n      availableModules.set(included, promise);\n    }\n  }\n\n  return promise;\n}\n\nasync function loadChunkByUrl(source: SourceInfo, chunkUrl: ChunkUrl) {\n  try {\n    await BACKEND.loadChunk(chunkUrl, source);\n  } catch (error) {\n    let loadReason;\n    switch (source.type) {\n      case SourceType.Runtime:\n        loadReason = `as a runtime dependency of chunk ${source.chunkPath}`;\n        break;\n      case SourceType.Parent:\n        loadReason = `from module ${source.parentId}`;\n        break;\n      case SourceType.Update:\n        loadReason = \"from an HMR update\";\n        break;\n      default:\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\n    }\n    throw new Error(\n      `Failed to load chunk ${chunkUrl} ${loadReason}${\n        error ? `: ${error}` : \"\"\n      }`,\n      error\n        ? {\n            cause: error,\n          }\n        : undefined\n    );\n  }\n}\n\nasync function loadChunkPath(\n  source: SourceInfo,\n  chunkPath: ChunkPath\n): Promise<any> {\n  const url = getChunkRelativeUrl(chunkPath);\n  return loadChunkByUrl(source, url);\n}\n\n/**\n * Returns an absolute url to an asset.\n */\nfunction createResolvePathFromModule(\n  resolver: (moduleId: string) => Exports\n): (moduleId: string) => string {\n  return function resolvePathFromModule(moduleId: string): string {\n    const exported = resolver(moduleId);\n    return exported?.default ?? exported;\n  };\n}\n\n/**\n * no-op for browser\n * @param modulePath\n */\nfunction resolveAbsolutePath(modulePath?: string): string {\n  return `/ROOT/${modulePath ?? \"\"}`;\n}\n\nfunction getWorkerBlobURL(chunks: ChunkPath[]): string {\n  let bootstrap = `self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};\nself.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(chunks.reverse().map(getChunkRelativeUrl), null, 2)};\nimportScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`;\n  let blob = new Blob([bootstrap], { type: \"text/javascript\" });\n  return URL.createObjectURL(blob);\n}\n\n/**\n * Adds a module to a chunk.\n */\nfunction addModuleToChunk(moduleId: ModuleId, chunkPath: ChunkPath) {\n  let moduleChunks = moduleChunksMap.get(moduleId);\n  if (!moduleChunks) {\n    moduleChunks = new Set([chunkPath]);\n    moduleChunksMap.set(moduleId, moduleChunks);\n  } else {\n    moduleChunks.add(chunkPath);\n  }\n\n  let chunkModules = chunkModulesMap.get(chunkPath);\n  if (!chunkModules) {\n    chunkModules = new Set([moduleId]);\n    chunkModulesMap.set(chunkPath, chunkModules);\n  } else {\n    chunkModules.add(moduleId);\n  }\n}\n\n/**\n * Returns the first chunk that included a module.\n * This is used by the Node.js backend, hence why it's marked as unused in this\n * file.\n */\nfunction getFirstModuleChunk(moduleId: ModuleId) {\n  const moduleChunkPaths = moduleChunksMap.get(moduleId);\n  if (moduleChunkPaths == null) {\n    return null;\n  }\n\n  return moduleChunkPaths.values().next().value;\n}\n\n/**\n * Instantiates a runtime module.\n */\nfunction instantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): Module {\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\n}\n/**\n * Returns the URL relative to the origin where a chunk can be fetched from.\n */\nfunction getChunkRelativeUrl(chunkPath: ChunkPath | ChunkListPath): ChunkUrl {\n  return `${CHUNK_BASE_PATH}${chunkPath\n    .split(\"/\")\n    .map((p) => encodeURIComponent(p))\n    .join(\"/\")}${CHUNK_SUFFIX_PATH}` as ChunkUrl;\n}\n\n/**\n * Return the ChunkPath from a ChunkScript.\n */\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkScript): ChunkPath;\nfunction getPathFromScript(chunkScript: ChunkListPath | ChunkListScript): ChunkListPath;\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkListPath | ChunkScript | ChunkListScript): ChunkPath | ChunkListPath {\n  if (typeof chunkScript === \"string\") {\n    return chunkScript as ChunkPath | ChunkListPath;\n  }\n  const chunkUrl = typeof TURBOPACK_NEXT_CHUNK_URLS !== \"undefined\"\n    ? TURBOPACK_NEXT_CHUNK_URLS.pop()!\n    : chunkScript.getAttribute(\"src\")!;\n  const src = decodeURIComponent(chunkUrl.replace(/[?#].*$/, \"\"));\n  const path = src.startsWith(CHUNK_BASE_PATH) ? src.slice(CHUNK_BASE_PATH.length) : src;\n  return path as ChunkPath | ChunkListPath;\n}\n\n/**\n * Marks a chunk list as a runtime chunk list. There can be more than one\n * runtime chunk list. For instance, integration tests can have multiple chunk\n * groups loaded at runtime, each with its own chunk list.\n */\nfunction markChunkListAsRuntime(chunkListPath: ChunkListPath) {\n  runtimeChunkLists.add(chunkListPath);\n}\n\nfunction registerChunk([\n  chunkScript,\n  chunkModules,\n  runtimeParams,\n]: ChunkRegistration) {\n  const chunkPath = getPathFromScript(chunkScript);\n  for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\n    if (!moduleFactories[moduleId]) {\n      moduleFactories[moduleId] = moduleFactory;\n    }\n    addModuleToChunk(moduleId, chunkPath);\n  }\n\n  return BACKEND.registerChunk(chunkPath, runtimeParams);\n}\n\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/;\n/**\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\n */\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\n  return regexJsUrl.test(chunkUrlOrPath);\n}\n\nconst regexCssUrl = /\\.css(?:\\?[^#]*)?(?:#.*)?$/;\n/**\n * Checks if a given path/URL ends with .css, optionally followed by ?query or #fragment.\n */\nfunction isCss(chunkUrl: ChunkUrl): boolean {\n  return regexCssUrl.test(chunkUrl);\n}\n", "/// <reference path=\"./runtime-base.ts\" />\n/// <reference path=\"./dummy.ts\" />\n\ndeclare var augmentContext: ((context: unknown) => unknown);\n\nconst moduleCache: ModuleCache<Module> = {};\n\n/**\n * Gets or instantiates a runtime module.\n */\n// @ts-ignore\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction getOrInstantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath,\n): Module {\n  const module = moduleCache[moduleId];\n  if (module) {\n    if (module.error) {\n      throw module.error;\n    }\n    return module;\n  }\n\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath });\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it if it is not cached.\n */\n// Used by the backend\n// @ts-ignore\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst getOrInstantiateModuleFromParent: GetOrInstantiateModuleFromParent<Module> = (\n  id,\n  sourceModule\n) => {\n  const module = moduleCache[id];\n\n  if (module) {\n    return module;\n  }\n\n  return instantiateModule(id, {\n    type: SourceType.Parent,\n    parentId: sourceModule.id,\n  });\n};\n\nfunction instantiateModule(id: ModuleId, source: SourceInfo): Module {\n  const moduleFactory = moduleFactories[id];\n  if (typeof moduleFactory !== \"function\") {\n    // This can happen if modules incorrectly handle HMR disposes/updates,\n    // e.g. when they keep a `setTimeout` around which still executes old code\n    // and contains e.g. a `require(\"something\")` call.\n    let instantiationReason;\n    switch (source.type) {\n      case SourceType.Runtime:\n        instantiationReason = `as a runtime entry of chunk ${source.chunkPath}`;\n        break;\n      case SourceType.Parent:\n        instantiationReason = `because it was required from module ${source.parentId}`;\n        break;\n      case SourceType.Update:\n        instantiationReason = \"because of an HMR update\";\n        break;\n      default:\n        invariant(source, (source) => `Unknown source type: ${source?.type}`);\n    }\n    throw new Error(\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\n    );\n  }\n\n  switch (source.type) {\n    case SourceType.Runtime:\n      runtimeModules.add(id);\n      break;\n    case SourceType.Parent:\n      // No need to add this module as a child of the parent module here, this\n      // has already been taken care of in `getOrInstantiateModuleFromParent`.\n      break;\n    case SourceType.Update:\n      throw new Error('Unexpected')\n    default:\n      invariant(source, (source) => `Unknown source type: ${source?.type}`);\n  }\n\n  const module: Module = {\n    exports: {},\n    error: undefined,\n    loaded: false,\n    id,\n    namespaceObject: undefined,\n  };\n\n  moduleCache[id] = module;\n\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\n  try {\n    const sourceInfo: SourceInfo = { type: SourceType.Parent, parentId: id };\n\n    const r = commonJsRequire.bind(null, module);\n    moduleFactory.call(\n      module.exports,\n      augmentContext({\n        a: asyncModule.bind(null, module),\n        e: module.exports,\n        r: commonJsRequire.bind(null, module),\n        t: runtimeRequire,\n        f: moduleContext,\n        i: esmImport.bind(null, module),\n        s: esmExport.bind(null, module, module.exports),\n        j: dynamicExport.bind(null, module, module.exports),\n        v: exportValue.bind(null, module),\n        n: exportNamespace.bind(null, module),\n        m: module,\n        c: moduleCache,\n        M: moduleFactories,\n        l: loadChunk.bind(null, sourceInfo),\n        L: loadChunkByUrl.bind(null, sourceInfo),\n        w: loadWebAssembly.bind(null, sourceInfo),\n        u: loadWebAssemblyModule.bind(null, sourceInfo),\n        g: globalThis,\n        P: resolveAbsolutePath,\n        U: relativeURL,\n        R: createResolvePathFromModule(r),\n        b: getWorkerBlobURL,\n        d: typeof module.id === \"string\" ? module.id.replace(/(^|\\/)\\/+$/, \"\") : module.id\n      })\n    );\n  } catch (error) {\n    module.error = error as any;\n    throw error;\n  }\n\n  module.loaded = true;\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\n    interopEsm(module.exports, module.namespaceObject);\n  }\n\n  return module;\n}\n\n", "/**\n * This file contains the runtime code specific to the Turbopack development\n * ECMAScript DOM runtime.\n *\n * It will be appended to the base development runtime code.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../../../browser/runtime/base/runtime-base.ts\" />\n/// <reference path=\"../../../shared/runtime-types.d.ts\" />\n\ntype ChunkResolver = {\n  resolved: boolean;\n  resolve: () => void;\n  reject: (error?: Error) => void;\n  promise: Promise<void>;\n};\n\nlet BACKEND: RuntimeBackend;\n\nfunction augmentContext(context: unknown): unknown {\n  return context;\n}\n\nfunction fetchWebAssembly(wasmChunkPath: ChunkPath) {\n  return fetch(getChunkRelativeUrl(wasmChunkPath));\n}\n\nasync function loadWebAssembly(\n  _source: unknown,\n  wasmChunkPath: ChunkPath,\n  importsObj: WebAssembly.Imports\n): Promise<Exports> {\n  const req = fetchWebAssembly(wasmChunkPath);\n\n  const { instance } = await WebAssembly.instantiateStreaming(req, importsObj);\n\n  return instance.exports;\n}\n\nasync function loadWebAssemblyModule(\n  _source: unknown,\n  wasmChunkPath: ChunkPath\n): Promise<WebAssembly.Module> {\n  const req = fetchWebAssembly(wasmChunkPath);\n\n  return await WebAssembly.compileStreaming(req);\n}\n\n/**\n * Maps chunk paths to the corresponding resolver.\n */\nconst chunkResolvers: Map<ChunkUrl, ChunkResolver> = new Map();\n\n(() => {\n  BACKEND = {\n    async registerChunk(chunkPath, params) {\n      const chunkUrl = getChunkRelativeUrl(chunkPath);\n\n      const resolver = getOrCreateResolver(chunkUrl);\n      resolver.resolve();\n\n      if (params == null) {\n        return;\n      }\n\n      for (const otherChunkData of params.otherChunks) {\n        const otherChunkPath = getChunkPath(otherChunkData)\n        const otherChunkUrl = getChunkRelativeUrl(otherChunkPath);\n\n        // Chunk might have started loading, so we want to avoid triggering another load.\n        getOrCreateResolver(otherChunkUrl);\n      }\n\n      // This waits for chunks to be loaded, but also marks included items as available.\n      await Promise.all(\n        params.otherChunks.map((otherChunkData) =>\n          loadChunk({ type: SourceType.Runtime, chunkPath }, otherChunkData)\n        )\n      );\n\n      if (params.runtimeModuleIds.length > 0) {\n        for (const moduleId of params.runtimeModuleIds) {\n          getOrInstantiateRuntimeModule(moduleId, chunkPath);\n        }\n      }\n    },\n\n    /**\n     * Loads the given chunk, and returns a promise that resolves once the chunk\n     * has been loaded.\n    */\n    loadChunk(chunkUrl, source) {\n      return doLoadChunk(chunkUrl, source);\n    },\n  };\n\n  function getOrCreateResolver(chunkUrl: ChunkUrl): ChunkResolver {\n    let resolver = chunkResolvers.get(chunkUrl);\n    if (!resolver) {\n      let resolve: () => void;\n      let reject: (error?: Error) => void;\n      const promise = new Promise<void>((innerResolve, innerReject) => {\n        resolve = innerResolve;\n        reject = innerReject;\n      });\n      resolver = {\n        resolved: false,\n        promise,\n        resolve: () => {\n          resolver!.resolved = true;\n          resolve();\n        },\n        reject: reject!,\n      };\n      chunkResolvers.set(chunkUrl, resolver);\n    }\n    return resolver;\n  }\n\n   /**\n    * Loads the given chunk, and returns a promise that resolves once the chunk\n    * has been loaded.\n    */\n  function doLoadChunk(chunkUrl: ChunkUrl, source: SourceInfo) {\n    const resolver = getOrCreateResolver(chunkUrl);\n    if (resolver.resolved) {\n      return resolver.promise;\n    }\n\n    if (source.type === SourceType.Runtime) {\n      // We don't need to load chunks references from runtime code, as they're already\n      // present in the DOM.\n\n      if (isCss(chunkUrl)) {\n        // CSS chunks do not register themselves, and as such must be marked as\n        // loaded instantly.\n        resolver.resolve();\n      }\n\n      // We need to wait for JS chunks to register themselves within `registerChunk`\n      // before we can start instantiating runtime modules, hence the absence of\n      // `resolver.resolve()` in this branch.\n\n      return resolver.promise;\n    }\n\n    if (typeof importScripts === \"function\") {\n      // We're in a web worker\n      if (isCss(chunkUrl)) {\n        // ignore\n      } else if (isJs(chunkUrl)) {\n        self.TURBOPACK_NEXT_CHUNK_URLS!.push(chunkUrl);\n        importScripts(TURBOPACK_WORKER_LOCATION + chunkUrl);\n      } else {\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl} in worker`);\n      }\n    } else {\n      // TODO(PACK-2140): remove this once all filenames are guaranteed to be escaped.\n      const decodedChunkUrl = decodeURI(chunkUrl);\n\n      if (isCss(chunkUrl)) {\n        const previousLinks = document.querySelectorAll(\n          `link[rel=stylesheet][href=\"${chunkUrl}\"],link[rel=stylesheet][href^=\"${chunkUrl}?\"],link[rel=stylesheet][href=\"${decodedChunkUrl}\"],link[rel=stylesheet][href^=\"${decodedChunkUrl}?\"]`\n        );\n        if (previousLinks.length > 0) {\n          // CSS chunks do not register themselves, and as such must be marked as\n          // loaded instantly.\n          resolver.resolve();\n        } else {\n          const link = document.createElement(\"link\");\n          link.rel = \"stylesheet\";\n          link.href = chunkUrl;\n          link.onerror = () => {\n            resolver.reject();\n          };\n          link.onload = () => {\n            // CSS chunks do not register themselves, and as such must be marked as\n            // loaded instantly.\n            resolver.resolve();\n          };\n          document.body.appendChild(link);\n        }\n      } else if (isJs(chunkUrl)) {\n        const previousScripts = document.querySelectorAll(\n          `script[src=\"${chunkUrl}\"],script[src^=\"${chunkUrl}?\"],script[src=\"${decodedChunkUrl}\"],script[src^=\"${decodedChunkUrl}?\"]`\n        );\n        if (previousScripts.length > 0) {\n          // There is this edge where the script already failed loading, but we\n          // can't detect that. The Promise will never resolve in this case.\n          for (const script of Array.from(previousScripts)) {\n            script.addEventListener(\"error\", () => {\n              resolver.reject();\n            });\n          }\n        } else {\n          const script = document.createElement(\"script\");\n          script.src = chunkUrl;\n          // We'll only mark the chunk as loaded once the script has been executed,\n          // which happens in `registerChunk`. Hence the absence of `resolve()` in\n          // this branch.\n          script.onerror = () => {\n            resolver.reject();\n          };\n          document.body.appendChild(script);\n        }\n      } else {\n        throw new Error(`can't infer type of chunk from URL ${chunkUrl}`);\n      }\n    }\n\n    return resolver.promise;\n  }\n\n})();\n"], "names": [], "mappings": "sPGmBI,iEHAE,EAAqB,OAAO,sBA4B5B,EAAiB,OAAO,SAAS,CAAC,cAAc,CAChD,EAAgC,aAAlB,OAAO,QAA0B,OAAO,WAAW,CAEvE,SAAS,EACP,CAAQ,CACR,CAAiB,CACjB,CAA2C,EAEvC,AAAC,EAAe,IAAI,CAAC,EAAK,IAC5B,OAAO,cAAc,CAAC,EAAK,EAAM,EACrC,CAKA,SAAS,EACP,CAAgB,CAChB,CAAoE,EAIpE,IAAK,IAAM,KAFX,EAAW,EAAS,aAAc,CAAE,OAAO,CAAK,GAC5C,GAAa,EAAW,EAAS,EAAa,CAAE,MAAO,QAAS,GAClD,EAAS,CACzB,IAAM,EAAO,CAAO,CAAC,EAAI,CACrB,MAAM,OAAO,CAAC,GAChB,EAAW,EADY,AACH,EAAK,CACvB,IAAK,CAAI,CAAC,EAAE,CACZ,IAAK,CAAI,CAAC,EAAE,CACZ,YAAY,CACd,GAEA,EAAW,EAAS,EAAK,CAAE,IAAK,EAAM,YAAY,CAAK,EAE3D,CACA,OAAO,IAAI,CAAC,EACd,CAKA,SAAS,EACP,CAAc,CACd,CAAgB,CAChB,CAAkC,EAElC,EAAO,eAAe,CAAG,EAAO,OAAO,CACvC,EAAI,EAAS,EACf,CAsCA,SAAS,EACP,CAAc,CACd,CAAgB,CAChB,CAA2B,SAtCvB,EAAoB,AAwCH,CAxCS,CAAC,EAAmB,IAGhD,EAAoB,CAAM,CAAC,EAAmB,CAAG,EAAE,CACnD,EAAO,OAAO,CAAG,EAAO,eAAe,CAAG,IAAI,MAAM,AAoCzB,EApCkC,CAC3D,IAAI,CAAM,CAAE,CAAI,EACd,GACE,EAAe,IAAI,CAAC,EAAQ,IACnB,YAAT,GACS,cACT,CADA,EAEA,OAAO,QAAQ,GAAG,CAAC,EAAQ,GAE7B,IAAK,IAAM,KAAO,EAAoB,CACpC,IAAM,EAAQ,QAAQ,GAAG,CAAC,EAAK,GAC/B,QAAc,IAAV,EAAqB,OAAO,CAClC,CAEF,EACA,QAAQ,CAAM,EACZ,IAAM,EAAO,QAAQ,OAAO,CAAC,GAC7B,IAAK,IAAM,KAAO,EAChB,IAAK,IAAM,KAAO,GADkB,KACV,OAAO,CAAC,GACpB,EAD0B,UAClC,CAAqB,EAAC,EAAK,QAAQ,CAAC,IAAM,EAAK,IAAI,CAAC,GAG5D,OAAO,CACT,CACF,IAcoB,UAAlB,OAAO,GAAkC,MAAM,CAAjB,GAChC,CAAM,CAAC,EAAmB,CAAE,IAAI,CAAC,EAErC,CAEA,SAAS,EAAY,CAAc,CAAE,CAAU,EAC7C,EAAO,OAAO,CAAG,CACnB,CAEA,SAAS,EAAgB,CAAc,CAAE,CAAc,EACrD,EAAO,OAAO,CAAG,EAAO,eAAe,CAAG,CAC5C,CASA,IAAM,EAA8B,OAAO,cAAc,CACrD,AAAC,GAAQ,OAAO,cAAc,CAAC,GAC/B,AAAC,GAAQ,EAAI,SAAS,CAGpB,EAAkB,CAAC,KAAM,EAAS,CAAC,GAAI,EAAS,EAAE,EAAG,EAAS,GAAU,CAS9E,SAAS,EACP,CAAY,CACZ,CAAsB,CACtB,CAA4B,EAE5B,IAAM,EAAsC,OAAO,MAAM,CAAC,MAC1D,IACE,IAAI,EAAU,EACd,CAAoB,UAAnB,OAAO,GAA2C,YAAnB,OAAO,CAAY,CAAU,EAC7D,CAAC,EAAgB,QAAQ,CAAC,GAC1B,EAAU,EAAS,GAEnB,IAAK,EADL,EACW,KAAO,OAAO,mBAAmB,CAAC,GAC3C,CAAO,CAAC,EAAI,CAlClB,AAkCqB,CADsC,QAjClD,AAAa,CAAiC,CAAE,CAAoB,EAC3E,MAAO,IAAM,CAAG,CAAC,EAAI,AACvB,EAgCkC,EAAK,GAWrC,OALI,AAAE,CAAD,EAAuB,YAAa,GACvC,GAAQ,CADsC,GAAG,AAC1C,GAAW,CAAG,IAAM,CAAA,EAG7B,EAAI,EAAI,GACD,CACT,CAYA,SAAS,EACP,CAAoB,CACpB,CAAY,EAEZ,IAAM,EAAS,EAAiC,EAAI,GACpD,GAAI,EAAO,KAAK,CAAE,MAAM,EAAO,KAAK,CAGpC,GAAI,EAAO,eAAe,CAAE,OAAO,EAAO,eAAe,CAGzD,IAAM,EAAM,EAAO,OAAO,CAC1B,OAAQ,EAAO,eAAe,CAAG,EAC/B,EAtBiB,AAAnB,GAuBE,SAvB6B,AAA3B,OAAO,EACF,SAAqB,GAAG,CAAW,EACxC,OAqBO,AArBA,EAAI,KAAK,CAAC,IAAI,CAAE,EACzB,EAEO,OAAO,MAAM,CAAC,MAmBrB,GAAQ,EAAY,UAAU,CAElC,CAIA,IAAM,EAEe,YAAnB,GADA,IACO,QAEH,CAHS,OAIT,SAAS,EACP,MAAM,AAAI,MAAM,oCAClB,EAEN,SAAS,EAAgB,CAAoB,CAAE,CAAY,EACzD,IAAM,EAAS,EAAiC,EAAI,GACpD,GAAI,EAAO,KAAK,CAAE,MAAM,EAAO,KAAK,CACpC,OAAO,EAAO,OAAO,AACvB,CAKA,SAAS,EAAc,CAAqB,EAC1C,SAAS,EAAc,CAAY,EACjC,GAAI,EAAe,IAAI,CAAC,EAAK,GAC3B,EADgC,KACzB,CAAG,CAAC,EAAG,CAAC,MAAM,GAGvB,IAAM,EAAI,AAAI,MAAM,CAAC,oBAAoB,EAAE,EAAG,CAAC,CAAC,CAEhD,OADC,EAAU,IAAI,CAAG,mBACZ,CACR,CAoBA,OAlBA,EAAc,IAAI,CAAG,IACZ,OAAO,IAAI,CAAC,GAGrB,EAAc,OAAO,CAAG,AAAC,IACvB,GAAI,EAAe,IAAI,CAAC,EAAK,GAC3B,EADgC,KACzB,CAAG,CAAC,EAAG,CAAC,EAAE,GAGnB,IAAM,EAAI,AAAI,MAAM,CAAC,oBAAoB,EAAE,EAAG,CAAC,CAAC,CAEhD,OADC,EAAU,IAAI,CAAG,mBACZ,CACR,EAEA,EAAc,MAAM,CAAG,MAAO,GACrB,MAAO,EAAc,GAGvB,CACT,CAsBA,SAAS,IACP,IAAI,EACA,EAOJ,MAAO,CACL,QANc,IAAI,QAAW,CAAC,EAAK,KACnC,EAAS,EACT,EAAU,CACZ,GAIE,QAAS,EACT,OAAQ,CACV,CACF,CAKA,IAAM,EAAkB,OAAO,oBACzB,EAAmB,OAAO,qBAC1B,EAAiB,OAAO,mBAa9B,SAAS,EAAa,CAAkB,EAClC,GAAqB,GAA2B,CAAvC,EAAM,MAAM,GACvB,EAAM,MAAM,CAAA,EACZ,EAAM,OAAO,CAAC,AAAC,GAAO,EAAG,UAAU,IACnC,EAAM,OAAO,CAAC,AAAC,GAAQ,EAAG,UAAU,GAAK,EAAG,UAAU,GAAK,KAE/D,CAgDA,SAAS,EACP,CAAc,CACd,CAKS,CACT,CAAiB,EAEjB,IAAM,EAAgC,EAClC,OAAO,MAAM,CAAC,EAAE,CAAE,CAAE,MAAM,CAAA,EAAsB,QAChD,EAEE,EAA6B,IAAI,IAEjC,SAAE,CAAO,QAAE,CAAM,CAAE,QAAS,CAAU,CAAE,CAAG,IAE3C,EAA8B,OAAO,MAAM,CAAC,EAAY,CAC5D,CAAC,EAAiB,CAAE,EAAO,OAAO,CAClC,CAAC,EAAgB,CAAE,AAAC,IAClB,GAAS,EAAG,GACZ,EAAU,OAAO,CAAC,GAClB,EAAQ,KAAD,AAAS,CAAC,KAAO,EAC1B,CACF,GAEM,EAAiC,KACrC,IACS,EAET,IAAI,CAAM,EAEJ,IAAM,IACR,CAAO,CAAC,EAAiB,CADR,AACW,EAEhC,CACF,EAEA,OAAO,cAAc,CAAC,EAAQ,UAAW,GACzC,OAAO,cAAc,CAAC,EAAQ,kBAAmB,GA0CjD,EAxCA,GAwCK,MAxCI,AAAwB,CAAW,EAC1C,IAAM,EA9ED,AA8EwB,EA9EnB,GAAG,CAAC,AAAC,IAqHa,AApH5B,EA6EoB,CA7ER,OAAR,GAA+B,UAAf,OAAO,EAAkB,CAC3C,GA1DG,CA0DC,IAAiB,EAAM,OAAO,EAClC,GA3DsB,AARxB,AAAgB,CAmEV,QAlEkB,UAAxB,OAkEgB,AAlET,GACP,UAAU,EACmB,YAA7B,OAAO,EAAa,IAAI,CAgEF,CAClB,IAAM,EAAoB,OAAO,MAAM,CAAC,EAAE,CAAE,CAC1C,MAAM,CAAA,CACR,GAEM,EAAsB,CAC1B,CAAC,EAAiB,CAAE,CAAC,EACrB,CAAC,EAAgB,CAAG,AAAD,GAAqC,EAAG,EAC7D,EAaA,OAXA,EAAI,IAAI,CACN,AAAC,IACC,CAAG,CAAC,EAAiB,CAAG,EACxB,EAAa,EACf,EACA,AAAC,IACC,CAAG,CAAC,EAAe,CAAG,EACtB,EAAa,EACf,GAGK,CACT,CACF,CAEA,MAAO,CACL,CAAC,EAAiB,CAAE,EACpB,CAAC,EAAgB,CAAE,KAAO,CAC5B,CACF,GAgDQ,EAAY,IAChB,EAAY,GAAG,CAAC,AAAC,IACf,GAAI,CAAC,CAAC,EAAe,CAAE,MAAM,CAAC,CAAC,EAAe,CAC9C,OAAO,CAAC,CAAC,EAAiB,AAC5B,GAEI,SAAE,CAAO,SAAE,CAAO,CAAE,CAAG,IAEvB,EAAmB,OAAO,MAAM,CAAC,IAAM,EAAQ,GAAY,CAC/D,WAAY,CACd,GAEA,SAAS,EAAQ,CAAa,EACxB,IAAM,GAAS,CAAC,EAAU,GAAG,CAAC,IAAI,CACpC,EAAU,GAAG,CAAC,GACV,GAAa,GAA6B,CAArC,EAAE,MAAM,GACf,EAAG,UAAU,GACb,EAAE,IAAI,CAAC,IAGb,CAIA,OAFA,EAAY,GAAG,CAAC,AAAC,GAAQ,CAAG,CAAC,EAAgB,CAAC,IAEvC,EAAG,UAAU,CAAG,EAAU,GACnC,EAEA,SAAS,AAAY,CAAS,EACxB,EACF,EAAQ,CADD,AACQ,CAAC,EAAe,CAAG,GAElC,EAAQ,CAAO,CAAC,EAAiB,EAGnC,EAAa,EACf,GAII,GAAqB,IAA0B,CAAtC,EAAM,MAAM,GACvB,EAAM,MAAM,CAAA,CAAA,CAEhB,CAYA,IAAM,EAAc,SAAS,AAAuB,CAAgB,EAClE,IAAM,EAAU,IAAI,IAAI,EAAU,OAC5B,EAA8B,CAAC,EACrC,IAAK,IAAM,KAAO,EAAS,CAAM,CAAC,EAAI,CAAI,CAAe,CAAC,EAAI,CAK9D,IAAK,IAAM,KAJX,EAAO,IAAI,CAAG,EACd,EAAO,QAAQ,CAAG,EAAS,OAAO,CAAC,SAAU,IAC7C,EAAO,MAAM,CAAG,EAAO,QAAQ,CAAG,GAClC,EAAO,QAAQ,CAAG,EAAO,MAAM,CAAG,CAAC,GAAG,IAAsB,EAC1C,EAChB,OAAO,cAAc,CAAC,IAAI,CAAE,EAAK,CAC/B,WAAY,GACZ,cAAc,EACd,MAAO,CAAM,CAAC,EAAI,AACpB,EACJ,EAOA,SAAS,EAAU,CAAY,CAAE,CAAoC,EACnE,MAAM,AAAI,MAAM,CAAC,WAAW,EAAE,EAAe,GAAA,CAAQ,CACvD,CAPA,EAAY,SAAS,CAAG,IAAI,SAAS,CC5crC,IAAK,EAAA,SAAA,CAAA,SAIF,CAAA,CAAA,EAAA,OAAA,CAAA,EAAA,CAAA,UAIA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,CAAA,SAKA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,CAAA,SAbE,GAAA,GAAA,CAAA,GA0CL,IAAM,EAAmC,OAAO,MAAM,CAAC,MAIjD,EAAgC,IAAI,IAQpC,EAAiD,IAAI,IAIrD,EAAiD,IAAI,IAgBrD,EAAuD,IAAI,IAE3D,EAA6D,IAAI,IAEvE,eAAe,EACb,CAAkB,CAClB,CAAoB,MAyBhB,EAvBJ,GAAI,AAAqB,UAAU,OAAxB,EACT,OAAO,EAAc,EAAQ,GAG/B,IAAM,EAAe,EAAU,QAAQ,EAAI,EAAE,CACvC,EAAkB,EAAa,GAAG,CAAE,AAAD,GACvC,EAAI,CAAe,CAAC,EAAS,EAAE,AACxB,EAAiB,GAAG,CAAC,CADU,GAGxC,GAAI,EAAgB,MAAM,CAAG,GAAK,EAAgB,KAAK,CAAC,AAAC,GAAM,GAE7D,CAFiE,MAE1D,QAAQ,GAAG,CAAC,GAGrB,IAAM,EAA2B,EAAU,YAAY,EAAI,EAAE,CACvD,EAAuB,EAC1B,GAAG,CAAC,AAAC,GAGG,EAAsB,GAAG,CAAC,IAElC,MAAM,CAAC,AAAC,GAAM,GAGjB,GAAI,EAAqB,MAAM,CAAG,EAAG,CAGnC,GAAI,EAAqB,MAAM,GAAK,EAAyB,MAAM,CAEjE,CAFmE,MAE5D,QAAQ,GAAG,CAAC,GAGrB,IAAM,EAAqC,IAAI,IAC/C,IAAK,IAAM,KAAe,EACpB,AAAC,EAAsB,GAAG,CAAC,IAC7B,EAAmB,GAAG,CAAC,GAI3B,CAL+C,GADK,AAM/C,IAAM,KAAqB,EAAoB,CAClD,IAAM,EAAU,EAAc,EAAQ,GAEtC,EAAsB,GAAG,CAAC,EAAmB,GAE7C,EAAqB,IAAI,CAAC,EAC5B,CAEA,EAAU,QAAQ,GAAG,CAAC,EACxB,MAIE,CAJK,GAIA,IAAM,KAHX,EAAU,EAAc,EAAQ,EAAU,IAAI,EAGZ,GAC5B,AAAC,EAAsB,GAAG,CAAC,IAC7B,EAAsB,GAAG,CAAC,EAAqB,GAKrD,CAP8D,GAOzD,GANoD,CAM9C,KAAY,EACjB,AAAC,EAAiB,GAAG,CAAC,IAGxB,CAJiC,CAIhB,GAAG,CAAC,CAHc,CAGJ,GAInC,OAAO,CACT,CAEA,eAAe,EAAe,CAAkB,CAAE,CAAkB,EAClE,GAAI,CACF,MAAM,EAAQ,SAAS,CAAC,EAAU,EACpC,CAAE,MAAO,EAAO,CACd,IAAI,EACJ,OAAQ,EAAO,IAAI,EACjB,KAAA,EACE,EAAa,CAAC,iCAAiC,EAAE,EAAO,SAAS,CAAA,CAAE,CACnE,KACF,MAAA,EACE,EAAa,CAAC,YAAY,EAAE,EAAO,QAAQ,CAAA,CAAE,CAC7C,KACF,MAAA,EACE,EAAa,qBACb,KACF,SACE,EAAU,EAAQ,AAAC,GAAW,CAAC,qBAAqB,EAAE,GAAQ,KAAA,CAAM,CACxE,CACA,MAAM,AAAI,MACR,CAAC,qBAAqB,EAAE,EAAS,CAAC,EAAE,EAAA,EAClC,EAAQ,CAAC,EAAE,EAAE,EAAA,CAAO,CAAG,GAAA,CACvB,CACF,EACI,CACE,MAAO,CACT,OACA,EAER,CACF,CAEA,eAAe,EACb,CAAkB,CAClB,CAAoB,EAGpB,OAAO,EAAe,EADV,EAAoB,GAElC,CADgC,AAmBhC,SAAS,EAAoB,CAAmB,EAC9C,MAAO,CAAC,MAAM,EAAE,GAAc,GAAA,CAAI,AACpC,CAEA,SAAS,EAAiB,CAAmB,EAI3C,IAAI,EAAO,IAAI,KAAK,CAHJ,CAAC,iCAAiC,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE;iCACrD,EAAE,KAAK,SAAS,CAAC,EAAO,OAAO,GAAG,GAAG,CAAC,GAAsB,KAAM,GAAG;wGACE,CAAC,CACxE,CAAE,CAAE,KAAM,iBAAkB,GAC3D,OAAO,IAAI,eAAe,CAAC,EAC7B,CAiDA,SAAS,EAAoB,CAAoC,EAC/D,MAAO,GAAG,IAAkB,EACzB,KAAK,CAAC,KACN,GAAG,CAAC,AAAC,GAAM,mBAAmB,IAC9B,IAAI,CAAC,MAA0B,AACpC,CADiB,AA6BjB,SAAS,EAAc,CACrB,EACA,EACA,EACkB,EAClB,IAAM,EA1BR,AA0BoB,SA1BX,AAAkB,CAAsE,EAC/F,GAA2B,UAAvB,AAAiC,OAA1B,EACT,OAAO,EAKT,IAAM,EAAM,mBAAmB,CAHuB,aAArC,OAAO,0BACpB,0BAA0B,GAAG,GAC7B,EAAY,YAAY,CAAC,MAAA,EACW,OAAO,CAAC,UAAW,KAE3D,OAAO,AADM,EAAI,UAAU,CAAC,GAAmB,EAAI,KAAK,CAAC,EAAgB,MAAM,EAAI,CAErF,EAgBsC,GACpC,IAAK,GAAM,CAAC,EAAU,EAAc,GAAI,OAAO,OAAO,CAAC,GACjD,AAAC,CAAe,CAAC,EAAS,EAAE,CAC9B,CAAe,CAAC,EAFkD,AAEzC,CAAG,CAAA,EArFlC,AAuFI,SAvFK,AAAiB,CAAkB,CAAE,CAAoB,EAChE,IAAI,EAAe,EAAgB,GAAG,CAAC,GAClC,EAIH,EAAa,GAAG,CAAC,IAHjB,EADiB,AACF,IAAI,IAAI,CAAC,EAAU,EAClC,EAAgB,GAAG,CAAC,EAAU,IAKhC,IAAI,EAAe,EAAgB,GAAG,CAAC,GAClC,EAIH,EAAa,GAAG,CAAC,IAHjB,EAAe,AADE,IACE,IAAI,CAAC,EAAS,EACjC,EAAgB,GAAG,CAAC,EAAW,GAInC,EAuEqB,EAAU,GAG7B,OAAO,EAAQ,aAAa,CAAC,EAAW,EAC1C,CAEA,IAAM,EAAa,4BAQb,EAAc,6BAIpB,SAAS,EAAM,CAAkB,EAC/B,OAAO,EAAY,IAAI,CAAC,EAC1B,CC/WA,IAAM,EAAmC,CAAC,EA4BpC,EAA6E,CACjF,EACA,KAEA,IAAM,EAAS,CAAW,CAAC,EAAG,QAE9B,AAAI,GAIG,EAAkB,EAAI,CAJjB,AAKV,KAAM,EAAW,MAAM,CACvB,SAAU,EAAa,EAAE,AAC3B,EACF,EAEA,SAAS,EAAkB,CAAY,CAAE,CAAkB,EACzD,IAAM,EAAgB,CAAe,CAAC,EAAG,CACzC,GAA6B,YAAzB,OAAO,EAA8B,CAIvC,IAAI,EACJ,OAAQ,EAAO,IAAI,EACjB,KAAK,EAAW,OAAO,CACrB,EAAsB,CAAC,4BAA4B,EAAE,EAAO,SAAS,CAAA,CAAE,CACvE,KACF,MAAK,EAAW,MAAM,CACpB,EAAsB,CAAC,oCAAoC,EAAE,EAAO,QAAQ,CAAA,CAAE,CAC9E,KACF,MAAK,EAAW,MAAM,CACpB,EAAsB,2BACtB,KACF,SACE,EAAU,EAAQ,AAAC,GAAW,CAAC,qBAAqB,EAAE,GAAQ,KAAA,CAAM,CACxE,CACA,MAAM,AAAI,MACR,CAAC,OAAO,EAAE,EAAG,kBAAkB,EAAE,EAAoB,uFAAuF,CAAC,CAEjJ,CAEA,OAAQ,EAAO,IAAI,EACjB,KAAK,EAAW,OAAO,CACrB,EAAe,GAAG,CAAC,GACnB,KACF,MAAK,EAAW,MAAM,CAGpB,KACF,MAAK,EAAW,MAAM,CACpB,MAAM,AAAI,MAAM,aAClB,SACE,EAAU,EAAQ,AAAC,GAAW,CAAC,qBAAqB,EAAE,GAAQ,KAAA,CAAM,CACxE,CAEA,IAAM,EAAiB,CACrB,QAAS,CAAC,EACV,WAAO,EACP,QAAQ,KACR,EACA,qBAAiB,CACnB,EAEA,CAAW,CAAC,EAAG,CAAG,EAGlB,GAAI,CACF,IAAM,EAAyB,CAAE,KAAM,EAAW,MAAM,CAAE,SAAU,CAAG,EAEjE,EAAI,EAAgB,IAAI,CAAC,KAAM,GACrC,EAAc,IAAI,CAChB,EAAO,OAAO,CACC,CAAf,AACE,EAAG,EAAY,IAAI,CAAC,KAAM,GAC1B,EAAG,EAAO,OAAO,CACjB,EAAG,EAAgB,IAAI,CAAC,KAAM,GAC9B,EAAG,EACH,EAAG,EACH,EAAG,EAAU,IAAI,CAAC,KAAM,GACxB,EAAG,EAAU,IAAI,CAAC,KAAM,EAAQ,EAAO,OAAO,EAC9C,EAAG,EAAc,IAAI,CAAC,KAAM,EAAQ,EAAO,OAAO,EAClD,EAAG,EAAY,IAAI,CAAC,KAAM,GAC1B,EAAG,EAAgB,IAAI,CAAC,KAAM,GAC9B,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAG,EAAU,IAAI,CAAC,KAAM,GACxB,EAAG,EAAe,IAAI,CAAC,KAAM,GAC7B,EAAG,EAAgB,IAAI,CAAC,KAAM,GAC9B,EAAG,EAAsB,IAAI,CAAC,KAAM,GACpC,EAAG,WACH,EAAG,EACH,EAAG,EACH,EDkHC,CClHE,QDkHO,AAAsB,CAAgB,EACpD,IAAM,EAAW,ACnHkB,EDmHT,GAC1B,OAAO,GAAU,SAAW,CAC9B,ECpHM,EAAG,EACH,EAAwB,UAArB,OAAO,EAAO,EAAE,CAAgB,EAAO,EAAE,CAAC,OAAO,CAAC,aAAc,IAAM,EAAO,EAAE,AACpF,EAEJ,CAAE,MAAO,EAAO,CAEd,MADA,EAAO,KAAK,CAAG,EACT,CACR,CAQA,OANA,EAAO,MAAM,EAAG,EACZ,EAAO,eAAe,EAAI,EAAO,OAAO,GAAK,EAAO,eAAe,EAAE,AAEvE,EAAW,EAAO,OAAO,CAAE,EAAO,eAAe,EAG5C,CACT,CClHA,eAAe,EACb,CAAgB,CAChB,CAAwB,CACxB,CAA+B,EAE/B,IAAM,MAAM,IAAiB,IAEvB,UAAE,CAAQ,CAAE,CAAG,MAAM,YAAY,oBAAoB,CAAC,EAAK,GAEjE,OAAO,EAAS,OAAO,AACzB,CAEA,eAAe,EACb,CAAgB,CAChB,CAAwB,EAExB,IAAM,EAnBC,IAmBK,EAnBC,EAmBgB,IAE7B,OAAO,MAAM,CArBoB,WAqBR,gBAAgB,CAAC,EAC5C,CAKA,IAAM,EAA+C,IAAI,IAEzD,CAAC,KA2CC,SAAS,EAAoB,CAAkB,EAC7C,IAAI,EAAW,EAAe,GAAG,CAAC,GAClC,GAAI,CAAC,EAAU,CACb,IAAI,EACA,EAKJ,EAAW,CACT,UAAU,EACV,QANc,IAAI,QAAc,CAAC,EAAc,KAC/C,EAAU,EACV,EAAS,CACX,GAIE,QAAS,KACP,EAAU,QAAQ,EAAG,EACrB,GACF,EACA,OAAQ,CACV,EACA,EAAe,GAAG,CAAC,EAAU,EAC/B,CACA,OAAO,CACT,CA/DA,EAAU,CACR,MAAM,cAAc,CAAS,CAAE,CAAM,EAMnC,GAHiB,AACjB,EAHiB,EAAoB,IAG5B,OAAO,GAEF,EAHuB,IAGjC,AAAgB,GAIpB,IAAK,IAAM,KAAkB,EAAO,WAAW,CAK7C,AAL+C,EAEzB,EHuNA,UAArB,CAAgC,KGpNb,CHoNZ,CGvNkC,GADN,AHwNS,EAAU,IAAI,GG1M7D,GANA,MAAM,QAAQ,GAAG,CACf,EAAO,WAAW,CAAC,GAAG,CAAC,AAAC,GACtB,EAAU,CAAE,KAAM,EAAW,OAAO,WAAE,CAAU,EAAG,KAInD,EAAO,gBAAgB,CAAC,MAAM,CAAG,EACnC,CADsC,GACjC,IAAM,KAAY,EAAO,gBAAgB,CAAE,CDvExD,ACwEU,SDxED,AACP,CAAkB,CAClB,CAAoB,EAEpB,IAAM,EAAS,CAAW,CAAC,EAAS,CACpC,GAAI,EAAQ,CACV,GAAI,EAAO,KAAK,CACd,CADgB,KACV,EAAO,KAAK,CAEpB,MACF,CAEO,AAHE,EAGgB,EAAU,CAAE,KAAM,EAAW,OAAO,WAAE,CAAU,EAC3E,EC2DwC,EAAU,GAG9C,YAMA,CAAU,EAAU,IACX,CA+BX,CAhCoB,AAAQ,QAgCnB,AAAY,CAAkB,CAAE,CAAkB,EACzD,IAAM,EAAW,EAAoB,GACrC,GAAI,EAAS,QAAQ,CACnB,CADqB,MACd,EAAS,OAAO,CAGzB,GAAI,EAAO,IAAI,GAAK,EAAW,OAAO,CAcpC,CAdsC,MAIlC,EAAM,IAGR,EAAS,KAHU,EAGH,GAOX,EAAS,OAAO,CAGzB,GAA6B,YAAzB,AAAqC,OAA9B,cAET,GAAI,EAAM,SAEH,EAFc,EAEV,MAAK,GACd,KAAK,GADoB,sBACK,CAAE,IAAI,CAAC,GACrC,cAAc,0BAA4B,QAE1C,MAAM,AAAI,MAAM,CAAC,mCAAmC,EAAE,EAAS,UAAU,CAAC,MAEvE,CAEL,IAAM,EAAkB,UAAU,GAElC,GAAI,EAAM,GAIR,GAHsB,AAGlB,KAJe,IACY,gBAAgB,CAC7C,CAAC,2BAA2B,EAAE,EAAS,+BAA+B,EAAE,EAAS,+BAA+B,EAAE,EAAgB,+BAA+B,EAAE,EAAgB,GAAG,CAAC,EAEvK,MAAM,CAAG,EAGzB,CAH4B,CAGnB,OAAO,OACX,CACL,IAAM,EAAO,SAAS,aAAa,CAAC,QACpC,EAAK,GAAG,CAAG,aACX,EAAK,IAAI,CAAG,EACZ,EAAK,OAAO,CAAG,KACb,EAAS,MAAM,EACjB,EACA,EAAK,MAAM,CAAG,KAGZ,EAAS,OAAO,EAClB,EACA,SAAS,IAAI,CAAC,WAAW,CAAC,EAC5B,MACK,GFmLJ,CEnLQ,CFmLG,IAAI,CAAC,AEnLH,GAAW,CACzB,IAAM,EAAkB,SAAS,gBAAgB,CAC/C,CAAC,YAAY,EAAE,EAAS,gBAAgB,EAAE,EAAS,gBAAgB,EAAE,EAAgB,gBAAgB,EAAE,EAAgB,GAAG,CAAC,EAE7H,GAAI,EAAgB,MAAM,CAAG,EAG3B,CAH8B,GAGzB,IAAM,KAAU,MAAM,IAAI,CAAC,GAC9B,EAAO,YADyC,IACzB,CAAC,QAAS,KAC/B,EAAS,MAAM,EACjB,OAEG,CACL,IAAM,EAAS,SAAS,aAAa,CAAC,SACtC,GAAO,GAAG,CAAG,EAIb,EAAO,OAAO,CAAG,KACf,EAAS,MAAM,EACjB,EACA,SAAS,IAAI,CAAC,WAAW,CAAC,EAC5B,CACF,MACE,CADK,KACC,AAAI,MAAM,CAAC,mCAAmC,EAAE,EAAA,CAAU,CAEpE,CAEA,OAAO,EAAS,OAAO,CACzB,EAvHuB,EAAU,EAEjC,EAuHF,CAAC", "ignoreList": [0, 1, 2, 3]}