{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAwBsB,8BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAiHsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAkJsB,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAuVsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA4gBsB,2BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA2lBsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAopBsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAmrBsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAktBsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAivBsB,oBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAgxBsB,kBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/consultations.ts"], "sourcesContent": ["'use server'\n\nimport { revalidatePath } from 'next/cache'\nimport { createClient } from '@/lib/supabase/server'\nimport { verifySession } from '@/lib/auth/dal'\nimport { ConsultationCreateSchema, ConsultationUpdateSchema } from '@/lib/validations'\nimport { ApiResponse, Consultation, Database } from '@/lib/types'\nimport { uploadFile, uploadMultipleFiles, calculateTotalFileSize, validateTotalSize } from '@/lib/storage'\n\n// Helper to parse a Json | null array field from Supabase\nfunction parseJsonStringArray(field: unknown): string[] {\n  if (!field) return []\n  if (Array.isArray(field)) return field as string[]\n  if (typeof field === 'string') {\n    try {\n      return JSON.parse(field)\n    } catch {\n      return []\n    }\n  }\n  return []\n}\n\n// New function to handle file uploads and consultation creation\nexport async function createConsultationWithFiles(\n  audioFile: File,\n  imageFiles: File[],\n  additionalAudioFiles: File[],\n  submittedBy: 'doctor' | 'receptionist',\n  doctorNotes?: string,\n  consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology' = 'outpatient',\n  patientName?: string\n): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate total file size\n    const allFiles = [audioFile, ...imageFiles, ...additionalAudioFiles]\n    const totalSizeValidation = validateTotalSize(allFiles)\n    if (!totalSizeValidation.valid) {\n      return { success: false, error: totalSizeValidation.error! }\n    }\n\n    // Generate consultation ID for file organization\n    const consultationId = crypto.randomUUID()\n\n    // Upload primary audio file\n    const audioUploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!audioUploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${audioUploadResult.error}` }\n    }\n\n    // Upload additional audio files\n    let additionalAudioUrls: string[] = []\n    if (additionalAudioFiles.length > 0) {\n      const additionalAudioResult = await uploadMultipleFiles(additionalAudioFiles, session.userId, consultationId, 'audio')\n      if (!additionalAudioResult.success) {\n        return { success: false, error: `Additional audio upload failed: ${additionalAudioResult.errors?.join(', ')}` }\n      }\n      additionalAudioUrls = additionalAudioResult.urls || []\n    }\n\n    // Upload image files\n    let imageUrls: string[] = []\n    if (imageFiles.length > 0) {\n      const imageUploadResult = await uploadMultipleFiles(imageFiles, session.userId, consultationId, 'image')\n      if (!imageUploadResult.success) {\n        return { success: false, error: `Image upload failed: ${imageUploadResult.errors?.join(', ')}` }\n      }\n      imageUrls = imageUploadResult.urls || []\n    }\n\n    const totalFileSize = calculateTotalFileSize(allFiles)\n\n    const supabase = await createClient()\n\n    // Insert consultation with pre-generated ID\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        id: consultationId,\n        doctor_id: session.userId,\n        primary_audio_url: audioUploadResult.url!,\n        additional_audio_urls: additionalAudioUrls,\n        image_urls: imageUrls,\n        submitted_by: submittedBy,\n        status: 'pending',\n        total_file_size_bytes: totalFileSize,\n        doctor_notes: doctorNotes || null,\n        consultation_type: consultationType,\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation with files error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveStreamingSummary(consultationId: string, summary: string): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with generated summary\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        ai_generated_note: summary,\n        status: 'generated',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId) // Ensure user can only update their own consultations\n\n    if (updateError) {\n      console.error('Update error:', updateError)\n      return { success: false, error: 'Failed to save generated summary' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: summary }\n  } catch (error) {\n    console.error('Save streaming summary error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function createConsultation(formData: FormData): Promise<ApiResponse<Consultation>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Extract URLs from form data\n    const primaryAudioUrl = formData.get('primary_audio_url') as string\n    const additionalAudioUrlsString = formData.get('additional_audio_urls') as string\n    const imageUrlsString = formData.get('image_urls') as string\n    const totalFileSizeString = formData.get('total_file_size_bytes') as string\n\n    let additionalAudioUrls: string[] = []\n    let imageUrls: string[] = []\n    let totalFileSize = 0\n\n    // Parse additional audio URLs\n    if (additionalAudioUrlsString) {\n      try {\n        additionalAudioUrls = JSON.parse(additionalAudioUrlsString)\n      } catch (error) {\n        console.error('Error parsing additional_audio_urls:', error)\n      }\n    }\n\n    // Parse image URLs\n    if (imageUrlsString) {\n      try {\n        imageUrls = JSON.parse(imageUrlsString)\n      } catch (error) {\n        console.error('Error parsing image_urls:', error)\n      }\n    }\n\n    // Parse total file size\n    if (totalFileSizeString) {\n      totalFileSize = parseInt(totalFileSizeString, 10) || 0\n    }\n\n    // Validate form data\n    const validatedFields = ConsultationCreateSchema.safeParse({\n      primary_audio_url: primaryAudioUrl,\n      additional_audio_urls: additionalAudioUrls,\n      image_urls: imageUrls,\n      submitted_by: formData.get('submitted_by'),\n      total_file_size_bytes: totalFileSize,\n    })\n\n    if (!validatedFields.success) {\n      return {\n        success: false,\n        error: 'Invalid form data: ' + JSON.stringify(validatedFields.error.flatten().fieldErrors)\n      }\n    }\n\n    const { primary_audio_url, additional_audio_urls, image_urls, submitted_by, total_file_size_bytes } = validatedFields.data\n\n    const supabase = await createClient()\n\n    // Insert consultation\n    const { data: consultation, error } = await supabase\n      .from('consultations')\n      .insert({\n        doctor_id: session.userId,\n        primary_audio_url,\n        additional_audio_urls: additional_audio_urls || [],\n        image_urls: image_urls || [],\n        submitted_by,\n        status: 'pending',\n        total_file_size_bytes: total_file_size_bytes || 0,\n      } as Database['public']['Tables']['consultations']['Insert'])\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to create consultation' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/mobile')\n\n    return { success: true, data: consultation as Consultation }\n  } catch (error) {\n    console.error('Create consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// OPTIMIZED: Paginated consultations with FTS search support\nexport async function getConsultations({\n  page = 1,\n  pageSize = 15, // Start with small initial load\n  status,\n  searchTerm\n}: {\n  page?: number\n  pageSize?: number\n  status?: 'pending' | 'generated' | 'approved'\n  searchTerm?: string\n} = {}): Promise<ApiResponse<{\n  consultations: Consultation[]\n  hasMore: boolean\n  totalCount?: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    const from = (page - 1) * pageSize\n    const to = from + pageSize - 1\n\n    let query = supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, total_file_size_bytes, file_retention_until, created_at, updated_at, consultation_type, doctor_notes, additional_notes', { count: 'exact' })\n      .eq('doctor_id', session.userId)\n      .order('created_at', { ascending: false })\n      .range(from, to)\n\n    // Apply status filter\n    if (status) {\n      query = query.eq('status', status)\n    }\n\n    // Apply FTS search if provided\n    if (searchTerm && searchTerm.trim()) {\n      // Convert search term to FTS query format (space-separated words joined with &)\n      const ftsQuery = searchTerm.trim().split(/\\s+/).join(' & ')\n      query = query.textSearch('fts', ftsQuery)\n    }\n\n    const { data: consultations, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n\n      // Handle \"Requested range not satisfiable\" error gracefully\n      if (error.code === 'PGRST103') {\n        return {\n          success: true,\n          data: {\n            consultations: [],\n            hasMore: false,\n            totalCount: count || 0\n          }\n        }\n      }\n\n      return { success: false, error: 'Failed to fetch consultations' }\n    }\n\n    // Map to Consultation[]\n    const typedConsultations = (consultations || []).map((row: Database['public']['Tables']['consultations']['Row']) => {\n      return {\n        id: row.id,\n        doctor_id: row.doctor_id!,\n        submitted_by: row.submitted_by as 'doctor' | 'receptionist',\n        primary_audio_url: row.primary_audio_url,\n        additional_audio_urls: parseJsonStringArray(row.additional_audio_urls),\n        image_urls: parseJsonStringArray(row.image_urls),\n        ai_generated_note: row.ai_generated_note ?? undefined,\n        edited_note: row.edited_note ?? undefined,\n        status: row.status as 'pending' | 'generated' | 'approved',\n        patient_number: row.patient_number ?? undefined,\n        patient_name: row.patient_name ?? undefined,\n        total_file_size_bytes: row.total_file_size_bytes ?? 0,\n        file_retention_until: row.file_retention_until,\n        created_at: row.created_at,\n        updated_at: row.updated_at,\n        consultation_type: row.consultation_type ?? 'outpatient',\n        doctor_notes: row.doctor_notes ?? undefined,\n        additional_notes: row.additional_notes ?? undefined,\n      } as Consultation\n    })\n\n    const hasMore = (count || 0) > to + 1\n\n    return {\n      success: true,\n      data: {\n        consultations: typedConsultations,\n        hasMore,\n        totalCount: count || 0\n      }\n    }\n  } catch (error) {\n    console.error('Get consultations error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n\n\nexport async function generateSummaryStream(\n  consultationId: string,\n  additionalImages?: string[],\n  onChunk?: (chunk: string) => void,\n  onComplete?: (summary: string) => void,\n  onError?: (error: string) => void,\n  consultationType?: string,\n  doctorNotes?: string,\n  additionalNotes?: string\n): Promise<ApiResponse<string>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      onError?.('Unauthorized')\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get consultation and doctor data\n    const { data: consultation, error: consultationError } = await supabase\n      .from('consultations')\n      .select('id, doctor_id, submitted_by, primary_audio_url, additional_audio_urls, image_urls, ai_generated_note, edited_note, status, patient_number, patient_name, created_at, updated_at, consultation_type, doctor_notes, doctors(name)')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (consultationError || !consultation) {\n      onError?.('Consultation not found')\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Strictly type consultation\n    type ConsultationRow = Database['public']['Tables']['consultations']['Row'] & { doctors: { name: string } }\n    const typedConsultation = consultation as ConsultationRow\n\n    // Parse additional_audio_urls and image_urls from JSON if needed\n    const additionalAudioUrls = parseJsonStringArray(typedConsultation.additional_audio_urls)\n    const imageUrls = parseJsonStringArray(typedConsultation.image_urls)\n    const allImageUrls = [...imageUrls, ...(additionalImages || [])]\n\n    // Use the SAME production-grade streaming approach as the Create buttons\n    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/generate-summary-stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        primary_audio_url: typedConsultation.primary_audio_url,\n        additional_audio_urls: Array.isArray(additionalAudioUrls) ? additionalAudioUrls : [],\n        image_urls: Array.isArray(allImageUrls) ? allImageUrls : [],\n        submitted_by: typedConsultation.submitted_by || 'doctor',\n        consultation_type: consultationType || typedConsultation.consultation_type || 'outpatient',\n        doctor_notes: doctorNotes || typedConsultation.doctor_notes || undefined,\n        additional_notes: additionalNotes || undefined,\n        patient_name: typedConsultation.patient_name || undefined,\n        doctor_name: typedConsultation.doctors.name || undefined,\n        created_at: typedConsultation.created_at || undefined,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorMsg = `HTTP ${response.status}: ${response.statusText}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n    const reader = response.body?.getReader()\n    if (!reader) {\n      onError?.('No reader available')\n      return { success: false, error: 'No reader available' }\n    }\n\n    const decoder = new TextDecoder()\n    let fullSummary = ''\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) break\n\n        const chunk = decoder.decode(value, { stream: true })\n        const lines = chunk.split('\\n')\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.type === 'chunk' && data.text) {\n                // Format text with proper line breaks (same as production)\n                const formattedText = data.text.replace(/\\\\n/g, '\\n').replace(/\\n\\n+/g, '\\n\\n')\n                fullSummary += formattedText\n                // Call onChunk with the new text chunk\n                onChunk?.(formattedText)\n              }\n            } catch (_e) {\n              // Ignore parse errors (same as production)\n            }\n          }\n        }\n      }\n\n      // Call onComplete with final summary\n      onComplete?.(fullSummary)\n\n      // Save the streamed summary using the same approach as production\n      try {\n        const saveResult = await saveStreamingSummary(consultationId, fullSummary)\n        if (!saveResult.success) {\n          console.error('Failed to save streaming summary:', saveResult.error)\n          onError?.(`Summary generated but failed to save: ${saveResult.error}`)\n          return { success: false, error: `Summary generated but failed to save: ${saveResult.error}` }\n        }\n      } catch (saveError) {\n        console.error('Error saving streaming summary:', saveError)\n        onError?.('Summary generated but failed to save. Please try regenerate.')\n        return { success: false, error: 'Summary generated but failed to save. Please try regenerate.' }\n      }\n\n      revalidatePath('/dashboard')\n      return { success: true, data: fullSummary }\n\n    } catch (error) {\n      console.error('❌ Generate error:', error)\n      const errorMsg = `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`\n      onError?.(errorMsg)\n      return { success: false, error: errorMsg }\n    }\n\n  } catch (error) {\n    console.error('Generate streaming summary error:', error)\n    onError?.('An unexpected error occurred')\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function approveConsultation(\n  consultationId: string,\n  editedNote: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    // Validate edited note\n    const validatedFields = ConsultationUpdateSchema.safeParse({\n      edited_note: editedNote,\n    })\n\n    if (!validatedFields.success) {\n      return { success: false, error: 'Invalid note content' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n        status: 'approved',\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update error:', error)\n      return { success: false, error: 'Failed to approve consultation' }\n    }\n\n    revalidatePath('/dashboard')\n\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Approve consultation error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationImages(consultationId: string, imageUrls: string[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with new image URLs\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: imageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation images error:', error)\n      return { success: false, error: 'Failed to update consultation images' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalAudio(consultationId: string, audioFile: File): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add audio to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload the additional audio file\n    const uploadResult = await uploadFile(audioFile, session.userId, consultationId, 'audio')\n    if (!uploadResult.success) {\n      return { success: false, error: `Audio upload failed: ${uploadResult.error}` }\n    }\n\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    additionalAudioUrls.push(uploadResult.url!)\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ additional_audio_urls: additionalAudioUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional audio' }\n    }\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function addAdditionalImages(consultationId: string, imageFiles: File[]): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n    const supabase = await createClient()\n\n    // Fetch the consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n    if (consultation.status === 'approved') {\n      return { success: false, error: `Cannot add images to approved consultations. Current status: ${consultation.status}` }\n    }\n\n    // Upload all image files\n    const uploadResults = await Promise.all(\n      imageFiles.map(file => uploadFile(file, session.userId, consultationId, 'image'))\n    )\n\n    // Check if all uploads succeeded\n    const failedUploads = uploadResults.filter(result => !result.success)\n    if (failedUploads.length > 0) {\n      const errors = failedUploads.map(f => f.error).join(', ')\n      return { success: false, error: `Image upload failed: ${errors}` }\n    }\n\n    // Get existing image URLs and add new ones\n    const existingImageUrls = parseJsonStringArray(consultation.image_urls)\n    const newImageUrls = uploadResults.map(result => result.url!).filter(url => url)\n    const allImageUrls = [...existingImageUrls, ...newImageUrls]\n\n    // Update the consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({ image_urls: allImageUrls } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n    if (updateError) {\n      return { success: false, error: 'Failed to add additional images' }\n    }\n\n    revalidatePath('/dashboard')\n    revalidatePath('/record')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Add additional images error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateConsultationType(consultationId: string, consultationType: 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology'): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation type\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        consultation_type: consultationType,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update consultation type error:', error)\n      return { success: false, error: 'Failed to update consultation type' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update consultation type error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function saveEditedNote(consultationId: string, editedNote: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with edited note\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: editedNote,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Save edited note error:', error)\n      return { success: false, error: 'Failed to save edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Save edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updateAdditionalNotes(consultationId: string, additionalNotes: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with additional notes\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        additional_notes: additionalNotes || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update additional notes error:', error)\n      return { success: false, error: 'Failed to update additional notes' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update additional notes error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function updatePatientName(consultationId: string, patientName: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Update consultation with patient name\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        patient_name: patientName || null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Update patient name error:', error)\n      return { success: false, error: 'Failed to update patient name' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Update patient name error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\nexport async function clearEditedNote(consultationId: string): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Clear edited note from consultation\n    const { error } = await supabase\n      .from('consultations')\n      .update({\n        edited_note: null,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (error) {\n      console.error('Clear edited note error:', error)\n      return { success: false, error: 'Failed to clear edited note' }\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Clear edited note error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete additional audio from consultation\nexport async function deleteAdditionalAudio(\n  consultationId: string,\n  audioUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('additional_audio_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the audio URL\n    const additionalAudioUrls = parseJsonStringArray(consultation.additional_audio_urls)\n    const updatedAudioUrls = additionalAudioUrls.filter(url => url !== audioUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        additional_audio_urls: updatedAudioUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete additional audio error:', updateError)\n      return { success: false, error: 'Failed to delete additional audio' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = audioUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'audio')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete additional audio error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Delete image from consultation\nexport async function deleteConsultationImage(\n  consultationId: string,\n  imageUrl: string\n): Promise<ApiResponse<boolean>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n\n    // Get current consultation\n    const { data: consultation, error: fetchError } = await supabase\n      .from('consultations')\n      .select('image_urls, status')\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n      .single()\n\n    if (fetchError || !consultation) {\n      return { success: false, error: 'Consultation not found' }\n    }\n\n    // Check if consultation can be modified\n    if (consultation.status === 'approved') {\n      return { success: false, error: 'Cannot delete files from approved consultations' }\n    }\n\n    // Parse and filter out the image URL\n    const imageUrls = parseJsonStringArray(consultation.image_urls)\n    const updatedImageUrls = imageUrls.filter(url => url !== imageUrl)\n\n    // Update consultation\n    const { error: updateError } = await supabase\n      .from('consultations')\n      .update({\n        image_urls: updatedImageUrls,\n      } as Database['public']['Tables']['consultations']['Update'])\n      .eq('id', consultationId)\n      .eq('doctor_id', session.userId)\n\n    if (updateError) {\n      console.error('Delete consultation image error:', updateError)\n      return { success: false, error: 'Failed to delete image' }\n    }\n\n    // Delete file from storage\n    try {\n      const { deleteFile } = await import('@/lib/storage')\n      const filePath = imageUrl.split('/').pop() || ''\n      await deleteFile(filePath, 'image')\n    } catch (storageError) {\n      console.error('Storage deletion error:', storageError)\n      // Don't fail the operation if storage deletion fails\n    }\n\n    revalidatePath('/dashboard')\n    return { success: true, data: true }\n  } catch (error) {\n    console.error('Delete consultation image error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n\n// Get consultation statistics using optimized database function\nexport async function getConsultationStats(): Promise<ApiResponse<{\n  total_consultations: number\n  pending_consultations: number\n  approved_consultations: number\n  today_consultations: number\n}>> {\n  try {\n    const session = await verifySession()\n    if (!session) {\n      return { success: false, error: 'Unauthorized' }\n    }\n\n    const supabase = await createClient()\n    \n    const { data, error } = await supabase.rpc('get_consultation_stats', {\n      doctor_uuid: session.userId\n    })\n\n    if (error) {\n      console.error('Database error:', error)\n      return { success: false, error: 'Failed to fetch consultation stats' }\n    }\n\n    const stats = data?.[0] || {\n      total_consultations: 0,\n      pending_consultations: 0,\n      approved_consultations: 0,\n      today_consultations: 0\n    }\n\n    return { \n      success: true, \n      data: {\n        total_consultations: Number(stats.total_consultations),\n        pending_consultations: Number(stats.pending_consultations),\n        approved_consultations: Number(stats.approved_consultations),\n        today_consultations: Number(stats.today_consultations)\n      }\n    }\n  } catch (error) {\n    console.error('Get consultation stats error:', error)\n    return { success: false, error: 'An unexpected error occurred' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAo7BsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}]}