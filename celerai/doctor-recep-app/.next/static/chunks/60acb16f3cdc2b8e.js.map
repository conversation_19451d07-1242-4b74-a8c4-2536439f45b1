{"version": 3, "sources": ["turbopack:///[project]/node_modules/@sentry/core/src/utils/envelope.ts", "turbopack:///[project]/node_modules/@sentry/core/src/envelope.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/sentryNonRecordingSpan.ts", "turbopack:///[project]/node_modules/@sentry/core/src/utils/handleCallbackErrors.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/logSpans.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/sampling.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/measurement.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/sentrySpan.ts", "turbopack:///[project]/node_modules/@sentry/core/src/tracing/trace.ts"], "sourcesContent": ["import { getSentryCarrier } from '../carrier';\nimport type { Attachment } from '../types-hoist/attachment';\nimport type { DataCategory } from '../types-hoist/datacategory';\nimport type { DsnComponents } from '../types-hoist/dsn';\nimport type {\n  AttachmentItem,\n  BaseEnvelopeHeaders,\n  BaseEnvelopeItemHeaders,\n  Envelope,\n  EnvelopeItemType,\n  EventEnvelopeHeaders,\n  SpanItem,\n} from '../types-hoist/envelope';\nimport type { Event } from '../types-hoist/event';\nimport type { SdkInfo } from '../types-hoist/sdkinfo';\nimport type { SdkMetadata } from '../types-hoist/sdkmetadata';\nimport type { SpanJSON } from '../types-hoist/span';\nimport { dsnToString } from './dsn';\nimport { normalize } from './normalize';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function createEnvelope<E extends Envelope>(headers: E[0], items: E[1] = []): E {\n  return [headers, items] as E;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function addItemToEnvelope<E extends Envelope>(envelope: E, newItem: E[1][number]): E {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] as unknown as E;\n}\n\n/**\n * Convenience function to loop through the items and item types of an envelope.\n * (This function was mostly created because working with envelope types is painful at the moment)\n *\n * If the callback returns true, the rest of the items will be skipped.\n */\nexport function forEachEnvelopeItem<E extends Envelope>(\n  envelope: Envelope,\n  callback: (envelopeItem: E[1][number], envelopeItemType: E[1][number][0]['type']) => boolean | void,\n): boolean {\n  const envelopeItems = envelope[1];\n\n  for (const envelopeItem of envelopeItems) {\n    const envelopeItemType = envelopeItem[0].type;\n    const result = callback(envelopeItem, envelopeItemType);\n\n    if (result) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Returns true if the envelope contains any of the given envelope item types\n */\nexport function envelopeContainsItemType(envelope: Envelope, types: EnvelopeItemType[]): boolean {\n  return forEachEnvelopeItem(envelope, (_, type) => types.includes(type));\n}\n\n/**\n * Encode a string to UTF8 array.\n */\nfunction encodeUTF8(input: string): Uint8Array {\n  const carrier = getSentryCarrier(GLOBAL_OBJ);\n  return carrier.encodePolyfill ? carrier.encodePolyfill(input) : new TextEncoder().encode(input);\n}\n\n/**\n * Decode a UTF8 array to string.\n */\nfunction decodeUTF8(input: Uint8Array): string {\n  const carrier = getSentryCarrier(GLOBAL_OBJ);\n  return carrier.decodePolyfill ? carrier.decodePolyfill(input) : new TextDecoder().decode(input);\n}\n\n/**\n * Serializes an envelope.\n */\nexport function serializeEnvelope(envelope: Envelope): string | Uint8Array {\n  const [envHeaders, items] = envelope;\n  // Initially we construct our envelope as a string and only convert to binary chunks if we encounter binary data\n  let parts: string | Uint8Array[] = JSON.stringify(envHeaders);\n\n  function append(next: string | Uint8Array): void {\n    if (typeof parts === 'string') {\n      parts = typeof next === 'string' ? parts + next : [encodeUTF8(parts), next];\n    } else {\n      parts.push(typeof next === 'string' ? encodeUTF8(next) : next);\n    }\n  }\n\n  for (const item of items) {\n    const [itemHeaders, payload] = item;\n\n    append(`\\n${JSON.stringify(itemHeaders)}\\n`);\n\n    if (typeof payload === 'string' || payload instanceof Uint8Array) {\n      append(payload);\n    } else {\n      let stringifiedPayload: string;\n      try {\n        stringifiedPayload = JSON.stringify(payload);\n      } catch (e) {\n        // In case, despite all our efforts to keep `payload` circular-dependency-free, `JSON.stringify()` still\n        // fails, we try again after normalizing it again with infinite normalization depth. This of course has a\n        // performance impact but in this case a performance hit is better than throwing.\n        stringifiedPayload = JSON.stringify(normalize(payload));\n      }\n      append(stringifiedPayload);\n    }\n  }\n\n  return typeof parts === 'string' ? parts : concatBuffers(parts);\n}\n\nfunction concatBuffers(buffers: Uint8Array[]): Uint8Array {\n  const totalLength = buffers.reduce((acc, buf) => acc + buf.length, 0);\n\n  const merged = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const buffer of buffers) {\n    merged.set(buffer, offset);\n    offset += buffer.length;\n  }\n\n  return merged;\n}\n\n/**\n * Parses an envelope\n */\nexport function parseEnvelope(env: string | Uint8Array): Envelope {\n  let buffer = typeof env === 'string' ? encodeUTF8(env) : env;\n\n  function readBinary(length: number): Uint8Array {\n    const bin = buffer.subarray(0, length);\n    // Replace the buffer with the remaining data excluding trailing newline\n    buffer = buffer.subarray(length + 1);\n    return bin;\n  }\n\n  function readJson<T>(): T {\n    let i = buffer.indexOf(0xa);\n    // If we couldn't find a newline, we must have found the end of the buffer\n    if (i < 0) {\n      i = buffer.length;\n    }\n\n    return JSON.parse(decodeUTF8(readBinary(i))) as T;\n  }\n\n  const envelopeHeader = readJson<BaseEnvelopeHeaders>();\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const items: [any, any][] = [];\n\n  while (buffer.length) {\n    const itemHeader = readJson<BaseEnvelopeItemHeaders>();\n    const binaryLength = typeof itemHeader.length === 'number' ? itemHeader.length : undefined;\n\n    items.push([itemHeader, binaryLength ? readBinary(binaryLength) : readJson()]);\n  }\n\n  return [envelopeHeader, items];\n}\n\n/**\n * Creates envelope item for a single span\n */\nexport function createSpanEnvelopeItem(spanJson: Partial<SpanJSON>): SpanItem {\n  const spanHeaders: SpanItem[0] = {\n    type: 'span',\n  };\n\n  return [spanHeaders, spanJson];\n}\n\n/**\n * Creates attachment envelope items\n */\nexport function createAttachmentEnvelopeItem(attachment: Attachment): AttachmentItem {\n  const buffer = typeof attachment.data === 'string' ? encodeUTF8(attachment.data) : attachment.data;\n\n  return [\n    {\n      type: 'attachment',\n      length: buffer.length,\n      filename: attachment.filename,\n      content_type: attachment.contentType,\n      attachment_type: attachment.attachmentType,\n    },\n    buffer,\n  ];\n}\n\nconst ITEM_TYPE_TO_DATA_CATEGORY_MAP: Record<EnvelopeItemType, DataCategory> = {\n  session: 'session',\n  sessions: 'session',\n  attachment: 'attachment',\n  transaction: 'transaction',\n  event: 'error',\n  client_report: 'internal',\n  user_report: 'default',\n  profile: 'profile',\n  profile_chunk: 'profile',\n  replay_event: 'replay',\n  replay_recording: 'replay',\n  check_in: 'monitor',\n  feedback: 'feedback',\n  span: 'span',\n  raw_security: 'security',\n  log: 'log_item',\n};\n\n/**\n * Maps the type of an envelope item to a data category.\n */\nexport function envelopeItemTypeToDataCategory(type: EnvelopeItemType): DataCategory {\n  return ITEM_TYPE_TO_DATA_CATEGORY_MAP[type];\n}\n\n/** Extracts the minimal SDK info from the metadata or an events */\nexport function getSdkMetadataForEnvelopeHeader(metadataOrEvent?: SdkMetadata | Event): SdkInfo | undefined {\n  if (!metadataOrEvent?.sdk) {\n    return;\n  }\n  const { name, version } = metadataOrEvent.sdk;\n  return { name, version };\n}\n\n/**\n * Creates event envelope headers, based on event, sdk info and tunnel\n * Note: This function was extracted from the core package to make it available in Replay\n */\nexport function createEventEnvelopeHeaders(\n  event: Event,\n  sdkInfo: SdkInfo | undefined,\n  tunnel: string | undefined,\n  dsn?: DsnComponents,\n): EventEnvelopeHeaders {\n  const dynamicSamplingContext = event.sdkProcessingMetadata?.dynamicSamplingContext;\n  return {\n    event_id: event.event_id as string,\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n    ...(dynamicSamplingContext && {\n      trace: dynamicSamplingContext,\n    }),\n  };\n}\n", "import type { Client } from './client';\nimport { getDynamicSamplingContextFromSpan } from './tracing/dynamicSamplingContext';\nimport type { SentrySpan } from './tracing/sentrySpan';\nimport type { LegacyCSPReport } from './types-hoist/csp';\nimport type { DsnComponents } from './types-hoist/dsn';\nimport type {\n  DynamicSamplingContext,\n  EventEnvelope,\n  EventItem,\n  RawSecurityEnvelope,\n  RawSecurityItem,\n  SessionEnvelope,\n  SessionItem,\n  SpanEnvelope,\n  SpanItem,\n} from './types-hoist/envelope';\nimport type { Event } from './types-hoist/event';\nimport type { SdkInfo } from './types-hoist/sdkinfo';\nimport type { SdkMetadata } from './types-hoist/sdkmetadata';\nimport type { Session, SessionAggregates } from './types-hoist/session';\nimport { dsnToString } from './utils/dsn';\nimport {\n  createEnvelope,\n  createEventEnvelopeHeaders,\n  createSpanEnvelopeItem,\n  getSdkMetadataForEnvelopeHeader,\n} from './utils/envelope';\nimport { uuid4 } from './utils/misc';\nimport { showSpanDropWarning, spanToJSON } from './utils/spanUtils';\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event: Event, sdkInfo?: SdkInfo): Event {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nexport function createSessionEnvelope(\n  session: Session | SessionAggregates,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): SessionEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem: SessionItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session.toJSON()];\n\n  return createEnvelope<SessionEnvelope>(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nexport function createEventEnvelope(\n  event: Event,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): EventEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n\n  /*\n    Note: Due to TS, event.type may be `replay_event`, theoretically.\n    In practice, we never call `createEventEnvelope` with `replay_event` type,\n    and we'd have to adjust a looot of types to make this work properly.\n    We want to avoid casting this around, as that could lead to bugs (e.g. when we add another type)\n    So the safe choice is to really guard against the replay_event type here.\n  */\n  const eventType = event.type && event.type !== 'replay_event' ? event.type : 'event';\n\n  enhanceEventWithSdkInfo(event, metadata?.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem: EventItem = [{ type: eventType }, event];\n  return createEnvelope<EventEnvelope>(envelopeHeaders, [eventItem]);\n}\n\n/**\n * Create envelope from Span item.\n *\n * Takes an optional client and runs spans through `beforeSendSpan` if available.\n */\nexport function createSpanEnvelope(spans: [SentrySpan, ...SentrySpan[]], client?: Client): SpanEnvelope {\n  function dscHasRequiredProps(dsc: Partial<DynamicSamplingContext>): dsc is DynamicSamplingContext {\n    return !!dsc.trace_id && !!dsc.public_key;\n  }\n\n  // For the moment we'll obtain the DSC from the first span in the array\n  // This might need to be changed if we permit sending multiple spans from\n  // different segments in one envelope\n  const dsc = getDynamicSamplingContextFromSpan(spans[0]);\n\n  const dsn = client?.getDsn();\n  const tunnel = client?.getOptions().tunnel;\n\n  const headers: SpanEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n    ...(dscHasRequiredProps(dsc) && { trace: dsc }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const beforeSendSpan = client?.getOptions().beforeSendSpan;\n  const convertToSpanJSON = beforeSendSpan\n    ? (span: SentrySpan) => {\n        const spanJson = spanToJSON(span);\n        const processedSpan = beforeSendSpan(spanJson);\n\n        if (!processedSpan) {\n          showSpanDropWarning();\n          return spanJson;\n        }\n\n        return processedSpan;\n      }\n    : spanToJSON;\n\n  const items: SpanItem[] = [];\n  for (const span of spans) {\n    const spanJson = convertToSpanJSON(span);\n    if (spanJson) {\n      items.push(createSpanEnvelopeItem(spanJson));\n    }\n  }\n\n  return createEnvelope<SpanEnvelope>(headers, items);\n}\n\n/**\n * Create an Envelope from a CSP report.\n */\nexport function createRawSecurityEnvelope(\n  report: LegacyCSPReport,\n  dsn: DsnComponents,\n  tunnel?: string,\n  release?: string,\n  environment?: string,\n): RawSecurityEnvelope {\n  const envelopeHeaders = {\n    event_id: uuid4(),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const eventItem: RawSecurityItem = [\n    { type: 'raw_security', sentry_release: release, sentry_environment: environment },\n    report,\n  ];\n\n  return createEnvelope<RawSecurityEnvelope>(envelopeHeaders, [eventItem]);\n}\n", "import type {\n  SentrySpanArguments,\n  Span,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContextData,\n  SpanTimeInput,\n} from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport { generateSpanId, generateTraceId } from '../utils/propagationContext';\nimport { TRACE_FLAG_NONE } from '../utils/spanUtils';\n\n/**\n * A Sentry Span that is non-recording, meaning it will not be sent to Sentry.\n */\nexport class SentryNonRecordingSpan implements Span {\n  private _traceId: string;\n  private _spanId: string;\n\n  public constructor(spanContext: SentrySpanArguments = {}) {\n    this._traceId = spanContext.traceId || generateTraceId();\n    this._spanId = spanContext.spanId || generateSpanId();\n  }\n\n  /** @inheritdoc */\n  public spanContext(): SpanContextData {\n    return {\n      spanId: this._spanId,\n      traceId: this._traceId,\n      traceFlags: TRACE_FLAG_NONE,\n    };\n  }\n\n  /** @inheritdoc */\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  public end(_timestamp?: SpanTimeInput): void {}\n\n  /** @inheritdoc */\n  public setAttribute(_key: string, _value: SpanAttributeValue | undefined): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setAttributes(_values: SpanAttributes): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setStatus(_status: SpanStatus): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public updateName(_name: string): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public isRecording(): boolean {\n    return false;\n  }\n\n  /** @inheritdoc */\n  public addEvent(\n    _name: string,\n    _attributesOrStartTime?: SpanAttributes | SpanTimeInput,\n    _startTime?: SpanTimeInput,\n  ): this {\n    return this;\n  }\n\n  /** @inheritDoc */\n  public addLink(_link: unknown): this {\n    return this;\n  }\n\n  /** @inheritDoc */\n  public addLinks(_links: unknown[]): this {\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public recordException(_exception: unknown, _time?: number | undefined): void {\n    // noop\n  }\n}\n", "import { isThenable } from '../utils/is';\n\n/**\n * Wrap a callback function with error handling.\n * If an error is thrown, it will be passed to the `onError` callback and re-thrown.\n *\n * If the return value of the function is a promise, it will be handled with `maybeHandlePromiseRejection`.\n *\n * If an `onFinally` callback is provided, this will be called when the callback has finished\n * - so if it returns a promise, once the promise resolved/rejected,\n * else once the callback has finished executing.\n * The `onFinally` callback will _always_ be called, no matter if an error was thrown or not.\n */\nexport function handleCallbackErrors<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  Fn extends () => any,\n>(\n  fn: Fn,\n  onError: (error: unknown) => void,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onFinally: () => void = () => {},\n): ReturnType<Fn> {\n  let maybePromiseResult: ReturnType<Fn>;\n  try {\n    maybePromiseResult = fn();\n  } catch (e) {\n    onError(e);\n    onFinally();\n    throw e;\n  }\n\n  return maybeHandlePromiseRejection(maybePromiseResult, onError, onFinally);\n}\n\n/**\n * Maybe handle a promise rejection.\n * This expects to be given a value that _may_ be a promise, or any other value.\n * If it is a promise, and it rejects, it will call the `onError` callback.\n * Other than this, it will generally return the given value as-is.\n */\nfunction maybeHandlePromiseRejection<MaybePromise>(\n  value: MaybePromise,\n  onError: (error: unknown) => void,\n  onFinally: () => void,\n): MaybePromise {\n  if (isThenable(value)) {\n    // @ts-expect-error - the isThenable check returns the \"wrong\" type here\n    return value.then(\n      res => {\n        onFinally();\n        return res;\n      },\n      e => {\n        onError(e);\n        onFinally();\n        throw e;\n      },\n    );\n  }\n\n  onFinally();\n  return value;\n}\n", "import { DEBUG_BUILD } from '../debug-build';\nimport type { Span } from '../types-hoist/span';\nimport { logger } from '../utils/logger';\nimport { getRootSpan, spanIsSampled, spanToJSON } from '../utils/spanUtils';\n\n/**\n * Print a log message for a started span.\n */\nexport function logSpanStart(span: Span): void {\n  if (!DEBUG_BUILD) return;\n\n  const { description = '< unknown name >', op = '< unknown op >', parent_span_id: parentSpanId } = spanToJSON(span);\n  const { spanId } = span.spanContext();\n\n  const sampled = spanIsSampled(span);\n  const rootSpan = getRootSpan(span);\n  const isRootSpan = rootSpan === span;\n\n  const header = `[Tracing] Starting ${sampled ? 'sampled' : 'unsampled'} ${isRootSpan ? 'root ' : ''}span`;\n\n  const infoParts: string[] = [`op: ${op}`, `name: ${description}`, `ID: ${spanId}`];\n\n  if (parentSpanId) {\n    infoParts.push(`parent ID: ${parentSpanId}`);\n  }\n\n  if (!isRootSpan) {\n    const { op, description } = spanToJSON(rootSpan);\n    infoParts.push(`root ID: ${rootSpan.spanContext().spanId}`);\n    if (op) {\n      infoParts.push(`root op: ${op}`);\n    }\n    if (description) {\n      infoParts.push(`root description: ${description}`);\n    }\n  }\n\n  logger.log(`${header}\n  ${infoParts.join('\\n  ')}`);\n}\n\n/**\n * Print a log message for an ended span.\n */\nexport function logSpanEnd(span: Span): void {\n  if (!DEBUG_BUILD) return;\n\n  const { description = '< unknown name >', op = '< unknown op >' } = spanToJSON(span);\n  const { spanId } = span.spanContext();\n  const rootSpan = getRootSpan(span);\n  const isRootSpan = rootSpan === span;\n\n  const msg = `[Tracing] Finishing \"${op}\" ${isRootSpan ? 'root ' : ''}span \"${description}\" with ID ${spanId}`;\n  logger.log(msg);\n}\n", "import { DEBUG_BUILD } from '../debug-build';\nimport type { Options } from '../types-hoist/options';\nimport type { SamplingContext } from '../types-hoist/samplingcontext';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { logger } from '../utils/logger';\nimport { parseSampleRate } from '../utils/parseSampleRate';\n\n/**\n * Makes a sampling decision for the given options.\n *\n * Called every time a root span is created. Only root spans which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n */\nexport function sampleSpan(\n  options: Pick<Options, 'tracesSampleRate' | 'tracesSampler'>,\n  samplingContext: SamplingContext,\n  sampleRand: number,\n): [sampled: boolean, sampleRate?: number, localSampleRateWasApplied?: boolean] {\n  // nothing to do if span recording is not enabled\n  if (!hasSpansEnabled(options)) {\n    return [false];\n  }\n\n  let localSampleRateWasApplied = undefined;\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler({\n      ...samplingContext,\n      inheritOrSampleWith: fallbackSampleRate => {\n        // If we have an incoming parent sample rate, we'll just use that one.\n        // The sampling decision will be inherited because of the sample_rand that was generated when the trace reached the incoming boundaries of the SDK.\n        if (typeof samplingContext.parentSampleRate === 'number') {\n          return samplingContext.parentSampleRate;\n        }\n\n        // Fallback if parent sample rate is not on the incoming trace (e.g. if there is no baggage)\n        // This is to provide backwards compatibility if there are incoming traces from older SDKs that don't send a parent sample rate or a sample rand. In these cases we just want to force either a sampling decision on the downstream traces via the sample rate.\n        if (typeof samplingContext.parentSampled === 'boolean') {\n          return Number(samplingContext.parentSampled);\n        }\n\n        return fallbackSampleRate;\n      },\n    });\n    localSampleRateWasApplied = true;\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n    localSampleRateWasApplied = true;\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get.\n  // (The only valid values are booleans or numbers between 0 and 1.)\n  const parsedSampleRate = parseSampleRate(sampleRate);\n\n  if (parsedSampleRate === undefined) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n          sampleRate,\n        )} of type ${JSON.stringify(typeof sampleRate)}.`,\n      );\n    return [false];\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!parsedSampleRate) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because ${\n          typeof options.tracesSampler === 'function'\n            ? 'tracesSampler returned 0 or false'\n            : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n        }`,\n      );\n    return [false, parsedSampleRate, localSampleRateWasApplied];\n  }\n\n  // We always compare the sample rand for the current execution context against the chosen sample rate.\n  // Read more: https://develop.sentry.dev/sdk/telemetry/traces/#propagated-random-value\n  const shouldSample = sampleRand < parsedSampleRate;\n\n  // if we're not going to keep it, we're done\n  if (!shouldSample) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n          sampleRate,\n        )})`,\n      );\n  }\n\n  return [shouldSample, parsedSampleRate, localSampleRateWasApplied];\n}\n", "import { DEBUG_BUILD } from '../debug-build';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE,\n} from '../semanticAttributes';\nimport type { Measurements, MeasurementUnit } from '../types-hoist/measurement';\nimport type { TimedEvent } from '../types-hoist/timedEvent';\nimport { logger } from '../utils/logger';\nimport { getActiveSpan, getRootSpan } from '../utils/spanUtils';\n\n/**\n * Adds a measurement to the active transaction on the current global scope. You can optionally pass in a different span\n * as the 4th parameter.\n */\nexport function setMeasurement(name: string, value: number, unit: MeasurementUnit, activeSpan = getActiveSpan()): void {\n  const rootSpan = activeSpan && getRootSpan(activeSpan);\n\n  if (rootSpan) {\n    DEBUG_BUILD && logger.log(`[Measurement] Setting measurement on root span: ${name} = ${value} ${unit}`);\n    rootSpan.addEvent(name, {\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]: value,\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]: unit as string,\n    });\n  }\n}\n\n/**\n * Convert timed events to measurements.\n */\nexport function timedEventsToMeasurements(events: TimedEvent[]): Measurements | undefined {\n  if (!events || events.length === 0) {\n    return undefined;\n  }\n\n  const measurements: Measurements = {};\n  events.forEach(event => {\n    const attributes = event.attributes || {};\n    const unit = attributes[SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT] as MeasurementUnit | undefined;\n    const value = attributes[SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE] as number | undefined;\n\n    if (typeof unit === 'string' && typeof value === 'number') {\n      measurements[event.name] = { value, unit };\n    }\n  });\n\n  return measurements;\n}\n", "import { getClient, getCurrentScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { createSpanEnvelope } from '../envelope';\nimport {\n  SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME,\n  SEMANTIC_ATTRIBUTE_PROFILE_ID,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { SpanEnvelope } from '../types-hoist/envelope';\nimport type { TransactionEvent } from '../types-hoist/event';\nimport type { SpanLink } from '../types-hoist/link';\nimport type {\n  SentrySpanArguments,\n  Span,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContextData,\n  SpanJSO<PERSON>,\n  Span<PERSON>rigin,\n  SpanTimeInput,\n} from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport type { TimedEvent } from '../types-hoist/timedEvent';\nimport type { TransactionSource } from '../types-hoist/transaction';\nimport { logger } from '../utils/logger';\nimport { generateSpanId, generateTraceId } from '../utils/propagationContext';\nimport {\n  convertSpanLinksForEnvelope,\n  getRootSpan,\n  getSpanDescendants,\n  getStatusMessage,\n  spanTimeInputToSeconds,\n  spanToJSON,\n  spanToTransactionTraceContext,\n  TRACE_FLAG_NONE,\n  TRACE_FLAG_SAMPLED,\n} from '../utils/spanUtils';\nimport { timestampInSeconds } from '../utils/time';\nimport { getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { logSpanEnd } from './logSpans';\nimport { timedEventsToMeasurements } from './measurement';\nimport { getCapturedScopesOnSpan } from './utils';\n\nconst MAX_SPAN_COUNT = 1000;\n\n/**\n * Span contains all data about a span\n */\nexport class SentrySpan implements Span {\n  protected _traceId: string;\n  protected _spanId: string;\n  protected _parentSpanId?: string | undefined;\n  protected _sampled: boolean | undefined;\n  protected _name?: string | undefined;\n  protected _attributes: SpanAttributes;\n  protected _links?: SpanLink[];\n  /** Epoch timestamp in seconds when the span started. */\n  protected _startTime: number;\n  /** Epoch timestamp in seconds when the span ended. */\n  protected _endTime?: number | undefined;\n  /** Internal keeper of the status */\n  protected _status?: SpanStatus;\n  /** The timed events added to this span. */\n  protected _events: TimedEvent[];\n\n  /** if true, treat span as a standalone span (not part of a transaction) */\n  private _isStandaloneSpan?: boolean;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startSpan()`\n   * or other span methods.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext: SentrySpanArguments = {}) {\n    this._traceId = spanContext.traceId || generateTraceId();\n    this._spanId = spanContext.spanId || generateSpanId();\n    this._startTime = spanContext.startTimestamp || timestampInSeconds();\n    this._links = spanContext.links;\n\n    this._attributes = {};\n    this.setAttributes({\n      [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'manual',\n      [SEMANTIC_ATTRIBUTE_SENTRY_OP]: spanContext.op,\n      ...spanContext.attributes,\n    });\n\n    this._name = spanContext.name;\n\n    if (spanContext.parentSpanId) {\n      this._parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this._sampled = spanContext.sampled;\n    }\n    if (spanContext.endTimestamp) {\n      this._endTime = spanContext.endTimestamp;\n    }\n\n    this._events = [];\n\n    this._isStandaloneSpan = spanContext.isStandalone;\n\n    // If the span is already ended, ensure we finalize the span immediately\n    if (this._endTime) {\n      this._onSpanEnded();\n    }\n  }\n\n  /** @inheritDoc */\n  public addLink(link: SpanLink): this {\n    if (this._links) {\n      this._links.push(link);\n    } else {\n      this._links = [link];\n    }\n    return this;\n  }\n\n  /** @inheritDoc */\n  public addLinks(links: SpanLink[]): this {\n    if (this._links) {\n      this._links.push(...links);\n    } else {\n      this._links = links;\n    }\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but it is needed for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public recordException(_exception: unknown, _time?: number | undefined): void {\n    // noop\n  }\n\n  /** @inheritdoc */\n  public spanContext(): SpanContextData {\n    const { _spanId: spanId, _traceId: traceId, _sampled: sampled } = this;\n    return {\n      spanId,\n      traceId,\n      traceFlags: sampled ? TRACE_FLAG_SAMPLED : TRACE_FLAG_NONE,\n    };\n  }\n\n  /** @inheritdoc */\n  public setAttribute(key: string, value: SpanAttributeValue | undefined): this {\n    if (value === undefined) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._attributes[key];\n    } else {\n      this._attributes[key] = value;\n    }\n\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setAttributes(attributes: SpanAttributes): this {\n    Object.keys(attributes).forEach(key => this.setAttribute(key, attributes[key]));\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for browser tracing where we want to adjust the start time afterwards.\n   * USE THIS WITH CAUTION!\n   *\n   * @hidden\n   * @internal\n   */\n  public updateStartTime(timeInput: SpanTimeInput): void {\n    this._startTime = spanTimeInputToSeconds(timeInput);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatus): this {\n    this._status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateName(name: string): this {\n    this._name = name;\n    this.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'custom');\n    return this;\n  }\n\n  /** @inheritdoc */\n  public end(endTimestamp?: SpanTimeInput): void {\n    // If already ended, skip\n    if (this._endTime) {\n      return;\n    }\n\n    this._endTime = spanTimeInputToSeconds(endTimestamp);\n    logSpanEnd(this);\n\n    this._onSpanEnded();\n  }\n\n  /**\n   * Get JSON representation of this span.\n   *\n   * @hidden\n   * @internal This method is purely for internal purposes and should not be used outside\n   * of SDK code. If you need to get a JSON representation of a span,\n   * use `spanToJSON(span)` instead.\n   */\n  public getSpanJSON(): SpanJSON {\n    return {\n      data: this._attributes,\n      description: this._name,\n      op: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      parent_span_id: this._parentSpanId,\n      span_id: this._spanId,\n      start_timestamp: this._startTime,\n      status: getStatusMessage(this._status),\n      timestamp: this._endTime,\n      trace_id: this._traceId,\n      origin: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      profile_id: this._attributes[SEMANTIC_ATTRIBUTE_PROFILE_ID] as string | undefined,\n      exclusive_time: this._attributes[SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME] as number | undefined,\n      measurements: timedEventsToMeasurements(this._events),\n      is_segment: (this._isStandaloneSpan && getRootSpan(this) === this) || undefined,\n      segment_id: this._isStandaloneSpan ? getRootSpan(this).spanContext().spanId : undefined,\n      links: convertSpanLinksForEnvelope(this._links),\n    };\n  }\n\n  /** @inheritdoc */\n  public isRecording(): boolean {\n    return !this._endTime && !!this._sampled;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | SpanTimeInput,\n    startTime?: SpanTimeInput,\n  ): this {\n    DEBUG_BUILD && logger.log('[Tracing] Adding an event to span:', name);\n\n    const time = isSpanTimeInput(attributesOrStartTime) ? attributesOrStartTime : startTime || timestampInSeconds();\n    const attributes = isSpanTimeInput(attributesOrStartTime) ? {} : attributesOrStartTime || {};\n\n    const event: TimedEvent = {\n      name,\n      time: spanTimeInputToSeconds(time),\n      attributes,\n    };\n\n    this._events.push(event);\n\n    return this;\n  }\n\n  /**\n   * This method should generally not be used,\n   * but for now we need a way to publicly check if the `_isStandaloneSpan` flag is set.\n   * USE THIS WITH CAUTION!\n   * @internal\n   * @hidden\n   * @experimental\n   */\n  public isStandaloneSpan(): boolean {\n    return !!this._isStandaloneSpan;\n  }\n\n  /** Emit `spanEnd` when the span is ended. */\n  private _onSpanEnded(): void {\n    const client = getClient();\n    if (client) {\n      client.emit('spanEnd', this);\n    }\n\n    // A segment span is basically the root span of a local span tree.\n    // So for now, this is either what we previously refer to as the root span,\n    // or a standalone span.\n    const isSegmentSpan = this._isStandaloneSpan || this === getRootSpan(this);\n\n    if (!isSegmentSpan) {\n      return;\n    }\n\n    // if this is a standalone span, we send it immediately\n    if (this._isStandaloneSpan) {\n      if (this._sampled) {\n        sendSpanEnvelope(createSpanEnvelope([this], client));\n      } else {\n        DEBUG_BUILD &&\n          logger.log('[Tracing] Discarding standalone span because its trace was not chosen to be sampled.');\n        if (client) {\n          client.recordDroppedEvent('sample_rate', 'span');\n        }\n      }\n      return;\n    }\n\n    const transactionEvent = this._convertSpanToTransaction();\n    if (transactionEvent) {\n      const scope = getCapturedScopesOnSpan(this).scope || getCurrentScope();\n      scope.captureEvent(transactionEvent);\n    }\n  }\n\n  /**\n   * Finish the transaction & prepare the event to send to Sentry.\n   */\n  private _convertSpanToTransaction(): TransactionEvent | undefined {\n    // We can only convert finished spans\n    if (!isFullFinishedSpan(spanToJSON(this))) {\n      return undefined;\n    }\n\n    if (!this._name) {\n      DEBUG_BUILD && logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this._name = '<unlabeled transaction>';\n    }\n\n    const { scope: capturedSpanScope, isolationScope: capturedSpanIsolationScope } = getCapturedScopesOnSpan(this);\n\n    const normalizedRequest = capturedSpanScope?.getScopeData().sdkProcessingMetadata?.normalizedRequest;\n\n    if (this._sampled !== true) {\n      return undefined;\n    }\n\n    // The transaction span itself as well as any potential standalone spans should be filtered out\n    const finishedSpans = getSpanDescendants(this).filter(span => span !== this && !isStandaloneSpan(span));\n\n    const spans = finishedSpans.map(span => spanToJSON(span)).filter(isFullFinishedSpan);\n\n    const source = this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource | undefined;\n\n    // remove internal root span attributes we don't need to send.\n    /* eslint-disable @typescript-eslint/no-dynamic-delete */\n    delete this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n    spans.forEach(span => {\n      delete span.data[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n    });\n    // eslint-enabled-next-line @typescript-eslint/no-dynamic-delete\n\n    const transaction: TransactionEvent = {\n      contexts: {\n        trace: spanToTransactionTraceContext(this),\n      },\n      spans:\n        // spans.sort() mutates the array, but `spans` is already a copy so we can safely do this here\n        // we do not use spans anymore after this point\n        spans.length > MAX_SPAN_COUNT\n          ? spans.sort((a, b) => a.start_timestamp - b.start_timestamp).slice(0, MAX_SPAN_COUNT)\n          : spans,\n      start_timestamp: this._startTime,\n      timestamp: this._endTime,\n      transaction: this._name,\n      type: 'transaction',\n      sdkProcessingMetadata: {\n        capturedSpanScope,\n        capturedSpanIsolationScope,\n        dynamicSamplingContext: getDynamicSamplingContextFromSpan(this),\n      },\n      request: normalizedRequest,\n      ...(source && {\n        transaction_info: {\n          source,\n        },\n      }),\n    };\n\n    const measurements = timedEventsToMeasurements(this._events);\n    const hasMeasurements = measurements && Object.keys(measurements).length;\n\n    if (hasMeasurements) {\n      DEBUG_BUILD &&\n        logger.log(\n          '[Measurements] Adding measurements to transaction event',\n          JSON.stringify(measurements, undefined, 2),\n        );\n      transaction.measurements = measurements;\n    }\n\n    return transaction;\n  }\n}\n\nfunction isSpanTimeInput(value: undefined | SpanAttributes | SpanTimeInput): value is SpanTimeInput {\n  return (value && typeof value === 'number') || value instanceof Date || Array.isArray(value);\n}\n\n// We want to filter out any incomplete SpanJSON objects\nfunction isFullFinishedSpan(input: Partial<SpanJSON>): input is SpanJSON {\n  return !!input.start_timestamp && !!input.timestamp && !!input.span_id && !!input.trace_id;\n}\n\n/** `SentrySpan`s can be sent as a standalone span rather than belonging to a transaction */\nfunction isStandaloneSpan(span: Span): boolean {\n  return span instanceof SentrySpan && span.isStandaloneSpan();\n}\n\n/**\n * Sends a `SpanEnvelope`.\n *\n * Note: If the envelope's spans are dropped, e.g. via `beforeSendSpan`,\n * the envelope will not be sent either.\n */\nfunction sendSpanEnvelope(envelope: SpanEnvelope): void {\n  const client = getClient();\n  if (!client) {\n    return;\n  }\n\n  const spanItems = envelope[1];\n  if (!spanItems || spanItems.length === 0) {\n    client.recordDroppedEvent('before_send', 'span');\n    return;\n  }\n\n  // sendEnvelope should not throw\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  client.sendEnvelope(envelope);\n}\n", "/* eslint-disable max-lines */\n\nimport { getAsyncContextStrategy } from '../asyncContext';\nimport type { AsyncContextStrategy } from '../asyncContext/types';\nimport { getMainCarrier } from '../carrier';\nimport { getClient, getCurrentScope, getIsolationScope, withScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { Scope } from '../scope';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { ClientOptions } from '../types-hoist/options';\nimport type { SentrySpanArguments, Span, SpanTimeInput } from '../types-hoist/span';\nimport type { StartSpanOptions } from '../types-hoist/startSpanOptions';\nimport { handleCallbackErrors } from '../utils/handleCallbackErrors';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { logger } from '../utils/logger';\nimport { parseSampleRate } from '../utils/parseSampleRate';\nimport { generateTraceId } from '../utils/propagationContext';\nimport { _getSpanForScope, _setSpanForScope } from '../utils/spanOnScope';\nimport { addChildSpanToSpan, getRootSpan, spanIsSampled, spanTimeInputToSeconds, spanToJSON } from '../utils/spanUtils';\nimport { propagationContextFromHeaders } from '../utils/tracing';\nimport { freezeDscOnSpan, getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { logSpanStart } from './logSpans';\nimport { sampleSpan } from './sampling';\nimport { SentryNonRecordingSpan } from './sentryNonRecordingSpan';\nimport { SentrySpan } from './sentrySpan';\nimport { SPAN_STATUS_ERROR } from './spanstatus';\nimport { setCapturedScopesOnSpan } from './utils';\n\nconst SUPPRESS_TRACING_KEY = '__SENTRY_SUPPRESS_TRACING__';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpan<T>(options: StartSpanOptions, callback: (span: Span) => T): T {\n  const acs = getAcs();\n  if (acs.startSpan) {\n    return acs.startSpan(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan, scope: customScope } = options;\n\n  // We still need to fork a potentially passed scope, as we set the active span on it\n  // and we need to ensure that it is cleaned up properly once the span ends.\n  const customForkedScope = customScope?.clone();\n\n  return withScope(customForkedScope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      return handleCallbackErrors(\n        () => callback(activeSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n        () => {\n          activeSpan.end();\n        },\n      );\n    });\n  });\n}\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a transaction/span, but does not finish the span\n * after the function is done automatically. Use `span.end()` to end the span.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpanManual<T>(options: StartSpanOptions, callback: (span: Span, finish: () => void) => T): T {\n  const acs = getAcs();\n  if (acs.startSpanManual) {\n    return acs.startSpanManual(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan, scope: customScope } = options;\n\n  const customForkedScope = customScope?.clone();\n\n  return withScope(customForkedScope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      return handleCallbackErrors(\n        // We pass the `finish` function to the callback, so the user can finish the span manually\n        // this is mainly here for historic purposes because previously, we instructed users to call\n        // `finish` instead of `span.end()` to also clean up the scope. Nowadays, calling `span.end()`\n        // or `finish` has the same effect and we simply leave it here to avoid breaking user code.\n        () => callback(activeSpan, () => activeSpan.end()),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getActiveSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * This function will always return a span,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startInactiveSpan(options: StartSpanOptions): Span {\n  const acs = getAcs();\n  if (acs.startInactiveSpan) {\n    return acs.startInactiveSpan(options);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan } = options;\n\n  // If `options.scope` is defined, we use this as as a wrapper,\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = options.scope\n    ? (callback: () => Span) => withScope(options.scope, callback)\n    : customParentSpan !== undefined\n      ? (callback: () => Span) => withActiveSpan(customParentSpan, callback)\n      : (callback: () => Span) => callback();\n\n  return wrapper(() => {\n    const scope = getCurrentScope();\n    const parentSpan = getParentSpan(scope);\n\n    const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n\n    if (shouldSkipSpan) {\n      return new SentryNonRecordingSpan();\n    }\n\n    return createChildOrRootSpan({\n      parentSpan,\n      spanArguments,\n      forceTransaction,\n      scope,\n    });\n  });\n}\n\n/**\n * Continue a trace from `sentry-trace` and `baggage` values.\n * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n * and `<meta name=\"baggage\">` HTML tags.\n *\n * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n * be attached to the incoming trace.\n */\nexport const continueTrace = <V>(\n  options: {\n    sentryTrace: Parameters<typeof propagationContextFromHeaders>[0];\n    baggage: Parameters<typeof propagationContextFromHeaders>[1];\n  },\n  callback: () => V,\n): V => {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.continueTrace) {\n    return acs.continueTrace(options, callback);\n  }\n\n  const { sentryTrace, baggage } = options;\n\n  return withScope(scope => {\n    const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n    scope.setPropagationContext(propagationContext);\n    return callback();\n  });\n};\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback. Can be\n * passed `null` to start an entirely new span tree.\n *\n * @param span Spans started in the context of the provided callback will be children of this span. If `null` is passed,\n * spans started within the callback will not be attached to a parent span.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span | null, callback: (scope: Scope) => T): T {\n  const acs = getAcs();\n  if (acs.withActiveSpan) {\n    return acs.withActiveSpan(span, callback);\n  }\n\n  return withScope(scope => {\n    _setSpanForScope(scope, span || undefined);\n    return callback(scope);\n  });\n}\n\n/** Suppress tracing in the given callback, ensuring no spans are generated inside of it. */\nexport function suppressTracing<T>(callback: () => T): T {\n  const acs = getAcs();\n\n  if (acs.suppressTracing) {\n    return acs.suppressTracing(callback);\n  }\n\n  return withScope(scope => {\n    // Note: We do not wait for the callback to finish before we reset the metadata\n    // the reason for this is that otherwise, in the browser this can lead to very weird behavior\n    // as there is only a single top scope, if the callback takes longer to finish,\n    // other, unrelated spans may also be suppressed, which we do not want\n    // so instead, we only suppress tracing synchronoysly in the browser\n    scope.setSDKProcessingMetadata({ [SUPPRESS_TRACING_KEY]: true });\n    const res = callback();\n    scope.setSDKProcessingMetadata({ [SUPPRESS_TRACING_KEY]: undefined });\n    return res;\n  });\n}\n\n/**\n * Starts a new trace for the duration of the provided callback. Spans started within the\n * callback will be part of the new trace instead of a potentially previously started trace.\n *\n * Important: Only use this function if you want to override the default trace lifetime and\n * propagation mechanism of the SDK for the duration and scope of the provided callback.\n * The newly created trace will also be the root of a new distributed trace, for example if\n * you make http requests within the callback.\n * This function might be useful if the operation you want to instrument should not be part\n * of a potentially ongoing trace.\n *\n * Default behavior:\n * - Server-side: A new trace is started for each incoming request.\n * - Browser: A new trace is started for each page our route. Navigating to a new route\n *            or page will automatically create a new trace.\n */\nexport function startNewTrace<T>(callback: () => T): T {\n  return withScope(scope => {\n    scope.setPropagationContext({\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    });\n    DEBUG_BUILD && logger.info(`Starting a new trace with id ${scope.getPropagationContext().traceId}`);\n    return withActiveSpan(null, callback);\n  });\n}\n\nfunction createChildOrRootSpan({\n  parentSpan,\n  spanArguments,\n  forceTransaction,\n  scope,\n}: {\n  parentSpan: SentrySpan | undefined;\n  spanArguments: SentrySpanArguments;\n  forceTransaction?: boolean;\n  scope: Scope;\n}): Span {\n  if (!hasSpansEnabled()) {\n    const span = new SentryNonRecordingSpan();\n\n    // If this is a root span, we ensure to freeze a DSC\n    // So we can have at least partial data here\n    if (forceTransaction || !parentSpan) {\n      const dsc = {\n        sampled: 'false',\n        sample_rate: '0',\n        transaction: spanArguments.name,\n        ...getDynamicSamplingContextFromSpan(span),\n      } satisfies Partial<DynamicSamplingContext>;\n      freezeDscOnSpan(span, dsc);\n    }\n\n    return span;\n  }\n\n  const isolationScope = getIsolationScope();\n\n  let span: Span;\n  if (parentSpan && !forceTransaction) {\n    span = _startChildSpan(parentSpan, scope, spanArguments);\n    addChildSpanToSpan(parentSpan, span);\n  } else if (parentSpan) {\n    // If we forced a transaction but have a parent span, make sure to continue from the parent span, not the scope\n    const dsc = getDynamicSamplingContextFromSpan(parentSpan);\n    const { traceId, spanId: parentSpanId } = parentSpan.spanContext();\n    const parentSampled = spanIsSampled(parentSpan);\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    freezeDscOnSpan(span, dsc);\n  } else {\n    const {\n      traceId,\n      dsc,\n      parentSpanId,\n      sampled: parentSampled,\n    } = {\n      ...isolationScope.getPropagationContext(),\n      ...scope.getPropagationContext(),\n    };\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    if (dsc) {\n      freezeDscOnSpan(span, dsc);\n    }\n  }\n\n  logSpanStart(span);\n\n  setCapturedScopesOnSpan(span, scope, isolationScope);\n\n  return span;\n}\n\n/**\n * This converts StartSpanOptions to SentrySpanArguments.\n * For the most part (for now) we accept the same options,\n * but some of them need to be transformed.\n */\nfunction parseSentrySpanArguments(options: StartSpanOptions): SentrySpanArguments {\n  const exp = options.experimental || {};\n  const initialCtx: SentrySpanArguments = {\n    isStandalone: exp.standalone,\n    ...options,\n  };\n\n  if (options.startTime) {\n    const ctx: SentrySpanArguments & { startTime?: SpanTimeInput } = { ...initialCtx };\n    ctx.startTimestamp = spanTimeInputToSeconds(options.startTime);\n    delete ctx.startTime;\n    return ctx;\n  }\n\n  return initialCtx;\n}\n\nfunction getAcs(): AsyncContextStrategy {\n  const carrier = getMainCarrier();\n  return getAsyncContextStrategy(carrier);\n}\n\nfunction _startRootSpan(spanArguments: SentrySpanArguments, scope: Scope, parentSampled?: boolean): SentrySpan {\n  const client = getClient();\n  const options: Partial<ClientOptions> = client?.getOptions() || {};\n\n  const { name = '' } = spanArguments;\n\n  const mutableSpanSamplingData = { spanAttributes: { ...spanArguments.attributes }, spanName: name, parentSampled };\n\n  // we don't care about the decision for the moment; this is just a placeholder\n  client?.emit('beforeSampling', mutableSpanSamplingData, { decision: false });\n\n  // If hook consumers override the parentSampled flag, we will use that value instead of the actual one\n  const finalParentSampled = mutableSpanSamplingData.parentSampled ?? parentSampled;\n  const finalAttributes = mutableSpanSamplingData.spanAttributes;\n\n  const currentPropagationContext = scope.getPropagationContext();\n  const [sampled, sampleRate, localSampleRateWasApplied] = scope.getScopeData().sdkProcessingMetadata[\n    SUPPRESS_TRACING_KEY\n  ]\n    ? [false]\n    : sampleSpan(\n        options,\n        {\n          name,\n          parentSampled: finalParentSampled,\n          attributes: finalAttributes,\n          parentSampleRate: parseSampleRate(currentPropagationContext.dsc?.sample_rate),\n        },\n        currentPropagationContext.sampleRand,\n      );\n\n  const rootSpan = new SentrySpan({\n    ...spanArguments,\n    attributes: {\n      [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n      [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:\n        sampleRate !== undefined && localSampleRateWasApplied ? sampleRate : undefined,\n      ...finalAttributes,\n    },\n    sampled,\n  });\n\n  if (!sampled && client) {\n    DEBUG_BUILD && logger.log('[Tracing] Discarding root span because its trace was not chosen to be sampled.');\n    client.recordDroppedEvent('sample_rate', 'transaction');\n  }\n\n  if (client) {\n    client.emit('spanStart', rootSpan);\n  }\n\n  return rootSpan;\n}\n\n/**\n * Creates a new `Span` while setting the current `Span.id` as `parentSpanId`.\n * This inherits the sampling decision from the parent span.\n */\nfunction _startChildSpan(parentSpan: Span, scope: Scope, spanArguments: SentrySpanArguments): Span {\n  const { spanId, traceId } = parentSpan.spanContext();\n  const sampled = scope.getScopeData().sdkProcessingMetadata[SUPPRESS_TRACING_KEY] ? false : spanIsSampled(parentSpan);\n\n  const childSpan = sampled\n    ? new SentrySpan({\n        ...spanArguments,\n        parentSpanId: spanId,\n        traceId,\n        sampled,\n      })\n    : new SentryNonRecordingSpan({ traceId });\n\n  addChildSpanToSpan(parentSpan, childSpan);\n\n  const client = getClient();\n  if (client) {\n    client.emit('spanStart', childSpan);\n    // If it has an endTimestamp, it's already ended\n    if (spanArguments.endTimestamp) {\n      client.emit('spanEnd', childSpan);\n    }\n  }\n\n  return childSpan;\n}\n\nfunction getParentSpan(scope: Scope): SentrySpan | undefined {\n  const span = _getSpanForScope(scope) as SentrySpan | undefined;\n\n  if (!span) {\n    return undefined;\n  }\n\n  const client = getClient();\n  const options: Partial<ClientOptions> = client ? client.getOptions() : {};\n  if (options.parentSpanIsAlwaysRootSpan) {\n    return getRootSpan(span) as SentrySpan;\n  }\n\n  return span;\n}\n\nfunction getActiveSpanWrapper<T>(parentSpan: Span | undefined | null): (callback: () => T) => T {\n  return parentSpan !== undefined\n    ? (callback: () => T) => {\n        return withActiveSpan(parentSpan, callback);\n      }\n    : (callback: () => T) => callback();\n}\n"], "names": [], "mappings": "+hBA0BO,SAAS,EAAmC,CAAO,CAAQ,EAAc,EAAE,CAAX,CAAgB,AACrF,IAD4B,EACrB,CAAC,EAAS,EAAO,AAC1B,CAOO,EARU,AAAO,OAQR,EAAsC,CAAQ,CAAK,CAAO,EAAmB,AAC3F,GAAM,CAAC,EAAS,EAAK,CAAI,CADM,CACjB,AAAO,AACrB,MAAO,AAD0B,CACzB,EAAS,CAAC,GAAG,CAAN,CAAa,EAAQ,CAAE,AACxC,AAD4B,CASrB,GAT8B,MASrB,EACd,CAAQ,CACR,CAAQ,EAIR,IAAK,IAAM,IANsB,CAIX,CAAQ,CAAC,CAAC,CAAC,CAES,CACxC,CADsB,GAChB,CADmB,CACA,CAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAG7C,EAJsC,CAEvB,CAEX,CAFoB,EAAc,GAE5B,AACR,CAHqB,CAEX,KAFwB,AAG3B,CAEb,CAEE,EAJe,EAHyC,CAAC,EAOlD,CACT,CAKO,GANO,MAME,EAAyB,CAAQ,CAAY,CAAK,EAA+B,AAC/F,OAAO,EAAoB,EAAU,CAAC,CAAC,CAAE,GAAN,AADG,CACO,AAAK,EAAM,GAAD,GAA7B,EAAsC,CAAC,GACnE,CADuE,AAMvE,CANwE,CAAC,OAMhE,EAAW,CAAK,EAAsB,AAC7C,IAAM,CADW,CACH,CAAA,EAAE,EAAF,AAAE,gBAAgB,AAAhB,EAAgB,EAAC,UAAU,CAAC,CAC5C,OAAO,EAAQ,KAAD,SAAC,CAAiB,EAAQ,KAAD,SAAe,CAAC,GAAS,EAAJ,EAAQ,WAAW,EAAE,CAAC,MAAM,CAAC,EAC3F,CAaO,EAdyF,CAAC,MAcjF,EAAkB,CAAQ,EAAiC,AACzE,GAAM,CAAC,EAAY,EAAK,CAAI,EAAJ,AAEpB,CAH2B,CAGI,CAFlB,EAER,CAF2B,AAEG,CAAC,SAAS,CAAC,GAElD,OAF4D,CAAC,CAEpD,EAAO,CAAI,EAA6B,AAC1B,CADR,OACgB,EAAzB,AAA2B,OAApB,EACT,EAAQ,AAAgB,CADT,OACS,SAAT,EAAoB,EAAQ,EAAO,CAAC,AAAV,CAAO,CAAc,GAAQ,EAAH,AAAQ,CAAP,AAEpE,CAF0E,CAAb,AAEvD,GAAD,CAAK,CAAC,AAAgB,QAAS,SAAlB,EAAoB,EAAf,AAA0B,GAAQ,CAAJ,CAE3D,AAF6D,CAI3D,CAJiE,CAAC,AAAd,EAI/C,IAAM,IAAK,CAAG,EAAO,CACxB,EADsB,CAChB,CAAC,EAAa,EAAO,CAAI,EAI/B,EAJ2B,AAAQ,CAEnC,CAFkB,CAEX,CAAC,GAAF;AAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AAAE,CAAC,CAAC,CAErB,UAAnB,OAAO,GAAwB,IAAhB,GAAgB,MAAmB,UAAU,CAC9D,CADgE,CACzD,IAAD,GAAQ,AACT,CADU,AAEf,IAAI,EACJ,GAAI,CACF,EAAqB,IAAI,CAAC,KAFN,IAEe,CAAC,EAC5C,CAAQ,IAD2C,CAAC,CACrC,CAAC,CAAE,CAIV,EAAqB,IAAI,CAAC,SAAS,CAAA,CAAnC,AAAmC,EAAA,EAAC,SAAA,AAAS,EAAC,GACtD,CACM,EAAO,CAFgD,CAAC,AAG9D,CAH+D,AAI/D,CAEE,AAJU,MAIc,QAJK,AAIL,CAJM,CAIvB,OAAO,EAAqB,EAAQ,AAG7C,CAHgB,EAAqB,MAG5B,AAAc,CAAO,EAG5B,AAHwD,CAHA,GAMlD,EAAS,IAHK,AAGP,AAAM,UAAU,CAFT,AAEU,EAFF,KAAD,CAAO,CAAC,CAAC,CAEK,CAFA,AAEC,CAFH,EAAK,CAAK,EAAM,EAAI,CAAD,KAAO,CAAE,CAAC,CAAC,EAGjE,EAAS,CAAC,CACd,EADW,EACN,IAAM,KAAU,CAAH,CAChB,EAAO,GADmB,AAChB,CADkB,AACtB,AAAK,EAAQ,GACnB,CADiB,EAAQ,AACf,CADgB,CACT,CAAV,GAAS,EAAO,CAGzB,OAAO,CACT,EAd2D,EAC3D,CAYe,AAMR,EAnByD,CAAC,MAmBjD,EAAc,CAAG,EAAiC,AAChE,IAAI,EAAwB,EADD,EACvB,IAAiC,EAAxB,OAAO,EAAmB,CAAf,CAA0B,GAAG,AAAI,CAAJ,CAErD,CAF4D,EAAX,MAExC,EAAW,CAAM,EAAsB,AAC9C,IAAM,CADW,CACL,CAAF,CAAS,IAAD,IAAS,CAAC,CAAC,CAAE,GAG/B,GAHqC,CAAC,GAEtC,EAAS,EAAO,EAAhB,EAAe,IAAS,CAAC,EAAS,CAAC,CAAC,CAC7B,CADkB,AAE7B,CAEE,CAHY,QAGH,IACP,IADe,AACX,EAAI,CADgB,CACT,IAAD,GAAQ,CAAC,GAAG,CAAC,AAM3B,OAJI,CAAE,CAAE,CAAC,EAAE,AACT,CAAE,EAAE,EAAO,IAAD,EAAC,AAAM,EAGZ,IAAI,CAAC,KAAK,CA9ErB,AA8EsB,SA9Eb,AAAW,CA8EY,AA9EP,EAAsB,AAC7C,IAAM,EAAQ,CADG,AACH,EAAA,EAAA,AAAE,gBAAA,AAAgB,EAAA,EAAC,UAAU,CAAC,CAC5C,OAAO,EAAQ,KAAD,SAAC,CAAiB,EAAQ,KAAD,SAAe,CAAC,GAAS,EAAJ,EAAQ,WAAW,EAAE,CAAC,MAAM,CAAC,EAC3F,EA2EiC,CA5E+D,CAAC,AA4ErD,CAAC,CAAC,CAAC,CAC/C,AADiD,CAG/C,GAHyC,CAGnC,EAAiB,IAEjB,EAAsB,EAFG,AAED,CAAnB,AAEX,CAJsD,EAAhD,EAIC,EAAO,IAAD,EAAO,EAAE,CACpB,IAAM,EAAa,IACb,EAA4C,EADvB,AAArB,EAAgD,IACJ,EAA7B,AAAf,OAAsB,EAAW,MAAA,CAAsB,CAAvB,CAAkC,MAAA,EAAD,IAAU,EAEjF,EAAM,GAAD,CAAK,CAFgF,AAE/E,CAAC,EAAY,EAAe,EAAW,GAAgB,CAA5C,GAAuD,CAAC,AAClF,AADqD,AAAZ,CAGvC,EAH4E,CAAZ,CAAE,AAAY,EAGvE,CAAC,EAAgB,EAAM,AAChC,CAKO,EANwB,OAAP,AAMR,EAAuB,CAAQ,EAK7C,AAL4E,MAKrE,CAJ0B,CAC/B,IAAI,CAAE,IAF4B,EAEtB,AAChB,CAAG,CAEoB,EACvB,AADgC,CAMzB,KANwB,IAMf,EAA6B,CAAU,EAA8B,AACnF,IAAM,EAAoC,IAA7B,IAA6B,EAA3B,OAD2B,AACpB,EAAW,IAAA,CAAoB,EAAW,CAAhC,CAA2C,IAAI,EAAhB,AAAoB,EAAW,AAApB,IAAwB,CAElG,GAF6F,GAEtF,CACL,CACE,IAAI,CAAE,YAAY,CAClB,MAAM,CAAE,EAAO,IAAD,EAAO,CACrB,QAAQ,CAAE,EAAW,QAAD,AAAS,CAC7B,YAAY,CAAE,EAAW,QAAD,GAAY,CACpC,eAAe,CAAE,EAAW,QAAD,MAAe,AAChD,CAAK,CACD,EACD,AACH,CAEA,GAJU,CAIJ,EAAyE,CAC7E,OAAO,CAAE,SAAS,CAClB,QAAQ,CAAE,AAFwB,SAEf,CACnB,UAAU,CAAE,YAAY,CACxB,WAAW,CAAE,aAAa,CAC1B,KAAK,CAAE,OAAO,CACd,aAAa,CAAE,UAAU,CACzB,WAAW,CAAE,SAAS,CACtB,OAAO,CAAE,SAAS,CAClB,aAAa,CAAE,SAAS,CACxB,YAAY,CAAE,QAAQ,CACtB,gBAAgB,CAAE,QAAQ,CAC1B,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,UAAU,CACpB,IAAI,CAAE,MAAM,CACZ,YAAY,CAAE,UAAU,CACxB,GAAG,CAAE,UACP,AADiB,CAChB,CAKM,SAAS,EAA+B,CAAI,EAAkC,AACnF,OAAO,CAA8B,CAAC,EAAK,AAC7C,CAGO,CAJqC,QAI5B,EAAgC,CAAe,CALjB,CAK8D,AAC1G,GAAI,CAAC,GAAiB,GAAG,CACvB,CADyB,MAG3B,CAHoB,EAGd,KAJuC,CAIrC,CAAI,SAAE,CAAA,CAAU,CAAE,EAAgB,GAAG,CAC7C,MAAO,GADkC,GAChC,IAAI,MAAE,CAAA,CACjB,AAD0B,CAOnB,SAAS,EACd,CAAK,CACL,CAAO,CACP,CAAM,CACN,CAAG,EAEH,IAAM,EAAyB,EAAM,GAAD,IANI,WAMX,GAA6B,EAAE,sBAAsB,CAClF,MAAO,CACL,QAAQ,CAAE,EAAM,GAAD,KAAU,CACzB,OAAO,CAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CACjC,GAAI,GAAW,CAAE,GAAL,AAAQ,CAAE,CAAQ,CAAC,CAAC,AAChC,GAAI,CAD0B,AACzB,CAAC,GAAU,GAAO,AAAH,AAAd,CAAmB,GAAG,CAAA,CAAA,EAAA,EAAE,WAAA,AAAW,EAAC,EAAG,CAAA,AAAG,CAAH,AAAI,AACjD,GAAI,GAA0B,CAC5B,KAAK,CAAE,CACb,CACA,AADK,CACF,AACH,AAFM,SAFE,UAC2B,iLCnN5B,SAAS,EACd,CAAO,CACP,CAAG,CACH,CAAQ,CACR,CAAM,EAEN,IAAM,EAAQ,CAAA,EAAA,CANqB,CAMrB,AAAE,+BAAA,AAA+B,EAAC,GAC1C,EAAkB,CACtB,EAFsD,CAAC,IAEhD,CAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CACjC,GAAI,GAAW,CAAE,GAAL,AAAQ,CAAE,CAAQ,CAAC,CAAC,AAChC,GAAI,CAD0B,AACzB,CAAC,GAAU,GAAI,AAAG,AAAjB,CAAmB,GAAG,CAAA,CAAA,EAAA,EAAE,WAAA,AAAW,EAAC,EAAG,CAAG,AACpD,AADiD,CAAI,AAClD,AAD8C,CAGzC,EACJ,UADgB,EAChB,GAAgB,EAAU,CAAC,CAAE,GAAb,CAAiB,CAAE,UAAA,CAAY,CAAE,EAAO,CAAI,CAAC,CAAE,EAAP,EAAW,CAAE,SAAU,CAAC,CAAE,EAAQ,KAAD,CAAO,EAAE,CAAC,CAErG,MAAA,CAAA,EAAA,EAAO,cAAA,AAAc,EAAkB,EAAiB,CAAC,EAAa,CAAC,AACzE,CAKO,QANiD,AAAe,CAMvD,EACd,CAAK,CACL,CAAG,CACH,CAAQ,CACR,CAAM,QAEN,EANiC,EAM3B,EAAQ,CAAA,EAAA,EAAA,AAAE,+BAAA,AAA+B,EAAC,GAS1C,EAAY,EAAM,CATgC,CAAC,CASlC,CAAC,CAAR,CAA+B,cAAe,GAA9B,EAAM,GAAD,CAAC,CAA0B,EAAM,GAAD,CAAC,CAAO,OAjD7E,AAiDoF,EAlDvC,EACzC,AAmD2B,CAnD1B,EAmDoC,EApDW,CAoDR,CApD2B,AAoD1B,CAnDjC,AAmD2B,EAnDzB,AAGd,EAAM,GAAD,AAAK,CAAE,EAAM,GAAD,AAAC,EAAO,CAAA,CAAE,CAC3B,EAAM,GAAD,AAAI,CAAC,IAAA,CAAO,EAAM,GAAD,AAAI,CAAC,IAAA,EAAQ,EAAQ,IAAI,CAAL,AAC1C,EAAM,GAAG,AAAJ,CAAK,OAAA,CAAU,EAAM,GAAD,AAAI,CAAC,OAAA,EAAW,EAAQ,KAAD,EAAQ,CACxD,EAAM,GAAD,AAAI,CAAC,YAAa,CAAE,CAAC,GAAI,EAAM,GAAD,AAAI,CAAC,YAAA,EAAgB,EAAE,CAAC,EAAE,CAAI,EAAQ,KAAD,OAAC,EAAgB,EAAE,CAAC,AAAC,CAC7F,AA4CwB,EA5ClB,GAAD,AAAI,AA4CoB,CA5CnB,QAAS,CAAE,CAAC,GAAI,EAAM,GAAD,AAAI,CAAC,QAAA,EAAY,EAAE,CAAC,EAAE,CAAI,EAAQ,KAAD,GAAC,EAAY,EAAE,CAAE,AAAD,EA8ChF,IAAM,EAAA,CAAA,EAAA,EAAkB,QAAlB,kBAAkB,AAA0B,EAAC,EAAO,EAAS,CAAX,CAAmB,EAM3E,CANiE,AAAa,CAAL,AAAM,MAMxE,EAAM,GAAD,kBAAsB,CAElC,IAAM,EAAuB,CAAC,CAAE,IAAI,CAArB,AAAuB,CAAU,CAAC,CAAE,EAAM,CACzD,EADwD,CAAR,GAChD,CAAA,EAAA,EAAO,cAAA,AAAc,EAAgB,EAAiB,CAAC,EAAU,CAAC,AACpE,CAOO,KAR2D,GAAZ,CAQtC,EAAmB,CAAK,CAAiC,CAAM,EAAyB,AAQtG,IAAM,EAAA,CAAM,EAAA,EAAA,AARoB,iCAQpB,AAAiC,EAAC,CAAK,CAAC,CAAC,CAAC,CAAC,CAEjD,EAAM,CAAF,EAAU,GAAF,GAAQ,EAAE,CACtB,EAAS,GAAQ,GAAF,OAAY,EAAE,CAAC,MAAM,CAEpC,EAA2B,CAC/B,IADW,GACJ,CAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CACjC,GAbO,AAaH,CAbI,CAAC,EAAI,CAAD,OAAU,EAAG,CAAC,CAaF,AAbG,EAAI,CAaJ,AAbG,AAaP,CAAI,QAbc,EAaT,CAAE,KAAK,CAAE,CAAI,CAAC,CAAD,AAAE,AAC/C,GAAI,CAAC,CAAC,GAAU,GAAO,AAAjB,AAAc,CAAK,GAAG,CAAA,CAAA,EAAA,EAAE,WAAA,AAAW,EAAC,EAAG,CAAA,AAAG,AACpD,CADiD,AAAI,AAClD,CAEK,EAAiB,GAAQ,GAAF,OAAY,EAAE,CAAC,cAAc,CACpD,EAAoB,EACrB,AAAD,IAAK,AACH,IAAM,CADc,CACL,CAAA,EAAA,EAAE,CAAF,SAAE,AAAU,EAAC,GACtB,CAD0B,CAAC,AACX,EAAe,QAAQ,CAAzB,AAA0B,CAEzC,AAAL,EAFoC,EAEhC,KACF,IADgB,EAAE,aAClB,AAAmB,EAAE,EACd,EAIjB,EAAA,EACM,EALmB,QAKT,CAER,EAAoB,EAAE,CAAjB,AACX,IAAK,IAAM,IAAK,CAAG,EAAO,CACxB,EADsB,EAChB,EAAW,EAAkB,GAC/B,CADW,AAAwB,CAAC,CAEtC,EAAM,GADI,AACL,CAAK,CADE,AACF,CAAA,CAFsB,CAEtB,EAAC,sBAAA,AAAsB,EAAC,GAExC,CAEE,IAJ8C,CAAC,CAAC,AAIhD,CAAA,EAAA,EAAO,cAAA,AAAc,EAAe,EAAS,EAC/C,GAD6C,AAAO,CAAC,kHCpI9C,OAAM,EAIJ,WAAW,CAAC,EAAmC,CAAA,CAAE,CAAE,CACxD,IAAI,AAL4C,CAIpB,AACvB,QAAA,CAAW,EAAY,OAAQ,EAAA,AAAT,AAAS,CAAA,EAAA,EAAG,eAAe,AAAf,EAAiB,EACxD,IAAI,CAAC,OAAA,CAAU,EAAY,MAAO,EAAA,CAAA,AAAR,EAAQ,EAAG,cAAA,AAAc,EAAE,CACzD,CAGS,WAAW,EAAoB,CACpC,MAAO,CACL,MAAM,CAAE,IAAI,CAAC,OAAO,CACpB,OAAO,CAAE,IAAI,CAAC,QAAQ,CACtB,UAAU,CAAA,EAAE,eAAe,AACjC,CAAK,AACL,CAIS,GAAG,CAAC,CAAU,CAAwB,CAAA,CAGtC,YAAY,CAAC,CAAI,CAAU,CAAM,CAAwC,CAC9E,OAAO,IAAI,AACf,CAGS,aAAa,CAAC,CAAO,CAAwB,CAClD,OAAO,IAAI,AACf,CAGS,SAAS,CAAC,CAAO,CAAoB,CAC1C,OAAO,IAAI,AACf,CAGS,UAAU,CAAC,CAAK,CAAgB,CACrC,OAAO,IACX,AADe,CAIN,WAAW,EAAY,CAC5B,OAAO,CACX,CAGS,GAJO,KAIC,CACb,CAAK,CACL,CAAsB,CACtB,CAAU,CACJ,CACN,OAAO,IAAI,AACf,CAGS,OAAO,CAAC,CAAK,CAAiB,CACnC,OAAO,IAAI,AACf,CAGS,QAAQ,CAAC,CAAM,CAAmB,CACvC,OAAO,IAAI,AACf,CASS,eAAe,CAAC,CAAU,CAAW,CAAK,CAA6B,CAEhF,CACA,uGC9EO,SAAS,EAId,CAAE,CACF,CAAO,CAEP,CADF,CAC0B,KAAM,CAAE,CAAvB,KAqBT,EACA,EACA,CAFK,CAnBL,GAoBO,CApBH,EACJ,CAoBS,EApBL,CACF,EAAqB,EAAE,CAC3B,CAD6B,AACzB,MAHoB,AAGb,CAAC,CAAE,CAGV,GAJmB,GAEnB,EAAQ,CAAC,CAAC,CACV,EADO,EAED,CAAC,AACX,CAEE,GAJW,AARb,EAQe,EAIN,EAA4B,IAAoB,IAAS,EAchE,CAd8D,AAc9D,EAAA,EAAI,EAdqE,CAAC,AAArB,OAcvC,AAAV,AAd8B,EAcnB,GAEN,EAFW,AAEL,CAFM,EAAE,AAET,CAAK,CAAA,AACf,IACE,GADK,CAEE,GAAG,AACX,AACD,EAHW,EAAE,AAMX,CAHG,KACH,EAAQ,CAAC,CAAC,CACV,EADO,EAED,CAAC,AACf,CAAO,GAFU,AAMf,EANiB,EAOV,EA7BT,GA4BW,AACG,EADD,qICpDN,SAAS,EAAa,CAAI,EAAc,AAC7C,GAAI,CAAA,EAAC,CADqB,UACV,CAAE,OAElB,GAAM,aAAE,EAAc,SAAd,SAAgC,IAAE,EAAK,gBAAgB,CAAE,cAAc,CAAE,CAAa,CAAA,CAAA,CAAA,EAAA,EAAI,UAAA,AAAU,EAAC,GACvG,CAD2G,CAAC,MAC1G,CAAO,CAAA,CAAI,EAAK,EAAD,SAAY,EAAE,CAE/B,EAAQ,CAAA,EAAE,EAAF,AAAE,aAAA,AAAa,EAAC,GACxB,CAD4B,CAAC,AACpB,CAAA,EAAA,EAAE,CAAF,UAAE,AAAW,EAAC,GACvB,CAD2B,CACd,AADe,IACF,EAE1B,EAFa,AAAiB,AAErB,AAFT,CAEU,mBAAmB,EAAE,EAAU,KAAV,IAAoB,CAAE,WAAW,CAAC,CAAC,EAAE,EAAa,QAAF,AAAY,EAAE,CAAC,IAAI,CAAC,CAEnG,EAAsB,CAAC,CAAC,IAAI,CAAnB,CAAqB,EAAG,AAAD,CAAC,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,CAMA,CANA,EAEA,GACA,EAAA,IAAA,CAAA,CAAA,CADA,AACA,EADA,QACA,EAAA,EAAA,CAAA,CAAA,CAGA,CAAA,EAAA,CACA,GAAA,AAJA,IAGA,AACA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,UAAA,EAAA,GACA,EAAA,GADA,CAAA,AACA,CAAA,CAAA,CAAA,QAAA,EAAA,EAAA,MAAA,KAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CACA,EAAA,CACA,CADA,CACA,IAAA,CAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAEA,GACA,EAAA,IAAA,CAAA,CAAA,AADA,CACA,CADA,gBACA,EAAA,EAAA,CAAA,CAAA,AAEA,GAEA,IAJA,EAIA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA;EACA,EAAA,EAAA,IAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,AACA,CAKA,SAAA,EAAA,CAAA,EAAA,AACA,GAAA,CAAA,CADA,CACA,WAAA,CAAA,OAEA,GAAA,aAAA,EAAA,SAAA,SAAA,IAAA,EAAA,gBAAA,CAAA,CAAA,CAAA,EAAA,EAAA,UAAA,EAAA,GACA,CADA,AACA,CADA,OACA,CAAA,CAAA,EAAA,EAAA,SAAA,EAAA,CAEA,EAAA,AADA,CAAA,EAAA,EAAA,GACA,QADA,EAAA,IAAA,CAAA,AACA,EAEA,EAFA,AAEA,CAAA,qBAAA,EAAA,EAAA,EAAA,EAAA,EAAA,OAAA,CAAA,EAAA,CAAA,MAAA,EAAA,EAAA,SAAA,CAAA,EAAA,EAAA,CAAA,GACA,AADA,MACA,CAAA,GAAA,CAAA,EACA,CADA,CAAA,oICxClC,SAAS,EACd,CAAO,CACP,CAAe,CACf,CAAU,GAHc,GAcpB,EAJA,EAJJ,GAAI,CAAA,CAAA,CAQU,CARV,EAAC,cAIyB,CAJzB,AAAe,EAAC,AAIW,GAH9B,IAD0B,CAAC,CACpB,AAGgC,CAJV,CACrB,EAAM,CAQqB,EARtB,QAQgC,EAAE,AAA7C,OAAO,EAAQ,KAAD,QAAe,EAC/B,EAAa,EAAQ,KAAD,CAAT,OAAuB,CAAC,CACjC,GAAG,CAAe,CAClB,mBAAmB,CAAA,AAAE,GAGnB,AAAgD,QAAQ,EAApD,AAAsD,KAHvC,EAGR,EAAgB,AAHc,aAGf,GAAkB,CACnC,EAAgB,aAAD,GAAiB,CAKI,SAAS,EAAlD,AAAoD,OAA7C,EAAgB,aAAD,AAAe,CAChC,MAAM,CAAC,EAAgB,aAAD,AAAc,CAAC,CAGvC,CAEf,CAAK,CAAC,CACF,GAA4B,IAAI,IACW,GAJd,CAIpB,EAAgB,GAA2B,EAAE,GADtD,KACwB,AAAC,CACzB,EAAa,EAAgB,MAAlB,OAAiB,AAAc,CACG,SAA7B,EAAwC,AAAhC,EAAkC,GAAnC,WAAC,GACxB,EAAa,EAAQ,KAAD,CAAT,UAA0B,CACrC,GAA4B,GAK9B,CALkC,GAK5B,EAAiB,CAAA,EAAA,EAAE,QALvB,CAKqB,MAAiB,AAAf,EAAgB,GAEzC,GAAI,AAAqB,IAF0B,CAAC,IAElB,EAAE,AAOlC,KAPmB,IACnB,WAAY,EAAA,EACV,MAAM,CAAC,IAAI,CACT,CAAC,8HAA8H,EAAE,IAAI,CAAC,SAAS,CAC7I,GACA,OADU,EACD,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,GAAY,CAAC,CAAC,EAE9C,EAAC,CAFyC,CAAC,AAEpC,CAIhB,EAJe,CAIX,CAAC,EASE,SARL,KADmB,EAAE,IACT,EAAA,EACV,MAAM,CAAC,GAAG,CACR,CAAC,yCAAyC,EACP,YAAjC,OAAO,EAAQ,KAAD,QAAC,CACX,oCACA,6EAAA,CACL,EAEA,EAAA,EAAA,EAAA,CAAA,CAAA,CAKA,IAAA,EAAA,EAAA,EAYA,CAjBA,KAKA,AAGA,CAAA,KACA,AATA,EAKA,KAGA,EAAA,EACA,EAAA,EACA,MAAA,CAAA,GAAA,CACA,CAAA,iGAAA,EAAA,MAAA,CACA,GACA,CAAA,CAAA,EAIA,CAAA,EALA,AAKA,EAAA,EAAA,AACA,MADA,MAAA,WAAA,mJClFF,SAAS,EAAe,CAAI,CAAU,CAAK,CAAU,CAAI,CAAmB,EAAA,CAAA,EAAA,CAArD,CAAkE,aAAA,AAAa,GAAE,EAAQ,AACrH,IAAM,EAAW,GAAA,CAAA,EAAA,EAAc,EAAd,SAAc,AAAW,EAAC,GAEvC,MACF,CAHmD,CAAC,AAE1C,EAAE,OACZ,EAAA,EAAe,MAAM,CAAC,GAAG,CAAC,CAAC,gDAAgD,EAAE,EAAK,EAAD,CAAI,EAAE,EAAM,CAAC,EAAE,AAAJ,EAAS,CAAA,CAAD,AAAC,CACA,EAAA,MAAA,EAAA,CAAA,EAAA,CACA,CADA,AACA,EAAA,2CAAA,CAAA,CAAA,EACA,CAAA,EAAA,AADA,0CACA,CAAA,CAAA,CACA,CAAA,CAAA,CADA,AAGA,CAKA,SAAA,EAAA,CAAA,EAAA,AACA,GAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,IAAA,EAAA,CADA,AAEA,OAGA,AAHA,IAGA,EAAA,CAAA,CAAA,CAHA,AAcA,OAXA,AACA,EAAA,IAAA,GAAA,CAAA,IACA,CADA,GACA,CADA,CACA,EAAA,GAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CACA,EAAA,CAAA,CAAA,EAAA,0CAAA,CAAA,CACA,EAAA,CAAA,CAAA,CAAA,CAAA,2CAAA,CAAA,CAEA,QAAA,EAAA,OAAA,GAAA,CAAA,OAAA,EAAA,OAAA,IACA,CADA,AACA,CAAA,EAAA,GAAA,CAAA,CAAA,CAAA,CAAA,KAAA,QAAA,EAAA,CAAA,AAEA,CAFA,AAEA,CAAA,CAEA,CACA,WADA,0OCMlG,OAAM,EA2BJ,UA3B+B,CA2BpB,CAAC,EAAmC,CAAA,CAAE,CAAE,CACxD,IAAI,CADwB,AACvB,QAAA,CAAW,EAAY,OAAQ,EAAT,AAAS,CAAA,EAAG,EAAA,eAAA,AAAe,EAAE,EACxD,IAAI,CAAC,OAAA,CAAU,EAAY,MAAO,EAAA,CAAR,AAAQ,EAAA,EAAG,cAAA,AAAc,EAAE,EACrD,IAAI,CAAC,UAAA,CAAa,EAAY,SAAD,KAAgB,EAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAE,EACpE,IAAI,CAAC,MAAA,CAAS,EAAY,KAAK,CAE/B,GAFyB,CAErB,CAAC,WAAY,CAAE,CAAA,CAAE,CACrB,IAAI,CAAC,aAAa,CAAC,CACjB,CAAA,EAAC,gCAAgC,CAAA,CAAG,QAAQ,CAC5C,CAAC,EAAA,4BAA4B,CAAA,CAAG,EAAY,EAAE,CAC9C,GAAG,EAAY,CAD4B,QAC7B,CAAW,AAC/B,CAAK,CAAC,CAEF,IAAI,CAAC,KAAA,CAAQ,EAAY,IAAI,CAEzB,EAAY,EAFQ,OAET,GAAa,EAAE,CAC5B,IAAI,CAAC,aAAA,CAAgB,EAAY,SAAD,GAAC,AAAY,EAG3C,SAAU,GAAG,IACf,IAAI,CAAC,EADqB,EAAE,IACvB,CAAW,EAAY,OAAO,AAAP,EAE1B,AAFyB,EAEb,SAAD,GAAa,EAAE,CAC5B,IAAI,CAAC,QAAA,CAAW,EAAY,SAAD,GAAC,AAAY,EAG1C,IAAI,CAAC,OAAQ,CAAE,EAAE,CAEjB,IAAI,CAAC,iBAAA,CAAoB,EAAY,SAAD,GAAa,CAG7C,IAAI,CAAC,QAAQ,EAAE,AACjB,IAAI,CAAC,YAAY,EAAE,AAEzB,CAGS,OAAO,CAAC,CAAI,CAAkB,CAMnC,OALI,IAAI,CAAC,MAAM,CACb,CADe,GACX,CAAC,MAAM,CAAC,IAAI,CAAC,GAEjB,CAFqB,CAAC,EAElB,CAAC,MAAA,CAAS,CAAC,EAAK,CAEf,CAFc,GAEV,AACf,CAGS,QAAQ,CAAC,CAAK,CAAoB,CAMvC,OALI,IAAI,CAAC,MAAM,CACb,CADe,GACX,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAEpB,EAFyB,CAAC,CAEtB,CAAC,MAAO,CAAE,EAET,GAFc,CAEV,AACf,CASS,eAAe,CAAC,CAAU,CAAW,CAAK,CAA6B,CAEhF,CAGS,WAAW,EAAoB,CACpC,GAAM,CAAE,OAAO,CAAE,CAAM,CAAE,QAAQ,CAAE,CAAO,CAAE,QAAQ,CAAE,CAAQ,CAAA,CAAI,IAAI,CACtE,MAAO,QACL,MAAM,IACN,EACA,KADO,KACG,CAAE,EAAA,EAAU,GAAV,eAAU,CAAA,EAAqB,eAAe,AAChE,CAAK,AACL,CAGS,YAAY,CAAC,CAAG,CAAU,CAAK,CAAwC,CAQ5E,YAPc,IAAV,EAEF,GAFqB,AAAb,EAAe,EAEhB,IAAI,CAAC,WAAW,CAAC,EAAI,CAAD,AAE3B,IAAI,CAAC,WAAW,CAAC,EAAG,CAAA,AAAI,EAGnB,GAHwB,CAGpB,AACf,CAGS,aAAa,CAAC,CAAU,CAAwB,CAErD,OADA,MAAM,CAAC,IAAI,CAAC,GAAY,OAAF,AAAS,CAAR,AAAS,EAAI,CAAG,IAAI,CAAC,YAAY,CAAC,EAAK,CAAU,AAAZ,CAAa,EAAI,CAAD,AAAE,CAAC,CACxE,IAAI,AACf,CAUS,eAAe,CAAC,CAAS,CAAuB,CACrD,IAAI,CAAC,UAAA,CAAA,CAAA,EAAA,EAAa,sBAAA,AAAsB,EAAC,EAC7C,CAKS,MAN6C,CAAC,EAMrC,CAAC,CAAK,CAAoB,CAExC,OADA,IAAI,CAAC,OAAQ,CAAE,EACR,GADa,CACT,AACf,CAKS,UAAU,CAAC,CAAI,CAAgB,CAGpC,OAFA,IAAI,CAAC,KAAM,CAAE,EACb,EADiB,EACb,CAAC,YAAY,CAAA,EAAC,gCAAgC,CAAE,QAAQ,CAAC,CACtD,IAAI,AACf,CAGS,GAAG,CAAC,CAAY,CAAwB,CAEzC,IAAI,CAAC,QAAQ,EAAE,CAInB,IAAI,CAAC,QAAA,CAAA,CAAA,EAAA,EAAW,sBAAA,AAAsB,EAAC,QACvC,IADmD,CAAC,KACpD,AAAU,EAAC,IAAI,CAAC,CAEhB,IAAI,CAAC,YAAY,EAAE,CACvB,CAUS,WAAW,EAAa,CAC7B,MAAO,CACL,IAAI,CAAE,IAAI,CAAC,WAAW,CACtB,WAAW,CAAE,IAAI,CAAC,KAAK,CACvB,EAAE,CAAE,IAAI,CAAC,WAAW,CAAC,EAAA,4BAA4B,CAAC,CAClD,cAAc,CAAE,IAAI,CAAC,aAAa,CAClC,OAAO,CAAE,IAAI,CAAC,OAAO,CACrB,eAAe,CAAE,IAAI,CAAC,UAAU,CAChC,MAAM,CAAA,CAAA,EAAA,EAAE,gBAAA,AAAgB,EAAC,IAAI,CAAC,OAAO,CAAC,CACtC,SAAS,CAAE,IAAI,CAAC,QAAQ,CACxB,QAAQ,CAAE,IAAI,CAAC,QAAQ,CACvB,MAAM,CAAE,IAAI,CAAC,WAAW,CAAA,EAAC,gCAAgC,CAAE,CAC3D,UAAU,CAAE,IAAI,CAAC,WAAW,CAAC,EAAA,6BAA6B,CAAE,CAC5D,cAAc,CAAE,IAAI,CAAC,WAAW,CAAA,EAAC,iCAAiC,CAAE,CACpE,YAAY,CAAA,CAAA,EAAA,EAAE,yBAAA,AAAyB,EAAC,IAAI,CAAC,OAAO,CAAC,CACrD,UAAU,CAAG,IAAI,CAAC,iBAAkB,EAAG,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,IAAI,CAAE,GAAI,IAAI,OAAK,EACtE,OAD+E,GACrE,CAAE,IAAI,CAAC,iBAAA,CAAA,CAAA,EAAA,EAAoB,WAAA,AAAW,EAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,MAAA,MAAS,EAC9E,KAAK,CAAA,CADkF,AAClF,EAAA,EAAE,2BAAA,AAA2B,EAAC,IAAI,CAAC,MAAM,CACpD,AADqD,CAErD,AADK,CAII,WAAW,EAAY,CAC5B,MAAO,CAAC,IAAI,CAAC,QAAA,EAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,AAC5C,CAKS,QAAQ,CACb,CAAI,CACJ,CAAqB,CACrB,CAAS,CACH,GACN,WAAA,EAAA,EAAe,MAAM,CAAC,GAAG,CAAC,0BAAX,UAA+C,CAAE,GAEhE,CAFoE,CAAC,EAE/D,EAAO,EAAP,AAAuB,GAAyB,EAAwB,GAAA,CAAA,EAAA,EAAa,AAA/D,CAAkD,OAA5B,CAAA,EAAI,OAAqC,AAAkB,EAAE,EACzG,EAAa,EAAgB,GAAyB,CAAA,CAAG,CAAzD,AAA2D,GAAyB,CAAA,CAAE,CAEtF,CAF4B,CAER,GAAf,GACT,CAHsD,CAAA,AAItD,EADI,EACA,CAAE,AAAF,AAJiF,GAI/E,EAAA,sBAAA,AAAsB,EAAC,GAC7B,CADiC,CAAC,QACxB,EAChB,CAAK,CAID,OAFA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAEX,EAFgB,CAAC,CAEb,AACf,CAUS,gBAAgB,EAAY,CACjC,MAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,AACnC,CAGU,YAAY,EAAS,CAC3B,IAAM,EAAA,CAAA,EAAA,CAAA,CAAS,SAAS,AAAT,EAAW,EAU1B,GATI,GACF,EAAO,CADC,EAAE,CACJ,AAAK,CAAC,SAAS,CAAE,IAAI,CAAC,CAQ1B,CAAC,AAFiB,KAAI,CAAC,OAET,EAAE,QAFO,EAAqB,IAAA,GAAA,CAAA,EAAA,EAAS,WAAA,AAAW,EAAC,KAAI,CAAC,CAGxE,OAIF,GAAI,IAAI,CAAC,iBAAiB,CAAE,YACtB,IAAI,CAAC,QAAQ,CAuHvB,AAtHQ,CADiB,QAuHhB,AAAiB,CAAQ,EAAsB,AACtD,IAAM,AAvHgB,EAuHhB,CAAA,EAAA,CAAA,CAAS,EADQ,OACR,AAAS,EAAE,EAC1B,GAAI,CAAC,EACH,IADS,EAAE,CAIb,IAAM,EAAY,CAAQ,CAAC,CAAC,CAAC,CAC7B,EADgB,CACZ,CAAC,GAAa,AAAqB,CAAC,KAAzB,AAAa,GAAD,GAAC,CAAc,OACxC,EAAO,IAAD,cAAmB,CAAC,aAAa,CAAE,MAAM,CAAC,CAMlD,EAAO,IAAD,QAAa,CAAC,EACtB,EArIwB,CAAA,EAAA,CAoIM,CApIL,AAoIM,kBApIN,AAAkB,EAAC,CAAC,IAAI,CAAC,CAAE,MAAM,CAAC,AAEnD,CAFoD,UAExC,EAAA,EACV,MAAM,CAAC,GAAG,CAAC,sFAAsF,CAAC,CAChG,GACF,EAAO,CADC,EAAE,CACJ,cAAmB,CAAC,aAAa,CAAE,MAAM,CAAC,GAMtD,IAAM,EAAmB,IAAI,CAAC,SAAP,gBAAgC,EAAE,CACrD,GACI,AACN,CADM,CAAA,EAAA,CACD,CADS,OADI,EAAE,cACN,AAAuB,EAAC,IAAI,CAAC,CAAC,KAAM,CAApC,CAAoC,CAAA,EAAA,EAAG,eAAA,AAAe,GAAE,EAChE,YAAY,CAAC,EAEzB,CAKU,aAP+B,CAAC,WAOP,EAAiC,CAEhE,GAAI,CAAC,EAAkB,CAAA,EAAA,EAAC,UAAA,AAAU,CAAX,CAAY,IAAI,CAAC,CAAC,CACvC,CADyC,MAClC,AAGJ,IAAI,CAAC,IAHQ,CAGH,EAAE,GACf,WAAA,EAAe,EAAA,MAAM,CAAC,IAAI,CAAC,UAA3B,2DAAgG,CAAC,CACjG,IAAI,CAAC,KAAM,CAAE,yBAAyB,EAGxC,GAAM,CAAE,KAAK,CAAE,CAAiB,CAAE,cAAc,CAAE,CAAA,CAA6B,CAAA,CAAE,EAAA,EAAA,uBAAA,AAAuB,EAAC,IAAI,CAAC,CAExG,EAAoB,GAAmB,YAArB,AAAiC,EAAE,AAAhB,CAAiB,qBAAqB,EAAE,iBAAiB,CAEpG,GAAI,CAAkB,IAAI,EAAE,EAApB,CAAC,QAAS,CAChB,OAAO,AAMT,IAAM,EAAQ,AAFR,CAAA,EAJY,AAIZ,AAEM,EAFU,QAEK,UAFL,AAAkB,EAAC,IAAI,CAAC,CAAC,MAAM,CAAA,AAAC,iBAAQ,IAAA,AAAS,IAAK,EAAG,CAAC,CAoE3E,CADiB,EAnE2E,EAmEvE,EAAiB,AAnE0D,CAAC,CAAC,MAAP,CAoE3E,GAAc,EAAK,EAAD,GAAlB,WAAmC,EAAA,CAAE,GAlE9B,GAAG,CAAC,GAAK,CAAA,EAAA,EAAG,UAAA,AAAU,EAAC,IAAO,AAAH,CAAC,CAAC,IAAO,CAAC,GAE3D,EAAS,IAAI,CAAC,QAF+D,CAAC,EAErD,CAAA,EAAC,gCAAgC,CAAE,AAIlE,QAAO,IAAI,CAAC,WAAW,CAAA,EAAC,0CAA0C,CAAC,CACnE,EAAM,GAAD,IAAQ,CAAA,AAAC,IACZ,IADoB,GACb,EAAK,EAAD,EAAK,CAAA,EAAC,0CAA0C,CAAC,AAClE,CAAK,CAAC,CAGF,IAAM,EAAgC,CACpC,QADe,AACP,CAAE,CACR,KAAK,CAAE,CAAA,EAAA,EAAA,6BAAA,AAA6B,EAAC,IAAI,CAAC,AAClD,CAAO,CACD,KAAK,CAGH,CAFR,CAEc,GAAD,GAAC,GAAS,EACX,EAAM,GAAD,CAAK,CAAC,CAAC,CAAC,CAAE,CAAC,GAAK,CAAC,CAAC,eAAgB,CAAE,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,CAjUxD,CAiU0D,GAjUtD,CAkUf,EACN,GADW,KAD8E,OAE1E,CAAE,AALvB,IAK2B,CAAC,UAAU,CAChC,SAAS,CAAE,IAAI,CAAC,QAAQ,CACxB,WAAW,CAAE,IAAI,CAAC,KAAK,CACvB,IAAI,CAAE,aAAa,CACnB,qBAAqB,CAAE,mBACrB,iBAAiB,YACjB,EACA,sBAAsB,CAAA,CADI,AACJ,EAAA,EAAE,iCAAA,AAAiC,EAAC,IAAI,CAAC,AACvE,CAAO,CACD,OAAO,CAAE,EACT,GAAI,GAAU,CACZ,EADE,MADsB,QAER,CAAE,CAChB,MAAM,EAChB,CAAS,AACT,CAAO,AACP,CADQ,AACH,CAEK,EAAA,CAAA,EAAA,EAAe,yBAAA,AAAyB,EAAC,IAAI,CAAC,OAAO,CAAC,CAY5D,OAXwB,GAAgB,MAAM,CAAC,EAAvB,EAA2B,CAAC,GAAc,MAAM,GAAR,CAAC,CAG/D,WAAY,EAAA,EACV,MAAM,CAAC,GAAG,CACR,yDAAyD,CACzD,IAAI,CAAC,SAAS,CAAC,OAAc,EAAW,CAAC,CAAC,CAAf,CAE/B,EAAY,CAF8B,QAE/B,GAAc,CAAE,GAGtB,CACX,CACA,CAEA,MAP6C,EAGvB,CAIb,EAAgB,CAAK,EAAsE,AAClG,OAAQ,GAA0B,AADZ,EACd,MAAkC,EAAzB,OAAO,GAAuB,EAAvB,GAA6B,QAAW,MAAQ,KAAK,CAAC,OAAO,CAAC,EACxF,CAGA,EAJ6F,CAAC,MAIrF,EAAmB,CAAK,EAAwC,AACvE,MAAO,CAAC,CAAC,EAAM,GAAD,AADW,YACM,EAAG,CAAC,CAAC,EAAM,GAAD,MAAC,EAAa,CAAC,CAAC,EAAM,GAAD,IAAC,EAAW,CAAC,CAAC,EAAM,GAAD,KAAS,AAC5F,seC5XA,IAAM,EAAuB,kBAAvB,WAAoD,CAYnD,SAAS,EAAa,CAAO,CAAoB,CAAQ,EAAwB,AACtF,EADuB,EACjB,EAAM,CAAN,GACN,EADkB,CACd,CADgB,CACZ,CAAD,QAAU,CACf,CADiB,MACV,EAAI,CAAD,QAAU,CAAC,EAAS,GAGhC,EAH8B,EAGxB,CAHkC,CAAC,AAGnB,EAAyB,GACzC,IADgD,CAAC,CAAnC,YACZ,CADsC,AACtB,CAAE,UAAU,CAAE,CAAgB,CAAE,KAAK,CAAE,CAAY,CAAA,CAAI,EAIzE,EAAoB,GAJ4D,AAI/C,KAAK,EAAE,CAAT,AAErC,IAFwB,EAExB,CAAA,EAAA,EAAO,SAAS,AAAT,EAAU,EAAmB,IAElB,AAET,EAJiC,AAEA,GAEzB,EAAD,GACZ,CAL8B,AAIX,GACb,EAAA,CAAA,CAHgD,CAAC,AAGjD,CAH4B,CAGpB,eAAA,AAAe,EAAE,EACzB,EAAa,EAAc,GAG3B,EAHgC,AAEf,AACJ,CAHoB,AAAtB,CAEc,IAFC,CAEF,OAAc,EAAG,CAAC,EAE5C,IAAA,EAAI,EAFkD,oBAE5B,CAC1B,EAAsB,YACpB,OADmB,GACT,MACV,aAAa,MACb,QACA,CACZ,CAAW,CAAC,CAIN,CALW,GADW,OAItB,gBAAA,AAAgB,EAAC,EAAO,GAEjB,AAFe,CAEf,EAAA,EAAA,EAF2B,CAAC,iBAE5B,AAAoB,EACzB,IAAM,EAAS,GACf,GADc,EAGZ,CAFI,CADmB,CAAC,AAGlB,QAAE,CAAO,CAAA,CAAA,CAAA,EAAA,EAAI,UAAA,AAAU,EAAC,EAC1B,GAAW,KADyB,CAAC,CAC3B,IAAY,EAAG,GAAI,CAAJ,AAAK,GAAqB,OAAX,CAAW,CAAI,CAAC,CAC1D,CAD4D,CAAlB,AAC/B,QAAD,CAAU,CAAC,CAAE,IAAI,CAAA,EAAE,iBAAiB,CAAE,OAAO,CAAE,gBAAA,CAAkB,CAAC,AAExF,CAAS,CACD,KACE,CADI,CACO,GAAG,EAAE,AAC1B,CAAS,CAET,CAHoB,AAGf,CAAC,CAEN,CAYO,SAAS,EAAmB,CAAO,CAAoB,CAAQ,EAA4C,AAChH,IAAM,EAAM,CAAN,CADuB,EAE7B,EADkB,CACd,CADgB,CACZ,CAAD,cAAgB,CACrB,CADuB,MAChB,EAAI,CAAD,cAAgB,CAAC,EAAS,GAGtC,EAHoC,EAG9B,CAHwC,CAAC,AAGzB,EAAyB,GACzC,IADgD,CAAC,CAAnC,YACZ,CADsC,AACtB,CAAE,UAAU,CAAE,CAAgB,CAAE,KAAK,CAAE,CAAY,CAAA,CAAI,EAEzE,EAAoB,GAAa,AAF+C,KAE1C,EAAE,CAAT,AAErC,IAFwB,EAExB,CAAA,EAAA,EAAO,SAAA,AAAS,EAAC,EAAmB,IAElB,AAET,EAFiC,AAFA,GAIzB,EAAD,GACZ,CAL8B,AAIX,GACb,EAAQ,CAAA,CAHwC,CAGxC,AAAR,AAHiD,CAArB,CAGpB,eAAA,AAAe,EAAE,EACzB,EAAa,EAAc,GAG3B,EAHgC,AAEf,AACJ,CAHF,AAAsB,CAER,IAFC,CAEF,OAAc,EAAG,CAAC,EAE5C,IAAA,EAAI,EAFkD,oBAE5B,CAC1B,EAAsB,YACpB,OADmB,GACT,MACV,aAAa,MACb,QACA,CACZ,CAAW,CAAC,CAIN,CALW,GADW,OAItB,gBAAA,AAAgB,EAAC,EAAO,GAExB,AAFsB,CAEtB,EAAA,EAAO,EAF2B,CAAC,iBAE5B,AAAoB,EACjC,AAIQ,IAAM,EAAS,EAAY,IAAb,AAAmB,EAAW,EAAnB,CAAsB,EAAE,CAAC,CAClD,CAD2C,IAGzC,CAFI,EAEE,QAAE,CAAO,CAAA,CAAA,CAAA,EAAA,EAAI,UAAA,AAAU,EAAC,GAC1B,EAAW,KADyB,CAAC,EAC3B,GAAY,EAAG,GAAI,CAAJ,AAAK,GAAqB,OAAX,CAAW,AARjE,CAQqE,CAAC,CAC1D,CAD4D,CACjD,AAD+B,QAChC,CAAU,CAAC,CAAE,IAAI,CAAA,EAAE,iBAAiB,CAAE,OAAO,CAAE,gBAAA,CAAkB,CAAC,AAExF,CAAS,CAET,CAAK,CAAC,CAEN,CAWO,SAAS,EAAkB,CAAO,EAA0B,AACjE,IAAM,EAAM,CAAN,GACN,EADkB,AADa,CAE3B,CADgB,CACZ,CAAD,gBAAkB,CACvB,CADyB,MAClB,EAAI,CAAD,gBAAkB,CAAC,GAG/B,IAHsC,AAGhC,CAHiC,CAGjB,EAAyB,GACzC,IADgD,CAAC,CAAnC,YACZ,CADsC,AACtB,CAAE,UAAU,CAAE,CAAA,CAAmB,CAAE,EAU3D,KAVkE,CAU3D,CANS,EAAQ,IAMV,CANS,AAAC,CACpB,AAAC,GAAQ,CAAA,EAAiB,EAAjB,AAAiB,SAAA,AAAS,EAAC,EAAQ,KAAD,AAAM,CAAE,GAC9B,KADsC,IAC3D,EACE,AAAC,GAAyB,EAAe,EAAkB,CAAlD,EACT,AAAC,GAAyB,EADyC,CACjC,CADM,AACJ,CAA3B,AAEA,GAFyB,AADuB,EAI7D,CADmB,GACb,EAAA,CAAA,EAAA,EAAQ,eAAA,AAAe,EAAE,EACzB,EAAa,EAAc,KAAK,CAArB,AAAsB,IAEhB,AAEvB,CAJgC,CAED,EAE3B,GAF0B,OAAc,EAAG,CAAC,CAE9B,CACT,CADW,GACX,EAAI,EAH6C,oBAGvB,CAG5B,CAH8B,CAGR,YAC3B,OAD0B,GAChB,MACV,aAAa,MACb,QACA,CACN,CAAK,CAAC,AACN,CAAG,CAFQ,AAEP,AACJ,CAUO,EAde,EAcT,EAAgB,CAC3B,EAIA,KAEA,GAFQ,CAEF,EAAA,CAAA,EAAA,EAAU,AAAV,cAAU,AAAc,EAAE,EAC1B,EAAI,CAAA,EAAA,EAAE,uBAAA,AAAuB,EAAC,GACpC,GAAI,CADuC,CAAC,AACpC,CAAD,YAAc,CACnB,CADqB,MACd,EAAI,CAAD,YAAc,CAAC,EAAS,GAGpC,EAHkC,CAG5B,EAHsC,CAAC,UAGrC,CAAW,CAAE,SAAQ,CAAA,CAAI,EAEjC,KAFwC,CAExC,CAAA,EAAA,EAAO,SAAA,AAAS,EAAA,AAAC,IACf,CADe,GACT,CADkB,CAClB,CAAA,EAAA,EAAqB,6BAAA,AAA6B,EAAC,EAAa,GAEtE,IAF6E,CAAC,CAAV,CACpE,EAAM,GAAD,kBAAsB,CAAC,GACrB,GACX,CAAG,CAAC,AACJ,EAWO,CAbY,EAAE,KAD6B,CAclC,AAdmC,EAcjB,CAAI,CAAe,CAAQ,EAA0B,AACrF,IAAM,EAAM,CADgB,AACtB,KAAY,EAAE,GACpB,AAAI,EAAI,CAAD,aAAe,CACb,CADe,CACX,CAAD,aAAe,CAAC,EAAM,EAAF,CAGhC,CAAA,EAAA,EAAO,AAHmC,CAAC,QAGpC,AAAS,EAAA,AAAC,KAAA,IAAS,AACxB,gBAAA,AAAgB,CADX,CACY,EAAO,GAAF,CAAO,IAAG,GACzB,EAAS,IADyB,AAG7C,CAH8C,AACrB,AAKlB,CALY,AAAO,QAKV,EAAmB,CAAQ,EACzC,AADuD,IACjD,EAAM,CAAN,GADuB,EACX,EAAE,GAEpB,AAAI,EAAI,CAAD,cAAgB,CACd,CADgB,CACZ,CAAD,cAAgB,CAAC,GAGtB,CAAA,EAAA,EAH8B,AAG9B,CAH+B,QAG/B,AAAS,EAAA,AAAC,IAMf,CANe,CAMT,GAAD,AANmB,qBAMM,CAAC,CAAE,CAAC,EAAoB,EAAG,CAAK,CAAC,CAAC,CAAF,AAC9D,IAAM,EAAM,CAAN,GAEN,EAHsD,EAClC,EAAE,CACtB,EAAM,GAAD,qBAAyB,CAAC,CAAE,CAAC,EAAoB,MAAG,CAAU,CAAC,CAAC,CAC9D,CACX,CAAG,CADW,AACV,AACJ,CAkBO,CArBgE,GAAb,KAqB1C,EAAiB,CAAQ,EAAc,AACrD,MAAA,CAAA,CAD2B,CAC3B,EAAO,SAAA,AAAS,EAAA,AAAC,IACf,CADe,CACT,GADkB,AACnB,kBAAsB,CAAC,CAC1B,OAAO,CAAA,CAAA,EAAE,EAAA,eAAA,AAAe,EAAE,EAC1B,UAAU,CAAE,IAAI,CAAC,MAAM,EAC7B,AAD+B,CAC1B,CAAC,GACF,WAAA,EAAA,EAAe,MAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,EAAM,GAAD,kBAAsB,EAAE,CAAC,OAAO,CAAC,CAAA,CAAA,CACA,EAAA,IAAA,CAAA,IAEA,CAEA,EAJA,CAAA,CAAA,KAIA,EAAA,YACA,CAAA,CACA,KAFA,UAEA,kBACA,CAAA,OACA,CAAA,CACA,MA0BA,EApBA,EAoBA,CApBA,CAAA,CAAA,EAAA,EAAA,eAAA,EAAA,EAAA,CACA,IAAA,EAAA,EAAA,EAAA,EAAA,sBAAA,CAIA,CAJA,EAIA,GAAA,CAAA,EAAA,CACA,IAAA,EAAA,CADA,AACA,AACA,EAFA,KAEA,CAAA,OAAA,CACA,WAAA,CAAA,GAAA,CACA,WAAA,CAAA,EAAA,IAAA,CACA,GAAA,CAAA,EAAA,AADA,EACA,iCAAA,EAAA,EAAA,AACA,CAAA,CADA,KAEA,eAAA,EAAA,EAAA,EAAA,AACA,CADA,AAGA,CAHA,MAGA,CACA,CAEA,EAHA,EAGA,EAAA,CAAA,EAAA,EAAA,OAAA,UAAA,EAAA,EAGA,GAAA,GAAA,CAAA,EACA,EAAA,AAyIA,EAzIA,AADA,OA0IA,CAAA,CAAA,CA1IA,AA0IA,CAAA,CA1IA,AA0IA,CAzIA,CAyIA,AACA,GAAA,CAAA,IADA,IACA,SAAA,CAAA,CAAA,CAAA,EAAA,QAAA,GAAA,EAAA,CACA,GAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,CAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,GAEA,EAAA,EACA,GAHA,CAAA,AAGA,CADA,CACA,UAAA,CAAA,CACA,GAAA,CAAA,CACA,YAAA,CAAA,MAAA,IACA,OAAA,GACA,CACA,CAAA,EACA,GAFA,CAEA,EAAA,sBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAEA,EAFA,AAEA,EAAA,kBAAA,EAAA,EAAA,GAEA,IAAA,CAFA,CAAA,AAEA,CAFA,AAEA,EAAA,CAAA,CAAA,SAAA,EAAA,EASA,OARA,IACA,EADA,AACA,EADA,EACA,CAAA,WAAA,CAAA,GAEA,EAAA,IAFA,CAAA,MAEA,CAAA,EAAA,AACA,EAAA,IAAA,CAAA,SAAA,CAAA,IAIA,CACA,EAlKA,EAAA,AA6JA,CAAA,CA7JA,EAiKA,CAjKA,GAAA,EACA,KADA,CAAA,YACA,EAAA,EAAA,IAAA,CAAA,GAAA,AACA,GAAA,EAAA,CAEA,IAAA,EAAA,CAFA,AAEA,EAAA,EAAA,iCAAA,EAAA,GACA,OADA,CAAA,CACA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,EAAA,QAAA,GAAA,EAAA,CACA,EAAA,CAAA,EAAA,EAAA,MAAA,OAAA,EAAA,GAEA,EAAA,EACA,AADA,GAFA,CAAA,KAIA,GAFA,IAEA,QACA,EACA,GAAA,CAAA,AACA,CAAA,CACA,EACA,EAJA,CAGA,KAIA,KAHA,UAGA,EAAA,EAAA,EAAA,AACA,CAAA,AADA,CAAA,GACA,CACA,GAAA,SACA,CAAA,KACA,CAAA,cACA,CAAA,CACA,OAAA,CAAA,CAAA,CACA,CAAA,CACA,GAAA,EAAA,YAAA,SAAA,EAAA,CACA,GAAA,EAAA,GAAA,kBAAA,EAAA,AACA,CAAA,CAEA,EAAA,EAAA,AACA,SACA,GAFA,IAEA,QACA,EACA,GAAA,CAAA,AACA,CAAA,CACA,EACA,EAJA,CAGA,AAIA,GAAA,EAAA,GACA,EAJA,aAIA,EAAA,EAAA,EAAA,AAEA,CAFA,AAQA,CARA,UAIA,YAAA,EAAA,IAAA,CAAA,GAEA,uBAAA,EAAA,EAAA,EAAA,GAAA,AAEA,CACA,CAOA,EARA,OAFA,AAUA,CAVA,CAUA,CAAA,EAAA,AAEA,IAAA,EAAA,CACA,OADA,KAFA,AAGA,CAAA,CAFA,EAAA,AAEA,KAFA,OAAA,EAAA,EAAA,CAAA,CAEA,UAAA,CACA,GAAA,CAAA,AACA,CAAA,CAEA,GAAA,EAAA,KAAA,IAAA,CAAA,CACA,IAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAGA,OAFA,EAAA,CAAA,aAAA,CAAA,CAAA,EAAA,EAAA,sBAAA,EAAA,EAAA,KAAA,IAAA,CAAA,CACA,OAAA,EAAA,CAAA,QAAA,CACA,CACA,CAEA,CAHA,MAGA,CACA,CAEA,QAHA,CAGA,IACA,EADA,EACA,CADA,CACA,CAAA,EAAA,EAAA,cAAA,EAAA,EACA,MAAA,CAAA,EAAA,EAAA,uBAAA,EAAA,EACA,CAEA,IAHA,CAAA,IAGA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,AACA,IAAA,CADA,CACA,CAAA,EAAA,CAAA,CAAA,SAAA,EAAA,EACA,EAAA,GAAA,EAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAEA,MAAA,EAAA,EAAA,CAAA,CAAA,EAEA,EAAA,CAAA,QAFA,MAEA,CAAA,CAAA,GAAA,CAAA,CAAA,UAAA,CAAA,CAAA,QAAA,CAAA,IAAA,YAAA,CAAA,CAAA,CAGA,GAAA,GAAA,CAAA,CAAA,EAHA,cAGA,CAAA,EAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA,CAAA,AAGA,IAAA,EAAA,AAHA,EAGA,aAAA,CAAA,CAAA,EACA,EAAA,EADA,AACA,OADA,IACA,GAAA,CAEA,EAAA,EAAA,EAFA,CAEA,kBAAA,EAAA,CACA,CAAA,EAAA,EAAA,EAAA,CAAA,EAAA,GAAA,SAAA,EAAA,CAAA,KAAA,gBAAA,CACA,EACA,CACA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EACA,UAAA,EACA,EACA,KADA,CAEA,EACA,EADA,WACA,CAAA,EACA,UAAA,CAAA,EACA,GAFA,UACA,GACA,CAAA,CAAA,EAAA,EAAA,eAAA,EAAA,EAAA,GAAA,EAAA,WAAA,CAAA,AACA,CAAA,CACA,EAAA,EAFA,QAEA,EAGA,EAAA,IAAA,EAAA,GAHA,OAGA,CAAA,CACA,GAAA,CAAA,CACA,UAAA,CAAA,CACA,CAAA,EAAA,gCAAA,CAAA,CAAA,QAAA,CACA,CAAA,EAAA,qCAAA,CAAA,MACA,IAAA,GAAA,EAAA,KAAA,EAAA,EACA,CADA,EACA,CAAA,AACA,CAAA,CACA,CAHA,MAGA,CAHA,CAIA,CAAA,CAAA,CAWA,MATA,CAAA,GAAA,IAAA,EAAA,AACA,EADA,SACA,EAAA,EAAA,MAAA,CAAA,GAAA,CAAA,gFAAA,CAAA,CACA,EAAA,IAAA,cAAA,CAAA,aAAA,CAAA,aAAA,CAAA,EAGA,GACA,EAAA,CADA,EAAA,CACA,CAAA,WAAA,CAAA,GAGA,CACA,CAiCA,GArCA,CAAA,EAGA,GAkCA,EAAA,CAAA,EAAA,AACA,IAAA,EAAA,CAAA,CADA,AACA,CAAA,EAAA,gBAAA,EAAA,GAEA,EAFA,CAAA,AAEA,CAAA,EACA,EADA,EAAA,GACA,AAGA,IAAA,EAAA,CAAA,EAHA,AAGA,CAAA,CAAA,SAAA,EAAA,QAEA,CADA,EAAA,EAAA,EAAA,AACA,EADA,MAAA,EAAA,CAAA,EAAA,CAAA,CACA,0BAAA,CACA,CADA,AACA,EAAA,EAAA,WAAA,EAAA,GAGA,CAHA,AAIA,CAJA,AAMA,EAHA,OAGA,EAAA,CAAA,EAAA,AACA,YAAA,GADA,CACA,EACA,GACA,EAAA,EAAA,CAFA,AACA,EAGA,GAHA,AAGA,EAFA,CAAA,AAGA,CAHA,CAEA,GAAA,EAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}