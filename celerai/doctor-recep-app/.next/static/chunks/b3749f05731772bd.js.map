{"version": 3, "sources": ["turbopack:///[project]/node_modules/lucide-react/src/icons/house.ts", "turbopack:///[project]/src/components/shared/dashboard-header.tsx", "turbopack:///[project]/src/components/templates/templates-sidebar.tsx", "turbopack:///[project]/src/components/templates/template-editor.tsx", "turbopack:///[project]/node_modules/lucide-react/src/icons/sparkles.ts", "turbopack:///[project]/src/components/templates/coming-soon-overlay.tsx", "turbopack:///[project]/src/components/templates/templates-interface.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n", "'use client'\n\nimport Link from 'next/link'\nimport { LogOut, Settings, Phone, Stethoscope, X, Home } from 'lucide-react'\nimport { logout } from '@/lib/actions/auth'\nimport { createContactRequest } from '@/lib/actions/contact-requests'\nimport { <PERSON> } from '@/lib/types'\nimport { useState } from 'react'\n\ninterface DashboardHeaderProps {\n  user: Doctor | null\n}\n\nexport function DashboardHeader({ user }: DashboardHeaderProps) {\n  const [showContactModal, setShowContactModal] = useState(false)\n  const [contactError, setContactError] = useState<string | null>(null)\n\n  const [contactSubject, setContactSubject] = useState('general')\n  const [contactMessage, setContactMessage] = useState('')\n  const [isSubmittingContact, setIsSubmittingContact] = useState(false)\n\n  const handleContactFounder = async () => {\n    // Just show modal - don't create contact request yet\n    setShowContactModal(true)\n    setContactError(null)\n    setContactSubject('general')\n    setContactMessage('')\n  }\n\n  const handleSubmitContactForm = async () => {\n    if (!contactMessage.trim()) {\n      setContactError('Please enter a message')\n      return\n    }\n\n    setIsSubmittingContact(true)\n    setContactError(null)\n\n    try {\n      if (user?.id) {\n        const result = await createContactRequest(\n          user.id,\n          `Subject: ${contactSubject}\\n\\nMessage: ${contactMessage}`,\n          contactSubject\n        )\n        if (!result.success) {\n          setContactError(result.error || 'Failed to create contact request')\n          console.error('Contact request failed:', result.error)\n        } else {\n          console.log('Contact request created successfully')\n          // Close modal after successful submission\n          setTimeout(() => setShowContactModal(false), 2000)\n        }\n      } else {\n        setContactError('User not found')\n      }\n    } catch (error) {\n      setContactError(error instanceof Error ? error.message : 'Unknown error')\n      console.error('Error creating contact request:', error)\n    } finally {\n      setIsSubmittingContact(false)\n    }\n  }\n\n  return (\n    <>\n    <header className=\"bg-white/80 backdrop-blur-sm shadow-sm border-b border-orange-200/50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between py-4 sm:py-6\">\n          {/* Left side - User info (hidden on mobile) */}\n          <div className=\"hidden sm:block flex-1 min-w-0\">\n            <div className=\"text-lg sm:text-xl font-bold text-slate-800\">\n              {/* Desktop: Single line with truncate */}\n              <h2 className=\"truncate\">\n                Dr. {user?.name || 'Doctor'}\n              </h2>\n            </div>\n            <p className=\"text-sm text-slate-600 truncate\">\n              {user?.clinic_name || 'Medical Practice'}\n            </p>\n          </div>\n\n          {/* Center - Celer AI Logo */}\n          <Link href=\"/dashboard\" className=\"flex items-center space-x-4 flex-shrink-0 mx-4 hover:opacity-80 transition-opacity\">\n            <div className=\"relative\">\n              <div className=\"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg\">\n                <Stethoscope className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\n              </div>\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping\"></div>\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full\"></div>\n            </div>\n            <div className=\"hidden sm:block text-center\">\n              <h1 className=\"text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent\">\n                Celer AI\n              </h1>\n              <p className=\"text-sm text-teal-600/80\">AI-Powered Healthcare</p>\n            </div>\n          </Link>\n\n          {/* Right side - Navigation & Actions */}\n          <div className=\"flex items-center gap-2 sm:gap-4 flex-1 justify-end\">\n            {/* Navigation Menu */}\n            <nav className=\"hidden md:flex items-center space-x-1\">\n              <Link\n                href=\"/dashboard\"\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-orange-50 hover:text-teal-700 transition-all duration-150 transform hover:scale-105 active:scale-95\"\n              >\n                <Home className=\"w-4 h-4 mr-2\" />\n                Dashboard\n              </Link>\n            </nav>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-wrap items-center gap-2 sm:gap-3\">\n              {/* Contact Founder */}\n              <button\n                type=\"button\"\n                onClick={handleContactFounder}\n                className=\"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95\"\n              >\n                <Phone className=\"w-4 h-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">Contact</span>\n              </button>\n\n              {/* Settings Link */}\n              <Link\n                href=\"/settings\"\n                prefetch={true}\n                className=\"inline-flex items-center px-2 sm:px-3 py-2 border border-orange-300 hover:border-teal-400 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-150 transform hover:scale-105 active:scale-95\"\n              >\n                <Settings className=\"w-4 h-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">Settings</span>\n              </Link>\n\n              {/* Logout */}\n              <form action={logout}>\n                <button\n                  type=\"submit\"\n                  className=\"inline-flex items-center px-2 sm:px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-md hover:shadow-lg transition-all duration-150 transform hover:scale-105 active:scale-95\"\n                >\n                  <LogOut className=\"w-4 h-4 sm:mr-2\" />\n                  <span className=\"hidden sm:inline\">Logout</span>\n                </button>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n\n    {/* Contact Modal */}\n    {showContactModal && (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n        <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 ease-out scale-100\">\n          <div className=\"relative\">\n            {/* Header */}\n            <div className=\"bg-gradient-to-r from-teal-500 to-emerald-600 rounded-t-2xl p-6 text-white\">\n              <button\n                onClick={() => setShowContactModal(false)}\n                className=\"absolute top-4 right-4 text-white hover:text-gray-200 transition-colors\"\n              >\n                <X className=\"w-6 h-6\" />\n              </button>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center\">\n                  <Phone className=\"w-6 h-6\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">Contact Founder</h3>\n                  <p className=\"text-emerald-100\">Get in touch with us</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6 space-y-4\">\n              {/* Contact Form */}\n              <div className=\"space-y-4\">\n                {/* Subject Selection */}\n                <div>\n                  <label className=\"block text-sm font-medium text-slate-700 mb-2\">\n                    Subject\n                  </label>\n                  <select\n                    value={contactSubject}\n                    onChange={(e) => setContactSubject(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-black bg-white\"\n                  >\n                    <option value=\"general\">General Feedback</option>\n                    <option value=\"technical\">Technical Issue</option>\n                    <option value=\"billing\">Billing Question</option>\n                    <option value=\"feature\">Feature Request</option>\n                    <option value=\"bug\">Bug Report</option>\n                  </select>\n                </div>\n\n                {/* Message Input */}\n                <div>\n                  <label className=\"block text-sm font-medium text-slate-700 mb-2\">\n                    Message\n                  </label>\n                  <textarea\n                    value={contactMessage}\n                    onChange={(e) => setContactMessage(e.target.value)}\n                    placeholder=\"Please describe your issue or feedback...\"\n                    rows={4}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none text-black bg-white placeholder-gray-500\"\n                  />\n                </div>\n\n                {/* Error/Success Messages */}\n                {contactError ? (\n                  <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                    <div className=\"text-sm text-red-700 font-medium\">\n                      ❌ {contactError}\n                    </div>\n                  </div>\n                ) : isSubmittingContact ? (\n                  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3\">\n                    <div className=\"text-sm text-blue-700 font-medium\">\n                      📤 Submitting your request...\n                    </div>\n                  </div>\n                ) : null}\n\n                {/* Submit Button */}\n                <button\n                  onClick={handleSubmitContactForm}\n                  disabled={isSubmittingContact || !contactMessage.trim()}\n                  className=\"w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                >\n                  {isSubmittingContact ? 'Submitting...' : 'Submit Request'}\n                </button>\n\n                {/* Divider */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-0 flex items-center\">\n                    <div className=\"w-full border-t border-gray-300\" />\n                  </div>\n                  <div className=\"relative flex justify-center text-sm\">\n                    <span className=\"px-2 bg-white text-gray-500\">or contact directly</span>\n                  </div>\n                </div>\n\n                {/* Direct Contact Options */}\n                <div className=\"text-center space-y-3\">\n                  <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n                    <div className=\"text-lg font-semibold text-slate-800 mb-2\">\n                      📞 +91 8921628177\n                    </div>\n                    <div className=\"text-sm text-slate-600\">\n                      Available: 9 AM - 9 PM IST\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <a\n                      href=\"tel:+918921628177\"\n                      className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                    >\n                      📞 Call Now\n                    </a>\n                    <a\n                      href=\"https://wa.me/918921628177\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                    >\n                      💬 WhatsApp\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )}\n\n    </>\n  )\n}", "'use client'\n\nimport { Plus, FileText, Clock } from 'lucide-react'\nimport { formatRelativeTime } from '@/lib/utils'\n\ninterface Template {\n  id: string\n  name: string\n  content: string\n  created_at: string\n  updated_at: string\n}\n\ninterface TemplatesSidebarProps {\n  templates: Template[]\n  selectedTemplate: Template | null\n  onSelectTemplate: (template: Template) => void\n  onNewTemplate: () => void\n}\n\nexport function TemplatesSidebar({ \n  templates, \n  selectedTemplate, \n  onSelectTemplate, \n  onNewTemplate \n}: TemplatesSidebarProps) {\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-orange-200/50\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-slate-800\">Templates</h2>\n          <button\n            onClick={onNewTemplate}\n            className=\"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg\"\n          >\n            <Plus className=\"w-4 h-4\" />\n            <span className=\"hidden sm:inline\">New</span>\n          </button>\n        </div>\n        \n        <div className=\"text-xs text-slate-600\">\n          {templates.length} template{templates.length !== 1 ? 's' : ''}\n        </div>\n      </div>\n\n      {/* Templates List */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-3\">\n        {templates.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <FileText className=\"mx-auto h-12 w-12 text-slate-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-slate-800\">No templates</h3>\n            <p className=\"mt-1 text-sm text-slate-600\">\n              Get started by creating your first template.\n            </p>\n          </div>\n        ) : (\n          templates.map((template) => (\n            <div\n              key={template.id}\n              onClick={() => onSelectTemplate(template)}\n              className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${\n                selectedTemplate?.id === template.id\n                  ? 'bg-gradient-to-r from-teal-50 to-emerald-50 border-teal-200 shadow-md'\n                  : 'bg-white/70 border-orange-200/50 hover:bg-orange-50/50'\n              }`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <h3 className=\"text-sm font-medium text-slate-800 truncate\">\n                    {template.name}\n                  </h3>\n                  <div className=\"mt-1 flex items-center space-x-2 text-xs text-slate-600\">\n                    <Clock className=\"w-3 h-3\" />\n                    <span>{formatRelativeTime(template.updated_at)}</span>\n                  </div>\n                  <p className=\"mt-2 text-xs text-slate-500 line-clamp-2\">\n                    {template.content.substring(0, 80)}...\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-orange-200/50\">\n        <div className=\"text-xs text-slate-500 text-center\">\n          Templates help standardize your consultation notes\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Save, Copy, Edit3, FileText } from 'lucide-react'\nimport { formatDate } from '@/lib/utils'\n\ninterface Template {\n  id: string\n  name: string\n  content: string\n  created_at: string\n  updated_at: string\n}\n\ninterface TemplateEditorProps {\n  template: Template | null\n  onSave: (template: Template) => void\n}\n\nexport function TemplateEditor({ template, onSave }: TemplateEditorProps) {\n  const [templateName, setTemplateName] = useState('')\n  const [templateContent, setTemplateContent] = useState('')\n  const [isEditing, setIsEditing] = useState(false)\n\n  useEffect(() => {\n    if (template) {\n      setTemplateName(template.name)\n      setTemplateContent(template.content)\n      setIsEditing(false)\n    }\n  }, [template])\n\n  const handleSave = () => {\n    if (!template) return\n    \n    const updatedTemplate: Template = {\n      ...template,\n      name: templateName,\n      content: templateContent,\n      updated_at: new Date().toISOString()\n    }\n    \n    onSave(updatedTemplate)\n    setIsEditing(false)\n  }\n\n  const handleCopy = async () => {\n    if (templateContent) {\n      try {\n        await navigator.clipboard.writeText(templateContent)\n        // Could add a toast notification here\n      } catch (error) {\n        console.error('Failed to copy template content:', error)\n      }\n    }\n  }\n\n  if (!template) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex items-center justify-center\">\n        <div className=\"text-center\">\n          <FileText className=\"mx-auto h-12 w-12 text-slate-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-slate-800\">No template selected</h3>\n          <p className=\"mt-1 text-sm text-slate-600\">\n            Select a template from the sidebar to view and edit it.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-lg h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-orange-200/50\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex-1 min-w-0\">\n            {isEditing ? (\n              <input\n                type=\"text\"\n                value={templateName}\n                onChange={(e) => setTemplateName(e.target.value)}\n                className=\"text-lg font-semibold text-slate-800 bg-transparent border-b border-teal-300 focus:outline-none focus:border-teal-500 w-full\"\n                placeholder=\"Template name...\"\n              />\n            ) : (\n              <h2 className=\"text-lg font-semibold text-slate-800 truncate\">\n                {template.name}\n              </h2>\n            )}\n            <div className=\"mt-1 text-xs text-slate-600\">\n              Created: {formatDate(template.created_at)} • \n              Last updated: {formatDate(template.updated_at)}\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2 ml-4\">\n            <button\n              onClick={handleCopy}\n              className=\"flex items-center space-x-2 px-3 py-2 border border-orange-300 hover:border-teal-400 text-sm font-medium rounded-lg text-slate-700 bg-white/70 hover:bg-orange-50 transition-all duration-200\"\n            >\n              <Copy className=\"w-4 h-4\" />\n              <span className=\"hidden sm:inline\">Copy</span>\n            </button>\n            \n            {isEditing ? (\n              <button\n                onClick={handleSave}\n                className=\"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg\"\n              >\n                <Save className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">Save</span>\n              </button>\n            ) : (\n              <button\n                onClick={() => setIsEditing(true)}\n                className=\"flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg\"\n              >\n                <Edit3 className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">Edit</span>\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Content Editor */}\n      <div className=\"flex-1 p-4\">\n        <div className=\"h-full\">\n          <label className=\"block text-sm font-medium text-slate-700 mb-2\">\n            Template Content\n          </label>\n          <textarea\n            value={templateContent}\n            onChange={(e) => setTemplateContent(e.target.value)}\n            placeholder=\"Enter your template content here...\"\n            className=\"w-full h-full p-4 border border-orange-200 rounded-lg resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent bg-white text-slate-800 text-sm font-mono\"\n            readOnly={!isEditing}\n            style={{\n              minHeight: '400px'\n            }}\n          />\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-orange-200/50\">\n        <div className=\"flex items-center justify-between text-xs text-slate-500\">\n          <span>\n            {templateContent.length} characters • {templateContent.split('\\n').length} lines\n          </span>\n          {isEditing && (\n            <span className=\"text-teal-600 font-medium\">\n              Editing mode - Click Save to apply changes\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z',\n      key: '4pj2yx',\n    },\n  ],\n  ['path', { d: 'M20 3v4', key: '1olli1' }],\n  ['path', { d: 'M22 5h-4', key: '1gvqau' }],\n  ['path', { d: 'M4 17v2', key: 'vumght' }],\n  ['path', { d: 'M5 18H3', key: 'zchphs' }],\n];\n\n/**\n * @component @name Sparkles\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS45MzcgMTUuNUEyIDIgMCAwIDAgOC41IDE0LjA2M2wtNi4xMzUtMS41ODJhLjUuNSAwIDAgMSAwLS45NjJMOC41IDkuOTM2QTIgMiAwIDAgMCA5LjkzNyA4LjVsMS41ODItNi4xMzVhLjUuNSAwIDAgMSAuOTYzIDBMMTQuMDYzIDguNUEyIDIgMCAwIDAgMTUuNSA5LjkzN2w2LjEzNSAxLjU4MWEuNS41IDAgMCAxIDAgLjk2NEwxNS41IDE0LjA2M2EyIDIgMCAwIDAtMS40MzcgMS40MzdsLTEuNTgyIDYuMTM1YS41LjUgMCAwIDEtLjk2MyAweiIgLz4KICA8cGF0aCBkPSJNMjAgM3Y0IiAvPgogIDxwYXRoIGQ9Ik0yMiA1aC00IiAvPgogIDxwYXRoIGQ9Ik00IDE3djIiIC8+CiAgPHBhdGggZD0iTTUgMThIMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sparkles\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sparkles = createLucideIcon('sparkles', __iconNode);\n\nexport default Sparkles;\n", "'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react'\n\nexport function ComingSoonOverlay() {\n  const [isVisible, setIsVisible] = useState(true)\n\n  if (!isVisible) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden\">\n        {/* Background decoration */}\n        <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-emerald-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"></div>\n        <div className=\"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-100 to-amber-100 rounded-full translate-y-12 -translate-x-12 opacity-50\"></div>\n        \n        {/* Close button */}\n        <button\n          onClick={() => setIsVisible(false)}\n          className=\"absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors\"\n        >\n          <X className=\"w-5 h-5 text-gray-500\" />\n        </button>\n\n        {/* Content */}\n        <div className=\"relative z-10\">\n          <div className=\"text-center mb-6\">\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-teal-500 to-emerald-600 rounded-full mb-4\">\n              <Sparkles className=\"w-8 h-8 text-white\" />\n            </div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              Coming Soon!\n            </h2>\n            <p className=\"text-gray-600\">\n              Custom templates feature is under development\n            </p>\n          </div>\n\n          <div className=\"space-y-4 mb-6\">\n            <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-lg\">\n              <div className=\"flex-shrink-0\">\n                <Zap className=\"w-5 h-5 text-teal-600\" />\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">Smart Templates</h3>\n                <p className=\"text-xs text-gray-600\">AI-powered template suggestions</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg\">\n              <div className=\"flex-shrink-0\">\n                <Clock className=\"w-5 h-5 text-orange-600\" />\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-900\">Quick Access</h3>\n                <p className=\"text-xs text-gray-600\">One-click template application</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-teal-600 to-emerald-700 text-white rounded-lg text-sm font-medium\">\n              <Sparkles className=\"w-4 h-4 mr-2\" />\n              Expected: Q1 2025\n            </div>\n          </div>\n\n          <div className=\"mt-4 text-center\">\n            <p className=\"text-xs text-gray-500\">\n              Click the X button to close this overlay\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { <PERSON> } from '@/lib/types'\nimport { DashboardHeader } from '@/components/shared/dashboard-header'\nimport { TemplatesSidebar } from './templates-sidebar'\nimport { TemplateEditor } from './template-editor'\nimport { ComingSoonOverlay } from './coming-soon-overlay'\n\ninterface TemplatesInterfaceProps {\n  user: Doctor | null\n  doctorId: string\n}\n\n// Mock template type for UI\ninterface Template {\n  id: string\n  name: string\n  content: string\n  created_at: string\n  updated_at: string\n}\n\n// Mock templates data\nconst mockTemplates: Template[] = [\n  {\n    id: '1',\n    name: 'General Consultation',\n    content: 'Pat<PERSON> presents with...\\n\\nChief Complaint:\\n\\nHistory of Present Illness:\\n\\nPhysical Examination:\\n\\nAssessment:\\n\\nPlan:',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2', \n    name: 'Follow-up Visit',\n    content: 'Follow-up visit for...\\n\\nInterval History:\\n\\nCurrent Medications:\\n\\nReview of Systems:\\n\\nPhysical Examination:\\n\\nAssessment and Plan:',\n    created_at: '2024-01-10T14:30:00Z',\n    updated_at: '2024-01-12T09:15:00Z'\n  },\n  {\n    id: '3',\n    name: 'Discharge Summary',\n    content: 'Discharge Summary\\n\\nAdmission Date:\\nDischarge Date:\\n\\nPrimary Diagnosis:\\nSecondary Diagnoses:\\n\\nHospital Course:\\n\\nDischarge Medications:\\n\\nFollow-up Instructions:',\n    created_at: '2024-01-08T16:45:00Z',\n    updated_at: '2024-01-08T16:45:00Z'\n  }\n]\n\nexport function TemplatesInterface({ user, doctorId: _doctorId }: TemplatesInterfaceProps) {\n  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(mockTemplates[0])\n  const [templates] = useState<Template[]>(mockTemplates)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative\">\n      {/* Header */}\n      <DashboardHeader user={user} />\n\n      {/* Main Content */}\n      <main className=\"pt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\">\n            \n            {/* Left Sidebar - Templates List */}\n            <div className=\"lg:col-span-1\">\n              <TemplatesSidebar \n                templates={templates}\n                selectedTemplate={selectedTemplate}\n                onSelectTemplate={setSelectedTemplate}\n                onNewTemplate={() => {\n                  const newTemplate: Template = {\n                    id: `new-${Date.now()}`,\n                    name: 'New Template',\n                    content: '',\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                  }\n                  setSelectedTemplate(newTemplate)\n                }}\n              />\n            </div>\n\n            {/* Right Content - Template Editor */}\n            <div className=\"lg:col-span-3\">\n              <TemplateEditor \n                template={selectedTemplate}\n                onSave={(template) => {\n                  // Mock save functionality\n                  console.log('Saving template:', template)\n                }}\n              />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Coming Soon Overlay */}\n      <ComingSoonOverlay />\n    </div>\n  )\n}\n"], "names": [], "mappings": "uNAGO,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAA6B,CAClC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AADyB,CAAA,AACzB,CADyB,AACzB,AAAE,EAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC3E,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CACE,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACF,CAaM,EAAQ,CAAA,EAAA,AAAR,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oLCvBlD,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,QAMO,SAAS,EAAgB,MAAE,CAAI,CAAwB,EAC5D,GAAM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,GACnD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAiB,EADhB,IAG1C,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,AAFb,WAGlC,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,KADA,GACA,AAAO,EAAE,IAC/C,CAAC,EAAqB,EAAuB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADnB,EAGtC,EAAuB,UAE3B,GAAoB,GACpB,EAAgB,GALoC,GAMpD,EAAkB,WAClB,EAAkB,GACpB,EAEM,EAA0B,UAC9B,GAAI,CAAC,EAAe,IAAI,GAAI,YAC1B,EAAgB,0BAIlB,GAAuB,GACvB,EAAgB,MAEhB,GAAI,CACF,GAAI,GAAM,GAAI,CACZ,IAAM,EAAS,MAAM,CAAA,EAAA,EAAA,oBAAA,AAAmB,EACtC,EAAK,EAAE,CACP,CAAC,QAFkB,CAET,EAAE,eAAe;AAAA;AAAA,SAAa,EAAE,EAAA,CAAgB,CAC1D,GAEG,EAAO,OAAO,EAAE,AAInB,QAAQ,GAAG,CAAC,wCAEZ,WAAW,IAAM,GAAoB,GAAQ,OAL7C,EAAgB,EAAO,KAAK,EAAI,oCAChC,QAAQ,KAAK,CAAC,0BAA2B,EAAO,KAAK,EAMzD,MACE,CADK,CACW,iBAEpB,CAAE,MAAO,EAAO,CACd,EAAgB,aAAiB,MAAQ,EAAM,OAAO,CAAG,iBACzD,QAAQ,KAAK,CAAC,kCAAmC,EACnD,QAAU,CACR,GAAuB,EACzB,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,gFAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,qBAAW,OAClB,GAAM,MAAQ,cAGvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,2CACV,GAAM,aAAe,wBAK1B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,aAAa,UAAU,AAAjC,+FACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0IACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,cAAtB,yBAEH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2FACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iFAEjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mHAA0G,aAGxH,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oCAA2B,gCAK5C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gEAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,aACL,UAFD,AAEW,0MAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,IAAhC,aAML,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,KAAK,SACL,QAAS,EACT,UAAU,gWAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,AACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,eAIrC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,YACL,UAAU,CAFX,CAGC,UAAU,gWAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,gBAAnB,IACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,gBAIrC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,OAAQ,EAAA,MAAM,UAClB,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,AADW,CAEV,KAAK,SACL,UAAU,oYAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,mBAAjB,CACD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4BAAmB,6BAUhD,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0FACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oHACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uFACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAoB,GACnC,UAAU,mFAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,cAEf,CAAA,EAAA,EAAA,IAAA,CAFG,CAEF,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+EACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,cAEnB,CAAA,EAAA,EAAA,CAFG,GAEH,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6BAAoB,oBAClC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4BAAmB,kCAMtC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,yDAAgD,YAGjE,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAkB,EAAE,MAAM,CAAC,KAAK,EACjD,UAAU,0IAEV,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,qBACxB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,qBAAY,oBAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,qBACxB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,oBACxB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,eAAM,qBAKxB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,yDAAgD,YAGjE,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAkB,EAAE,MAAM,CAAC,KAAK,EACjD,YAAY,4CACZ,KAAM,EACN,UAAU,sKAKb,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CAAmC,KAC7C,OAGL,EACF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAoC,oCAInD,KAGJ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,EACT,SAAU,GAAuB,CAAC,EAAe,IAAI,GACrD,UAAU,uKAET,EAAsB,gBAAkB,mBAI3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sCAEjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uCAA8B,6BAKlD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDAA4C,sBAG3D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCAAyB,kCAK1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CACC,KAAK,oBACL,UAAU,sHACX,gBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CACC,KAAK,6BACL,OAAO,SACP,IAAI,sBACJ,UAAU,wHACX,oCAcrB,kGCvRA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAiBO,SAAS,EAAiB,CAC/B,WAAS,kBACT,CAAgB,kBAChB,CAAgB,eAChB,CAAa,CACS,EACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+GAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gDAAuC,cACrD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,yOAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GAAA,CADC,CACA,OAAA,CAAK,UAAU,4BAAmB,cAIvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACZ,EAAU,MAAM,CAAC,YAAU,AAAqB,MAAX,MAAM,CAAS,IAAM,SAK/D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDACS,IAArB,EAAU,MAAM,CACf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,oBACD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,iBACxD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,oDAK7C,EAAU,GAAG,CAAC,AAAC,GACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,QAAS,IAAM,EAAiB,GAChC,UAAW,CAAC,iFAAiF,EAC3F,GAAkB,KAAO,EAAS,EAAE,CAChC,wEACA,yDAAA,CACJ,UAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uDACX,EAAS,IAAI,GAEhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oEACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GAAA,AADC,AACD,EAAC,OAAA,UAAM,CAAA,EAAA,EAAA,kBAAA,AAAiB,EAAE,EAAS,UAAU,KAAtC,EAET,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACV,EAAS,OAAO,CAAC,SAAS,CAAC,EAAG,IAAI,eAlBpC,EAAS,EAAE,KA4BxB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CAAqC,2DAM5D,gGC5FA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAeO,SAAS,EAAe,UAAE,CAAQ,QAAE,CAAM,CAAuB,EACtE,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,IAC3C,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,EADf,EAElC,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,GAAE,CADG,EAG9C,CAAA,EAAA,EAAA,SAAA,AAAQ,EAAE,KACJ,EAH4B,EAI9B,EAAgB,EAAS,EADb,EACiB,EAC7B,EAAmB,EAAS,GAHhC,IAGuC,EACnC,GAAa,GAEjB,EAAG,CAAC,EAAS,EAgBb,IAAM,EAAa,UACjB,GAAI,EACF,GAAI,CACF,MAAM,KAFW,KAED,SAAS,CAAC,SAAS,CAAC,EAEtC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,mCAAoC,EACpD,CAEJ,SAEA,AAAK,EAeH,CAAA,CAfE,CAeF,EAAA,GAfa,CAeb,EAAC,MAAA,CAAI,UAAU,+GAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACZ,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,MAAO,EACP,SAAU,AAAC,GAAM,EAAgB,EAAE,MAAM,CAAC,KAAK,EAC/C,UAAU,+HACV,YAAY,qBAGd,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yDACX,EAAS,IAAI,GAGlB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCAA8B,YACjC,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAS,UAAU,EAAE,WAAhC,SACK,CAAA,EAAA,EAAA,UAAA,AAAS,EAAE,EAAS,UAAU,QAIjD,CAAA,EAAA,EAAA,AAJmB,IAInB,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,EACT,UAAU,0MAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GAAA,CADC,CACA,OAAA,CAAK,UAAU,4BAAmB,YAGpC,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QA3EK,CA2EI,IA1EhB,IASL,EAPkC,CAChC,GAHa,AAGV,CAME,AANM,CACX,KAAM,EACN,QAAS,EACT,WAAY,IAAI,OAAO,WAAW,EACpC,GAGA,GAAa,GACf,EAgEc,UAAU,yOAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,YAChB,CAAA,EAAA,EAAA,GAAA,CADC,CACA,OAAA,CAAK,UAAU,4BAAmB,YAGrC,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAa,IAC5B,UAAU,uOAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GADC,AACD,EAAC,OAAA,CAAK,UAAU,4BAAmB,oBAQ7C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,yDAAgD,qBAGjE,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAmB,EAAE,MAAM,CAAC,KAAK,EAClD,YAAY,sCACZ,UAAU,wKACV,SAAU,CAAC,EACX,MAAO,CACL,UAAW,OACb,SAMN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qEACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WACE,EAAgB,MAAM,CAAC,iBAAe,EAAgB,KAAK,CAAC,MAAM,MAAM,CAAC,YAE3E,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,uDA7FlD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iIACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,oBACD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,mDAA0C,yBACxD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,gEAiGrD,0GC7JO,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAA6B,CAClC,CACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAFgC,CAGhC,AAHgC,CAI9B,AAJ8B,CAI3B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CACA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,AAAL,QAAK,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,AAAL,QAAK,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,AAAE,EAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,CAC1C,CAaM,EAAW,CAAA,EAAA,EAAA,CAAX,AAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,CAAA,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wLC1BxD,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,QAEO,SAAS,IACd,GAAM,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAE,WAE3C,AAAK,EAGH,CAAA,CAHE,CAGF,EAAA,GAAA,CAHc,CAGb,IAL+B,EAK/B,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yFAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2IACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6IAGf,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,GAAa,GAC5B,UAAU,uFAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAC,CAAA,CAAC,UAAU,wBAAZ,IAIH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6HACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,QAEH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iDAAwC,iBAGtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,qDAK/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mGACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,GAAG,CAAA,CAAC,UAAU,sBAAd,MAEH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,oBAClD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,0CAIzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mGACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,oBAAhB,UAEH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,iBAClD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,4CAK3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uIACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAnB,AAAoC,yBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,uDA7DxB,IAqEzB,qGC3EA,EAAA,EAAA,CAAA,CAAA,QAEA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,QACA,EAAA,EAAA,CAAA,CAAA,OAiBA,IAAM,EAA4B,CAChC,CACE,GAAI,IACJ,KAAM,uBACN,QAAS,+HACT,WAAY,uBACZ,WAAY,sBACd,EACA,CACE,GAAI,IACJ,KAAM,kBACN,QAAS,6IACT,WAAY,uBACZ,WAAY,sBACd,EACA,CACE,GAAI,IACJ,KAAM,oBACN,QAAS,6KACT,WAAY,uBACZ,WAAY,sBACd,EACD,CAEM,SAAS,EAAmB,MAAE,CAAI,CAAE,SAAU,CAAS,CAA2B,EACvF,GAAM,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAO,AAAP,EAA0B,CAAa,CAAC,EAAE,EACpF,CAAC,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAO,EAAc,EADO,CAGhD,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,AAHa,UAGH,6FAEb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,CAAC,KAAM,IAGvB,CAAA,EAAA,EAAA,GAAA,EAAC,CAHA,MAGA,CAAK,UAAU,iBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uEAGb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,gBAAgB,CAAA,CACf,UAAW,EACX,OAFD,UAEmB,EAClB,iBAAkB,EAClB,cAAe,KAQb,EAP8B,CAC5B,GAAI,CAAC,IAAI,EAAE,KAAK,EAME,CANC,GAAA,CAAI,CACvB,KAAM,eACN,QAAS,GACT,WAAY,IAAI,OAAO,WAAW,GAClC,WAAY,IAAI,OAAO,WAAW,EACpC,EAEF,MAKJ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,cAAc,CAAA,CACb,SAAU,EACV,OAAQ,AAAC,GAFV,CAIG,QAAQ,GAAG,CAAC,mBAAoB,EAClC,aAQV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,iBAAiB,CAAA,CAAA,KAGxB,YAHO", "ignoreList": [0, 4]}