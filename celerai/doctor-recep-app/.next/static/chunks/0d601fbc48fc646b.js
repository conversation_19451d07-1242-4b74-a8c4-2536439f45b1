(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{560566:e=>{"use strict";var{g:r,__dirname:o}=e;{function t(){for(var e,r,o=0,t="",a=arguments.length;o<a;o++)(e=arguments[o])&&(r=function e(r){var o,t,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(o=0;o<n;o++)r[o]&&(t=e(r[o]))&&(a&&(a+=" "),a+=t)}else for(t in r)r[t]&&(a&&(a+=" "),a+=t);return a}(e))&&(t&&(t+=" "),t+=r);return t}e.s({clsx:()=>t,default:()=>r});let r=t}},866032:e=>{"use strict";var{g:r,__dirname:o}=e;{e.s({createTailwindMerge:()=>a,extendTailwindMerge:()=>eg,fromTheme:()=>x,getDefaultConfig:()=>ed,mergeConfigs:()=>ec,twJoin:()=>t,twMerge:()=>eb,validators:()=>el});let r=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),o(t,r)||s(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&a[e]?[...o,...a[e]]:o}}},o=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],a=r.nextPart.get(t),n=a?o(e.slice(1),a):void 0;if(n)return n;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},n=/^\[(.+)\]$/,s=e=>{if(n.test(e)){let r=n.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},i=e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)l(o[e],t,e,r);return t},l=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:d(r,e)).classGroupId=o;return}if("function"==typeof e)return c(e)?void l(e(t),r,o,t):void r.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,a])=>{l(a,d(r,e),o,t)})})},d=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},c=e=>e.isThemeGetter,m=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,a=(a,n)=>{o.set(a,n),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(a(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):a(e,r)}}},p=e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r,o=[],t=0,a=0,n=0;for(let s=0;s<e.length;s++){let i=e[s];if(0===t&&0===a){if(":"===i){o.push(e.slice(n,s)),n=s+1;continue}if("/"===i){r=s;continue}}"["===i?t++:"]"===i?t--:"("===i?a++:")"===i&&a--}let s=0===o.length?e:e.substring(n),i=u(s);return{modifiers:o,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>n?r-n:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t},u=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}},g=e=>({cache:m(e.cacheSize),parseClassName:p(e),sortModifiers:f(e),...r(e)}),b=/\s+/,h=(e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:a,sortModifiers:n}=r,s=[],i=e.trim().split(b),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=o(r);if(d){l=r+(l.length>0?" "+l:l);continue}let f=!!u,g=t(f?p.substring(0,u):p);if(!g){if(!f||!(g=t(p))){l=r+(l.length>0?" "+l:l);continue}f=!1}let b=n(c).join(":"),h=m?b+"!":b,w=h+g;if(s.includes(w))continue;s.push(w);let x=a(g,f);for(let e=0;e<x.length;++e){let r=x[e];s.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function t(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=w(e))&&(t&&(t+=" "),t+=r);return t}let w=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=w(e[t]))&&(o&&(o+=" "),o+=r);return o};function a(e,...r){let o,n,s,i=function(t){return n=(o=g(r.reduce((e,r)=>r(e),e()))).cache.get,s=o.cache.set,i=l,l(t)};function l(e){let r=n(e);if(r)return r;let t=h(e,o);return s(e,t),t}return function(){return i(t.apply(null,arguments))}}let x=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},k=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,y=/^\((?:(\w[\w-]*):)?(.+)\)$/i,v=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,M=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,C=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,G=e=>v.test(e),P=e=>!!e&&!Number.isNaN(Number(e)),j=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&P(e.slice(0,-1)),D=e=>z.test(e),I=()=>!0,A=e=>M.test(e)&&!C.test(e),E=()=>!1,N=e=>S.test(e),U=e=>$.test(e),F=e=>!R(e)&&!L(e),O=e=>Z(e,et,E),R=e=>k.test(e),_=e=>Z(e,ea,A),W=e=>Z(e,en,P),B=e=>Z(e,er,E),q=e=>Z(e,eo,U),H=e=>Z(e,ei,N),L=e=>y.test(e),J=e=>ee(e,ea),K=e=>ee(e,es),Y=e=>ee(e,er),V=e=>ee(e,et),Q=e=>ee(e,eo),X=e=>ee(e,ei,!0),Z=(e,r,o)=>{let t=k.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},ee=(e,r,o=!1)=>{let t=y.exec(e);return!!t&&(t[1]?r(t[1]):o)},er=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,et=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,en=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e,el=Object.defineProperty({__proto__:null,isAny:I,isAnyNonArbitrary:F,isArbitraryImage:q,isArbitraryLength:_,isArbitraryNumber:W,isArbitraryPosition:B,isArbitraryShadow:H,isArbitrarySize:O,isArbitraryValue:R,isArbitraryVariable:L,isArbitraryVariableFamilyName:K,isArbitraryVariableImage:Q,isArbitraryVariableLength:J,isArbitraryVariablePosition:Y,isArbitraryVariableShadow:X,isArbitraryVariableSize:V,isFraction:G,isInteger:j,isNumber:P,isPercent:T,isTshirtSize:D},Symbol.toStringTag,{value:"Module"}),ed=()=>{let e=x("color"),r=x("font"),o=x("text"),t=x("font-weight"),a=x("tracking"),n=x("leading"),s=x("breakpoint"),i=x("container"),l=x("spacing"),d=x("radius"),c=x("shadow"),m=x("inset-shadow"),p=x("text-shadow"),u=x("drop-shadow"),f=x("blur"),g=x("perspective"),b=x("aspect"),h=x("ease"),w=x("animate"),k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...y(),L,R],z=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto","contain","none"],C=()=>[L,R,l],S=()=>[G,"full","auto",...C()],$=()=>[j,"none","subgrid",L,R],A=()=>["auto",{span:["full",j,L,R]},j,L,R],E=()=>[j,"auto",L,R],N=()=>["auto","min","max","fr",L,R],U=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Z=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],er=()=>[G,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],eo=()=>[e,L,R],et=()=>[...y(),Y,B,{position:[L,R]}],ea=()=>["no-repeat",{repeat:["","x","y","space","round"]}],en=()=>["auto","cover","contain",V,O,{size:[L,R]}],es=()=>[T,J,_],ei=()=>["","none","full",d,L,R],el=()=>["",P,J,_],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[P,T,Y,B],ep=()=>["","none",f,L,R],eu=()=>["none",P,L,R],ef=()=>["none",P,L,R],eg=()=>[P,L,R],eb=()=>[G,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[D],breakpoint:[D],color:[I],container:[D],"drop-shadow":[D],ease:["in","out","in-out"],font:[F],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[D],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[D],shadow:[D],spacing:["px",P],text:[D],"text-shadow":[D],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",G,R,L,b]}],container:["container"],columns:[{columns:[P,R,L,i]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:z()}],"overflow-x":[{"overflow-x":z()}],"overflow-y":[{"overflow-y":z()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[j,"auto",L,R]}],basis:[{basis:[G,"full","auto",i,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[P,G,"auto","initial","none",R]}],grow:[{grow:["",P,L,R]}],shrink:[{shrink:["",P,L,R]}],order:[{order:[j,"first","last","none",L,R]}],"grid-cols":[{"grid-cols":$()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":$()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...U(),"normal"]}],"justify-items":[{"justify-items":[...Z(),"normal"]}],"justify-self":[{"justify-self":["auto",...Z()]}],"align-content":[{content:["normal",...U()]}],"align-items":[{items:[...Z(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Z(),{baseline:["","last"]}]}],"place-content":[{"place-content":U()}],"place-items":[{"place-items":[...Z(),"baseline"]}],"place-self":[{"place-self":["auto",...Z()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[s]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",o,J,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,L,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,R]}],"font-family":[{font:[K,R,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,L,R]}],"line-clamp":[{"line-clamp":[P,"none",L,W]}],leading:[{leading:[n,...C()]}],"list-image":[{"list-image":["none",L,R]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,R]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[P,"from-font","auto",L,_]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[P,"auto",L,R]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,R]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,R]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:ea()}],"bg-size":[{bg:en()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},j,L,R],radial:["",L,R],conic:[j,L,R]},Q,q]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[P,L,R]}],"outline-w":[{outline:["",P,J,_]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",c,X,H]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",m,X,H]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[P,_]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",p,X,H]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[P,L,R]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[P]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[L,R]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[P]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:ea()}],"mask-size":[{mask:en()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,R]}],filter:[{filter:["","none",L,R]}],blur:[{blur:ep()}],brightness:[{brightness:[P,L,R]}],contrast:[{contrast:[P,L,R]}],"drop-shadow":[{"drop-shadow":["","none",u,X,H]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",P,L,R]}],"hue-rotate":[{"hue-rotate":[P,L,R]}],invert:[{invert:["",P,L,R]}],saturate:[{saturate:[P,L,R]}],sepia:[{sepia:["",P,L,R]}],"backdrop-filter":[{"backdrop-filter":["","none",L,R]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[P,L,R]}],"backdrop-contrast":[{"backdrop-contrast":[P,L,R]}],"backdrop-grayscale":[{"backdrop-grayscale":["",P,L,R]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[P,L,R]}],"backdrop-invert":[{"backdrop-invert":["",P,L,R]}],"backdrop-opacity":[{"backdrop-opacity":[P,L,R]}],"backdrop-saturate":[{"backdrop-saturate":[P,L,R]}],"backdrop-sepia":[{"backdrop-sepia":["",P,L,R]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,R]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[P,"initial",L,R]}],ease:[{ease:["linear","initial",h,L,R]}],delay:[{delay:[P,L,R]}],animate:[{animate:["none",w,L,R]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,L,R]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[L,R,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,R]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,R]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[P,J,_,W]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ec=(e,{cacheSize:r,prefix:o,experimentalParseClassName:t,extend:a={},override:n={}})=>(em(e,"cacheSize",r),em(e,"prefix",o),em(e,"experimentalParseClassName",t),ep(e.theme,n.theme),ep(e.classGroups,n.classGroups),ep(e.conflictingClassGroups,n.conflictingClassGroups),ep(e.conflictingClassGroupModifiers,n.conflictingClassGroupModifiers),em(e,"orderSensitiveModifiers",n.orderSensitiveModifiers),eu(e.theme,a.theme),eu(e.classGroups,a.classGroups),eu(e.conflictingClassGroups,a.conflictingClassGroups),eu(e.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),ef(e,a,"orderSensitiveModifiers"),e),em=(e,r,o)=>{void 0!==o&&(e[r]=o)},ep=(e,r)=>{if(r)for(let o in r)em(e,o,r[o])},eu=(e,r)=>{if(r)for(let o in r)ef(e,r,o)},ef=(e,r,o)=>{let t=r[o];void 0!==t&&(e[o]=e[o]?e[o].concat(t):t)},eg=(e,...r)=>"function"==typeof e?a(ed,e,...r):a(()=>ec(ed(),e),...r),eb=a(ed)}},766805:e=>{"use strict";var{g:r,__dirname:o}=e;e.s({cn:()=>n,createFileFromBlob:()=>s,formatDate:()=>l,formatDuration:()=>i,formatRelativeTime:()=>d,generatePrompt:()=>p,isValidUrl:()=>m,retryWithBackoff:()=>c,supportsAudioRecording:()=>f,supportsCamera:()=>g,truncateText:()=>u});var t=e.i(560566),a=e.i(866032);function n(...e){return(0,a.twMerge)((0,t.clsx)(e))}function s(e,r){return new File([e],r,{type:e.type})}function i(e){let r=Math.floor(e/60),o=Math.floor(e%60);return`${r.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`}function l(e){let r=new Date(e),o=r.getFullYear(),t=r.toLocaleDateString("en-US",{month:"short"}),a=r.getDate().toString().padStart(2,"0"),n=r.getMinutes().toString().padStart(2,"0"),s=r.getHours()>=12?"PM":"AM",i=r.getHours()%12||12;return`${t} ${a}, ${o} at ${i.toString().padStart(2,"0")}:${n} ${s}`}function d(e){let r=new Date(e),o=Math.floor((new Date().getTime()-r.getTime())/1e3);if(o<60)return"Just now";if(o<3600){let e=Math.floor(o/60);return`${e} minute${e>1?"s":""} ago`}if(o<86400){let e=Math.floor(o/3600);return`${e} hour${e>1?"s":""} ago`}{let e=Math.floor(o/86400);return`${e} day${e>1?"s":""} ago`}}async function c(e,r={maxAttempts:3,baseDelay:1e3,maxDelay:1e4}){let o;for(let t=1;t<=r.maxAttempts;t++)try{return await e()}catch(a){if(o=a,t===r.maxAttempts)throw o;let e=Math.min(r.baseDelay*Math.pow(2,t-1),r.maxDelay);console.warn(`Attempt ${t} failed, retrying in ${e}ms:`,a),await new Promise(r=>setTimeout(r,e))}throw o}function m(e){try{return new URL(e),!0}catch{return!1}}function p(e){return`
You are an AI assistant helping Indian doctors create patient consultation summaries.

Context: ${"doctor"===e?"This consultation was recorded by the doctor during patient visit.":"This consultation is being reviewed by the receptionist for final summary."}

Please analyze the provided audio recording and any handwritten notes (if image provided) to generate a comprehensive patient summary.

Requirements:
- Language: English
- Tone: Professional
- Format: Standard medical format
- Include sections: Chief Complaint, History, Examination, Diagnosis, Treatment Plan, Follow-up

Instructions:
1. Transcribe the audio accurately
2. Extract key medical information
3. If image provided, include any relevant handwritten notes
4. Structure the summary according to the specified sections
5. Use appropriate medical terminology
6. Ensure the summary is clear and professional

Please provide a well-structured patient consultation summary based on the audio and image inputs.
  `.trim()}function u(e,r){return e.length<=r?e:e.substring(0,r)+"..."}function f(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}function g(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}}}]);

//# sourceMappingURL=f7365b79d11b0b6b.js.map