{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/use-merged-ref.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/utils/error-once.ts", "turbopack:///[project]/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "process", "env", "NODE_ENV", "errorOnce", "_", "LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "window", "useMemo", "resolvedHref", "child", "Children", "only", "childRef", "observeLinkVisibilityOnMount", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "type", "addBasePath", "link", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "wPASgBA,eAAAA,qCAAAA,aAT8B,CAAA,CAAA,IAAA,IASvC,SAASA,EACdC,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MACvCC,EAAWD,CAAAA,EAAAA,EAAAA,MAAM,AAANA,EAA4B,MAS7C,MAAOE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAChB,AAACC,IACC,GAAgB,OAAZA,EAAkB,CACpB,IAAMC,EAAaL,EAASI,OAAO,CAC/BC,IACFL,EAASI,MADK,CACE,CAAG,KACnBC,KAEF,IAAMC,EAAaJ,EAASE,OAAO,CAC/BE,IACFJ,EAASE,MADK,CACE,CAAG,KACnBE,IAEJ,MACMR,CADC,EAEHE,GADQ,AACCI,OAAO,CAAGG,EAAST,EAAMM,EAAAA,EAEhCL,IACFG,EADQ,AACCE,OAAO,CAAGG,EAASR,EAAMK,EAAAA,CAGxC,EACA,CAACN,EAAMC,EAAK,CAEhB,CAEA,SAASQ,EACPT,CAAgC,CAChCM,CAAiB,EAEjB,GAAoB,YAAhB,OAAON,EAST,OADAA,EAAKM,OAAO,CAAGA,EACR,KACLN,EAAKM,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAMI,EAAUV,EAAKM,SACrB,AAAuB,YAAnB,AAA+B,OAAxBI,EACFA,EAEA,IAAMV,EAAK,KAEtB,CAMF,MANS,oRC3DoB,EAAA,CAAA,CAAA,iFAUpBc,YAAAA,qCAAAA,KAXT,IAAIA,EAAY,AAACC,IAAe,oECkXD,EAAA,CAAA,CAAA,gEArD/B,OAyZC,CAAA,kBAzZuBC,GA+ZXC,aAAa,CAAA,kBAAbA,+GA1tB2D,CAAA,CAAA,IAAA,SAE9C,CAAA,CAAA,IAAA,QACO,CAAA,CAAA,IAAA,OACJ,CAAA,CAAA,IAAA,QACA,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACH,CAAA,CAAA,IAAA,WASlB,CAAA,CAAA,IAAA,QACoB,CAAA,CAAA,IAAA,QACY,CAAA,CAAA,IAAA,IAsRvC,SAASiC,EAAkBC,CAAkC,QAC3D,AAA8B,UAA1B,AAAoC,OAA7BA,EACFA,EAGFC,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAACD,EACnB,CAYe,SAASnC,EACtBqC,CAGC,EAED,IAEIK,EA+LAoC,EAyLAmB,EA1XE,CAAC3D,EAAYC,EAAwB,CAAGC,CAAAA,EAAAA,EAAAA,aAAa,AAAbA,EAAcC,EAAAA,gBAAgB,EAItEvB,EAAkB/B,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MAE9C,CACJ6B,KAAM2B,CAAQ,CACd1B,GAAI2B,CAAM,CACVF,SAAUG,CAAY,CACtBC,SAAUC,EAAe,IAAI,CAC7BC,UAAQ,SACR7B,CAAO,SACP8B,CAAO,QACP7B,CAAM,SACN8B,CAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,YACtBlC,CAAU,CACVmC,IAAKC,CAAY,CACjBC,yBAAuB,CACvB,GAAGC,EACJ,CAAGtB,EAEJK,EAAWG,EAGTU,GACC,CAAoB,cAApB,GAAOb,GAAyB,AAAoB,iBAAbA,CAAa,CAAO,GAC5D,AACAA,EAAW,CAAA,EAAA,EAAA,GAAA,CAAXA,CAAYkB,IAAAA,MAAZlB,IAAeA,KAGjB,IAAMmB,EAAS7B,EAAAA,OAAK,CAAC8B,UAAU,CAACC,EAAAA,gBAAgB,EAE1CC,EAAmC,KAAjBjB,EAQlBkB,EACJlB,AAAiB,SAAOmB,EAAAA,YAAY,CAACC,IAAI,CAAGD,EAAAA,YAAY,CAACE,IAAI,CA2IzD,MAAEpD,CAAI,IAAEC,CAAE,CAAE,CAAGe,EAAAA,OAAK,CAAC4C,OAAO,CAAC,KACjC,IAAMC,EAAe3C,EAAkBS,GACvC,MAAO,CACL3B,KAAM6D,EACN5D,GAAI2B,EAASV,EAAkBU,GAAUiC,CAC3C,CACF,EAAG,CAAClC,EAAUC,EAAO,EAIjBW,IA4BAuB,EAAQ9C,EAAAA,OAAK,CA5BG,AA4BF+C,QAAQ,CAACC,IAAI,CAACtC,EAAAA,EAYhC,IAAMuC,EAAgB1B,EAClBuB,GAA0B,UAAjB,OAAOA,GAAsBA,EAAMtB,GAAG,CAC/CC,EAMEyB,EAA+BlD,EAAAA,OAAK,CAAC3C,WAAW,CACpD,AAAC8F,IACgB,MAAM,CAAjBtB,GACF3C,GAAgB5B,OAAO,CAAG8F,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EACzCD,EACAnE,EACA6C,EACAI,EACAD,EACAzB,EAAAA,EAIG,KACDrB,EAAgB5B,OAAO,EAAE,CAC3B+F,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACnE,EAAgB5B,OAAO,EACvD4B,EAAgB5B,OAAO,CAAG,MAE5BgG,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAACH,EAC9B,GAEF,CAACnB,EAAiBhD,EAAM6C,EAAQI,EAAiB1B,EAAwB,EAKrEiD,EAMF,CACFhC,IATgBzE,CASXwG,EATWxG,EAAAA,YAAAA,AAAY,EAACmG,EAA8BD,GAU3D/B,QAAQnC,CAAC,EASH,AAACwC,GAAqC,YAAnB,AAA+B,OAAxBL,GAC5BA,EAAQnC,GAIRwC,GACAuB,EAAMzC,KAAK,EACoB,YAC/B,AADA,OAAOyC,EAAMzC,KAAK,CAACa,OAAO,EAE1B4B,EAAMzC,KAAK,CAACa,OAAO,CAACnC,GAGjB8C,IAID9C,EAAE0E,EAJO,cAIS,EAAE,AAnY9B,AAuYM3E,SAvYGA,AACPC,CAAmB,CACnBC,CAAY,CACZC,CAAU,CACVC,CAAqD,CACrDC,CAAiB,CACjBC,CAAgB,CAChBC,CAAmC,EAEnC,GAAM,UAAEC,CAAQ,CAAE,CAAGP,EAAEV,aAAa,CAKpC,KAFoD,AAGjDkB,MAHsBD,EAASE,WAAW,IAGtBtB,AA5BzB,SAASA,AAAgBC,CAAuB,EAE9C,IAAMG,EADcH,AACLC,EADWC,aAAa,CACZE,YAAY,CAAC,UACxC,OACGD,GAAqB,UAAXA,GACXH,EAAMK,OAAO,EACbL,EAAMM,OAAO,EACbN,EAAMO,QAAQ,EACdP,EAAMQ,MAAM,EACXR,EADe,AACTS,WAAW,EAAgC,IAA5BT,EAAMS,UADiB,CACN,CAACC,KAAK,AAEjD,EAiByCE,IACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,WAAA,GAC7B,AAKF,GAAI,CAACC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACV,GAAO,CACjBG,IAGFJ,EAAEY,GAHS,WAGK,GAChBC,SAAST,OAAO,CAACH,IAInB,MACF,CAEAD,EAAEY,cAAc,GAyBhBK,EAAAA,OAAK,CAACC,eAAe,CAvBJ,AAuBKJ,KAtBpB,GAAIR,EAAY,CACd,IAAIS,GAAqB,EAQzB,GANAT,EAAW,CACTM,eAAgB,KACdG,GAAqB,CACvB,CACF,GAEIA,EACF,MAEJ,CAEAC,CAAAA,EAAAA,EAAAA,MAL0B,gBAK1BA,AAAsB,EACpBd,GAAMD,EACNG,EAAU,UAAY,OACtBC,SAAAA,EACAF,EAAgB5B,KADN,EACa,CAE3B,GAGF,EA2UkByB,EAAGC,EAAMC,EAAIC,EAAiBC,EAASC,EAAQC,GAC7D,EACA8B,aAAapC,CAAC,EACR,AAACwC,GAA8C,YAA5B,AAAwC,OAAjCH,GAC5BA,EAAiBrC,GAIjBwC,GACAuB,EAAMzC,KAAK,EACX,AAAoC,YACpC,OADOyC,EAAMzC,KAAK,CAACc,YAAY,EAE/B2B,EAAMzC,KAAK,CAACc,YAAY,CAACpC,GAGtB8C,GAIAG,GAKL2B,CAAAA,CATa,CASbA,EAAAA,WALwBhG,OAKxBgG,AAAkB,CALc/F,CAM9BmB,EANiC,AAM/BV,CANgCR,QAAQ,IAM3B,EAF4C,AAG3D6F,IAH+BhC,EAKnC,EACAL,aAEI,CAFU1D,GAVqC,KAU7BC,AAETyD,AAAatC,CAAC,EAFF,AAGf,AAACwC,CAHeqC,EAG+B,IAbS,QAaG,AAAxC,OAAOtC,GAC5BA,EAAiBvC,AAJ2B,GAQ5CwC,AAPJsC,GAQIf,EAAMzC,KAAK,EACyB,YAApC,AACA,OADOyC,EAAMzC,KAAK,CAACgB,YAAY,EAE/ByB,EAAMzC,KAAK,CAACgB,YAAY,CAACtC,GAGtB8C,GAIAG,GAKL2B,CAAAA,CATa,CASbA,EAAAA,SALsB,SAKtBA,AAAkB,EAChB5E,EAAEV,aAAa,EAF4C,AAG3DqF,IAH+BhC,EAKnC,CACN,EAmCA,MA9BIoC,CA8BJ,EA9BIA,EAAAA,OA8BJ,MA9BiB,AAAbA,EAAc7E,GAChBuE,EADqB,AACVxE,IAAI,CAAGC,EAElB,AAACsC,IACDP,IACgB,MAAf8B,CAAsB,CAAhBiB,AAAkB,IAAd,EAAc,SAAUjB,EAAMzC,KAAI,GAC7C,CACAmD,EAAWxE,IAAI,CAAGgF,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC/E,EAAAA,EAc9BgF,EATE1C,EASKvB,EAAAA,CAAPiE,MAAY,CAACC,IATK,AASlBD,QAAyB,CAACnB,EAAOU,GAG/B,CAAA,EAAA,EAAA,GAAA,EAAC5B,IAAAA,CAAG,GAAGD,CAAS,CAAG,GAAG6B,CAAU,UAC7B9C,IAML,CAAA,EAAA,EAAA,GAAA,EAACyD,EAAkBC,QAAQ,CAAA,CAACC,MAAO/D,WAChC2D,GAGP,GAhsB0B,CAAA,CAAA,IAAA,IAksB1B,IAAME,EAAoBG,CAAAA,EAAAA,EAAAA,aAApBH,AAAoBG,AAAa,EAErC7D,EAAAA,OAFI0D,SAEY,EAELlG,EAAgB,IACpB6D,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACqC", "ignoreList": [0, 1, 2]}