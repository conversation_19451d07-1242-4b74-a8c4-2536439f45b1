(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/analytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "initializeAnalytics": (()=>initializeAnalytics),
    "trackAuth": (()=>trackAuth),
    "trackConsultation": (()=>trackConsultation),
    "trackEvent": (()=>trackEvent),
    "trackPageView": (()=>trackPageView),
    "trackQuotaWarning": (()=>trackQuotaWarning)
});
'use client';
// Check if user is in authenticated app
function isAuthenticatedRoute() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const authenticatedPaths = [
        '/dashboard',
        '/info',
        '/settings',
        '/templates',
        '/admin'
    ];
    return authenticatedPaths.some((path)=>window.location.pathname.startsWith(path));
}
// Allowlist of safe events for authenticated zone
const SAFE_AUTHENTICATED_EVENTS = [
    'consultation_generated',
    'summary_approved',
    'quota_warning',
    'dashboard_viewed',
    'settings_accessed',
    'template_updated'
];
// Scrub any potentially sensitive parameters
function scrubParameters(params) {
    const scrubbed = {};
    // Only allow specific safe parameters
    const allowedParams = [
        'page_title',
        'consultation_type',
        'quota_percentage',
        'feature_used',
        'error_type'
    ];
    for (const [key, value] of Object.entries(params)){
        if (allowedParams.includes(key) && value !== undefined) {
            // Additional validation for specific params
            if (key === 'quota_percentage' && typeof value === 'number') {
                scrubbed[key] = Math.round(value) // Round to avoid precision-based identification
                ;
            } else if (typeof value === 'string' && value.length < 100) {
                scrubbed[key] = value;
            } else if (typeof value === 'boolean') {
                scrubbed[key] = value;
            }
        }
    }
    return scrubbed;
}
function trackEvent(event, parameters = {}) {
    // Only track in browser environment
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if GA4 is loaded
    if (typeof window.gtag !== 'function') {
        console.warn('GA4 not loaded, skipping analytics event:', event);
        return;
    }
    try {
        const isAuthenticated = isAuthenticatedRoute();
        if (isAuthenticated) {
            // AUTHENTICATED ZONE: Strict allowlist enforcement
            if (!SAFE_AUTHENTICATED_EVENTS.includes(event)) {
                console.warn(`Analytics: Blocked unauthorized event in authenticated zone: ${event}`);
                return;
            }
            // Scrub all parameters for safety
            const scrubbedParams = scrubParameters(parameters);
            console.log(`Analytics: Tracking safe authenticated event: ${event}`, scrubbedParams);
            window.gtag('event', event, scrubbedParams);
        } else {
            // PUBLIC ZONE: Full tracking allowed (but still scrub for safety)
            const scrubbedParams = scrubParameters(parameters);
            console.log(`Analytics: Tracking public event: ${event}`, scrubbedParams);
            window.gtag('event', event, scrubbedParams);
        }
    } catch (error) {
        console.error('Analytics tracking error:', error);
    }
}
function trackPageView(pageTitle) {
    const isAuthenticated = isAuthenticatedRoute();
    if (isAuthenticated) {
        // In authenticated zone, only track generic dashboard view
        trackEvent('dashboard_viewed', {
            page_title: 'Dashboard'
        });
    } else {
        // In public zone, track full page view
        trackEvent('page_view', {
            page_title: pageTitle
        });
    }
}
function trackConsultation(type, consultationType) {
    if (!isAuthenticatedRoute()) return;
    if (type === 'generated') {
        trackEvent('consultation_generated', {
            consultation_type: consultationType
        });
    } else if (type === 'approved') {
        trackEvent('summary_approved', {
            consultation_type: consultationType
        });
    }
}
function trackQuotaWarning(percentage) {
    if (!isAuthenticatedRoute()) return;
    trackEvent('quota_warning', {
        quota_percentage: percentage
    });
}
function trackAuth(type) {
    // Only track in public zone
    if (isAuthenticatedRoute()) return;
    trackEvent(type);
}
function initializeAnalytics() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Log analytics initialization
    console.log('Analytics initialized with two-zone security model');
    // Track initial page view
    trackPageView(document.title);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/analytics/analytics-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnalyticsProvider": (()=>AnalyticsProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analytics.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AnalyticsProvider({ children }) {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsProvider.useEffect": ()=>{
            // Lazy initialize analytics after page is fully loaded
            const timer = setTimeout({
                "AnalyticsProvider.useEffect.timer": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initializeAnalytics"])();
                }
            }["AnalyticsProvider.useEffect.timer"], 100) // Small delay to ensure page is interactive
            ;
            return ({
                "AnalyticsProvider.useEffect": ()=>clearTimeout(timer)
            })["AnalyticsProvider.useEffect"];
        }
    }["AnalyticsProvider.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsProvider.useEffect": ()=>{
            // Lazy track page changes with debouncing
            const timer = setTimeout({
                "AnalyticsProvider.useEffect.timer": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["trackPageView"])(document.title);
                }
            }["AnalyticsProvider.useEffect.timer"], 50);
            return ({
                "AnalyticsProvider.useEffect": ()=>clearTimeout(timer)
            })["AnalyticsProvider.useEffect"];
        }
    }["AnalyticsProvider.useEffect"], [
        pathname
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AnalyticsProvider, "tjXKfJWuFDa0epp0CJaCeazyqhM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = AnalyticsProvider;
var _c;
__turbopack_context__.k.register(_c, "AnalyticsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_ba710dd8._.js.map