{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/actions/auth.ts"], "sourcesContent": ["'use server'\n\nimport { redirect } from 'next/navigation'\nimport bcrypt from 'bcryptjs'\nimport { createClient } from '@/lib/supabase/server'\nimport { createSession, deleteSession, refreshSession } from '@/lib/auth/session'\nimport { SignupFormSchema, LoginFormSchema } from '@/lib/validations'\nimport { FormState } from '@/lib/types'\nimport { processReferralSignup } from '@/lib/actions/referrals'\n\nexport async function signup(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = SignupFormSchema.safeParse({\n    name: formData.get('name'),\n    email: formData.get('email'),\n    password: formData.get('password'),\n    clinic_name: formData.get('clinic_name'),\n    phone: formData.get('phone'),\n  })\n\n  // Get referral code from form\n  const referralCode = formData.get('referral_code') as string\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.', // A generic message for validation failure\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  // Prepare data for insertion into database\n  const { name, email, password, clinic_name, phone } = validatedFields.data\n  const hashedPassword = await bcrypt.hash(password, 10)\n\n  try {\n    const supabase = await createClient()\n\n    // Check if user already exists (email or phone)\n    const { data: existingUser } = await supabase\n      .from('doctors')\n      .select('id')\n      .eq('email', email)\n      .single()\n\n    if (existingUser) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An account with this email already exists.',\n      }\n    }\n\n    // Check if phone number already exists (if phone is provided)\n    if (phone) {\n      const { data: existingPhone } = await supabase\n        .from('doctors')\n        .select('id')\n        .eq('phone', phone)\n        .single()\n\n      if (existingPhone) {\n        return {\n          success: false,\n          message: 'An account with this phone number already exists.',\n        }\n      }\n    }\n\n    // Insert the user into the database with approval required and new default quota\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .insert({\n        name,\n        email,\n        password_hash: hashedPassword,\n        clinic_name,\n        phone,\n        approved: false, // New doctors require approval\n        monthly_quota: 50, // Set default quota to 50\n      })\n      .select('id, approved')\n      .single()\n\n    if (error) {\n      console.error('Database error:', error)\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    if (!user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'An error occurred while creating your account.',\n      }\n    }\n\n    // Process referral if referral code was provided\n    if (referralCode) {\n      await processReferralSignup(referralCode, user.id)\n    }\n\n    // Don't create session for unapproved users\n    if (!user.approved) {\n      return {\n        success: true, // This is a success state, but with an informative message\n        message: 'Account created successfully! Please wait for admin approval before you can log in.',\n      }\n    }\n\n    // Create user session (only for approved users)\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Signup error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means signup was successful and session created (if approved)\n  // or a success message was returned for pending approval.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function login(state: FormState, formData: FormData): Promise<FormState> {\n  // Validate form fields\n  const validatedFields = LoginFormSchema.safeParse({\n    email: formData.get('email'),\n    password: formData.get('password'),\n  })\n\n  // If any form fields are invalid, return early\n  if (!validatedFields.success) {\n    return {\n      // Provide required 'success' and 'message' properties\n      success: false,\n      message: 'Invalid form fields.',\n      // Use 'fieldErrors' as defined in FormState, and pass the specific field errors\n      fieldErrors: validatedFields.error.flatten().fieldErrors,\n    }\n  }\n\n  const { email, password } = validatedFields.data\n\n  try {\n    const supabase = await createClient()\n\n    // Get user from database\n    const { data: user, error } = await supabase\n      .from('doctors')\n      .select('id, password_hash, approved')\n      .eq('email', email)\n      .single()\n\n    if (error || !user) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Verify password\n    const isValidPassword = await bcrypt.compare(password, user.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false, // Ensure 'success' is explicitly false for errors\n        message: 'Invalid email or password.',\n      }\n    }\n\n    // Check if user is approved\n    if (!user.approved) {\n      return {\n        success: false, // This is an error state, preventing login\n        message: 'Your account is pending admin approval. Please wait for approval before logging in.',\n      }\n    }\n\n    // Create user session\n    await createSession(user.id)\n  } catch (error) {\n    console.error('Login error:', error)\n    return {\n      success: false, // Ensure 'success' is explicitly false for unexpected errors\n      message: 'An unexpected error occurred.',\n    }\n  }\n\n  // If execution reaches here, it means login was successful and session created.\n  // The redirect will handle navigation, but a FormState must be returned for useActionState.\n  redirect('/dashboard')\n  // This return is theoretically unreachable but satisfies the Promise<FormState> return type\n  return { success: true, message: 'Redirecting...' };\n}\n\nexport async function logout() {\n  await deleteSession()\n  redirect('/login')\n}\n\nexport async function changePassword(doctorId: string, formData: FormData): Promise<FormState> {\n  const currentPassword = formData.get('currentPassword') as string\n  const newPassword = formData.get('newPassword') as string\n  const confirmPassword = formData.get('confirmPassword') as string\n\n  // Basic validation\n  if (!currentPassword || !newPassword || !confirmPassword) {\n    return {\n      success: false,\n      message: 'All fields are required.',\n    }\n  }\n\n  if (newPassword !== confirmPassword) {\n    return {\n      success: false,\n      message: 'New passwords do not match.',\n    }\n  }\n\n  if (newPassword.length < 8) {\n    return {\n      success: false,\n      message: 'New password must be at least 8 characters long.',\n    }\n  }\n\n  try {\n    const supabase = await createClient()\n\n    // Get current password hash\n    const { data: doctor, error: fetchError } = await supabase\n      .from('doctors')\n      .select('password_hash')\n      .eq('id', doctorId)\n      .single()\n\n    if (fetchError || !doctor) {\n      return {\n        success: false,\n        message: 'Doctor not found.',\n      }\n    }\n\n    // Verify current password\n    const isValidPassword = await bcrypt.compare(currentPassword, doctor.password_hash)\n\n    if (!isValidPassword) {\n      return {\n        success: false,\n        message: 'Current password is incorrect.',\n      }\n    }\n\n    // Hash new password\n    const hashedNewPassword = await bcrypt.hash(newPassword, 10)\n\n    // Update password\n    const { error: updateError } = await supabase\n      .from('doctors')\n      .update({ password_hash: hashedNewPassword })\n      .eq('id', doctorId)\n\n    if (updateError) {\n      console.error('Password update error:', updateError)\n      return {\n        success: false,\n        message: 'Failed to update password.',\n      }\n    }\n\n    // Refresh session with updated credentials\n    await refreshSession(doctorId)\n\n    return {\n      success: true,\n      message: 'Password changed successfully!',\n    }\n  } catch (error) {\n    console.error('Password change error:', error)\n    return {\n      success: false,\n      message: 'An unexpected error occurred.',\n    }\n  }\n}"], "names": [], "mappings": ";;;;;;IAiNsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/settings/password-change-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { X, Eye, EyeOff, Lock } from 'lucide-react'\nimport { changePassword } from '@/lib/actions/auth'\nimport { FormState } from '@/lib/types'\n\ninterface PasswordChangeModalProps {\n  isOpen: boolean\n  onClose: () => void\n  doctorId: string\n}\n\nexport function PasswordChangeModal({ isOpen, onClose, doctorId }: PasswordChangeModalProps) {\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false)\n  const [showNewPassword, setShowNewPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState<FormState | null>(null)\n\n  const handleSubmit = async (formData: FormData) => {\n    setIsLoading(true)\n    setMessage(null)\n\n    try {\n      const result = await changePassword(doctorId, formData)\n      setMessage(result)\n      \n      if (result.success) {\n        setTimeout(() => {\n          onClose()\n          setMessage(null)\n        }, 2000)\n      }\n    } catch (_error) {\n      setMessage({\n        success: false,\n        message: 'An unexpected error occurred'\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto bg-black/50 backdrop-blur-sm\">\n      <div className=\"flex items-center justify-center min-h-screen p-4\">\n        {/* Modal - Responsive sizing */}\n        <div className=\"w-full max-w-sm sm:max-w-md bg-white rounded-xl shadow-2xl transform transition-all\">\n          {/* Header - Theme matching */}\n          <div className=\"px-4 sm:px-6 py-4 border-b border-orange-100 bg-gradient-to-r from-orange-50 to-amber-50 rounded-t-xl\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Lock className=\"w-4 h-4 text-orange-600\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-slate-800\">Change Password</h3>\n              </div>\n              <button\n                onClick={onClose}\n                disabled={isLoading}\n                className=\"p-1 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-orange-100 disabled:opacity-50\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"px-4 sm:px-6 py-4\">\n            <form action={handleSubmit} className=\"space-y-4\">\n              {/* Current Password */}\n              <div>\n                <label htmlFor=\"currentPassword\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n                  Current Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"currentPassword\"\n                    name=\"currentPassword\"\n                    type={showCurrentPassword ? 'text' : 'password'}\n                    required\n                    className=\"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all\"\n                    placeholder=\"Enter current password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}\n                    className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors\"\n                  >\n                    {showCurrentPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                  </button>\n                </div>\n              </div>\n\n              {/* New Password */}\n              <div>\n                <label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n                  New Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    type={showNewPassword ? 'text' : 'password'}\n                    required\n                    minLength={8}\n                    className=\"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all\"\n                    placeholder=\"Enter new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowNewPassword(!showNewPassword)}\n                    className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors\"\n                  >\n                    {showNewPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                  </button>\n                </div>\n                <p className=\"text-xs text-slate-500 mt-1\">Must be at least 8 characters long</p>\n              </div>\n\n              {/* Confirm Password */}\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-slate-700 mb-2\">\n                  Confirm New Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    required\n                    minLength={8}\n                    className=\"w-full px-3 py-2.5 border border-orange-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-slate-800 pr-10 transition-all\"\n                    placeholder=\"Confirm new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-orange-600 transition-colors\"\n                  >\n                    {showConfirmPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                  </button>\n                </div>\n              </div>\n\n              {/* Message */}\n              {message && (\n                <div className={`p-3 rounded-lg border text-sm ${\n                  message.success \n                    ? 'bg-green-50 border-green-200 text-green-700' \n                    : 'bg-red-50 border-red-200 text-red-700'\n                }`}>\n                  <p className=\"font-medium\">{message.message}</p>\n                  {message.fieldErrors && (\n                    <div className=\"mt-2 space-y-1\">\n                      {Object.entries(message.fieldErrors).map(([field, errors]) => (\n                        <p key={field} className=\"text-xs\">\n                          {field}: {errors.join(', ')}\n                        </p>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Actions */}\n              <div className=\"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-2\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  disabled={isLoading}\n                  className=\"px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all disabled:opacity-50 order-2 sm:order-1\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 border border-transparent rounded-lg hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-[1.02] active:scale-[0.98] order-1 sm:order-2\"\n                >\n                  {isLoading ? (\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                      <span>Changing...</span>\n                    </div>\n                  ) : (\n                    'Change Password'\n                  )}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAaO,SAAS,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAA4B;;IACzF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzD,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YAC9C,WAAW;YAEX,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW;oBACT;oBACA,WAAW;gBACb,GAAG;YACL;QACF,EAAE,OAAO,QAAQ;YACf,WAAW;gBACT,SAAS;gBACT,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAEb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;;;;;;;8CAEvD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,QAAQ;4BAAc,WAAU;;8CAEpC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAgD;;;;;;sDAG3F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,uBAAuB,CAAC;oDACvC,WAAU;8DAET,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM7E,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAgD;;;;;;sDAGvF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,kBAAkB,SAAS;oDACjC,QAAQ;oDACR,WAAW;oDACX,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,mBAAmB,CAAC;oDACnC,WAAU;8DAET,gCAAkB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGvE,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAI7C,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAgD;;;;;;sDAG3F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,WAAW;oDACX,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,uBAAuB,CAAC;oDACvC,WAAU;8DAET,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gCAM5E,yBACC,6LAAC;oCAAI,WAAW,CAAC,8BAA8B,EAC7C,QAAQ,OAAO,GACX,gDACA,yCACJ;;sDACA,6LAAC;4CAAE,WAAU;sDAAe,QAAQ,OAAO;;;;;;wCAC1C,QAAQ,WAAW,kBAClB,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,QAAQ,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,iBACvD,6LAAC;oDAAc,WAAU;;wDACtB;wDAAM;wDAAG,OAAO,IAAI,CAAC;;mDADhB;;;;;;;;;;;;;;;;8CAUlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,0BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAK;;;;;;;;;;;uDAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;GA1LgB;KAAA", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/settings/settings-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Save, RotateCcw, Lock } from 'lucide-react'\nimport { PasswordChangeModal } from './password-change-modal'\ninterface SettingsFormProps {\n  doctorId: string\n  doctorName: string\n  doctorEmail: string\n}\n\nexport function SettingsForm({ doctorId, doctorName, doctorEmail }: SettingsFormProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const [messageType, setMessageType] = useState<'success' | 'error'>('success')\n  const [showPasswordModal, setShowPasswordModal] = useState(false)\n\n  const handleSave = async () => {\n    setIsLoading(true)\n    setMessage('')\n\n    try {\n      // Since we removed template config settings, just show success message\n      setMessageType('success')\n      setMessage('Settings updated successfully!')\n    } catch {\n      setMessageType('error')\n      setMessage('An unexpected error occurred')\n    } finally {\n      setIsLoading(false)\n      setTimeout(() => setMessage(''), 3000)\n    }\n  }\n\n  const handleReset = () => {\n    // No template config to reset anymore\n    setMessage('')\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Doctor Info & Security */}\n      <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4\">\n          <h3 className=\"text-lg font-medium text-slate-800 mb-2 sm:mb-0\">Doctor Information</h3>\n          <button\n            onClick={() => setShowPasswordModal(true)}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-fit\"\n          >\n            <Lock className=\"w-4 h-4\" />\n            <span>Change Password</span>\n          </button>\n        </div>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"font-medium text-slate-600\">Name:</span>\n            <span className=\"ml-2 text-slate-800\">{doctorName}</span>\n          </div>\n          <div>\n            <span className=\"font-medium text-slate-600\">Email:</span>\n            <span className=\"ml-2 text-slate-800\">{doctorEmail}</span>\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Status Message */}\n      {message && (\n        <div className={`p-4 rounded-xl border ${\n          messageType === 'success'\n            ? 'bg-gradient-to-r from-emerald-50 to-cyan-50 border-emerald-200'\n            : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'\n        }`}>\n          <p className={`text-sm font-medium flex items-center space-x-2 ${\n            messageType === 'success' ? 'text-emerald-800' : 'text-red-800'\n          }`}>\n            <span className={`w-2 h-2 rounded-full ${\n              messageType === 'success' ? 'bg-emerald-400' : 'bg-red-400'\n            }`}></span>\n            <span>{message}</span>\n          </p>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex items-center justify-between pt-6 border-t border-white/30\">\n        <button\n          onClick={handleReset}\n          className=\"flex items-center space-x-2 px-4 py-3 border border-slate-200 text-sm font-medium rounded-xl text-slate-700 bg-white/70 hover:bg-gradient-to-r hover:from-slate-50 hover:to-indigo-50 transition-all duration-300 transform hover:scale-105 active:scale-95\"\n        >\n          <RotateCcw className=\"w-4 h-4\" />\n          <span>Reset to Defaults</span>\n        </button>\n\n        <button\n          onClick={handleSave}\n          disabled={isLoading}\n          className=\"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          <Save className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />\n          <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>\n        </button>\n      </div>\n\n      {/* Password Change Modal */}\n      <PasswordChangeModal\n        isOpen={showPasswordModal}\n        onClose={() => setShowPasswordModal(false)}\n        doctorId={doctorId}\n      />\n\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAWO,SAAS,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAqB;;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,aAAa;QACjB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,uEAAuE;YACvE,eAAe;YACf,WAAW;QACb,EAAE,OAAM;YACN,eAAe;YAC<PERSON>,WAAW;QACb,SAAU;YACR,aAAa;YACb,WAAW,IAAM,WAAW,KAAK;QACnC;IACF;IAEA,MAAM,cAAc;QAClB,sCAAsC;QACtC,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,6LAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;0CAEzC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,yBACC,6LAAC;gBAAI,WAAW,CAAC,sBAAsB,EACrC,gBAAgB,YACZ,mEACA,0DACJ;0BACA,cAAA,6LAAC;oBAAE,WAAW,CAAC,gDAAgD,EAC7D,gBAAgB,YAAY,qBAAqB,gBACjD;;sCACA,6LAAC;4BAAK,WAAW,CAAC,qBAAqB,EACrC,gBAAgB,YAAY,mBAAmB,cAC/C;;;;;;sCACF,6LAAC;sCAAM;;;;;;;;;;;;;;;;;0BAMb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;kCAGR,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;0CAC7D,6LAAC;0CAAM,YAAY,cAAc;;;;;;;;;;;;;;;;;;0BAKrC,6LAAC,gKAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,UAAU;;;;;;;;;;;;AAKlB;GAvGgB;KAAA", "debugId": null}}]}