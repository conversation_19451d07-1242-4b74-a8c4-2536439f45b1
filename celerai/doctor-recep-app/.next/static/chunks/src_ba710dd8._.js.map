{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/lib/analytics.ts"], "sourcesContent": ["'use client'\n\n/**\n * Analytics Abstraction Layer - Celer AI\n * \n * SECURITY FRAMEWORK:\n * - Two-Zone Analytics: Public (full tracking) vs Authenticated (PII-free only)\n * - Zero PII Policy: Never send user_id, email, patient_name, or PHI to GA4\n * - Allowlist Approach: Only predefined safe events pass through\n * \n * CTO APPROVED EVENTS FOR AUTHENTICATED ZONE:\n * - consultation_generated: Track consultation creation (no patient data)\n * - summary_approved: Track summary approval (no content)\n * - quota_warning: Track quota threshold alerts (no user data)\n * - dashboard_viewed: Track dashboard access (no personal data)\n */\n\n// Type definitions for safe analytics events\ntype PublicPageEvent = \n  | 'page_view'\n  | 'signup_started'\n  | 'signup_completed'\n  | 'login_attempted'\n  | 'login_successful'\n  | 'blog_post_viewed'\n  | 'guide_viewed'\n\ntype AuthenticatedAppEvent = \n  | 'consultation_generated'\n  | 'summary_approved'\n  | 'quota_warning'\n  | 'dashboard_viewed'\n  | 'settings_accessed'\n  | 'template_updated'\n\ntype AnalyticsEvent = PublicPageEvent | AuthenticatedAppEvent\n\n// Safe event parameters (no PII allowed)\ninterface SafeEventParams {\n  // Only non-PII metadata allowed\n  page_title?: string\n  consultation_type?: string\n  quota_percentage?: number\n  feature_used?: string\n  error_type?: string\n  [key: string]: string | number | boolean | undefined\n}\n\n// Check if user is in authenticated app\nfunction isAuthenticatedRoute(): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const authenticatedPaths = ['/dashboard', '/info', '/settings', '/templates', '/admin']\n  return authenticatedPaths.some(path => window.location.pathname.startsWith(path))\n}\n\n// Allowlist of safe events for authenticated zone\nconst SAFE_AUTHENTICATED_EVENTS: AuthenticatedAppEvent[] = [\n  'consultation_generated',\n  'summary_approved', \n  'quota_warning',\n  'dashboard_viewed',\n  'settings_accessed',\n  'template_updated'\n]\n\n// Scrub any potentially sensitive parameters\nfunction scrubParameters(params: SafeEventParams): SafeEventParams {\n  const scrubbed: SafeEventParams = {}\n  \n  // Only allow specific safe parameters\n  const allowedParams = [\n    'page_title',\n    'consultation_type', \n    'quota_percentage',\n    'feature_used',\n    'error_type'\n  ]\n  \n  for (const [key, value] of Object.entries(params)) {\n    if (allowedParams.includes(key) && value !== undefined) {\n      // Additional validation for specific params\n      if (key === 'quota_percentage' && typeof value === 'number') {\n        scrubbed[key] = Math.round(value) // Round to avoid precision-based identification\n      } else if (typeof value === 'string' && value.length < 100) {\n        scrubbed[key] = value\n      } else if (typeof value === 'boolean') {\n        scrubbed[key] = value\n      }\n    }\n  }\n  \n  return scrubbed\n}\n\n/**\n * Main analytics tracking function\n * Enforces two-zone security model\n */\nexport function trackEvent(\n  event: AnalyticsEvent, \n  parameters: SafeEventParams = {}\n): void {\n  // Only track in browser environment\n  if (typeof window === 'undefined') return\n  \n  // Check if GA4 is loaded\n  if (typeof window.gtag !== 'function') {\n    console.warn('GA4 not loaded, skipping analytics event:', event)\n    return\n  }\n  \n  try {\n    const isAuthenticated = isAuthenticatedRoute()\n    \n    if (isAuthenticated) {\n      // AUTHENTICATED ZONE: Strict allowlist enforcement\n      if (!SAFE_AUTHENTICATED_EVENTS.includes(event as AuthenticatedAppEvent)) {\n        console.warn(`Analytics: Blocked unauthorized event in authenticated zone: ${event}`)\n        return\n      }\n      \n      // Scrub all parameters for safety\n      const scrubbedParams = scrubParameters(parameters)\n      \n      console.log(`Analytics: Tracking safe authenticated event: ${event}`, scrubbedParams)\n      window.gtag('event', event, scrubbedParams)\n      \n    } else {\n      // PUBLIC ZONE: Full tracking allowed (but still scrub for safety)\n      const scrubbedParams = scrubParameters(parameters)\n      \n      console.log(`Analytics: Tracking public event: ${event}`, scrubbedParams)\n      window.gtag('event', event, scrubbedParams)\n    }\n    \n  } catch (error) {\n    console.error('Analytics tracking error:', error)\n  }\n}\n\n/**\n * Track page views with automatic zone detection\n */\nexport function trackPageView(pageTitle?: string): void {\n  const isAuthenticated = isAuthenticatedRoute()\n  \n  if (isAuthenticated) {\n    // In authenticated zone, only track generic dashboard view\n    trackEvent('dashboard_viewed', { page_title: 'Dashboard' })\n  } else {\n    // In public zone, track full page view\n    trackEvent('page_view', { page_title: pageTitle })\n  }\n}\n\n/**\n * Track consultation events (authenticated zone only)\n */\nexport function trackConsultation(type: 'generated' | 'approved', consultationType?: string): void {\n  if (!isAuthenticatedRoute()) return\n  \n  if (type === 'generated') {\n    trackEvent('consultation_generated', { \n      consultation_type: consultationType \n    })\n  } else if (type === 'approved') {\n    trackEvent('summary_approved', { \n      consultation_type: consultationType \n    })\n  }\n}\n\n/**\n * Track quota warnings (authenticated zone only)\n */\nexport function trackQuotaWarning(percentage: number): void {\n  if (!isAuthenticatedRoute()) return\n  \n  trackEvent('quota_warning', { \n    quota_percentage: percentage \n  })\n}\n\n/**\n * Track authentication events (public zone only)\n */\nexport function trackAuth(type: 'signup_started' | 'signup_completed' | 'login_attempted' | 'login_successful'): void {\n  // Only track in public zone\n  if (isAuthenticatedRoute()) return\n\n  trackEvent(type)\n}\n\n/**\n * Initialize analytics with environment detection\n */\nexport function initializeAnalytics(): void {\n  if (typeof window === 'undefined') return\n\n  // Log analytics initialization\n  console.log('Analytics initialized with two-zone security model')\n\n  // Track initial page view\n  trackPageView(document.title)\n}\n\n// Global gtag function type declaration\ndeclare global {\n  interface Window {\n    gtag: (\n      command: 'config' | 'event' | 'js',\n      targetId: string | AnalyticsEvent,\n      config?: any\n    ) => void\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAgDA,wCAAwC;AACxC,SAAS;IACP,uCAAmC;;IAAW;IAE9C,MAAM,qBAAqB;QAAC;QAAc;QAAS;QAAa;QAAc;KAAS;IACvF,OAAO,mBAAmB,IAAI,CAAC,CAAA,OAAQ,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC7E;AAEA,kDAAkD;AAClD,MAAM,4BAAqD;IACzD;IACA;IACA;IACA;IACA;IACA;CACD;AAED,6CAA6C;AAC7C,SAAS,gBAAgB,MAAuB;IAC9C,MAAM,WAA4B,CAAC;IAEnC,sCAAsC;IACtC,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,IAAI,cAAc,QAAQ,CAAC,QAAQ,UAAU,WAAW;YACtD,4CAA4C;YAC5C,IAAI,QAAQ,sBAAsB,OAAO,UAAU,UAAU;gBAC3D,QAAQ,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,OAAO,gDAAgD;;YACpF,OAAO,IAAI,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KAAK;gBAC1D,QAAQ,CAAC,IAAI,GAAG;YAClB,OAAO,IAAI,OAAO,UAAU,WAAW;gBACrC,QAAQ,CAAC,IAAI,GAAG;YAClB;QACF;IACF;IAEA,OAAO;AACT;AAMO,SAAS,WACd,KAAqB,EACrB,aAA8B,CAAC,CAAC;IAEhC,oCAAoC;IACpC,uCAAmC;;IAAK;IAExC,yBAAyB;IACzB,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;QACrC,QAAQ,IAAI,CAAC,6CAA6C;QAC1D;IACF;IAEA,IAAI;QACF,MAAM,kBAAkB;QAExB,IAAI,iBAAiB;YACnB,mDAAmD;YACnD,IAAI,CAAC,0BAA0B,QAAQ,CAAC,QAAiC;gBACvE,QAAQ,IAAI,CAAC,CAAC,6DAA6D,EAAE,OAAO;gBACpF;YACF;YAEA,kCAAkC;YAClC,MAAM,iBAAiB,gBAAgB;YAEvC,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,OAAO,EAAE;YACtE,OAAO,IAAI,CAAC,SAAS,OAAO;QAE9B,OAAO;YACL,kEAAkE;YAClE,MAAM,iBAAiB,gBAAgB;YAEvC,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,EAAE;YAC1D,OAAO,IAAI,CAAC,SAAS,OAAO;QAC9B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAKO,SAAS,cAAc,SAAkB;IAC9C,MAAM,kBAAkB;IAExB,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,WAAW,oBAAoB;YAAE,YAAY;QAAY;IAC3D,OAAO;QACL,uCAAuC;QACvC,WAAW,aAAa;YAAE,YAAY;QAAU;IAClD;AACF;AAKO,SAAS,kBAAkB,IAA8B,EAAE,gBAAyB;IACzF,IAAI,CAAC,wBAAwB;IAE7B,IAAI,SAAS,aAAa;QACxB,WAAW,0BAA0B;YACnC,mBAAmB;QACrB;IACF,OAAO,IAAI,SAAS,YAAY;QAC9B,WAAW,oBAAoB;YAC7B,mBAAmB;QACrB;IACF;AACF;AAKO,SAAS,kBAAkB,UAAkB;IAClD,IAAI,CAAC,wBAAwB;IAE7B,WAAW,iBAAiB;QAC1B,kBAAkB;IACpB;AACF;AAKO,SAAS,UAAU,IAAoF;IAC5G,4BAA4B;IAC5B,IAAI,wBAAwB;IAE5B,WAAW;AACb;AAKO,SAAS;IACd,uCAAmC;;IAAK;IAExC,+BAA+B;IAC/B,QAAQ,GAAG,CAAC;IAEZ,0BAA0B;IAC1B,cAAc,SAAS,KAAK;AAC9B", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/zop/celerai/doctor-recep-app/src/components/analytics/analytics-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { initializeAnalytics, trackPageView } from '@/lib/analytics'\n\n/**\n * Analytics Provider Component\n * Handles client-side analytics initialization and page tracking\n * Respects the two-zone security model\n * Uses lazy initialization to avoid blocking page load\n */\nexport function AnalyticsProvider({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname()\n\n  useEffect(() => {\n    // Lazy initialize analytics after page is fully loaded and idle\n    const timer = setTimeout(() => {\n      // Use requestIdleCallback for better performance\n      if ('requestIdleCallback' in window) {\n        requestIdleCallback(() => initializeAnalytics(), { timeout: 2000 })\n      } else {\n        initializeAnalytics()\n      }\n    }, 500) // Increased delay to avoid blocking initial render\n\n    return () => clearTimeout(timer)\n  }, [])\n\n  useEffect(() => {\n    // Lazy track page changes with debouncing and idle callback\n    const timer = setTimeout(() => {\n      if ('requestIdleCallback' in window) {\n        requestIdleCallback(() => trackPageView(document.title), { timeout: 1000 })\n      } else {\n        trackPageView(document.title)\n      }\n    }, 100)\n\n    return () => clearTimeout(timer)\n  }, [pathname])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYO,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;;IAC3E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,gEAAgE;YAChE,MAAM,QAAQ;qDAAW;oBACvB,iDAAiD;oBACjD,IAAI,yBAAyB,QAAQ;wBACnC;iEAAoB,IAAM,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD;gEAAK;4BAAE,SAAS;wBAAK;oBACnE,OAAO;wBACL,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD;oBACpB;gBACF;oDAAG,KAAK,mDAAmD;;YAE3D;+CAAO,IAAM,aAAa;;QAC5B;sCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,4DAA4D;YAC5D,MAAM,QAAQ;qDAAW;oBACvB,IAAI,yBAAyB,QAAQ;wBACnC;iEAAoB,IAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK;gEAAG;4BAAE,SAAS;wBAAK;oBAC3E,OAAO;wBACL,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK;oBAC9B;gBACF;oDAAG;YAEH;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC;KAAS;IAEb,qBAAO;kBAAG;;AACZ;GA/BgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}]}