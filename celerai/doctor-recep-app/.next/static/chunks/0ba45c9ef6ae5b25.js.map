{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/shared/lib/segment.ts", "turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-status-code.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/is-next-router-error.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/hooks-client-context.shared-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/async-local-storage.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/work-async-storage-instance.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/work-async-storage.external.ts", "turbopack:///[project]/node_modules/next/src/client/components/app-router-headers.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/app-router-context.shared-runtime.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/action-async-storage-instance.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/action-async-storage.external.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect.ts", "turbopack:///[project]/node_modules/next/src/client/components/not-found.ts", "turbopack:///[project]/node_modules/next/src/client/components/forbidden.ts", "turbopack:///[project]/node_modules/next/src/client/components/unauthorized.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.browser.ts", "turbopack:///[project]/node_modules/next/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/node_modules/next/src/server/lib/router-utils/is-postpone.ts", "turbopack:///[project]/node_modules/next/src/client/components/hooks-server-context.ts", "turbopack:///[project]/node_modules/next/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/work-unit-async-storage-instance.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/work-unit-async-storage.external.ts", "turbopack:///[project]/node_modules/next/src/lib/metadata/metadata-constants.tsx", "turbopack:///[project]/node_modules/next/src/lib/scheduler.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/dynamic-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.server.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.react-server.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/server-inserted-html.shared-runtime.tsx", "turbopack:///[project]/node_modules/next/src/client/components/bailout-to-client-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n", "import type { AsyncLocalStorage } from 'async_hooks'\n\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\n  'Invariant: AsyncLocalStorage accessed in runtime where it is not available'\n)\n\nclass FakeAsyncLocalStorage<Store extends {}>\n  implements AsyncLocalStorage<Store>\n{\n  disable(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  getStore(): Store | undefined {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined\n  }\n\n  run<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  exit<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  enterWith(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  static bind<T>(fn: T): T {\n    return fn\n  }\n}\n\nconst maybeGlobalAsyncLocalStorage =\n  typeof globalThis !== 'undefined' && (globalThis as any).AsyncLocalStorage\n\nexport function createAsyncLocalStorage<\n  Store extends {},\n>(): AsyncLocalStorage<Store> {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage()\n  }\n  return new FakeAsyncLocalStorage()\n}\n\nexport function bindSnapshot<T>(fn: T): T {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn)\n  }\n  return FakeAsyncLocalStorage.bind(fn)\n}\n\nexport function createSnapshot(): <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot()\n  }\n  return function (fn: any, ...args: any[]) {\n    return fn(...args)\n  }\n}\n", "import type { WorkAsyncStorage } from './work-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const workAsyncStorageInstance: WorkAsyncStorage =\n  createAsyncLocalStorage()\n", "import type { AsyncLocalStorage } from 'async_hooks'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { FetchMetrics } from '../base-http'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { AfterContext } from '../after/after-context'\nimport type { CacheLife } from '../use-cache/cache-life'\n\n// Share the instance module in the next-shared layer\nimport { workAsyncStorageInstance } from './work-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { LazyResult } from '../lib/lazy-result'\n\nexport interface WorkStore {\n  readonly isStaticGeneration: boolean\n\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  readonly page: string\n\n  /**\n   * The route that is being rendered. This is the page property without the\n   * trailing `/page` or `/route` suffix.\n   */\n  readonly route: string\n\n  /**\n   * The set of unknown route parameters. Accessing these will be tracked as\n   * a dynamic access.\n   */\n  readonly fallbackRouteParams: FallbackRouteParams | null\n\n  readonly incrementalCache?: IncrementalCache\n  readonly cacheLifeProfiles?: { [profile: string]: CacheLife }\n\n  readonly isOnDemandRevalidate?: boolean\n  readonly isPrerendering?: boolean\n  readonly isRevalidate?: boolean\n\n  forceDynamic?: boolean\n  fetchCache?: AppSegmentConfig['fetchCache']\n\n  forceStatic?: boolean\n  dynamicShouldError?: boolean\n  pendingRevalidates?: Record<string, Promise<any>>\n  pendingRevalidateWrites?: Array<Promise<void>> // This is like pendingRevalidates but isn't used for deduping.\n  readonly afterContext: AfterContext\n\n  dynamicUsageDescription?: string\n  dynamicUsageStack?: string\n\n  /**\n   * Invalid usage errors might be caught in userland. We attach them to the\n   * work store to ensure we can still fail the build or dev render.\n   */\n  // TODO: Collect an array of errors, and throw as AggregateError when\n  // `serializeError` and the Dev Overlay support it.\n  invalidUsageError?: Error\n\n  nextFetchId?: number\n  pathWasRevalidated?: boolean\n\n  /**\n   * Tags that were revalidated during the current request. They need to be sent\n   * to cache handlers to propagate their revalidation.\n   */\n  pendingRevalidatedTags?: string[]\n\n  /**\n   * Tags that were previously revalidated (e.g. by a redirecting server action)\n   * and have already been sent to cache handlers. Retrieved cache entries that\n   * include any of these tags must be discarded.\n   */\n  readonly previouslyRevalidatedTags: readonly string[]\n\n  /**\n   * This map contains lazy results so that we can evaluate them when the first\n   * cache entry is read. It allows us to skip refreshing tags if no caches are\n   * read at all.\n   */\n  readonly refreshTagsByCacheKind: Map<string, LazyResult<void>>\n\n  fetchMetrics?: FetchMetrics\n\n  isDraftMode?: boolean\n  isUnstableNoStore?: boolean\n  isPrefetchRequest?: boolean\n\n  requestEndedState?: { ended?: boolean }\n\n  buildId: string\n\n  readonly reactLoadableManifest?: DeepReadonly<\n    Record<string, { files: string[] }>\n  >\n  readonly assetPrefix?: string\n\n  dynamicIOEnabled: boolean\n  dev: boolean\n}\n\nexport type WorkAsyncStorage = AsyncLocalStorage<WorkStore>\n\nexport { workAsyncStorageInstance as workAsyncStorage }\n", "export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n", "'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  /**\n   * The timestamp of the navigation that last updated the CacheNode's data. If\n   * a CacheNode is reused from a previous navigation, this value is not\n   * updated. Used to track the staleness of the data.\n   */\n  navigatedAt: number\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n\n  navigatedAt: number\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n", "import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n", "import type { ActionAsyncStorage } from './action-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const actionAsyncStorageInstance: ActionAsyncStorage =\n  createAsyncLocalStorage()\n", "import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { actionAsyncStorageInstance } from './action-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nexport interface ActionStore {\n  readonly isAction?: boolean\n  readonly isAppRoute?: boolean\n}\n\nexport type ActionAsyncStorage = AsyncLocalStorage<ActionStore>\n\nexport { actionAsyncStorageInstance as actionAsyncStorage }\n", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n", "const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorageInstance: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n", "import type { AsyncLocalStorage } from 'async_hooks'\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { ReadonlyHeaders } from '../web/spec-extension/adapters/headers'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { CacheSignal } from './cache-signal'\nimport type { DynamicTrackingState } from './dynamic-rendering'\n\n// Share the instance module in the next-shared layer\nimport { workUnitAsyncStorageInstance } from './work-unit-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type {\n  RenderResumeDataCache,\n  PrerenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\nimport type { ImplicitTags } from '../lib/implicit-tags'\nimport type { WorkStore } from './work-async-storage.external'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../client/components/app-router-headers'\n\nexport type WorkUnitPhase = 'action' | 'render' | 'after'\n\nexport interface CommonWorkUnitStore {\n  /** NOTE: Will be mutated as phases change */\n  phase: WorkUnitPhase\n  readonly implicitTags: ImplicitTags\n}\n\nexport interface RequestStore extends CommonWorkUnitStore {\n  type: 'request'\n\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL.\n   */\n  readonly url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    readonly pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    readonly search: string\n  }\n\n  readonly headers: ReadonlyHeaders\n  // This is mutable because we need to reassign it when transitioning from the action phase to the render phase.\n  // The cookie object itself is deliberately read only and thus can't be updated.\n  cookies: ReadonlyRequestCookies\n  readonly mutableCookies: ResponseCookies\n  readonly userspaceMutableCookies: ResponseCookies\n  readonly draftMode: DraftModeProvider\n  readonly isHmrRefresh?: boolean\n  readonly serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  readonly rootParams: Params\n\n  /**\n   * The resume data cache for this request. This will be a immutable cache.\n   */\n  renderResumeDataCache: RenderResumeDataCache | null\n\n  // DEV-only\n  usedDynamic?: boolean\n  prerenderPhase?: boolean\n}\n\n/**\n * The Prerender store is for tracking information related to prerenders.\n *\n * It can be used for both RSC and SSR prerendering and should be scoped as close\n * to the individual `renderTo...` API call as possible. To keep the type simple\n * we don't distinguish between RSC and SSR prerendering explicitly but instead\n * use conditional object properties to infer which mode we are in. For instance cache tracking\n * only needs to happen during the RSC prerender when we are prospectively prerendering\n * to fill all caches.\n */\nexport interface PrerenderStoreModern extends CommonWorkUnitStore {\n  type: 'prerender'\n\n  /**\n   * This signal is aborted when the React render is complete. (i.e. it is the same signal passed to react)\n   */\n  readonly renderSignal: AbortSignal\n  /**\n   * This is the AbortController which represents the boundary between Prerender and dynamic. In some renders it is\n   * the same as the controller for the renderSignal but in others it is a separate controller. It should be aborted\n   * whenever the we are no longer in the prerender phase of rendering. Typically this is after one task or when you call\n   * a sync API which requires the prerender to end immediately\n   */\n  readonly controller: AbortController\n\n  /**\n   * when not null this signal is used to track cache reads during prerendering and\n   * to await all cache reads completing before aborting the prerender.\n   */\n  readonly cacheSignal: null | CacheSignal\n\n  /**\n   * During some prerenders we want to track dynamic access.\n   */\n  readonly dynamicTracking: null | DynamicTrackingState\n\n  readonly rootParams: Params\n\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache | null\n\n  // DEV ONLY\n  // When used this flag informs certain APIs to skip logging because we're\n  // not part of the primary render path and are just prerendering to produce\n  // validation results\n  validating?: boolean\n\n  /**\n   * The HMR refresh hash is only provided in dev mode. It is needed for the dev\n   * warmup render to ensure that the cache keys will be identical for the\n   * subsequent dynamic render.\n   */\n  readonly hmrRefreshHash: string | undefined\n}\n\nexport interface PrerenderStorePPR extends CommonWorkUnitStore {\n  type: 'prerender-ppr'\n  readonly rootParams: Params\n  readonly dynamicTracking: null | DynamicTrackingState\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache\n}\n\nexport interface PrerenderStoreLegacy extends CommonWorkUnitStore {\n  type: 'prerender-legacy'\n  readonly rootParams: Params\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n}\n\nexport type PrerenderStore =\n  | PrerenderStoreLegacy\n  | PrerenderStorePPR\n  | PrerenderStoreModern\n\nexport interface CommonCacheStore\n  extends Omit<CommonWorkUnitStore, 'implicitTags'> {\n  /**\n   * A cache work unit store might not always have an outer work unit store,\n   * from which implicit tags could be inherited.\n   */\n  readonly implicitTags: ImplicitTags | undefined\n}\n\nexport interface UseCacheStore extends CommonCacheStore {\n  type: 'cache'\n  // Collected revalidate times and tags for this cache entry during the cache render.\n  revalidate: number // implicit revalidate time from inner caches / fetches\n  expire: number // server expiration time\n  stale: number // client expiration time\n  explicitRevalidate: undefined | number // explicit revalidate time from cacheLife() calls\n  explicitExpire: undefined | number // server expiration time\n  explicitStale: undefined | number // client expiration time\n  tags: null | string[]\n  readonly hmrRefreshHash: string | undefined\n  readonly isHmrRefresh: boolean\n  readonly serverComponentsHmrCache: ServerComponentsHmrCache | undefined\n  readonly forceRevalidate: boolean\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\nexport interface UnstableCacheStore extends CommonCacheStore {\n  type: 'unstable-cache'\n  // Draft mode is only available if the outer work unit store is a request\n  // store and draft mode is enabled.\n  readonly draftMode: DraftModeProvider | undefined\n}\n\n/**\n * The Cache store is for tracking information inside a \"use cache\" or unstable_cache context.\n * Inside this context we should never expose any request or page specific information.\n */\nexport type CacheStore = UseCacheStore | UnstableCacheStore\n\nexport type WorkUnitStore = RequestStore | CacheStore | PrerenderStore\n\nexport type WorkUnitAsyncStorage = AsyncLocalStorage<WorkUnitStore>\n\nexport { workUnitAsyncStorageInstance as workUnitAsyncStorage }\n\nexport function getExpectedRequestStore(\n  callingExpression: string\n): RequestStore {\n  const workUnitStore = workUnitAsyncStorageInstance.getStore()\n\n  if (!workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return workUnitStore\n\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // This should not happen because we should have checked it already.\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`\n      )\n\n    case 'cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n\n    case 'unstable-cache':\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nexport function throwForMissingRequestStore(callingExpression: string): never {\n  throw new Error(\n    `\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`\n  )\n}\n\nexport function getPrerenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): PrerenderResumeDataCache | null {\n  if (\n    workUnitStore.type === 'prerender' ||\n    workUnitStore.type === 'prerender-ppr'\n  ) {\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getRenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): RenderResumeDataCache | null {\n  if (\n    workUnitStore.type !== 'prerender-legacy' &&\n    workUnitStore.type !== 'cache' &&\n    workUnitStore.type !== 'unstable-cache'\n  ) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.renderResumeDataCache\n    }\n\n    // We return the mutable resume data cache here as an immutable version of\n    // the cache as it can also be used for reading.\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getHmrRefreshHash(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): string | undefined {\n  if (!workStore.dev) {\n    return undefined\n  }\n\n  return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender'\n    ? workUnitStore.hmrRefreshHash\n    : workUnitStore.type === 'request'\n      ? workUnitStore.cookies.get(NEXT_HMR_REFRESH_HASH_COOKIE)?.value\n      : undefined\n}\n\n/**\n * Returns a draft mode provider only if draft mode is enabled.\n */\nexport function getDraftModeProviderForCacheScope(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): DraftModeProvider | undefined {\n  if (workStore.isDraftMode) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n      case 'request':\n        return workUnitStore.draftMode\n      default:\n        return undefined\n    }\n  }\n\n  return undefined\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "'use client'\n\nimport React, { useContext } from 'react'\n\nexport type ServerInsertedHTMLHook = (callbacks: () => React.ReactNode) => void\n\n// Use `React.createContext` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { createContext } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport const ServerInsertedHTMLContext =\n  React.createContext<ServerInsertedHTMLHook | null>(null as any)\n\nexport function useServerInsertedHTML(callback: () => React.ReactNode): void {\n  const addInsertedServerHTMLCallback = useContext(ServerInsertedHTMLContext)\n  // Should have no effects on client where there's no flush effects provider\n  if (addInsertedServerHTMLCallback) {\n    addInsertedServerHTMLCallback(callback)\n  }\n}\n", "import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n", "import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify", "HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "errorCode", "type", "destination", "slice", "join", "at", "statusCode", "isNaN", "isNextRouterError", "BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "err", "process", "env", "NODE_ENV", "PathParamsContext", "PathnameContext", "SearchParamsContext", "createContext", "bindSnapshot", "createAsyncLocalStorage", "createSnapshot", "sharedAsyncLocalStorageNotAvailableError", "FakeAsyncLocalStorage", "disable", "getStore", "undefined", "run", "exit", "enterWith", "bind", "fn", "maybeGlobalAsyncLocalStorage", "globalThis", "AsyncLocalStorage", "snapshot", "args", "workAsyncStorageInstance", "workAsyncStorage", "ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER", "AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "getSegmentValue", "Array", "isArray", "actionAsyncStorageInstance", "actionAsyncStorage", "getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "window", "require", "url", "TemporaryRedirect", "isAction", "push", "replace", "PermanentRedirect", "notFound", "DIGEST", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "forbidden", "unauthorized", "unstable_rethrow", "cause", "isHangingPromiseRejectionError", "makeHangingPromise", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "currentListeners", "get", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "$$typeof", "DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "description", "StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "code", "workUnitAsyncStorageInstance", "getDraftModeProviderForCacheScope", "getExpectedRequestStore", "getHmrRefreshHash", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "throwForMissingRequestStore", "workUnitAsyncStorage", "callingExpression", "workUnitStore", "_exhaustiveCheck", "prerenderResumeDataCache", "renderResumeDataCache", "workStore", "dev", "hmrRefreshHash", "cookies", "value", "isDraftMode", "draftMode", "METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "NEXT_RUNTIME", "atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "resolve", "then", "nextTick", "setImmediate", "r", "Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "store", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "prerenderStore", "_store", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "controller", "abort", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "NEXT_PRERENDER_INTERRUPTED", "serverDynamic", "clientDynamic", "filter", "access", "map", "line", "AbortController", "x", "cacheSignal", "inputReady", "isStaticGeneration", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "hasViewportRegex", "hasOutletRegex", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "ReadonlyURLSearchParams", "ReadonlyURLSearchParamsError", "URLSearchParams", "append", "delete", "sort", "ServerInsertedHTMLContext", "useServerInsertedHTML", "callback", "addInsertedServerHTMLCallback", "useContext", "bailoutToClientRendering", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "readonlySearchParams", "useMemo", "router", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "segmentValue", "context", "parentTree", "selectedLayoutSegments", "selectedLayoutSegment"], "mappings": "uOA4BaA,mBAAmB,CAAA,kBAAnBA,GADAC,gBAAgB,CAAA,kBAAhBA,GAhBGC,4BAA4B,CAAA,kBAA5BA,GATAC,cAAc,CAAA,kBAAdA,GAKAC,sBAAsB,CAAA,kBAAtBA,uEALT,SAASD,EAAeE,CAAe,EAE5C,MAAsB,MAAfA,CAAO,CAAC,EAAE,EAAYA,EAAQC,QAAQ,CAAC,IAChD,CAEO,SAASF,EAAuBC,CAAe,EACpD,OAAOA,EAAQE,UAAU,CAAC,MAAoB,cAAZF,CACpC,CAEO,SAASH,EACdG,CAAgB,CAChBG,CAA2D,EAI3D,GAFsBH,CAElBI,CAF0BC,QAAQ,CAACT,GAEpB,CACjB,IAAMU,EAAmBC,KAAKC,SAAS,CAACL,GACxC,MAA4B,OAArBG,EACHV,EAAmB,IAAMU,EACzBV,CACN,CAEA,OAAOI,CACT,CAEO,IAAMJ,EAAmB,WACnBD,EAAsB,wIC5BtBc,qBAAqB,CAAA,kBAArBA,GAQAC,8BAA8B,CAAA,kBAA9BA,GAuCGC,kCAAkC,CAAA,kBAAlCA,GAPAC,2BAA2B,CAAA,kBAA3BA,GAnBAC,yBAAyB,CAAA,kBAAzBA,uEArBT,IAAMJ,EAAwB,CACnCK,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAEMC,EAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX,IAE/BC,EAAiC,2BAavC,SAASG,EACdQ,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAET,GAAM,CAACC,EAAQC,EAAW,CAAGH,EAAMC,MAAM,CAACG,KAAK,CAAC,KAEhD,OACEF,IAAWb,GACXO,EAAcS,GAAG,CAACC,OAAOH,GAE7B,CAEO,SAASZ,EACdS,CAA8B,EAG9B,OAAOM,OAAOH,AADKH,EAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAASd,EACdiB,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,qWC5DYC,qBAAAA,qCAAAA,KAAL,IAAKA,IAAAA,iBAAAA,CAAAA,UAAAA,GAAAA,gGAAAA,gVCIAE,KAFCD,OAEDC,YAFoB,CAAA,kBAAnBD,GAEDC,YAAY,CAAA,kBAAZA,GAgBIC,eAAe,CAAA,kBAAfA,+EApBmB,CAAA,CAAA,IAAA,IAEtBF,EAAsB,gBAE5B,IAAKC,eAAAA,WAAAA,GAAAA,aAAAA,GAgBL,SAASC,EAAgBX,CAAc,EAC5C,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAGT,IAAMA,EAASD,EAAMC,MAAM,CAACG,KAAK,CAAC,KAC5B,CAACQ,EAAWC,EAAK,CAAGZ,EACpBa,EAAcb,EAAOc,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAGvCE,EAAaZ,OAFJL,AAEWM,EAFJU,EAAE,CAAC,CAAC,IAI1B,OACEL,IAAcH,IACJ,YAATI,GAA+B,IAA/BA,KAAsBA,CAAS,CAAK,EACrC,AAAuB,iBAAhBC,GACP,CAACK,MAAMD,IACPA,KAAcV,EAAAA,kBAAkB,AAEpC,iWCjCgBY,oBAAAA,qCAAAA,aART,CAAA,CAAA,IAAA,QAC6C,CAAA,CAAA,IAAA,IAO7C,SAASA,EACdpB,CAAc,EAEd,MAAOW,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACX,IAAUR,CAAAA,EAAAA,EAAAA,yBAAyB,AAAzBA,EAA0BQ,EAC7D,kVCXaqB,iBAAiB,CAAA,kBAAjBA,GASGC,mBAAmB,CAAA,kBAAnBA,uEAZhB,IAAMC,EAAiB,kCAGhB,OAAMF,UAA0BG,MAGrCC,YAA4BC,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZzB,MAAAA,CAASsB,CAIzB,CACF,CAGO,SAASD,EAAoBK,CAAY,QAC9C,AAAmB,UAAf,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAItDA,AAJwD,EAIpD1B,CAJwD,KAIlD,GAAKsB,CACxB,uDCV6B,EAAA,CAAA,CAAA,6EAFhBQ,iBAAiB,CAAA,kBAAjBA,GADAC,eAAe,CAAA,kBAAfA,GADAC,mBAAmB,CAAA,kBAAnBA,+EAHiB,CAAA,CAAA,IAAA,IAGjBA,EAAsBC,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAyB,MAC5DF,EAAkBE,GAAAA,EAAAA,aAAAA,AAAa,EAAgB,MAC/CH,EAAoBG,CAAAA,EAAAA,EAAAA,aAAAA,AAAa,EAAgB,+HCwC9CC,YAAY,CAAA,kBAAZA,GATAC,uBAAuB,CAAA,kBAAvBA,GAgBAC,cAAc,CAAA,kBAAdA,uEApDhB,IAAMC,EAA2C,OAAA,cAEhD,CAFgD,AAAId,MACnD,8EAD+C,oBAAA,OAAA,mBAAA,gBAAA,CAEjD,EAEA,OAAMe,EAGJC,SAAgB,CACd,MAAMF,CACR,CAEAG,UAA8B,CAG9B,CAEAE,KAAY,CACV,MAAML,CACR,CAEAM,MAAa,CACX,MAAMN,CACR,CAEAO,WAAkB,CAChB,MAAMP,CACR,CAEA,OAAOQ,KAAQC,CAAK,CAAK,CACvB,OAAOA,CACT,CACF,CAEA,IAAMC,EACkB,AAAtB,oBAAOC,YAA+BA,WAAmBC,iBAAiB,CAErE,SAASd,WAGd,AAAIY,EACK,IAAIA,EAEN,IAAIT,CACb,CAEO,SAASJ,EAAgBY,CAAK,IAND,KAOlC,AAAIC,EACKA,EAA6BF,IAAI,CAACC,GAEpCR,EAAsBO,IAAI,CAACC,EACpC,CAEO,QAN6B,CAMpBV,WAId,AAAIW,EACKA,EAA6BG,QAAQ,GAEvC,SAAUJ,CAAO,CAAE,GAAGK,CAHK,AAGM,EACtC,OAAOL,KAAMK,EACf,CACF,6IC7DaC,2BAAAA,qCAAAA,KAAN,IAAMA,EACXjB,CAAAA,EAAAA,EAHsC,CAAA,CAAA,IAAA,GAGtCA,uBAAAA,AAAuB,+ICoGYkB,mBAAAA,qCAA5BD,EAAAA,wBAAwB,YA9FQ,CAAA,CAAA,IAAA,8HCT5BE,aAAa,CAAA,kBAAbA,GAiBAC,cAAc,CAAA,kBAAdA,GAWAC,wBAAwB,CAAA,kBAAxBA,GAfAC,4BAA4B,CAAA,kBAA5BA,GADAC,uBAAuB,CAAA,kBAAvBA,GAmBAC,wBAAwB,CAAA,kBAAxBA,GAFAC,0BAA0B,CAAA,kBAA1BA,GACAC,2BAA2B,CAAA,kBAA3BA,GAzBAC,2BAA2B,CAAA,kBAA3BA,GAKAC,mCAAmC,CAAA,kBAAnCA,GAiBAC,6BAA6B,CAAA,kBAA7BA,GAvBAC,6BAA6B,CAAA,kBAA7BA,GAqBAC,oBAAoB,CAAA,kBAApBA,GAXAC,QAAQ,CAAA,kBAARA,GACAC,uBAAuB,CAAA,kBAAvBA,GAhBAC,UAAU,CAAA,kBAAVA,uEAAN,IAAMA,EAAa,MACbf,EAAgB,cAIhBW,EAAgC,yBAChCH,EAA8B,uBAK9BC,EACX,+BACWL,EAA0B,mBAC1BD,EAA+B,4BAC/BU,EAAW,WACXC,EAA0B,mBAE1Bb,EAAiB,CAC5Bc,EACAJ,EACAH,EACAJ,EACAK,EACD,CAEYG,EAAuB,OAEvBF,EAAgC,sBAChCR,EAA2B,qBAC3BI,EAA6B,0BAC7BC,EAA8B,2BAC9BF,EAA2B,gSCkJX,EAAA,CAAA,CAAA,6EAlBhBW,gBAAgB,CAAA,kBAAhBA,GAUAC,yBAAyB,CAAA,kBAAzBA,GAPAC,mBAAmB,CAAA,kBAAnBA,GAsBAC,kBAAkB,CAAA,kBAAlBA,GATAC,eAAe,CAAA,kBAAfA,6FArKK,CAAA,CAAA,IAAA,KAqJLJ,EAAmBK,EAAAA,OAAK,CAAC1C,aAAa,CACjD,MAEWuC,EAAsBG,EAAAA,OAAK,CAAC1C,aAAa,CAK5C,MAEGsC,EAA4BI,EAAAA,OAAK,CAAC1C,aAAa,CAIzD,MAEUyC,EAAkBC,EAAAA,OAAK,CAAC1C,aAAa,CAAkB,MASvDwC,EAAqBE,EAAAA,OAAK,CAAC1C,aAAa,CAAc,IAAIrC,sECvLhE,SAASgF,EAAgBlG,CAAgB,EAC9C,OAAOmG,MAAMC,OAAO,CAACpG,GAAWA,CAAO,CAAC,EAAE,CAAGA,CAC/C,0EAFgBkG,kBAAAA,qCAAAA,sWCCHG,6BAAAA,qCAAAA,KAAN,IAAMA,EACX5C,CAAAA,EAAAA,EAHsC,CAAA,CAAA,IAAA,GAGtCA,uBAAAA,AAAuB,+ICOc6C,qBAAAA,qCAA9BD,EAAAA,0BAA0B,YARQ,CAAA,CAAA,IAAA,8HCY3BE,gBAAgB,CAAA,kBAAhBA,GA6EAC,8BAA8B,CAAA,kBAA9BA,GARAC,wBAAwB,CAAA,kBAAxBA,GARAC,uBAAuB,CAAA,kBAAvBA,GAhBAC,iBAAiB,CAAA,kBAAjBA,GAvBAC,QAAQ,CAAA,kBAARA,+EArCmB,CAAA,CAAA,IAAA,QAM5B,CAAA,CAAA,IAAA,IAEDN,EACc,aAAlB,OAAOO,OAEDC,EAAQ,CAAA,CAAA,IAAA,IACRR,kBAAkB,MACpBvC,EAEC,SAASwC,EACdQ,CAAW,CACX7E,CAAkB,CAClBK,CAAqE,EAArEA,KAAAA,OAAAA,GAAiCV,EAAAA,kBAAkB,CAACmF,iBAAAA,AAAiB,EAErE,IAAM3F,EAAQ,OAAA,cAA8B,CAA9B,AAAIwB,MAAMf,EAAAA,mBAAmB,EAA7B,oBAAA,OAAA,kBAAA,iBAAA,CAA6B,GAE3C,OADAT,EAAMC,MAAM,CAAMQ,EAAAA,mBAAmB,CAAC,IAAGI,EAAK,IAAG6E,EAAI,IAAGxE,EAAW,IAC5DlB,CACT,CAcO,SAASuF,EAEdG,CAAW,CACX7E,CAAmB,IAFnB,EAISoE,CAIT,OAJApE,MAAAA,CAAAA,GAAAA,EAASoE,CAAAA,IAJkB,EAIlBA,CAAAA,EAAAA,AAA4B,GAA5BA,IAAAA,EAAAA,EAAoBxC,QAAQ,EAAA,CAAA,CAAA,KAAA,EAA5BwC,EAAgCW,QAAQ,EAC7ClF,EAAAA,YAAY,CAACmF,IAAI,CACjBnF,EAAAA,YAAY,CAACoF,OAAO,AAAPA,EAEXZ,EAAiBQ,EAAK7E,EAAML,EAAAA,kBAAkB,CAACmF,iBAAiB,CACxE,CAaO,SAASL,EAEdI,CAAW,CACX7E,CAAyC,EAEzC,MAFAA,KAAAA,AAFA,IAEAA,IAAAA,EAAqBH,EAAAA,YAAY,CAACoF,EAFP,KAEOA,AAAO,EAEnCZ,EAAiBQ,EAAK7E,EAAML,EAAAA,kBAAkB,CAACuF,iBAAiB,CACxE,CAUO,SAASV,EAAwBrF,CAAc,QAC/CW,AAAL,CAAKA,EAAAA,CAAD,CAACA,eAAAA,AAAe,EAACX,GAIdA,EAAMC,GAJgB,GAIV,CAACG,KAAK,CAAC,KAAKW,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAJb,IAKtC,CAEO,SAASoE,EAAyBpF,CAAoB,EAC3D,GAAI,CAACW,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACX,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAA7BwB,AAAJ,MAAU,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOxB,EAAMC,MAAM,CAACG,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,AACtC,CAEO,SAAS+E,EAA+BnF,CAAoB,EACjE,GAAI,CAACW,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACX,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAIwB,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOlB,OAAON,EAAMC,MAAM,CAACG,KAAK,CAAC,KAAKa,EAAE,CAAC,CAAC,GAC5C,mWC5EgB+E,WAAAA,qCAAAA,KAFhB,IAAMC,EAAU,GAAE5G,EAjBX,CAAA,CAAA,IAAA,IAiBWA,8BAA8B,CAAC,OAE1C,SAAS2G,IAEd,IAAMhG,EAAQ,OAAA,cAAiB,CAAjB,AAAIwB,MAAMyE,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgB,EAG9B,OAFEjG,EAAkCC,MAAM,CAAGgG,EAEvCjG,CACR,yRCPO,SAASmG,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI3E,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,CAVmB0E,CAAmC,CAAA,CAAA,CAAA,iFADtCC,YAAAA,qCAAAA,KAFE9G,EAhBX,CAAA,CAAA,IAAA,IAgBWA,8BAA8B,GAAC,qRCG1C,SAAS+G,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI5E,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,CAVmB0E,CAAmC,CAAA,CAAA,CAAA,iFADtCE,eAAAA,qCAAAA,KAFE/G,EAjBX,CAAA,CAAA,IAAA,IAiBWA,8BAA8B,GAAC,8VCjBjCgH,mBAAAA,qCAAAA,AAAT,SAASA,EAAiBrG,CAAc,EAC7C,GAAIoB,CAAAA,EAAAA,EAAAA,iBAAiB,AAAjBA,EAAkBpB,IAAUsB,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACtB,GAClD,KAD0D,CACpDA,EAGJA,aAAiBwB,OAAS,UAAWxB,GACvCqG,EAAiBrG,EAD6B,AACvBsG,KAAK,CAEhC,aAXoC,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,mVCDlBC,8BAA8B,CAAA,kBAA9BA,GAgCAC,kBAAkB,CAAA,kBAAlBA,uEAhCT,SAASD,EACd5E,CAAY,QAEO,AAAnB,UAAI,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAI1B,CAJwD,KAIlD,GAAKwG,CACxB,CAEA,IAAMA,EAA4B,2BAElC,OAAMC,UAAqClF,MAGzCC,YAA4BkF,CAAkB,CAAE,CAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,qJAAqJ,CAAC,EAAA,IAAA,CAFnRA,UAAAA,CAAAA,EAAAA,IAAAA,CAFZ1G,MAAAA,CAASwG,CAMzB,CACF,CAGA,IAAMG,EAAyB,IAAIC,QAS5B,SAASL,EACdM,CAAmB,CACnBH,CAAkB,EAElB,GAAIG,EAAOC,OAAO,CAChB,CADkB,MACXC,QAAQC,MAAM,CAAC,IAAIP,EAA6BC,GAClD,EACL,IAAMO,EAAiB,IAAIF,QAAW,CAACG,EAAGF,KACxC,IAAMG,EAAiBH,EAAOnE,IAAI,CAChC,KACA,IAAI4D,EAA6BC,IAE/BU,EAAmBT,EAAuBU,GAAG,CAACR,GAClD,GAAIO,EACFA,EAAiBxB,IAAI,CAACuB,OACjB,CACL,CAHoB,GAGdG,EAAY,CAACH,EAAe,CAClCR,EAAuBY,GAAG,CAACV,EAAQS,GACnCT,EAAOW,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUI,MAAM,CAAED,IAAK,AACzCH,CAAS,CAACG,EAAE,EAEhB,EACA,CAAEE,MAAM,CAAK,EAEjB,CACF,GAKA,OADAV,EAAeW,KAAK,CAACC,GACdZ,CACT,CACF,CAEA,SAASY,IAAgB,4ICnETC,aAAAA,qCAAAA,KAFhB,IAAMC,EAA8BC,OAAOC,GAAG,CAAC,kBAExC,SAASH,EAAW/H,CAAU,EACnC,MACmB,UAAjB,OAAOA,GACG,OAAVA,GACAA,EAAMmI,QAAQ,GAAKH,CAEvB,4HCNaI,kBAAkB,CAAA,kBAAlBA,GAQGC,oBAAoB,CAAA,kBAApBA,uEAVhB,IAAMC,EAAqB,sBAEpB,OAAMF,UAA2B5G,MAGtCC,YAA4B8G,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5BtI,MAAAA,CAAoCqI,CAIpC,CACF,CAEO,SAASD,EAAqB1G,CAAY,QAE9B,AADjB,UACE,OAAOA,GACC,OAARA,CACA,CAAE,CAAA,WAAYA,GAAE,AACM,UAAtB,AACA,OADOA,EAAI1B,MAAM,EAKZ0B,EAAI1B,MAAM,GAAKqI,CACxB,kVCnBaE,qBAAqB,CAAA,kBAArBA,GAIGC,uBAAuB,CAAA,kBAAvBA,uEANhB,IAAMC,EAA0B,yBAEzB,OAAMF,UAA8BhH,wBAApC,KAAA,IAAA,GAAA,IAAA,CACWmH,IAAAA,CAAOD,EACzB,CAEO,SAASD,EACdzI,CAAc,QAEO,AAArB,UAAI,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAJ4D,AAItD2I,GAJ0D,CAItD,GAAKD,CACxB,mWCXaE,+BAAAA,qCAAAA,KAAN,IAAMA,EACXxG,CAAAA,EAAAA,EAJsC,CAAA,CAAA,IAAA,GAItCA,uBAAAA,AAAuB,8HC6STyG,iCAAiC,CAAA,kBAAjCA,GA9FAC,uBAAuB,CAAA,kBAAvBA,GA4EAC,iBAAiB,CAAA,kBAAjBA,GAjCAC,2BAA2B,CAAA,kBAA3BA,GAaAC,wBAAwB,CAAA,kBAAxBA,GAnBAC,2BAA2B,CAAA,kBAA3BA,GAvCyBC,oBAAoB,CAAA,kBAApDP,EAAAA,4BAA4B,8EAxMQ,CAAA,CAAA,IAAA,QASA,CAAA,CAAA,IAAA,IAiMtC,SAASE,EACdM,CAAyB,EAEzB,IAAMC,EAAgBT,EAAAA,4BAA4B,CAACnG,QAAQ,GAM3D,OAJI,CAAC4G,GACHH,EAA4BE,GAGtBC,EAAcxI,IAAI,CAJN,CAKlB,IAAK,UAqBL,QApBE,OAAOwI,CAET,KAAK,YACL,IAAK,gBACL,IAAK,mBAEH,MAAM,OAAA,cAEL,CAFK,AAAI7H,MACR,CAAC,EAAE,EAAE4H,EAAkB,iEAAiE,CAAC,EADrF,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,KAAK,QACH,MAAM,OAAA,cAEL,CAFK,AAAI5H,MACR,CAAC,EAAE,EAAE4H,EAAkB,2JAA2J,CAAC,EAD/K,oBAAA,OAAA,kBAAA,gBAAA,CAEN,EAEF,KAAK,iBACH,MAAM,OAAA,cAEL,CAFK,AAAI5H,MACR,CAAC,EAAE,EAAE4H,EAAkB,sKAAsK,CAAC,EAD1L,oBAAA,OAAA,kBAAA,gBAAA,CAEN,EAKJ,CACF,CAEO,SAASF,EAA4BE,CAAyB,EACnE,MAAM,OAAA,cAEL,CAFK,AAAI5H,MACR,CAAC,EAAE,EAAE4H,EAAkB,iHAAiH,CAAC,EADrI,oBAAA,OAAA,mBAAA,eAAA,EAEN,EACF,CAEO,SAASJ,EACdK,CAA4B,QAE5B,AACyB,cAAvBA,EAAcxI,IAAI,EACK,iBACvB,CADAwI,EAAcxI,IAAI,CAEXwI,EAAcE,wBAAwB,CAGxC,IACT,CAEO,SAASN,EACdI,CAA4B,QAE5B,AACyB,qBAAvBA,EAAcxI,IAAI,EACK,UAAvBwI,EAAcxI,IAAI,EACK,kBACvB,CADAwI,EAAcxI,IAAI,CAElB,AAA2B,WAAW,CAAlCwI,EAAcxI,IAAI,CACbwI,EAAcG,qBAAqB,CAKrCH,EAAcE,wBAAwB,CAGxC,IACT,CAEO,SAASR,EACdU,CAAoB,CACpBJ,CAA4B,MAStBA,EAPN,GAAKI,CAAD,CAAWC,GAAG,CAIlB,CAJoB,KAIU,UAAvBL,EAAcxI,IAAI,EAAuC,cAAvBwI,EAAcxI,IAAI,CACvDwI,EAAcM,cAAc,CACL,YAAvBN,EAAcxI,IAAI,CAAK,AACiC,OAAtDwI,EAAAA,EAAcO,OAAO,CAACtC,GAAG,CAAC5D,EAAAA,6BAA4B,CAAA,CAAA,KAAA,EAAtD2F,EAAyDQ,KAAK,MAC9DnH,CACR,CAKO,SAASmG,EACdY,CAAoB,CACpBJ,CAA4B,EAE5B,GAAII,EAAUK,WAAW,CACvB,CADyB,MACjBT,EAAcxI,IAAI,EACxB,IAAK,QACL,IAAK,iBACL,IAAK,UACH,OAAOwI,EAAcU,SAAS,AAGlC,CAIJ,4HCjUaC,sBAAsB,CAAA,kBAAtBA,GAEAC,oBAAoB,CAAA,kBAApBA,GADAC,sBAAsB,CAAA,kBAAtBA,uEADN,IAAMF,EAAyB,6BACzBE,EAAyB,6BACzBD,EAAuB,6FCe5BrI,IAAAA,EAA6B,EAArBC,AAAqB,CAAA,CAAA,CAAlB,CAACsI,YAAY,KAAK,6CA2BrBC,cAAc,CAAA,kBAAdA,GAbHC,iBAAiB,CAAA,kBAAjBA,GAtBAC,kBAAkB,CAAA,kBAAlBA,GAgDGC,6BAA6B,CAAA,kBAA7BA,uEAhDT,IAAMD,EAAqB,AAAWE,IAO3CxD,QAAQyD,OAAO,GAAGC,IAAI,CAAC,OAInB9I,OAAAA,CAAQ+I,QAAQ,CAACH,EAErB,EACF,EAQaH,EAAoB,AAAWG,IAIxCI,aAAaJ,EAEjB,EAOO,SAASJ,IACd,OAAO,IAAIpD,QAAc,AAACyD,GAAYJ,EAAkBI,GAC1D,CAWO,SAASF,IAIZ,OAAO,IAAIvD,QAAS6D,AAAD,GAAOD,aAAaC,GAE3C,uDC2GMxB,EACAA,CAAAA,CAAAA,GAAcxI,IAAI,CAvJvB,CAuJKwI,GAAuB,mEA6LbyB,QAAQ,CAAA,kBAARA,GA3CAC,2CAA2C,CAAA,kBAA3CA,GAlCAC,kCAAkC,CAAA,kBAAlCA,GAuKAC,mBAAmB,CAAA,kBAAnBA,GA4GAC,qBAAqB,CAAA,kBAArBA,GAtGAC,oBAAoB,CAAA,kBAApBA,GAhXAC,0BAA0B,CAAA,kBAA1BA,GAWAC,4BAA4B,CAAA,kBAA5BA,GAmbAC,6BAA6B,CAAA,kBAA7BA,GAjBAC,0BAA0B,CAAA,kBAA1BA,GAlDAC,wBAAwB,CAAA,kBAAxBA,GAtWAC,qBAAqB,CAAA,kBAArBA,GAgSAC,iBAAiB,CAAA,kBAAjBA,GAwCAC,2BAA2B,CAAA,kBAA3BA,GA3TAC,yBAAyB,CAAA,kBAAzBA,GAuPAC,oBAAoB,CAAA,kBAApBA,GAgSAC,wBAAwB,CAAA,kBAAxBA,GAvcAC,gCAAgC,CAAA,kBAAhCA,GA6ZAC,yBAAyB,CAAA,kBAAzBA,GApYAC,+BAA+B,CAAA,kBAA/BA,GAzCAC,0BAA0B,CAAA,kBAA1BA,GAiHAC,qCAAqC,CAAA,kBAArCA,GAmDHC,sCAAsC,CAAA,kBAAtCA,GA+NGC,qBAAqB,CAAA,kBAArBA,kFA9hBE,CAAA,CAAA,IAAA,qCAEiB,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACE,CAAA,CAAA,IAAA,QAK5B,CAAA,CAAA,IAAA,QAC4B,CAAA,CAAA,IAAA,IAE7BC,EAAiD,YAAnC,OAAO1H,EAAAA,OAAK,CAAC2H,iBAAiB,CA2C3C,SAASnB,EACdoB,CAA2C,EAE3C,MAAO,wBACLA,EACAC,gBAAiB,EAAE,CACnBC,2BAAuBhK,EACvBiK,0BAA2B,IAC7B,CACF,CAEO,SAAStB,IACd,MAAO,CACLuB,qBAAqB,EACrBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASvB,EACdwB,CAAmC,MAE5BA,EAAP,OAAA,AAAuC,OAAhCA,EAAAA,EAAcR,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCQ,EAAkCtG,UAAU,AACrD,CASO,SAASiF,EACdsB,CAAgB,CAChB7D,CAAuE,CACvE1C,CAAkB,EAElB,KAAI0C,GAEuB,UAAvBA,EAAcxI,IAAI,EACK,kBACvB,CADAwI,EAAcxI,IAHdwI,AAGkB,GAHH,CAef6D,EAAMC,YAAY,GAAID,EAAME,WAAW,EAAE,AAE7C,GAAIF,EAAMG,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAI7E,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAE0E,EAAMI,KAAK,CAAC,8EAA8E,EAAE3G,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAI0C,GACF,GAA2B,SADV,QAC2B,CAAxCA,EAAcxI,IAAI,CACpBgL,EACEqB,EAAMI,KAAK,CACX3G,EACA0C,EAAckE,eAAe,OAE1B,GAA2B,qBAAvBlE,EAAcxI,IAAI,CAAyB,CACpDwI,EAAcmE,UAAU,CAAG,EAG3B,IAAM7L,EAAM,OAAA,cAEX,CAFW,IAAIyG,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE8E,EAAMI,KAAK,CAAC,iDAAiD,EAAE3G,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,eAAA,EAEZ,EAIA,OAHAuG,EAAMO,uBAAuB,CAAG9G,EAChCuG,EAAMQ,iBAAiB,CAAG/L,EAAIgM,KAAK,CAE7BhM,CACR,CAMA,EAEJ,CAUO,GAlBI,MAkBKuK,EACdgB,CAAgB,CAChBvG,CAAkB,EAElB,IAAMiH,EAAiBzE,EAAAA,kBAlBnB,EAkBuC,CAAC1G,QAAQ,GAC/CmL,GAA0C,iBAAiB,CAAzCA,EAAe/M,IAAI,EAE1CgL,EAAqBqB,EAAMI,KAAK,CAAE3G,EAAYiH,EAAeL,eAAe,CAC9E,CAQO,SAASxB,EACdpF,CAAkB,CAClBuG,CAAgB,CAChBU,CAAoC,EAGpC,IAAMjM,EAAM,OAAA,cAEX,CAFW,IAAIyG,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAE8E,EAAMI,KAAK,CAAC,mDAAmD,EAAE3G,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAOA,OALAiH,EAAeJ,UAAU,CAAG,EAE5BN,EAAMO,uBAAuB,CAAG9G,EAChCuG,EAAMQ,iBAAiB,CAAG/L,EAAIgM,KAAK,CAE7BhM,CACR,CASO,SAASsK,EACd4B,CAAiB,CACjBxE,CAAmC,EAE/BA,GAEuB,UAAvBA,EAFe,AAEDxI,IAAI,EACK,kBACvB,CADAwI,EAAcxI,IAAI,GAQK,cAAvBwI,EAAcxI,IAAI,EACK,qBAAvBwI,EAAcxI,IAAI,AAAK,GACvB,AACAwI,GAAcmE,UAAU,EAAG,CASjC,CAKA,SAASM,EACPR,CAAa,CACb3G,CAAkB,CAClBiH,CAAoC,EAIpC,IAAM5N,EAAQ+N,EAFC,CAAC,MAAM,EAAET,EAAM,mBAEgB5L,8CAFiD,EAAEiF,EAAW,CAAC,CAAC,EAI9GiH,EAAeI,UAAU,CAACC,KAAK,CAACjO,GAEhC,IAAMuN,EAAkBK,EAAeL,eAAe,CAClDA,GACFA,EAAgBd,YADG,GACY,CAAC5G,IAAI,CAAC,CAGnC8H,MAAOJ,EAAgBf,sBAAsB,CACzC,AAAIhL,QAAQmM,KAAK,MACjBjL,aACJiE,CACF,EAEJ,CAEO,SAASqE,EACdsC,CAAa,CACb3G,CAAkB,CAClBuH,CAAqB,CACrBN,CAAoC,EAEpC,IAAML,EAAkBK,EAAeL,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBZ,KADD,oBAC0B,GAC3CY,EAAgBb,qBAAqB,CAAG/F,EACxC4G,EAAgBZ,yBAAyB,CAAGuB,GAGhDJ,EAAoCR,EAAO3G,EAAYiH,EACzD,CAEO,SAASzB,EACdgC,CAA0B,EAI1BA,EAAaC,cAAc,CAAG,EAChC,CAYO,SAASrD,EACduC,CAAa,CACb3G,CAAkB,CAClBuH,CAAqB,CACrBN,CAAoC,EAGpC,IAAgC,IADRA,AACpBS,EADmCL,UAAU,CAAClH,MAAM,CACpCC,OAAO,CAAY,CAMrC,IAAMwG,EAAkBK,EAAeL,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBZ,KADD,oBAC0B,GAC3CY,EAAgBb,qBAAqB,CAAG/F,EACxC4G,EAAgBZ,yBAAyB,CAAGuB,GACV,IAA9BN,EAAeU,AAAqB,UAAX,GAG3Bf,EAAgBgB,iBAAiB,EAAG,CAAA,GAI1CT,EAAoCR,EAAO3G,EAAYiH,EACzD,CACA,MAAMG,EACJ,CAAC,MAAM,EAAET,EAAM,iEAAiE,EAAE3G,EAAW,CAAC,CAAC,CAEnG,CAGO,IAAMyF,EACXD,EASK,SAASrB,EAAS,QAAEpJ,CAAM,OAAE4L,CAAK,CAAiB,EACvD,IAAMM,EAAiBzE,EAAAA,oBAAoB,CAAC1G,QAAQ,GAKpDoJ,EAAqByB,EAAO5L,EAH1BkM,GAA0C,GAGRL,eAHhBK,EAAe/M,IAAI,CACjC+M,EAAeL,eAAe,CAC9B,KAER,CAEO,SAAS1B,EACdyB,CAAa,CACb3G,CAAkB,CAClB4G,CAA4C,EAE5CiB,IACIjB,GACFA,EAAgBd,YADG,GACY,CAAC5G,IAAI,CAAC,CAGnC8H,MAAOJ,EAAgBf,sBAAsB,CACzC,AAAIhL,QAAQmM,KAAK,CACjBjL,kBACJiE,CACF,GAGF/B,EAAAA,OAAK,CAAC2H,iBAAiB,CAACkC,EAAqBnB,EAAO3G,GACtD,CAEA,SAAS8H,EAAqBnB,CAAa,CAAE3G,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAE2G,EAAM,iEAAiE,EAAE3G,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKW+E,EAAkB/J,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACgC,UAAhC,AACA,OADQA,EAAY+M,OAAO,EAEpBC,EAAyBhN,EAAY+M,AAXoC,CAAC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASC,EAAwBjN,CAAc,EAC7C,OACEA,EAAO1C,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnF0C,EAAO1C,QAAQ,CACb,gEAGN,CAEA,IAAoE,IAAhE2P,EAAwBF,CAA+C,CAA1B,MAAO,QACtD,MAAM,OAAA,cAEL,CAFK,AAAIjN,MACR,0FADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAMoN,EAA6B,6BAEnC,SAASb,EAAgCW,CAAe,EACtD,IAAM1O,EAAQ,OAAA,cAAkB,CAAlB,AAAIwB,MAAMkN,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADE1O,EAAcC,MAAM,CAAG2O,EAClB5O,CACT,CAMO,SAAS2L,EACd3L,CAAc,EAEd,MACmB,UAAjB,OAAOA,GACG,OAAVA,GACCA,EAAcC,MAAM,GAAK2O,GAC1B,SAAU5O,GACV,YAAaA,GACbA,aAAiBwB,KAErB,CAEO,SAASyJ,EACdwB,CAAqC,EAErC,OAAOA,EAAgB9E,MAAM,CAAG,CAClC,CAEO,SAASwD,EACd0D,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAcpC,eAAe,CAAC5G,IAAI,IAAIiJ,EAAcrC,eAAe,EAC5DoC,EAAcpC,eAAe,AACtC,CAEO,SAASjB,EACdiB,CAAqC,EAErC,OAAOA,EACJsC,MAAM,CACL,AAACC,GACC,AAAwB,iBAAjBA,EAAOrB,KAAK,EAAiBqB,EAAOrB,KAAK,CAAChG,MAAM,CAAG,GAE7DsH,GAAG,CAAC,CAAC,YAAEtI,CAAU,OAAEgH,CAAK,CAAE,IACzBA,EAAQA,EACLvN,KAAK,CAAC,MAINW,AAHD,KAGM,CAAC,GACNgO,MAAM,CAAC,AAACG,KAEHA,EAAKlQ,QAAQ,CAAC,uBAAuB,AAKrCkQ,EAAKlQ,QAAQ,CAAC,MAXoD,aAWjC,AAKjCkQ,EAAKlQ,QAAQ,CAAC,YAAY,CAM/BgC,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAE2F,EAAW;AAAG,EAAEgH,EAAAA,CAAO,EAEjE,CAEA,SAASa,IACP,GAAI,CAAClC,EACH,MAAM,KADU,EACV,cAEL,CAFK,AAAI9K,MACR,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEJ,CAMO,SAAS+J,EAA2B7J,CAAc,EACvD8M,IACA,IAAMR,EAAa,IAAImB,gBAEvB,GAAI,CACFvK,EAAAA,OAAK,CAAC2H,iBAAiB,CAAC7K,EAC1B,CAAE,MAAO0N,EAAY,CACnBpB,EAAWC,KAAK,CAACmB,EACnB,CACA,OAAOpB,EAAWlH,MAAM,AAC1B,CAOO,SAASwE,EACdjC,CAAmC,EAEnC,IAAM2E,EAAa,IAAImB,gBAkBvB,OAhBI9F,EAAcgG,WAAW,CAI3BhG,CAJ6B,CAIfgG,WAAW,CAACC,UAAU,GAAG5E,IAAI,CAAC,KAC1CsD,EAAWC,KAAK,EAClB,GAOA3D,CAAAA,EAAAA,EAAAA,kBAAkB,AAAlBA,EAAmB,IAAM0D,EAAWC,KAAK,IAGpCD,EAAWlH,MAAM,AAC1B,CAEO,SAASoE,EACdvE,CAAkB,CAClBiH,CAAoC,EAEpC,IAAML,EAAkBK,EAAeL,eAAe,CAClDA,GACFA,EAAgBd,YADG,GACY,CAAC5G,IAAI,CAAC,CACnC8H,MAAOJ,EAAgBf,sBAAsB,CACzC,AAAIhL,QAAQmM,KAAK,MACjBjL,aACJiE,CACF,EAEJ,CAEO,SAAS0F,EAAsB1F,CAAkB,EACtD,IAAM8C,EAAYnG,EAAAA,gBAAgB,CAACb,QAAQ,GAE3C,GACEgH,GACAA,EAAU8F,kBAAkB,EAC5B9F,EAAU+F,mBAAmB,EAC7B/F,EAAU+F,mBAAmB,CAACC,IAAI,CAAG,EACrC,CAGA,IAAMpG,EAAgBF,EAAAA,oBAAoB,CAAC1G,QAAQ,GAC/C4G,IAEyB,WAFV,EAEuB,CAApCA,EAAcxI,IAAI,CAIpB+D,EAAAA,OAAK,CAAC8K,GAAG,CAAClJ,GAAAA,EAAAA,kBAAAA,AAAkB,EAAC6C,EAAcsG,YAAY,CAAEhJ,IACzB,iBAAiB,CAAxC0C,EAAcxI,IAAI,CAE3BgL,EACEpC,EAAU6D,KAAK,CACf3G,EACA0C,EAAckE,eAAe,EAEC,oBAAoB,CAA3ClE,EAAcxI,IAAI,EAC3BkL,EAAiCpF,EAAY8C,EAAWJ,GAG9D,CACF,CAEA,IAAMuG,EAAmB,mCACnBC,EAAmB,AAAIC,OAC3B,CAAC,UAAU,EAAE9F,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzC+F,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAE5F,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzC8F,EAAiB,AAAIF,OAAO,CAAC,UAAU,EAAE7F,EAAAA,oBAAoB,CAAC,QAAQ,CAAC,EAEtE,SAAS+B,EACdsB,CAAa,CACb2C,CAAsB,CACtBC,CAAyC,CACzCrB,CAAmC,CACnCC,CAAmC,EAEnC,IAAIkB,EAAeG,IAAI,CAACF,IAGjB,GAAIJ,EAAiBM,IAAI,CAACF,GAHQ,AAGS,CAChDC,EAAkBrD,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAIkD,EAAiBI,IAAI,CAACF,GAAiB,CAChDC,EAAkBpD,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAI8C,EAAiBO,IAAI,CAACF,GAAiB,CAChDC,EAAkBtD,mBAAmB,EAAG,EACxC,MACF,MAAO,GACLiC,EAAclC,yBAAyB,EACvCmC,EAAcnC,yBAAyB,CACvC,CACAuD,EAAkBnD,oBAAoB,EAAG,EACzC,MACF,KAAO,CAEL,IAAM/M,EAAQoQ,AAMlB,SAASA,AACP1B,CAAe,CACfuB,CAAsB,EAEtB,IAAMjQ,EAAQ,OAAA,GAVgC0O,WAUd,CAAlB,AAAIlN,MAAMkN,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADA1O,EAAM2N,KAAK,CAAG,UAAYe,EAAUuB,EAC7BjQ,CACT,EAdoB,CAAC,OAAO,EAAEsN,EAAM,+UAA+U,CAAC,CAC3T2C,GACrDC,EAAkBlD,aAAa,CAACnH,IAAI,CAAC7F,GACrC,MACF,EACF,CAWO,SAAS8L,EACdwB,CAAa,CACb4C,CAAyC,CACzCrB,CAAmC,CACnCC,CAAmC,MAE/BuB,EACAC,EACAC,EAeJ,GAdI1B,EAAclC,yBAAyB,EACzC0D,AAD2C,EAC/BxB,EAAclC,yBAAyB,CACnD2D,EAAiBzB,EAAcnC,qBAAqB,CACpD6D,GAAiD,IAApC1B,EAAcN,iBAAiB,EACnCO,EAAcnC,yBAAyB,EAAE,AAClD0D,EAAYvB,EAAcnC,yBAAyB,CACnD2D,EAAiBxB,EAAcpC,qBAAqB,CACpD6D,EAAazB,CAAoC,MAAtBP,iBAAiB,GAE5C8B,EAAY,KACZC,OAAiB5N,EACjB6N,GAAa,GAGXL,EAAkBnD,oBAAoB,EAAIsD,EAO5C,MANI,AAACE,GADkD,AAIrDC,QAAQxQ,CAHO,IAGF,CAACqQ,GAGV,IAAI7H,EAAAA,qBAAqB,CAGjC,IAAMwE,EAAgBkD,EAAkBlD,aAAa,CACrD,GAAIA,EAAcrF,MAAM,CAAE,CACxB,IAAK,IAAID,EAAI,EAAGA,EAAIsF,EAAcrF,MAAM,CAAED,IAAK,AAC7C8I,QAAQxQ,KAAK,CAACgN,CAAa,CAACtF,EAAE,CAGhC,OAAM,IAAIc,EAAAA,qBAAqB,AACjC,CAEA,GAAI,CAAC0H,EAAkBtD,mBAAmB,EAAE,AAC1C,GAAIsD,EAAkBrD,kBAAkB,CAAE,CACxC,GAAIwD,EAEF,MADAG,GADa,KACLxQ,KAAK,CAACqQ,GACR,OAAA,cAEL,CAFK,IAAI7H,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE8E,EAAM,oEAAoE,EAAEgD,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAI9H,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE8E,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAO,GAAI4C,EAAkBpD,kBAAkB,CAAE,CAC/C,GAAIuD,EAEF,MADAG,GADa,KACLxQ,KAAK,CAACqQ,GACR,OAAA,cAEL,CAFK,IAAI7H,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE8E,EAAM,oEAAoE,EAAEgD,EAAe,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAEF,OAAM,OAAA,cAEL,CAFK,IAAI9H,EAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAE8E,EAAM,8cAA8c,CAAC,EAD3d,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GACF,CAEJ,4IC3sBgBjH,mBAAAA,qCAAAA,AAAT,SAASA,EAAiBrG,CAAc,EAC7C,GACEoB,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACpB,IAClBsB,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAACtB,IACpBqI,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAACrI,IACrB0L,CAAAA,EAAAA,EAAAA,iBAAiB,AAAjBA,EAAkB1L,IAClB+H,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC/H,IACXuG,CAAAA,EAAAA,EAAAA,8BAAAA,AAA8B,EAACvG,GAE/B,KADA,CACMA,EAGJA,aAAiBwB,OAAS,UAAWxB,GACvCqG,EAAiBrG,EAD6B,AACvBsG,KAAK,CAEhC,aAtB+C,CAAA,CAAA,IAAA,QACpB,CAAA,CAAA,IAAA,OACS,CAAA,CAAA,IAAA,QACF,CAAA,CAAA,IAAA,MACA,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,gRCCpC,sFACYD,mBAAAA,qCAAAA,KAAN,IAAMA,EACO,aAAlB,OAAOb,OAEDC,EAAQ,CAAA,CAAA,IAAA,GACRY,gBAAgB,CAEhBZ,EAAQ,CAAA,CAAA,IAAA,GACRY,gBAAgB,6QCdV,qEAkCLoK,uBAAuB,CAAA,kBAAvBA,GALA/P,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAEZyF,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTH,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAFEV,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EAA3BC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIRa,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EACZC,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,8EALmB,CAAA,CAAA,IAAA,QACf,CAAA,CAAA,IAAA,QACJ,CAAA,CAAA,IAAA,QACC,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,QACI,CAAA,CAAA,IAAA,GAhCjC,OAAMqK,UAAqClP,MACzCC,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAMgP,UAAgCE,gBAEpCC,QAAS,CACP,MAAM,IAAIF,CACZ,CAEAG,QAAS,CACP,MAAM,IAAIH,CACZ,CAEAlJ,KAAM,CACJ,MAAM,IAAIkJ,CACZ,CAEAI,MAAO,CACL,MAAM,IAAIJ,CACZ,CACF,kVCdaK,yBAAyB,CAAA,kBAAzBA,GAGGC,qBAAqB,CAAA,kBAArBA,6FAbkB,CAAA,CAAA,IAAA,KAUrBD,EACXnM,EAAAA,OAAK,CAAC1C,aAAa,CAAgC,EADxC6O,IAGN,OAHMA,EAGGC,EAAsBC,CAA+B,EACnE,IAAMC,EAAgCC,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACJ,GAE7CG,GACFA,EAA8BD,EAElC,wBAHqC,qHCfrBG,2BAAAA,qCAAAA,aAHkB,CAAA,CAAA,IAAA,QACD,CAAA,CAAA,IAAA,IAE1B,SAASA,EAAyB1P,CAAc,EACrD,IAAM+H,EAAYnG,EAAAA,gBAAgB,CAACb,QAAQ,GAE3C,IAAIgH,MAAAA,CAAAA,GAAAA,EAAW2D,AAAX3D,WAAW2D,AAAW,EAAE,EAExB3D,MAAAA,EAAAA,KAAAA,EAAAA,EAAW8F,kBAAAA,AAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,IAAIlO,EAAAA,iBAAiB,CAACK,GAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAA4B,EACvE,kVCiRE+O,uBAAuB,CAAA,kBAAvBA,EAAAA,uBAAuB,EADvB/P,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EApLZqQ,yBAAyB,CAAA,kBAAzBA,EAAAA,yBAAyB,EAgLzB5K,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTH,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIRV,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EADjBC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EADRa,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAKZC,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,EApIFgL,SAAS,CAAA,kBAATA,GA5DAC,WAAW,CAAA,kBAAXA,GAiCAC,SAAS,CAAA,kBAATA,GA9EAC,eAAe,CAAA,kBAAfA,GA6MAC,wBAAwB,CAAA,kBAAxBA,GA/BAC,yBAAyB,CAAA,kBAAzBA,GAtHdV,qBAAqB,CAAA,kBAArBA,EAAAA,qBAAqB,8EAnGa,CAAA,CAAA,IAAA,QAK7B,CAAA,CAAA,IAAA,OAKA,CAAA,CAAA,IAAA,QACyB,CAAA,CAAA,IAAA,QACsB,CAAA,CAAA,IAAA,QACd,CAAA,CAAA,IAAA,QAuFjC,CAAA,CAAA,IAAA,IArFD3E,EACc,aAAlB,OAAO7G,OAEDC,EAAQ,CAAA,CAAA,IAAA,IACR4G,qBAAqB,MACvB3J,EAuBC,SAAS8O,IACd,IAAM1S,EAAeqS,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAClP,EAAAA,mBAAmB,EAK7C0P,EAAuBC,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,IACnC,AAAK9S,EAME,EANH,EAMO2R,EAAAA,MANQ,iBAMe,CAAC3R,GAH1B,KAIR,CAACA,EAAa,EAEjB,GAAsB,aAAlB,OAAO0G,OAAwB,CAEjC,GAAM,0BAAE4L,CAAwB,CAAE,CAChC3L,EAAQ,CAAA,CAAA,IAAA,IAEV2L,EAAyB,oBAC3B,CAEA,OAAOO,CACT,CAoBO,SAASL,IAKd,OAJAjF,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,gBAIO8E,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACnP,EAAAA,eAAe,CACnC,CA2BO,SAASuP,IACd,IAAMM,EAASV,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC5M,EAAAA,gBAAgB,EAC1C,GAAe,MAAM,CAAjBsN,EACF,MAAM,OAAA,cAAwD,CAAxD,AAAIrQ,MAAM,+CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAuD,GAG/D,OAAOqQ,CACT,CAoBO,SAASR,IAGd,OAFAhF,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,cAEO8E,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACpP,EAAAA,iBAAiB,CACrC,CAiEO,SAAS2P,EACdM,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3B3F,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,8BAEA,IAAMkG,EAAUpB,GAAAA,EAAAA,UAAAA,AAAU,EAAC1M,EAAAA,mBAAmB,SAE9C,AAAK8N,EAEET,AAtET,EAoEM,KAAU,EApEPA,EACPC,CAAuB,CACvBC,CAAwB,CACxBC,CAAY,CACZC,CAA0B,MAEtBC,EACJ,GAJAF,KAAAA,IAAAA,IAAAA,GAAQ,CAAA,EACRC,KAAAA,QAAAA,EAAwB,EAAA,AAAE,EAGtBD,EAEFE,EAAOJ,CAAI,CAAC,CAFH,CAEK,CAACC,EAAiB,KAC3B,KAGEI,EADP,IAAMA,EAAiBL,CAAI,CAAC,EAAE,CAC9BI,EAAOC,AAAuB,OAAvBA,EAAAA,EAAeC,QAAAA,AAAQ,EAAvBD,EAA2BtS,OAAOC,MAAM,CAACqS,EAAe,CAAC,EAAE,AACpE,CAEA,GAAI,CAACD,EAAM,OAAOD,EAClB,IAAMvT,EAAUwT,CAAI,CAAC,EAAE,CAEnBG,EAAezN,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAClG,SAEnC,AAAI,CAAC2T,GAAgBA,EAAazT,UAAU,CAACN,EAAAA,gBAAgB,EACpD2T,CADuD,EAIhEA,EAAYrM,IAAI,CAACyM,GAEVR,EACLK,EACAH,GACA,EACAE,GAEJ,EAqCsCK,EAAQC,UAAU,CAAER,GAFnC,IAGvB,CAqBO,SAASP,EACdO,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3B3F,OAAAA,EAAAA,EAAwB,CAAxBA,6BAEA,IAAMoG,EAAyBf,EAA0BM,GAEzD,GAAI,CAACS,GAA4D,GAAG,CAArCA,EAAuB9K,MAAM,CAC1D,OAAO,KAGT,IAAM+K,EACiB,aAArBV,EACIS,CAAsB,CAAC,EAAE,CACzBA,CAAsB,CAACA,EAAuB9K,MAAM,CAAG,EAAE,CAI/D,OAAO+K,IAA0BpU,EAAAA,mBAAmB,CAChD,KACAoU,CACN", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34]}