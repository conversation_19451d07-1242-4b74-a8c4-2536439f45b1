(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{931209:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{e.i(854301);"use strict";Object.defineProperty(o,"__esModule",{value:!0});var u={DecodeError:function(){return v},MiddlewareNotFoundError:function(){return w},MissingStaticPage:function(){return _},NormalizeError:function(){return g},PageNotFoundError:function(){return b},SP:function(){return h},ST:function(){return m},WEB_VITALS:function(){return t},execOnce:function(){return a},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return c},isAbsoluteUrl:function(){return n},isResSent:function(){return f},loadGetInitialProps:function(){return p},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return y}};for(var i in u)Object.defineProperty(o,i,{enumerable:!0,get:u[i]});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function a(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,n=e=>r.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function c(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function f(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function p(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await p(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&f(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,m=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class v extends Error{}class g extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class _ extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class w extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}}},365659:function(e){var{g:t,__dirname:r,m:n,e:o}=e,u={229:function(e){var t,r,n,o=e.exports={};function u(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}try{t="function"==typeof setTimeout?setTimeout:u}catch(e){t=u}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===u||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}var s=[],c=!1,l=-1;function f(){c&&n&&(c=!1,n.length?s=n.concat(s):l=-1,s.length&&d())}function d(){if(!c){var e=a(f);c=!0;for(var t=s.length;t;){for(n=s,s=[];++l<t;)n&&n[l].run();l=-1,t=s.length}n=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function y(){}o.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];s.push(new p(e,t)),1!==s.length||c||a(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=y,o.addListener=y,o.once=y,o.off=y,o.removeListener=y,o.removeAllListeners=y,o.emit=y,o.prependListener=y,o.prependOnceListener=y,o.listeners=function(e){return[]},o.binding=function(e){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw Error("process.chdir is not supported")},o.umask=function(){return 0}}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}},n=!0;try{u[e](r,r.exports,a),n=!1}finally{n&&delete i[e]}return r.exports}a.ab=r+"/",n.exports=a(229)},854301:function(e){var t,r,{g:n,__dirname:o,m:u,e:i}=e;"use strict";u.exports=(null==(t=n.process)?void 0:t.env)&&"object"==typeof(null==(r=n.process)?void 0:r.env)?n.process:e.r(365659)},691380:function(e){"use strict";var{g:t,__dirname:r,m:n,e:o}=e,u=e.i(854301),i=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),v=Symbol.iterator,g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b=Object.assign,_={};function w(e,t,r){this.props=e,this.context=t,this.refs=_,this.updater=r||g}function E(){}function j(e,t,r){this.props=e,this.context=t,this.refs=_,this.updater=r||g}w.prototype.isReactComponent={},w.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},w.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},E.prototype=w.prototype;var x=j.prototype=new E;x.constructor=j,b(x,w.prototype),x.isPureReactComponent=!0;var S=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},O=Object.prototype.hasOwnProperty;function T(e,t,r,n,o,u){return{$$typeof:i,type:e,key:t,ref:void 0!==(r=u.ref)?r:null,props:u}}function k(e){return"object"==typeof e&&null!==e&&e.$$typeof===i}var C=/\/+/g;function R(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function M(){}function A(e,t,r){if(null==e)return e;var n=[],o=0;return!function e(t,r,n,o,u){var s,c,l,f=typeof t;("undefined"===f||"boolean"===f)&&(t=null);var d=!1;if(null===t)d=!0;else switch(f){case"bigint":case"string":case"number":d=!0;break;case"object":switch(t.$$typeof){case i:case a:d=!0;break;case m:return e((d=t._init)(t._payload),r,n,o,u)}}if(d)return u=u(t),d=""===o?"."+R(t,0):o,S(u)?(n="",null!=d&&(n=d.replace(C,"$&/")+"/"),e(u,r,n,"",function(e){return e})):null!=u&&(k(u)&&(s=u,c=n+(null==u.key||t&&t.key===u.key?"":(""+u.key).replace(C,"$&/")+"/")+d,u=T(s.type,c,void 0,void 0,void 0,s.props)),r.push(u)),1;d=0;var p=""===o?".":o+":";if(S(t))for(var y=0;y<t.length;y++)f=p+R(o=t[y],y),d+=e(o,r,n,f,u);else if("function"==typeof(y=null===(l=t)||"object"!=typeof l?null:"function"==typeof(l=v&&l[v]||l["@@iterator"])?l:null))for(t=y.call(t),y=0;!(o=t.next()).done;)f=p+R(o=o.value,y++),d+=e(o,r,n,f,u);else if("object"===f){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(M,M):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,n,o,u);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return d}(e,n,"","",function(e){return t.call(r,e,o++)}),n}function I(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var N="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof u.default&&"function"==typeof u.default.emit)return void u.default.emit("uncaughtException",e);console.error(e)};function H(){}o.Children={map:A,forEach:function(e,t,r){A(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return A(e,function(){t++}),t},toArray:function(e){return A(e,function(e){return e})||[]},only:function(e){if(!k(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},o.Component=w,o.Fragment=s,o.Profiler=l,o.PureComponent=j,o.StrictMode=c,o.Suspense=y,o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,o.__COMPILER_RUNTIME={__proto__:null,c:function(e){return P.H.useMemoCache(e)}},o.cache=function(e){return function(){return e.apply(null,arguments)}},o.cloneElement=function(e,t,r){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var n=b({},e.props),o=e.key,u=void 0;if(null!=t)for(i in void 0!==t.ref&&(u=void 0),void 0!==t.key&&(o=""+t.key),t)O.call(t,i)&&"key"!==i&&"__self"!==i&&"__source"!==i&&("ref"!==i||void 0!==t.ref)&&(n[i]=t[i]);var i=arguments.length-2;if(1===i)n.children=r;else if(1<i){for(var a=Array(i),s=0;s<i;s++)a[s]=arguments[s+2];n.children=a}return T(e.type,o,void 0,void 0,u,n)},o.createContext=function(e){return(e={$$typeof:d,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:f,_context:e},e},o.createElement=function(e,t,r){var n,o={},u=null;if(null!=t)for(n in void 0!==t.key&&(u=""+t.key),t)O.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(o[n]=t[n]);var i=arguments.length-2;if(1===i)o.children=r;else if(1<i){for(var a=Array(i),s=0;s<i;s++)a[s]=arguments[s+2];o.children=a}if(e&&e.defaultProps)for(n in i=e.defaultProps)void 0===o[n]&&(o[n]=i[n]);return T(e,u,void 0,void 0,null,o)},o.createRef=function(){return{current:null}},o.forwardRef=function(e){return{$$typeof:p,render:e}},o.isValidElement=k,o.lazy=function(e){return{$$typeof:m,_payload:{_status:-1,_result:e},_init:I}},o.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},o.startTransition=function(e){var t=P.T,r={};P.T=r;try{var n=e(),o=P.S;null!==o&&o(r,n),"object"==typeof n&&null!==n&&"function"==typeof n.then&&n.then(H,N)}catch(e){N(e)}finally{P.T=t}},o.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},o.use=function(e){return P.H.use(e)},o.useActionState=function(e,t,r){return P.H.useActionState(e,t,r)},o.useCallback=function(e,t){return P.H.useCallback(e,t)},o.useContext=function(e){return P.H.useContext(e)},o.useDebugValue=function(){},o.useDeferredValue=function(e,t){return P.H.useDeferredValue(e,t)},o.useEffect=function(e,t,r){var n=P.H;if("function"==typeof r)throw Error("useEffect CRUD overload is not enabled in this build of React.");return n.useEffect(e,t)},o.useId=function(){return P.H.useId()},o.useImperativeHandle=function(e,t,r){return P.H.useImperativeHandle(e,t,r)},o.useInsertionEffect=function(e,t){return P.H.useInsertionEffect(e,t)},o.useLayoutEffect=function(e,t){return P.H.useLayoutEffect(e,t)},o.useMemo=function(e,t){return P.H.useMemo(e,t)},o.useOptimistic=function(e,t){return P.H.useOptimistic(e,t)},o.useReducer=function(e,t,r){return P.H.useReducer(e,t,r)},o.useRef=function(e){return P.H.useRef(e)},o.useState=function(e){return P.H.useState(e)},o.useSyncExternalStore=function(e,t,r){return P.H.useSyncExternalStore(e,t,r)},o.useTransition=function(){return P.H.useTransition()},o.version="19.1.0"},591468:function(e){var{g:t,__dirname:r,m:n,e:o}=e;e.i(854301);"use strict";n.exports=e.r(691380)},430089:function(e){var{g:t,__dirname:r,m:n,e:o}=e;"use strict";o._=function(e){return e&&e.__esModule?e:{default:e}}},419809:function(e){var{g:t,__dirname:r,m:n,e:o}=e;"use strict";var u=Symbol.for("react.transitional.element");function i(e,t,r){var n=null;if(void 0!==r&&(n=""+r),void 0!==t.key&&(n=""+t.key),"key"in t)for(var o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return{$$typeof:u,type:e,key:n,ref:void 0!==(t=r.ref)?t:null,props:r}}o.Fragment=Symbol.for("react.fragment"),o.jsx=i,o.jsxs=i},9158:function(e){var{g:t,__dirname:r,m:n,e:o}=e;e.i(854301);"use strict";n.exports=e.r(419809)},992744:function(e){var{g:t,__dirname:r,m:n,e:o}=e;"use strict";function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}o._=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}},917584:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";e.i(854301),Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"HeadManagerContext",{enumerable:!0,get:function(){return t}});let t=e.r(430089)._(e.r(591468)).default.createContext({})}},208349:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return u}});let t=e.r(591468),r="undefined"==typeof window,n=r?()=>{}:t.useLayoutEffect,i=r?()=>{}:t.useEffect;function u(e){let{headManager:o,reduceComponentsToState:u}=e;function a(){if(o&&o.mountedInstances){let r=t.Children.toArray(Array.from(o.mountedInstances).filter(Boolean));o.updateHead(u(r,e))}}if(r){var s;null==o||null==(s=o.mountedInstances)||s.add(e.children),a()}return n(()=>{var t;return null==o||null==(t=o.mountedInstances)||t.add(e.children),()=>{var t;null==o||null==(t=o.mountedInstances)||t.delete(e.children)}}),n(()=>(o&&(o._pendingUpdate=a),()=>{o&&(o._pendingUpdate=a)})),i(()=>(o&&o._pendingUpdate&&(o._pendingUpdate(),o._pendingUpdate=null),()=>{o&&o._pendingUpdate&&(o._pendingUpdate(),o._pendingUpdate=null)})),null}}},114134:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";e.i(854301),Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"AmpStateContext",{enumerable:!0,get:function(){return t}});let t=e.r(430089)._(e.r(591468)).default.createContext({})}},225205:function(e){var{g:t,__dirname:r,m:n,e:o}=e;"use strict";function u(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isInAmpMode",{enumerable:!0,get:function(){return u}})},32870:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";e.i(854301),Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"warnOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}}},302211:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";var u=e.i(854301);Object.defineProperty(o,"__esModule",{value:!0});var i={default:function(){return g},defaultHead:function(){return s}};for(var a in i)Object.defineProperty(o,a,{enumerable:!0,get:i[a]});let t=e.r(430089),r=e.r(992744),f=e.r(9158),d=r._(e.r(591468)),p=t._(e.r(208349)),y=e.r(114134),h=e.r(917584),m=e.r(225205);function s(e){void 0===e&&(e=!1);let t=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function c(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===d.default.Fragment?e.concat(d.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(32870);let v=["name","httpEquiv","charSet","itemProp"];function l(e,t){let{inAmpMode:r}=t;return e.reduce(c,[]).reverse().concat(s(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let u=!0,i=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){i=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?u=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?u=!1:t.add(o.type);break;case"meta":for(let e=0,t=v.length;e<t;e++){let t=v[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?u=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!i)&&r.has(e)?u=!1:(r.add(e),n[t]=r)}}}return u}}()).reverse().map((e,t)=>{let n=e.key||t;if(u.default.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,d.default.cloneElement(e,t)}return d.default.cloneElement(e,{key:n})})}let g=function(e){let{children:t}=e,r=(0,d.useContext)(y.AmpStateContext),n=(0,d.useContext)(h.HeadManagerContext);return(0,f.jsx)(p.default,{reduceComponentsToState:l,headManager:n,inAmpMode:(0,m.isInAmpMode)(r),children:t})};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)}},826239:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";Object.defineProperty(o,"__esModule",{value:!0});var u={NEXT_REQUEST_META:function(){return e},addRequestMeta:function(){return c},getRequestMeta:function(){return a},removeRequestMeta:function(){return l},setRequestMeta:function(){return s}};for(var i in u)Object.defineProperty(o,i,{enumerable:!0,get:u[i]});let e=Symbol.for("NextInternalRequestMeta");function a(t,r){let n=t[e]||{};return"string"==typeof r?n[r]:n}function s(t,r){return t[e]=r,r}function c(e,t,r){let n=a(e);return n[t]=r,s(e,n)}function l(e,t){let r=a(e);return delete r[t],s(e,r)}}},351194:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return l}});let t=e.r(430089),r=e.r(9158),i=t._(e.r(591468)),a=t._(e.r(302211)),s={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function u(t){let r,{req:n,res:o,err:u}=t,i=o&&o.statusCode?o.statusCode:u?u.statusCode:404;if("undefined"!=typeof window)r=window.location.hostname;else if(n){let{getRequestMeta:t}=e.r(826239),o=t(n,"initURL");o&&(r=new URL(o).hostname)}return{statusCode:i,hostname:r}}let c={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class l extends i.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,n=this.props.title||s[e]||"An unexpected error has occurred";return(0,r.jsxs)("div",{style:c.error,children:[(0,r.jsx)(a.default,{children:(0,r.jsx)("title",{children:e?e+": "+n:"Application error: a client-side exception has occurred"})}),(0,r.jsxs)("div",{style:c.desc,children:[(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,r.jsx)("h1",{className:"next-error-h1",style:c.h1,children:e}):null,(0,r.jsx)("div",{style:c.wrap,children:(0,r.jsxs)("h2",{style:c.h2,children:[this.props.title||e?n:(0,r.jsxs)(r.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,r.jsxs)(r.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}l.displayName="ErrorPage",l.getInitialProps=u,l.origGetInitialProps=u,("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)}},715025:function(e){var{g:t,__dirname:r,m:n,e:o}=e;n.exports=e.r(351194)},986151:function(e){var{g:t,__dirname:r,m:n,e:o}=e;{let t="/_error";(window.__NEXT_P=window.__NEXT_P||[]).push([t,()=>e.r(715025)]),n.hot&&n.hot.dispose(function(){window.__NEXT_P.push([t])})}},385529:e=>{var{g:t,__dirname:r}=e;e.v(e=>Promise.resolve().then(()=>e(351194)))},515461:e=>{var{g:t,__dirname:r}=e;e.v(t=>Promise.all(["static/chunks/34cb5aeb0502f902.js"].map(t=>e.l(t))).then(()=>t(461034)))}}]);

//# sourceMappingURL=fcb1531d4e380ca0.js.map