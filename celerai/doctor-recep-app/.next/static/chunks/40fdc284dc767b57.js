(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{307477:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],s=(0,r.default)("shield",t)}},63879:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Shield:()=>r.default});var r=e.i(307477)},708632:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],s=(0,r.default)("chart-column",t)}},697688:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({BarChart3:()=>r.default});var r=e.i(708632)},658105:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],s=(0,r.default)("bell",t)}},339951:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Bell:()=>r.default});var r=e.i(658105)},812898:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({adminLogout:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("0052d311627b1e498ec699db5ec34cf21bfb47393c",r.callServer,void 0,r.findSourceMapURL,"adminLogout")},989827:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getContactRequestsCount:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("00d1a59b77a19556ea23f7f6b189fe135b5a9f71e9",r.callServer,void 0,r.findSourceMapURL,"getContactRequestsCount")},557912:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({resolveFetch:()=>t});let t=t=>{let s;return s=t||("undefined"==typeof fetch?(...t)=>e.r(308217)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>s(...e)}}},49882:e=>{"use strict";var t,{g:s,__dirname:r}=e;{e.s({FunctionRegion:()=>t,FunctionsError:()=>s,FunctionsFetchError:()=>r,FunctionsHttpError:()=>a,FunctionsRelayError:()=>i});class s extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class r extends s{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class i extends s{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class a extends s{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(t||(t={}))}},891275:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({FunctionsClient:()=>t});var r=e.i(557912),i=e.i(49882),a=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};class t{constructor(e,{headers:t={},customFetch:s,region:a=i.FunctionRegion.Any}={}){this.url=e,this.headers=t,this.region=a,this.fetch=(0,r.resolveFetch)(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return a(this,void 0,void 0,function*(){try{let r,a,{headers:n,method:o,body:l}=t,c={},{region:h}=t;h||(h=this.region),h&&"any"!==h&&(c["x-region"]=h),l&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&l instanceof Blob||l instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",r=l):"string"==typeof l?(c["Content-Type"]="text/plain",r=l):"undefined"!=typeof FormData&&l instanceof FormData?r=l:(c["Content-Type"]="application/json",r=JSON.stringify(l)));let u=yield this.fetch(`${this.url}/${e}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),n),body:r}).catch(e=>{throw new i.FunctionsFetchError(e)}),d=u.headers.get("x-relay-error");if(d&&"true"===d)throw new i.FunctionsRelayError(u);if(!u.ok)throw new i.FunctionsHttpError(u);let p=(null!=(s=u.headers.get("Content-Type"))?s:"text/plain").split(";")[0].trim();return{data:"application/json"===p?yield u.json():"application/octet-stream"===p?yield u.blob():"text/event-stream"===p?u:"multipart/form-data"===p?yield u.formData():yield u.text(),error:null}}catch(e){return{data:null,error:e}}})}}}},466929:function(e){var{g:t,__dirname:s,m:r,e:i}=e;"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}},332773:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=a(e.r(531237)),s=a(e.r(466929));i.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let a=null,n=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(n="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let s=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),i=null==(r=e.headers.get("content-range"))?void 0:r.split("/");s&&i&&i.length>1&&(o=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(n)&&(n.length>1?(a={code:"PGRST116",details:`Results contain ${n.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},n=null,o=null,l=406,c="Not Acceptable"):n=1===n.length?n[0]:null)}else{let t=await e.text();try{a=JSON.parse(t),Array.isArray(a)&&404===e.status&&(n=[],a=null,l=200,c="OK")}catch(s){404===e.status&&""===t?(l=204,c="No Content"):a={message:t}}if(a&&this.isMaybeSingle&&(null==(i=null==a?void 0:a.details)?void 0:i.includes("0 rows"))&&(a=null,l=200,c="OK"),a&&this.shouldThrowOnError)throw new s.default(a)}return{error:a,data:n,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,s,r;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(s=null==e?void 0:e.stack)?s:""}`,hint:"",code:`${null!=(r=null==e?void 0:e.code)?r:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}}},266319:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=a(e.r(332773));class s extends t.default{select(e){let t=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:i=r}={}){let a=i?`${i}.order`:"order",n=this.url.searchParams.get(a);return this.url.searchParams.set(a,`${n?`${n},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){let r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){let i=void 0===r?"offset":`${r}.offset`,a=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(a,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:i=!1,format:a="text"}={}){var n;let o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(n=this.headers.Accept)?n:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${a}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}i.default=s}},454825:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=a(e.r(266319));class s extends t.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let s=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let i="";"plain"===r?i="pl":"phrase"===r?i="ph":"websearch"===r&&(i="w");let a=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${i}fts${a}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){let r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}i.default=s}},809298:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=a(e.r(454825));i.default=class{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:s=!1,count:r}={}){let i=!1,a=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",a),r&&(this.headers.Prefer=`count=${r}`),new t.default({method:s?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:s,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),s&&i.push(`count=${s}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:s,ignoreDuplicates:r=!1,count:i,defaultToNull:a=!0}={}){let n=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==s&&this.url.searchParams.set("on_conflict",s),this.headers.Prefer&&n.push(this.headers.Prefer),i&&n.push(`count=${i}`),a||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:s}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),s&&r.push(`count=${s}`),this.headers.Prefer=r.join(","),new t.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let s=[];return e&&s.push(`count=${e}`),this.headers.Prefer&&s.unshift(this.headers.Prefer),this.headers.Prefer=s.join(","),new t.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}}},452666:function(e){var{g:t,__dirname:s,m:r,e:i}=e;"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.version=void 0,i.version="0.0.0-automated"},133287:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.DEFAULT_HEADERS=void 0;let t=e.r(452666);i.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${t.version}`}}},258323:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0});let t=a(e.r(809298)),s=a(e.r(454825)),r=e.r(133287);class n{constructor(e,{headers:t={},schema:s,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},r.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=i}from(e){let s=new URL(`${this.url}/${e}`);return new t.default(s,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new n(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:a}={}){let n,o,l=new URL(`${this.url}/rpc/${e}`);r||i?(n=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(n="POST",o=t);let c=Object.assign({},this.headers);return a&&(c.Prefer=`count=${a}`),new s.default({method:n,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}i.default=n}},198567:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";var a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(i,"__esModule",{value:!0}),i.PostgrestError=i.PostgrestBuilder=i.PostgrestTransformBuilder=i.PostgrestFilterBuilder=i.PostgrestQueryBuilder=i.PostgrestClient=void 0;let t=a(e.r(258323));i.PostgrestClient=t.default;let s=a(e.r(809298));i.PostgrestQueryBuilder=s.default;let r=a(e.r(454825));i.PostgrestFilterBuilder=r.default;let n=a(e.r(266319));i.PostgrestTransformBuilder=n.default;let o=a(e.r(332773));i.PostgrestBuilder=o.default;let l=a(e.r(466929));i.PostgrestError=l.default,i.default={PostgrestClient:t.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:r.default,PostgrestTransformBuilder:n.default,PostgrestBuilder:o.default,PostgrestError:l.default}}},369967:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({PostgrestBuilder:()=>a,PostgrestClient:()=>t,PostgrestError:()=>n,PostgrestFilterBuilder:()=>r,PostgrestQueryBuilder:()=>s,PostgrestTransformBuilder:()=>i,default:()=>o});let{PostgrestClient:t,PostgrestQueryBuilder:s,PostgrestFilterBuilder:r,PostgrestTransformBuilder:i,PostgrestBuilder:a,PostgrestError:n}=e.i(198567).default,o={PostgrestClient:t,PostgrestQueryBuilder:s,PostgrestFilterBuilder:r,PostgrestTransformBuilder:i,PostgrestBuilder:a,PostgrestError:n}}},998867:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({version:()=>t});let t="2.11.2"}},447515:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({CHANNEL_EVENTS:()=>a,CHANNEL_STATES:()=>i,CONNECTION_STATE:()=>o,DEFAULT_HEADERS:()=>t,DEFAULT_TIMEOUT:()=>c,SOCKET_STATES:()=>r,TRANSPORTS:()=>n,VSN:()=>s,WS_CLOSE_NORMAL:()=>h});var r,i,a,n,o,l=e.i(998867);let t={"X-Client-Info":`realtime-js/${l.version}`},s="1.0.0",c=1e4,h=1e3;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(r||(r={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(i||(i={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(a||(a={})),(n||(n={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(o||(o={}))}},652478:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});class t{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){let r=t.getUint8(1),i=t.getUint8(2),a=this.HEADER_LENGTH+2,n=s.decode(e.slice(a,a+r));a+=r;let o=s.decode(e.slice(a,a+i));return a+=i,{ref:null,topic:n,event:o,payload:JSON.parse(s.decode(e.slice(a,e.byteLength)))}}}}},109141:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});class t{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}}},638037:e=>{"use strict";var t,{g:s,__dirname:r}=e;{e.s({PostgresTypes:()=>t,convertCell:()=>i,convertChangeData:()=>s,convertColumn:()=>r,httpEndpointURL:()=>u,toArray:()=>c,toBoolean:()=>n,toJson:()=>l,toNumber:()=>o,toTimestampString:()=>h}),function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(t||(t={}));let s=(e,t,s={})=>{var i;let a=null!=(i=s.skipTypes)?i:[];return Object.keys(t).reduce((s,i)=>(s[i]=r(i,e,t,a),s),{})},r=(e,t,s,r)=>{let n=t.find(t=>t.name===e),o=null==n?void 0:n.type,l=s[e];return o&&!r.includes(o)?i(o,l):a(l)},i=(e,s)=>{if("_"===e.charAt(0))return c(s,e.slice(1,e.length));switch(e){case t.bool:return n(s);case t.float4:case t.float8:case t.int2:case t.int4:case t.int8:case t.numeric:case t.oid:return o(s);case t.json:case t.jsonb:return l(s);case t.timestamp:return h(s);case t.abstime:case t.date:case t.daterange:case t.int4range:case t.int8range:case t.money:case t.reltime:case t.text:case t.time:case t.timestamptz:case t.timetz:case t.tsrange:case t.tstzrange:default:return a(s)}},a=e=>e,n=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},o=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},c=(e,t)=>{if("string"!=typeof e)return e;let s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r,a=e.slice(1,s);try{r=JSON.parse("["+a+"]")}catch(e){r=a?a.split(","):[]}return r.map(e=>i(t,e))}return e},h=e=>"string"==typeof e?e.replace(" ","T"):e,u=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")}}},249386:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});var r=e.i(447515);class t{constructor(e,t,s={},i=r.DEFAULT_TIMEOUT){this.channel=e,this.event=t,this.payload=s,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null==(s=this.receivedResp)?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}}},445872:e=>{"use strict";var t,{g:s,__dirname:r}=e;{e.s({REALTIME_PRESENCE_LISTEN_EVENTS:()=>t,default:()=>s}),function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(t||(t={}));class s{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=s.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=s.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=s.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){let i=this.cloneDeep(e),a=this.transformState(t),n={},o={};return this.map(i,(e,t)=>{a[e]||(o[e]=t)}),this.map(a,(e,t)=>{let s=i[e];if(s){let r=t.map(e=>e.presence_ref),i=s.map(e=>e.presence_ref),a=t.filter(e=>0>i.indexOf(e.presence_ref)),l=s.filter(e=>0>r.indexOf(e.presence_ref));a.length>0&&(n[e]=a),l.length>0&&(o[e]=l)}else n[e]=t}),this.syncDiff(i,{joins:n,leaves:o},s,r)}static syncDiff(e,t,s,r){let{joins:i,leaves:a}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(t,r)=>{var i;let a=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(r),a.length>0){let s=e[t].map(e=>e.presence_ref),r=a.filter(e=>0>s.indexOf(e.presence_ref));e[t].unshift(...r)}s(t,a,r)}),this.map(a,(t,s)=>{let i=e[t];if(!i)return;let a=s.map(e=>e.presence_ref);i=i.filter(e=>0>a.indexOf(e.presence_ref)),e[t]=i,r(t,i,s),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,s)=>{let r=e[s];return"metas"in r?t[s]=r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}}},862686:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({REALTIME_CHANNEL_STATES:()=>t,REALTIME_LISTEN_TYPES:()=>i,REALTIME_POSTGRES_CHANGES_LISTEN_EVENT:()=>r,REALTIME_SUBSCRIBE_STATES:()=>a,default:()=>s});var r,i,a,n=e.i(447515),o=e.i(249386),l=e.i(109141),c=e.i(445872),h=e.i(638037);!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(r||(r={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(i||(i={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(a||(a={}));let t=n.CHANNEL_STATES;class s{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=n.CHANNEL_STATES.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new o.default(this,n.CHANNEL_EVENTS.join,this.params,this.timeout),this.rejoinTimer=new l.default(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=n.CHANNEL_STATES.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=n.CHANNEL_STATES.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=n.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=n.CHANNEL_STATES.errored,this.rejoinTimer.scheduleTimeout())}),this._on(n.CHANNEL_EVENTS.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new c.default(this),this.broadcastEndpointURL=(0,h.httpEndpointURL)(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:n,private:o}}=this.params;this._onError(t=>null==e?void 0:e(a.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(a.CLOSED));let l={},c={broadcast:i,presence:n,postgres_changes:null!=(r=null==(s=this.bindings.postgres_changes)?void 0:s.map(e=>e.filter))?r:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0===t){null==e||e(a.SUBSCRIBED);return}{let r=this.bindings.postgres_changes,i=null!=(s=null==r?void 0:r.length)?s:0,n=[];for(let s=0;s<i;s++){let i=r[s],{filter:{event:o,schema:l,table:c,filter:h}}=i,u=t&&t[s];if(u&&u.event===o&&u.schema===l&&u.table===c&&u.filter===h)n.push(Object.assign(Object.assign({},i),{id:u.id}));else{this.unsubscribe(),null==e||e(a.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(a.SUBSCRIBED);return}}).receive("error",t=>{null==e||e(a.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(a.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,i,a;let n=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(a=null==(i=null==(r=this.params)?void 0:r.config)?void 0:i.broadcast)?void 0:a.ack)||s("ok"),n.receive("ok",()=>s("ok")),n.receive("error",()=>s("error")),n.receive("timeout",()=>s("timed out"))});{let{event:i,payload:a}=e,n={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:a,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,n,null!=(s=t.timeout)?s:this.timeout);return await (null==(r=e.body)?void 0:r.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=n.CHANNEL_STATES.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(n.CHANNEL_EVENTS.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(s=>{let r=new o.default(this,n.CHANNEL_EVENTS.leave,{},e);r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})})}async _fetchWithTimeout(e,t,s){let r=new AbortController,i=setTimeout(()=>r.abort(),s),a=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),a}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new o.default(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;let a=e.toLocaleLowerCase(),{close:o,error:l,leave:c,join:h}=n.CHANNEL_EVENTS;if(s&&[o,l,c,h].indexOf(a)>=0&&s!==this._joinRef())return;let u=this._onMessage(a,t,s);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?null==(r=this.bindings.postgres_changes)||r.filter(e=>{var t,s,r;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(r=null==(s=e.filter)?void 0:s.event)?void 0:r.toLocaleLowerCase())===a}).map(e=>e.callback(u,s)):null==(i=this.bindings[a])||i.filter(e=>{var s,r,i,n,o,l;if(!["broadcast","presence","postgres_changes"].includes(a))return e.type.toLocaleLowerCase()===a;if("id"in e){let a=e.id,n=null==(s=e.filter)?void 0:s.event;return a&&(null==(r=t.ids)?void 0:r.includes(a))&&("*"===n||(null==n?void 0:n.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let s=null==(o=null==(n=null==e?void 0:e.filter)?void 0:n.event)?void 0:o.toLocaleLowerCase();return"*"===s||s===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:s,commit_timestamp:r,type:i,errors:a}=e;u=Object.assign(Object.assign({},{schema:t,table:s,commit_timestamp:r,eventType:i,new:{},old:{},errors:a}),this._getPayloadRecords(e))}e.callback(u,s)})}_isClosed(){return this.state===n.CHANNEL_STATES.closed}_isJoined(){return this.state===n.CHANNEL_STATES.joined}_isJoining(){return this.state===n.CHANNEL_STATES.joining}_isLeaving(){return this.state===n.CHANNEL_STATES.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){let r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null==(i=e.type)?void 0:i.toLocaleLowerCase())===r&&s.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(n.CHANNEL_EVENTS.close,{},e)}_onError(e){this._on(n.CHANNEL_EVENTS.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=n.CHANNEL_STATES.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=(0,h.convertChangeData)(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=(0,h.convertChangeData)(e.columns,e.old_record)),t}}}},49340:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>c});var r=e.i(447515),i=e.i(652478),a=e.i(109141),n=e.i(638037),o=e.i(862686);let t=()=>{},s="undefined"!=typeof WebSocket,l=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class c{constructor(s,o){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=r.DEFAULT_HEADERS,this.params={},this.timeout=r.DEFAULT_TIMEOUT,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=t,this.conn=null,this.sendBuffer=[],this.serializer=new i.default,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=t=>{let s;return s=t||("undefined"==typeof fetch?(...t)=>e.r(308217)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>s(...e)},this.endPoint=`${s}/${r.TRANSPORTS.websocket}`,this.httpEndpoint=(0,n.httpEndpointURL)(s),(null==o?void 0:o.transport)?this.transport=o.transport:this.transport=null,(null==o?void 0:o.params)&&(this.params=o.params),(null==o?void 0:o.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),o.headers)),(null==o?void 0:o.timeout)&&(this.timeout=o.timeout),(null==o?void 0:o.logger)&&(this.logger=o.logger),(null==o?void 0:o.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=o.heartbeatIntervalMs);let c=null==(l=null==o?void 0:o.params)?void 0:l.apikey;if(c&&(this.accessTokenValue=c,this.apiKey=c),this.reconnectAfterMs=(null==o?void 0:o.reconnectAfterMs)?o.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==o?void 0:o.encode)?o.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==o?void 0:o.decode)?o.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new a.default(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==o?void 0:o.fetch),null==o?void 0:o.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==o?void 0:o.worker)||!1,this.workerUrl=null==o?void 0:o.workerUrl}this.accessToken=(null==o?void 0:o.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(s){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new h(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),e.r(111371)(e.i).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:r.VSN}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case r.SOCKET_STATES.connecting:return r.CONNECTION_STATE.Connecting;case r.SOCKET_STATES.open:return r.CONNECTION_STATE.Open;case r.SOCKET_STATES.closing:return r.CONNECTION_STATE.Closing;default:return r.CONNECTION_STATE.Closed}}isConnected(){return this.connectionState()===r.CONNECTION_STATE.Open}channel(e,t={config:{}}){let s=new o.default(`realtime:${e}`,t,this);return this.channels.push(s),s}push(e){let{topic:t,event:s,payload:r,ref:i}=e,a=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(e&&e.exp&&!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`);this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(r.CHANNEL_EVENTS.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null==(e=this.conn)||e.close(r.WS_CLOSE_NORMAL,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:i}=e;i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${i&&"("+i+")"||""}`,r),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(r.CHANNEL_EVENTS.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([l],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class h{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=r.SOCKET_STATES.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}}},299978:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(49340),e.i(862686),e.i(445872)},664785:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(49340),e.i(862686),e.i(445872),e.i(299978)},807561:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({RealtimeClient:()=>r.default});var r=e.i(49340)},412889:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({StorageApiError:()=>s,StorageError:()=>t,StorageUnknownError:()=>i,isStorageError:()=>r});class t extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function r(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class s extends t{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class i extends t{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}}},174806:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({recursiveToCamel:()=>i,resolveFetch:()=>t,resolveResponse:()=>s});var r=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};let t=t=>{let s;return s=t||("undefined"==typeof fetch?(...t)=>e.r(308217)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>s(...e)},s=()=>r(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield e.r(308217)(e.i)).Response:Response}),i=e=>{if(Array.isArray(e))return e.map(e=>i(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,s])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=i(s)}),t}}},211857:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({get:()=>o,head:()=>h,post:()=>l,put:()=>c,remove:()=>u});var r=e.i(412889),i=e.i(174806),a=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};let t=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),s=(e,s,n)=>a(void 0,void 0,void 0,function*(){e instanceof(yield(0,i.resolveResponse)())&&!(null==n?void 0:n.noResolveJson)?e.json().then(i=>{s(new r.StorageApiError(t(i),e.status||500))}).catch(e=>{s(new r.StorageUnknownError(t(e),e))}):s(new r.StorageUnknownError(t(e),e))}),d=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),s))};function n(e,t,r,i,n,o){return a(this,void 0,void 0,function*(){return new Promise((a,l)=>{e(r,d(t,i,n,o)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>s(e,l,i))})})}function o(e,t,s,r){return a(this,void 0,void 0,function*(){return n(e,"GET",t,s,r)})}function l(e,t,s,r,i){return a(this,void 0,void 0,function*(){return n(e,"POST",t,r,i,s)})}function c(e,t,s,r,i){return a(this,void 0,void 0,function*(){return n(e,"PUT",t,r,i,s)})}function h(e,t,s,r){return a(this,void 0,void 0,function*(){return n(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)})}function u(e,t,s,r,i){return a(this,void 0,void 0,function*(){return n(e,"DELETE",t,r,i,s)})}}},87981:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>l});var r=e.i(109562),i=e.i(412889),a=e.i(211857),n=e.i(174806),o=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};let t={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},s={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class l{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=(0,n.resolveFetch)(r)}uploadOrUpdate(e,t,r,a){return o(this,void 0,void 0,function*(){try{let i,n=Object.assign(Object.assign({},s),a),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),l=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((i=new FormData).append("cacheControl",n.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((i=r).append("cacheControl",n.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=r,o["cache-control"]=`max-age=${n.cacheControl}`,o["content-type"]=n.contentType,l&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),(null==a?void 0:a.headers)&&(o=Object.assign(Object.assign({},o),a.headers));let c=this._removeEmptyFolders(t),h=this._getFinalPath(c),u=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:i,headers:o},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),d=yield u.json();if(u.ok)return{data:{path:c,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}upload(e,t,s){return o(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,r,a){return o(this,void 0,void 0,function*(){let n=this._removeEmptyFolders(e),o=this._getFinalPath(n),l=new URL(this.url+`/object/upload/sign/${o}`);l.searchParams.set("token",t);try{let e,t=Object.assign({upsert:s.upsert},a),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);let o=yield this.fetch(l.toString(),{method:"PUT",body:e,headers:i}),c=yield o.json();if(o.ok)return{data:{path:n,fullPath:c.Key},error:null};return{data:null,error:c}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return o(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");let n=yield(0,a.post)(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),o=new URL(this.url+n.url),l=o.searchParams.get("token");if(!l)throw new i.StorageError("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:l},error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}update(e,t,s){return o(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.post)(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}copy(e,t,s){return o(this,void 0,void 0,function*(){try{return{data:{path:(yield(0,a.post)(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,s){return o(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield(0,a.post)(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,s){return o(this,void 0,void 0,function*(){try{let r=yield(0,a.post)(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}download(e,t){return o(this,void 0,void 0,function*(){let s=void 0!==(null==t?void 0:t.transform),r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=r?`?${r}`:"";try{let t=this._getFinalPath(e),r=yield(0,a.get)(this.fetch,`${this.url}/${s?"render/image/authenticated":"object"}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}info(e){return o(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield(0,a.get)(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:(0,n.recursiveToCamel)(e),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}exists(e){return o(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield(0,a.head)(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if((0,i.isStorageError)(e)&&e instanceof i.StorageUnknownError){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let s=this._getFinalPath(e),r=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&r.push(i);let a=void 0!==(null==t?void 0:t.transform),n=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==n&&r.push(n);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${a?"render/image":"object"}/public/${s}${o}`)}}}remove(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.remove)(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}list(e,s,r){return o(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},t),s),{prefix:e||""});return{data:yield(0,a.post)(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==r.Buffer?r.Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}}},222509:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({version:()=>t});let t="2.7.1"}},842705:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({DEFAULT_HEADERS:()=>t});var r=e.i(222509);let t={"X-Client-Info":`storage-js/${r.version}`}}},244133:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});var r=e.i(842705),i=e.i(412889),a=e.i(211857),n=e.i(174806),o=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};class t{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},r.DEFAULT_HEADERS),t),this.fetch=(0,n.resolveFetch)(s)}listBuckets(){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.get)(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}getBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.get)(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.post)(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.put)(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}emptyBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.post)(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}deleteBucket(e){return o(this,void 0,void 0,function*(){try{return{data:yield(0,a.remove)(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}})}}}},882553:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({StorageClient:()=>t});var r=e.i(87981),i=e.i(244133);class t extends i.default{constructor(e,t={},s){super(e,t,s)}from(e){return new r.default(this.url,this.headers,e,this.fetch)}}}},649744:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({version:()=>t});let t="2.49.8"}},834688:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({DEFAULT_AUTH_OPTIONS:()=>n,DEFAULT_DB_OPTIONS:()=>a,DEFAULT_GLOBAL_OPTIONS:()=>i,DEFAULT_HEADERS:()=>s,DEFAULT_REALTIME_OPTIONS:()=>o});var r=e.i(649744);let t="";t="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let s={"X-Client-Info":`supabase-js-${t}/${r.version}`},i={headers:s},a={schema:"public"},n={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},o={}}},327346:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({fetchWithAuth:()=>a,resolveFetch:()=>t,resolveHeadersConstructor:()=>s});var r=e.i(531237),i=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};let t=e=>{let t;return t=e||("undefined"==typeof fetch?r.default:fetch),(...e)=>t(...e)},s=()=>"undefined"==typeof Headers?r.Headers:Headers,a=(e,r,a)=>{let n=t(a),o=s();return(t,s)=>i(void 0,void 0,void 0,function*(){var i;let a=null!=(i=yield r())?i:e,l=new o(null==s?void 0:s.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),n(t,Object.assign(Object.assign({},s),{headers:l}))})}}},369016:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({applySettingDefaults:()=>n,ensureTrailingSlash:()=>a,isBrowser:()=>t,uuid:()=>i});var r=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}function a(e){return e.endsWith("/")?e:e+"/"}let t=()=>"undefined"!=typeof window;function n(e,t){var s,i;let{db:a,auth:n,realtime:o,global:l}=e,{db:c,auth:h,realtime:u,global:d}=t,p={db:Object.assign(Object.assign({},c),a),auth:Object.assign(Object.assign({},h),n),realtime:Object.assign(Object.assign({},u),o),global:Object.assign(Object.assign(Object.assign({},d),l),{headers:Object.assign(Object.assign({},null!=(s=null==d?void 0:d.headers)?s:{}),null!=(i=null==l?void 0:l.headers)?i:{})}),accessToken:()=>r(this,void 0,void 0,function*(){return""})};return e.accessToken?p.accessToken=e.accessToken:delete p.accessToken,p}}},630272:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({version:()=>t});let t="2.69.1"}},176934:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({API_VERSIONS:()=>u,API_VERSION_HEADER_NAME:()=>h,AUDIENCE:()=>o,AUTO_REFRESH_TICK_DURATION_MS:()=>t,AUTO_REFRESH_TICK_THRESHOLD:()=>s,BASE64URL_REGEX:()=>d,DEFAULT_HEADERS:()=>l,EXPIRY_MARGIN_MS:()=>i,GOTRUE_URL:()=>a,JWKS_TTL:()=>p,NETWORK_FAILURE:()=>c,STORAGE_KEY:()=>n});var r=e.i(630272);let t=3e4,s=3,i=9e4,a="http://localhost:9999",n="supabase.auth.token",o="",l={"X-Client-Info":`gotrue-js/${r.version}`},c={MAX_RETRIES:10,RETRY_INTERVAL:2},h="X-Supabase-Api-Version",u={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},d=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,p=6e5}},545657:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({AuthApiError:()=>s,AuthError:()=>t,AuthImplicitGrantRedirectError:()=>f,AuthInvalidCredentialsError:()=>p,AuthInvalidJwtError:()=>y,AuthInvalidTokenResponseError:()=>d,AuthPKCEGrantCodeExchangeError:()=>g,AuthRetryableFetchError:()=>m,AuthSessionMissingError:()=>u,AuthUnknownError:()=>c,AuthWeakPasswordError:()=>v,CustomAuthError:()=>h,isAuthApiError:()=>i,isAuthError:()=>r,isAuthImplicitGrantRedirectError:()=>n,isAuthRetryableFetchError:()=>o,isAuthSessionMissingError:()=>a,isAuthWeakPasswordError:()=>l});class t extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function r(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class s extends t{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}function i(e){return r(e)&&"AuthApiError"===e.name}class c extends t{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class h extends t{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class u extends h{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function a(e){return r(e)&&"AuthSessionMissingError"===e.name}class d extends h{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class p extends h{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class f extends h{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function n(e){return r(e)&&"AuthImplicitGrantRedirectError"===e.name}class g extends h{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class m extends h{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function o(e){return r(e)&&"AuthRetryableFetchError"===e.name}class v extends h{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}function l(e){return r(e)&&"AuthWeakPasswordError"===e.name}class y extends h{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}}},19362:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({base64UrlToUint8Array:()=>h,byteFromBase64URL:()=>i,byteToBase64URL:()=>r,codepointToUTF8:()=>o,stringFromBase64URL:()=>n,stringFromUTF8:()=>c,stringToBase64URL:()=>a,stringToUTF8:()=>l,stringToUint8Array:()=>u});let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),s=" 	\n\r=".split(""),d=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<s.length;t+=1)e[s[t].charCodeAt(0)]=-2;for(let s=0;s<t.length;s+=1)e[t[s].charCodeAt(0)]=s;return e})();function r(e,s,r){if(null!==e)for(s.queue=s.queue<<8|e,s.queuedBits+=8;s.queuedBits>=6;)r(t[s.queue>>s.queuedBits-6&63]),s.queuedBits-=6;else if(s.queuedBits>0)for(s.queue=s.queue<<6-s.queuedBits,s.queuedBits=6;s.queuedBits>=6;)r(t[s.queue>>s.queuedBits-6&63]),s.queuedBits-=6}function i(e,t,s){let r=d[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===r)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function a(e){let t=[],s=e=>{t.push(e)},i={queue:0,queuedBits:0};return l(e,e=>{r(e,i,s)}),r(null,i,s),t.join("")}function n(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},a={queue:0,queuedBits:0},n=e=>{c(e,r,s)};for(let t=0;t<e.length;t+=1)i(e.charCodeAt(t),a,n);return t.join("")}function o(e,t){if(e<=127)return void t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function l(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}o(r,t)}}function c(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}function h(e){let t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)i(e.charCodeAt(t),s,r);return new Uint8Array(t)}function u(e){let t=[];return l(e,e=>t.push(e)),new Uint8Array(t)}}},349921:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({Deferred:()=>j,decodeJWT:()=>c,expiresAt:()=>n,generatePKCEChallenge:()=>g,generatePKCEVerifier:()=>p,getAlgorithm:()=>w,getCodeChallengeAndMethod:()=>m,getItemAsync:()=>S,isBrowser:()=>t,looksLikeFetchResponse:()=>_,parseParametersFromURL:()=>l,parseResponseAPIVersion:()=>v,removeItemAsync:()=>k,resolveFetch:()=>x,retryable:()=>u,setItemAsync:()=>E,sleep:()=>h,supportsLocalStorage:()=>b,uuid:()=>o,validateExp:()=>y});var r=e.i(176934),i=e.i(545657),a=e.i(19362);function n(e){return Math.round(Date.now()/1e3)+e}function o(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}let t=()=>"undefined"!=typeof window&&"undefined"!=typeof document,s={tested:!1,writable:!1},b=()=>{if(!t())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(s.tested)return s.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),s.tested=!0,s.writable=!0}catch(e){s.tested=!0,s.writable=!1}return s.writable};function l(e){let t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(e){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}let x=t=>{let s;return s=t||("undefined"==typeof fetch?(...t)=>e.r(308217)(e.i).then(({default:e})=>e(...t)):fetch),(...e)=>s(...e)},_=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,E=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},S=async(e,t)=>{let s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(e){return s}},k=async(e,t)=>{await e.removeItem(t)};class j{constructor(){this.promise=new j.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function c(e){let t=e.split(".");if(3!==t.length)throw new i.AuthInvalidJwtError("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!r.BASE64URL_REGEX.test(t[e]))throw new i.AuthInvalidJwtError("JWT not in base64url format");return{header:JSON.parse((0,a.stringFromBase64URL)(t[0])),payload:JSON.parse((0,a.stringFromBase64URL)(t[1])),signature:(0,a.base64UrlToUint8Array)(t[2]),raw:{header:t[0],payload:t[1]}}}async function h(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function u(e,t){return new Promise((s,r)=>{(async()=>{for(let i=0;i<1/0;i++)try{let r=await e(i);if(!t(i,null,r))return void s(r)}catch(e){if(!t(i,e))return void r(e)}})()})}function d(e){return("0"+e.toString(16)).substr(-2)}function p(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,d).join("")}async function f(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function g(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await f(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function m(e,t,s=!1){let r=p(),i=r;s&&(i+="/PASSWORD_RECOVERY"),await E(e,`${t}-code-verifier`,i);let a=await g(r),n=r===a?"plain":"s256";return[a,n]}j.promiseConstructor=Promise;let T=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function v(e){let t=e.headers.get(r.API_VERSION_HEADER_NAME);if(!t||!t.match(T))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}function y(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}function w(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}}},109545:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({_generateLinkResponse:()=>f,_noResolveJsonResponse:()=>g,_request:()=>l,_sessionResponse:()=>h,_sessionResponsePassword:()=>u,_ssoResponse:()=>p,_userResponse:()=>d,handleError:()=>o});var r=e.i(176934),i=e.i(349921),a=e.i(545657),n=this&&this.__rest||function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};let t=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),s=[502,503,504];async function o(e){var n;let o,l;if(!(0,i.looksLikeFetchResponse)(e))throw new a.AuthRetryableFetchError(t(e),0);if(s.includes(e.status))throw new a.AuthRetryableFetchError(t(e),e.status);try{o=await e.json()}catch(e){throw new a.AuthUnknownError(t(e),e)}let c=(0,i.parseResponseAPIVersion)(e);if(c&&c.getTime()>=r.API_VERSIONS["2024-01-01"].timestamp&&"object"==typeof o&&o&&"string"==typeof o.code?l=o.code:"object"==typeof o&&o&&"string"==typeof o.error_code&&(l=o.error_code),l){if("weak_password"===l)throw new a.AuthWeakPasswordError(t(o),e.status,(null==(n=o.weak_password)?void 0:n.reasons)||[]);else if("session_not_found"===l)throw new a.AuthSessionMissingError}else if("object"==typeof o&&o&&"object"==typeof o.weak_password&&o.weak_password&&Array.isArray(o.weak_password.reasons)&&o.weak_password.reasons.length&&o.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new a.AuthWeakPasswordError(t(o),e.status,o.weak_password.reasons);throw new a.AuthApiError(t(o),e.status||500,l)}let m=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),s))};async function l(e,t,s,i){var a;let n=Object.assign({},null==i?void 0:i.headers);n[r.API_VERSION_HEADER_NAME]||(n[r.API_VERSION_HEADER_NAME]=r.API_VERSIONS["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let o=null!=(a=null==i?void 0:i.query)?a:{};(null==i?void 0:i.redirectTo)&&(o.redirect_to=i.redirectTo);let l=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",h=await c(e,t,s+l,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(h):{data:Object.assign({},h),error:null}}async function c(e,s,r,i,n,l){let c,h=m(s,i,n,l);try{c=await e(r,Object.assign({},h))}catch(e){throw console.error(e),new a.AuthRetryableFetchError(t(e),0)}if(c.ok||await o(c),null==i?void 0:i.noResolveJson)return c;try{return await c.json()}catch(e){await o(e)}}function h(e){var t,s;let r=null;return(s=e).access_token&&s.refresh_token&&s.expires_in&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(0,i.expiresAt)(e.expires_in))),{data:{session:r,user:null!=(t=e.user)?t:e},error:null}}function u(e){let t=h(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function d(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function p(e){return{data:e,error:null}}function f(e){let{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:a}=e;return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:a},user:Object.assign({},n(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function g(e){return e}}},949987:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});var r=e.i(109545),i=e.i(349921),a=e.i(545657),n=this&&this.__rest||function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};class t{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=(0,i.resolveFetch)(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await (0,r._request)(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await (0,r._request)(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:r._userResponse})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,s=n(e,["options"]),i=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(i.new_email=null==s?void 0:s.newEmail,delete i.newEmail),await (0,r._request)(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:r._generateLinkResponse,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if((0,a.isAuthError)(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await (0,r._request)(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:r._userResponse})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,s,i,n,o,l,c;try{let a={nextPage:null,lastPage:0,total:0},h=await (0,r._request)(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(s=null==(t=null==e?void 0:e.page)?void 0:t.toString())?s:"",per_page:null!=(n=null==(i=null==e?void 0:e.perPage)?void 0:i.toString())?n:""},xform:r._noResolveJsonResponse});if(h.error)throw h.error;let u=await h.json(),d=null!=(o=h.headers.get("x-total-count"))?o:0,p=null!=(c=null==(l=h.headers.get("link"))?void 0:l.split(","))?c:[];return p.length>0&&(p.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);a[`${s}Page`]=t}),a.total=parseInt(d)),{data:Object.assign(Object.assign({},u),a),error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){try{return await (0,r._request)(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:r._userResponse})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){try{return await (0,r._request)(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:r._userResponse})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){try{return await (0,r._request)(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:r._userResponse})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){try{let{data:t,error:s}=await (0,r._request)(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async _deleteFactor(e){try{return{data:await (0,r._request)(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}}}},125239:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({localStorageAdapter:()=>t,memoryLocalStorageAdapter:()=>i});var r=e.i(349921);let t={getItem:e=>(0,r.supportsLocalStorage)()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{(0,r.supportsLocalStorage)()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{(0,r.supportsLocalStorage)()&&globalThis.localStorage.removeItem(e)}};function i(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}}},535667:e=>{"use strict";var{g:t,__dirname:s}=e;function r(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}e.s({polyfillGlobalThis:()=>r})},789029:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({LockAcquireTimeoutError:()=>s,NavigatorLockAcquireTimeoutError:()=>n,ProcessLockAcquireTimeoutError:()=>o,internals:()=>t,navigatorLock:()=>i,processLock:()=>a});var r=e.i(349921);let t={debug:!!(globalThis&&(0,r.supportsLocalStorage)()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class s extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class n extends s{}class o extends s{}async function i(e,s,r){t.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,s);let i=new globalThis.AbortController;return s>0&&setTimeout(()=>{i.abort(),t.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},s),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===s?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){t.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{t.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}if(0===s)throw t.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new n(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(t.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}let l={};async function a(e,t,s){var r;let i=null!=(r=l[e])?r:Promise.resolve(),a=Promise.race([i.catch(()=>null),t>=0?new Promise((s,r)=>{setTimeout(()=>{r(new o(`Acquring process lock with name "${e}" timed out`))},t)}):null].filter(e=>e)).catch(e=>{if(e&&e.isAcquireTimeout)throw e;return null}).then(async()=>await s());return l[e]=a.catch(async e=>{if(e&&e.isAcquireTimeout)return await i,null;throw e}),await a}}},731799:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>s});var r=e.i(949987),i=e.i(176934),a=e.i(545657),n=e.i(109545),o=e.i(349921),l=e.i(125239),c=e.i(535667),h=e.i(630272),u=e.i(789029),d=e.i(19362);(0,c.polyfillGlobalThis)();let t={url:i.GOTRUE_URL,storageKey:i.STORAGE_KEY,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:i.DEFAULT_HEADERS,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function p(e,t,s){return await s()}class s{constructor(e){var i,a;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=s.nextInstanceID,s.nextInstanceID+=1,this.instanceID>0&&(0,o.isBrowser)()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let n=Object.assign(Object.assign({},t),e);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new r.default({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=(0,o.resolveFetch)(n.fetch),this.lock=n.lock||p,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:(0,o.isBrowser)()&&(null==(i=null==globalThis?void 0:globalThis.navigator)?void 0:i.locks)?this.lock=u.navigatorLock:this.lock=p,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:(0,o.supportsLocalStorage)()?this.storage=l.localStorageAdapter:(this.memoryStorage={},this.storage=(0,l.memoryLocalStorageAdapter)(this.memoryStorage)):(this.memoryStorage={},this.storage=(0,l.memoryLocalStorageAdapter)(this.memoryStorage)),(0,o.isBrowser)()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(a=this.broadcastChannel)||a.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${h.version}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=(0,o.parseParametersFromURL)(window.location.href),s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),(0,o.isBrowser)()&&this.detectSessionInUrl&&"none"!==s){let{data:r,error:i}=await this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),(0,a.isAuthImplicitGrantRedirectError)(i)){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:n,redirectType:o}=r;return this._debug("#_initialize()","detected session in URL",n,"redirect type",o),await this._saveSession(n),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if((0,a.isAuthError)(e))return{error:e};return{error:new a.AuthUnknownError("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{let{data:i,error:a}=await (0,n._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(s=null==(t=null==e?void 0:e.options)?void 0:t.data)?s:{},gotrue_meta_security:{captcha_token:null==(r=null==e?void 0:e.options)?void 0:r.captchaToken}},xform:n._sessionResponse});if(a||!i)return{data:{user:null,session:null},error:a};let o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,s,r;try{let i;if("email"in e){let{email:s,password:r,options:a}=e,l=null,c=null;"pkce"===this.flowType&&([l,c]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey)),i=await (0,n._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==a?void 0:a.emailRedirectTo,body:{email:s,password:r,data:null!=(t=null==a?void 0:a.data)?t:{},gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken},code_challenge:l,code_challenge_method:c},xform:n._sessionResponse})}else if("phone"in e){let{phone:t,password:a,options:o}=e;i=await (0,n._request)(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:a,data:null!=(s=null==o?void 0:o.data)?s:{},channel:null!=(r=null==o?void 0:o.channel)?r:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:n._sessionResponse})}else throw new a.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:l,error:c}=i;if(c||!l)return{data:{user:null,session:null},error:c};let h=l.session,u=l.user;return l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",h)),{data:{user:u,session:h},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:s,password:r,options:i}=e;t=await (0,n._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:n._sessionResponsePassword})}else if("phone"in e){let{phone:s,password:r,options:i}=e;t=await (0,n._request)(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:n._sessionResponsePassword})}else throw new a.AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:s,error:r}=t;if(r)return{data:{user:null,session:null},error:r};if(!s||!s.session||!s.user)return{data:{user:null,session:null},error:new a.AuthInvalidTokenResponseError};return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,s,r,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(s=e.options)?void 0:s.scopes,queryParams:null==(r=e.options)?void 0:r.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){let t=await (0,o.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await (0,n._request)(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:n._sessionResponse});if(await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new a.AuthInvalidTokenResponseError};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:i}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:s,token:r,access_token:i,nonce:o}=e,{data:l,error:c}=await (0,n._request)(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:n._sessionResponse});if(c)return{data:{user:null,session:null},error:c};if(!l||!l.session||!l.user)return{data:{user:null,session:null},error:new a.AuthInvalidTokenResponseError};return l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,s,r,i,l;try{if("email"in e){let{email:r,options:i}=e,a=null,l=null;"pkce"===this.flowType&&([a,l]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{error:c}=await (0,n._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(s=null==i?void 0:i.shouldCreateUser)||s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:a,code_challenge_method:l},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:c}}if("phone"in e){let{phone:t,options:s}=e,{data:a,error:o}=await (0,n._request)(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(r=null==s?void 0:s.data)?r:{},create_user:null==(i=null==s?void 0:s.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!=(l=null==s?void 0:s.channel)?l:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new a.AuthInvalidCredentialsError("You must provide either an email or phone number.")}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,s;try{let r,i;"options"in e&&(r=null==(t=e.options)?void 0:t.redirectTo,i=null==(s=e.options)?void 0:s.captchaToken);let{data:a,error:o}=await (0,n._request)(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:n._sessionResponse});if(o)throw o;if(!a)throw Error("An error occurred on token verification.");let l=a.session,c=a.user;return(null==l?void 0:l.access_token)&&(await this._saveSession(l),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,s,r;try{let i=null,a=null;return"pkce"===this.flowType&&([i,a]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey)),await (0,n._request)(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(s=null==(t=e.options)?void 0:t.redirectTo)?s:void 0}),(null==(r=null==e?void 0:e.options)?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:a}),headers:this.headers,xform:n._ssoResponse})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new a.AuthSessionMissingError;let{error:r}=await (0,n._request)(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:s,type:r,options:i}=e,{error:a}=await (0,n._request)(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:s,type:r,options:i}=e,{data:a,error:o}=await (0,n._request)(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new a.AuthInvalidCredentialsError("You must provide either an email or phone number and a type")}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await (0,o.getItemAsync)(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let s=!!e.expires_at&&1e3*e.expires_at-Date.now()<i.EXPIRY_MARGIN_MS;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}let{session:r,error:a}=await this._callRefreshToken(e.refresh_token);if(a)return{data:{session:null},error:a};return{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await (0,n._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:n._userResponse});return await this._useSession(async e=>{var t,s,r;let{data:i,error:o}=e;if(o)throw o;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await (0,n._request)(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0,xform:n._userResponse}):{data:{user:null},error:new a.AuthSessionMissingError}})}catch(e){if((0,a.isAuthError)(e))return(0,a.isAuthSessionMissingError)(e)&&(await this._removeSession(),await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{let{data:r,error:i}=s;if(i)throw i;if(!r.session)throw new a.AuthSessionMissingError;let l=r.session,c=null,h=null;"pkce"===this.flowType&&null!=e.email&&([c,h]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey));let{data:u,error:d}=await (0,n._request)(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:c,code_challenge_method:h}),jwt:l.access_token,xform:n._userResponse});if(d)throw d;return l.user=u.user,await this._saveSession(l),await this._notifyAllSubscribers("USER_UPDATED",l),{data:{user:l.user},error:null}})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new a.AuthSessionMissingError;let t=Date.now()/1e3,s=t,r=!0,i=null,{payload:n}=(0,o.decodeJWT)(e.access_token);if(n.exp&&(r=(s=n.exp)<=t),r){let{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:r,error:a}=await this._getUser(e.access_token);if(a)throw a;i={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){let{data:r,error:i}=t;if(i)throw i;e=null!=(s=r.session)?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new a.AuthSessionMissingError;let{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if((0,a.isAuthError)(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!(0,o.isBrowser)())throw new a.AuthImplicitGrantRedirectError("No browser detected.");if(e.error||e.error_description||e.error_code)throw new a.AuthImplicitGrantRedirectError(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new a.AuthPKCEGrantCodeExchangeError("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new a.AuthImplicitGrantRedirectError("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new a.AuthPKCEGrantCodeExchangeError("No code detected.");let{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;let r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:s,provider_refresh_token:r,access_token:n,refresh_token:l,expires_in:c,expires_at:h,token_type:u}=e;if(!n||!c||!l||!u)throw new a.AuthImplicitGrantRedirectError("No session defined in URL");let d=Math.round(Date.now()/1e3),p=parseInt(c),f=d+p;h&&(f=parseInt(h));let g=f-d;1e3*g<=i.AUTO_REFRESH_TICK_DURATION_MS&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${p}s`);let m=f-p;d-m>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",m,f,d):d-m<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",m,f,d);let{data:v,error:y}=await this._getUser(n);if(y)throw y;let w={provider_token:s,provider_refresh_token:r,access_token:n,expires_in:p,expires_at:f,refresh_token:l,token_type:u,user:v.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:w,redirectType:e.type},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await (0,o.getItemAsync)(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{error:i};let n=null==(s=r.session)?void 0:s.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!((0,a.isAuthApiError)(t)&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await (0,o.removeItemAsync)(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t=(0,o.uuid)(),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{let{data:{session:r},error:i}=t;if(i)throw i;await (null==(s=this.stateChangeEmitters.get(e))?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(t){await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey,!0));try{return await (0,n._request)(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:s,error:r}=await this._useSession(async t=>{var s,r,i,a,o;let{data:l,error:c}=t;if(c)throw c;let h=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(s=e.options)?void 0:s.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await (0,n._request)(this.fetch,"GET",h,{headers:this.headers,jwt:null!=(o=null==(a=l.session)?void 0:a.access_token)?o:void 0})});if(r)throw r;return!(0,o.isBrowser)()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(t){if((0,a.isAuthError)(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:a}=t;if(a)throw a;return await (0,n._request)(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0})})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{let s=Date.now();return await (0,o.retryable)(async s=>(s>0&&await (0,o.sleep)(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await (0,n._request)(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:n._sessionResponse})),(e,t)=>{let r=200*Math.pow(2,e);return t&&(0,a.isAuthRetryableFetchError)(t)&&Date.now()+r-s<i.AUTO_REFRESH_TICK_DURATION_MS})}catch(e){if(this._debug(t,"error",e),(0,a.isAuthError)(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),(0,o.isBrowser)()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let s=await (0,o.getItemAsync)(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),null!==s&&await this._removeSession();return}let r=(null!=(e=s.expires_at)?e:1/0)*1e3-Date.now()<i.EXPIRY_MARGIN_MS;if(this._debug(t,`session has${r?"":" not"} expired with margin of ${i.EXPIRY_MARGIN_MS}s`),r){if(this.autoRefreshToken&&s.refresh_token){let{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),(0,a.isAuthRetryableFetchError)(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new a.AuthSessionMissingError;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new o.Deferred;let{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new a.AuthSessionMissingError;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(e){if(this._debug(r,"error",e),(0,a.isAuthError)(e)){let s={session:null,error:e};return(0,a.isAuthRetryableFetchError)(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(s),s}throw null==(s=this.refreshingDeferred)||s.reject(e),e}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){let r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});let r=[],i=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(e){r.push(e)}});if(await Promise.all(i),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await (0,o.setItemAsync)(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await (0,o.removeItemAsync)(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&(0,o.isBrowser)()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),i.AUTO_REFRESH_TICK_DURATION_MS);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let r=Math.floor((1e3*s.expires_at-e)/i.AUTO_REFRESH_TICK_DURATION_MS);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts ${i.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${i.AUTO_REFRESH_TICK_THRESHOLD} ticks`),r<=i.AUTO_REFRESH_TICK_THRESHOLD&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof u.LockAcquireTimeoutError)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!(0,o.isBrowser)()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){let r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){let[e,t]=await (0,o.getCodeChallengeAndMethod)(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){let e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await (0,n._request)(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:a}=t;if(a)return{data:null,error:a};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=await (0,n._request)(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null==(s=null==i?void 0:i.session)?void 0:s.access_token});return c?{data:null,error:c}:("totp"===e.factorType&&(null==(r=null==l?void 0:l.totp)?void 0:r.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{data:null,error:i};let{data:a,error:o}=await (0,n._request)(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:o})})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await (0,n._request)(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let s=(null==e?void 0:e.factors)||[],r=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:r,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;let{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:a}=(0,o.decodeJWT)(r.access_token),n=null;a.aal&&(n=a.aal);let l=n;return(null!=(s=null==(t=r.user.factors)?void 0:t.filter(e=>"verified"===e.status))?s:[]).length>0&&(l="aal2"),{data:{currentLevel:n,nextLevel:l,currentAuthenticationMethods:a.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(t=>t.kid===e);if(s||(s=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+i.JWKS_TTL>Date.now())return s;let{data:r,error:o}=await (0,n._request)(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!r.keys||0===r.keys.length)throw new a.AuthInvalidJwtError("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),!(s=r.keys.find(t=>t.kid===e)))throw new a.AuthInvalidJwtError("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}let{header:r,payload:i,signature:n,raw:{header:l,payload:c}}=(0,o.decodeJWT)(s);if((0,o.validateExp)(i.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:i,header:r,signature:n},error:null}}let h=(0,o.getAlgorithm)(r.alg),u=await this.fetchJwk(r.kid,t),p=await crypto.subtle.importKey("jwk",u,h,!0,["verify"]);if(!await crypto.subtle.verify(h,p,n,(0,d.stringToUint8Array)(`${l}.${c}`)))throw new a.AuthInvalidJwtError("Invalid JWT signature");return{data:{claims:i,header:r,signature:n},error:null}}catch(e){if((0,a.isAuthError)(e))return{data:null,error:e};throw e}}}s.nextInstanceID=0}},757062:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});let t=e.i(949987).default}},646789:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});let t=e.i(731799).default}},15913:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({})},111:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(949987),e.i(731799),e.i(757062),e.i(646789),e.i(15913),e.i(545657),e.i(789029)},970582:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(949987),e.i(731799),e.i(757062),e.i(646789),e.i(15913),e.i(545657),e.i(789029),e.i(111)},583695:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({AuthClient:()=>r.default});var r=e.i(646789)},163937:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({SupabaseAuthClient:()=>t}),e.i(970582);var r=e.i(583695);class t extends r.AuthClient{constructor(e){super(e)}}}},144184:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({default:()=>t});var r=e.i(891275),i=e.i(369967);e.i(664785);var a=e.i(807561),n=e.i(882553),o=e.i(834688),l=e.i(327346),c=e.i(369016),h=e.i(163937),u=this&&this.__awaiter||function(e,t,s,r){return new(s||(s=Promise))(function(i,a){function n(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}l((r=r.apply(e,t||[])).next())})};class t{constructor(e,t,s){var r,a,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let h=new URL((0,c.ensureTrailingSlash)(e));this.realtimeUrl=new URL("realtime/v1",h),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",h),this.storageUrl=new URL("storage/v1",h),this.functionsUrl=new URL("functions/v1",h);let u=`sb-${h.hostname.split(".")[0]}-auth-token`,d={db:o.DEFAULT_DB_OPTIONS,realtime:o.DEFAULT_REALTIME_OPTIONS,auth:Object.assign(Object.assign({},o.DEFAULT_AUTH_OPTIONS),{storageKey:u}),global:o.DEFAULT_GLOBAL_OPTIONS},p=(0,c.applySettingDefaults)(null!=s?s:{},d);this.storageKey=null!=(r=p.auth.storageKey)?r:"",this.headers=null!=(a=p.global.headers)?a:{},p.accessToken?(this.accessToken=p.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(n=p.auth)?n:{},this.headers,p.global.fetch),this.fetch=(0,l.fetchWithAuth)(t,this._getAccessToken.bind(this),p.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},p.realtime)),this.rest=new i.PostgrestClient(new URL("rest/v1",h).href,{headers:this.headers,schema:p.db.schema,fetch:this.fetch}),p.accessToken||this._listenForAuthEvents()}get functions(){return new r.FunctionsClient(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new n.StorageClient(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return u(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();let{data:s}=yield this.auth.getSession();return null!=(t=null==(e=s.session)?void 0:e.access_token)?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:a,lock:n,debug:o},l,c){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new h.SupabaseAuthClient({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:a,lock:n,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new a.RealtimeClient(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==s?this.changedAccessToken=s:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}}},134936:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({createClient:()=>t});var r=e.i(144184);e.i(970582),e.i(369967),e.i(664785);let t=(e,t,s)=>new r.default(e,t,s)}},152238:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({VERSION:()=>t});let t="0.6.1"}},528484:function(e){var{g:t,__dirname:s,m:r,e:i}=e;{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.parse=function(e,t){let s=new c,r=e.length;if(r<2)return s;let i=t?.decode||o,l=0;do{let t=e.indexOf("=",l);if(-1===t)break;let o=e.indexOf(";",l),c=-1===o?r:o;if(t>c){l=e.lastIndexOf(";",t-1)+1;continue}let h=a(e,l,t),u=n(e,t,h),d=e.slice(h,u);if(void 0===s[d]){let r=a(e,t+1,c),o=n(e,c,r),l=i(e.slice(r,o));s[d]=l}l=c+1}while(l<r)return s},i.serialize=function(i,a,n){let o=n?.encode||encodeURIComponent;if(!e.test(i))throw TypeError(`argument name is invalid: ${i}`);let c=o(a);if(!t.test(c))throw TypeError(`argument val is invalid: ${a}`);let h=i+"="+c;if(!n)return h;if(void 0!==n.maxAge){if(!Number.isInteger(n.maxAge))throw TypeError(`option maxAge is invalid: ${n.maxAge}`);h+="; Max-Age="+n.maxAge}if(n.domain){if(!s.test(n.domain))throw TypeError(`option domain is invalid: ${n.domain}`);h+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError(`option path is invalid: ${n.path}`);h+="; Path="+n.path}if(n.expires){var u;if(u=n.expires,"[object Date]"!==l.call(u)||!Number.isFinite(n.expires.valueOf()))throw TypeError(`option expires is invalid: ${n.expires}`);h+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(h+="; HttpOnly"),n.secure&&(h+="; Secure"),n.partitioned&&(h+="; Partitioned"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${n.sameSite}`)}return h};let e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,l=Object.prototype.toString,c=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function a(e,t,s){do{let s=e.charCodeAt(t);if(32!==s&&9!==s)return t}while(++t<s)return s}function n(e,t,s){for(;t>s;){let s=e.charCodeAt(--t);if(32!==s&&9!==s)return t+1}return s}function o(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}},936153:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({isBrowser:()=>n,parse:()=>t,parseCookieHeader:()=>i,serialize:()=>s,serializeCookieHeader:()=>a});var r=e.i(528484);let t=r.parse,s=r.serialize;function i(e){let t=(0,r.parse)(e);return Object.keys(t??{}).map(e=>({name:e,value:t[e]}))}function a(e,t,s){return(0,r.serialize)(e,t,s)}function n(){return"undefined"!=typeof window&&void 0!==window.document}}},906151:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({DEFAULT_COOKIE_OPTIONS:()=>t});let t={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4}}},976285:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({MAX_CHUNK_SIZE:()=>t,combineChunks:()=>a,createChunks:()=>i,deleteChunks:()=>n,isChunkLike:()=>r});let t=3180,s=/^(.*)[.](0|[1-9][0-9]*)$/;function r(e,t){if(e===t)return!0;let r=e.match(s);return!!r&&r[1]===t}function i(e,s,r){let i=r??t,a=encodeURIComponent(s);if(a.length<=i)return[{name:e,value:s}];let n=[];for(;a.length>0;){let e=a.slice(0,i),t=e.lastIndexOf("%");t>i-3&&(e=e.slice(0,t));let s="";for(;e.length>0;)try{s=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(s),a=a.slice(e.length)}return n.map((t,s)=>({name:`${e}.${s}`,value:t}))}async function a(e,t){let s=await t(e);if(s)return s;let r=[];for(let s=0;;s++){let i=`${e}.${s}`,a=await t(i);if(!a)break;r.push(a)}return r.length>0?r.join(""):null}async function n(e,t,s){await t(e)&&await s(e);for(let r=0;;r++){let i=`${e}.${r}`;if(!await t(i))break;await s(i)}}}},246564:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({codepointToUTF8:()=>a,stringFromBase64URL:()=>i,stringFromUTF8:()=>o,stringToBase64URL:()=>r,stringToUTF8:()=>n});let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),s=" 	\n\r=".split(""),l=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<s.length;t+=1)e[s[t].charCodeAt(0)]=-2;for(let s=0;s<t.length;s+=1)e[t[s].charCodeAt(0)]=s;return e})();function r(e){let s=[],r=0,i=0;if(n(e,e=>{for(r=r<<8|e,i+=8;i>=6;){let e=r>>i-6&63;s.push(t[e]),i-=6}}),i>0)for(r<<=6-i,i=6;i>=6;){let e=r>>i-6&63;s.push(t[e]),i-=6}return s.join("")}function i(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i=0,a=0;for(let t=0;t<e.length;t+=1){let n=l[e.charCodeAt(t)];if(n>-1)for(i=i<<6|n,a+=6;a>=8;)o(i>>a-8&255,r,s),a-=8;else if(-2===n)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}function a(e,t){if(e<=127)return void t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function n(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}a(r,t)}}function o(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}}},138965:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(936153),e.i(906151),e.i(976285),e.i(246564)},583058:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(936153),e.i(906151),e.i(976285),e.i(246564),e.i(138965)},689466:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({applyServerStorage:()=>c,createStorageFromOptions:()=>l});var r=e.i(528484);e.i(583058);var i=e.i(906151),a=e.i(976285),n=e.i(936153),o=e.i(246564);let t="base64-";function l(e,s){let l,h,u=e.cookies??null,d=e.cookieEncoding,p={},f={};if(u)if("get"in u){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,s)=>`${e}.${s}`)]),s=[];for(let e=0;e<t.length;e+=1){let r=await u.get(t[e]);(r||"string"==typeof r)&&s.push({name:t[e],value:r})}return s};if(l=async t=>await e(t),"set"in u&&"remove"in u)h=async e=>{for(let t=0;t<e.length;t+=1){let{name:s,value:r,options:i}=e[t];r?await u.set(s,r,i):await u.remove(s,i)}};else if(s)h=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in u)if(l=async()=>await u.getAll(),"setAll"in u)h=u.setAll;else if(s)h=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${s?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,n.isBrowser)()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!s&&(0,n.isBrowser)()){let e=()=>{let e=(0,r.parse)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};l=()=>e(),h=e=>{e.forEach(({name:e,value:t,options:s})=>{document.cookie=(0,r.serialize)(e,t,s)})}}else if(s)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else l=()=>[],h=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return s?{getAll:l,setAll:h,setItems:p,removedItems:f,storage:{isServer:!0,getItem:async e=>{if("string"==typeof p[e])return p[e];if(f[e])return null;let s=await l([e]),r=await (0,a.combineChunks)(e,async e=>{let t=s?.find(({name:t})=>t===e)||null;return t?t.value:null});if(!r)return null;let i=r;return"string"==typeof r&&r.startsWith(t)&&(i=(0,o.stringFromBase64URL)(r.substring(t.length))),i},setItem:async(t,s)=>{t.endsWith("-code-verifier")&&await c({getAll:l,setAll:h,setItems:{[t]:s},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:d}),p[t]=s,delete f[t]},removeItem:async e=>{delete p[e],f[e]=!0}}}:{getAll:l,setAll:h,setItems:p,removedItems:f,storage:{isServer:!1,getItem:async e=>{let s=await l([e]),r=await (0,a.combineChunks)(e,async e=>{let t=s?.find(({name:t})=>t===e)||null;return t?t.value:null});if(!r)return null;let i=r;return r.startsWith(t)&&(i=(0,o.stringFromBase64URL)(r.substring(t.length))),i},setItem:async(s,r)=>{let n=await l([s]),c=new Set((n?.map(({name:e})=>e)||[]).filter(e=>(0,a.isChunkLike)(e,s))),u=r;"base64url"===d&&(u=t+(0,o.stringToBase64URL)(r));let p=(0,a.createChunks)(s,u);p.forEach(({name:e})=>{c.delete(e)});let f={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:0},g={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:i.DEFAULT_COOKIE_OPTIONS.maxAge};delete f.name,delete g.name;let m=[...[...c].map(e=>({name:e,value:"",options:f})),...p.map(({name:e,value:t})=>({name:e,value:t,options:g}))];m.length>0&&await h(m)},removeItem:async t=>{let s=await l([t]),r=(s?.map(({name:e})=>e)||[]).filter(e=>(0,a.isChunkLike)(e,t)),n={...i.DEFAULT_COOKIE_OPTIONS,...e?.cookieOptions,maxAge:0};delete n.name,r.length>0&&await h(r.map(e=>({name:e,value:"",options:n})))}}}}async function c({getAll:e,setAll:s,setItems:r,removedItems:n},l){let c=l.cookieEncoding,h=l.cookieOptions??null,u=await e([...r?Object.keys(r):[],...n?Object.keys(n):[]]),d=u?.map(({name:e})=>e)||[],p=Object.keys(n).flatMap(e=>d.filter(t=>(0,a.isChunkLike)(t,e))),f=Object.keys(r).flatMap(e=>{let s=new Set(d.filter(t=>(0,a.isChunkLike)(t,e))),i=r[e];"base64url"===c&&(i=t+(0,o.stringToBase64URL)(i));let n=(0,a.createChunks)(e,i);return n.forEach(e=>{s.delete(e.name)}),p.push(...s),n}),g={...i.DEFAULT_COOKIE_OPTIONS,...h,maxAge:0},m={...i.DEFAULT_COOKIE_OPTIONS,...h,maxAge:i.DEFAULT_COOKIE_OPTIONS.maxAge};delete g.name,delete m.name,await s([...p.map(e=>({name:e,value:"",options:g})),...f.map(({name:e,value:t})=>({name:e,value:t,options:m}))])}}},639238:e=>{"use strict";var{g:t,__dirname:s}=e;{let t;e.s({createBrowserClient:()=>o});var r=e.i(134936),i=e.i(152238);e.i(583058);var a=e.i(936153),n=e.i(689466);function o(e,s,o){let l=o?.isSingleton===!0||(!o||!("isSingleton"in o))&&(0,a.isBrowser)();if(l&&t)return t;if(!e||!s)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:c}=(0,n.createStorageFromOptions)({...o,cookieEncoding:o?.cookieEncoding??"base64url"},!1),h=(0,r.createClient)(e,s,{...o,global:{...o?.global,headers:{...o?.global?.headers,"X-Client-Info":`supabase-ssr/${i.VERSION} createBrowserClient`}},auth:{...o?.auth,...o?.cookieOptions?.name?{storageKey:o.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:(0,a.isBrowser)(),detectSessionInUrl:(0,a.isBrowser)(),persistSession:!0,storage:c}});return l&&(t=h),h}}},722712:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({createServerClient:()=>n});var r=e.i(134936),i=e.i(152238),a=e.i(689466);function n(e,t,s){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:n,getAll:o,setAll:l,setItems:c,removedItems:h}=(0,a.createStorageFromOptions)({...s,cookieEncoding:s?.cookieEncoding??"base64url"},!0),u=(0,r.createClient)(e,t,{...s,global:{...s?.global,headers:{...s?.global?.headers,"X-Client-Info":`supabase-ssr/${i.VERSION} createServerClient`}},auth:{...s?.cookieOptions?.name?{storageKey:s.cookieOptions.name}:null,...s?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:n}});return u.auth.onAuthStateChange(async e=>{(Object.keys(c).length>0||Object.keys(h).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await (0,a.applyServerStorage)({getAll:o,setAll:l,setItems:c,removedItems:h},{cookieOptions:s?.cookieOptions??null,cookieEncoding:s?.cookieEncoding??"base64url"})}),u}},254827:function(e){var{g:t,__dirname:s,m:r,e:i}=e},681737:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(639238),e.i(722712),e.i(254827),e.i(583058)},349472:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({}),e.i(639238),e.i(722712),e.i(254827),e.i(583058),e.i(681737)},370867:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({createClient:()=>i}),e.i(922271),e.i(349472);var r=e.i(639238);function i(){return(0,r.createBrowserClient)("https://tzjelqzwdgidsjqhmvkr.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6amVscXp3ZGdpZHNqcWhtdmtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTAsImV4cCI6MjA2MzkwNDY5MH0.MVkBlC_QI1IT7licw9CzLoS9yyo-QGXR7EFyXYZDyZc")}},985456:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({AdminDashboardHeader:()=>g});var r=e.i(731636),i=e.i(186240),a=e.i(555366),n=e.i(838653),o=e.i(828846),l=e.i(63879),c=e.i(697688),h=e.i(471895),u=e.i(339951),d=e.i(812898),p=e.i(989827),f=e.i(370867);function g({admin:e}){let t=(0,a.useRouter)(),[s,g]=(0,n.useState)(0);(0,n.useEffect)(()=>{let e=async()=>{try{let e=await (0,p.getContactRequestsCount)();e.success&&g(e.data.pending||0)}catch(e){console.error("Error fetching contact requests:",e)}};e();let t=(0,f.createClient)(),s=t.channel("contact_requests_changes").on("postgres_changes",{event:"*",schema:"public",table:"contact_requests"},t=>{console.log("Contact request change detected:",t),e()}).subscribe();return()=>{t.removeChannel(s)}},[]);let m=async()=>{try{await (0,d.adminLogout)(),t.push("/admin/login")}catch(e){console.error("Logout error:",e),t.push("/admin/login")}};return(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.Shield,{className:"w-6 h-6 text-red-600"}),(0,r.jsx)("h2",{className:"text-lg font-bold text-slate-900",children:"Admin Dashboard"})]}),e?.role==="super_admin"&&(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:"Super Admin"})]}),(0,r.jsxs)("p",{className:"text-sm text-slate-900 mt-1",children:["Welcome back, ",e?.name,(0,r.jsxs)("span",{className:"ml-2 text-slate-600",children:["• ",e?.email]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 flex-shrink-0 mx-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,r.jsx)(h.Stethoscope,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full animate-ping"}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 sm:w-5 sm:h-5 bg-orange-400 rounded-full"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-slate-800 to-teal-700 bg-clip-text text-transparent",children:"Celer AI"}),(0,r.jsx)("p",{className:"text-sm text-teal-600/80",children:"Admin Portal"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 flex-1 justify-end",children:[s>0&&(0,r.jsxs)(i.default,{href:"/admin/dashboard?tab=billing",className:"flex items-center space-x-1 px-3 py-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-md transition-colors",children:[(0,r.jsx)(u.Bell,{className:"w-4 h-4 text-orange-600"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-orange-800",children:[s," pending contact",1!==s?"s":""]})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:(0,r.jsxs)(i.default,{href:"/admin/dashboard",className:"flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium",children:[(0,r.jsx)(c.BarChart3,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Dashboard"})]})}),(0,r.jsxs)("button",{onClick:m,className:"flex items-center space-x-1 text-slate-900 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium",children:[(0,r.jsx)(o.LogOut,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Logout"})]})]})]})})})}},258725:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],s=(0,r.default)("square-pen",t)}},253183:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Edit:()=>r.default});var r=e.i(258725)},978268:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]],s=(0,r.default)("ban",t)}},653606:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({Ban:()=>r.default});var r=e.i(978268)},869616:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({performAdminAction:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("400ef906d85e45dc697968c1d9fb5f164b7102286a",r.callServer,void 0,r.findSourceMapURL,"performAdminAction")},486425:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({resetDoctorQuota:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("4068a3d4eaa9d307836531ef9e56a76d4f4a38edea",r.callServer,void 0,r.findSourceMapURL,"resetDoctorQuota")},526841:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({DoctorsTable:()=>g});var r=e.i(731636),i=e.i(838653),a=e.i(429483),n=e.i(855423),o=e.i(253183),l=e.i(643208),c=e.i(653606),h=e.i(811035),u=e.i(685068),d=e.i(869616),p=e.i(486425),f=e.i(766805);function g({doctors:e}){let[t,s]=(0,i.useState)(null),[g,m]=(0,i.useState)(null),v=async(e,t)=>{s(t),m(null);try{let s=await (0,d.performAdminAction)({action:e,doctor_id:t});s.success?(m({type:"success",text:`Doctor ${e}d successfully!`}),window.location.reload()):m({type:"error",text:s.error||`Failed to ${e} doctor`})}catch(e){m({type:"error",text:"An error occurred. Please try again."})}finally{s(null)}},y=async e=>{let t=prompt("Enter new monthly quota:");t&&!isNaN(Number(t))&&await v("approve",e)},w=async e=>{s(e),m(null);try{let t=await (0,p.resetDoctorQuota)(e);t.success?(m({type:"success",text:"Quota reset successfully!"}),window.location.reload()):m({type:"error",text:t.error||"Failed to reset quota"})}catch(e){m({type:"error",text:"An error occurred while resetting quota"})}finally{s(null)}},b=async e=>{confirm("Are you sure you want to reject this doctor? This action cannot be undone.")&&await v("reject",e)},x=async e=>{confirm("Are you sure you want to disable this doctor? They will not be able to log in until re-enabled.")&&await v("disable",e)},_=async e=>{confirm("Are you sure you want to reset this doctor's quota usage to zero?")&&await w(e)},E=e=>e.approved?(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(h.CheckCircle,{className:"w-3 h-3 mr-1"}),"Active"]}):(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(u.Clock,{className:"w-3 h-3 mr-1"}),"Pending"]}),S=e=>{let t=e.quota_percentage||0;return(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:[e.quota_used," / ",e.monthly_quota]}),(0,r.jsxs)("div",{className:`text-xs ${t>=90?"text-red-600":t>=70?"text-orange-600":"text-green-600"}`,children:[t,"% used"]})]})};return(0,r.jsxs)("div",{className:"overflow-hidden",children:[g&&(0,r.jsx)("div",{className:`p-4 mb-4 rounded-md ${"success"===g.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"}`,children:g.text}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Doctor"}),(0,r.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quota"}),(0,r.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell",children:"Activity"}),(0,r.jsx)("th",{className:"px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-xs sm:text-sm text-gray-500 break-all",children:e.email}),e.clinic_name?(0,r.jsx)("div",{className:"text-xs text-gray-400 hidden sm:block",children:e.clinic_name}):(0,r.jsx)("div",{className:"text-xs text-gray-400 hidden sm:block",children:"No clinic specified"})]})}),(0,r.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:E(e)}),(0,r.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap",children:S(e)}),(0,r.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs",children:["Joined: ",(0,f.formatDate)(e.created_at)]}),(0,r.jsxs)("div",{className:"text-xs",children:["Last active: ",e.last_activity?(0,f.formatDate)(e.last_activity):"Never"]})]})}),(0,r.jsx)("td",{className:"px-3 sm:px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-1 sm:gap-2",children:e.approved?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>y(e.id),disabled:t===e.id,className:"text-blue-600 hover:text-blue-900 disabled:opacity-50",title:"Update Quota",children:(0,r.jsx)(o.Edit,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>_(e.id),disabled:t===e.id,className:"text-purple-600 hover:text-purple-900 disabled:opacity-50",title:"Reset Quota",children:(0,r.jsx)(l.RotateCcw,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>x(e.id),disabled:t===e.id,className:"text-red-600 hover:text-red-900 disabled:opacity-50",title:"Disable Doctor",children:(0,r.jsx)(c.Ban,{className:"w-4 h-4"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>v("approve",e.id),disabled:t===e.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",title:"Approve Doctor",children:(0,r.jsx)(a.Check,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>b(e.id),disabled:t===e.id,className:"text-red-600 hover:text-red-900 disabled:opacity-50",title:"Reject Doctor",children:(0,r.jsx)(n.X,{className:"w-4 h-4"})})]})})})]},e.id))})]})}),0===e.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No doctors found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"No doctors have signed up yet."})]})]})}},82058:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],s=(0,r.default)("credit-card",t)}},905462:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({CreditCard:()=>r.default});var r=e.i(82058)},227150:e=>{"use strict";var{g:t,__dirname:s}=e;{e.s({__iconNode:()=>t,default:()=>s});var r=e.i(722486);let t=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],s=(0,r.default)("dollar-sign",t)}},130888:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({DollarSign:()=>r.default});var r=e.i(227150)},318296:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({PlanSelectionModal:()=>o});var r=e.i(731636),i=e.i(838653),a=e.i(855423),n=e.i(905462);function o({isOpen:e,onClose:t,onSelectPlan:s,plans:o,doctorName:l,isLoading:c=!1}){let[h,u]=(0,i.useState)(null);if(!e)return null;let d=e=>`₹${e.toLocaleString()}`;return(0,r.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-md w-full p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.CreditCard,{className:"w-5 h-5 text-teal-600"}),(0,r.jsxs)("h3",{className:"text-lg font-semibold text-slate-800",children:["Select Plan for Dr. ",l]})]}),(0,r.jsx)("button",{onClick:t,className:"text-slate-400 hover:text-slate-600",children:(0,r.jsx)(a.X,{className:"w-5 h-5"})})]}),(0,r.jsx)("div",{className:"space-y-3 mb-6",children:o.map(e=>(0,r.jsxs)("label",{className:`block p-4 rounded-lg cursor-pointer transition-colors ${h===e.id?"bg-teal-50 border-2 border-teal-500":"bg-slate-50 border-2 border-transparent hover:bg-slate-100"}`,children:[(0,r.jsx)("input",{type:"radio",name:"plan",value:e.id,checked:h===e.id,onChange:e=>u(e.target.value),className:"sr-only"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-slate-800",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-slate-600",children:[e.quota_limit," consultations"]})]}),(0,r.jsx)("div",{className:"text-lg font-bold text-slate-800",children:d(e.monthly_price)})]})]},e.id))}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:t,disabled:c,className:"flex-1 px-4 py-2 text-slate-700 bg-slate-200 rounded-lg hover:bg-slate-300 disabled:opacity-50",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:()=>{if(h){let e=o.find(e=>e.id===h);e&&s(h,e.monthly_price)}},disabled:!h||c,className:"flex-1 px-4 py-2 text-white bg-teal-600 rounded-lg hover:bg-teal-700 disabled:opacity-50",children:c?"Creating...":"Create Bill"})]})]})})}},329080:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getBillingStats:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("005135c7e18ac53e83714924ee812a087cbd5961ac",r.callServer,void 0,r.findSourceMapURL,"getBillingStats")},491945:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getDoctorsBillingInfo:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("00de095d837551bbb86629fce0ed19e4828a33db55",r.callServer,void 0,r.findSourceMapURL,"getDoctorsBillingInfo")},912434:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getAllBillingTransactions:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("601593b7e410bcaa66088bb5ce3de5cefb59afd238",r.callServer,void 0,r.findSourceMapURL,"getAllBillingTransactions")},490111:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({markPaymentPaid:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("70b9ce8de127fd1018b818463398d90f2be7ba0dd1",r.callServer,void 0,r.findSourceMapURL,"markPaymentPaid")},208363:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({createBillingTransaction:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("7c5e8dd2e45b0074066c6bf877fc2898170b991828",r.callServer,void 0,r.findSourceMapURL,"createBillingTransaction")},577669:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getBillingPlans:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("00eac522dfe7cabe2a6e71d6d21856221a9f52a549",r.callServer,void 0,r.findSourceMapURL,"getBillingPlans")},445825:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({getContactRequests:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("0009c0c46815af213900a610cc4a7e2d00a036b8b7",r.callServer,void 0,r.findSourceMapURL,"getContactRequests")},849719:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({updateContactRequestStatus:()=>i});var r=e.i(286714),i=(0,r.createServerReference)("60bd2570fa48d9714b2922cc0537d755b4c2180d2e",r.callServer,void 0,r.findSourceMapURL,"updateContactRequestStatus")},520573:e=>{"use strict";var{g:t,__dirname:s}=e;e.s({BillingManagement:()=>A});var r=e.i(731636),i=e.i(838653),a=e.i(905462),n=e.i(130888),o=e.i(614391),l=e.i(110845),c=e.i(811035),h=e.i(360229),u=e.i(685068),d=e.i(877099),p=e.i(505964),f=e.i(339951),g=e.i(565992),m=e.i(754085),v=e.i(318296),y=e.i(329080),w=e.i(491945),b=e.i(912434),x=e.i(490111),_=e.i(208363),E=e.i(577669),S=e.i(445825),k=e.i(849719),j=e.i(989827),T=e.i(370867);function A(){let[e,t]=(0,i.useState)("overview"),[s,A]=(0,i.useState)(null),[N,C]=(0,i.useState)([]),[R,O]=(0,i.useState)([]),[P,I]=(0,i.useState)([]),[L,$]=(0,i.useState)([]),[U,D]=(0,i.useState)(null),[q,B]=(0,i.useState)(!0),[F,M]=(0,i.useState)(""),[H,z]=(0,i.useState)("all"),[K,J]=(0,i.useState)(null),[G,V]=(0,i.useState)(!1),[W,X]=(0,i.useState)(null),[Y,Q]=(0,i.useState)(null),Z=(0,i.useRef)(e);(0,i.useEffect)(()=>{Z.current=e},[e]);let ee=(0,i.useCallback)(async()=>{try{let[e,t]=await Promise.all([(0,S.getContactRequests)(),(0,j.getContactRequestsCount)()]);e.success&&$(e.data),t.success&&D(t.data)}catch(e){console.error("Error loading contact requests data:",e)}},[]),et=(0,i.useCallback)(async()=>{try{let[e,t]=await Promise.all([(0,y.getBillingStats)(),(0,b.getAllBillingTransactions)(100)]);e.success&&A(e.data),t.success&&O(t.data.transactions)}catch(e){console.error("Error loading billing data:",e)}},[]),es=(0,i.useCallback)(async()=>{try{let e=await (0,w.getDoctorsBillingInfo)();e.success&&C(e.data)}catch(e){console.error("Error loading doctors data:",e)}},[]),er=(0,i.useCallback)(async()=>{B(!0);try{let[t,s,r]=await Promise.all([(0,y.getBillingStats)(),(0,j.getContactRequestsCount)(),(0,E.getBillingPlans)()]);t.success&&A(t.data),s.success&&D(s.data),r.success&&I(r.data),"transactions"===e?await (0,b.getAllBillingTransactions)(50).then(e=>e.success&&O(e.data.transactions)):"doctors"===e?await (0,w.getDoctorsBillingInfo)().then(e=>e.success&&C(e.data)):"requests"===e&&await (0,S.getContactRequests)().then(e=>e.success&&$(e.data))}catch(e){console.error("Error loading billing data:",e)}finally{B(!1)}},[e]);(0,i.useEffect)(()=>{er()},[er]),(0,i.useEffect)(()=>{let e=(0,T.createClient)(),t=e.channel("contact_requests_billing_changes").on("postgres_changes",{event:"*",schema:"public",table:"contact_requests"},e=>{console.log("Contact request change detected:",e),"requests"===Z.current||"INSERT"===e.eventType?ee():(0,j.getContactRequestsCount)().then(e=>e.success&&D(e.data))}).subscribe(),s=e.channel("billing_transactions_changes").on("postgres_changes",{event:"*",schema:"public",table:"billing_transactions"},e=>{console.log("Billing transaction change detected:",e),(0,y.getBillingStats)().then(e=>e.success&&A(e.data)),"transactions"===Z.current&&et()}).subscribe(),r=e.channel("doctors_billing_changes").on("postgres_changes",{event:"*",schema:"public",table:"doctors"},e=>{console.log("Doctor billing info change detected:",e),(0,y.getBillingStats)().then(e=>e.success&&A(e.data)),"doctors"===Z.current&&es()}).subscribe(),i=e.channel("referral_discounts_changes").on("postgres_changes",{event:"*",schema:"public",table:"referral_discounts"},e=>{console.log("Referral discount change detected:",e),("INSERT"===e.eventType||"UPDATE"===e.eventType&&e.new?.status==="applied")&&(0,y.getBillingStats)().then(e=>e.success&&A(e.data))}).subscribe();return()=>{e.removeChannel(t),e.removeChannel(s),e.removeChannel(r),e.removeChannel(i)}},[et,ee,es]);let ei=async e=>{if(confirm("Are you sure you want to mark this payment as paid?")){J(e),Q(null);try{let t=await (0,x.markPaymentPaid)(e,"admin_manual","Manual payment confirmation");t.success?(Q({type:"success",text:"Payment marked as paid successfully!"}),await er()):Q({type:"error",text:t.error||"Failed to mark payment as paid"})}catch(e){Q({type:"error",text:"An unexpected error occurred."})}finally{J(null)}}},ea=async(e,t,s)=>{J(e),Q(null);try{let r=await (0,_.createBillingTransaction)(e,t,s);r.success?(V(!1),X(null),await er()):Q({type:"error",text:r.error||"Failed to create bill"})}catch(e){Q({type:"error",text:"An unexpected error occurred."})}finally{J(null)}},en=async(e,t)=>{J(e),Q(null);try{let s=await (0,k.updateContactRequestStatus)(e,t);s.success?await er():Q({type:"error",text:s.error||"Failed to update status"})}catch(e){Q({type:"error",text:"An unexpected error occurred."})}finally{J(null)}},eo=e=>`₹${e.toLocaleString()}`,el=e=>{switch(e){case"paid":return(0,r.jsx)(c.CheckCircle,{className:"w-4 h-4 text-green-600"});case"pending":return(0,r.jsx)(u.Clock,{className:"w-4 h-4 text-yellow-600"});case"failed":return(0,r.jsx)(h.AlertCircle,{className:"w-4 h-4 text-red-600"});default:return(0,r.jsx)(u.Clock,{className:"w-4 h-4 text-gray-600"})}},ec=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"trial":return"bg-blue-100 text-blue-800";case"suspended":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},eh=N.filter(e=>e.name.toLowerCase().includes(F.toLowerCase())||e.email.toLowerCase().includes(F.toLowerCase())).filter(e=>"all"===H||e.billing_status===H),eu=R.filter(e=>e.doctor.name.toLowerCase().includes(F.toLowerCase())||e.doctor.email.toLowerCase().includes(F.toLowerCase())).filter(e=>"all"===H||e.payment_status===H);if(q&&!s)return(0,r.jsx)("div",{className:"p-6 max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mt-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-gray-200 h-32 rounded-lg"},e))}),(0,r.jsx)("div",{className:"bg-gray-200 h-64 rounded-lg mt-6"})]})});let ed=[{id:"overview",label:"Overview",icon:l.TrendingUp},{id:"transactions",label:"Transactions",icon:a.CreditCard},{id:"doctors",label:"Doctors",icon:o.Users},{id:"plans",label:"Plans",icon:d.Gift},{id:"requests",label:`Requests ${U?.pending?`(${U.pending})`:""}`,icon:f.Bell}];return(0,r.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Billing Management"}),(0,r.jsx)("p",{className:"text-slate-800",children:"Manage payments, subscriptions, and referral discounts"})]}),Y&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"success"===Y.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:(0,r.jsx)("p",{className:"text-sm font-medium",children:Y.text})}),(0,r.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:ed.map(s=>{let i=s.icon;return(0,r.jsxs)("button",{onClick:()=>{t(s.id),M(""),z("all")},className:`flex-shrink-0 flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${e===s.id?"border-teal-500 text-teal-600":"border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300"}`,children:[(0,r.jsx)(i,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:s.label})]},s.id)})})}),"overview"===e&&s&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:eo(s.total_revenue)})]}),(0,r.jsx)(n.DollarSign,{className:"w-8 h-8 text-green-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Monthly Revenue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:eo(s.monthly_revenue)})]}),(0,r.jsx)(l.TrendingUp,{className:"w-8 h-8 text-blue-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Pending Payments"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:eo(s.pending_payments)})]}),(0,r.jsx)(u.Clock,{className:"w-8 h-8 text-yellow-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Active Subscriptions"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:s.active_subscriptions})]}),(0,r.jsx)(o.Users,{className:"w-8 h-8 text-purple-600"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-4",children:"User Distribution"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-slate-800",children:"Trial Users"}),(0,r.jsx)("span",{className:"font-medium text-slate-900",children:s.trial_users})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-slate-800",children:"Active Subscribers"}),(0,r.jsx)("span",{className:"font-medium text-slate-900",children:s.active_subscriptions})]})]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-4",children:"Referral Program"}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-slate-800",children:"Total Discounts Given"}),(0,r.jsx)("span",{className:"font-medium text-slate-900",children:eo(s.referral_discounts_given)})]})})]})]})]}),"transactions"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(p.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search transactions...",value:F,onChange:e=>M(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white"})]}),(0,r.jsxs)("select",{value:H,onChange:e=>z(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"paid",children:"Paid"}),(0,r.jsx)("option",{value:"failed",children:"Failed"})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Period"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eu.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-slate-900",children:e.doctor.name}),(0,r.jsx)("div",{className:"text-sm text-slate-600",children:e.doctor.email})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-slate-900",children:[eo(e.final_amount),e.discount_amount>0&&(0,r.jsxs)("div",{className:"text-xs text-green-600",children:[eo(e.discount_amount)," discount applied"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[el(e.payment_status),(0,r.jsx)("span",{className:"text-sm capitalize",children:e.payment_status})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:[new Date(e.billing_period_start).toLocaleDateString()," - ",new Date(e.billing_period_end).toLocaleDateString()]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:"pending"===e.payment_status&&(0,r.jsx)("button",{onClick:()=>ei(e.id),disabled:K===e.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",title:"Mark Payment as Paid",children:K===e.id?"Processing...":"Mark Paid"})})]},e.id))})]}),0===eu.length&&!q&&(0,r.jsx)("div",{className:"text-center py-12 text-gray-500",children:"No transactions found."})]})]}),"doctors"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(p.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search doctors...",value:F,onChange:e=>M(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-slate-800 bg-white"})]}),(0,r.jsxs)("select",{value:H,onChange:e=>z(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-md text-slate-800 bg-white",children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"trial",children:"Trial"}),(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Total Paid"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Referrals"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Available Discount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:eh.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-slate-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-slate-600",children:e.email}),e.clinic_name&&(0,r.jsx)("div",{className:"text-xs text-slate-500",children:e.clinic_name})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs rounded-full font-medium ${ec(e.billing_status)}`,children:e.billing_status})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-900",children:[eo(e.total_paid),e.pending_payments>0&&(0,r.jsxs)("div",{className:"text-xs text-yellow-600",children:[eo(e.pending_payments)," pending"]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:[e.referral_info.successful_referrals," successful",e.referral_info.referred_by&&(0,r.jsxs)("div",{className:"text-xs text-blue-600",children:["Referred by ",e.referral_info.referred_by]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-900",children:eo(e.available_discount_amount)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:(0,r.jsx)("button",{onClick:()=>{X(e),V(!0)},className:"text-blue-600 hover:text-blue-900",children:"Create Bill"})})]},e.id))})]}),0===eh.length&&!q&&(0,r.jsx)("div",{className:"text-center py-12 text-gray-500",children:"No doctors found."})]})]}),"plans"===e&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:P.map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-2",children:e.name}),e.description&&(0,r.jsx)("p",{className:"text-slate-800 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"text-2xl font-bold text-slate-900 mb-4",children:[eo(e.monthly_price),"/month"]}),(0,r.jsxs)("div",{className:"text-sm text-slate-800 mb-4",children:[e.quota_limit," consultations"]}),e.features&&"object"==typeof e.features&&"features"in e.features&&Array.isArray(e.features.features)&&(0,r.jsx)("ul",{className:"text-sm text-slate-800 space-y-1",children:e.features.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.CheckCircle,{className:"w-3 h-3 text-green-500"}),(0,r.jsx)("span",{children:e})]},t))})]},e.id))}),"requests"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Total Requests"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:U?.total||0})]}),(0,r.jsx)(m.MessageCircle,{className:"w-8 h-8 text-blue-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Pending"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:U?.pending||0})]}),(0,r.jsx)(u.Clock,{className:"w-8 h-8 text-orange-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Contacted"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:U?.contacted||0})]}),(0,r.jsx)(g.Phone,{className:"w-8 h-8 text-blue-600"})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-slate-900",children:"Resolved"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:U?.resolved||0})]}),(0,r.jsx)(c.CheckCircle,{className:"w-8 h-8 text-green-600"})]})})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-x-auto",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-900",children:"Contact Requests"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-slate-800",children:"Doctors requesting quota upgrades or plan changes"})]}),(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Doctor"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Request Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Contact Info"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:L.map(e=>(0,r.jsxs)("tr",{className:"pending"===e.status?"bg-orange-50":"",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-slate-900",children:e.doctor_name}),(0,r.jsx)("div",{className:"text-sm text-slate-600",children:e.doctor_email}),e.clinic_name&&(0,r.jsx)("div",{className:"text-xs text-slate-500",children:e.clinic_name})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-slate-900",children:e.request_type})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-slate-900",children:"+91 8921628177"}),(0,r.jsx)("a",{href:`mailto:${e.doctor_email}`,className:"text-xs text-blue-600 hover:text-blue-900",children:"Send Email"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs rounded-full font-medium ${"pending"===e.status?"bg-orange-100 text-orange-800":"contacted"===e.status?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-slate-600",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm space-x-2",children:["pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[" ",(0,r.jsx)("button",{onClick:()=>en(e.id,"contacted"),disabled:K===e.id,className:"text-blue-600 hover:text-blue-900 disabled:opacity-50",children:"Mark Contacted"}),(0,r.jsx)("button",{onClick:()=>en(e.id,"resolved"),disabled:K===e.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",children:"Mark Resolved"})," "]}),"contacted"===e.status&&(0,r.jsx)("button",{onClick:()=>en(e.id,"resolved"),disabled:K===e.id,className:"text-green-600 hover:text-green-900 disabled:opacity-50",children:"Mark Resolved"})]})]},e.id))})]}),0===L.length&&!q&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.MessageCircle,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-slate-900",children:"No contact requests"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-slate-600",children:"No doctors have requested contact yet."})]})]})]}),(0,r.jsx)(v.PlanSelectionModal,{isOpen:G,onClose:()=>{V(!1),X(null)},onSelectPlan:(e,t)=>{W&&ea(W.id,e,t)},plans:P,doctorName:W?.name||"",isLoading:!!K})]})}}}]);

//# sourceMappingURL=1412f4cf982d9b54.js.map