(globalThis.TURBOPACK=globalThis.TURBOPACK||[]).push(["object"==typeof document?document.currentScript:void 0,{26910:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({addItemToEnvelope:()=>p,createAttachmentEnvelopeItem:()=>_,createEnvelope:()=>o,createEventEnvelopeHeaders:()=>h,createSpanEnvelopeItem:()=>g,envelopeContainsItemType:()=>c,envelopeItemTypeToDataCategory:()=>m,forEachEnvelopeItem:()=>l,getSdkMetadataForEnvelopeHeader:()=>T,parseEnvelope:()=>u,serializeEnvelope:()=>S});var a=e.i(204472),r=e.i(937227),s=e.i(568368),i=e.i(104248);function o(e,t=[]){return[e,t]}function p(e,t){let[n,a]=e;return[n,[...a,t]]}function l(e,t){for(let n of e[1]){let e=n[0].type;if(t(n,e))return!0}return!1}function c(e,t){return l(e,(e,n)=>t.includes(n))}function d(e){let t=(0,a.getSentryCarrier)(i.GLOBAL_OBJ);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}function S(e){let[t,n]=e,a=JSON.stringify(t);function r(e){"string"==typeof a?a="string"==typeof e?a+e:[d(a),e]:a.push("string"==typeof e?d(e):e)}for(let e of n){let[t,n]=e;if(r(`
${JSON.stringify(t)}
`),"string"==typeof n||n instanceof Uint8Array)r(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify((0,s.normalize)(n))}r(e)}}return"string"==typeof a?a:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),n=0;for(let a of e)t.set(a,n),n+=a.length;return t}(a)}function u(e){let t="string"==typeof e?d(e):e;function n(e){let n=t.subarray(0,e);return t=t.subarray(e+1),n}function r(){let e=t.indexOf(10);return e<0&&(e=t.length),JSON.parse(function(e){let t=(0,a.getSentryCarrier)(i.GLOBAL_OBJ);return t.decodePolyfill?t.decodePolyfill(e):new TextDecoder().decode(e)}(n(e)))}let s=r(),o=[];for(;t.length;){let e=r(),t="number"==typeof e.length?e.length:void 0;o.push([e,t?n(t):r()])}return[s,o]}function g(e){return[{type:"span"},e]}function _(e){let t="string"==typeof e.data?d(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}let t={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function m(e){return t[e]}function T(e){if(!e?.sdk)return;let{name:t,version:n}=e.sdk;return{name:t,version:n}}function h(e,t,n,a){let s=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&a&&{dsn:(0,r.dsnToString)(a)},...s&&{trace:s}}}}},477766:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({createEventEnvelope:()=>p,createSessionEnvelope:()=>o,createSpanEnvelope:()=>l});var a=e.i(169305),r=e.i(937227),s=e.i(26910),i=e.i(350613);function o(e,t,n,a){let i=(0,s.getSdkMetadataForEnvelopeHeader)(n),o={sent_at:new Date().toISOString(),...i&&{sdk:i},...!!a&&t&&{dsn:(0,r.dsnToString)(t)}},p="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return(0,s.createEnvelope)(o,[p])}function p(e,t,n,a){var r;let i=(0,s.getSdkMetadataForEnvelopeHeader)(n),o=e.type&&"replay_event"!==e.type?e.type:"event";(r=n?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||r.name,e.sdk.version=e.sdk.version||r.version,e.sdk.integrations=[...e.sdk.integrations||[],...r.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...r.packages||[]]);let p=(0,s.createEventEnvelopeHeaders)(e,i,a,t);delete e.sdkProcessingMetadata;let l=[{type:o},e];return(0,s.createEnvelope)(p,[l])}function l(e,t){let n=(0,a.getDynamicSamplingContextFromSpan)(e[0]),o=t?.getDsn(),p=t?.getOptions().tunnel,l={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!p&&o&&{dsn:(0,r.dsnToString)(o)}},c=t?.getOptions().beforeSendSpan,d=c?e=>{let t=(0,i.spanToJSON)(e),n=c(t);return n||((0,i.showSpanDropWarning)(),t)}:i.spanToJSON,S=[];for(let t of e){let e=d(t);e&&S.push((0,s.createSpanEnvelopeItem)(e))}return(0,s.createEnvelope)(l,S)}},905397:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SentryNonRecordingSpan:()=>t});var a=e.i(359366),r=e.i(350613);class t{constructor(e={}){this._traceId=e.traceId||(0,a.generateTraceId)(),this._spanId=e.spanId||(0,a.generateSpanId)()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:r.TRACE_FLAG_NONE}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}}},737374:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({handleCallbackErrors:()=>r});var a=e.i(952776);function r(e,t,n=()=>{}){var s,i,o;let p;try{p=e()}catch(e){throw t(e),n(),e}return s=p,i=t,o=n,(0,a.isThenable)(s)?s.then(e=>(o(),e),e=>{throw i(e),o(),e}):(o(),s)}},72908:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({logSpanEnd:()=>o,logSpanStart:()=>i});var a=e.i(451484),r=e.i(979811),s=e.i(350613);function i(e){if(!a.DEBUG_BUILD)return;let{description:t="< unknown name >",op:n="< unknown op >",parent_span_id:i}=(0,s.spanToJSON)(e),{spanId:o}=e.spanContext(),p=(0,s.spanIsSampled)(e),l=(0,s.getRootSpan)(e),c=l===e,d=`[Tracing] Starting ${p?"sampled":"unsampled"} ${c?"root ":""}span`,S=[`op: ${n}`,`name: ${t}`,`ID: ${o}`];if(i&&S.push(`parent ID: ${i}`),!c){let{op:e,description:t}=(0,s.spanToJSON)(l);S.push(`root ID: ${l.spanContext().spanId}`),e&&S.push(`root op: ${e}`),t&&S.push(`root description: ${t}`)}r.logger.log(`${d}
  ${S.join("\n  ")}`)}function o(e){if(!a.DEBUG_BUILD)return;let{description:t="< unknown name >",op:n="< unknown op >"}=(0,s.spanToJSON)(e),{spanId:i}=e.spanContext(),o=(0,s.getRootSpan)(e)===e,p=`[Tracing] Finishing "${n}" ${o?"root ":""}span "${t}" with ID ${i}`;r.logger.log(p)}},983788:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({sampleSpan:()=>o});var a=e.i(451484),r=e.i(293208),s=e.i(979811),i=e.i(603469);function o(e,t,n){let o,p;if(!(0,r.hasSpansEnabled)(e))return[!1];"function"==typeof e.tracesSampler?(o=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),p=!0):void 0!==t.parentSampled?o=t.parentSampled:void 0!==e.tracesSampleRate&&(o=e.tracesSampleRate,p=!0);let l=(0,i.parseSampleRate)(o);if(void 0===l)return a.DEBUG_BUILD&&s.logger.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(o)} of type ${JSON.stringify(typeof o)}.`),[!1];if(!l)return a.DEBUG_BUILD&&s.logger.log(`[Tracing] Discarding transaction because ${"function"==typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),[!1,l,p];let c=n<l;return!c&&a.DEBUG_BUILD&&s.logger.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(o)})`),[c,l,p]}},983326:e=>{"use strict";var{g:t,__dirname:n}=e;e.s({setMeasurement:()=>o,timedEventsToMeasurements:()=>p});var a=e.i(451484),r=e.i(699732),s=e.i(979811),i=e.i(350613);function o(e,t,n,p=(0,i.getActiveSpan)()){let l=p&&(0,i.getRootSpan)(p);l&&(a.DEBUG_BUILD&&s.logger.log(`[Measurement] Setting measurement on root span: ${e} = ${t} ${n}`),l.addEvent(e,{[r.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]:t,[r.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]:n}))}function p(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let n=e.attributes||{},a=n[r.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT],s=n[r.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE];"string"==typeof a&&"number"==typeof s&&(t[e.name]={value:s,unit:a})}),t}},354209:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({SentrySpan:()=>t});var a=e.i(655220),r=e.i(451484),s=e.i(477766),i=e.i(699732),o=e.i(979811),p=e.i(359366),l=e.i(350613),c=e.i(515186),d=e.i(169305),S=e.i(72908),u=e.i(983326),g=e.i(815246);class t{constructor(e={}){this._traceId=e.traceId||(0,p.generateTraceId)(),this._spanId=e.spanId||(0,p.generateSpanId)(),this._startTime=e.startTimestamp||(0,c.timestampInSeconds)(),this._links=e.links,this._attributes={},this.setAttributes({[i.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"manual",[i.SEMANTIC_ATTRIBUTE_SENTRY_OP]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:n?l.TRACE_FLAG_SAMPLED:l.TRACE_FLAG_NONE}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=(0,l.spanTimeInputToSeconds)(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(i.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,"custom"),this}end(e){this._endTime||(this._endTime=(0,l.spanTimeInputToSeconds)(e),(0,S.logSpanEnd)(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[i.SEMANTIC_ATTRIBUTE_SENTRY_OP],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:(0,l.getStatusMessage)(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[i.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN],profile_id:this._attributes[i.SEMANTIC_ATTRIBUTE_PROFILE_ID],exclusive_time:this._attributes[i.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME],measurements:(0,u.timedEventsToMeasurements)(this._events),is_segment:this._isStandaloneSpan&&(0,l.getRootSpan)(this)===this||void 0,segment_id:this._isStandaloneSpan?(0,l.getRootSpan)(this).spanContext().spanId:void 0,links:(0,l.convertSpanLinksForEnvelope)(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){r.DEBUG_BUILD&&o.logger.log("[Tracing] Adding an event to span:",e);let a=_(t)?t:n||(0,c.timestampInSeconds)(),s=_(t)?{}:t||{},i={name:e,time:(0,l.spanTimeInputToSeconds)(a),attributes:s};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=(0,a.getClient)();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===(0,l.getRootSpan)(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=(0,a.getClient)();if(!t)return;let n=e[1];if(!n||0===n.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}((0,s.createSpanEnvelope)([this],e)):(r.DEBUG_BUILD&&o.logger.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));let t=this._convertSpanToTransaction();t&&((0,g.getCapturedScopesOnSpan)(this).scope||(0,a.getCurrentScope)()).captureEvent(t)}_convertSpanToTransaction(){if(!m((0,l.spanToJSON)(this)))return;this._name||(r.DEBUG_BUILD&&o.logger.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");let{scope:e,isolationScope:n}=(0,g.getCapturedScopesOnSpan)(this),a=e?.getScopeData().sdkProcessingMetadata?.normalizedRequest;if(!0!==this._sampled)return;let s=(0,l.getSpanDescendants)(this).filter(e=>{var n;return e!==this&&!((n=e)instanceof t&&n.isStandaloneSpan())}).map(e=>(0,l.spanToJSON)(e)).filter(m),p=this._attributes[i.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];delete this._attributes[i.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME],s.forEach(e=>{delete e.data[i.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]});let c={contexts:{trace:(0,l.spanToTransactionTraceContext)(this)},spans:s.length>1e3?s.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:(0,d.getDynamicSamplingContextFromSpan)(this)},request:a,...p&&{transaction_info:{source:p}}},S=(0,u.timedEventsToMeasurements)(this._events);return S&&Object.keys(S).length&&(r.DEBUG_BUILD&&o.logger.log("[Measurements] Adding measurements to transaction event",JSON.stringify(S,void 0,2)),c.measurements=S),c}}function _(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function m(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}}},892425:e=>{"use strict";var{g:t,__dirname:n}=e;{e.s({continueTrace:()=>n,startInactiveSpan:()=>A,startNewTrace:()=>b,startSpan:()=>y,startSpanManual:()=>R,suppressTracing:()=>C,withActiveSpan:()=>N});var a=e.i(921903),r=e.i(204472),s=e.i(655220),i=e.i(451484),o=e.i(699732),p=e.i(737374),l=e.i(293208),c=e.i(979811),d=e.i(603469),S=e.i(359366),u=e.i(437189),g=e.i(350613),_=e.i(82280),m=e.i(169305),T=e.i(72908),h=e.i(983788),E=e.i(905397),f=e.i(354209),I=e.i(993478),v=e.i(815246);let t="__SENTRY_SUPPRESS_TRACING__";function y(e,t){let n=D();if(n.startSpan)return n.startSpan(e,t);let a=k(e),{forceTransaction:r,parentSpan:i,scope:o}=e,l=o?.clone();return(0,s.withScope)(l,()=>B(i)(()=>{let n=(0,s.getCurrentScope)(),i=M(n),o=e.onlyIfParent&&!i?new E.SentryNonRecordingSpan:U({parentSpan:i,spanArguments:a,forceTransaction:r,scope:n});return(0,u._setSpanForScope)(n,o),(0,p.handleCallbackErrors)(()=>t(o),()=>{let{status:e}=(0,g.spanToJSON)(o);o.isRecording()&&(!e||"ok"===e)&&o.setStatus({code:I.SPAN_STATUS_ERROR,message:"internal_error"})},()=>{o.end()})}))}function R(e,t){let n=D();if(n.startSpanManual)return n.startSpanManual(e,t);let a=k(e),{forceTransaction:r,parentSpan:i,scope:o}=e,l=o?.clone();return(0,s.withScope)(l,()=>B(i)(()=>{let n=(0,s.getCurrentScope)(),i=M(n),o=e.onlyIfParent&&!i?new E.SentryNonRecordingSpan:U({parentSpan:i,spanArguments:a,forceTransaction:r,scope:n});return(0,u._setSpanForScope)(n,o),(0,p.handleCallbackErrors)(()=>t(o,()=>o.end()),()=>{let{status:e}=(0,g.spanToJSON)(o);o.isRecording()&&(!e||"ok"===e)&&o.setStatus({code:I.SPAN_STATUS_ERROR,message:"internal_error"})})}))}function A(e){let t=D();if(t.startInactiveSpan)return t.startInactiveSpan(e);let n=k(e),{forceTransaction:a,parentSpan:r}=e;return(e.scope?t=>(0,s.withScope)(e.scope,t):void 0!==r?e=>N(r,e):e=>e())(()=>{let t=(0,s.getCurrentScope)(),r=M(t);return e.onlyIfParent&&!r?new E.SentryNonRecordingSpan:U({parentSpan:r,spanArguments:n,forceTransaction:a,scope:t})})}let n=(e,t)=>{let n=(0,r.getMainCarrier)(),i=(0,a.getAsyncContextStrategy)(n);if(i.continueTrace)return i.continueTrace(e,t);let{sentryTrace:o,baggage:p}=e;return(0,s.withScope)(e=>{let n=(0,_.propagationContextFromHeaders)(o,p);return e.setPropagationContext(n),t()})};function N(e,t){let n=D();return n.withActiveSpan?n.withActiveSpan(e,t):(0,s.withScope)(n=>((0,u._setSpanForScope)(n,e||void 0),t(n)))}function C(e){let n=D();return n.suppressTracing?n.suppressTracing(e):(0,s.withScope)(n=>{n.setSDKProcessingMetadata({[t]:!0});let a=e();return n.setSDKProcessingMetadata({[t]:void 0}),a})}function b(e){return(0,s.withScope)(t=>(t.setPropagationContext({traceId:(0,S.generateTraceId)(),sampleRand:Math.random()}),i.DEBUG_BUILD&&c.logger.info(`Starting a new trace with id ${t.getPropagationContext().traceId}`),N(null,e)))}function U({parentSpan:e,spanArguments:n,forceTransaction:a,scope:r}){let i;if(!(0,l.hasSpansEnabled)()){let t=new E.SentryNonRecordingSpan;if(a||!e){let e={sampled:"false",sample_rate:"0",transaction:n.name,...(0,m.getDynamicSamplingContextFromSpan)(t)};(0,m.freezeDscOnSpan)(t,e)}return t}let o=(0,s.getIsolationScope)();if(e&&!a)i=function(e,n,a){let{spanId:r,traceId:i}=e.spanContext(),o=!n.getScopeData().sdkProcessingMetadata[t]&&(0,g.spanIsSampled)(e),p=o?new f.SentrySpan({...a,parentSpanId:r,traceId:i,sampled:o}):new E.SentryNonRecordingSpan({traceId:i});(0,g.addChildSpanToSpan)(e,p);let l=(0,s.getClient)();return l&&(l.emit("spanStart",p),a.endTimestamp&&l.emit("spanEnd",p)),p}(e,r,n),(0,g.addChildSpanToSpan)(e,i);else if(e){let t=(0,m.getDynamicSamplingContextFromSpan)(e),{traceId:a,spanId:s}=e.spanContext(),o=(0,g.spanIsSampled)(e);i=O({traceId:a,parentSpanId:s,...n},r,o),(0,m.freezeDscOnSpan)(i,t)}else{let{traceId:e,dsc:t,parentSpanId:a,sampled:s}={...o.getPropagationContext(),...r.getPropagationContext()};i=O({traceId:e,parentSpanId:a,...n},r,s),t&&(0,m.freezeDscOnSpan)(i,t)}return(0,T.logSpanStart)(i),(0,v.setCapturedScopesOnSpan)(i,r,o),i}function k(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let n={...t};return n.startTimestamp=(0,g.spanTimeInputToSeconds)(e.startTime),delete n.startTime,n}return t}function D(){let e=(0,r.getMainCarrier)();return(0,a.getAsyncContextStrategy)(e)}function O(e,n,a){let r=(0,s.getClient)(),p=r?.getOptions()||{},{name:l=""}=e,S={spanAttributes:{...e.attributes},spanName:l,parentSampled:a};r?.emit("beforeSampling",S,{decision:!1});let u=S.parentSampled??a,g=S.spanAttributes,_=n.getPropagationContext(),[m,T,E]=n.getScopeData().sdkProcessingMetadata[t]?[!1]:(0,h.sampleSpan)(p,{name:l,parentSampled:u,attributes:g,parentSampleRate:(0,d.parseSampleRate)(_.dsc?.sample_rate)},_.sampleRand),I=new f.SentrySpan({...e,attributes:{[o.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"custom",[o.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:void 0!==T&&E?T:void 0,...g},sampled:m});return!m&&r&&(i.DEBUG_BUILD&&c.logger.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",I),I}function M(e){let t=(0,u._getSpanForScope)(e);if(!t)return;let n=(0,s.getClient)();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?(0,g.getRootSpan)(t):t}function B(e){return void 0!==e?t=>N(e,t):e=>e()}}}}]);

//# sourceMappingURL=60acb16f3cdc2b8e.js.map